definitions:
  model.TimeNormal:
    properties:
      isDateOnly:
        description: 标记是否仅包含日期部分
        type: boolean
      time.Time:
        type: string
    type: object
  v1.Account:
    properties:
      email:
        type: string
      phone:
        description: Account string `json:"account,omitempty"`
        type: string
    type: object
  v1.QID:
    properties:
      id:
        type: integer
    type: object
  v1.Response:
    properties:
      code:
        type: string
      data: {}
      message:
        type: string
      succeed:
        type: boolean
    type: object
  v1.User:
    properties:
      account:
        type: string
      email:
        type: string
      id:
        type: integer
      phone:
        type: string
      postIds:
        items:
          type: integer
        type: array
      remark:
        type: string
      roleIds:
        items:
          type: integer
        type: array
      sex:
        description: 枚举限制
        enum:
        - male
        - female
        - other
        type: string
      sort:
        type: integer
      status:
        type: string
      username:
        type: string
    required:
    - sex
    type: object
  v1.UserPage:
    properties:
      account:
        type: string
      createTime:
        $ref: '#/definitions/model.TimeNormal'
      currentPage:
        type: integer
      email:
        type: string
      id:
        type: integer
      pageSize:
        type: integer
      phone:
        type: string
      postIds:
        items:
          type: integer
        type: array
      remark:
        type: string
      roleIds:
        items:
          type: integer
        type: array
      sex:
        description: 枚举限制
        enum:
        - male
        - female
        - other
        type: string
      sort:
        type: integer
      status:
        type: string
      updateTime:
        $ref: '#/definitions/model.TimeNormal'
      username:
        type: string
    required:
    - currentPage
    - pageSize
    - sex
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is a sample server celler server.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Nunu Example API
  version: 1.0.0
paths:
  /admin/system/user/check:
    post:
      consumes:
      - application/json
      description: 通过邮箱或手机号检查用户是否存在
      parameters:
      - description: 用户账号信息
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/v1.Account'
      produces:
      - application/json
      responses:
        "200":
          description: 响应结果
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 检查用户是否存在
      tags:
      - 用户管理
  /admin/system/user/create:
    post:
      consumes:
      - application/json
      description: 创建新用户
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/v1.User'
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 创建用户
      tags:
      - 用户管理
  /admin/system/user/delete:
    post:
      consumes:
      - application/json
      description: 批量删除用户
      parameters:
      - description: 用户ID数组
        in: body
        name: ids
        required: true
        schema:
          items:
            type: integer
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 删除用户
      tags:
      - 用户管理
  /admin/system/user/disable:
    post:
      consumes:
      - application/json
      description: 批量禁用用户
      parameters:
      - description: 用户ID数组
        in: body
        name: ids
        required: true
        schema:
          items:
            type: integer
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 禁用用户
      tags:
      - 用户管理
  /admin/system/user/enable:
    post:
      consumes:
      - application/json
      description: 批量启用用户
      parameters:
      - description: 用户ID数组
        in: body
        name: ids
        required: true
        schema:
          items:
            type: integer
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 启用用户
      tags:
      - 用户管理
  /admin/system/user/query:
    get:
      consumes:
      - application/json
      description: 获取所有用户列表
      produces:
      - application/json
      responses:
        "200":
          description: 用户列表
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 查询所有用户
      tags:
      - 用户管理
  /admin/system/user/queryById:
    post:
      consumes:
      - application/json
      description: 通过用户ID查询用户详细信息
      parameters:
      - description: 用户ID
        in: body
        name: id
        required: true
        schema:
          $ref: '#/definitions/v1.QID'
      produces:
      - application/json
      responses:
        "200":
          description: 用户信息
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 通过ID查询用户
      tags:
      - 用户管理
  /admin/system/user/queryPage:
    post:
      consumes:
      - application/json
      description: 分页查询用户列表
      parameters:
      - description: 查询条件
        in: body
        name: query
        required: true
        schema:
          $ref: '#/definitions/v1.UserPage'
      produces:
      - application/json
      responses:
        "200":
          description: 用户分页数据
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 分页查询用户
      tags:
      - 用户管理
  /admin/system/user/update:
    post:
      consumes:
      - application/json
      description: 更新用户信息
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/v1.User'
      produces:
      - application/json
      responses:
        "200":
          description: 操作成功
          schema:
            $ref: '#/definitions/v1.Response'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/v1.Response'
        "500":
          description: 内部错误
          schema:
            $ref: '#/definitions/v1.Response'
      summary: 更新用户
      tags:
      - 用户管理
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
