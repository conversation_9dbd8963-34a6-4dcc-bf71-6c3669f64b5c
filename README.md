# N-Admin

<div align="center">
  <img src="https://github.com/go-nunu/nunu-layout-admin/blob/main/web/src/assets/images/logo.png?raw=true" alt="N-Admin Logo" width="180">
  <h3>A Modern Go-Based Admin System</h3>
  <p>An enterprise-level permission management platform based on Gin+Casbin+Vue3</p>
  
  [![Go Report Card](https://goreportcard.com/badge/github.com/go-nunu/nunu-layout-admin)](https://goreportcard.com/report/github.com/go-nunu/nunu-layout-admin)
  [![GoDoc](https://godoc.org/github.com/go-nunu/nunu-layout-admin?status.svg)](https://godoc.org/github.com/go-nunu/nunu-layout-admin)
  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
</div>

## 📖 Introduction

N-Admin is an open-source admin boilerplate based on [go-nunu](https://github.com/go-nunu/nunu), using a technology stack of **Gin + Casbin (RBAC) + Vue3 + AntDesignVue + AntdvPro**. It provides a foundational architecture for rapid development. The system integrates enterprise-level features like a workflow engine, expression engine, and permission control, making it suitable for the quick development of various management systems.

### The Problem
Enterprises in rapid growth need various management systems to support their business operations. However, developing these systems from scratch is time-consuming and labor-intensive, often lacking unified permission control and user management solutions. Traditional permission management systems are often too complex or not flexible enough to meet the diverse needs of modern enterprises.

### Our Solution
N-Admin provides an out-of-the-box, enterprise-level backend management system template. It integrates core functionalities like comprehensive RBAC permission control, user management, and organizational structure. Through modular design and a modern technology stack, it helps development teams quickly build management systems that meet enterprise needs.

### System Preview

<p align="center"><img src="https://github.com/go-nunu/nunu-layout-admin/blob/main/web/src/assets/images/preview-home.png?raw=true" alt="Homepage Preview"></p>
<p align="center"><img src="https://github.com/go-nunu/nunu-layout-admin/blob/main/web/src/assets/images/preview-api.png?raw=true" alt="API Preview"></p>

## ✨ Key Features

- ✅ **Permission Management**: Implements RBAC role-based permission control using Casbin, with granularity for both API and menu access.
- ✅ **Multi-Database Support**: Supports MySQL, Postgres, SQLite, and other databases.
- ✅ **User Management**: Supports CRUD operations for admin accounts, with encrypted password storage.
- ✅ **Department Management**: Supports organizational structure management with a multi-level department structure.
- ✅ **Role Management**: Supports role creation, editing, deletion, and permission assignment.
- ✅ **JWT Authentication**: Supports token-based authentication with login and logout functionalities.
- ✅ **Workflow Engine**: Supports business process design, execution, and monitoring.
- ✅ **Expression Engine**: Supports rule configuration and complex logical expression evaluation.
- ✅ **Scheduled Tasks**: Supports the creation and management of scheduled tasks.
- ✅ **API Documentation**: Integrates Swagger for easy API debugging.
- ✅ **Logging System**: Detailed operational logs with support for log querying.
- ✅ **Frontend-Backend Separation**: RESTful API design, supporting independent deployment of frontend and backend.
- ✅ **One-Click Packaging**: Package the entire application into a single executable binary.
- ✅ **Fool-Proof Design**: The superadmin account always has all menu and API permissions to prevent accidental lockouts.

## 🚀 Technology Stack

### Backend
- **[go-nunu](https://github.com/go-nunu/nunu)** - Lightweight Golang boilerplate
- **[Gin](https://github.com/gin-gonic/gin)** - Lightweight web framework
- **[Casbin](https://github.com/casbin/casbin)** - Permission management (RBAC)
- **[GORM](https://github.com/go-gorm/gorm)** - Golang ORM framework
- **[JWT](https://github.com/golang-jwt/jwt)** - Authentication and authorization
- **[Wire](https://github.com/google/wire)** - Dependency injection
- **[Zap](https://github.com/uber-go/zap)** - High-performance logging library
- **[MySQL/Postgres/Sqlite]** - Database support

### Frontend
- **[AntdvPro](https://github.com/antdv-pro/antdv-pro)** - Enterprise-level frontend/design solution
- **[Vue3](https://github.com/vuejs/)** - Progressive JavaScript framework
- **[Vite](https://github.com/vitejs/vite)** - Extremely fast build tool
- **[TypeScript](https://github.com/microsoft/TypeScript)** - Superset of JavaScript
- **[Pinia](https://github.com/vuejs/pinia)** - Vue state management library

## 🔧 Getting Started

### Method 1: Direct Run (for a quick experience)

```bash
# 1. Clone the project
git clone https://github.com/go-nunu/nunu-layout-admin.git

# 2. Navigate to the project directory
cd nunu-layout-admin

# 3. Start the project
go run cmd/server/main.go

# 4. Access the project
# Open your browser to: http://localhost:8000
```

**Default Credentials:**
- Superadmin: `admin` / Password: `123456`
- Regular User: `user` / Password: `123456`

### Method 2: Full Development Flow (for development)

#### 1️⃣ Clone the Project
```bash
git clone https://github.com/go-nunu/nunu-layout-admin.git
cd nunu-layout-admin
```

#### 2️⃣ Backend Setup
1.  **Modify Configuration**
    Edit `config/local.yml` and update the database and other necessary configurations.

2.  **Run Data Migration** (only for the first time)
    ```bash
    go run cmd/migration/main.go
    ```

3.  **Run Backend Service**
    ```bash
    # Direct run
    go run cmd/server/main.go
   
    # Or use hot-reload (requires nunu)
    nunu run
    ```

#### 3️⃣ Frontend Setup
```bash
cd web
npm install
npm run dev
```

#### 4️⃣ Access URLs
- Backend Service: `http://localhost:8000`
- Frontend Service: `http://localhost:6678`

## 📁 Project Structure

```
/
├── api/              # API definitions and routing
│   └── v1/           # API v1
├── cmd/              # Command-line tool entry points
│   ├── gen/          # Code generation tool
│   ├── migration/    # Database migration tool
│   ├── server/       # Server entry point
│   └── task/         # Task processing entry point
├── config/           # Configuration files
├── deploy/           # Deployment-related files
│   ├── build/        # Docker build files
│   └── docker-compose/ # Docker Compose configurations
├── docs/             # API documentation
├── internal/         # Internal packages
│   ├── controller/   # Controllers
│   ├── enum/         # Enum definitions
│   ├── gen/          # Generated code
│   ├── job/          # Scheduled jobs
│   ├── middleware/   # Middlewares
│   ├── model/        # Data models
│   ├── repository/   # Data repositories
│   ├── server/       # Server configuration
│   ├── service/      # Business logic services
│   ├── task/         # Task processing
│   └── utils/        # Utility functions
├── pkg/              # Reusable packages
│   ├── app/          # Application-related utilities
│   ├── config/       # Configuration utilities
│   ├── expr/         # Expression engine
│   ├── jwt/          # JWT utilities
│   ├── log/          # Logging utilities
│   ├── rules/        # Rule engine
│   ├── server/       # Server utilities
│   ├── sid/          # ID generation utilities
│   ├── workflow/     # Workflow engine
│   └── zapgorm2/     # Zap and Gorm integration
├── scripts/          # Script files
│   └── sql/          # SQL scripts
├── storage/          # Storage files
│   └── logs/         # Log files
└── web/              # Frontend code
```

## 📚 Role and Permission Workflow

### Permission Control Flow
When adding a new API endpoint or menu, you need to manually add the permission policy:

1.  Add API endpoint (Path: Permissions Module -> API Management -> Add API)
2.  Add frontend menu (Path: Permissions Module -> Menu Management -> Add Menu)
3.  Add permission policy (Path: Permissions Module -> Role Management -> Add Role/Assign Permissions)

### Casbin Permission Model

This project uses **Casbin** for role-based permission management:

-   **Roles**: Admin, regular user, etc.
-   **Permissions**: CRUD and other operational permissions.
-   **Model**: Uses the `RBAC` access control model.
-   **Storage**: Permission policies are stored in the database.

**Example Policies:**

**API Permissions**
```
p, admin, api:/api/user, GET
p, admin, api:/api/user, POST
p, admin, api:/api/user, PUT
p, admin, api:/api/user, DELETE
p, user, api:/api/profile, GET
```

**Frontend Menu Permissions**
```
p, admin, menu:/users, read
p, user, menu:/admin/roles, read
```

## 🔌 Special Features

### Workflow Engine
The system integrates a complete workflow engine that supports process design, execution, and monitoring:
- Supports process definition and version control.
- Supports task execution, delegation, and transfer.
- Supports process variables and expression evaluation.
- Supports querying of process history.

### Expression Engine
The built-in expression engine supports the configuration and evaluation of complex business rules:
- Supports common expression types like arithmetic, logical, and relational.
- Supports variables, constants, built-in functions, and custom functions.
- Supports expression compilation and caching to improve performance.
- Supports a sandboxed execution environment to ensure security.

### Rule Engine
The system includes a high-performance, flexible, and extensible rule engine for complex condition evaluation and action execution, suitable for business rule processing, decision systems, etc.:
- **Powerful Condition System**: Supports various condition types like simple, compound, regex, and time-based.
- **Flexible Action System**: Includes actions like setting/modifying attributes, logging, conditional actions, and composite actions.
- **High-Performance Design**: Supports concurrent rule execution, object pooling, rule indexing, and condition optimization.
- **Path Expressions**: Use flexible path expressions to access attributes in complex data structures.
- **Priority Control**: Rule execution order is based on priority, ensuring important rules are processed first.
- **Fully Extensible**: Based on an interface design, all components can be extended and customized.
- **Detailed Documentation**: For more details, refer to the [Rule Engine Documentation](./pkg/rules/README.md).

## 📦 Packaging and Deployment

### All-in-One Deployment
```bash
# Build the frontend
cd web
npm run build

# Return to the project root and build the server (will automatically integrate frontend assets)
cd ..
go build -o server cmd/server/main.go

# Run the server
./server

# Access: http://127.0.0.1:8000/
```

### Docker Deployment
```bash
# Build the Docker image
docker build -t nadmin:latest -f deploy/build/Dockerfile .

# Run the container
docker run -p 8000:8000 nadmin:latest
```

### Separate Deployment
If you don't want Go to render the frontend, you can use a reverse proxy like Nginx to deploy the frontend static assets:

1.  Deploy the frontend to Nginx.
2.  Deploy the backend independently.
3.  Configure Nginx to reverse proxy API requests to the backend service.

## 🛠️ Development Guide

### Generate Database Models
```bash
go run cmd/gen/main.go
```

### Generate Swagger Documentation
```bash
swag init -g cmd/server/main.go
```

### Dependency Injection
This project uses [Wire](https://github.com/google/wire) for dependency injection:
```bash
# Update dependency injection
cd cmd/server/wire
wire
```

## 🤝 Contribution Guide
We welcome contributions, bug reports, and new feature suggestions! Please read our [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct, and the process for submitting pull requests to us.

## 📜 License
This project is open-source under the **MIT License**. See the [LICENSE](./LICENSE) file for details.