# CRUD 生成器配置文件
# 生成器配置
generator:
  model_dir: "internal/model"                    # 模型文件目录
  exclude_models:                                # 排除的模型
    - "CasbinRule"
    - "Migration"
  include_models: []                             # 包含的模型（空表示包含所有非排除的模型）
  exclude_fields:                                # 排除的字段
    - "Password"
    - "Salt"
  exclude_system_fields: true                    # 排除系统字段
  generate_controller: true                      # 生成Controller
  generate_service: true                         # 生成Service
  generate_repository: true                      # 生成Repository
  generate_api_structs: true                     # 生成API结构体
  generate_routes: false                         # 生成路由配置
  generate_tests: false                          # 生成测试文件
  generate_wire_set: false                       # 生成Wire配置
  package_name: "admin"                          # 包名
  module_name: "admin"                           # 模块名
  author: "CRUD Generator"                       # 作者
  copyright: "Copyright 2024"                    # 版权信息
  smart_features:                                # 智能功能配置
    smart_query: true                            # 智能查询字段选择
    reuse_public_structs: true                   # 复用公共结构体
    tree_model_support: true                     # 树形模型支持
    optimize_imports: true                       # 优化包引入
    smart_validation: true                       # 智能验证规则
  error_handling:                                # 错误处理配置
    use_predefined_errors: true                  # 使用预定义错误
    error_package: "api/v1"                      # 错误包路径

# 模板配置
templates:
  template_dir: "templates/crud"                 # 模板目录
  controller: "controller.tmpl"                  # Controller模板
  service: "service.tmpl"                        # Service模板
  repository: "repository.tmpl"                  # Repository模板
  api_struct: "api_struct.tmpl"                  # API结构体模板
  routes: "routes.tmpl"                          # 路由模板
  test: "test.tmpl"                              # 测试模板
  wire_set: "wire_set.tmpl"                      # Wire配置模板
  custom_templates: {}                           # 自定义模板

# 输出配置
output:
  controller_dir: "internal/controller"          # Controller输出目录
  service_dir: "internal/service"                # Service输出目录
  repository_dir: "internal/repository"          # Repository输出目录
  api_dir: "api/v1"                             # API结构体输出目录
  routes_file: "internal/server/http_generated.go" # 路由文件
  test_dir: "test/generated"                     # 测试文件目录
  wire_file: "internal/wire/wire_generated.go"   # Wire配置文件
  backup_dir: ".crud_backup"                     # 备份目录
  file_permission: "0644"                        # 文件权限

# 功能特性配置
features:
  enable_cache: false                             # 启用缓存
  enable_validation: false                        # 启用数据验证
  enable_soft_delete: false                       # 启用软删除
  enable_audit: true                             # 启用审计日志
  enable_tenant: true                            # 启用多租户
  enable_tree: true                              # 启用树形结构
  enable_status: true                            # 启用状态管理
  enable_pagination: true                        # 启用分页
  enable_search: true                            # 启用搜索
  enable_sort: true                              # 启用排序
  enable_export: false                           # 启用导出
  enable_import: false                           # 启用导入
  enable_batch: false                             # 启用批量操作
  enable_transaction: false                       # 启用事务

# 代码风格配置
code_style:
  naming:
    controller_suffix: "Controller"              # Controller后缀
    service_suffix: "Service"                    # Service后缀
    repository_suffix: "Repository"              # Repository后缀
    interface_suffix: ""                         # 接口后缀
    test_suffix: "Test"                          # 测试后缀
    request_suffix: "Req"                        # 请求后缀
    response_suffix: "Resp"                      # 响应后缀
  comments:
    controller: "{{.ModelComment}}控制器"         # Controller注释模板
    service: "{{.ModelComment}}服务"             # Service注释模板
    repository: "{{.ModelComment}}仓储"          # Repository注释模板
    api_struct: "{{.ModelComment}}API结构体"     # API结构体注释模板
    add_author: true                             # 添加作者信息
    add_date: true                               # 添加日期信息
  formatting:
    indent: "\t"                                 # 缩进字符
    line_length: 120                             # 行长度限制
    sort_imports: true                           # 排序导入
    group_imports: true                          # 分组导入

# 字段映射配置
field_mappings:
  string:
    type: "string"
    validation: "omitempty,max=255"
    is_searchable: true
    is_sortable: true
    is_filterable: true
  int64:
    type: "int64"
    validation: "omitempty,gte=0"
    is_searchable: false
    is_sortable: true
    is_filterable: true
  time.Time:
    type: "time.Time"
    validation: "omitempty"
    is_searchable: false
    is_sortable: true
    is_filterable: true
  email:
    type: "string"
    validation: "omitempty,email"
    is_searchable: true
    is_sortable: false
    is_filterable: true
  phone:
    type: "string"
    validation: "omitempty,len=11,numeric"
    is_searchable: true
    is_sortable: false
    is_filterable: true
  password:
    type: "string"
    validation: "required,min=6,max=20"
    json_tag: "-"
    is_searchable: false
    is_sortable: false
    is_filterable: false
  uuid:
    type: "string"
    validation: "omitempty,uuid"
    is_searchable: false
    is_sortable: false
    is_filterable: true
  bool:
    type: "bool"
    validation: "omitempty"
    is_searchable: false
    is_sortable: true
    is_filterable: true
  float64:
    type: "float64"
    validation: "omitempty,gte=0"
    is_searchable: false
    is_sortable: true
    is_filterable: true

# 验证配置
validation:
  enable_custom_validators: true                 # 启用自定义验证器
  custom_validators:                             # 自定义验证器列表
    - "unique"
    - "exists"
    - "enum"
  validation_rules: {}                           # 验证规则

# 数据库配置
database:
  driver: "mysql"                                # 数据库驱动
  connection_string: ""                          # 连接字符串
  max_open_conns: 100                           # 最大打开连接数
  max_idle_conns: 10                            # 最大空闲连接数
  conn_max_lifetime: "1h"                       # 连接最大生存时间

# 元数据配置
metadata:
  version: "1.0.0"                              # 配置版本
  description: "CRUD Generator Configuration"    # 配置描述
  created_at: "2024-01-01T00:00:00Z"            # 创建时间
  updated_at: "2024-01-01T00:00:00Z"            # 更新时间