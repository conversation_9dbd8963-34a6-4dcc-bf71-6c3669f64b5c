# 模型方法模板配置
templates:
  - name: "BeforeCreate"
    description: "在创建记录前自动生成UUID（仅UUID字段）"
    condition: "uuid"  # 当模型有uuid字段时生成
    exclude_condition: "tenant_id"  # 但如果有tenant_id字段则不生成这个版本
    imports:
      - '"admin/internal/utils"'
      - '"gorm.io/gorm"'
    template: |
      // BeforeCreate 在创建记录前自动生成UUID
      func (m *{{.ModelName}}) BeforeCreate(tx *gorm.DB) error {
      	if m.UUID == "" {
      		m.UUID = utils.GetUUID()
      	}
      	return nil
      }

  - name: "BeforeCreateWithTenant"
    description: "在创建记录前自动生成UUID和设置租户ID"
    condition: "uuid,tenant_id"  # 当模型同时有uuid和tenant_id字段时生成
    imports:
      - '"admin/internal/utils"'
      - '"gorm.io/gorm"'
      - '"admin/pkg/jwt"'
    template: |
      // BeforeCreate 在创建记录前自动生成UUID和设置租户ID
      func (m *{{.ModelName}}) BeforeCreate(tx *gorm.DB) error {
      	if m.UUID == "" {
      		m.UUID = utils.GetUUID()
      	}
         // 从上下文中获取租户ID
        if m.TenantID==nil {
          if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
             m.TenantID = &userInfo.TenantID
         }
        }
      	return nil
      }

  - name: "TreeMethods"
    description: "树形结构相关方法"
    condition: "parent_id"  # 当模型有parent_id字段时生成
    imports:
      - '"gorm.io/gorm"'
      - '"sort"'
    template: |
      // ToTree 将平级数据转换为树形结构
      func (t *{{.ModelName}}) ToTree(nodeList []*{{.ModelName}}) []*{{.ModelName}} {
      	var roots []*{{.ModelName}}
      
      	// 先遍历一遍节点列表，将每个节点的Children初始化为空切片
      	for _, node := range nodeList {
      		node.Children = make([]*{{.ModelName}}, 0)
      	}
      
      	// 对节点列表按照ID进行排序
      	sort.Slice(nodeList, func(i, j int) bool {
      		return nodeList[i].ID < nodeList[j].ID
      	})
      
      	// 创建一个map，用于快速查找节点，键为节点UUID，值为节点指针
      	nodeMap := make(map[string]*{{.ModelName}})
      	for _, node := range nodeList {
      		nodeMap[node.UUID] = node
      	}
      
      	// 遍历节点列表，构建树形结构
      	for _, node := range nodeList {
      		// 检查节点是否有明确的父节点
      		if node.ParentID != nil && *node.ParentID != "" {
      			parent, ok := nodeMap[*node.ParentID]
      			if ok {
      				parent.Children = append(parent.Children, node)
      			} else {
      				// 如果找不到父节点，则当作顶级节点处理
      				roots = append(roots, node)
      			}
      		} else {
      			// 如果节点没有父节点或者父节点ID为空，直接作为顶级节点
      			roots = append(roots, node)
      		}
      	}
      
      	return roots
      }
      
      // Flatten{{.ModelName}}Tree 将树形结构转换为平级列表
      func Flatten{{.ModelName}}Tree(roots []*{{.ModelName}}) []*{{.ModelName}} {
      	var result []*{{.ModelName}}
      	
      	var flatten func([]*{{.ModelName}})
      	flatten = func(nodes []*{{.ModelName}}) {
      		for _, node := range nodes {
      			result = append(result, node)
      			if len(node.Children) > 0 {
      				flatten(node.Children)
      			}
      		}
      	}
      	
      	flatten(roots)
      	return result
      }
 
  - name: "SoftDelete"
    description: "软删除相关方法"
    condition: "deleted_at"  # 当模型有deleted_at字段时生成
    imports:
      - '"gorm.io/gorm"'
    template: |
      // IsDeleted 检查记录是否已被软删除
      func (m *{{.ModelName}}) IsDeleted() bool {
      	return m.DeletedAt.Valid
      }
      
      // Restore 恢复软删除的记录
      func (m *{{.ModelName}}) Restore(db *gorm.DB) error {
      	return db.Model(m).Update("deleted_at", nil).Error
      }

  - name: "StatusMethods"
    description: "状态管理方法"
    condition: "status"  # 当模型有status字段时生成
    imports:
      - '"gorm.io/gorm"'
    template: |
      // IsEnabled 检查记录是否启用
      func (m *{{.ModelName}}) IsEnabled() bool {
      	return m.Status != nil && *m.Status == "enable"
      }
      
      // Enable 启用记录
      func (m *{{.ModelName}}) Enable(db *gorm.DB) error {
      	status := "enable"
      	return db.Model(m).Update("status", status).Error
      }
      
      // Disable 禁用记录
      func (m *{{.ModelName}}) Disable(db *gorm.DB) error {
      	status := "disabled"
      	return db.Model(m).Update("status", status).Error
      }
