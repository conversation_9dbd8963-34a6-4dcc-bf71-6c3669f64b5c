<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置管理 - Kubernetes UI设计</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Anime.js用于动画 -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@latest/lib/anime.min.js"></script>
    <style>
        .tab-active {
            border-bottom: 2px solid #2563eb;
            color: #2563eb;
        }
        .config-card {
            transition: all 0.3s ease;
        }
        .config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background-color: #10b981; }
        .status-inactive { background-color: #6b7280; }
        .config-preview {
            background-color: #1f2937;
            color: #f9fafb;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-gray-900">
            <i class="fa fa-ship text-white text-2xl mr-2"></i>
            <span class="text-white text-lg font-semibold">K8s Manager</span>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="dashboard.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-dashboard mr-3"></i>
                    仪表板
                </a>
                <a href="workloads.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cubes mr-3"></i>
                    工作负载
                </a>
                <a href="nodes.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-server mr-3"></i>
                    节点管理
                </a>
                <a href="storage.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-database mr-3"></i>
                    存储管理
                </a>
                <a href="network.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-sitemap mr-3"></i>
                    网络管理
                </a>
                <a href="#" class="bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cogs mr-3"></i>
                    配置管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
        <!-- 顶部导航栏 -->
        <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
            <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" id="sidebar-toggle">
                <i class="fa fa-bars text-xl"></i>
            </button>
            
            <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                <div class="relative flex flex-1 items-center">
                    <h1 class="text-xl font-semibold text-gray-900">配置管理</h1>
                </div>
                <div class="flex items-center gap-x-4 lg:gap-x-6">
                    <!-- 命名空间选择器 -->
                    <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                        <option>所有命名空间</option>
                        <option>default</option>
                        <option>kube-system</option>
                        <option>production</option>
                        <option>staging</option>
                    </select>
                    
                    <!-- 创建按钮 -->
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                        <i class="fa fa-plus mr-1"></i>
                        创建配置
                    </button>
                    
                    <!-- 刷新按钮 -->
                    <button class="border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <main class="py-8">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <!-- 配置概览 -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                    <!-- ConfigMaps总数 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-file-text text-2xl text-blue-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">ConfigMaps</dt>
                                        <dd class="text-lg font-medium text-gray-900">18</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Secrets总数 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-lock text-2xl text-red-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Secrets</dt>
                                        <dd class="text-lg font-medium text-gray-900">12</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ServiceAccounts总数 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-user text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Service Accounts</dt>
                                        <dd class="text-lg font-medium text-gray-900">8</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- RBAC规则总数 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-shield text-2xl text-purple-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">RBAC Rules</dt>
                                        <dd class="text-lg font-medium text-gray-900">24</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 配置类型标签页 -->
                <div class="border-b border-gray-200 mb-8">
                    <nav class="-mb-px flex space-x-8">
                        <button class="tab-active py-2 px-1 border-b-2 font-medium text-sm" data-tab="configmaps">
                            ConfigMaps
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="secrets">
                            Secrets
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="serviceaccounts">
                            Service Accounts
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="rbac">
                            RBAC
                        </button>
                    </nav>
                </div>

                <!-- ConfigMaps 标签页内容 -->
                <div id="configmaps-content" class="tab-content">
                    <!-- 搜索和过滤 -->
                    <div class="mb-6 flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fa fa-search text-gray-400"></i>
                                </div>
                                <input type="text" placeholder="搜索 ConfigMaps..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>

                    <!-- ConfigMaps 列表 -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="config-card">
                                <div class="px-6 py-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fa fa-file-text text-2xl text-blue-600"></i>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">app-config</div>
                                                <div class="text-sm text-gray-500">production namespace</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-6">
                                            <div class="text-center">
                                                <div class="text-sm font-medium text-gray-900">5</div>
                                                <div class="text-xs text-gray-500">Keys</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="text-sm font-medium text-gray-900">2天前</div>
                                                <div class="text-xs text-gray-500">创建时间</div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-800 text-sm" onclick="toggleConfigPreview('config-preview-1')">查看</button>
                                                <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                                <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 配置预览 -->
                                    <div id="config-preview-1" class="hidden mt-4 p-4 config-preview rounded-md text-sm">
                                        <div class="mb-2 text-green-400"># app-config ConfigMap</div>
                                        <div class="space-y-1">
                                            <div><span class="text-yellow-400">database_url:</span> <span class="text-blue-400">"******************************/app"</span></div>
                                            <div><span class="text-yellow-400">redis_url:</span> <span class="text-blue-400">"redis://redis:6379"</span></div>
                                            <div><span class="text-yellow-400">log_level:</span> <span class="text-blue-400">"info"</span></div>
                                            <div><span class="text-yellow-400">debug:</span> <span class="text-blue-400">"false"</span></div>
                                            <div><span class="text-yellow-400">max_connections:</span> <span class="text-blue-400">"100"</span></div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            
                            <li class="config-card">
                                <div class="px-6 py-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fa fa-file-text text-2xl text-green-600"></i>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">nginx-config</div>
                                                <div class="text-sm text-gray-500">default namespace</div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-6">
                                            <div class="text-center">
                                                <div class="text-sm font-medium text-gray-900">2</div>
                                                <div class="text-xs text-gray-500">Keys</div>
                                            </div>
                                            <div class="text-center">
                                                <div class="text-sm font-medium text-gray-900">1周前</div>
                                                <div class="text-xs text-gray-500">创建时间</div>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-800 text-sm" onclick="toggleConfigPreview('config-preview-2')">查看</button>
                                                <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                                <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 配置预览 -->
                                    <div id="config-preview-2" class="hidden mt-4 p-4 config-preview rounded-md text-sm">
                                        <div class="mb-2 text-green-400"># nginx-config ConfigMap</div>
                                        <div class="space-y-1">
                                            <div><span class="text-yellow-400">nginx.conf:</span> <span class="text-blue-400">"server { listen 80; ... }"</span></div>
                                            <div><span class="text-yellow-400">default.conf:</span> <span class="text-blue-400">"upstream backend { ... }"</span></div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Secrets 标签页内容 -->
                <div id="secrets-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-lock text-2xl text-red-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">database-secret</div>
                                            <div class="text-sm text-gray-500">production namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">Opaque</div>
                                            <div class="text-xs text-gray-500">Type</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">3</div>
                                            <div class="text-xs text-gray-500">Keys</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">5天前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-lock text-2xl text-blue-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">tls-secret</div>
                                            <div class="text-sm text-gray-500">default namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">kubernetes.io/tls</div>
                                            <div class="text-xs text-gray-500">Type</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2</div>
                                            <div class="text-xs text-gray-500">Keys</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2周前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-lock text-2xl text-green-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">registry-secret</div>
                                            <div class="text-sm text-gray-500">kube-system namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">kubernetes.io/dockerconfigjson</div>
                                            <div class="text-xs text-gray-500">Type</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1</div>
                                            <div class="text-xs text-gray-500">Keys</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1个月前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Service Accounts 标签页内容 -->
                <div id="serviceaccounts-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-user text-2xl text-green-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">api-service-account</div>
                                            <div class="text-sm text-gray-500">production namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2</div>
                                            <div class="text-xs text-gray-500">Secrets</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1周前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <span class="status-dot status-active"></span>
                                                Active
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-user text-2xl text-blue-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">default</div>
                                            <div class="text-sm text-gray-500">default namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1</div>
                                            <div class="text-xs text-gray-500">Secrets</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">45天前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <span class="status-dot status-active"></span>
                                                Active
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- RBAC 标签页内容 -->
                <div id="rbac-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-shield text-2xl text-purple-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">api-admin-role</div>
                                            <div class="text-sm text-gray-500">ClusterRole</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">5</div>
                                            <div class="text-xs text-gray-500">Rules</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2</div>
                                            <div class="text-xs text-gray-500">Bindings</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1周前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="config-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-shield text-2xl text-green-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">pod-reader</div>
                                            <div class="text-sm text-gray-500">Role • production namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">3</div>
                                            <div class="text-xs text-gray-500">Rules</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1</div>
                                            <div class="text-xs text-gray-500">Bindings</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">3天前</div>
                                            <div class="text-xs text-gray-500">创建时间</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 侧边栏切换
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });

        // 标签页切换
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');

                // 移除所有标签页的活动状态
                document.querySelectorAll('[data-tab]').forEach(t => {
                    t.classList.remove('tab-active');
                    t.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                });

                // 激活当前标签页
                this.classList.add('tab-active');
                this.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');

                // 隐藏所有内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });

                // 显示目标内容
                document.getElementById(targetTab + '-content').classList.remove('hidden');
            });
        });

        // 配置预览切换
        function toggleConfigPreview(previewId) {
            const preview = document.getElementById(previewId);
            preview.classList.toggle('hidden');
        }

        // 页面加载动画
        anime({
            targets: '.config-card',
            translateY: [20, 0],
            opacity: [0, 1],
            delay: anime.stagger(50),
            duration: 400,
            easing: 'easeOutExpo'
        });
    </script>
</body>
</html>
