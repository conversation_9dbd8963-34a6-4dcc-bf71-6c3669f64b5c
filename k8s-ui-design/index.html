<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes管理系统 - UI设计导航</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Anime.js用于动画 -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@latest/lib/anime.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fa fa-ship text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">Kubernetes管理系统 UI设计</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm opacity-90">设计版本: v1.0</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 设计系统概览 -->
        <div class="mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">设计系统概览</h2>
            
            <!-- 颜色系统 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-xl font-semibold mb-4">颜色系统</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-600 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">主色</p>
                        <p class="text-xs text-gray-500">#2563eb</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-indigo-600 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">辅助色</p>
                        <p class="text-xs text-gray-500">#4f46e5</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">成功</p>
                        <p class="text-xs text-gray-500">#10b981</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-500 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">警告</p>
                        <p class="text-xs text-gray-500">#f59e0b</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-500 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">错误</p>
                        <p class="text-xs text-gray-500">#ef4444</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-900 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">文本主色</p>
                        <p class="text-xs text-gray-500">#111827</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-500 rounded-lg mx-auto mb-2"></div>
                        <p class="text-sm font-medium">文本辅色</p>
                        <p class="text-xs text-gray-500">#6b7280</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-2 border"></div>
                        <p class="text-sm font-medium">背景色</p>
                        <p class="text-xs text-gray-500">#f3f4f6</p>
                    </div>
                </div>
            </div>

            <!-- 组件库 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-semibold mb-4">核心组件</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- 按钮组件 -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-700">按钮</h4>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">主要按钮</button>
                        <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">次要按钮</button>
                        <button class="text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">文本按钮</button>
                    </div>
                    
                    <!-- 输入框组件 -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-700">输入框</h4>
                        <input type="text" placeholder="默认输入框" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <input type="text" placeholder="搜索..." class="w-full border border-gray-300 rounded-lg px-3 py-2 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    
                    <!-- 状态标签 -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-700">状态标签</h4>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">运行中</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">等待中</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">错误</span>
                    </div>
                    
                    <!-- 卡片组件 -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-gray-700">卡片</h4>
                        <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <h5 class="font-medium text-gray-900">卡片标题</h5>
                            <p class="text-sm text-gray-500 mt-1">卡片内容描述</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页面导航 -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-6">页面设计导航</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 仪表板 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <i class="fa fa-dashboard text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">仪表板</h3>
                        <p class="text-gray-600 mb-4">集群概览、资源监控、关键指标展示</p>
                        <a href="dashboard.html" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                            查看设计 <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>

                <!-- 工作负载 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="h-48 bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center">
                        <i class="fa fa-cubes text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">工作负载</h3>
                        <p class="text-gray-600 mb-4">Pods、Deployments、Services管理</p>
                        <a href="workloads.html" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                            查看设计 <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>

                <!-- 节点管理 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="h-48 bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center">
                        <i class="fa fa-server text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">节点管理</h3>
                        <p class="text-gray-600 mb-4">集群节点状态、资源分配管理</p>
                        <a href="nodes.html" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                            查看设计 <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>

                <!-- 存储管理 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="h-48 bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
                        <i class="fa fa-database text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">存储管理</h3>
                        <p class="text-gray-600 mb-4">PV、PVC、StorageClass管理</p>
                        <a href="storage.html" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                            查看设计 <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>

                <!-- 网络管理 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="h-48 bg-gradient-to-br from-indigo-500 to-blue-600 flex items-center justify-center">
                        <i class="fa fa-sitemap text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">网络管理</h3>
                        <p class="text-gray-600 mb-4">Ingress、NetworkPolicy配置</p>
                        <a href="network.html" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                            查看设计 <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>

                <!-- 配置管理 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden card-hover">
                    <div class="h-48 bg-gradient-to-br from-teal-500 to-green-600 flex items-center justify-center">
                        <i class="fa fa-cogs text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">配置管理</h3>
                        <p class="text-gray-600 mb-4">ConfigMap、Secret配置管理</p>
                        <a href="config.html" class="inline-flex items-center text-blue-600 hover:text-blue-800">
                            查看设计 <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        anime({
            targets: '.card-hover',
            translateY: [50, 0],
            opacity: [0, 1],
            delay: anime.stagger(100),
            duration: 800,
            easing: 'easeOutExpo'
        });
    </script>
</body>
</html>
