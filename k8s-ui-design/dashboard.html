<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes仪表板 - UI设计</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Anime.js用于动画 -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@latest/lib/anime.min.js"></script>
    <!-- 引入Chart.js用于图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #10b981; }
        .status-pending { background-color: #f59e0b; }
        .status-failed { background-color: #ef4444; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-gray-900">
            <i class="fa fa-ship text-white text-2xl mr-2"></i>
            <span class="text-white text-lg font-semibold">K8s Manager</span>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="#" class="bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-dashboard mr-3"></i>
                    仪表板
                </a>
                <a href="workloads.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cubes mr-3"></i>
                    工作负载
                </a>
                <a href="nodes.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-server mr-3"></i>
                    节点管理
                </a>
                <a href="storage.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-database mr-3"></i>
                    存储管理
                </a>
                <a href="network.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-sitemap mr-3"></i>
                    网络管理
                </a>
                <a href="config.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cogs mr-3"></i>
                    配置管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
        <!-- 顶部导航栏 -->
        <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
            <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" id="sidebar-toggle">
                <i class="fa fa-bars text-xl"></i>
            </button>
            
            <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                <div class="relative flex flex-1 items-center">
                    <h1 class="text-xl font-semibold text-gray-900">集群仪表板</h1>
                </div>
                <div class="flex items-center gap-x-4 lg:gap-x-6">
                    <!-- 集群选择器 -->
                    <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                        <option>production-cluster</option>
                        <option>staging-cluster</option>
                        <option>development-cluster</option>
                    </select>
                    
                    <!-- 刷新按钮 -->
                    <button class="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition-colors">
                        <i class="fa fa-refresh mr-1"></i>
                        刷新
                    </button>
                    
                    <!-- 用户菜单 -->
                    <div class="relative">
                        <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <main class="py-8">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <!-- 关键指标卡片 -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                    <!-- 节点状态 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg metric-card">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-server text-2xl text-blue-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">节点总数</dt>
                                        <dd class="text-lg font-medium text-gray-900">12</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3">
                            <div class="text-sm">
                                <span class="status-dot status-running"></span>
                                <span class="text-green-600 font-medium">10 运行中</span>
                                <span class="text-gray-500 ml-2">2 维护中</span>
                            </div>
                        </div>
                    </div>

                    <!-- Pod状态 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg metric-card">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-cube text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Pod总数</dt>
                                        <dd class="text-lg font-medium text-gray-900">248</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3">
                            <div class="text-sm">
                                <span class="status-dot status-running"></span>
                                <span class="text-green-600 font-medium">235 运行中</span>
                                <span class="text-yellow-600 ml-2">8 等待中</span>
                                <span class="text-red-600 ml-2">5 失败</span>
                            </div>
                        </div>
                    </div>

                    <!-- CPU使用率 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg metric-card">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-microchip text-2xl text-orange-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">CPU使用率</dt>
                                        <dd class="text-lg font-medium text-gray-900">68%</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-600 h-2 rounded-full" style="width: 68%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 内存使用率 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg metric-card">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-memory text-2xl text-purple-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">内存使用率</dt>
                                        <dd class="text-lg font-medium text-gray-900">72%</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-5 py-3">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-600 h-2 rounded-full" style="width: 72%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表和详细信息 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- 资源使用趋势图 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">资源使用趋势</h3>
                        </div>
                        <div class="p-6">
                            <canvas id="resourceChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- 集群健康状态 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">集群健康状态</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-900">API Server</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <span class="status-dot status-running"></span>
                                        健康
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-900">Controller Manager</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <span class="status-dot status-running"></span>
                                        健康
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-900">Scheduler</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <span class="status-dot status-running"></span>
                                        健康
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-900">etcd</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <span class="status-dot status-pending"></span>
                                        警告
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近事件和Pod列表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 最近事件 -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">最近事件</h3>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <div class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <i class="fa fa-exclamation-triangle text-yellow-500"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm text-gray-900">Pod nginx-deployment-7d5c6d8b4f-xyz12 重启</p>
                                        <p class="text-xs text-gray-500">2分钟前</p>
                                    </div>
                                </div>
                            </div>
                            <div class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <i class="fa fa-check-circle text-green-500"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm text-gray-900">新节点 worker-node-04 加入集群</p>
                                        <p class="text-xs text-gray-500">15分钟前</p>
                                    </div>
                                </div>
                            </div>
                            <div class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    <div class="flex-shrink-0">
                                        <i class="fa fa-info-circle text-blue-500"></i>
                                    </div>
                                    <div class="min-w-0 flex-1">
                                        <p class="text-sm text-gray-900">Deployment redis-cache 扩容至3个副本</p>
                                        <p class="text-xs text-gray-500">1小时前</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 问题Pod -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">问题Pod</h3>
                        </div>
                        <div class="divide-y divide-gray-200">
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">mysql-db-7f8c9d6e5f-abc12</p>
                                        <p class="text-xs text-gray-500">default namespace</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        CrashLoopBackOff
                                    </span>
                                </div>
                            </div>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">api-gateway-6b7d8e9f0g-def34</p>
                                        <p class="text-xs text-gray-500">production namespace</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Pending
                                    </span>
                                </div>
                            </div>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">worker-service-8c9d0e1f2g-ghi56</p>
                                        <p class="text-xs text-gray-500">staging namespace</p>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        ImagePullBackOff
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 侧边栏切换
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });

        // 页面加载动画
        anime({
            targets: '.metric-card',
            translateY: [30, 0],
            opacity: [0, 1],
            delay: anime.stagger(100),
            duration: 600,
            easing: 'easeOutExpo'
        });

        // 资源使用趋势图
        const ctx = document.getElementById('resourceChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                datasets: [{
                    label: 'CPU使用率',
                    data: [45, 52, 68, 75, 82, 68, 58],
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4
                }, {
                    label: '内存使用率',
                    data: [60, 65, 72, 78, 85, 72, 68],
                    borderColor: '#8b5cf6',
                    backgroundColor: 'rgba(139, 92, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    </script>
</body>
</html>
