<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点管理 - Kubernetes UI设计</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Anime.js用于动画 -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@latest/lib/anime.min.js"></script>
    <style>
        .node-card {
            transition: all 0.3s ease;
        }
        .node-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-ready { background-color: #10b981; }
        .status-notready { background-color: #ef4444; }
        .status-unknown { background-color: #6b7280; }
        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-gray-900">
            <i class="fa fa-ship text-white text-2xl mr-2"></i>
            <span class="text-white text-lg font-semibold">K8s Manager</span>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="dashboard.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-dashboard mr-3"></i>
                    仪表板
                </a>
                <a href="workloads.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cubes mr-3"></i>
                    工作负载
                </a>
                <a href="#" class="bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-server mr-3"></i>
                    节点管理
                </a>
                <a href="storage.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-database mr-3"></i>
                    存储管理
                </a>
                <a href="network.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-sitemap mr-3"></i>
                    网络管理
                </a>
                <a href="config.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cogs mr-3"></i>
                    配置管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
        <!-- 顶部导航栏 -->
        <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
            <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" id="sidebar-toggle">
                <i class="fa fa-bars text-xl"></i>
            </button>
            
            <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                <div class="relative flex flex-1 items-center">
                    <h1 class="text-xl font-semibold text-gray-900">节点管理</h1>
                </div>
                <div class="flex items-center gap-x-4 lg:gap-x-6">
                    <!-- 视图切换 -->
                    <div class="flex border border-gray-300 rounded-md">
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-l-md" id="grid-view">
                            <i class="fa fa-th"></i>
                        </button>
                        <button class="px-3 py-1 text-sm text-gray-700 hover:bg-gray-50 rounded-r-md" id="list-view">
                            <i class="fa fa-list"></i>
                        </button>
                    </div>
                    
                    <!-- 刷新按钮 -->
                    <button class="border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <main class="py-8">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <!-- 集群概览 -->
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                    <!-- 总节点数 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-server text-2xl text-blue-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">总节点数</dt>
                                        <dd class="text-lg font-medium text-gray-900">12</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 健康节点 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-check-circle text-2xl text-green-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">健康节点</dt>
                                        <dd class="text-lg font-medium text-gray-900">10</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 总CPU核心 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-microchip text-2xl text-orange-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">总CPU核心</dt>
                                        <dd class="text-lg font-medium text-gray-900">96</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 总内存 -->
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-5">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fa fa-memory text-2xl text-purple-600"></i>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">总内存</dt>
                                        <dd class="text-lg font-medium text-gray-900">384 GB</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索和过滤 -->
                <div class="mb-6 flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fa fa-search text-gray-400"></i>
                            </div>
                            <input type="text" placeholder="搜索节点..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>所有状态</option>
                            <option>Ready</option>
                            <option>NotReady</option>
                            <option>Unknown</option>
                        </select>
                        <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                            <option>所有角色</option>
                            <option>Master</option>
                            <option>Worker</option>
                        </select>
                    </div>
                </div>

                <!-- 节点网格视图 -->
                <div id="grid-content" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Master节点 -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden node-card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <i class="fa fa-server text-2xl text-blue-600 mr-3"></i>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">master-node-01</h3>
                                        <p class="text-sm text-gray-500">192.168.1.10</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Master
                                </span>
                            </div>
                            
                            <div class="mb-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="status-dot status-ready"></span>
                                    Ready
                                </span>
                            </div>

                            <div class="space-y-3">
                                <!-- CPU使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">CPU</span>
                                        <span class="font-medium">45%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full progress-bar" style="width: 45%"></div>
                                    </div>
                                </div>

                                <!-- 内存使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">内存</span>
                                        <span class="font-medium">62%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-600 h-2 rounded-full progress-bar" style="width: 62%"></div>
                                    </div>
                                </div>

                                <!-- 磁盘使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">磁盘</span>
                                        <span class="font-medium">38%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-600 h-2 rounded-full progress-bar" style="width: 38%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex justify-between text-sm text-gray-600">
                                    <span>Pods: 15/110</span>
                                    <span>运行时间: 45天</span>
                                </div>
                            </div>

                            <div class="mt-4 flex space-x-2">
                                <button class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                                    详情
                                </button>
                                <button class="flex-1 border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                                    监控
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Worker节点1 -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden node-card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <i class="fa fa-server text-2xl text-green-600 mr-3"></i>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">worker-node-01</h3>
                                        <p class="text-sm text-gray-500">192.168.1.11</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Worker
                                </span>
                            </div>
                            
                            <div class="mb-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="status-dot status-ready"></span>
                                    Ready
                                </span>
                            </div>

                            <div class="space-y-3">
                                <!-- CPU使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">CPU</span>
                                        <span class="font-medium">78%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-orange-600 h-2 rounded-full progress-bar" style="width: 78%"></div>
                                    </div>
                                </div>

                                <!-- 内存使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">内存</span>
                                        <span class="font-medium">85%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-red-600 h-2 rounded-full progress-bar" style="width: 85%"></div>
                                    </div>
                                </div>

                                <!-- 磁盘使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">磁盘</span>
                                        <span class="font-medium">52%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-600 h-2 rounded-full progress-bar" style="width: 52%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex justify-between text-sm text-gray-600">
                                    <span>Pods: 28/110</span>
                                    <span>运行时间: 42天</span>
                                </div>
                            </div>

                            <div class="mt-4 flex space-x-2">
                                <button class="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                                    详情
                                </button>
                                <button class="flex-1 border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                                    监控
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 问题节点 -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden node-card">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <i class="fa fa-server text-2xl text-red-600 mr-3"></i>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">worker-node-05</h3>
                                        <p class="text-sm text-gray-500">192.168.1.15</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Worker
                                </span>
                            </div>
                            
                            <div class="mb-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <span class="status-dot status-notready"></span>
                                    NotReady
                                </span>
                            </div>

                            <div class="space-y-3">
                                <!-- CPU使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">CPU</span>
                                        <span class="font-medium text-gray-400">--</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gray-400 h-2 rounded-full progress-bar" style="width: 0%"></div>
                                    </div>
                                </div>

                                <!-- 内存使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">内存</span>
                                        <span class="font-medium text-gray-400">--</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gray-400 h-2 rounded-full progress-bar" style="width: 0%"></div>
                                    </div>
                                </div>

                                <!-- 磁盘使用率 -->
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="text-gray-600">磁盘</span>
                                        <span class="font-medium text-gray-400">--</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gray-400 h-2 rounded-full progress-bar" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="flex justify-between text-sm text-gray-600">
                                    <span>Pods: 0/110</span>
                                    <span class="text-red-600">连接丢失</span>
                                </div>
                            </div>

                            <div class="mt-4 flex space-x-2">
                                <button class="flex-1 bg-red-600 text-white px-3 py-2 rounded-md text-sm hover:bg-red-700 transition-colors">
                                    诊断
                                </button>
                                <button class="flex-1 border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                                    重启
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 侧边栏切换
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });

        // 视图切换
        document.getElementById('grid-view').addEventListener('click', function() {
            this.classList.add('bg-blue-600', 'text-white');
            this.classList.remove('text-gray-700');
            document.getElementById('list-view').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('list-view').classList.add('text-gray-700');
        });

        document.getElementById('list-view').addEventListener('click', function() {
            this.classList.add('bg-blue-600', 'text-white');
            this.classList.remove('text-gray-700');
            document.getElementById('grid-view').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('grid-view').classList.add('text-gray-700');
        });

        // 页面加载动画
        anime({
            targets: '.node-card',
            translateY: [30, 0],
            opacity: [0, 1],
            delay: anime.stagger(100),
            duration: 600,
            easing: 'easeOutExpo'
        });

        // 进度条动画
        anime({
            targets: '.progress-bar',
            width: function(el) {
                return el.style.width;
            },
            delay: 500,
            duration: 1000,
            easing: 'easeOutExpo'
        });
    </script>
</body>
</html>
