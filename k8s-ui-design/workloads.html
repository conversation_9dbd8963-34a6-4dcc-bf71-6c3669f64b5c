<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作负载管理 - Kubernetes UI设计</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome图标 -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Anime.js用于动画 -->
    <script src="https://cdn.jsdelivr.net/npm/animejs@latest/lib/anime.min.js"></script>
    <style>
        .tab-active {
            border-bottom: 2px solid #2563eb;
            color: #2563eb;
        }
        .resource-card {
            transition: all 0.3s ease;
        }
        .resource-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #10b981; }
        .status-pending { background-color: #f59e0b; }
        .status-failed { background-color: #ef4444; }
        .status-succeeded { background-color: #3b82f6; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-center h-16 bg-gray-900">
            <i class="fa fa-ship text-white text-2xl mr-2"></i>
            <span class="text-white text-lg font-semibold">K8s Manager</span>
        </div>
        <nav class="mt-8">
            <div class="px-4 space-y-2">
                <a href="dashboard.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-dashboard mr-3"></i>
                    仪表板
                </a>
                <a href="#" class="bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cubes mr-3"></i>
                    工作负载
                </a>
                <a href="nodes.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-server mr-3"></i>
                    节点管理
                </a>
                <a href="storage.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-database mr-3"></i>
                    存储管理
                </a>
                <a href="network.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-sitemap mr-3"></i>
                    网络管理
                </a>
                <a href="config.html" class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    <i class="fa fa-cogs mr-3"></i>
                    配置管理
                </a>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
        <!-- 顶部导航栏 -->
        <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
            <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" id="sidebar-toggle">
                <i class="fa fa-bars text-xl"></i>
            </button>

            <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                <div class="relative flex flex-1 items-center">
                    <h1 class="text-xl font-semibold text-gray-900">工作负载管理</h1>
                </div>
                <div class="flex items-center gap-x-4 lg:gap-x-6">
                    <!-- 命名空间选择器 -->
                    <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                        <option>所有命名空间</option>
                        <option>default</option>
                        <option>kube-system</option>
                        <option>production</option>
                        <option>staging</option>
                    </select>

                    <!-- 创建按钮 -->
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                        <i class="fa fa-plus mr-1"></i>
                        创建资源
                    </button>

                    <!-- 刷新按钮 -->
                    <button class="border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                        <i class="fa fa-refresh"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <main class="py-8">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <!-- 工作负载类型标签页 -->
                <div class="border-b border-gray-200 mb-8">
                    <nav class="-mb-px flex space-x-8">
                        <button class="tab-active py-2 px-1 border-b-2 font-medium text-sm" data-tab="deployments">
                            Deployments
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="pods">
                            Pods
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="services">
                            Services
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="jobs">
                            Jobs
                        </button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-2 px-1 border-b-2 font-medium text-sm" data-tab="cronjobs">
                            CronJobs
                        </button>
                    </nav>
                </div>

                <!-- Deployments 标签页内容 -->
                <div id="deployments-content" class="tab-content">
                    <!-- 搜索和过滤 -->
                    <div class="mb-6 flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fa fa-search text-gray-400"></i>
                                </div>
                                <input type="text" placeholder="搜索 Deployments..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                                <option>所有状态</option>
                                <option>运行中</option>
                                <option>更新中</option>
                                <option>失败</option>
                            </select>
                            <button class="border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50 transition-colors">
                                <i class="fa fa-filter mr-1"></i>
                                过滤
                            </button>
                        </div>
                    </div>

                    <!-- Deployments 列表 -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-cube text-2xl text-blue-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">nginx-deployment</div>
                                            <div class="text-sm text-gray-500">default namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">3/3</div>
                                            <div class="text-xs text-gray-500">Ready</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">3</div>
                                            <div class="text-xs text-gray-500">Up-to-date</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">3</div>
                                            <div class="text-xs text-gray-500">Available</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <span class="status-dot status-running"></span>
                                                运行中
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-cube text-2xl text-green-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">api-gateway</div>
                                            <div class="text-sm text-gray-500">production namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2/2</div>
                                            <div class="text-xs text-gray-500">Ready</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2</div>
                                            <div class="text-xs text-gray-500">Up-to-date</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2</div>
                                            <div class="text-xs text-gray-500">Available</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <span class="status-dot status-running"></span>
                                                运行中
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-cube text-2xl text-yellow-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">redis-cache</div>
                                            <div class="text-sm text-gray-500">staging namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1/3</div>
                                            <div class="text-xs text-gray-500">Ready</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1</div>
                                            <div class="text-xs text-gray-500">Up-to-date</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1</div>
                                            <div class="text-xs text-gray-500">Available</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                <span class="status-dot status-pending"></span>
                                                更新中
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Pods 标签页内容 -->
                <div id="pods-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-cube text-xl text-green-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">nginx-deployment-7d5c6d8b4f-xyz12</div>
                                            <div class="text-sm text-gray-500">default namespace • node-01</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1/1</div>
                                            <div class="text-xs text-gray-500">Ready</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">0</div>
                                            <div class="text-xs text-gray-500">Restarts</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <span class="status-dot status-running"></span>
                                                Running
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">日志</button>
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">终端</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-cube text-xl text-red-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">mysql-db-7f8c9d6e5f-abc12</div>
                                            <div class="text-sm text-gray-500">default namespace • node-02</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">0/1</div>
                                            <div class="text-xs text-gray-500">Ready</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">5</div>
                                            <div class="text-xs text-gray-500">Restarts</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <span class="status-dot status-failed"></span>
                                                CrashLoopBackOff
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">日志</button>
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">终端</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Services 标签页内容 -->
                <div id="services-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-sitemap text-xl text-blue-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">nginx-service</div>
                                            <div class="text-sm text-gray-500">default namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">ClusterIP</div>
                                            <div class="text-xs text-gray-500">Type</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">***********</div>
                                            <div class="text-xs text-gray-500">Cluster IP</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">80/TCP</div>
                                            <div class="text-xs text-gray-500">Port(s)</div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Jobs 标签页内容 -->
                <div id="jobs-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-tasks text-xl text-green-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">backup-job-20240627</div>
                                            <div class="text-sm text-gray-500">default namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">1/1</div>
                                            <div class="text-xs text-gray-500">Completions</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">5m32s</div>
                                            <div class="text-xs text-gray-500">Duration</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <span class="status-dot status-succeeded"></span>
                                                Complete
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">日志</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- CronJobs 标签页内容 -->
                <div id="cronjobs-content" class="tab-content hidden">
                    <div class="bg-white shadow overflow-hidden sm:rounded-md">
                        <ul class="divide-y divide-gray-200">
                            <li class="resource-card">
                                <div class="px-6 py-4 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fa fa-clock-o text-xl text-purple-600"></i>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">daily-backup</div>
                                            <div class="text-sm text-gray-500">default namespace</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-6">
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">0 2 * * *</div>
                                            <div class="text-xs text-gray-500">Schedule</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-sm font-medium text-gray-900">2024-06-27 02:00</div>
                                            <div class="text-xs text-gray-500">Last Schedule</div>
                                        </div>
                                        <div class="text-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <span class="status-dot status-running"></span>
                                                Active
                                            </span>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 侧边栏切换
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });

        // 标签页切换
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');

                // 移除所有标签页的活动状态
                document.querySelectorAll('[data-tab]').forEach(t => {
                    t.classList.remove('tab-active');
                    t.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                });

                // 激活当前标签页
                this.classList.add('tab-active');
                this.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');

                // 隐藏所有内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });

                // 显示目标内容
                document.getElementById(targetTab + '-content').classList.remove('hidden');
            });
        });

        // 页面加载动画
        anime({
            targets: '.resource-card',
            translateY: [20, 0],
            opacity: [0, 1],
            delay: anime.stagger(50),
            duration: 400,
            easing: 'easeOutExpo'
        });
    </script>
</body>
</html>