/**
 * 表达式引擎 Web UI 样式
 */

/* 主题颜色 */
:root {
  --primary-color: #3f51b5;
  --primary-light: #7986cb;
  --secondary-color: #ff4081;
  --dark-color: #303030;
  --light-color: #f5f5f5;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
}

/* 基础样式 */
body {
  background-color: #f8f9fa;
  color: var(--gray-800);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  padding-bottom: 60px;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.nav-link.active {
  font-weight: 600;
  color: var(--primary-color) !important;
}

/* 内容区域 */
.content-section {
  animation: fadein 0.3s;
}

@keyframes fadein {
  from { opacity: 0; }
  to   { opacity: 1; }
}

/* 标题样式 */
.section-title {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-light);
  padding-bottom: 8px;
  margin-bottom: 20px;
}

/* 卡片样式 */
.card {
  box-shadow: 0 2px 4px rgba(0,0,0,.1);
  transition: all 0.3s ease;
  border: none;
}

.card:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,.1);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid var(--gray-200);
  font-weight: 600;
  color: var(--primary-color);
}

/* 表达式编辑器 */
.CodeMirror {
  height: auto !important;
  min-height: 120px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  padding: 8px;
  font-family: "Fira Code", "Source Code Pro", monospace;
  font-size: 14px;
  line-height: 1.5;
}

.CodeMirror-focused {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(63, 81, 181, 0.25);
}

/* 结果区域 */
#resultArea {
  min-height: 50px;
  transition: all 0.3s ease;
}

.result-success {
  border-color: var(--success-color) !important;
  border-left-width: 5px !important;
}

.result-error {
  border-color: var(--error-color) !important;
  border-left-width: 5px !important;
}

.result-value {
  font-family: "Fira Code", "Source Code Pro", monospace;
  font-size: 16px;
  font-weight: 500;
  color: var(--dark-color);
}

/* 变量表格 */
#variablesTable {
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  overflow: hidden;
}

#variablesTable th {
  background-color: var(--gray-100);
  color: var(--gray-700);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  padding: 12px;
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #303f9f;
  border-color: #303f9f;
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-secondary:hover {
  background-color: #d81b60;
  border-color: #d81b60;
}

/* 函数和操作符下拉菜单 */
.dropdown-menu {
  box-shadow: 0 4px 8px rgba(0,0,0,.1);
  border: none;
}

.dropdown-item {
  padding: 6px 12px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
}

/* 示例卡片 */
.example-item {
  cursor: pointer;
  padding: 8px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  background-color: #fff;
}

.example-item:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
}

.example-expr {
  font-family: "Fira Code", "Source Code Pro", monospace;
  font-size: 13px;
  color: var(--gray-800);
  display: block;
  margin-bottom: 4px;
}

.example-result {
  font-size: 12px;
  color: var(--gray-600);
  font-style: italic;
}

/* 参考表格 */
.reference-table th {
  background-color: var(--primary-light);
  color: white;
}

.reference-table .function-name,
.reference-table .operator-name {
  font-family: "Fira Code", "Source Code Pro", monospace;
  font-weight: 500;
}

/* 关于页面 */
.feature-icon {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.about-feature {
  padding: 15px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .CodeMirror {
    min-height: 80px;
    font-size: 13px;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .result-value {
    font-size: 14px;
  }
}

/* 动画效果 */
.btn {
  transition: all 0.3s ease;
}

.btn:active {
  transform: scale(0.96);
}

/* 排版细节 */
.code-block {
  font-family: "Fira Code", "Source Code Pro", monospace;
  background-color: var(--gray-100);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
}