/**
 * 规则引擎页面脚本
 * 提供规则引擎页面的交互功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // 规则引擎主要功能区域
    const ruleEngineSection = document.getElementById('ruleEngineSection');
    const examplesSection = document.getElementById('examplesSection');
    const referenceSection = document.getElementById('referenceSection');
    const aboutSection = document.getElementById('aboutSection');
    
    // 导航栏链接
    const examplesTab = document.getElementById('examplesTab');
    const referenceTab = document.getElementById('referenceTab');
    const aboutTab = document.getElementById('aboutTab');
    
    // 初始化导航切换功能
    setupNavigation();
    
    // 初始化规则管理功能
    setupRuleManagement();
    
    // 初始化规则编辑器
    setupRuleEditor();
    
    // 初始化代码编辑器
    setupCodeEditor();
    
    // 初始化示例规则加载功能
    initExampleRules();

    // 默认显示规则引擎主区域
    showSection(ruleEngineSection);

    /**
     * 设置导航切换功能
     */
    function setupNavigation() {
        examplesTab.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(examplesSection);
        });
        
        referenceTab.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(referenceSection);
        });
        
        aboutTab.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(aboutSection);
        });
    }
    
    /**
     * 显示指定区域，隐藏其他区域
     * @param {HTMLElement} section 要显示的区域
     */
    function showSection(section) {
        // 隐藏所有区域
        ruleEngineSection.classList.add('d-none');
        examplesSection.classList.add('d-none');
        referenceSection.classList.add('d-none');
        aboutSection.classList.add('d-none');
        
        // 显示指定区域
        section.classList.remove('d-none');
        
        // 更新导航栏高亮
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(link => link.classList.remove('active'));
        
        if (section === examplesSection) {
            examplesTab.classList.add('active');
        } else if (section === referenceSection) {
            referenceTab.classList.add('active');
        } else if (section === aboutSection) {
            aboutTab.classList.add('active');
        } else {
            document.querySelector('.navbar-nav .nav-link[href="rules.html"]').classList.add('active');
        }
    }
    
    /**
     * 设置规则管理功能
     */
    function setupRuleManagement() {
        const createRuleBtn = document.getElementById('createRuleBtn');
        const editRuleButtons = document.querySelectorAll('.edit-rule');
        const copyRuleButtons = document.querySelectorAll('.copy-rule');
        const deleteRuleButtons = document.querySelectorAll('.delete-rule');
        const searchRuleBtn = document.getElementById('searchRuleBtn');
        const searchRuleInput = document.getElementById('searchRuleInput');
        const ruleEditorCard = document.getElementById('ruleEditorCard');
        const saveRuleBtn = document.getElementById('saveRuleBtn');
        const cancelEditBtn = document.getElementById('cancelEditBtn');
        const closeEditorBtn = document.getElementById('closeEditorBtn'); // Added close button
        
        // 新建规则
        createRuleBtn.addEventListener('click', function() {
            // 显示规则编辑器并清空表单
            ruleEditorCard.style.display = 'block';
            document.getElementById('ruleForm').reset();
            document.getElementById('ruleId').value = ''; // Clear rule ID for new rule
            // Clear dynamic conditions and actions
            document.querySelector('.rule-conditions-container').innerHTML = '';
            document.querySelector('.rule-actions-container').innerHTML = '';
            updateConditionPreview(); // Update preview for empty state
            document.getElementById('ruleName').focus();
            
            // 滚动到编辑区域
            ruleEditorCard.scrollIntoView({ behavior: 'smooth' });
        });
        
        // 编辑规则
        editRuleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const ruleId = this.getAttribute('data-rule-id');
                editRule(ruleId);
            });
        });
        
        // 复制规则
        copyRuleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const ruleId = this.getAttribute('data-rule-id');
                copyRule(ruleId);
            });
        });
        
        // 删除规则
        deleteRuleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const ruleId = this.getAttribute('data-rule-id');
                deleteRule(ruleId);
            });
        });
        
        // 搜索规则
        searchRuleBtn.addEventListener('click', function() {
            searchRules(searchRuleInput.value);
        });
        
        searchRuleInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchRules(this.value);
            }
        });
        
        // 保存规则 (Form submission)
        document.getElementById('ruleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveRule();
        });
        
        // 取消编辑 / 关闭编辑器
        const closeOrCancelEditor = function() {
            ruleEditorCard.style.display = 'none';
            // 滚动到规则列表顶部 (optional)
            // document.querySelector('.card:not(#ruleEditorCard)').scrollIntoView({ behavior: 'smooth' });
        };
        cancelEditBtn.addEventListener('click', closeOrCancelEditor);
        closeEditorBtn.addEventListener('click', closeOrCancelEditor); // Added listener for close button
    }
    
    /**
     * 编辑规则
     * @param {string} ruleId 规则ID
     */
    function editRule(ruleId) {
        console.log(`编辑规则: ${ruleId}`);
        
        // --- Mock Backend Data Fetch ---
        // In a real application, fetch rule details via API:
        // fetch(`/api/rules/${ruleId}`)
        //   .then(response => response.json())
        //   .then(ruleData => populateEditor(ruleData));
        
        // Mock data based on Go test cases
        let mockRuleData;
        if (ruleId === '1') { // Corresponds to rule-vip-001 (Priority 2)
            mockRuleData = {
                id: 'rule-vip-001',
                name: 'VIP城市规则',
                description: 'VIP用户且在北京，增加100积分并添加标签',
                priority: 2,
                enabled: true,
                condition: {
                    type: 'composite',
                    operator: 'and',
                    conditions: [
                        { type: 'simple', path: 'user.vip', operator: 'eq', value: true },
                        { type: 'simple', path: 'user.city', operator: 'eq', value: '北京' }
                    ]
                },
                actions: [
                    { type: 'modify_property', path: 'user.points', operator: 'add', value: 100 },
                    { type: 'append_to_array', path: 'user.tags', value: 'VIP专享' }
                ]
            };
        } else if (ruleId === '2') { // Corresponds to rule-order-001 (Priority 4)
             mockRuleData = {
                id: 'rule-order-001',
                name: '高价值客户规则',
                description: '订单金额超过200，标记为高价值客户',
                priority: 4,
                enabled: true,
                condition: { type: 'simple', path: 'user.orders[0].amount', operator: 'gt', value: 200 },
                actions: [
                    { type: 'append_to_array', path: 'user.tags', value: '高价值客户' }
                ]
            };
        } else if (ruleId === '3') { // Corresponds to rule-time-001 (Priority 1)
             mockRuleData = {
                id: 'rule-time-001',
                name: '新用户规则',
                description: '30天内加入的用户标记为新用户',
                priority: 1,
                enabled: false, // Example: disabled rule
                condition: { type: 'time', path: 'user.joinTime', operator: 'in_last', value: '30d', isRelative: true },
                actions: [
                    { type: 'append_to_array', path: 'user.tags', value: '新用户' }
                ]
            };
        } else {
            alert(`未找到ID为 ${ruleId} 的规则模拟数据`);
            return;
        }
        // --- End Mock Data ---
        
        populateEditor(mockRuleData);
        
        // 显示规则编辑器
        const ruleEditorCard = document.getElementById('ruleEditorCard');
        ruleEditorCard.style.display = 'block';
        ruleEditorCard.scrollIntoView({ behavior: 'smooth' });
    }
    
    /**
     * Populate the rule editor form with data
     * @param {object} ruleData The rule data object from backend
     */
    function populateEditor(ruleData) {
        document.getElementById('ruleId').value = ruleData.id || '';
        document.getElementById('ruleName').value = ruleData.name || '';
        document.getElementById('ruleDescription').value = ruleData.description || '';
        document.getElementById('rulePriority').value = ruleData.priority || 10;
        document.getElementById('ruleStatus').checked = ruleData.enabled !== undefined ? ruleData.enabled : true;
        
        const conditionsContainer = document.querySelector('.rule-conditions-container');
        conditionsContainer.innerHTML = ''; // Clear existing conditions
        if (ruleData.condition) {
            addConditionElement(conditionsContainer, ruleData.condition);
        }
        
        const actionsContainer = document.querySelector('.rule-actions-container');
        actionsContainer.innerHTML = ''; // Clear existing actions
        if (ruleData.actions && Array.isArray(ruleData.actions)) {
            ruleData.actions.forEach(actionData => addActionElement(actionsContainer, actionData));
        }
        
        updateConditionPreview();
    }
    
    /**
     * 复制规则
     * @param {string} ruleId 规则ID
     */
    function copyRule(ruleId) {
        console.log(`复制规则: ${ruleId}`);
        
        // Fetch original rule data first
        editRule(ruleId); // Load data into editor
        
        // Modify fields for a copy
        document.getElementById('ruleId').value = ''; // Clear ID for new rule
        const ruleNameInput = document.getElementById('ruleName');
        ruleNameInput.value = ruleNameInput.value + ' (副本)';
        ruleNameInput.focus();
    }
    
    /**
     * 删除规则
     * @param {string} ruleId 规则ID
     */
    function deleteRule(ruleId) {
        // Find the actual rule ID from the button's data attribute if needed
        const actualRuleId = document.querySelector(`.delete-rule[data-rule-id="${ruleId}"]`)?.closest('tr')?.cells[0]?.textContent || ruleId;
        
        if (confirm(`确定要删除ID为 ${actualRuleId} 的规则吗？`)) {
            console.log(`删除规则: ${actualRuleId}`);
            
            // --- Mock Backend Call ---
            // fetch(`/api/rules/${actualRuleId}`, { method: 'DELETE' })
            //   .then(response => {
            //     if (response.ok) {
            //       const row = document.querySelector(`[data-rule-id="${ruleId}"]`).closest('tr');
            //       row.remove();
            //       alert('规则删除成功!');
            //     } else {
            //       alert('删除失败!');
            //     }
            //   });
            // --- End Mock ---
            
            // Frontend removal for demo
            const row = document.querySelector(`button[data-rule-id="${ruleId}"]`)?.closest('tr');
            if (row) {
                row.remove();
                alert('规则删除成功 (模拟)!');
            } else {
                 alert(`未找到规则行 (模拟)`);
            }
        }
    }
    
    /**
     * 搜索规则
     * @param {string} keyword 搜索关键词
     */
    function searchRules(keyword) {
        console.log(`搜索规则: ${keyword}`);
        
        // 实际应用中应该调用后端API搜索规则
        // 这里仅作演示，简单过滤表格
        const keyword_lower = keyword.toLowerCase();
        const rows = document.querySelectorAll('#rulesTable tbody tr');
        
        rows.forEach(row => {
            const ruleName = row.cells[1].textContent.toLowerCase();
            const ruleDesc = row.cells[2].textContent.toLowerCase();
            
            if (keyword === '' || ruleName.includes(keyword_lower) || ruleDesc.includes(keyword_lower)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    
    /**
     * 保存规则 (Collects data and simulates API call)
     */
    function saveRule() {
        console.log('保存规则');
        
        // 获取基本信息
        const ruleId = document.getElementById('ruleId').value;
        const ruleName = document.getElementById('ruleName').value;
        const ruleDescription = document.getElementById('ruleDescription').value;
        const rulePriority = parseInt(document.getElementById('rulePriority').value, 10) || 10;
        const ruleStatus = document.getElementById('ruleStatus').checked;
        
        // 验证必填项
        if (!ruleName) {
            alert('请输入规则名称');
            document.getElementById('ruleName').focus();
            return;
        }
        
        // 收集条件数据
        const conditionsContainer = document.querySelector('.rule-conditions-container');
        const rootConditionElement = conditionsContainer.querySelector(':scope > .condition-group, :scope > .condition-card'); // Get the top-level element
        const conditionData = rootConditionElement ? parseConditionElement(rootConditionElement) : null;
        
        // 收集动作数据
        const actionsContainer = document.querySelector('.rule-actions-container');
        const actionElements = actionsContainer.querySelectorAll(':scope > .action-card');
        const actionData = Array.from(actionElements).map(parseActionElement);
        
        // 构建规则数据对象 (matching Go structure)
        const ruleData = {
            id: ruleId || undefined, // Send undefined/null for new rules, let backend generate ID
            name: ruleName,
            description: ruleDescription,
            priority: rulePriority,
            enabled: ruleStatus,
            condition: conditionData,
            actions: actionData,
        };
        
        // 打印保存的数据（实际应用中应该调用API保存）
        console.log('规则数据:', JSON.stringify(ruleData, null, 2));
        
        // --- Mock Backend Call ---
        const method = ruleId ? 'PUT' : 'POST';
        const url = ruleId ? `/api/rules/${ruleId}` : '/api/rules';
        console.log(`模拟 ${method} 请求到 ${url}`);
        // fetch(url, {
        //     method: method,
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(ruleData)
        // })
        // .then(response => response.json())
        // .then(savedRule => {
        //     console.log('保存成功:', savedRule);
        //     alert('规则保存成功!');
        //     // Refresh rule list or update the specific row
        //     document.getElementById('closeEditorBtn').click(); // Close editor on success
        // })
        // .catch(error => {
        //      console.error('保存失败:', error);
        //      alert('规则保存失败!');
        // });
        // --- End Mock ---
        
        alert('规则保存成功 (模拟)!');
        document.getElementById('closeEditorBtn').click(); // Close editor on success (simulation)
        // TODO: Refresh rule list or update row in a real app
    }
    
    /**
     * Recursively parse condition elements from the editor
     * @param {HTMLElement} element The condition card or group element
     * @returns {object | null} Parsed condition data or null if invalid
     */
    function parseConditionElement(element) {
        if (element.classList.contains('condition-card')) {
            // Simple, Regex, Time, Function Condition Card
            const typeSelect = element.querySelector('.condition-type-select');
            const pathInput = element.querySelector('.condition-path-input');
            const operatorSelect = element.querySelector('.condition-operator-select');
            const valueInput = element.querySelector('.condition-value-input'); // Might be text, number, etc.
            const valueInput2 = element.querySelector('.condition-value-input-2'); // For range operators etc.
            const caseInsensitiveCheckbox = element.querySelector('.condition-case-insensitive');
            const isRelativeCheckbox = element.querySelector('.condition-is-relative');
            const funcArgsInput = element.querySelector('.condition-func-args');
            
            const type = typeSelect?.value;
            const path = pathInput?.value.trim();
            const operator = operatorSelect?.value;
            let value = valueInput?.value;
            
            if (!type || !path || !operator) return null; // Basic validation
            
            // Attempt to parse value based on context (simple approach)
            if (valueInput?.type === 'number') {
                value = parseFloat(value);
            } else if (valueInput?.type === 'checkbox') {
                 value = valueInput.checked;
            } else if (typeof value === 'string') {
                if (value.toLowerCase() === 'true') value = true;
                else if (value.toLowerCase() === 'false') value = false;
                else if (!isNaN(value) && value.trim() !== '') value = parseFloat(value); // Try parsing as number if it looks like one
            }
            
            const condition = { type: type, path: path, operator: operator, value: value };
            
            if (type === 'regex' && caseInsensitiveCheckbox) {
                condition.caseInsensitive = caseInsensitiveCheckbox.checked;
            }
            if (type === 'time' && isRelativeCheckbox) {
                condition.isRelative = isRelativeCheckbox.checked;
            }
             if (type === 'function' && funcArgsInput) {
                try {
                    // Assuming args are comma-separated paths
                    condition.argPaths = funcArgsInput.value.split(',').map(s => s.trim()).filter(s => s);
                    // The 'path' field becomes the function name for function conditions
                    condition.functionName = path;
                    delete condition.path;
                } catch (e) { console.error("Error parsing function args:", e); }
            }
            // Handle operators requiring a second value (e.g., between) if added later
            // if (valueInput2) { condition.value2 = valueInput2.value; }
            
            return condition;
            
        } else if (element.classList.contains('condition-group')) {
            // Composite Condition Group
            const operatorSelect = element.querySelector('.composite-operator-select');
            const subConditionElements = element.querySelectorAll(':scope > .condition-group-body > .condition-group, :scope > .condition-group-body > .condition-card');
            const operator = operatorSelect?.value;
            
            if (!operator) return null;
            
            const conditions = Array.from(subConditionElements)
                                    .map(parseConditionElement)
                                    .filter(c => c !== null); // Filter out any invalid sub-conditions
            
            if (conditions.length === 0) return null; // Don't save empty groups
            
            // Simplify NOT: If a NOT group has only one child, represent it directly
            if (operator === 'not' && conditions.length === 1) {
                 return { type: 'composite', operator: 'not', conditions: conditions };
            }
             // Simplify AND/OR: If a group has only one child, return the child directly
            if ((operator === 'and' || operator === 'or') && conditions.length === 1) {
                return conditions[0];
            }
            
            return { type: 'composite', operator: operator, conditions: conditions };
        }
        return null; // Should not happen
    }
    
    /**
     * Parse an action element from the editor
     * @param {HTMLElement} element The action card element
     * @returns {object | null} Parsed action data or null if invalid
     */
    function parseActionElement(element) {
        const typeSelect = element.querySelector('.action-type-select');
        const paramsContainer = element.querySelector('.action-params');
        
        const type = typeSelect?.value;
        if (!type) return null;
        
        const action = { type: type };
        
        // Extract parameters based on action type
        switch (type) {
            case 'set_property':
            case 'append_to_array': {
                const pathInput = paramsContainer.querySelector('input:nth-child(1)');
                const valueInput = paramsContainer.querySelector('input:nth-child(2), select:nth-child(2)'); // Handle boolean select
                if (!pathInput || !valueInput) return null;
                action.path = pathInput.value.trim();
                action.value = parseValueInput(valueInput);
                break;
            }
            case 'modify_property': {
                const pathInput = paramsContainer.querySelector('input:nth-child(1)');
                const operatorSelect = paramsContainer.querySelector('select:nth-child(2)');
                const valueInput = paramsContainer.querySelector('input:nth-child(3)');
                 if (!pathInput || !operatorSelect || !valueInput) return null;
                action.path = pathInput.value.trim();
                action.operator = operatorSelect.value;
                action.value = parseValueInput(valueInput);
                break;
            }
            case 'log': {
                const messageInput = paramsContainer.querySelector('input:nth-child(1)');
                const levelSelect = paramsContainer.querySelector('select:nth-child(2)');
                if (!messageInput || !levelSelect) return null;
                action.message = messageInput.value;
                action.level = levelSelect.value;
                // action.template = paramsContainer.querySelector('input:nth-child(3)')?.value; // Optional template
                break;
            }
             case 'function': {
                const funcNameInput = paramsContainer.querySelector('input:nth-child(1)');
                const paramsInput = paramsContainer.querySelector('input:nth-child(2)'); // Assuming JSON string for params map
                 if (!funcNameInput) return null;
                action.functionName = funcNameInput.value.trim();
                 try {
                    action.params = paramsInput?.value ? JSON.parse(paramsInput.value) : {};
                 } catch (e) {
                    console.error("Invalid JSON for function params:", paramsInput?.value);
                    action.params = {};
                 }
                break;
            }
            // TODO: Add parsing for 'conditional' and 'composite' actions if UI is added
            default:
                console.warn(`Unknown action type for parsing: ${type}`);
                return null;
        }
        
         // Basic validation for path
        if (action.path === '') return null;
        
        return action;
    }
    
    /**
     * Helper to parse value from different input types
     */
    function parseValueInput(inputElement) {
        if (!inputElement) return null;
        let value = inputElement.value;
         if (inputElement.tagName === 'SELECT') {
             if (value === 'true') return true;
             if (value === 'false') return false;
             return value; // Return string value for other selects
         }
         if (inputElement.type === 'number') {
            return parseFloat(value);
         }
         if (inputElement.type === 'checkbox') {
            return inputElement.checked;
         }
         // Attempt smart parsing for text inputs
         if (typeof value === 'string') {
            if (value.toLowerCase() === 'true') return true;
            if (value.toLowerCase() === 'false') return false;
            if (!isNaN(value) && value.trim() !== '') return parseFloat(value); // Try number
            // TODO: Could try JSON parsing for arrays/objects if needed
         }
        return value; // Return as string by default
    }
    
    /**
     * 设置规则编辑器功能 (Condition & Action UI)
     */
    function setupRuleEditor() {
        const addConditionBtn = document.getElementById('addConditionBtn');
        const conditionsContainer = document.querySelector('.rule-conditions-container');
        const addActionBtn = document.getElementById('addActionBtn');
        const actionsContainer = document.querySelector('.rule-actions-container');
        
        // --- Condition Operators ---
        const simpleOperators = {
            'eq': '== (等于)', 'neq': '!= (不等于)', 'gt': '> (大于)', 'gte': '>= (大于等于)',
            'lt': '< (小于)', 'lte': '<= (小于等于)', 'contains': 'Contains (包含)',
            'not_contains': 'Not Contains (不包含)', 'starts_with': 'Starts With (开头是)',
            'ends_with': 'Ends With (结尾是)'
        };
        const timeOperators = {
            'before': 'Before (早于)', 'after': 'After (晚于)', 'on': 'On (同一天)',
            'in_last': 'In Last (过去)', 'not_in_last': 'Not In Last (不在过去)'
        };
         const functionOperators = { // Operators for comparing function result
            'eq': '==', 'neq': '!=', 'gt': '>', 'gte': '>=', 'lt': '<', 'lte': '<='
        };
        
        // --- Action Types ---
        const actionTypes = {
            'set_property': 'Set Property (设置属性)',
            'modify_property': 'Modify Property (修改属性)',
            'append_to_array': 'Append To Array (追加数组)',
            'log': 'Log Message (记录日志)',
            'function': 'Call Function (调用函数)',
            // 'conditional': 'Conditional Action (条件动作)', // UI TBD
            // 'composite': 'Composite Action (组合动作)' // UI TBD
        };
        const modifyOperators = {
            'add': '+ (加)', 'sub': '- (减)', 'mul': '* (乘)', 'div': '/ (除)', 'concat': '++ (拼接)'
        };
        
        // 添加条件/条件组
        addConditionBtn.addEventListener('click', function() {
            // If container is empty or only has simple conditions, add a simple one.
            // If it has a composite condition, add inside the first one (basic logic).
            const firstChild = conditionsContainer.firstElementChild;
            if (!firstChild || firstChild.classList.contains('condition-card')) {
                 addConditionElement(conditionsContainer, { type: 'simple' }); // Add simple by default
            } else if (firstChild.classList.contains('condition-group')) {
                 const groupBody = firstChild.querySelector('.condition-group-body');
                 addConditionElement(groupBody, { type: 'simple' }); // Add inside existing group
            }
             updateConditionPreview();
        });
        
        // 添加动作
        addActionBtn.addEventListener('click', function() {
            addActionElement(actionsContainer, { type: 'set_property' }); // Default to set_property
        });
        
        /**
         * Dynamically adds a condition element (card or group) to the container
         * @param {HTMLElement} container The parent element
         * @param {object} conditionData Optional data to populate the element
         */
        window.addConditionElement = function(container, conditionData = {}) {
            if (conditionData.type === 'composite') {
                const group = createConditionGroupElement(conditionData);
                container.appendChild(group);
                // Recursively add children
                if (conditionData.conditions && Array.isArray(conditionData.conditions)) {
                    const groupBody = group.querySelector('.condition-group-body');
                    conditionData.conditions.forEach(subCond => addConditionElement(groupBody, subCond));
                }
            } else {
                // Default to simple or handle other specific types
                const card = createConditionCardElement(conditionData);
                container.appendChild(card);
            }
        }
        
        /**
         * Creates a condition group (AND/OR/NOT) HTML element
         */
        function createConditionGroupElement(groupData = {}) {
            const operator = groupData.operator || 'and'; // Default to AND
            const groupId = `group-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
            
            const groupDiv = document.createElement('div');
            groupDiv.className = 'card mb-2 condition-group';
            groupDiv.dataset.groupId = groupId;
            groupDiv.innerHTML = `
                <div class="card-header p-2 bg-light border-bottom">
                    <div class="d-flex align-items-center">
                        <select class="form-select form-select-sm me-2 composite-operator-select" style="width: 80px;">
                            <option value="and" ${operator === 'and' ? 'selected' : ''}>AND</option>
                            <option value="or" ${operator === 'or' ? 'selected' : ''}>OR</option>
                            <option value="not" ${operator === 'not' ? 'selected' : ''}>NOT</option>
                        </select>
                        <span class="flex-grow-1"></span>
                        <button type="button" class="btn btn-sm btn-outline-success me-1 add-condition-to-group" title="Add Condition">
                            <i class="fas fa-plus"></i>
                        </button>
                         <button type="button" class="btn btn-sm btn-outline-primary me-1 add-group-to-group" title="Add Group">
                            <i class="fas fa-object-group"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-element" title="Remove Group">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-2 condition-group-body" style="border-left: 3px solid #eee; margin-left: 10px; padding-left: 10px !important;">
                    <!-- Nested conditions/groups go here -->
                </div>
            `;
            
            // Event Listeners
            groupDiv.querySelector('.composite-operator-select').addEventListener('change', updateConditionPreview);
            groupDiv.querySelector('.remove-element').addEventListener('click', () => {
                groupDiv.remove();
                updateConditionPreview();
            });
            groupDiv.querySelector('.add-condition-to-group').addEventListener('click', () => {
                addConditionElement(groupDiv.querySelector('.condition-group-body'), { type: 'simple' });
                updateConditionPreview();
            });
             groupDiv.querySelector('.add-group-to-group').addEventListener('click', () => {
                addConditionElement(groupDiv.querySelector('.condition-group-body'), { type: 'composite', operator: 'and' });
                updateConditionPreview();
            });
            
            return groupDiv;
        }
        
        /**
         * Creates a single condition card (Simple, Regex, Time, Function) HTML element
         */
        function createConditionCardElement(conditionData = {}) {
            const type = conditionData.type || 'simple';
            const path = conditionData.path || (type === 'function' ? conditionData.functionName : '') || '';
            const operator = conditionData.operator || (type === 'simple' ? 'eq' : (type === 'time' ? 'on' : (type === 'function' ? 'eq' : 'contains')));
            let value = conditionData.value !== undefined ? conditionData.value : '';
            const caseInsensitive = conditionData.caseInsensitive || false;
            const isRelative = conditionData.isRelative || false;
            const argPaths = conditionData.argPaths || [];
            
            const card = document.createElement('div');
            card.className = 'card mb-2 condition-card';
            card.innerHTML = `
                <div class="card-body p-2">
                    <div class="d-flex align-items-center flex-wrap">
                        <select class="form-select form-select-sm me-2 mb-1 condition-type-select" style="width: 120px;">
                            <option value="simple" ${type === 'simple' ? 'selected' : ''}>Simple</option>
                            <option value="regex" ${type === 'regex' ? 'selected' : ''}>Regex</option>
                            <option value="time" ${type === 'time' ? 'selected' : ''}>Time</option>
                            <option value="function" ${type === 'function' ? 'selected' : ''}>Function</option>
                        </select>
                        <input type="text" class="form-control form-control-sm me-2 mb-1 flex-grow-1 condition-path-input"
                               placeholder="${type === 'function' ? 'Function Name' : 'Path (e.g., user.age)'}" value="${path}">
                        <select class="form-select form-select-sm me-2 mb-1 condition-operator-select" style="width: 150px;">
                            <!-- Operators populated dynamically -->
                        </select>
                        <input type="text" class="form-control form-control-sm me-2 mb-1 condition-value-input" placeholder="Value" value="${value}">
                        <!-- Optional elements based on type -->
                        <div class="form-check form-check-inline me-2 mb-1 condition-regex-options" style="display: ${type === 'regex' ? 'inline-block' : 'none'};">
                            <input class="form-check-input condition-case-insensitive" type="checkbox" ${caseInsensitive ? 'checked' : ''}>
                            <label class="form-check-label small">Ignore Case</label>
                        </div>
                         <div class="form-check form-check-inline me-2 mb-1 condition-time-options" style="display: ${type === 'time' ? 'inline-block' : 'none'};">
                            <input class="form-check-input condition-is-relative" type="checkbox" ${isRelative ? 'checked' : ''}>
                            <label class="form-check-label small">Relative</label>
                        </div>
                         <input type="text" class="form-control form-control-sm me-2 mb-1 condition-func-args"
                               placeholder="Arg Paths (comma-sep)" value="${argPaths.join(', ')}" style="display: ${type === 'function' ? 'block' : 'none'}; width: 200px;">
                        
                        <button type="button" class="btn btn-sm btn-outline-danger mb-1 remove-element">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            
            const typeSelect = card.querySelector('.condition-type-select');
            const operatorSelect = card.querySelector('.condition-operator-select');
            const pathInput = card.querySelector('.condition-path-input');
            const regexOptions = card.querySelector('.condition-regex-options');
            const timeOptions = card.querySelector('.condition-time-options');
            const funcArgsInput = card.querySelector('.condition-func-args');
            const valueInput = card.querySelector('.condition-value-input');
            
            // Populate operators based on initial type
            populateOperators(operatorSelect, type, operator);
            
            // Event Listeners
            typeSelect.addEventListener('change', function() {
                const newType = this.value;
                populateOperators(operatorSelect, newType);
                // Show/hide type-specific options
                regexOptions.style.display = newType === 'regex' ? 'inline-block' : 'none';
                timeOptions.style.display = newType === 'time' ? 'inline-block' : 'none';
                funcArgsInput.style.display = newType === 'function' ? 'block' : 'none';
                pathInput.placeholder = newType === 'function' ? 'Function Name' : 'Path (e.g., user.age)';
                // Reset value type if needed (e.g., time might need specific format)
                valueInput.type = 'text'; // Reset to text
                valueInput.value = ''; // Clear value on type change
                updateConditionPreview();
            });
            
            card.querySelector('.remove-element').addEventListener('click', function() {
                card.remove();
                updateConditionPreview();
            });
            
            // Update preview on any input change
            card.querySelectorAll('select, input').forEach(input => {
                input.addEventListener('change', updateConditionPreview);
                input.addEventListener('keyup', updateConditionPreview); // For text inputs
            });
            
            return card;
        }
        
        /** Populate operator dropdown based on condition type */
        function populateOperators(selectElement, conditionType, selectedOperator) {
            let operators;
            switch (conditionType) {
                case 'regex': operators = { 'matches': 'Matches', 'not_matches': 'Not Matches' }; break;
                case 'time': operators = timeOperators; break;
                case 'function': operators = functionOperators; break;
                case 'simple':
                default: operators = simpleOperators; break;
            }
            selectElement.innerHTML = ''; // Clear existing
            for (const [value, text] of Object.entries(operators)) {
                const option = document.createElement('option');
                option.value = value;
                option.textContent = text;
                if (value === selectedOperator) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            }
        }
        
        /**
         * Dynamically adds an action element to the container
         * @param {HTMLElement} container The parent element
         * @param {object} actionData Optional data to populate the element
         */
        window.addActionElement = function(container, actionData = {}) {
            const type = actionData.type || 'set_property';
            
            const actionCard = document.createElement('div');
            actionCard.className = 'card mb-2 action-card';
            actionCard.innerHTML = `
                <div class="card-body p-2">
                    <div class="d-flex align-items-center mb-2">
                        <select class="form-select form-select-sm me-2 action-type-select" style="width: 200px;">
                            ${Object.entries(actionTypes).map(([value, text]) =>
                                `<option value="${value}" ${type === value ? 'selected' : ''}>${text}</option>`
                            ).join('')}
                        </select>
                        <span class="flex-grow-1"></span>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-element">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="action-params">
                        <!-- Parameters populated dynamically -->
                    </div>
                </div>
            `;
            
            const typeSelect = actionCard.querySelector('.action-type-select');
            const paramsContainer = actionCard.querySelector('.action-params');
            
            // Populate initial parameters
            updateActionParams(paramsContainer, type, actionData);
            
            // Event Listeners
            typeSelect.addEventListener('change', function() {
                updateActionParams(paramsContainer, this.value); // Pass empty data on type change
            });
            
            actionCard.querySelector('.remove-element').addEventListener('click', function() {
                actionCard.remove();
            });
            
            container.appendChild(actionCard);
        }
        
        /** Update action parameter inputs based on selected type */
        function updateActionParams(paramsContainer, actionType, actionData = {}) {
            paramsContainer.innerHTML = ''; // Clear existing params
            
            switch (actionType) {
                case 'set_property':
                    paramsContainer.innerHTML = `
                        <div class="row g-2">
                            <div class="col-md-5">
                                <input type="text" class="form-control form-control-sm" placeholder="Path (e.g., user.verified)" value="${actionData.path || ''}">
                            </div>
                            <div class="col-md-5">
                                <!-- Basic value input, could be enhanced for type -->
                                <input type="text" class="form-control form-control-sm" placeholder="Value (e.g., true, 'active', 10)" value="${actionData.value !== undefined ? actionData.value : ''}">
                            </div>
                             <div class="col-md-2">
                                <select class="form-select form-select-sm value-type-hint" title="Value Type Hint">
                                    <option value="auto">Auto</option>
                                    <option value="string">String</option>
                                    <option value="number">Number</option>
                                    <option value="boolean">Boolean</option>
                                </select>
                            </div>
                        </div>`;
                    // Add boolean select specifically if needed
                     if (typeof actionData.value === 'boolean') {
                         paramsContainer.querySelector('.col-md-5:nth-child(2)').innerHTML = `
                            <select class="form-select form-select-sm">
                                <option value="true" ${actionData.value === true ? 'selected' : ''}>True</option>
                                <option value="false" ${actionData.value === false ? 'selected' : ''}>False</option>
                            </select>`;
                         paramsContainer.querySelector('.value-type-hint').value = 'boolean';
                     } else if (typeof actionData.value === 'number') {
                         paramsContainer.querySelector('.value-type-hint').value = 'number';
                     } else if (typeof actionData.value === 'string') {
                          paramsContainer.querySelector('.value-type-hint').value = 'string';
                     }
                    
                    break;
                
                case 'modify_property':
                    paramsContainer.innerHTML = `
                        <div class="row g-2">
                            <div class="col-md-4">
                                <input type="text" class="form-control form-control-sm" placeholder="Path (e.g., user.points)" value="${actionData.path || ''}">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm">
                                     ${Object.entries(modifyOperators).map(([value, text]) =>
                                        `<option value="${value}" ${actionData.operator === value ? 'selected' : ''}>${text}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-5">
                                <input type="text" class="form-control form-control-sm" placeholder="Value (e.g., 10, 'suffix')" value="${actionData.value !== undefined ? actionData.value : ''}">
                            </div>
                        </div>`;
                    break;
                
                case 'append_to_array':
                     paramsContainer.innerHTML = `
                        <div class="row g-2">
                            <div class="col-md-6">
                                <input type="text" class="form-control form-control-sm" placeholder="Array Path (e.g., user.tags)" value="${actionData.path || ''}">
                            </div>
                            <div class="col-md-6">
                                <input type="text" class="form-control form-control-sm" placeholder="Value to Append" value="${actionData.value !== undefined ? actionData.value : ''}">
                            </div>
                        </div>`;
                    break;
                
                case 'log':
                    paramsContainer.innerHTML = `
                        <div class="row g-2">
                            <div class="col-md-8">
                                <input type="text" class="form-control form-control-sm" placeholder="Log Message" value="${actionData.message || ''}">
                            </div>
                            <div class="col-md-4">
                                <select class="form-select form-select-sm">
                                    <option value="info" ${actionData.level === 'info' ? 'selected' : ''}>Info</option>
                                    <option value="warn" ${actionData.level === 'warn' ? 'selected' : ''}>Warning</option>
                                    <option value="error" ${actionData.level === 'error' ? 'selected' : ''}>Error</option>
                                    <option value="debug" ${actionData.level === 'debug' ? 'selected' : ''}>Debug</option>
                                </select>
                            </div>
                            <!-- Optional Template Input
                            <div class="col-12 mt-1">
                                <input type="text" class="form-control form-control-sm" placeholder="Optional Message Template" value="${actionData.template || ''}">
                            </div>
                            -->
                        </div>`;
                    break;
                 case 'function':
                    paramsContainer.innerHTML = `
                        <div class="row g-2">
                            <div class="col-md-5">
                                <input type="text" class="form-control form-control-sm" placeholder="Function Name" value="${actionData.functionName || ''}">
                            </div>
                            <div class="col-md-7">
                                <input type="text" class="form-control form-control-sm" placeholder='Params (JSON map, e.g., {"key": "value"})' value="${actionData.params ? JSON.stringify(actionData.params) : ''}">
                            </div>
                        </div>`;
                    break;
                
                // TODO: Add UI for 'conditional' and 'composite' actions
                default:
                    paramsContainer.innerHTML = `<small class="text-muted">No parameters for this action type.</small>`;
            }
        }
        
        // --- Initial Condition Preview Update ---
        updateConditionPreview(); // Call initially
    }
    
    /**
     * Update the condition expression preview text
     */
    function updateConditionPreview() {
        const conditionsContainer = document.querySelector('.rule-conditions-container');
        const rootElement = conditionsContainer.querySelector(':scope > .condition-group, :scope > .condition-card');
        let previewText = rootElement ? generatePreviewText(rootElement) : '未定义条件';
        
        document.getElementById('conditionPreview').innerHTML = `<code>${previewText}</code>`;
    }
    
    /**
     * Recursively generate preview text for conditions
     * @param {HTMLElement} element Condition card or group
     * @returns {string} Preview text fragment
     */
    function generatePreviewText(element) {
        if (element.classList.contains('condition-card')) {
            const type = element.querySelector('.condition-type-select')?.value || 'simple';
            const path = element.querySelector('.condition-path-input')?.value || '?path?';
            const operator = element.querySelector('.condition-operator-select')?.value || '?op?';
            let value = element.querySelector('.condition-value-input')?.value || '?value?';
            const caseInsensitive = element.querySelector('.condition-case-insensitive')?.checked;
            const isRelative = element.querySelector('.condition-is-relative')?.checked;
            const funcArgs = element.querySelector('.condition-func-args')?.value;
            
            // Quote string values, handle boolean/numbers
            if (typeof value === 'string') {
                 if (value.toLowerCase() === 'true' || value.toLowerCase() === 'false' || !isNaN(value)) {
                     // Looks like boolean or number, don't quote
                 } else {
                     value = `"${value}"`; // Quote other strings
                 }
            }
            
            let operatorText = getOperatorText(operator);
            let text = '';
            
            switch (type) {
                case 'regex':
                    text = `${path} ${operatorText} /${value.replace(/['"]+/g, '')}/${caseInsensitive ? 'i' : ''}`;
                    break;
                case 'time':
                    text = `${path} ${operatorText} ${value}${isRelative ? ' (relative)' : ''}`;
                    break;
                 case 'function':
                    const args = funcArgs ? `(${funcArgs})` : '()';
                    text = `${path}${args} ${operatorText} ${value}`;
                    break;
                case 'simple':
                default:
                    text = `${path} ${operatorText} ${value}`;
                    break;
            }
            return text;
            
        } else if (element.classList.contains('condition-group')) {
            const operator = element.querySelector('.composite-operator-select')?.value.toUpperCase() || 'AND';
            const subElements = element.querySelectorAll(':scope > .condition-group-body > .condition-group, :scope > .condition-group-body > .condition-card');
            
            if (subElements.length === 0) return '';
            
            const subTexts = Array.from(subElements).map(generatePreviewText).filter(t => t);
            
            if (operator === 'NOT') {
                 // Handle NOT - needs only one child ideally
                 if (subTexts.length > 0) {
                     return `NOT (${subTexts[0]})`; // Only use first child for NOT preview
                 }
                 return 'NOT (...)';
            }
            
            if (subTexts.length === 1) {
                return subTexts[0]; // Don't wrap single child in unnecessary AND/OR
            }
            
            return `(${subTexts.join(` ${operator} `)})`;
        }
        return '';
    }
    
    /** Get readable text for operators */
    function getOperatorText(op) {
        const operators = {
            'eq': '==', 'neq': '!=', 'gt': '>', 'gte': '>=', 'lt': '<', 'lte': '<=',
            'contains': 'CONTAINS', 'not_contains': 'NOT CONTAINS', 'starts_with': 'STARTS WITH', 'ends_with': 'ENDS WITH',
            'matches': 'MATCHES', 'not_matches': 'NOT MATCHES',
            'before': 'BEFORE', 'after': 'AFTER', 'on': 'ON', 'in_last': 'IN LAST', 'not_in_last': 'NOT IN LAST'
        };
        return operators[op] || op; // Fallback to the value itself
    }
    
    /**
     * 设置代码编辑器 (CodeMirror)
     */
    function setupCodeEditor() {
        const testInputEditorEl = document.getElementById('testInputEditor');
        if (testInputEditorEl) {
            const editor = CodeMirror.fromTextArea(testInputEditorEl, {
                mode: "application/json", // Correct mode for JSON
                theme: "default", // Use default theme as requested
                lineNumbers: true,
                matchBrackets: true,
                autoCloseBrackets: true,
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                lint: true // Enable linting if json-lint is available
            });
            
            editor.setSize("100%", "300px"); // Increased height
            
            // Get initial JSON content and format it
            try {
                const initialJson = JSON.parse(testInputEditorEl.value || '{}');
                editor.setValue(JSON.stringify(initialJson, null, 2));
            } catch (e) {
                console.error("Initial JSON in textarea is invalid:", e);
                editor.setValue('{\n  "error": "Initial JSON was invalid"\n}');
            }
            
            // Store editor instance
            window.testInputEditor = editor;
            
            // Test Rule Button
            const testRuleBtn = document.getElementById('testRuleBtn');
            if (testRuleBtn) {
                testRuleBtn.addEventListener('click', function() {
                    testRule(window.testInputEditor.getValue());
                });
            }
        }
    }
    
    /**
     * 测试规则 (模拟后端执行)
     * @param {string} inputJson 测试输入JSON
     */
    function testRule(inputJson) {
        console.log('测试规则输入:', inputJson);
        const outputArea = document.getElementById('testOutputArea');
        outputArea.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div> Simulating execution...';
        
        let inputData;
        try {
            inputData = JSON.parse(inputJson);
        } catch (e) {
            showTestError('JSON格式错误: ' + e.message);
            return;
        }
        
        // --- Simulate Backend Call ---
        // In a real app, send the current rule definition and input data
        // const currentRuleData = buildRuleDataForTest(); // Function to get current editor state
        // fetch('/api/rules/test', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify({ rule: currentRuleData, data: inputData })
        // })
        // .then(response => response.json())
        // .then(result => showTestResult(result))
        // .catch(error => showTestError('API Error: ' + error.message));
        
        // --- Mock Simulation ---
        // Simulate delay
        setTimeout(() => {
            try {
                 // Basic simulation - This needs to be much more sophisticated
                 // to actually evaluate the rules defined in the editor against inputData.
                 // For now, return a static mock result based on the input structure.
                const mockResult = {
                    matches: [],
                    errors: [],
                    executionTime: Math.random() * 50 + 5 // Random time
                };
                
                // Add some mock matches based on input for demo
                if (inputData?.user?.vip && inputData?.user?.city === '北京') {
                    mockResult.matches.push({
                        ruleId: 'rule-vip-001', // Assuming this ID exists if loaded
                        ruleName: 'VIP城市规则 (模拟)',
                        actionsExecuted: 2
                    });
                }
                 if (inputData?.user?.orders?.[0]?.amount > 200) {
                     mockResult.matches.push({
                        ruleId: 'rule-order-001',
                        ruleName: '高价值客户规则 (模拟)',
                        actionsExecuted: 1
                    });
                 }
                
                 // Simulate potential error during execution
                 if (inputData?.context?.source === 'invalid') {
                     mockResult.errors.push("Simulated error: Invalid context source.");
                 }
                
                // Simulate data modification (very basic)
                const outputData = JSON.parse(JSON.stringify(inputData)); // Deep copy
                if (mockResult.matches.some(m => m.ruleId === 'rule-vip-001')) {
                    if (outputData.user.tags && Array.isArray(outputData.user.tags)) {
                         if (!outputData.user.tags.includes('VIP专享')) {
                            outputData.user.tags.push('VIP专享');
                         }
                    }
                    if (typeof outputData.user.points === 'number') {
                        outputData.user.points += 100; // Simulate point addition
                    }
                }
                 if (mockResult.matches.some(m => m.ruleId === 'rule-order-001')) {
                     if (outputData.user.tags && Array.isArray(outputData.user.tags)) {
                         if (!outputData.user.tags.includes('高价值客户')) {
                            outputData.user.tags.push('高价值客户');
                         }
                     }
                 }
                
                mockResult.modifiedData = outputData; // Add modified data to result
                
                showTestResult(mockResult);
                
            } catch (simError) {
                console.error("Simulation Error:", simError);
                showTestError('模拟执行时出错: ' + simError.message);
            }
        }, 500); // 500ms delay
    }
    
    /**
     * 显示测试结果
     * @param {Object} result 测试结果对象 (simulated structure)
     */
    function showTestResult(result) {
        const outputArea = document.getElementById('testOutputArea');
        outputArea.innerHTML = ''; // Clear previous
        
        // Display Errors First
        if (result.errors && result.errors.length > 0) {
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger alert-sm';
            errorAlert.innerHTML = `<strong><i class="fas fa-exclamation-triangle me-1"></i> 执行错误:</strong><ul>${result.errors.map(e => `<li>${e}</li>`).join('')}</ul>`;
            outputArea.appendChild(errorAlert);
        }
        
        // Execution Summary
        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'mb-3';
        summaryDiv.innerHTML = `
            <div class="alert ${result.matches.length > 0 ? 'alert-success' : 'alert-secondary'} alert-sm mb-2">
                <i class="fas ${result.matches.length > 0 ? 'fa-check-circle' : 'fa-info-circle'} me-1"></i>
                ${result.matches.length > 0 ? `匹配 ${result.matches.length} 条规则` : '没有匹配的规则'}
            </div>
            <div class="small text-muted mb-2">
                模拟执行时间: ${result.executionTime.toFixed(2)} ms
            </div>
        `;
        outputArea.appendChild(summaryDiv);
        
        // Matched Rules Details
        if (result.matches.length > 0) {
            const matchesDiv = document.createElement('div');
            matchesDiv.className = 'mb-3';
            matchesDiv.innerHTML = `<h6>匹配规则详情:</h6>`;
            const listGroup = document.createElement('ul');
            listGroup.className = 'list-group list-group-sm mb-3';
            result.matches.forEach(match => {
                const li = document.createElement('li');
                li.className = 'list-group-item d-flex justify-content-between align-items-center';
                li.innerHTML = `
                    <span>${match.ruleName || match.ruleId}</span>
                    <span class="badge bg-primary rounded-pill" title="Executed Actions">${match.actionsExecuted || '?'}</span>
                `;
                listGroup.appendChild(li);
            });
            matchesDiv.appendChild(listGroup);
            outputArea.appendChild(matchesDiv);
        }
        
        // Modified Data Output (if available)
        if (result.modifiedData) {
            const dataDiv = document.createElement('div');
            dataDiv.innerHTML = `
                <h6>修改后的数据:</h6>
                <div class="bg-white p-2 border rounded">
                    <pre class="mb-0 small"><code>${JSON.stringify(result.modifiedData, null, 2)}</code></pre>
                </div>
            `;
             outputArea.appendChild(dataDiv);
        } else {
             outputArea.innerHTML += '<p><small><em>未检测到数据修改 (或模拟未实现)。</em></small></p>';
        }
    }
    
    /**
     * 显示测试错误
     * @param {string} errorMessage 错误信息
     */
    function showTestError(errorMessage) {
        const outputArea = document.getElementById('testOutputArea');
        
        outputArea.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-1"></i> 
                <strong>错误</strong>
                <p>${errorMessage}</p>
            </div>
        `;
    }
    
    /**
     * 初始化示例规则加载
     */
    function initExampleRules() {
        const exampleItems = document.querySelectorAll('.example-item');
        
        exampleItems.forEach(item => {
            item.addEventListener('click', function() {
                // 实际应用中应该从服务端获取示例规则详情
                // 这里简单模拟切换到规则引擎界面并加载示例规则
                showSection(ruleEngineSection);
                
                const ruleName = this.querySelector('code').textContent;
                alert(`已加载示例规则: ${ruleName}`);
            });
        });
    }
    
    // --- Object Pool for Results (Simple Implementation) ---
    // In a real high-performance scenario, you might pool frequently created objects
    // like action results or matched rules, but for typical UI interaction,
    // the overhead might outweigh the benefits. The Go backend uses pooling effectively.
    // Example stubs:
    function GetResultFromPool() { return { Matches: [], ErrorCollector: { errors: [], AddError: function(err) { this.errors.push(err); }, GetErrors: function() { return this.errors; } } }; }
    function PutResultToPool(result) { /* Reset result if needed */ }
    function GetMatchedRuleFromPool() { return { Rule: null, ActionResults: [] }; }
    function PutMatchedRuleToPool(matched) { matched.Rule = null; matched.ActionResults = []; }
    function GetActionResultFromPool() { return { Action: null, Error: null }; }
    function PutActionResultToPool(actionResult) { actionResult.Action = null; actionResult.Error = null; }
});