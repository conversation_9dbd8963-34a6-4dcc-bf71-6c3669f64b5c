/**
 * 表达式引擎 Web UI 交互脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化编辑器
    const editor = CodeMirror(document.getElementById("expressionEditor"), {
        mode: "javascript",
        theme: "default",
        lineNumbers: true,
        lineWrapping: true,
        autofocus: true,
        tabSize: 2,
        matchBrackets: true,
        autoCloseBrackets: true,
        placeholder: "在此输入表达式，例如：x + y > 10 ? 'Greater' : 'Less'"
    });
    
    // 默认填充一个简单的表达式
    editor.setValue("(x + y) * 2");
    
    // 初始化导航标签切换
    initTabNavigation();
    
    // 初始化变量表格操作
    initVariableTable();
    
    // 初始化示例操作
    initExamples();
    
    // 初始化函数和操作符插入按钮
    initInsertButtons(editor);
    
    // 监听计算按钮点击
    document.getElementById("evalBtn").addEventListener("click", function() {
        evaluateExpression(editor);
    });
    
    // 监听清除按钮点击
    document.getElementById("clearExpr").addEventListener("click", function() {
        editor.setValue("");
        editor.focus();
    });
    
    // 在表达式编辑器中按Enter键时自动计算
    editor.on("keydown", function(cm, event) {
        if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();
            evaluateExpression(editor);
        }
    });
});

/**
 * 初始化导航标签切换
 */
function initTabNavigation() {
    // 导航标签切换
    const tabs = ["calculator", "examples", "reference", "about"];
    tabs.forEach(tab => {
        document.getElementById(`${tab}Tab`).addEventListener("click", function(e) {
            e.preventDefault();
            
            // 隐藏所有section
            tabs.forEach(t => {
                document.getElementById(`${t}Section`).classList.add("d-none");
                document.getElementById(`${t}Tab`).classList.remove("active");
            });
            
            // 显示选中的section
            document.getElementById(`${tab}Section`).classList.remove("d-none");
            document.getElementById(`${tab}Tab`).classList.add("active");
        });
    });
}

/**
 * 初始化变量表格操作
 */
function initVariableTable() {
    // 添加变量行
    document.getElementById("addVarBtn").addEventListener("click", function() {
        const tbody = document.querySelector("#variablesTable tbody");
        const newRow = document.createElement("tr");
        
        newRow.innerHTML = `
            <td><input type="text" class="form-control form-control-sm var-name" value="var${tbody.children.length + 1}"></td>
            <td>
                <select class="form-select form-select-sm var-type">
                    <option value="number" selected>数字</option>
                    <option value="string">字符串</option>
                    <option value="boolean">布尔值</option>
                    <option value="array">数组</option>
                    <option value="object">对象</option>
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm var-value" value="0"></td>
            <td class="text-center">
                <button class="btn btn-sm btn-outline-danger delete-var">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(newRow);
        
        // 添加删除按钮事件监听
        newRow.querySelector(".delete-var").addEventListener("click", deleteVariableRow);
        
        // 变量类型改变时更新默认值
        newRow.querySelector(".var-type").addEventListener("change", updateDefaultValue);
    });
    
    // 添加现有行的删除按钮事件监听
    document.querySelectorAll(".delete-var").forEach(btn => {
        btn.addEventListener("click", deleteVariableRow);
    });
    
    // 为现有的变量类型选择器添加事件监听
    document.querySelectorAll(".var-type").forEach(select => {
        select.addEventListener("change", updateDefaultValue);
    });
}

/**
 * 删除变量行
 */
function deleteVariableRow() {
    // 如果只有一行，不允许删除
    const tbody = document.querySelector("#variablesTable tbody");
    if (tbody.children.length <= 1) {
        showErrorMessage("变量表格至少需要保留一个变量。");
        return;
    }
    
    this.closest("tr").remove();
}

/**
 * 根据变量类型更新默认值
 */
function updateDefaultValue(event) {
    const row = event.target.closest("tr");
    const valueInput = row.querySelector(".var-value");
    const varType = event.target.value;
    
    switch (varType) {
        case "number":
            valueInput.value = "0";
            break;
        case "string":
            valueInput.value = "";
            break;
        case "boolean":
            valueInput.value = "false";
            break;
        case "array":
            valueInput.value = "[]";
            break;
        case "object":
            valueInput.value = "{}";
            break;
    }
}

/**
 * 初始化示例功能
 */
function initExamples() {
    document.querySelectorAll(".example-item").forEach(item => {
        item.addEventListener("click", function() {
            // 获取示例表达式
            const expr = this.dataset.expr;
            
            // 切换到计算器标签
            document.getElementById("calculatorTab").click();
            
            // 设置编辑器内容
            const editor = document.querySelector(".CodeMirror").CodeMirror;
            editor.setValue(expr);
            editor.focus();
            
            // 自动计算表达式
            setTimeout(() => {
                evaluateExpression(editor);
            }, 100);
        });
    });
}

/**
 * 初始化函数和操作符插入按钮
 */
function initInsertButtons(editor) {
    // 插入函数
    document.querySelectorAll("#functionsList .dropdown-item").forEach(item => {
        item.addEventListener("click", function(e) {
            e.preventDefault();
            const fn = this.dataset.fn;
            insertTextAtCursor(editor, fn);
        });
    });
    
    // 插入操作符
    document.querySelectorAll("#operatorsList .dropdown-item").forEach(item => {
        item.addEventListener("click", function(e) {
            e.preventDefault();
            const op = this.dataset.op;
            
            // 特殊处理三元运算符
            if (op === "?:") {
                insertTextAtCursor(editor, " ? : ");
                // 将光标定位到第一个冒号前
                const cursor = editor.getCursor();
                editor.setCursor({line: cursor.line, ch: cursor.ch - 2});
            } else {
                insertTextAtCursor(editor, ` ${op} `);
            }
        });
    });
}

/**
 * 在编辑器光标位置插入文本
 */
function insertTextAtCursor(editor, text) {
    const doc = editor.getDoc();
    const cursor = doc.getCursor();
    doc.replaceRange(text, cursor);
    editor.focus();
}

/**
 * 执行表达式计算
 */
function evaluateExpression(editor) {
    const expression = editor.getValue().trim();
    
    // 表达式为空时显示提示
    if (!expression) {
        showErrorMessage("请输入要计算的表达式");
        return;
    }
    
    try {
        // 收集变量
        const variables = collectVariables();
        
        // 调用表达式API计算结果
        // 注意：这里需要替换为与后端API的实际通信
        // 目前使用模拟实现，实际项目中应该实现与后端的通信
        const startTime = performance.now();
        const result = mockEvaluateExpression(expression, variables);
        const endTime = performance.now();
        const execTime = (endTime - startTime).toFixed(2);
        
        // 显示结果
        showResult(result, execTime);
        
    } catch (error) {
        showErrorMessage(error.message);
    }
}

/**
 * 收集变量表格中的变量
 */
function collectVariables() {
    const variables = {};
    const rows = document.querySelectorAll("#variablesTable tbody tr");
    
    rows.forEach(row => {
        const nameInput = row.querySelector(".var-name");
        const typeSelect = row.querySelector(".var-type");
        const valueInput = row.querySelector(".var-value");
        
        if (!nameInput || !typeSelect || !valueInput) return;
        
        const name = nameInput.value.trim();
        const type = typeSelect.value;
        const rawValue = valueInput.value.trim();
        
        // 检查变量名是否有效
        if (!name || !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)) {
            throw new Error(`变量名 "${name}" 无效，变量名必须以字母或下划线开头，且只能包含字母、数字和下划线`);
        }
        
        // 转换变量值为对应类型
        let value;
        try {
            switch (type) {
                case "number":
                    value = Number(rawValue);
                    if (isNaN(value)) {
                        throw new Error(`变量 "${name}" 的值不是有效的数字`);
                    }
                    break;
                case "string":
                    value = String(rawValue);
                    break;
                case "boolean":
                    if (rawValue.toLowerCase() === "true") {
                        value = true;
                    } else if (rawValue.toLowerCase() === "false") {
                        value = false;
                    } else {
                        throw new Error(`变量 "${name}" 的值不是有效的布尔值，应为 true 或 false`);
                    }
                    break;
                case "array":
                case "object":
                    try {
                        // 尝试解析JSON
                        value = JSON.parse(rawValue);
                        
                        // 验证类型
                        if (type === "array" && !Array.isArray(value)) {
                            throw new Error(`变量 "${name}" 的值不是有效的数组`);
                        } else if (type === "object" && (Array.isArray(value) || typeof value !== "object")) {
                            throw new Error(`变量 "${name}" 的值不是有效的对象`);
                        }
                    } catch (e) {
                        throw new Error(`变量 "${name}" 的值格式不正确: ${e.message}`);
                    }
                    break;
                default:
                    value = rawValue;
            }
            
            variables[name] = value;
        } catch (e) {
            throw new Error(`变量 "${name}" 设置错误: ${e.message}`);
        }
    });
    
    return variables;
}

/**
 * 模拟表达式计算 (实际项目中应替换为API调用)
 */
function mockEvaluateExpression(expression, variables) {
    try {
        // 安全处理各种可能的表达式
        // 注意：这只是为了演示，实际应该调用后端API
        // 这种方法在生产环境中不安全，不要在真实项目中使用eval

        // 预处理表达式，处理特殊情况
        let processedExpr = expression.replace(/'/g, '"'); // 单引号替换为双引号
        
        // 模拟内置函数
        const mockFunctions = {
            min: Math.min,
            max: Math.max,
            abs: Math.abs,
            round: (num, decimals = 0) => {
                const factor = Math.pow(10, decimals);
                return Math.round(num * factor) / factor;
            },
            length: str => String(str).length,
            upper: str => String(str).toUpperCase(),
            lower: str => String(str).toLowerCase(),
            concat: (...args) => args.join('')
        };
        
        // 构建执行上下文，包含变量和模拟函数
        const context = {...variables, ...mockFunctions};
        
        // 简单实现三元运算符 (? :) 的处理
        processedExpr = processedExpr.replace(/(\S+)\s*\?\s*(\S+)\s*:\s*(\S+)/g, 
            (match, condition, trueVal, falseVal) => `(${condition} ? ${trueVal} : ${falseVal})`);
        
        // 实现一个非常简化的解释器，仅供演示 (不安全，仅用于UI展示)
        // 创建函数并带入上下文变量
        const keys = Object.keys(context);
        const values = Object.values(context);
        
        // 使用Function构造函数模拟表达式计算，这在实际项目中应替换为安全的后端API调用
        // eslint-disable-next-line no-new-func
        const result = new Function(...keys, `"use strict"; return (${processedExpr});`)(...values);
        
        return result;
    } catch (error) {
        console.error("Expression evaluation error:", error);
        throw new Error(`表达式计算错误: ${error.message}`);
    }
}

/**
 * 显示计算结果
 */
function showResult(result, execTime) {
    const resultArea = document.getElementById("resultArea");
    const errorAlert = document.getElementById("errorAlert");
    const infoAlert = document.getElementById("infoAlert");
    
    // 清除之前的结果和错误信息
    errorAlert.classList.add("d-none");
    
    // 清除结果区域
    resultArea.innerHTML = "";
    resultArea.className = "p-3 border rounded bg-light";
    
    // 根据结果类型，设置不同的显示样式
    let resultType = typeof result;
    let resultHtml = "";
    
    if (result === null) {
        resultType = "null";
        resultHtml = `<span class="text-muted">null</span>`;
    } else if (result === undefined) {
        resultType = "undefined";
        resultHtml = `<span class="text-muted">undefined</span>`;
    } else if (Array.isArray(result)) {
        resultType = "array";
        resultHtml = `<pre class="result-value mb-0">${formatArrayResult(result)}</pre>`;
    } else if (typeof result === "object") {
        resultType = "object";
        resultHtml = `<pre class="result-value mb-0">${formatObjectResult(result)}</pre>`;
    } else if (typeof result === "string") {
        resultHtml = `<div class="result-value">"${escapeHtml(result)}"</div>`;
    } else if (typeof result === "boolean") {
        const color = result ? "text-success" : "text-danger";
        resultHtml = `<div class="result-value ${color}">${result}</div>`;
    } else if (typeof result === "number") {
        resultHtml = `<div class="result-value">${result}</div>`;
    } else {
        resultHtml = `<div class="result-value">${String(result)}</div>`;
    }
    
    // 添加结果到结果区域
    resultArea.innerHTML = resultHtml;
    resultArea.classList.add("result-success");
    
    // 显示执行信息
    document.getElementById("execTime").textContent = execTime;
    document.getElementById("resultType").textContent = resultType;
    infoAlert.classList.remove("d-none");
}

/**
 * 格式化数组结果
 */
function formatArrayResult(arr) {
    try {
        return JSON.stringify(arr, null, 2)
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;");
    } catch (e) {
        return "[无法显示数组内容]";
    }
}

/**
 * 格式化对象结果
 */
function formatObjectResult(obj) {
    try {
        return JSON.stringify(obj, null, 2)
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;");
    } catch (e) {
        return "{无法显示对象内容}";
    }
}

/**
 * 显示错误信息
 */
function showErrorMessage(message) {
    const resultArea = document.getElementById("resultArea");
    const errorAlert = document.getElementById("errorAlert");
    const errorDetails = document.getElementById("errorDetails");
    const infoAlert = document.getElementById("infoAlert");
    
    // 隐藏执行信息
    infoAlert.classList.add("d-none");
    
    // 清除之前的结果
    resultArea.innerHTML = `
        <div class="text-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            计算出错
        </div>
    `;
    resultArea.className = "p-3 border rounded bg-light result-error";
    
    // 显示错误详情
    errorDetails.textContent = message;
    errorAlert.classList.remove("d-none");
}

/**
 * 转义HTML特殊字符
 */
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}