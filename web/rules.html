<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>规则引擎 - 在线规则管理工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/lib/codemirror.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/theme/default.css" rel="stylesheet">
    <link href="static/css/styles.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-gears me-2"></i>
                规则引擎
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">表达式引擎</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="rules.html">规则引擎</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="examplesTab">示例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="referenceTab">参考文档</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="aboutTab">关于</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 规则引擎界面 -->
        <div id="ruleEngineSection">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>规则管理
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <div>
                            <button class="btn btn-primary" id="createRuleBtn">
                                <i class="fas fa-plus-circle me-1"></i>新建规则
                            </button>
                        </div>
                        <div class="input-group" style="max-width: 300px;">
                            <input type="text" class="form-control" placeholder="搜索规则" id="searchRuleInput">
                            <button class="btn btn-outline-secondary" type="button" id="searchRuleBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 规则列表 -->
                    <div class="table-responsive">
                        <table class="table table-hover table-sm" id="rulesTable">
                            <thead>
                                <tr>
                                    <th scope="col" style="width: 5%;">ID</th>
                                    <th scope="col" style="width: 20%;">名称</th>
                                    <th scope="col" style="width: 35%;">描述</th>
                                    <th scope="col" style="width: 10%;">优先级</th>
                                    <th scope="col" style="width: 10%;">状态</th>
                                    <th scope="col" style="width: 20%;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 示例规则行 -->
                                <tr>
                                    <td>1</td>
                                    <td>客户风险评估</td>
                                    <td>基于客户属性和交易历史评估风险等级</td>
                                    <td><span class="badge bg-warning">High (2)</span></td>
                                    <td><span class="badge bg-success">启用</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary edit-rule" data-rule-id="1" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-sm btn-outline-secondary copy-rule" data-rule-id="1" title="复制"><i class="fas fa-copy"></i></button>
                                        <button class="btn btn-sm btn-outline-danger delete-rule" data-rule-id="1" title="删除"><i class="fas fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>折扣计算规则</td>
                                    <td>根据客户等级、订单金额计算折扣率</td>
                                    <td><span class="badge bg-info">Medium (4)</span></td>
                                    <td><span class="badge bg-success">启用</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary edit-rule" data-rule-id="2" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-sm btn-outline-secondary copy-rule" data-rule-id="2" title="复制"><i class="fas fa-copy"></i></button>
                                        <button class="btn btn-sm btn-outline-danger delete-rule" data-rule-id="2" title="删除"><i class="fas fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>交易异常检测</td>
                                    <td>监测交易行为，识别可能的欺诈活动</td>
                                    <td><span class="badge bg-warning">High (1)</span></td>
                                    <td><span class="badge bg-secondary">禁用</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary edit-rule" data-rule-id="3" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="btn btn-sm btn-outline-secondary copy-rule" data-rule-id="3" title="复制"><i class="fas fa-copy"></i></button>
                                        <button class="btn btn-sm btn-outline-danger delete-rule" data-rule-id="3" title="删除"><i class="fas fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                <!-- 更多规则行... -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 规则编辑器 -->
            <div class="card shadow-sm mb-4" id="ruleEditorCard" style="display: none;">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>规则编辑器
                    </h5>
                    <button type="button" class="btn-close" aria-label="Close" id="closeEditorBtn"></button>
                </div>
                <div class="card-body">
                    <form id="ruleForm">
                        <input type="hidden" id="ruleId">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="ruleName" class="form-label">规则名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ruleName" required>
                            </div>
                            <div class="col-md-3">
                                <label for="rulePriority" class="form-label">优先级</label>
                                <input type="number" class="form-control" id="rulePriority" value="10" min="1">
                                <small class="text-muted">数字越小，优先级越高</small>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="ruleStatus" checked>
                                    <label class="form-check-label" for="ruleStatus">启用规则</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="ruleDescription" class="form-label">规则描述</label>
                            <textarea class="form-control" id="ruleDescription" rows="2"></textarea>
                        </div>

                        <!-- 条件构建器 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="fas fa-filter me-1"></i>条件 (WHEN)
                            </div>
                            <div class="card-body">
                                <div class="rule-conditions-container mb-2">
                                    <!-- 条件将动态添加到这里 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-success" id="addConditionBtn">
                                    <i class="fas fa-plus me-1"></i>添加条件
                                </button>
                                <div class="mt-2">
                                    <small class="text-muted">条件表达式预览:</small>
                                    <div id="conditionPreview" class="bg-light p-2 rounded small"><code>未定义条件</code></div>
                                </div>
                            </div>
                        </div>

                        <!-- 动作构建器 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="fas fa-bolt me-1"></i>动作 (THEN)
                            </div>
                            <div class="card-body">
                                <div class="rule-actions-container mb-2">
                                    <!-- 动作将动态添加到这里 -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-success" id="addActionBtn">
                                    <i class="fas fa-plus me-1"></i>添加动作
                                </button>
                            </div>
                        </div>

                        <!-- 规则测试 -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="fas fa-vial me-1"></i>规则测试
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>输入数据 (JSON)</h6>
                                        <textarea id="testInputEditor" class="form-control" rows="8">{
"user": {
    "id": 1001,
    "name": "张三",
    "age": 30,
    "points": 85,
    "vip": true,
    "tags": ["活跃", "新客户", "已认证"],
    "city": "北京",
    "joinTime": "2025-01-15T10:30:00Z",
    "orders": [
        {
            "id": "ORD-001",
            "amount": 299.99,
            "createTime": "2025-04-01T10:00:00Z",
            "status": "已完成",
            "productName": "高级会员",
            "tags": ["会员", "自动续费"]
        },
        {
            "id": "ORD-002",
            "amount": 99.50,
            "createTime": "2025-04-10T14:20:00Z",
            "status": "已完成",
            "productName": "季度课程",
            "tags": ["课程", "已完成"]
        }
    ],
    "preferences": {
        "emailNotification": true,
        "theme": "dark",
        "language": "zh-CN"
    }
},
"context": {
    "deviceType": "mobile",
    "appVersion": "2.5.3",
    "requestTime": "2025-04-15T10:30:00Z",
    "source": "app"
}
}</textarea>
                                        <button class="btn btn-info mt-2" id="testRuleBtn">
                                            <i class="fas fa-play me-1"></i>测试规则
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>执行结果</h6>
                                        <div id="testOutputArea" class="bg-light p-3 rounded" style="min-height: 200px; font-size: 0.9em;">
                                            点击 "测试规则" 查看结果...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" id="cancelEditBtn">取消</button>
                            <button type="submit" class="btn btn-primary" id="saveRuleBtn">
                                <i class="fas fa-save me-1"></i>保存规则
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 示例区域 -->
        <div id="examplesSection" class="d-none">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>规则示例
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">点击示例可直接加载到规则引擎中使用。</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">客户分类规则</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>VIP客户识别规则</code>
                                        <small class="text-muted d-block">根据消费金额和频次识别VIP客户</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>新客户特殊处理规则</code>
                                        <small class="text-muted d-block">注册30天内的客户获得额外优惠</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">促销规则</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>满减促销规则</code>
                                        <small class="text-muted d-block">订单满特定金额后减免一定金额</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>特定商品折扣规则</code>
                                        <small class="text-muted d-block">针对特定类别商品的折扣策略</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">风控规则</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>异常交易监测规则</code>
                                        <small class="text-muted d-block">监测短时间内频繁交易行为</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>高风险地区交易规则</code>
                                        <small class="text-muted d-block">针对特定地区交易的额外验证措施</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">审批规则</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>大额订单审批规则</code>
                                        <small class="text-muted d-block">超过特定金额的订单需要额外审批</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item">
                                        <code>特殊客户绿色通道规则</code>
                                        <small class="text-muted d-block">特定客户群体享受快速审批流程</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 参考文档区域 -->
        <div id="referenceSection" class="d-none">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>规则引擎参考文档
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="referenceTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                概述
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="conditions-doc-tab" data-bs-toggle="tab" data-bs-target="#conditions-doc" type="button" role="tab">
                                条件配置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="actions-doc-tab" data-bs-toggle="tab" data-bs-target="#actions-doc" type="button" role="tab">
                                动作配置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                                高级特性
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0 rounded-bottom" id="referenceTabContent">
                        <!-- 概述标签内容 -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                            <h4>规则引擎简介</h4>
                            <p>规则引擎是一个强大的决策系统，允许业务逻辑与应用代码分离。通过配置规则而非编码方式实现业务逻辑，具有以下优势：</p>
                            <ul>
                                <li>业务规则与应用代码解耦，便于维护</li>
                                <li>业务人员可直接参与规则配置，无需开发介入</li>
                                <li>规则可动态更新，无需重新部署应用</li>
                                <li>规则可重用，提高开发效率</li>
                                <li>提高系统灵活性，快速适应业务变化</li>
                            </ul>

                            <h4>规则组成</h4>
                            <p>一个完整的规则通常由以下部分组成：</p>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>组成部分</th>
                                            <th>描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>条件 (When)</td>
                                            <td>规则触发的条件，可以是简单条件或复合条件</td>
                                        </tr>
                                        <tr>
                                            <td>动作 (Then)</td>
                                            <td>条件满足时执行的操作</td>
                                        </tr>
                                        <tr>
                                            <td>优先级</td>
                                            <td>规则的执行优先顺序</td>
                                        </tr>
                                        <tr>
                                            <td>元数据</td>
                                            <td>规则的名称、描述、版本等信息</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>规则执行流程</h4>
                            <ol>
                                <li>准备事实数据（Facts）：收集规则评估所需的输入数据</li>
                                <li>规则匹配：将事实数据与规则条件进行匹配</li>
                                <li>冲突解决：当多个规则同时匹配时，根据优先级决定执行顺序</li>
                                <li>规则执行：执行匹配规则的动作部分</li>
                                <li>结果处理：处理规则执行的结果数据</li>
                            </ol>
                        </div>

                        <!-- 条件配置标签内容 -->
                        <div class="tab-pane fade" id="conditions-doc" role="tabpanel" aria-labelledby="conditions-doc-tab">
                            <h4>条件配置</h4>
                            <p>条件是规则的触发前提，定义了规则何时应该被执行。</p>

                            <h5>条件类型</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>类型</th>
                                            <th>描述</th>
                                            <th>示例</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>比较条件</td>
                                            <td>比较变量与特定值或其他变量</td>
                                            <td>客户等级 == "VIP"</td>
                                        </tr>
                                        <tr>
                                            <td>范围条件</td>
                                            <td>检查变量是否在特定范围内</td>
                                            <td>10 <= 订单金额 <= 1000</td>
                                        </tr>
                                        <tr>
                                            <td>存在条件</td>
                                            <td>检查某个值是否存在于集合中</td>
                                            <td>商品ID 在 [101, 102, 103] 中</td>
                                        </tr>
                                        <tr>
                                            <td>模式匹配</td>
                                            <td>检查变量是否匹配特定模式</td>
                                            <td>邮箱地址 匹配 "*.edu"</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>条件组合</h5>
                            <p>多个条件可以通过逻辑运算符组合成复合条件：</p>
                            <ul>
                                <li><strong>AND</strong>：所有条件都必须满足</li>
                                <li><strong>OR</strong>：至少一个条件满足</li>
                                <li><strong>NOT</strong>：条件不满足</li>
                            </ul>

                            <h5>条件表达式示例</h5>
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <pre><code>(客户等级 == "VIP" OR 订单金额 > 1000) AND 商品类别 != "限制商品"</code></pre>
                                </div>
                            </div>

                            <h5>条件评估最佳实践</h5>
                            <ol>
                                <li>将最可能快速失败的条件放在前面（短路评估）</li>
                                <li>避免过度复杂的条件组合，保持可读性</li>
                                <li>考虑使用规则分组替代过于复杂的条件表达式</li>
                                <li>对频繁变化的阈值使用可配置参数</li>
                            </ol>
                        </div>

                        <!-- 动作配置标签内容 -->
                        <div class="tab-pane fade" id="actions-doc" role="tabpanel" aria-labelledby="actions-doc-tab">
                            <h4>动作配置</h4>
                            <p>当规则条件满足时，将执行动作部分定义的操作。</p>

                            <h5>常用动作类型</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>动作类型</th>
                                            <th>描述</th>
                                            <th>配置参数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>设置变量</td>
                                            <td>为变量赋予新值</td>
                                            <td>变量名, 新值</td>
                                        </tr>
                                        <tr>
                                            <td>发送通知</td>
                                            <td>发送各种形式的通知</td>
                                            <td>通知类型, 接收方, 内容模板</td>
                                        </tr>
                                        <tr>
                                            <td>应用折扣</td>
                                            <td>为订单应用折扣率</td>
                                            <td>折扣率, 折扣项</td>
                                        </tr>
                                        <tr>
                                            <td>设置标记</td>
                                            <td>标记某个对象</td>
                                            <td>标记名称, 标记值</td>
                                        </tr>
                                        <tr>
                                            <td>调用服务</td>
                                            <td>调用外部服务或API</td>
                                            <td>服务名, 参数</td>
                                        </tr>
                                        <tr>
                                            <td>触发工作流</td>
                                            <td>启动一个工作流程</td>
                                            <td>工作流ID, 输入参数</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>动作执行顺序</h5>
                            <p>一个规则可以定义多个动作，默认按照定义顺序依次执行。可以通过以下方式控制执行：</p>
                            <ul>
                                <li>设置先决条件：只有满足特定条件时才执行某个动作</li>
                                <li>定义执行阶段：将动作分配到不同执行阶段</li>
                                <li>配置动作超时：防止长时间执行影响整体性能</li>
                            </ul>

                            <h5>动作配置示例</h5>
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <pre><code>// 设置折扣变量
discount = 0.85

// 发送通知
send_notification(
    type: "sms",
    template: "discount_notice",
    recipient: customer.phone
)</code></pre>
                                </div>
                            </div>

                            <h5>动作执行最佳实践</h5>
                            <ol>
                                <li>保持动作的原子性和独立性</li>
                                <li>处理动作执行可能的异常情况</li>
                                <li>记录动作执行的日志</li>
                                <li>对关键动作增加安全检查</li>
                                <li>避免在单个规则中定义过多动作</li>
                            </ol>
                        </div>

                        <!-- 高级特性标签内容 -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel" aria-labelledby="advanced-tab">
                            <h4>规则引擎高级特性</h4>

                            <h5>规则分组与流</h5>
                            <p>将相关规则组织成组或流，实现更复杂的业务场景：</p>
                            <ul>
                                <li>规则链：规则按顺序执行，前一个规则的输出作为下一个规则的输入</li>
                                <li>规则组：同一业务领域的规则集合，共享上下文</li>
                                <li>决策表：通过表格形式配置多条件组合下的决策结果</li>
                            </ul>

                            <h5>规则版本控制</h5>
                            <p>对规则进行版本管理，支持以下功能：</p>
                            <ul>
                                <li>规则历史记录：跟踪规则的变更历史</li>
                                <li>版本比较：对比不同版本规则的差异</li>
                                <li>回滚功能：将规则恢复到之前的版本</li>
                                <li>规则分支：支持多分支并行开发</li>
                            </ul>

                            <h5>规则测试与验证</h5>
                            <p>确保规则的正确性和有效性：</p>
                            <ul>
                                <li>单元测试：验证单个规则的行为</li>
                                <li>场景测试：验证多个规则组合的行为</li>
                                <li>回归测试：确保新规则不破坏现有功能</li>
                                <li>性能测试：评估规则执行效率</li>
                                <li>覆盖率分析：确保测试覆盖所有条件分支</li>
                            </ul>

                            <h5>规则监控与分析</h5>
                            <p>监控规则执行情况并提供分析：</p>
                            <ul>
                                <li>执行统计：规则触发次数、执行时间等</li>
                                <li>匹配率分析：规则条件匹配成功率</li>
                                <li>影响分析：规则执行对业务指标的影响</li>
                                <li>热点规则识别：识别频繁触发的规则</li>
                                <li>异常检测：识别执行异常或性能问题</li>
                            </ul>

                            <h5>规则引擎集成</h5>
                            <p>将规则引擎与其他系统集成：</p>
                            <ul>
                                <li>服务调用：通过API调用规则服务</li>
                                <li>事件驱动：基于事件触发规则执行</li>
                                <li>工作流集成：作为工作流决策节点</li>
                                <li>批处理：批量执行规则评估</li>
                                <li>实时处理：对流数据应用规则</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关于区域 -->
        <div id="aboutSection" class="d-none">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-circle-info me-2"></i>关于规则引擎
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-7">
                            <h4>规则引擎</h4>
                            <p>规则引擎是一个强大的业务规则管理和执行平台，提供了业务规则与应用程序代码分离的能力。它允许业务人员和技术人员以可视化和直观的方式定义、管理和执行业务规则。</p>
                            
                            <p>核心功能包括：</p>
                            <ul>
                                <li>规则定义：通过直观界面定义复杂业务规则</li>
                                <li>规则管理：版本控制、变更追踪、权限管理</li>
                                <li>规则执行：高性能规则评估和执行</li>
                                <li>规则监控：执行统计、性能分析</li>
                                <li>多种集成方式：API调用、事件驱动、工作流集成</li>
                            </ul>

                            <p>适用场景：</p>
                            <ul>
                                <li>动态定价和折扣管理</li>
                                <li>风险评估和控制</li>
                                <li>合规检查和验证</li>
                                <li>客户分群和个性化推荐</li>
                                <li>工作流程决策点</li>
                                <li>复杂事件处理</li>
                            </ul>
                        </div>
                        <div class="col-md-5">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5>版本信息</h5>
                                    <p><strong>引擎版本:</strong> 1.0.0</p>
                                    <p><strong>UI版本:</strong> 1.0.0</p>
                                    <p><strong>最后更新:</strong> 2023-05-25</p>
                                    
                                    <h5 class="mt-3">技术栈</h5>
                                    <p><strong>前端:</strong> HTML5, CSS3, JavaScript, Bootstrap 5</p>
                                    <p><strong>后端:</strong> Go 1.18+</p>
                                    <p><strong>规则存储:</strong> Database, JSON</p>
                                    
                                    <h5 class="mt-3">联系方式</h5>
                                    <p>如有问题或建议，请联系开发团队。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-light py-3 mt-5 border-top">
        <div class="container text-center text-muted">
            <p class="mb-0">&copy; 2023 规则引擎 | 版权所有</p>
        </div>
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/lib/codemirror.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/mode/javascript/javascript.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/addon/edit/matchbrackets.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/addon/edit/closebrackets.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/addon/fold/foldcode.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/addon/fold/foldgutter.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/addon/fold/brace-fold.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/addon/fold/comment-fold.js"></script>
    <script src="static/js/rules.js"></script>
</body>
</html>