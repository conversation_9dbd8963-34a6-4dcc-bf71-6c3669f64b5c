<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表达式引擎 - 在线计算工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/lib/codemirror.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/theme/dracula.css" rel="stylesheet">
    <link href="static/css/styles.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-square-root-variable me-2"></i>
                表达式引擎
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html" id="calculatorTab">表达式引擎</a> <!-- Updated href -->
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="rule_engine.html">规则引擎</a> <!-- Added link to rule engine -->
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="examplesTab">示例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="referenceTab">参考文档</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="aboutTab">关于</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- 计算器界面 -->
        <div id="calculatorSection">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>表达式计算器
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="expressionInput" class="form-label">输入表达式</label>
                                <div id="expressionEditor"></div>
                                <div class="form-text">
                                    提示：可使用算术表达式、逻辑表达式、条件表达式等
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">快捷操作</label>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-sm btn-outline-secondary" id="clearExpr">
                                        <i class="fas fa-eraser me-1"></i>清空
                                    </button>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle w-100" type="button" id="insertFunctionBtn" data-bs-toggle="dropdown">
                                            <i class="fas fa-function me-1"></i>插入函数
                                        </button>
                                        <ul class="dropdown-menu" id="functionsList">
                                            <li><a class="dropdown-item" href="#" data-fn="min(10, 5)">min()</a></li>
                                            <li><a class="dropdown-item" href="#" data-fn="max(10, 5)">max()</a></li>
                                            <li><a class="dropdown-item" href="#" data-fn="abs(-15)">abs()</a></li>
                                            <li><a class="dropdown-item" href="#" data-fn="round(3.14159, 2)">round()</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" data-fn="length('Hello')">length()</a></li>
                                            <li><a class="dropdown-item" href="#" data-fn="upper('hello')">upper()</a></li>
                                            <li><a class="dropdown-item" href="#" data-fn="lower('HELLO')">lower()</a></li>
                                            <li><a class="dropdown-item" href="#" data-fn="concat('Hello', ' ', 'World')">concat()</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" data-fn="if(x > 10, '大', '小')">if()</a></li>
                                        </ul>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle w-100" type="button" id="insertOperatorBtn" data-bs-toggle="dropdown">
                                            <i class="fas fa-code me-1"></i>插入操作符
                                        </button>
                                        <ul class="dropdown-menu" id="operatorsList">
                                            <li><a class="dropdown-item" href="#" data-op="+">+ (加)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="-">- (减)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="*">* (乘)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="/">/  (除)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="%">% (取模)</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" data-op="==">=== (等于)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="!=">>!= (不等于)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op=">">  > (大于)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="<">  < (小于)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op=">=">>= (大于等于)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="<=">>= (小于等于)</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" data-op="&&">&& (逻辑与)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="||">|| (逻辑或)</a></li>
                                            <li><a class="dropdown-item" href="#" data-op="!">! (逻辑非)</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="#" data-op="?:">?: (三元运算符)</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label class="form-label">
                                <i class="fas fa-table-list me-1"></i>变量定义
                                <button class="btn btn-sm btn-outline-success ms-2" id="addVarBtn">
                                    <i class="fas fa-plus-circle"></i> 添加变量
                                </button>
                            </label>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered table-hover" id="variablesTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width:25%">变量名</th>
                                            <th style="width:20%">类型</th>
                                            <th style="width:45%">值</th>
                                            <th style="width:10%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><input type="text" class="form-control form-control-sm var-name" value="x"></td>
                                            <td>
                                                <select class="form-select form-select-sm var-type">
                                                    <option value="number" selected>数字</option>
                                                    <option value="string">字符串</option>
                                                    <option value="boolean">布尔值</option>
                                                    <option value="array">数组</option>
                                                    <option value="object">对象</option>
                                                </select>
                                            </td>
                                            <td><input type="text" class="form-control form-control-sm var-value" value="10"></td>
                                            <td class="text-center">
                                                <button class="btn btn-sm btn-outline-danger delete-var">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><input type="text" class="form-control form-control-sm var-name" value="y"></td>
                                            <td>
                                                <select class="form-select form-select-sm var-type">
                                                    <option value="number" selected>数字</option>
                                                    <option value="string">字符串</option>
                                                    <option value="boolean">布尔值</option>
                                                    <option value="array">数组</option>
                                                    <option value="object">对象</option>
                                                </select>
                                            </td>
                                            <td><input type="text" class="form-control form-control-sm var-value" value="5"></td>
                                            <td class="text-center">
                                                <button class="btn btn-sm btn-outline-danger delete-var">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><input type="text" class="form-control form-control-sm var-name" value="name"></td>
                                            <td>
                                                <select class="form-select form-select-sm var-type">
                                                    <option value="number">数字</option>
                                                    <option value="string" selected>字符串</option>
                                                    <option value="boolean">布尔值</option>
                                                    <option value="array">数组</option>
                                                    <option value="object">对象</option>
                                                </select>
                                            </td>
                                            <td><input type="text" class="form-control form-control-sm var-value" value="张三"></td>
                                            <td class="text-center">
                                                <button class="btn btn-sm btn-outline-danger delete-var">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" id="evalBtn">
                            <i class="fas fa-play me-1"></i>执行计算
                        </button>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-square-poll-vertical me-2"></i>计算结果
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="result-container">
                            <div id="resultArea" class="p-3 border rounded bg-light">
                                <div class="result-placeholder text-secondary text-center">
                                    <i class="fas fa-calculator fa-3x mb-2"></i>
                                    <p>执行表达式后结果将显示在这里</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-danger d-none" id="errorAlert" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-1"></i>错误信息
                        </h6>
                        <div id="errorDetails"></div>
                    </div>

                    <div class="alert alert-info d-none" id="infoAlert">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-1"></i>执行信息
                        </h6>
                        <div id="infoDetails">
                            <p>表达式执行时间: <span id="execTime">0</span> ms</p>
                            <p>结果类型: <span id="resultType">-</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 示例界面 -->
        <div id="examplesSection" class="d-none">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>表达式示例
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">点击示例可直接加载到计算器中使用。</p>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">算术表达式</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item" data-expr="2 + 3 * (4 - 1)">
                                        <code>2 + 3 * (4 - 1)</code>
                                        <small class="text-muted d-block">基本运算与括号</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="(10 + 5) / 3">
                                        <code>(10 + 5) / 3</code>
                                        <small class="text-muted d-block">除法运算</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="7 % 4">
                                        <code>7 % 4</code>
                                        <small class="text-muted d-block">取模运算</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="x * 2 + y">
                                        <code>x * 2 + y</code>
                                        <small class="text-muted d-block">使用变量的表达式</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">逻辑表达式</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item" data-expr="true && false">
                                        <code>true && false</code>
                                        <small class="text-muted d-block">逻辑与运算</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="true || !false">
                                        <code>true || !false</code>
                                        <small class="text-muted d-block">逻辑或与逻辑非</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="!(true && false)">
                                        <code>!(true && false)</code>
                                        <small class="text-muted d-block">复合逻辑表达式</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="x > 5 && y < 10">
                                        <code>x > 5 && y < 10</code>
                                        <small class="text-muted d-block">使用变量的逻辑表达式</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">比较表达式</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item" data-expr="5 == 5">
                                        <code>5 == 5</code>
                                        <small class="text-muted d-block">相等比较</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="10 != 5">
                                        <code>10 != 5</code>
                                        <small class="text-muted d-block">不等比较</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="10 > 5 && 10 <= 15">
                                        <code>10 > 5 && 10 <= 15</code>
                                        <small class="text-muted d-block">大小比较</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">字符串操作</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item" data-expr="'Hello' + ' ' + 'World'">
                                        <code>'Hello' + ' ' + 'World'</code>
                                        <small class="text-muted d-block">字符串连接</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="length('Hello')">
                                        <code>length('Hello')</code>
                                        <small class="text-muted d-block">字符串长度</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="upper(name)">
                                        <code>upper(name)</code>
                                        <small class="text-muted d-block">转换为大写</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">条件表达式</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item" data-expr="x > 10 ? '大于10' : '不大于10'">
                                        <code>x > 10 ? '大于10' : '不大于10'</code>
                                        <small class="text-muted d-block">三元表达式</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="if(x > 10, '大于10', '不大于10')">
                                        <code>if(x > 10, '大于10', '不大于10')</code>
                                        <small class="text-muted d-block">if函数</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header">复杂表达式</div>
                                <div class="list-group list-group-flush">
                                    <button class="list-group-item list-group-item-action example-item" data-expr="(x + y) * 2 > 30 ? 'High' : 'Low'">
                                        <code>(x + y) * 2 > 30 ? 'High' : 'Low'</code>
                                        <small class="text-muted d-block">算术、比较和条件组合</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action example-item" data-expr="round(min(x, y) / max(x, y) * 100, 2) + '%'">
                                        <code>round(min(x, y) / max(x, y) * 100, 2) + '%'</code>
                                        <small class="text-muted d-block">复合函数计算</small>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 参考文档界面 -->
        <div id="referenceSection" class="d-none">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>表达式语法参考
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="referenceTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="operators-tab" data-bs-toggle="tab" data-bs-target="#operators" type="button" role="tab">
                                操作符
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="functions-tab" data-bs-toggle="tab" data-bs-target="#functions" type="button" role="tab">
                                内置函数
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="types-tab" data-bs-toggle="tab" data-bs-target="#types" type="button" role="tab">
                                数据类型
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="syntax-tab" data-bs-toggle="tab" data-bs-target="#syntax" type="button" role="tab">
                                语法规则
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content p-3 border border-top-0 rounded-bottom" id="referenceTabContent">
                        <!-- 操作符标签内容 -->
                        <div class="tab-pane fade show active" id="operators" role="tabpanel" aria-labelledby="operators-tab">
                            <h4>算术操作符</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>操作符</th>
                                            <th>描述</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>+</td><td>加法</td><td>5 + 3</td><td>8</td></tr>
                                        <tr><td>-</td><td>减法</td><td>5 - 3</td><td>2</td></tr>
                                        <tr><td>*</td><td>乘法</td><td>5 * 3</td><td>15</td></tr>
                                        <tr><td>/</td><td>除法</td><td>6 / 3</td><td>2</td></tr>
                                        <tr><td>%</td><td>取模（余数）</td><td>7 % 3</td><td>1</td></tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>比较操作符</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>操作符</th>
                                            <th>描述</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>==</td><td>等于</td><td>5 == 5</td><td>true</td></tr>
                                        <tr><td>!=</td><td>不等于</td><td>5 != 3</td><td>true</td></tr>
                                        <tr><td>></td><td>大于</td><td>5 > 3</td><td>true</td></tr>
                                        <tr><td><</td><td>小于</td><td>3 < 5</td><td>true</td></tr>
                                        <tr><td>>=</td><td>大于等于</td><td>5 >= 5</td><td>true</td></tr>
                                        <tr><td><=</td><td>小于等于</td><td>5 <= 5</td><td>true</td></tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>逻辑操作符</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>操作符</th>
                                            <th>描述</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>&&</td><td>逻辑与</td><td>true && false</td><td>false</td></tr>
                                        <tr><td>||</td><td>逻辑或</td><td>true || false</td><td>true</td></tr>
                                        <tr><td>!</td><td>逻辑非</td><td>!false</td><td>true</td></tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>其他操作符</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>操作符</th>
                                            <th>描述</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td>? :</td><td>三元条件</td><td>5 > 3 ? 'yes' : 'no'</td><td>'yes'</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 内置函数标签内容 -->
                        <div class="tab-pane fade" id="functions" role="tabpanel" aria-labelledby="functions-tab">
                            <h4>数学函数</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>函数</th>
                                            <th>描述</th>
                                            <th>参数</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>min</code></td>
                                            <td>返回最小值</td>
                                            <td>至少1个数值</td>
                                            <td>min(10, 5, 20)</td>
                                            <td>5</td>
                                        </tr>
                                        <tr>
                                            <td><code>max</code></td>
                                            <td>返回最大值</td>
                                            <td>至少1个数值</td>
                                            <td>max(10, 5, 20)</td>
                                            <td>20</td>
                                        </tr>
                                        <tr>
                                            <td><code>abs</code></td>
                                            <td>返回绝对值</td>
                                            <td>1个数值</td>
                                            <td>abs(-15)</td>
                                            <td>15</td>
                                        </tr>
                                        <tr>
                                            <td><code>round</code></td>
                                            <td>四舍五入</td>
                                            <td>1-2个参数 (值,小数位)</td>
                                            <td>round(3.14159, 2)</td>
                                            <td>3.14</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>字符串函数</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>函数</th>
                                            <th>描述</th>
                                            <th>参数</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>length</code></td>
                                            <td>返回字符串长度</td>
                                            <td>1个字符串</td>
                                            <td>length('Hello')</td>
                                            <td>5</td>
                                        </tr>
                                        <tr>
                                            <td><code>upper</code></td>
                                            <td>转换为大写</td>
                                            <td>1个字符串</td>
                                            <td>upper('hello')</td>
                                            <td>HELLO</td>
                                        </tr>
                                        <tr>
                                            <td><code>lower</code></td>
                                            <td>转换为小写</td>
                                            <td>1个字符串</td>
                                            <td>lower('HELLO')</td>
                                            <td>hello</td>
                                        </tr>
                                        <tr>
                                            <td><code>concat</code></td>
                                            <td>连接字符串</td>
                                            <td>至少2个字符串</td>
                                            <td>concat('a', 'b', 'c')</td>
                                            <td>abc</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>逻辑函数</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>函数</th>
                                            <th>描述</th>
                                            <th>参数</th>
                                            <th>示例</th>
                                            <th>结果</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>if</code></td>
                                            <td>条件判断</td>
                                            <td>3个参数(条件,真值,假值)</td>
                                            <td>if(10 > 5, 'yes', 'no')</td>
                                            <td>yes</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 数据类型标签内容 -->
                        <div class="tab-pane fade" id="types" role="tabpanel" aria-labelledby="types-tab">
                            <h4>基本数据类型</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>类型</th>
                                            <th>描述</th>
                                            <th>示例</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>数字 (Number)</td>
                                            <td>浮点数值</td>
                                            <td>42, 3.14, -10</td>
                                        </tr>
                                        <tr>
                                            <td>字符串 (String)</td>
                                            <td>文本数据</td>
                                            <td>'hello', "world"</td>
                                        </tr>
                                        <tr>
                                            <td>布尔值 (Boolean)</td>
                                            <td>逻辑真/假</td>
                                            <td>true, false</td>
                                        </tr>
                                        <tr>
                                            <td>数组 (Array)</td>
                                            <td>有序集合</td>
                                            <td>[1, 2, 3], ['a', 'b']</td>
                                        </tr>
                                        <tr>
                                            <td>对象 (Object)</td>
                                            <td>键值对集合</td>
                                            <td>{"name": "张三", "age": 30}</td>
                                        </tr>
                                        <tr>
                                            <td>空值 (Null)</td>
                                            <td>代表没有值</td>
                                            <td>null</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h4>类型自动转换</h4>
                            <p>表达式引擎在某些情况下会自动进行类型转换：</p>
                            <ul>
                                <li>字符串与数字连接时，数字会被转换为字符串</li>
                                <li>比较运算中，尝试将不同类型转为相同类型后比较</li>
                                <li>逻辑运算中，非布尔值会被转换为布尔值：0为false，非0数字为true</li>
                            </ul>
                        </div>

                        <!-- 语法规则标签内容 -->
                        <div class="tab-pane fade" id="syntax" role="tabpanel" aria-labelledby="syntax-tab">
                            <h4>表达式语法规则</h4>
                            <ul>
                                <li>表达式由字面量、变量、操作符和函数调用组成</li>
                                <li>数字可以是整数或浮点数：<code>42</code>, <code>3.14</code></li>
                                <li>字符串需要使用单引号或双引号：<code>'hello'</code>, <code>"world"</code></li>
                                <li>布尔值为<code>true</code>或<code>false</code></li>
                                <li>变量名区分大小写，可以包含字母、数字和下划线，但不能以数字开头</li>
                                <li>函数调用格式为：<code>函数名(参数1, 参数2, ...)</code></li>
                                <li>使用圆括号<code>()</code>可以改变运算优先级</li>
                                <li>数组使用方括号定义：<code>[元素1, 元素2, ...]</code></li>
                                <li>对象使用花括号定义：<code>{"键1": 值1, "键2": 值2, ...}</code></li>
                                <li>访问数组元素：<code>数组[索引]</code>，索引从0开始</li>
                                <li>访问对象属性：<code>对象["属性名"]</code></li>
                            </ul>

                            <h4>运算优先级</h4>
                            <p>从高到低的优先级顺序：</p>
                            <ol>
                                <li>括号 <code>()</code></li>
                                <li>函数调用</li>
                                <li>数组/对象访问 <code>[]</code></li>
                                <li>一元运算符 <code>!</code>, <code>-</code> (负号)</li>
                                <li>乘法、除法、取模 <code>*</code>, <code>/</code>, <code>%</code></li>
                                <li>加法、减法 <code>+</code>, <code>-</code></li>
                                <li>比较运算符 <code><</code>, <code><=</code>, <code>></code>, <code>>=</code></li>
                                <li>相等运算符 <code>==</code>, <code>!=</code></li>
                                <li>逻辑与 <code>&&</code></li>
                                <li>逻辑或 <code>||</code></li>
                                <li>三元运算符 <code>?:</code></li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关于界面 -->
        <div id="aboutSection" class="d-none">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-circle-info me-2"></i>关于表达式引擎
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-7">
                            <h4>表达式引擎</h4>
                            <p>表达式引擎是一个功能强大、易扩展且高性能的表达式解析与计算引擎，支持多种表达式类型、变量、函数及自定义扩展，适用于数据处理、规则引擎、配置解析、工作流条件判断等多种场景。</p>
                            
                            <p>本引擎采用编译执行模式，并通过优化与缓存提高执行效率。支持的功能包括：</p>
                            <ul>
                                <li>算术表达式：加减乘除、取模、括号嵌套等操作</li>
                                <li>逻辑表达式：与、或、非、多层逻辑嵌套</li>
                                <li>比较表达式：等于、不等于、大于、小于等比较操作</li>
                                <li>三元表达式：条件判断</li>
                                <li>字符串操作：连接、比较</li>
                                <li>数组与对象：访问和操作</li>
                                <li>变量支持：访问上下文变量</li>
                                <li>函数支持：内置函数和自定义函数</li>
                                <li>高级特性：表达式编译与缓存、错误处理、类型自动转换等</li>
                            </ul>
                        </div>
                        <div class="col-md-5">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5>版本信息</h5>
                                    <p><strong>引擎版本:</strong> 1.0.0</p>
                                    <p><strong>UI版本:</strong> 1.0.0</p>
                                    <p><strong>最后更新:</strong> 2023-05-18</p>
                                    
                                    <h5 class="mt-3">技术栈</h5>
                                    <p><strong>前端:</strong> HTML5, CSS3, JavaScript, Bootstrap 5</p>
                                    <p><strong>后端:</strong> Go 1.18+</p>
                                    
                                    <h5 class="mt-3">联系方式</h5>
                                    <p>如有问题或建议，请联系开发团队。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-light py-3 mt-5 border-top">
        <div class="container text-center text-muted">
            <p class="mb-0">&copy; 2023 表达式引擎 | 版权所有</p>
        </div>
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/lib/codemirror.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/codemirror@5.65.5/mode/javascript/javascript.js"></script>
    <script src="static/js/expr.js"></script>
</body>
</html>