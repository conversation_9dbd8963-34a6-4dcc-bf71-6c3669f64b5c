<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Container Images - 高保真设计稿</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Prism.js 代码高亮 -->
    <link id="prism-theme" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Anime.js 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* 8px基线网格系统 */
        .spacing-8 { margin: 8px; }
        .spacing-16 { margin: 16px; }
        .spacing-24 { margin: 24px; }
        .spacing-32 { margin: 32px; }
        
        /* 图标标准尺寸 */
        .icon-24 { width: 24px; height: 24px; }
        .icon-32 { width: 32px; height: 32px; }
        .icon-48 { width: 48px; height: 48px; }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1f2937; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #6b7280; }
        
        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-6px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        /* 镜像标签样式 */
        .image-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        /* 大小显示动画 */
        .size-animate {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            background-size: 200% 100%;
            animation: sizeShine 3s linear infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        @keyframes sizeShine {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 镜像层级显示 */
        .layer-indicator {
            position: relative;
            overflow: hidden;
        }
        
        .layer-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: layerScan 2s infinite;
        }
        
        @keyframes layerScan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        /* 安全扫描结果 */
        .security-good { border-left: 4px solid #22c55e; }
        .security-warning { border-left: 4px solid #eab308; }
        .security-critical { border-left: 4px solid #ef4444; }
        
        /* 镜像来源标识 */
        .official-badge {
            background: linear-gradient(135deg, #1e40af, #3b82f6);
            color: white;
            border-radius: 16px;
            padding: 4px 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .verified-badge {
            background: linear-gradient(135deg, #059669, #10b981);
            color: white;
            border-radius: 16px;
            padding: 4px 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        /* 下载进度条 */
        .download-progress {
            background: linear-gradient(90deg, #06b6d4, #0891b2);
            border-radius: 4px;
            height: 6px;
            position: relative;
            overflow: hidden;
        }
        
        .download-progress::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: downloadFlow 1.5s infinite;
        }
        
        @keyframes downloadFlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* 版本号样式 */
        .version-tag {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border-radius: 8px;
            padding: 2px 6px;
            font-size: 0.7rem;
            font-weight: 500;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        /* 镜像大小渐变 */
        .size-gradient {
            background: linear-gradient(135deg, #ec4899, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        registry: {
                            500: '#0891b2',
                            600: '#0e7490',
                        },
                        success: {
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            500: '#eab308',
                            600: '#ca8a04',
                        },
                        error: {
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    
    <!-- 设计系统展示区域 -->
    <div class="container mx-auto px-8 py-16">
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Container Images</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">高保真UI设计稿 - 镜像管理界面</p>
        </div>
        
        <!-- 颜色系统 -->
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">颜色系统</h2>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-8">
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-registry-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">仓库色</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#0891B2</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-success-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">安全通过</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#22C55E</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-warning-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">安全警告</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#EAB308</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-error-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">安全严重</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#EF4444</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">镜像标签</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">渐变色</p>
                </div>
            </div>
        </div>
        
        <!-- 组件设计系统 -->
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">镜像组件系统</h2>
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 镜像操作按钮 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">镜像操作</h3>
                        <div class="space-y-3">
                            <button class="w-full px-4 py-2 bg-registry-500 text-white rounded-lg font-medium hover:bg-registry-600 transition-all text-sm flex items-center">
                                <i class="fas fa-download mr-2"></i>拉取镜像
                            </button>
                            <button class="w-full px-4 py-2 bg-primary-500 text-white rounded-lg font-medium hover:bg-primary-600 transition-all text-sm flex items-center">
                                <i class="fas fa-rocket mr-2"></i>运行镜像
                            </button>
                            <button class="w-full px-4 py-2 bg-error-500 text-white rounded-lg font-medium hover:bg-error-600 transition-all text-sm flex items-center">
                                <i class="fas fa-trash mr-2"></i>删除镜像
                            </button>
                        </div>
                    </div>
                    
                    <!-- 镜像标签 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">镜像标签</h3>
                        <div class="space-y-3">
                            <div class="flex flex-wrap gap-2">
                                <span class="official-badge">Official</span>
                                <span class="verified-badge">Verified</span>
                                <span class="image-tag">latest</span>
                                <span class="version-tag">v1.21.0</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 安全扫描结果 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">安全扫描</h3>
                        <div class="space-y-3">
                            <div class="security-good p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-shield-alt text-green-500 mr-2"></i>
                                    <span class="text-sm font-medium text-green-800 dark:text-green-300">安全通过</span>
                                </div>
                            </div>
                            <div class="security-warning p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                    <span class="text-sm font-medium text-yellow-800 dark:text-yellow-300">2个警告</span>
                                </div>
                            </div>
                            <div class="security-critical p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-times-circle text-red-500 mr-2"></i>
                                    <span class="text-sm font-medium text-red-800 dark:text-red-300">严重漏洞</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 高保真界面设计 -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden">
            <!-- 模拟浏览器标题栏 -->
            <div class="bg-gray-200 dark:bg-gray-700 px-6 py-4 flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div class="flex-1 mx-4">
                    <div class="bg-white dark:bg-gray-600 rounded-lg px-4 py-1 text-sm text-gray-600 dark:text-gray-300">
                        https://registry-manager.com/images
                    </div>
                </div>
            </div>
            
            <!-- 界面内容 -->
            <div class="p-8">
                <!-- 页面头部 -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                            <i class="fas fa-compact-disc text-registry-500 mr-3 text-4xl"></i>
                            容器镜像
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400">管理本地和远程镜像仓库 • 共 28 个镜像</p>
                    </div>
                    <div class="flex space-x-4">
                        <button class="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all flex items-center">
                            <i class="fas fa-sync mr-2"></i>刷新镜像
                        </button>
                        <button class="px-6 py-3 bg-registry-500 text-white rounded-xl font-medium hover:bg-registry-600 transition-all transform hover:scale-105 shadow-lg flex items-center">
                            <i class="fas fa-search mr-2"></i>搜索镜像
                        </button>
                    </div>
                </div>
                
                <!-- 快速统计 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-br from-registry-50 to-registry-100 dark:from-registry-900 dark:to-registry-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-registry-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-compact-disc text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-registry-600 dark:text-registry-400 text-sm font-medium">本地镜像</p>
                                <p class="text-2xl font-bold text-registry-800 dark:text-registry-200">28</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-success-50 to-success-100 dark:from-success-900 dark:to-success-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-success-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-shield-alt text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-success-600 dark:text-success-400 text-sm font-medium">安全通过</p>
                                <p class="text-2xl font-bold text-success-800 dark:text-success-200">22</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-warning-50 to-warning-100 dark:from-warning-900 dark:to-warning-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-warning-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-warning-600 dark:text-warning-400 text-sm font-medium">有警告</p>
                                <p class="text-2xl font-bold text-warning-800 dark:text-warning-200">4</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-error-50 to-error-100 dark:from-error-900 dark:to-error-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-error-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-times-circle text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-error-600 dark:text-error-400 text-sm font-medium">严重漏洞</p>
                                <p class="text-2xl font-bold text-error-800 dark:text-error-200">2</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="mb-8">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1 relative">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索镜像名称、标签、仓库..." class="w-full pl-12 pr-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-registry-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                        </div>
                        <select class="px-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-registry-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            <option>所有仓库</option>
                            <option>Docker Hub</option>
                            <option>私有仓库</option>
                            <option>本地镜像</option>
                        </select>
                        <select class="px-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-registry-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            <option>按大小排序</option>
                            <option>按名称排序</option>
                            <option>按创建时间</option>
                            <option>按使用频率</option>
                        </select>
                    </div>
                </div>
                
                <!-- 镜像列表 -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    <!-- 镜像卡片 1 - Nginx -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover security-good">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-green-500 rounded-xl flex items-center justify-center layer-indicator">
                                    <i class="fas fa-server text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900 dark:text-white text-lg">nginx</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">官方镜像</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="official-badge">Official</span>
                                <span class="size-gradient">142MB</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="image-tag">latest</span>
                                <span class="version-tag">1.21.0</span>
                                <span class="version-tag">alpine</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                高性能Web服务器和反向代理服务器
                            </p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">架构</span>
                                <span class="font-medium text-gray-900 dark:text-white">linux/amd64</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">层数</span>
                                <span class="font-medium text-gray-900 dark:text-white">6 层</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">安全扫描</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                    <i class="fas fa-shield-alt mr-1"></i>通过
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">最后更新</span>
                                <span class="font-medium text-gray-900 dark:text-white">2天前</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-rocket mr-1"></i>运行
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-info mr-1"></i>详情
                            </button>
                            <button class="px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 镜像卡片 2 - MySQL -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover security-warning">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-orange-500 rounded-xl flex items-center justify-center layer-indicator">
                                    <i class="fas fa-database text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900 dark:text-white text-lg">mysql</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">官方镜像</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="official-badge">Official</span>
                                <span class="size-gradient">546MB</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="image-tag">8.0</span>
                                <span class="version-tag">8.0.28</span>
                                <span class="version-tag">latest</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                世界上最流行的开源数据库
                            </p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">架构</span>
                                <span class="font-medium text-gray-900 dark:text-white">linux/amd64</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">层数</span>
                                <span class="font-medium text-gray-900 dark:text-white">12 层</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">安全扫描</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100">
                                    <i class="fas fa-exclamation-triangle mr-1"></i>2个警告
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">最后更新</span>
                                <span class="font-medium text-gray-900 dark:text-white">5天前</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-rocket mr-1"></i>运行
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-info mr-1"></i>详情
                            </button>
                            <button class="px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 镜像卡片 3 - Redis -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover security-good">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-red-500 rounded-xl flex items-center justify-center layer-indicator">
                                    <i class="fas fa-memory text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900 dark:text-white text-lg">redis</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">官方镜像</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="official-badge">Official</span>
                                <span class="size-gradient">113MB</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="image-tag">7.0</span>
                                <span class="version-tag">7.0.0</span>
                                <span class="version-tag">alpine</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                内存数据结构存储，用作数据库、缓存
                            </p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">架构</span>
                                <span class="font-medium text-gray-900 dark:text-white">linux/amd64</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">层数</span>
                                <span class="font-medium text-gray-900 dark:text-white">5 层</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">安全扫描</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                    <i class="fas fa-shield-alt mr-1"></i>通过
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">最后更新</span>
                                <span class="font-medium text-gray-900 dark:text-white">1天前</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-rocket mr-1"></i>运行
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-info mr-1"></i>详情
                            </button>
                            <button class="px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 镜像卡片 4 - Node.js -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover security-good">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-green-600 rounded-xl flex items-center justify-center layer-indicator">
                                    <i class="fab fa-node-js text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900 dark:text-white text-lg">node</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">官方镜像</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="official-badge">Official</span>
                                <span class="size-gradient">993MB</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="image-tag">18</span>
                                <span class="version-tag">18.12.1</span>
                                <span class="version-tag">alpine</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                基于Chrome V8引擎的JavaScript运行时
                            </p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">架构</span>
                                <span class="font-medium text-gray-900 dark:text-white">linux/amd64</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">层数</span>
                                <span class="font-medium text-gray-900 dark:text-white">8 层</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">安全扫描</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                    <i class="fas fa-shield-alt mr-1"></i>通过
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">最后更新</span>
                                <span class="font-medium text-gray-900 dark:text-white">3天前</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-rocket mr-1"></i>运行
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-info mr-1"></i>详情
                            </button>
                            <button class="px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 镜像卡片 5 - Python -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover security-critical">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center layer-indicator">
                                    <i class="fab fa-python text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900 dark:text-white text-lg">python</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">官方镜像</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="official-badge">Official</span>
                                <span class="size-gradient">915MB</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="image-tag">3.10</span>
                                <span class="version-tag">3.10.8</span>
                                <span class="version-tag">slim</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                Python是一种编程语言，让您快速工作
                            </p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">架构</span>
                                <span class="font-medium text-gray-900 dark:text-white">linux/amd64</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">层数</span>
                                <span class="font-medium text-gray-900 dark:text-white">10 层</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">安全扫描</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-error-100 text-error-800 dark:bg-error-800 dark:text-error-100">
                                    <i class="fas fa-times-circle mr-1"></i>严重漏洞
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">最后更新</span>
                                <span class="font-medium text-gray-900 dark:text-white">7天前</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-warning-500 text-white rounded-lg text-sm font-medium hover:bg-warning-600 transition-all">
                                <i class="fas fa-exclamation-triangle mr-1"></i>风险运行
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-info mr-1"></i>详情
                            </button>
                            <button class="px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 镜像卡片 6 - 自定义镜像 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover security-good">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-14 h-14 bg-purple-500 rounded-xl flex items-center justify-center layer-indicator">
                                    <i class="fas fa-code text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-gray-900 dark:text-white text-lg">my-app</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">自定义镜像</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2">
                                <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium">Custom</span>
                                <span class="size-gradient">1.2GB</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="image-tag">v1.0.0</span>
                                <span class="version-tag">latest</span>
                                <span class="version-tag">prod</span>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                基于Node.js构建的自定义应用镜像
                            </p>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">架构</span>
                                <span class="font-medium text-gray-900 dark:text-white">linux/amd64</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">层数</span>
                                <span class="font-medium text-gray-900 dark:text-white">15 层</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">安全扫描</span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                    <i class="fas fa-shield-alt mr-1"></i>通过
                                </span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">构建时间</span>
                                <span class="font-medium text-gray-900 dark:text-white">昨天</span>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-rocket mr-1"></i>运行
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-info mr-1"></i>详情
                            </button>
                            <button class="px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 批量操作和分页 -->
                <div class="flex flex-col md:flex-row justify-between items-center mt-8 space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-registry-500 text-white rounded-lg hover:bg-registry-600 transition-all text-sm flex items-center">
                            <i class="fas fa-download mr-2"></i>批量拉取
                        </button>
                        <button class="px-4 py-2 bg-error-500 text-white rounded-lg hover:bg-error-600 transition-all text-sm flex items-center">
                            <i class="fas fa-trash mr-2"></i>批量删除
                        </button>
                        <button class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all text-sm flex items-center">
                            <i class="fas fa-broom mr-2"></i>清理悬挂镜像
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            显示 1-6 项，共 28 个镜像
                        </p>
                        <div class="flex space-x-2">
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-2 bg-registry-500 text-white rounded-lg">1</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">2</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">3</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">4</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">5</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 镜像卡片入场动画
            anime({
                targets: '.card-hover',
                opacity: [0, 1],
                // translateY: [50, 0],
                // rotate: [3, 0],
                delay: anime.stagger(120),
                duration: 900,
                easing: 'easeOutExpo'
            });
            
            // 统计卡片动画
            anime({
                targets: '.bg-gradient-to-br',
                scale: [0.8, 1],
                opacity: [0, 1],
                delay: anime.stagger(150),
                duration: 700,
                easing: 'easeOutBack'
            });
            
            // 标题动画
            anime({
                targets: 'h1',
                opacity: [0, 1],
                translateX: [-50, 0],
                duration: 1200,
                easing: 'easeOutExpo'
            });
            
            // 镜像大小数字动画
            const sizeElements = document.querySelectorAll('.size-gradient');
            sizeElements.forEach(element => {
                const finalText = element.textContent;
                element.textContent = '0MB';
                
                anime({
                    targets: element,
                    innerHTML: [0, parseInt(finalText)],
                    duration: 2000,
                    easing: 'easeOutQuart',
                    delay: 500,
                    update: function(anim) {
                        element.textContent = Math.round(anim.animatables[0].target.innerHTML) + 'MB';
                        if (finalText.includes('GB')) {
                            element.textContent = (Math.round(anim.animatables[0].target.innerHTML) / 1000).toFixed(1) + 'GB';
                        }
                    }
                });
            });
            
            // 层级扫描动画
            anime({
                targets: '.layer-indicator',
                boxShadow: [
                    '0 0 0 0 rgba(59, 130, 246, 0.4)',
                    '0 0 0 10px rgba(59, 130, 246, 0)'
                ],
                duration: 2000,
                loop: true,
                easing: 'easeOutQuad'
            });
            
            // 安全扫描结果动画
            const securityElements = document.querySelectorAll('[class*="security-"]');
            securityElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    anime({
                        targets: this,
                        scale: [1, 1.02],
                        duration: 200,
                        easing: 'easeOutQuad'
                    });
                });
                
                element.addEventListener('mouseleave', function() {
                    anime({
                        targets: this,
                        scale: [1.02, 1],
                        duration: 200,
                        easing: 'easeOutQuad'
                    });
                });
            });
            
            // 标签悬浮动画
            document.querySelectorAll('.image-tag, .version-tag').forEach(tag => {
                tag.addEventListener('mouseenter', function() {
                    anime({
                        targets: this,
                        scale: [1, 1.1],
                        duration: 150,
                        easing: 'easeOutQuad'
                    });
                });
                
                tag.addEventListener('mouseleave', function() {
                    anime({
                        targets: this,
                        scale: [1.1, 1],
                        duration: 150,
                        easing: 'easeOutQuad'
                    });
                });
            });
        });
        
        // 模拟镜像操作
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function(e) {
                if (this.textContent.includes('运行') || this.textContent.includes('详情') || this.textContent.includes('拉取')) {
                    e.preventDefault();
                    
                    // 按钮点击反馈
                    anime({
                        targets: this,
                        scale: [1, 0.95, 1],
                        duration: 200,
                        easing: 'easeInOutQuad'
                    });
                    
                    // 如果是运行按钮，显示进度
                    if (this.textContent.includes('运行')) {
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>启动中';
                        
                        setTimeout(() => {
                            this.innerHTML = originalText;
                        }, 2000);
                    }
                }
            });
        });
    </script>

    <!-- Prism.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
