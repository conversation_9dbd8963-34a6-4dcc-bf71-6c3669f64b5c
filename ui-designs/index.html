<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes & Docker Manager - 设计系统总览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="./shared/design-system.css">
    <script src="./shared/components.js"></script>
    <style>
        .page-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .page-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .page-card:hover .page-icon {
            transform: scale(1.1);
        }
        
        .page-icon {
            transition: transform 0.2s ease;
        }
        
        .page-thumbnail {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px 8px 0 0;
            position: relative;
            overflow: hidden;
        }
        
        .page-thumbnail::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
            opacity: 0.1;
        }
        
        .page-thumbnail.kubernetes {
            background: linear-gradient(135deg, #326ce5 0%, #4285f4 100%);
        }
        
        .page-thumbnail.docker {
            background: linear-gradient(135deg, #0db7ed 0%, #2496ed 100%);
        }
        
        .page-thumbnail.logs {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        
        .status-indicator {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #22c55e;
            box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    <i class="fas fa-palette text-blue-500 mr-3"></i>
                    Kubernetes & Docker Manager
                </h1>
                <p class="text-xl text-gray-600 mb-2">高保真UI设计系统 & Mockup展示</p>
                <p class="text-gray-500">专业级8px基线网格 • 现代扁平化设计 • 完整组件库 • 响应式布局</p>
                
                <!-- 设计规范标签 -->
                <div class="mt-6 flex flex-wrap justify-center gap-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-ruler-combined mr-1"></i>
                        8px基线网格
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <i class="fas fa-palette mr-1"></i>
                        色彩系统
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        <i class="fas fa-cube mr-1"></i>
                        组件库
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                        <i class="fas fa-mobile-alt mr-1"></i>
                        响应式
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                        <i class="fas fa-eye mr-1"></i>
                        像素级精细
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-3xl font-bold text-blue-600">7</div>
                <div class="text-sm text-gray-600 mt-1">高保真页面</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-3xl font-bold text-green-600">50+</div>
                <div class="text-sm text-gray-600 mt-1">设计组件</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-3xl font-bold text-purple-600">8px</div>
                <div class="text-sm text-gray-600 mt-1">基线网格</div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="text-3xl font-bold text-orange-600">100%</div>
                <div class="text-sm text-gray-600 mt-1">响应式支持</div>
            </div>
        </div>

        <!-- 页面展示 -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">
                <i class="fas fa-th-large mr-2"></i>
                高保真页面展示
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Kubernetes Deployments -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./kubernetes-deployments.html', '_blank')">
                    <div class="page-thumbnail kubernetes">
                        <div class="status-indicator"></div>
                        <i class="page-icon fas fa-dharmachakra text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Kubernetes Deployments</h3>
                        <p class="text-gray-600 text-sm mb-4">Kubernetes部署管理界面，展示部署状态、副本数量、资源使用情况等关键信息</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                完成
                            </span>
                            <span class="text-xs text-gray-500">主页面</span>
                        </div>
                    </div>
                </div>

                <!-- Kubernetes Deployment Details -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./kubernetes-deployment-details.html', '_blank')">
                    <div class="page-thumbnail kubernetes">
                        <div class="status-indicator"></div>
                        <i class="page-icon fas fa-info-circle text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Deployment Details</h3>
                        <p class="text-gray-600 text-sm mb-4">单个部署的详细信息页面，包含YAML配置、Pod状态、事件日志等</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                完成
                            </span>
                            <span class="text-xs text-gray-500">详情页</span>
                        </div>
                    </div>
                </div>

                <!-- Docker Containers -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./docker-containers.html', '_blank')">
                    <div class="page-thumbnail docker">
                        <div class="status-indicator"></div>
                        <i class="page-icon fab fa-docker text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Docker Containers</h3>
                        <p class="text-gray-600 text-sm mb-4">Docker容器管理界面，显示容器状态、资源占用、端口映射等信息</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                完成
                            </span>
                            <span class="text-xs text-gray-500">主页面</span>
                        </div>
                    </div>
                </div>

                <!-- Docker Container Details -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./docker-container-details.html', '_blank')">
                    <div class="page-thumbnail docker">
                        <div class="status-indicator"></div>
                        <i class="page-icon fas fa-server text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Container Details</h3>
                        <p class="text-gray-600 text-sm mb-4">容器详细信息页面，包含运行时配置、环境变量、网络设置等</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                完成
                            </span>
                            <span class="text-xs text-gray-500">详情页</span>
                        </div>
                    </div>
                </div>

                <!-- Container Images -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./container-images.html', '_blank')">
                    <div class="page-thumbnail docker">
                        <div class="status-indicator"></div>
                        <i class="page-icon fas fa-layer-group text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Container Images</h3>
                        <p class="text-gray-600 text-sm mb-4">容器镜像管理界面，展示镜像列表、大小、标签、安全扫描结果等</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                完成
                            </span>
                            <span class="text-xs text-gray-500">主页面</span>
                        </div>
                    </div>
                </div>

                <!-- Container Image Details -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./container-image-details.html', '_blank')">
                    <div class="page-thumbnail docker">
                        <div class="status-indicator"></div>
                        <i class="page-icon fas fa-image text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Image Details</h3>
                        <p class="text-gray-600 text-sm mb-4">镜像详细信息页面，包含层级结构、漏洞扫描、构建历史等</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                完成
                            </span>
                            <span class="text-xs text-gray-500">详情页</span>
                        </div>
                    </div>
                </div>

                <!-- Log Viewer -->
                <div class="page-card bg-white rounded-lg shadow-md overflow-hidden" onclick="window.open('./log-viewer.html', '_blank')">
                    <div class="page-thumbnail logs">
                        <div class="status-indicator"></div>
                        <i class="page-icon fas fa-file-alt text-6xl text-white"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">日志查看器</h3>
                        <p class="text-gray-600 text-sm mb-4">实时日志查看界面，支持日志过滤、搜索，错误日志突出显示</p>
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-fire mr-1"></i>
                                新增
                            </span>
                            <span class="text-xs text-gray-500">日志界面</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设计系统特性 -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">
                <i class="fas fa-star mr-2"></i>
                设计系统特性
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-ruler-combined text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">8px基线网格</h3>
                    </div>
                    <p class="text-gray-600">严格遵循8px基线网格系统，确保所有元素对齐一致，视觉层次清晰</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-palette text-green-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">专业色彩系统</h3>
                    </div>
                    <p class="text-gray-600">完整的色彩体系，包含主色、辅色、状态色，支持明暗主题切换</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-cube text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">可复用组件库</h3>
                    </div>
                    <p class="text-gray-600">标准化的UI组件，包含按钮、表格、卡片、表单等，提高开发效率</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-mobile-alt text-orange-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">完全响应式</h3>
                    </div>
                    <p class="text-gray-600">适配所有设备尺寸，从手机到大屏显示器都有完美的显示效果</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-eye text-red-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">像素级精细</h3>
                    </div>
                    <p class="text-gray-600">每个元素都经过精心调校，阴影、圆角、间距都符合现代设计标准</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-magic text-indigo-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">微交互动画</h3>
                    </div>
                    <p class="text-gray-600">精心设计的动画效果，提升用户体验，让界面更加生动有趣</p>
                </div>
            </div>
        </div>

        <!-- 技术栈 -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">
                <i class="fas fa-code mr-2"></i>
                技术栈 & 工具
            </h2>
            
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6 text-center">
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                        <i class="fab fa-html5 text-2xl text-orange-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">HTML5</span>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                        <i class="fab fa-css3-alt text-2xl text-blue-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">CSS3</span>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                        <i class="fab fa-js-square text-2xl text-yellow-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">JavaScript</span>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-wind text-2xl text-cyan-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">Tailwind CSS</span>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-code text-2xl text-purple-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">Prism.js</span>
                </div>
                
                <div class="flex flex-col items-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                        <i class="fas fa-play text-2xl text-pink-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700">Anime.js</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部 -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center">
                <h3 class="text-lg font-semibold mb-4">Kubernetes & Docker Manager</h3>
                <p class="text-gray-400 mb-4">专业级高保真UI设计系统</p>
                <div class="flex justify-center space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fab fa-figma text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-palette text-xl"></i>
                    </a>
                </div>
                <div class="mt-8 pt-8 border-t border-gray-800">
                    <p class="text-gray-500 text-sm">
                        © 2024 Kubernetes & Docker Manager. 所有设计遵循现代UI/UX规范.
                    </p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
