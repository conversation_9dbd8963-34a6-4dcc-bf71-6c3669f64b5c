<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Container Image Details - 高保真设计稿</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Anime.js 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    
    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="./shared/design-system.css">

    <style>
        /* 镜像层级可视化 */
        .layer-visualizer {
            position: relative;
            background: linear-gradient(135deg, #1e293b, #334155);
            border-radius: 12px;
            padding: 20px;
            overflow: hidden;
        }
        
        .layer-item {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .layer-item:hover {
            background: rgba(59, 130, 246, 0.2);
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateX(4px);
        }
        
        .layer-item::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(to bottom, #3b82f6, #06b6d4);
        }
        
        /* 安全扫描结果 */
        .vulnerability-item {
            border-left: 4px solid;
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }
        
        .vulnerability-critical {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .vulnerability-high {
            border-color: #f97316;
            background: rgba(249, 115, 22, 0.1);
        }
        
        .vulnerability-medium {
            border-color: #eab308;
            background: rgba(234, 179, 8, 0.1);
        }
        
        .vulnerability-low {
            border-color: #22c55e;
            background: rgba(34, 197, 94, 0.1);
        }
        
        /* Dockerfile 语法高亮 */
        .dockerfile-line {
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 4px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .dockerfile-instruction {
            color: #8b5cf6;
            font-weight: 600;
        }
        
        .dockerfile-argument {
            color: #22c55e;
        }
        
        .dockerfile-string {
            color: #06b6d4;
        }
        
        .dockerfile-comment {
            color: #6b7280;
            font-style: italic;
        }
        
        /* 镜像标签云 */
        .tag-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
        }
        
        .tag-cloud .tag {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
        }
        
        .tag-cloud .tag:hover {
            transform: scale(1.1);
        }
        
        /* 镜像历史时间轴 */
        .history-timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .history-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3b82f6, #06b6d4);
        }
        
        .history-item {
            position: relative;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 16px;
        }
        
        .dark .history-item {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .history-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 20px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #3b82f6;
            border: 2px solid #1e293b;
        }
        
        /* 文件系统树形结构 */
        .fs-tree-container {
            background: #1e293b;
            border-radius: 8px;
            padding: 16px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .fs-node {
            padding: 2px 0;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .fs-node:hover {
            background-color: rgba(59, 130, 246, 0.2);
        }
        
        .fs-folder {
            color: #fbbf24;
        }
        
        .fs-file {
            color: #94a3b8;
        }
        
        .fs-executable {
            color: #22c55e;
        }
        
        .fs-indent-1 { padding-left: 20px; }
        .fs-indent-2 { padding-left: 40px; }
        .fs-indent-3 { padding-left: 60px; }
        .fs-indent-4 { padding-left: 80px; }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        registry: {
                            500: '#0891b2',
                            600: '#0e7490',
                        }
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    
    <div class="container mx-auto px-8 py-16">
        <!-- 设计系统展示 -->
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Container Image Details</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">高保真UI设计稿 - 镜像详情页面</p>
        </div>

        <div id="color-system"></div>
        <div id="component-system"></div>

        <!-- 高保真界面设计 -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden">
            <div id="browser-header"></div>
            
            <!-- 页面内容 -->
            <div class="p-8">
                <!-- 面包屑导航 -->
                <div id="breadcrumb"></div>
                
                <!-- 页面头部 -->
                <div class="flex justify-between items-start mb-8">
                    <div class="flex items-start space-x-6">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center">
                            <i class="fas fa-server text-white text-3xl"></i>
                        </div>
                        <div>
                            <div class="flex items-center space-x-3 mb-2">
                                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">nginx</h1>
                                <span class="badge badge-primary">Official</span>
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                                <span><i class="fas fa-tag mr-1"></i>1.21.0</span>
                                <span><i class="fas fa-weight-hanging mr-1"></i>142 MB</span>
                                <span><i class="fas fa-layer-group mr-1"></i>6 层</span>
                                <span><i class="fas fa-clock mr-1"></i>2天前</span>
                            </div>
                            <div class="tag-cloud">
                                <span class="tag tag-latest">latest</span>
                                <span class="tag tag-version">1.21.0</span>
                                <span class="tag tag-latest">alpine</span>
                                <span class="tag tag-version">stable</span>
                                <span class="tag tag-latest">mainline</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button class="btn btn-ghost">
                            <i class="fas fa-download mr-2"></i>拉取
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-rocket mr-2"></i>运行
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-search mr-2"></i>扫描
                        </button>
                        <button class="btn btn-error">
                            <i class="fas fa-trash mr-2"></i>删除
                        </button>
                    </div>
                </div>

                <!-- 快速统计 -->
                <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
                    <div id="stat-size"></div>
                    <div id="stat-layers"></div>
                    <div id="stat-security"></div>
                    <div id="stat-pulls"></div>
                    <div id="stat-arch"></div>
                </div>

                <!-- 主要内容 -->
                <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    <!-- 左侧内容 -->
                    <div class="xl:col-span-2 space-y-8">
                        <!-- 镜像描述 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">镜像描述</h2>
                            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                                Nginx (发音为 "engine-x") 是一个高性能的 HTTP 和反向代理服务器，也是一个 IMAP/POP3/SMTP 服务器。
                                这个官方镜像包含了最新的 Nginx 稳定版本，基于 Alpine Linux 构建，体积小巧且安全。
                                适用于静态网站托管、负载均衡、API 网关等多种场景。
                            </p>
                            
                            <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">1B+</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">下载次数</div>
                                </div>
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">4.9/5</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">用户评分</div>
                                </div>
                                <div class="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600">24/7</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">技术支持</div>
                                </div>
                            </div>
                        </div>

                        <!-- 镜像层级 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">镜像层级结构</h2>
                            
                            <div class="layer-visualizer">
                                <div class="layer-item">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-mono text-sm text-blue-400">sha256:e7c96db7181b</div>
                                            <div class="text-xs text-gray-400 mt-1">ADD file:f278386b0cef68136... 5.6MB</div>
                                        </div>
                                        <span class="text-sm text-gray-400">24MB</span>
                                    </div>
                                </div>
                                
                                <div class="layer-item">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-mono text-sm text-blue-400">sha256:8cc3d16a4a55</div>
                                            <div class="text-xs text-gray-400 mt-1">RUN /bin/sh -c apk add --no-cache nginx</div>
                                        </div>
                                        <span class="text-sm text-gray-400">45MB</span>
                                    </div>
                                </div>
                                
                                <div class="layer-item">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-mono text-sm text-blue-400">sha256:b49b96bfa4da</div>
                                            <div class="text-xs text-gray-400 mt-1">COPY file:nginx.conf /etc/nginx/nginx.conf</div>
                                        </div>
                                        <span class="text-sm text-gray-400">2KB</span>
                                    </div>
                                </div>
                                
                                <div class="layer-item">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-mono text-sm text-blue-400">sha256:f56be85fc22e</div>
                                            <div class="text-xs text-gray-400 mt-1">RUN /bin/sh -c mkdir -p /var/log/nginx</div>
                                        </div>
                                        <span class="text-sm text-gray-400">0B</span>
                                    </div>
                                </div>
                                
                                <div class="layer-item">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-mono text-sm text-blue-400">sha256:3c9de8c3cf67</div>
                                            <div class="text-xs text-gray-400 mt-1">EXPOSE 80/tcp 443/tcp</div>
                                        </div>
                                        <span class="text-sm text-gray-400">0B</span>
                                    </div>
                                </div>
                                
                                <div class="layer-item">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-mono text-sm text-blue-400">sha256:9e7b4e7a3c19</div>
                                            <div class="text-xs text-gray-400 mt-1">CMD ["nginx", "-g", "daemon off;"]</div>
                                        </div>
                                        <span class="text-sm text-gray-400">0B</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 安全扫描结果 -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">安全扫描结果</h2>
                                <div class="flex items-center space-x-2">
                                    <span class="badge badge-success">
                                        <i class="fas fa-shield-alt mr-1"></i>通过
                                    </span>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">最后扫描: 1小时前</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-4 gap-4 mb-6">
                                <div class="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                    <div class="text-lg font-bold text-red-600">0</div>
                                    <div class="text-xs text-red-600">严重</div>
                                </div>
                                <div class="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                                    <div class="text-lg font-bold text-orange-600">1</div>
                                    <div class="text-xs text-orange-600">高危</div>
                                </div>
                                <div class="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                                    <div class="text-lg font-bold text-yellow-600">3</div>
                                    <div class="text-xs text-yellow-600">中危</div>
                                </div>
                                <div class="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <div class="text-lg font-bold text-green-600">12</div>
                                    <div class="text-xs text-green-600">低危</div>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="vulnerability-item vulnerability-high">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="font-semibold text-orange-700 dark:text-orange-400">CVE-2023-1234</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">OpenSSL 版本过旧可能存在安全漏洞</p>
                                            <div class="text-xs text-gray-500 mt-2">影响包: openssl 1.1.1</div>
                                        </div>
                                        <span class="badge badge-warning">高危</span>
                                    </div>
                                </div>
                                
                                <div class="vulnerability-item vulnerability-medium">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="font-semibold text-yellow-700 dark:text-yellow-400">CVE-2023-5678</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">zlib 库可能存在缓冲区溢出</p>
                                            <div class="text-xs text-gray-500 mt-2">影响包: zlib 1.2.11</div>
                                        </div>
                                        <span class="badge badge-warning">中危</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dockerfile -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Dockerfile</h2>
                                <button class="btn btn-sm btn-ghost">
                                    <i class="fas fa-copy mr-2"></i>复制
                                </button>
                            </div>
                            
                            <div id="dockerfile-content"></div>
                        </div>
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="space-y-6">
                        <!-- 镜像信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">镜像信息</h3>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">仓库</span>
                                    <span>nginx</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">标签</span>
                                    <span>1.21.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">摘要</span>
                                    <span class="font-mono text-xs">sha256:abc123...</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">架构</span>
                                    <span>linux/amd64</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">OS</span>
                                    <span>linux</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">创建时间</span>
                                    <span>2天前</span>
                                </div>
                            </div>
                        </div>

                        <!-- 配置信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">配置信息</h3>
                            <div class="space-y-4">
                                <div>
                                    <h4 class="font-semibold text-sm mb-2">暴露端口</h4>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="badge badge-info">80/tcp</span>
                                        <span class="badge badge-info">443/tcp</span>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold text-sm mb-2">工作目录</h4>
                                    <span class="font-mono text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/usr/share/nginx/html</span>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold text-sm mb-2">入口点</h4>
                                    <span class="font-mono text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/docker-entrypoint.sh</span>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold text-sm mb-2">默认命令</h4>
                                    <span class="font-mono text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">nginx -g "daemon off;"</span>
                                </div>
                            </div>
                        </div>

                        <!-- 多架构支持 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">多架构支持</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-microchip text-blue-500"></i>
                                        <span class="font-medium">linux/amd64</span>
                                    </div>
                                    <span class="badge badge-success">可用</span>
                                </div>
                                
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-microchip text-green-500"></i>
                                        <span class="font-medium">linux/arm64</span>
                                    </div>
                                    <span class="badge badge-success">可用</span>
                                </div>
                                
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-microchip text-purple-500"></i>
                                        <span class="font-medium">linux/arm/v7</span>
                                    </div>
                                    <span class="badge badge-success">可用</span>
                                </div>
                            </div>
                        </div>

                        <!-- 使用统计 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">使用统计</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span>本月下载</span>
                                        <span class="font-bold">1.2M</span>
                                    </div>
                                    <div class="progress progress-success">
                                        <div class="progress-bar" style="width: 85%"></div>
                                    </div>
                                </div>
                                
                                <div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span>今日下载</span>
                                        <span class="font-bold">45K</span>
                                    </div>
                                    <div class="progress progress-info">
                                        <div class="progress-bar" style="width: 65%"></div>
                                    </div>
                                </div>
                                
                                <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                                    <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                        <div>总下载量: 1.2B+</div>
                                        <div>星标数: 15.6K</div>
                                        <div>用户评分: 4.9/5.0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件系统结构 -->
                <div class="mt-8">
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">文件系统结构</h2>
                            <span class="badge badge-info">展示主要目录</span>
                        </div>
                        
                        <div class="fs-tree-container">
                            <div class="fs-node fs-folder">
                                <i class="fas fa-folder mr-2"></i>/
                            </div>
                            <div class="fs-node fs-folder fs-indent-1">
                                <i class="fas fa-folder mr-2"></i>etc/
                            </div>
                            <div class="fs-node fs-folder fs-indent-2">
                                <i class="fas fa-folder mr-2"></i>nginx/
                            </div>
                            <div class="fs-node fs-file fs-indent-3">
                                <i class="fas fa-file mr-2"></i>nginx.conf
                            </div>
                            <div class="fs-node fs-folder fs-indent-3">
                                <i class="fas fa-folder mr-2"></i>conf.d/
                            </div>
                            <div class="fs-node fs-file fs-indent-4">
                                <i class="fas fa-file mr-2"></i>default.conf
                            </div>
                            <div class="fs-node fs-folder fs-indent-1">
                                <i class="fas fa-folder mr-2"></i>usr/
                            </div>
                            <div class="fs-node fs-folder fs-indent-2">
                                <i class="fas fa-folder mr-2"></i>share/
                            </div>
                            <div class="fs-node fs-folder fs-indent-3">
                                <i class="fas fa-folder mr-2"></i>nginx/
                            </div>
                            <div class="fs-node fs-folder fs-indent-4">
                                <i class="fas fa-folder mr-2"></i>html/
                            </div>
                            <div class="fs-node fs-file fs-indent-4">
                                <i class="fas fa-file mr-2"></i>index.html
                            </div>
                            <div class="fs-node fs-executable fs-indent-2">
                                <i class="fas fa-cog mr-2"></i>sbin/nginx
                            </div>
                            <div class="fs-node fs-folder fs-indent-1">
                                <i class="fas fa-folder mr-2"></i>var/
                            </div>
                            <div class="fs-node fs-folder fs-indent-2">
                                <i class="fas fa-folder mr-2"></i>log/
                            </div>
                            <div class="fs-node fs-folder fs-indent-3">
                                <i class="fas fa-folder mr-2"></i>nginx/
                            </div>
                            <div class="fs-node fs-file fs-indent-4">
                                <i class="fas fa-file mr-2"></i>access.log
                            </div>
                            <div class="fs-node fs-file fs-indent-4">
                                <i class="fas fa-file mr-2"></i>error.log
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共组件 -->
    <script src="./shared/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 生成设计系统
            document.getElementById('color-system').innerHTML = DesignSystem.generateColorSystem();
            document.getElementById('component-system').innerHTML = DesignSystem.generateComponentSystem();
            
            // 生成浏览器头部
            document.getElementById('browser-header').innerHTML = DesignSystem.generateBrowserHeader('https://registry.hub.docker.com/nginx/1.21.0');
            
            // 生成面包屑
            document.getElementById('breadcrumb').innerHTML = DesignSystem.generateBreadcrumb([
                { label: '首页', href: '#', icon: 'fas fa-home' },
                { label: '镜像仓库', href: '#', icon: 'fas fa-compact-disc' },
                { label: 'nginx', href: '#' },
                { label: '1.21.0', href: '#' }
            ]);
            
            // 生成统计卡片
            document.getElementById('stat-size').innerHTML = DesignSystem.generateStatCard('镜像大小', '142MB', 'fas fa-weight-hanging', 'blue');
            document.getElementById('stat-layers').innerHTML = DesignSystem.generateStatCard('层数', '6', 'fas fa-layer-group', 'green');
            document.getElementById('stat-security').innerHTML = DesignSystem.generateStatCard('安全扫描', '通过', 'fas fa-shield-alt', 'green');
            document.getElementById('stat-pulls').innerHTML = DesignSystem.generateStatCard('下载量', '1.2B+', 'fas fa-download', 'purple');
            document.getElementById('stat-arch').innerHTML = DesignSystem.generateStatCard('支持架构', '3', 'fas fa-microchip', 'yellow');
            
            // 生成 Dockerfile
            const dockerfileContent = `# 使用官方 Alpine Linux 作为基础镜像
FROM alpine:3.14

# 设置维护者信息
LABEL maintainer="<EMAIL>"

# 安装 Nginx
RUN apk add --no-cache nginx

# 复制 Nginx 配置文件
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# 创建必要的目录
RUN mkdir -p /var/log/nginx \\
    && mkdir -p /var/cache/nginx \\
    && mkdir -p /var/run/nginx

# 设置工作目录
WORKDIR /usr/share/nginx/html

# 复制默认网页文件
COPY index.html .

# 暴露端口
EXPOSE 80 443

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]`;
            
            document.getElementById('dockerfile-content').innerHTML = DesignSystem.generateCodeBlock(dockerfileContent, 'dockerfile');
        });
    </script>
</body>
</html>
