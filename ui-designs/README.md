# Kubernetes & Docker Manager - 高保真UI设计系统

## 📋 项目概述

这是一个专业级的Kubernetes & Docker容器管理平台的高保真UI设计系统，严格遵循现代UI/UX设计规范，提供完整的设计系统和组件库。

## 🎨 设计规范

### 基线网格系统
- **8px基线网格**: 所有元素严格按照8px的倍数进行间距和尺寸设置
- **响应式断点**: 移动端(<768px)、平板端(768-1024px)、桌面端(>1024px)
- **像素级精细**: 每个元素都经过精心调校，确保视觉一致性

### 色彩系统
- **主色调**: 蓝色系(#3B82F6) - Kubernetes风格
- **辅助色**: Docker蓝(#0EA5E9)、成功绿(#22C55E)、警告黄(#F59E0B)、错误红(#EF4444)
- **中性色**: 完整的灰度色阶，从50到900
- **状态色**: 专门的日志级别颜色系统
- **明暗主题**: 完整支持深色模式

### 字体系统
- **主字体**: Inter - 现代无衬线字体，优秀的可读性
- **等宽字体**: JetBrains Mono - 用于代码显示和日志查看

## 📁 文件结构

```
ui-designs/
├── index.html                           # 设计系统总览页面
├── kubernetes-deployments.html          # Kubernetes部署管理主页面
├── kubernetes-deployment-details.html   # 部署详情页面
├── docker-containers.html              # Docker容器管理主页面
├── docker-container-details.html       # 容器详情页面
├── container-images.html               # 容器镜像管理主页面
├── container-image-details.html        # 镜像详情页面
├── log-viewer.html                      # 日志查看器(新增)
├── shared/
│   ├── design-system.css               # 公共设计系统样式
│   └── components.js                   # 公共组件库
└── README.md                           # 本文档
```

## 🎯 页面功能

### 主页面 (Dashboard Pages)
1. **Kubernetes Deployments** - K8s部署管理
   - 部署状态概览
   - 副本数量监控
   - 资源使用情况
   - 批量操作功能

2. **Docker Containers** - 容器管理
   - 容器状态监控
   - 资源占用统计
   - 端口映射管理
   - 容器操作控制

3. **Container Images** - 镜像管理
   - 镜像列表展示
   - 安全扫描结果
   - 标签管理
   - 存储空间统计

### 详情页面 (Detail Pages)
1. **Deployment Details** - 部署详情
   - YAML配置查看
   - Pod状态监控
   - 事件日志
   - 扩缩容操作

2. **Container Details** - 容器详情
   - 运行时配置
   - 环境变量
   - 网络设置
   - 性能指标

3. **Image Details** - 镜像详情
   - 层级结构分析
   - 漏洞扫描报告
   - 构建历史
   - 依赖关系

### 特殊功能页面
4. **Log Viewer** - 日志查看器 ⭐ **新增特性**
   - **实时日志流**: 支持实时日志更新
   - **颜色区分**: 错误日志突出显示，一眼可见
   - **级别过滤**: 支持按INFO/WARN/ERROR/DEBUG/SUCCESS过滤
   - **搜索功能**: 全文搜索和高亮显示
   - **统计面板**: 各级别日志数量统计
   - **自动滚动**: 可开关的自动滚动功能

## 🛠️ 技术栈

### 前端框架
- **HTML5**: 语义化标签，无障碍访问支持
- **CSS3**: 现代CSS特性，网格布局，弹性盒子
- **JavaScript ES6+**: 模块化开发，现代语法

### UI框架与库
- **Tailwind CSS**: 原子化CSS框架，快速构建
- **Font Awesome**: 图标库，丰富的图标资源
- **Prism.js**: 代码高亮显示
- **Anime.js**: 轻量级动画库

### 设计工具
- **Google Fonts**: Web字体服务
- **Unsplash/Lorem Picsum**: 占位图片服务

## 🎨 设计系统组件

### 基础组件
- **按钮系统**: Primary, Secondary, Success, Warning, Danger
- **表单组件**: Input, Select, Checkbox, Radio, Textarea
- **卡片组件**: 基础卡片，统计卡片，信息卡片
- **标签系统**: 状态标签，分类标签，数量标签

### 数据展示
- **表格组件**: 响应式表格，排序，分页
- **列表组件**: 简单列表，描述列表，操作列表
- **进度条**: 线性进度条，环形进度条
- **统计图表**: 数值显示，趋势图表

### 导航组件
- **面包屑**: 层级导航
- **标签页**: 内容切换
- **分页器**: 数据分页
- **菜单系统**: 下拉菜单，侧边菜单

### 特殊组件
- **代码块**: 语法高亮，复制功能
- **日志显示**: 多级别颜色区分，时间戳显示
- **状态指示器**: 运行状态，健康状态
- **操作按钮组**: 批量操作，快捷操作

## 📱 响应式设计

### 断点系统
- **Mobile**: < 768px - 单列布局，堆叠显示
- **Tablet**: 768px - 1024px - 双列布局，适配触控
- **Desktop**: > 1024px - 多列布局，完整功能

### 适配策略
- **弹性网格**: CSS Grid + Flexbox混合布局
- **相对单位**: rem/em单位，确保缩放一致性
- **媒体查询**: 渐进增强的响应式设计
- **触控优化**: 移动端触控目标尺寸优化

## 🌈 日志查看器特色功能

### 视觉区分系统
- **错误日志**: 红色边框 + 背景闪烁动画，确保错误一眼可见
- **警告日志**: 橙色边框 + 图标提示
- **成功日志**: 绿色边框 + 成功图标
- **调试日志**: 灰色显示，降低视觉权重
- **信息日志**: 蓝色边框，标准显示

### 交互功能
- **实时更新**: 模拟实时日志流
- **过滤系统**: 按日志级别快速过滤
- **搜索高亮**: 关键词搜索并高亮显示
- **统计面板**: 各级别日志数量实时统计
- **导出功能**: 支持日志导出

## 🚀 使用说明

### 快速开始
1. 打开 `index.html` 查看设计系统总览
2. 点击各个页面卡片进入具体页面
3. 所有页面都支持在新窗口中打开

### 组件使用
```html
<!-- 引入设计系统 -->
<link rel="stylesheet" href="./shared/design-system.css">
<script src="./shared/components.js"></script>

<!-- 使用组件 -->
<button class="btn btn-primary">
    <i class="fas fa-play"></i> 开始部署
</button>
```

### 自定义主题
```css
:root {
    --color-primary-500: #your-color;
    --color-success-500: #your-color;
    /* 更多自定义颜色 */
}
```

## 📋 开发规范

### CSS命名规范
- **BEM命名**: 块级元素__子元素--修饰符
- **组件前缀**: 所有组件以功能前缀命名
- **状态类**: is-active, is-disabled, is-loading

### JavaScript规范
- **ES6+语法**: 使用现代JavaScript特性
- **模块化**: 组件独立，可复用
- **命名规范**: 驼峰命名，语义化

### 设计规范
- **8px网格**: 所有间距、尺寸必须是8的倍数
- **颜色使用**: 严格按照设计系统色彩
- **字体大小**: 12px, 14px, 16px, 18px, 20px, 24px递进

## 🔄 更新日志

### v1.2.0 (最新)
- ✨ 新增日志查看器页面
- 🎨 错误日志特殊颜色区分和动画效果
- 📊 日志统计面板
- 🔍 日志搜索和过滤功能
- 📱 全面响应式优化

### v1.1.0
- 🏗️ 完成所有详情页面设计
- 🎨 统一设计系统和组件库
- 📁 文件结构重构为统一目录
- 🔧 公共样式和组件抽取

### v1.0.0
- 🎉 初始版本发布
- 📄 完成三个主页面设计
- 🎨 建立基础设计系统
- 📐 实现8px基线网格

## 📞 支持与反馈

如有任何问题或建议，请通过以下方式联系：
- 📧 Email: <EMAIL>
- 💬 Issue: 在项目仓库提交Issue
- 📖 文档: 查看在线文档站点

---

**🎨 设计理念**: 追求像素级完美，注重用户体验，让复杂的容器管理变得简单直观。

**⚡ 技术标准**: 严格遵循Web标准，确保兼容性和可访问性。

**🔮 未来规划**: 持续优化，添加更多交互特效和高级功能。
