<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes Deployments - 高保真设计稿</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Prism.js 代码高亮 -->
    <link id="prism-theme" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Anime.js 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* 8px基线网格系统 */
        .spacing-8 { margin: 8px; }
        .spacing-16 { margin: 16px; }
        .spacing-24 { margin: 24px; }
        .spacing-32 { margin: 32px; }
        
        /* 图标标准尺寸 */
        .icon-24 { width: 24px; height: 24px; }
        .icon-32 { width: 32px; height: 32px; }
        .icon-48 { width: 48px; height: 48px; }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1f2937; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #6b7280; }
        
        /* 玻璃态效果 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-effect-dark {
            background: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        /* 状态指示器 */
        .status-dot {
            position: relative;
            display: inline-block;
        }
        
        .status-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-running::before { background-color: #22c55e; }
        .status-pending::before { background-color: #eab308; }
        .status-error::before { background-color: #ef4444; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 进度条动画 */
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            background-size: 200% 100%;
            animation: progressShine 2s linear infinite;
        }
        
        @keyframes progressShine {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        success: {
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            500: '#eab308',
                            600: '#ca8a04',
                        },
                        error: {
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    
    <!-- 设计系统展示区域 -->
    <div class="container mx-auto px-8 py-16">
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Kubernetes Deployments</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">高保真UI设计稿 - 部署管理界面</p>
        </div>
        
        <!-- 颜色系统 -->
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">颜色系统</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-primary-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">主色调</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#3B82F6</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-success-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">成功状态</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#22C55E</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-warning-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">警告状态</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#EAB308</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-error-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">错误状态</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#EF4444</p>
                </div>
            </div>
        </div>
        
        <!-- 组件设计系统 -->
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">组件设计系统</h2>
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 按钮组件 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">按钮组件</h3>
                        <div class="space-y-4">
                            <button class="w-full px-6 py-3 bg-primary-500 text-white rounded-xl font-medium hover:bg-primary-600 transition-all duration-200 transform hover:scale-105 shadow-lg">
                                <i class="fas fa-plus mr-2"></i>创建部署
                            </button>
                            <button class="w-full px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-xl font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200">
                                取消操作
                            </button>
                        </div>
                    </div>
                    
                    <!-- 状态标签 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">状态标签</h3>
                        <div class="space-y-3">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                <span class="status-dot status-running mr-2"></span>运行中
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100">
                                <span class="status-dot status-pending mr-2"></span>待部署
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-error-100 text-error-800 dark:bg-error-800 dark:text-error-100">
                                <span class="status-dot status-error mr-2"></span>错误
                            </span>
                        </div>
                    </div>
                    
                    <!-- 输入框组件 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">输入框组件</h3>
                        <div class="space-y-4">
                            <div class="relative">
                                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" placeholder="搜索部署..." class="w-full pl-12 pr-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-primary-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 高保真界面设计 -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden">
            <!-- 模拟浏览器标题栏 -->
            <div class="bg-gray-200 dark:bg-gray-700 px-6 py-4 flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div class="flex-1 mx-4">
                    <div class="bg-white dark:bg-gray-600 rounded-lg px-4 py-1 text-sm text-gray-600 dark:text-gray-300">
                        https://k8s-manager.com/deployments
                    </div>
                </div>
            </div>
            
            <!-- 界面内容 -->
            <div class="p-8">
                <!-- 页面头部 -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Kubernetes 部署</h1>
                        <p class="text-gray-600 dark:text-gray-400">管理集群中的所有部署 • 共 24 个部署</p>
                    </div>
                    <div class="flex space-x-4">
                        <button class="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all flex items-center">
                            <i class="fas fa-download mr-2"></i>导出配置
                        </button>
                        <button class="px-6 py-3 bg-primary-500 text-white rounded-xl font-medium hover:bg-primary-600 transition-all transform hover:scale-105 shadow-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>新建部署
                        </button>
                    </div>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="mb-8">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1 relative">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索部署名称、命名空间..." class="w-full pl-12 pr-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-primary-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                        </div>
                        <select class="px-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-primary-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            <option>所有命名空间</option>
                            <option>default</option>
                            <option>kube-system</option>
                            <option>production</option>
                        </select>
                        <select class="px-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-primary-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            <option>所有状态</option>
                            <option>运行中</option>
                            <option>待部署</option>
                            <option>错误</option>
                        </select>
                    </div>
                </div>
                
                <!-- 部署列表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    <!-- 部署卡片 1 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover cursor-pointer">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-dharmachakra text-primary-600 dark:text-primary-400 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">web-frontend</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">production</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                <span class="status-dot status-running mr-2"></span>运行中
                            </span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                <span class="font-medium text-gray-900 dark:text-white">3 / 3</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">镜像版本</span>
                                <span class="font-medium text-gray-900 dark:text-white">v2.1.0</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">运行时间</span>
                                <span class="font-medium text-gray-900 dark:text-white">12天</span>
                            </div>
                        </div>
                        
                        <!-- 资源使用进度条 -->
                        <div class="mb-4">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>CPU 使用率</span>
                                <span>65%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 65%"></div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                        </div>
                    </div>
                    
                    <!-- 部署卡片 2 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover cursor-pointer">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-server text-success-600 dark:text-success-400 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">api-backend</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">production</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                <span class="status-dot status-running mr-2"></span>运行中
                            </span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                <span class="font-medium text-gray-900 dark:text-white">5 / 5</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">镜像版本</span>
                                <span class="font-medium text-gray-900 dark:text-white">v1.8.3</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">运行时间</span>
                                <span class="font-medium text-gray-900 dark:text-white">8天</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>CPU 使用率</span>
                                <span>42%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 42%"></div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                        </div>
                    </div>
                    
                    <!-- 部署卡片 3 - 有问题的部署 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-red-200 dark:border-red-800 card-hover cursor-pointer">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-error-100 dark:bg-error-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-database text-error-600 dark:text-error-400 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">database-cache</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">default</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-error-100 text-error-800 dark:bg-error-800 dark:text-error-100">
                                <span class="status-dot status-error mr-2"></span>错误
                            </span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                <span class="font-medium text-error-600 dark:text-error-400">1 / 3</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">镜像版本</span>
                                <span class="font-medium text-gray-900 dark:text-white">v3.2.1</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">错误信息</span>
                                <span class="font-medium text-error-600 dark:text-error-400 text-xs">ImagePullBackOff</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>健康状态</span>
                                <span>33%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-error-500 h-2 rounded-full" style="width: 33%"></div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-error-500 text-white rounded-lg text-sm font-medium hover:bg-error-600 transition-all">
                                <i class="fas fa-redo mr-1"></i>重启
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-bug mr-1"></i>调试
                            </button>
                        </div>
                    </div>
                    
                    <!-- 部署卡片 4 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover cursor-pointer">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-cogs text-warning-600 dark:text-warning-400 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">worker-queue</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">production</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100">
                                <span class="status-dot status-pending mr-2"></span>更新中
                            </span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                <span class="font-medium text-gray-900 dark:text-white">2 / 4</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">镜像版本</span>
                                <span class="font-medium text-warning-600 dark:text-warning-400">v1.5.0 → v1.6.0</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">更新策略</span>
                                <span class="font-medium text-gray-900 dark:text-white">滚动更新</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>更新进度</span>
                                <span>50%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-warning-500 h-2 rounded-full progress-bar" style="width: 50%"></div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-warning-500 text-white rounded-lg text-sm font-medium hover:bg-warning-600 transition-all">
                                <i class="fas fa-pause mr-1"></i>暂停
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-history mr-1"></i>回滚
                            </button>
                        </div>
                    </div>
                    
                    <!-- 部署卡片 5 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover cursor-pointer">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-shield-alt text-primary-600 dark:text-primary-400 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">auth-service</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">security</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                <span class="status-dot status-running mr-2"></span>运行中
                            </span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                <span class="font-medium text-gray-900 dark:text-white">2 / 2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">镜像版本</span>
                                <span class="font-medium text-gray-900 dark:text-white">v2.0.1</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">运行时间</span>
                                <span class="font-medium text-gray-900 dark:text-white">3天</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>CPU 使用率</span>
                                <span>28%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 28%"></div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                        </div>
                    </div>
                    
                    <!-- 部署卡片 6 -->
                    <div class="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover cursor-pointer">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-chart-line text-success-600 dark:text-success-400 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900 dark:text-white">monitoring</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">kube-system</p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                <span class="status-dot status-running mr-2"></span>运行中
                            </span>
                        </div>
                        
                        <div class="space-y-3 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                <span class="font-medium text-gray-900 dark:text-white">1 / 1</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">镜像版本</span>
                                <span class="font-medium text-gray-900 dark:text-white">v9.3.2</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600 dark:text-gray-400">运行时间</span>
                                <span class="font-medium text-gray-900 dark:text-white">25天</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                <span>CPU 使用率</span>
                                <span>15%</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="progress-bar h-2 rounded-full" style="width: 15%"></div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-all">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="flex justify-between items-center mt-8">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        显示 1-6 项，共 24 项部署
                    </p>
                    <div class="flex space-x-2">
                        <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="px-3 py-2 bg-primary-500 text-white rounded-lg">1</button>
                        <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">2</button>
                        <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">3</button>
                        <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 卡片入场动画
            anime({
                targets: '.card-hover',
                opacity: [0, 1],
                translateY: [30, 0],
                delay: anime.stagger(100),
                duration: 800,
                easing: 'easeOutExpo'
            });
            
            // 标题动画
            anime({
                targets: 'h1',
                opacity: [0, 1],
                translateY: [-20, 0],
                duration: 1000,
                easing: 'easeOutExpo'
            });
            
            // 搜索框聚焦效果
            const searchInput = document.querySelector('input[type="text"]');
            searchInput.addEventListener('focus', function() {
                anime({
                    targets: this,
                    scale: [1, 1.02],
                    duration: 200,
                    easing: 'easeOutQuad'
                });
            });
            
            searchInput.addEventListener('blur', function() {
                anime({
                    targets: this,
                    scale: [1.02, 1],
                    duration: 200,
                    easing: 'easeOutQuad'
                });
            });
        });
    </script>

    <!-- Prism.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
