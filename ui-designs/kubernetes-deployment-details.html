<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes Deployment Details - 高保真设计稿</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Anime.js 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    
    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="./shared/design-system.css">

    <style>
        /* Kubernetes 特有样式 */
        .k8s-blue { color: #326ce5; }
        .k8s-bg { background: linear-gradient(135deg, #326ce5, #4285f4); }
        
        /* Pod 状态颜色 */
        .pod-running { background: linear-gradient(135deg, #22c55e, #16a34a); }
        .pod-pending { background: linear-gradient(135deg, #eab308, #ca8a04); }
        .pod-failed { background: linear-gradient(135deg, #ef4444, #dc2626); }
        .pod-succeeded { background: linear-gradient(135deg, #06b6d4, #0891b2); }
        
        /* 资源使用图表 */
        .resource-chart {
            background: linear-gradient(90deg, #22c55e 0%, #eab308 70%, #ef4444 100%);
            height: 8px;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        
        .resource-indicator {
            position: absolute;
            top: 0;
            height: 100%;
            width: 2px;
            background: white;
            box-shadow: 0 0 4px rgba(0,0,0,0.3);
        }
        
        /* 网络拓扑图样式 */
        .network-node {
            transition: all 0.3s ease;
        }
        
        .network-node:hover {
            transform: scale(1.1);
            filter: brightness(1.2);
        }
        
        /* YAML 高亮 */
        .yaml-key { color: #8b5cf6; }
        .yaml-value { color: #22c55e; }
        .yaml-string { color: #06b6d4; }
        .yaml-number { color: #f59e0b; }
        
        /* 时间轴样式 */
        .timeline {
            position: relative;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #3b82f6, #06b6d4);
        }
        
        .timeline-item {
            position: relative;
            padding-left: 50px;
            margin-bottom: 24px;
        }
        
        .timeline-icon {
            position: absolute;
            left: 8px;
            top: 8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            z-index: 1;
        }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        kubernetes: {
                            500: '#326ce5',
                            600: '#2563eb',
                        }
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    
    <div class="container mx-auto px-8 py-16">
        <!-- 设计系统展示 -->
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Kubernetes Deployment Details</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">高保真UI设计稿 - Deployment 详情页面</p>
        </div>

        <div id="color-system"></div>
        <div id="component-system"></div>

        <!-- 高保真界面设计 -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden">
            <div id="browser-header"></div>
            
            <!-- 页面内容 -->
            <div class="p-8">
                <!-- 面包屑导航 -->
                <div id="breadcrumb"></div>
                
                <!-- 页面头部 -->
                <div class="flex justify-between items-start mb-8">
                    <div class="flex items-start space-x-4">
                        <div class="w-16 h-16 k8s-bg rounded-2xl flex items-center justify-center">
                            <i class="fas fa-dharmachakra text-white text-2xl"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">nginx-deployment</h1>
                            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                <span><i class="fas fa-layer-group mr-1"></i>Deployment</span>
                                <span><i class="fas fa-cube mr-1"></i>default namespace</span>
                                <span><i class="fas fa-clock mr-1"></i>创建于 2天前</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-3">
                                <span class="badge badge-success">
                                    <i class="fas fa-check mr-1"></i>运行中
                                </span>
                                <span class="badge badge-info">
                                    <i class="fas fa-sync mr-1"></i>已就绪
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button class="btn btn-ghost">
                            <i class="fas fa-edit mr-2"></i>编辑
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-redo mr-2"></i>重启
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-expand-arrows-alt mr-2"></i>扩缩容
                        </button>
                        <button class="btn btn-error">
                            <i class="fas fa-trash mr-2"></i>删除
                        </button>
                    </div>
                </div>

                <!-- 快速统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div id="stat-pods"></div>
                    <div id="stat-replicas"></div>
                    <div id="stat-cpu"></div>
                    <div id="stat-memory"></div>
                </div>

                <!-- 主要内容区域 -->
                <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    <!-- 左侧内容 -->
                    <div class="xl:col-span-2 space-y-8">
                        <!-- Pod 列表 -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Pod 实例</h2>
                                <span class="badge badge-info">3 个 Pod</span>
                            </div>
                            
                            <div class="space-y-4">
                                <!-- Pod 1 -->
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 pod-running rounded-lg flex items-center justify-center">
                                                <i class="fas fa-cube text-white"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900 dark:text-white">nginx-deployment-7d6c8f9b8-abc12</h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                                    <span><i class="fas fa-server mr-1"></i>node-1</span>
                                                    <span><i class="fas fa-network-wired mr-1"></i>10.244.1.15</span>
                                                    <span><i class="fas fa-clock mr-1"></i>运行 2天</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <span class="badge badge-success">Running</span>
                                            <button class="btn btn-sm btn-ghost">
                                                <i class="fas fa-external-link-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- 资源使用 -->
                                    <div class="mt-4 grid grid-cols-2 gap-4">
                                        <div>
                                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                <span>CPU</span>
                                                <span>45m / 500m</span>
                                            </div>
                                            <div class="progress progress-success">
                                                <div class="progress-bar" style="width: 9%"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                <span>Memory</span>
                                                <span>128Mi / 512Mi</span>
                                            </div>
                                            <div class="progress progress-success">
                                                <div class="progress-bar" style="width: 25%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pod 2 -->
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 pod-running rounded-lg flex items-center justify-center">
                                                <i class="fas fa-cube text-white"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900 dark:text-white">nginx-deployment-7d6c8f9b8-def34</h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                                    <span><i class="fas fa-server mr-1"></i>node-2</span>
                                                    <span><i class="fas fa-network-wired mr-1"></i>10.244.2.16</span>
                                                    <span><i class="fas fa-clock mr-1"></i>运行 2天</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <span class="badge badge-success">Running</span>
                                            <button class="btn btn-sm btn-ghost">
                                                <i class="fas fa-external-link-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4 grid grid-cols-2 gap-4">
                                        <div>
                                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                <span>CPU</span>
                                                <span>52m / 500m</span>
                                            </div>
                                            <div class="progress progress-success">
                                                <div class="progress-bar" style="width: 10.4%"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                <span>Memory</span>
                                                <span>145Mi / 512Mi</span>
                                            </div>
                                            <div class="progress progress-warning">
                                                <div class="progress-bar" style="width: 28%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pod 3 -->
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 pod-running rounded-lg flex items-center justify-center">
                                                <i class="fas fa-cube text-white"></i>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900 dark:text-white">nginx-deployment-7d6c8f9b8-ghi56</h3>
                                                <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                                    <span><i class="fas fa-server mr-1"></i>node-3</span>
                                                    <span><i class="fas fa-network-wired mr-1"></i>10.244.3.17</span>
                                                    <span><i class="fas fa-clock mr-1"></i>运行 2天</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <span class="badge badge-success">Running</span>
                                            <button class="btn btn-sm btn-ghost">
                                                <i class="fas fa-external-link-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4 grid grid-cols-2 gap-4">
                                        <div>
                                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                <span>CPU</span>
                                                <span>38m / 500m</span>
                                            </div>
                                            <div class="progress progress-success">
                                                <div class="progress-bar" style="width: 7.6%"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                <span>Memory</span>
                                                <span>98Mi / 512Mi</span>
                                            </div>
                                            <div class="progress progress-success">
                                                <div class="progress-bar" style="width: 19%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 事件时间轴 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">事件时间轴</h2>
                            
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-icon bg-green-500">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <h3 class="font-semibold text-gray-900 dark:text-white">成功创建 Pod</h3>
                                            <span class="text-xs text-gray-500">2分钟前</span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Successfully assigned default/nginx-deployment-7d6c8f9b8-ghi56 to node-3</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-icon bg-blue-500">
                                        <i class="fas fa-download"></i>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <h3 class="font-semibold text-gray-900 dark:text-white">镜像拉取完成</h3>
                                            <span class="text-xs text-gray-500">5分钟前</span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Successfully pulled image "nginx:1.21.0"</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-icon bg-purple-500">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <h3 class="font-semibold text-gray-900 dark:text-white">容器启动</h3>
                                            <span class="text-xs text-gray-500">8分钟前</span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Started container nginx</p>
                                    </div>
                                </div>
                                
                                <div class="timeline-item">
                                    <div class="timeline-icon bg-cyan-500">
                                        <i class="fas fa-play"></i>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <h3 class="font-semibold text-gray-900 dark:text-white">Deployment 创建</h3>
                                            <span class="text-xs text-gray-500">2天前</span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Deployment nginx-deployment created successfully</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">基本信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">命名空间</span>
                                    <span class="font-medium">default</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">策略</span>
                                    <span class="font-medium">RollingUpdate</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">副本数</span>
                                    <span class="font-medium">3 / 3</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">镜像</span>
                                    <span class="font-medium font-mono text-sm">nginx:1.21.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">选择器</span>
                                    <span class="font-medium font-mono text-sm">app=nginx</span>
                                </div>
                            </div>
                        </div>

                        <!-- 服务端口 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">服务端口</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium">HTTP</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">容器端口 80</div>
                                    </div>
                                    <span class="badge badge-success">TCP</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <div>
                                        <div class="font-medium">HTTPS</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">容器端口 443</div>
                                    </div>
                                    <span class="badge badge-success">TCP</span>
                                </div>
                            </div>
                        </div>

                        <!-- 标签 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">标签</h3>
                            <div class="flex flex-wrap gap-2">
                                <span class="tag tag-version">app=nginx</span>
                                <span class="tag tag-latest">version=v1.21.0</span>
                                <span class="tag tag-version">env=production</span>
                                <span class="tag tag-latest">tier=frontend</span>
                            </div>
                        </div>

                        <!-- 资源配额 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">资源配额</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span>CPU 请求</span>
                                        <span>100m</span>
                                    </div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span>CPU 限制</span>
                                        <span>500m</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span>内存请求</span>
                                        <span>128Mi</span>
                                    </div>
                                    <div class="flex justify-between text-sm mb-2">
                                        <span>内存限制</span>
                                        <span>512Mi</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- YAML 配置 -->
                <div class="mt-8">
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">YAML 配置</h2>
                            <div class="flex space-x-2">
                                <button class="btn btn-sm btn-ghost">
                                    <i class="fas fa-download mr-2"></i>下载
                                </button>
                                <button class="btn btn-sm btn-ghost">
                                    <i class="fas fa-copy mr-2"></i>复制
                                </button>
                            </div>
                        </div>
                        
                        <div id="yaml-config"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共组件 -->
    <script src="./shared/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 生成设计系统
            document.getElementById('color-system').innerHTML = DesignSystem.generateColorSystem();
            document.getElementById('component-system').innerHTML = DesignSystem.generateComponentSystem();
            
            // 生成浏览器头部
            document.getElementById('browser-header').innerHTML = DesignSystem.generateBrowserHeader('https://k8s-manager.com/deployments/nginx-deployment');
            
            // 生成面包屑
            document.getElementById('breadcrumb').innerHTML = DesignSystem.generateBreadcrumb([
                { label: '首页', href: '#', icon: 'fas fa-home' },
                { label: 'Kubernetes', href: '#', icon: 'fas fa-dharmachakra' },
                { label: 'Deployments', href: '#' },
                { label: 'nginx-deployment', href: '#' }
            ]);
            
            // 生成统计卡片
            document.getElementById('stat-pods').innerHTML = DesignSystem.generateStatCard('运行 Pods', '3', 'fas fa-cube', 'green');
            document.getElementById('stat-replicas').innerHTML = DesignSystem.generateStatCard('副本状态', '3/3', 'fas fa-clone', 'blue');
            document.getElementById('stat-cpu').innerHTML = DesignSystem.generateStatCard('CPU 使用', '9%', 'fas fa-microchip', 'yellow');
            document.getElementById('stat-memory').innerHTML = DesignSystem.generateStatCard('内存使用', '24%', 'fas fa-memory', 'purple');
            
            // 生成 YAML 配置
            const yamlConfig = `apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  namespace: default
  labels:
    app: nginx
    version: v1.21.0
    env: production
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.21.0
        ports:
        - containerPort: 80
          protocol: TCP
        - containerPort: 443
          protocol: TCP
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: NGINX_PORT
          value: "80"
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config`;
            
            document.getElementById('yaml-config').innerHTML = DesignSystem.generateCodeBlock(yamlConfig, 'yaml');
        });
    </script>
</body>
</html>
