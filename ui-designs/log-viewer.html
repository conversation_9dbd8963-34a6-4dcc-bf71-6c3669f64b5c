<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器 - Kubernetes & Docker Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link rel="stylesheet" href="./shared/design-system.css">
    <script src="./shared/components.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* 与其他页面一致的深色主题样式 */
        .log-viewer {
            height: calc(100vh - 300px);
            min-height: 400px;
        }
        
        .log-filter-btn {
            transition: all 0.2s ease;
        }
        
        .log-filter-btn.active {
            transform: scale(1.05);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }
        
        .log-search-highlight {
            background-color: rgba(255, 255, 0, 0.3);
            padding: 1px 2px;
            border-radius: 2px;
        }
        
        .log-stats-card {
            transition: all 0.3s ease;
        }
        
        .log-stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        /* 日志级别指示器 */
        .log-level-indicator {
            width: 4px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            border-radius: 0 2px 2px 0;
        }
        
        .log-level-indicator.error {
            background: linear-gradient(45deg, #dc2626, #ef4444);
            box-shadow: 0 0 8px rgba(220, 38, 38, 0.3);
        }
        
        .log-level-indicator.warn {
            background: linear-gradient(45deg, #d97706, #f59e0b);
            box-shadow: 0 0 8px rgba(217, 119, 6, 0.3);
        }
        
        .log-level-indicator.info {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
        }
        
        .log-level-indicator.debug {
            background: linear-gradient(45deg, #4b5563, #6b7280);
        }
        
        .log-level-indicator.success {
            background: linear-gradient(45deg, #16a34a, #22c55e);
        }
        
        /* 错误日志闪烁效果 */
        @keyframes errorPulse {
            0%, 100% { 
                background-color: rgba(239, 68, 68, 0.05);
                border-left-color: #dc2626;
            }
            50% { 
                background-color: rgba(239, 68, 68, 0.15);
                border-left-color: #ef4444;
            }
        }
        
        .log-line.error {
            animation: errorPulse 2s ease-in-out infinite;
        }
        
        /* 日志级别标签样式 */
        .log-level-tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 50px;
            text-align: center;
        }
        
        .log-level-tag.error {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
        }
        
        .log-level-tag.warn {
            background: linear-gradient(135deg, #d97706, #f59e0b);
            color: white;
            box-shadow: 0 2px 4px rgba(217, 119, 6, 0.3);
        }
        
        .log-level-tag.info {
            background: linear-gradient(135deg, #2563eb, #3b82f6);
            color: white;
        }
        
        .log-level-tag.debug {
            background: linear-gradient(135deg, #4b5563, #6b7280);
            color: white;
        }
        
        .log-level-tag.success {
            background: linear-gradient(135deg, #16a34a, #22c55e);
            color: white;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1f2937; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #6b7280; }
        
        /* 深色主题配色 */
        .dark-card {
            background: rgba(31, 41, 55, 0.8);
            border: 1px solid rgba(75, 85, 99, 0.3);
            backdrop-filter: blur(10px);
        }
        
        .dark-card:hover {
            background: rgba(31, 41, 55, 0.9);
            border-color: rgba(75, 85, 99, 0.5);
        }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        success: {
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            500: '#eab308',
                            600: '#ca8a04',
                        },
                        error: {
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-900 dark:bg-gray-900 text-gray-100 dark:text-gray-100 min-h-screen">
    <!-- 设计系统展示区 -->
    <div class="bg-gray-800 dark:bg-gray-800 border-b border-gray-700 dark:border-gray-700 p-6 mb-8">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-2xl font-bold text-gray-100 dark:text-gray-100 mb-6">Kubernetes & Docker Manager - 设计系统</h1>
            
            <!-- 颜色系统 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">颜色系统</h2>
                <div id="color-system" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                    <!-- 将通过 JavaScript 动态生成 -->
                </div>
            </div>
            
            <!-- 按钮系统 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">按钮系统</h2>
                <div class="flex flex-wrap gap-4">
                    <button class="btn btn-primary"><i class="fas fa-play"></i> Primary</button>
                    <button class="btn btn-secondary"><i class="fas fa-pause"></i> Secondary</button>
                    <button class="btn btn-success"><i class="fas fa-check"></i> Success</button>
                    <button class="btn btn-warning"><i class="fas fa-exclamation-triangle"></i> Warning</button>
                    <button class="btn btn-danger"><i class="fas fa-times"></i> Danger</button>
                    <button class="btn btn-primary btn-sm">Small</button>
                    <button class="btn btn-primary btn-lg">Large</button>
                </div>
            </div>
            
            <!-- 状态标签 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">状态标签</h2>
                <div class="flex flex-wrap gap-4">
                    <span class="badge badge-success"><i class="fas fa-check-circle"></i> Running</span>
                    <span class="badge badge-warning"><i class="fas fa-exclamation-triangle"></i> Warning</span>
                    <span class="badge badge-error"><i class="fas fa-times-circle"></i> Error</span>
                    <span class="badge badge-info"><i class="fas fa-info-circle"></i> Info</span>
                    <span class="badge badge-gray"><i class="fas fa-pause-circle"></i> Pending</span>
                </div>
            </div>
            
            <!-- 日志级别标签 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">日志级别标签</h2>
                <div class="flex flex-wrap gap-4">
                    <span class="log-level-tag error">ERROR</span>
                    <span class="log-level-tag warn">WARN</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-level-tag debug">DEBUG</span>
                    <span class="log-level-tag success">SUCCESS</span>
                </div>
            </div>
            
            <!-- 进度条 -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">进度条</h2>
                <div class="space-y-4">
                    <div class="progress"><div class="progress-bar" style="width: 75%"></div></div>
                    <div class="progress"><div class="progress-bar success" style="width: 50%"></div></div>
                    <div class="progress"><div class="progress-bar warning" style="width: 90%"></div></div>
                    <div class="progress"><div class="progress-bar error" style="width: 25%"></div></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 页面头部 -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">日志查看器</h1>
                    <p class="mt-2 text-gray-600">实时查看和分析容器日志，快速定位问题</p>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="btn btn-secondary">
                        <i class="fas fa-download"></i>
                        导出日志
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i>
                        刷新
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="log-stats-card bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-2xl text-blue-500"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">INFO</p>
                        <p class="text-2xl font-bold text-gray-900">1,234</p>
                    </div>
                </div>
            </div>
            
            <div class="log-stats-card bg-white rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-2xl text-yellow-500"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">WARNING</p>
                        <p class="text-2xl font-bold text-gray-900">89</p>
                    </div>
                </div>
            </div>
            
            <div class="log-stats-card bg-white rounded-lg shadow-md p-6 border-l-4 border-red-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle text-2xl text-red-500 animate-pulse"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">ERROR</p>
                        <p class="text-2xl font-bold text-red-600">23</p>
                    </div>
                </div>
            </div>
            
            <div class="log-stats-card bg-white rounded-lg shadow-md p-6 border-l-4 border-gray-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-bug text-2xl text-gray-500"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">DEBUG</p>
                        <p class="text-2xl font-bold text-gray-900">456</p>
                    </div>
                </div>
            </div>
            
            <div class="log-stats-card bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-2xl text-green-500"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">SUCCESS</p>
                        <p class="text-2xl font-bold text-gray-900">678</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <!-- 容器选择和时间范围 -->
                <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">容器:</label>
                        <select class="form-select text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option>nginx-deployment-7d4c8c4f8-xyz123</option>
                            <option>api-server-6b9c5d7e8-abc456</option>
                            <option>redis-cache-5a8b9c7d6-def789</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <label class="text-sm font-medium text-gray-700">时间范围:</label>
                        <select class="form-select text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                            <option>最近1小时</option>
                            <option>最近6小时</option>
                            <option>最近24小时</option>
                            <option>最近7天</option>
                            <option>自定义范围</option>
                        </select>
                    </div>
                </div>
                
                <!-- 日志级别过滤器 -->
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-700">过滤级别:</span>
                    <div class="flex space-x-2">
                        <button class="log-filter-btn btn btn-sm btn-secondary active" data-level="all">
                            <i class="fas fa-list"></i> 全部
                        </button>
                        <button class="log-filter-btn btn btn-sm" style="background-color: var(--error-600); color: white;" data-level="error">
                            <i class="fas fa-times-circle"></i> 错误
                        </button>
                        <button class="log-filter-btn btn btn-sm" style="background-color: var(--warning-600); color: white;" data-level="warn">
                            <i class="fas fa-exclamation-triangle"></i> 警告
                        </button>
                        <button class="log-filter-btn btn btn-sm" style="background-color: var(--primary-600); color: white;" data-level="info">
                            <i class="fas fa-info-circle"></i> 信息
                        </button>
                        <button class="log-filter-btn btn btn-sm" style="background-color: var(--gray-600); color: white;" data-level="debug">
                            <i class="fas fa-bug"></i> 调试
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 搜索框 -->
            <div class="mt-4 flex items-center space-x-4">
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" 
                           id="log-search"
                           class="form-input block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="搜索日志内容...">
                </div>
                <button class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <button class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    清除
                </button>
            </div>
        </div>

        <!-- 日志查看器 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-terminal mr-2"></i>
                        实时日志流
                    </h3>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span class="text-sm text-gray-600">实时更新</span>
                        </div>
                        <button class="btn btn-sm btn-secondary">
                            <i class="fas fa-pause"></i>
                            暂停
                        </button>
                        <button class="btn btn-sm btn-secondary">
                            <i class="fas fa-expand"></i>
                            全屏
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="log-viewer log-container">
                <!-- 示例日志数据 -->
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:15</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Starting nginx server on port 80</span>
                </div>
                
                <div class="log-line success" data-level="success">
                    <div class="log-level-indicator success"></div>
                    <span class="log-timestamp">10:30:16</span>
                    <span class="log-level-tag success">SUCCESS</span>
                    <span class="log-message">Server successfully started and listening on port 80</span>
                </div>
                
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:17</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Loading configuration from /etc/nginx/nginx.conf</span>
                </div>
                
                <div class="log-line warn" data-level="warn">
                    <div class="log-level-indicator warn"></div>
                    <span class="log-timestamp">10:30:18</span>
                    <span class="log-level-tag warn">WARN</span>
                    <span class="log-message">⚠️ Configuration file has deprecated directive 'ssl_protocols TLSv1'</span>
                </div>
                
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:19</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Enabling gzip compression for text/html, text/css, application/json</span>
                </div>
                
                <div class="log-line error" data-level="error">
                    <div class="log-level-indicator error"></div>
                    <span class="log-timestamp">10:30:20</span>
                    <span class="log-level-tag error">ERROR</span>
                    <span class="log-message">🔥 Failed to connect to database: connection refused at localhost:5432</span>
                </div>
                
                <div class="log-line error" data-level="error">
                    <div class="log-level-indicator error"></div>
                    <span class="log-timestamp">10:30:21</span>
                    <span class="log-level-tag error">ERROR</span>
                    <span class="log-message">💥 Critical: Unable to initialize database connection pool</span>
                </div>
                
                <div class="log-line debug" data-level="debug">
                    <div class="log-level-indicator debug"></div>
                    <span class="log-timestamp">10:30:22</span>
                    <span class="log-level-tag debug">DEBUG</span>
                    <span class="log-message">Database connection parameters: host=localhost, port=5432, user=app_user</span>
                </div>
                
                <div class="log-line warn" data-level="warn">
                    <div class="log-level-indicator warn"></div>
                    <span class="log-timestamp">10:30:23</span>
                    <span class="log-level-tag warn">WARN</span>
                    <span class="log-message">⚠️ Retrying database connection in 5 seconds (attempt 1/3)</span>
                </div>
                
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:24</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Received HTTP GET request for /api/health from *************</span>
                </div>
                
                <div class="log-line error" data-level="error">
                    <div class="log-level-indicator error"></div>
                    <span class="log-timestamp">10:30:25</span>
                    <span class="log-level-tag error">ERROR</span>
                    <span class="log-message">🚨 Health check failed: database connection timeout after 30s</span>
                </div>
                
                <div class="log-line warn" data-level="warn">
                    <div class="log-level-indicator warn"></div>
                    <span class="log-timestamp">10:30:26</span>
                    <span class="log-level-tag warn">WARN</span>
                    <span class="log-message">⚠️ Retrying database connection in 5 seconds (attempt 2/3)</span>
                </div>
                
                <div class="log-line success" data-level="success">
                    <div class="log-level-indicator success"></div>
                    <span class="log-timestamp">10:30:31</span>
                    <span class="log-level-tag success">SUCCESS</span>
                    <span class="log-message">✅ Database connection established successfully</span>
                </div>
                
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:32</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Health check endpoint responding normally</span>
                </div>
                
                <div class="log-line debug" data-level="debug">
                    <div class="log-level-indicator debug"></div>
                    <span class="log-timestamp">10:30:33</span>
                    <span class="log-level-tag debug">DEBUG</span>
                    <span class="log-message">Connection pool stats: active=2, idle=8, max=10</span>
                </div>
                
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:34</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Processing user authentication request</span>
                </div>
                
                <div class="log-line error" data-level="error">
                    <div class="log-level-indicator error"></div>
                    <span class="log-timestamp">10:30:35</span>
                    <span class="log-level-tag error">ERROR</span>
                    <span class="log-message">🔐 Authentication failed: invalid credentials for user 'admin'</span>
                </div>
                
                <div class="log-line warn" data-level="warn">
                    <div class="log-level-indicator warn"></div>
                    <span class="log-timestamp">10:30:36</span>
                    <span class="log-level-tag warn">WARN</span>
                    <span class="log-message">⚠️ Multiple failed login attempts detected from IP *************</span>
                </div>
                
                <div class="log-line error" data-level="error">
                    <div class="log-level-indicator error"></div>
                    <span class="log-timestamp">10:30:37</span>
                    <span class="log-level-tag error">ERROR</span>
                    <span class="log-message">🛡️ Security alert: IP ************* has been temporarily blocked</span>
                </div>
                
                <div class="log-line info" data-level="info">
                    <div class="log-level-indicator info"></div>
                    <span class="log-timestamp">10:30:38</span>
                    <span class="log-level-tag info">INFO</span>
                    <span class="log-message">Scheduled backup task started</span>
                </div>
                
                <div class="log-line success" data-level="success">
                    <div class="log-level-indicator success"></div>
                    <span class="log-timestamp">10:30:45</span>
                    <span class="log-level-tag success">SUCCESS</span>
                    <span class="log-message">✅ Database backup completed successfully (size: 1.2GB)</span>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-1"></i>
                        最后更新: 2024-01-20 10:30:45
                    </span>
                    <span class="text-sm text-gray-600">
                        <i class="fas fa-database mr-1"></i>
                        总计: 2,480 条日志
                    </span>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">自动滚动</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" value="" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化设计系统
        document.addEventListener('DOMContentLoaded', function() {
            // 生成颜色系统
            if (typeof window.generateColorSystem === 'function') {
                generateColorSystem();
            }
            
            // 初始化日志过滤器
            initLogFilters();
            
            // 初始化搜索功能
            initLogSearch();
            
            // 初始化动画
            initAnimations();
            
            // 模拟实时日志更新
            simulateLogUpdates();
        });

        function initLogFilters() {
            const filterButtons = document.querySelectorAll('.log-filter-btn');
            const logLines = document.querySelectorAll('.log-line');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有活动状态
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加当前活动状态
                    this.classList.add('active');
                    
                    const level = this.dataset.level;
                    
                    logLines.forEach(line => {
                        if (level === 'all' || line.dataset.level === level) {
                            line.style.display = 'flex';
                            // 添加显示动画
                            line.style.animation = 'fadeIn 0.3s ease-out';
                        } else {
                            line.style.display = 'none';
                        }
                    });
                    
                    // 更新统计数据
                    updateLogStats(level);
                });
            });
        }

        function initLogSearch() {
            const searchInput = document.getElementById('log-search');
            const logLines = document.querySelectorAll('.log-line');
            
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                logLines.forEach(line => {
                    const message = line.querySelector('.log-message');
                    const originalText = message.textContent;
                    
                    if (searchTerm === '') {
                        // 清除高亮
                        message.innerHTML = originalText;
                        line.style.display = 'flex';
                    } else {
                        // 搜索匹配
                        if (originalText.toLowerCase().includes(searchTerm)) {
                            // 高亮匹配文本
                            const highlightedText = originalText.replace(
                                new RegExp(searchTerm, 'gi'),
                                match => `<span class="log-search-highlight">${match}</span>`
                            );
                            message.innerHTML = highlightedText;
                            line.style.display = 'flex';
                        } else {
                            line.style.display = 'none';
                        }
                    }
                });
            });
        }

        function initAnimations() {
            // 为错误日志添加特殊动画
            const errorLines = document.querySelectorAll('.log-line.error');
            errorLines.forEach((line, index) => {
                setTimeout(() => {
                    line.style.animation = 'fadeIn 0.5s ease-out';
                }, index * 100);
            });
            
            // 统计卡片动画
            const statsCards = document.querySelectorAll('.log-stats-card');
            anime({
                targets: statsCards,
                translateY: [-20, 0],
                opacity: [0, 1],
                delay: anime.stagger(100),
                duration: 600,
                easing: 'easeOutQuad'
            });
        }

        function simulateLogUpdates() {
            const logContainer = document.querySelector('.log-container');
            const logLevels = ['info', 'warn', 'error', 'debug', 'success'];
            const logMessages = [
                'Processing user request',
                'Database query executed successfully',
                'Cache miss, fetching from database',
                'Memory usage: 85%',
                'Connection established',
                'Authentication successful',
                'File uploaded successfully',
                'Task completed',
                'Backup process started',
                'Configuration reloaded'
            ];
            
            setInterval(() => {
                const level = logLevels[Math.floor(Math.random() * logLevels.length)];
                const message = logMessages[Math.floor(Math.random() * logMessages.length)];
                const timestamp = new Date().toLocaleTimeString();
                
                const logLine = document.createElement('div');
                logLine.className = `log-line ${level}`;
                logLine.dataset.level = level;
                
                logLine.innerHTML = `
                    <div class="log-level-indicator ${level}"></div>
                    <span class="log-timestamp">${timestamp}</span>
                    <span class="log-level-tag ${level}">${level.toUpperCase()}</span>
                    <span class="log-message">${message}</span>
                `;
                
                logContainer.appendChild(logLine);
                
                // 添加动画
                logLine.style.animation = 'fadeIn 0.3s ease-out';
                
                // 滚动到底部
                logContainer.scrollTop = logContainer.scrollHeight;
                
                // 限制日志行数
                const logLines = logContainer.querySelectorAll('.log-line');
                if (logLines.length > 100) {
                    logLines[0].remove();
                }
            }, 3000);
        }

        function updateLogStats(level) {
            // 这里可以实现统计数据的实时更新
            console.log('Updating stats for level:', level);
        }
    </script>
</body>
</html>
