<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docker Containers - 高保真设计稿</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Prism.js 代码高亮 -->
    <link id="prism-theme" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Anime.js 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* 8px基线网格系统 */
        .spacing-8 { margin: 8px; }
        .spacing-16 { margin: 16px; }
        .spacing-24 { margin: 24px; }
        .spacing-32 { margin: 32px; }
        
        /* 图标标准尺寸 */
        .icon-24 { width: 24px; height: 24px; }
        .icon-32 { width: 32px; height: 32px; }
        .icon-48 { width: 48px; height: 48px; }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #1f2937; }
        ::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 4px; }
        ::-webkit-scrollbar-thumb:hover { background: #6b7280; }
        
        /* 状态指示器 */
        .status-dot {
            position: relative;
            display: inline-block;
        }
        
        .status-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-running::before { background-color: #22c55e; }
        .status-stopped::before { background-color: #ef4444; }
        .status-paused::before { background-color: #eab308; }
        .status-restarting::before { background-color: #3b82f6; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        /* 卡片悬浮效果 */
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        /* 资源使用进度条 */
        .resource-bar {
            background: linear-gradient(90deg, #22c55e, #eab308, #ef4444);
            background-size: 200% 100%;
            animation: resourceFlow 3s linear infinite;
        }
        
        @keyframes resourceFlow {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* Docker 特有样式 */
        .docker-blue { color: #2496ed; }
        .docker-bg { background: linear-gradient(135deg, #2496ed, #0db7ed); }
        
        /* 按钮增强样式 */
        .action-btn {
            min-width: 32px;
            min-height: 32px;
            padding: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: none;
            cursor: pointer;
            flex-shrink: 0;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }
        
        .action-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .action-btn i {
            font-size: 12px;
            line-height: 1;
        }
        
        /* 响应式按钮布局 */
        @media (max-width: 768px) {
            .action-btn {
                min-width: 28px;
                min-height: 28px;
                padding: 4px;
            }
            
            .action-btn i {
                font-size: 10px;
            }
        }
        
        /* 按钮组容器样式 */
        .lg\\:col-span-2 .flex {
            gap: 4px;
        }
        
        @media (max-width: 1024px) {
            .lg\\:col-span-2 .flex {
                justify-content: flex-start !important;
                margin-top: 8px;
            }
        }
        
        /* 容器卡片网格优化 */
        @media (max-width: 1024px) {
            .grid.lg\\:grid-cols-10 {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .grid.lg\\:grid-cols-10 > div {
                grid-column: span 1;
            }
        }
        
        /* 确保按钮组不会超出容器边界 */
        .container-card .flex.flex-wrap {
            max-width: 100%;
            overflow: hidden;
        }
        
        /* 状态指示器增强 */
        .status-indicator {
            position: relative;
            overflow: hidden;
        }
        
        .status-indicator::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: statusScan 3s infinite;
        }
        
        @keyframes statusScan {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        /* 容器状态颜色 */
        .container-running { border-left: 4px solid #22c55e; }
        .container-stopped { border-left: 4px solid #ef4444; }
        .container-paused { border-left: 4px solid #eab308; }
        .container-restarting { border-left: 4px solid #3b82f6; }
        
        /* 终端样式 */
        .terminal {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
        }
        
        .terminal-cursor::after {
            content: '█';
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        },
                        docker: {
                            500: '#2496ed',
                            600: '#0db7ed',
                        },
                        success: {
                            500: '#22c55e',
                            600: '#16a34a',
                        },
                        warning: {
                            500: '#eab308',
                            600: '#ca8a04',
                        },
                        error: {
                            500: '#ef4444',
                            600: '#dc2626',
                        }
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    
    <!-- 设计系统展示区域 -->
    <div class="container mx-auto px-8 py-16">
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Docker Containers</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">高保真UI设计稿 - 容器管理界面</p>
        </div>
        
        <!-- 颜色系统 -->
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">颜色系统</h2>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-8">
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl docker-bg mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">Docker蓝</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#2496ED</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-success-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">运行中</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#22C55E</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-error-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">已停止</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#EF4444</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-warning-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">暂停中</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#EAB308</p>
                </div>
                <div class="text-center">
                    <div class="w-24 h-24 rounded-2xl bg-primary-500 mx-auto mb-4 shadow-lg"></div>
                    <h3 class="font-medium text-gray-900 dark:text-white">重启中</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">#3B82F6</p>
                </div>
            </div>
        </div>
        
        <!-- 组件设计系统 -->
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">Docker 组件系统</h2>
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Docker 操作按钮 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">容器操作</h3>
                        <div class="space-y-3">
                            <button class="w-full px-4 py-2 bg-success-500 text-white rounded-lg font-medium hover:bg-success-600 transition-all text-sm flex items-center">
                                <i class="fas fa-play mr-2"></i>启动容器
                            </button>
                            <button class="w-full px-4 py-2 bg-error-500 text-white rounded-lg font-medium hover:bg-error-600 transition-all text-sm flex items-center">
                                <i class="fas fa-stop mr-2"></i>停止容器
                            </button>
                            <button class="w-full px-4 py-2 bg-primary-500 text-white rounded-lg font-medium hover:bg-primary-600 transition-all text-sm flex items-center">
                                <i class="fas fa-redo mr-2"></i>重启容器
                            </button>
                        </div>
                    </div>
                    
                    <!-- 状态标签 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">容器状态</h3>
                        <div class="space-y-3">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                <span class="status-dot status-running mr-2"></span>Running
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-error-100 text-error-800 dark:bg-error-800 dark:text-error-100">
                                <span class="status-dot status-stopped mr-2"></span>Stopped
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100">
                                <span class="status-dot status-paused mr-2"></span>Paused
                            </span>
                        </div>
                    </div>
                    
                    <!-- 终端预览 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">终端预览</h3>
                        <div class="terminal text-xs">
                            <div>$ docker ps -a</div>
                            <div>CONTAINER ID   IMAGE     STATUS</div>
                            <div class="terminal-cursor">abc123456789   nginx     Up 2 hours</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 高保真界面设计 -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden">
            <!-- 模拟浏览器标题栏 -->
            <div class="bg-gray-200 dark:bg-gray-700 px-6 py-4 flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div class="flex-1 mx-4">
                    <div class="bg-white dark:bg-gray-600 rounded-lg px-4 py-1 text-sm text-gray-600 dark:text-gray-300">
                        https://docker-manager.com/containers
                    </div>
                </div>
            </div>
            
            <!-- 界面内容 -->
            <div class="p-8">
                <!-- 页面头部 -->
                <div class="flex justify-between items-center mb-8">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2 flex items-center">
                            <i class="fab fa-docker docker-blue mr-3 text-4xl"></i>
                            Docker 容器
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400">管理本地Docker容器 • 共 12 个容器</p>
                    </div>
                    <div class="flex space-x-4">
                        <button class="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all flex items-center">
                            <i class="fas fa-terminal mr-2"></i>打开终端
                        </button>
                        <button class="px-6 py-3 docker-bg text-white rounded-xl font-medium hover:opacity-90 transition-all transform hover:scale-105 shadow-lg flex items-center">
                            <i class="fas fa-plus mr-2"></i>创建容器
                        </button>
                    </div>
                </div>
                
                <!-- 统计概览 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gradient-to-br from-success-50 to-success-100 dark:from-success-900 dark:to-success-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-success-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-play text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-success-600 dark:text-success-400 text-sm font-medium">运行中</p>
                                <p class="text-2xl font-bold text-success-800 dark:text-success-200">8</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-error-50 to-error-100 dark:from-error-900 dark:to-error-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-error-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-stop text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-error-600 dark:text-error-400 text-sm font-medium">已停止</p>
                                <p class="text-2xl font-bold text-error-800 dark:text-error-200">3</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-warning-50 to-warning-100 dark:from-warning-900 dark:to-warning-800 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-warning-500 rounded-xl flex items-center justify-center">
                                <i class="fas fa-pause text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-warning-600 dark:text-warning-400 text-sm font-medium">暂停中</p>
                                <p class="text-2xl font-bold text-warning-800 dark:text-warning-200">1</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 p-6 rounded-2xl">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gray-500 rounded-xl flex items-center justify-center">
                                <i class="fab fa-docker text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm font-medium">总数</p>
                                <p class="text-2xl font-bold text-gray-800 dark:text-gray-200">12</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="mb-8">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1 relative">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索容器名称、镜像、ID..." class="w-full pl-12 pr-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-docker-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                        </div>
                        <select class="px-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-docker-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            <option>所有状态</option>
                            <option>运行中</option>
                            <option>已停止</option>
                            <option>暂停中</option>
                        </select>
                        <select class="px-4 py-3 bg-gray-100 dark:bg-gray-700 border-2 border-transparent rounded-xl focus:border-docker-500 focus:bg-white dark:focus:bg-gray-800 transition-all outline-none">
                            <option>所有镜像</option>
                            <option>nginx</option>
                            <option>mysql</option>
                            <option>redis</option>
                        </select>
                    </div>
                </div>
                
                <!-- 容器列表 -->
                <div class="space-y-4">
                    <!-- 容器 1 - 运行中 -->
                    <div class="container-card bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover container-running">
                        <div class="grid grid-cols-1 lg:grid-cols-10 gap-4 items-center">
                            <div class="lg:col-span-3">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 docker-bg rounded-xl flex items-center justify-center">
                                        <i class="fab fa-docker text-white text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 dark:text-white">nginx-web</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">ID: abc123456789</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">镜像</p>
                                    <p class="font-medium text-gray-900 dark:text-white">nginx:1.21</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                    <span class="status-dot status-running mr-2"></span>Running
                                </span>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">端口映射</p>
                                    <p class="font-medium text-gray-900 dark:text-white">80:8080</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">运行时间</p>
                                    <p class="font-medium text-gray-900 dark:text-white">2小时</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="flex flex-wrap gap-2 justify-end">
                                    <button class="action-btn bg-error-500 text-white hover:bg-error-600" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="action-btn bg-primary-500 text-white hover:bg-primary-600" title="重启">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="action-btn bg-gray-500 text-white hover:bg-gray-600" title="日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 资源使用情况 -->
                        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    <span>CPU</span>
                                    <span>45%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-success-500 h-2 rounded-full resource-bar" style="width: 45%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    <span>内存</span>
                                    <span>256MB / 1GB</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-primary-500 h-2 rounded-full" style="width: 25%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    <span>网络I/O</span>
                                    <span>2.3MB/s</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-warning-500 h-2 rounded-full resource-bar" style="width: 60%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 容器 2 - 运行中 -->
                    <div class="container-card bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover container-running">
                        <div class="grid grid-cols-1 lg:grid-cols-10 gap-4 items-center">
                            <div class="lg:col-span-3">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-database text-white text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 dark:text-white">mysql-db</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">ID: def456789012</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">镜像</p>
                                    <p class="font-medium text-gray-900 dark:text-white">mysql:8.0</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-success-100 text-success-800 dark:bg-success-800 dark:text-success-100">
                                    <span class="status-dot status-running mr-2"></span>Running
                                </span>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">端口映射</p>
                                    <p class="font-medium text-gray-900 dark:text-white">3306:3306</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">运行时间</p>
                                    <p class="font-medium text-gray-900 dark:text-white">5天</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="flex flex-wrap gap-2 justify-end">
                                    <button class="action-btn bg-error-500 text-white hover:bg-error-600" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="action-btn bg-primary-500 text-white hover:bg-primary-600" title="重启">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="action-btn bg-gray-500 text-white hover:bg-gray-600" title="日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    <span>CPU</span>
                                    <span>12%</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-success-500 h-2 rounded-full" style="width: 12%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    <span>内存</span>
                                    <span>512MB / 2GB</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-primary-500 h-2 rounded-full" style="width: 25%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    <span>磁盘I/O</span>
                                    <span>5.8MB/s</span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-warning-500 h-2 rounded-full" style="width: 35%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 容器 3 - 已停止 -->
                    <div class="bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover container-stopped opacity-75">
                        <div class="grid grid-cols-1 lg:grid-cols-10 gap-4 items-center">
                            <div class="lg:col-span-3">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-memory text-white text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 dark:text-white">redis-cache</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">ID: ghi789012345</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">镜像</p>
                                    <p class="font-medium text-gray-900 dark:text-white">redis:7.0</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-error-100 text-error-800 dark:bg-error-800 dark:text-error-100">
                                    <span class="status-dot status-stopped mr-2"></span>Stopped
                                </span>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">端口映射</p>
                                    <p class="font-medium text-gray-500 dark:text-gray-500">6379:6379</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">停止时间</p>
                                    <p class="font-medium text-gray-900 dark:text-white">1小时前</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="flex flex-wrap gap-2 justify-end">
                                    <button class="action-btn bg-success-500 text-white hover:bg-success-600" title="启动">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="action-btn bg-error-500 text-white hover:bg-error-600" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <button class="action-btn bg-gray-500 text-white hover:bg-gray-600" title="日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 容器 4 - 暂停中 -->
                    <div class="bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 card-hover container-paused">
                        <div class="grid grid-cols-1 lg:grid-cols-10 gap-4 items-center">
                            <div class="lg:col-span-3">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-code text-white text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 dark:text-white">app-backend</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">ID: jkl012345678</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">镜像</p>
                                    <p class="font-medium text-gray-900 dark:text-white">node:16-alpine</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-warning-100 text-warning-800 dark:bg-warning-800 dark:text-warning-100">
                                    <span class="status-dot status-paused mr-2"></span>Paused
                                </span>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">端口映射</p>
                                    <p class="font-medium text-gray-900 dark:text-white">3000:3000</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-1">
                                <div class="text-sm">
                                    <p class="text-gray-600 dark:text-gray-400">暂停时间</p>
                                    <p class="font-medium text-gray-900 dark:text-white">30分钟前</p>
                                </div>
                            </div>
                            
                            <div class="lg:col-span-2">
                                <div class="flex flex-wrap gap-2 justify-end">
                                    <button class="action-btn bg-success-500 text-white hover:bg-success-600" title="恢复">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="action-btn bg-error-500 text-white hover:bg-error-600" title="停止">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="action-btn bg-gray-500 text-white hover:bg-gray-600" title="日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 分页和批量操作 -->
                <div class="flex flex-col md:flex-row justify-between items-center mt-8 space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <button class="px-4 py-2 bg-error-500 text-white rounded-lg hover:bg-error-600 transition-all text-sm flex items-center shadow-md hover:shadow-lg">
                            <i class="fas fa-stop mr-2"></i>批量停止
                        </button>
                        <button class="px-4 py-2 bg-success-500 text-white rounded-lg hover:bg-success-600 transition-all text-sm flex items-center shadow-md hover:shadow-lg">
                            <i class="fas fa-play mr-2"></i>批量启动
                        </button>
                        <button class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all text-sm flex items-center shadow-md hover:shadow-lg">
                            <i class="fas fa-broom mr-2"></i>清理停止容器
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            显示 1-4 项，共 12 个容器
                        </p>
                        <div class="flex space-x-2">
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-2 bg-docker-500 text-white rounded-lg">1</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">2</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">3</button>
                            <button class="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 容器卡片入场动画
            anime({
                targets: '.card-hover',
                opacity: [0, 1],
                translateX: [-50, 0],
                delay: anime.stagger(150),
                duration: 800,
                easing: 'easeOutExpo'
            });
            
            // 统计卡片动画
            anime({
                targets: '.bg-gradient-to-br',
                scale: [0.9, 1],
                opacity: [0, 1],
                delay: anime.stagger(100),
                duration: 600,
                easing: 'easeOutBack'
            });
            
            // 标题动画
            anime({
                targets: 'h1',
                opacity: [0, 1],
                translateY: [-30, 0],
                duration: 1000,
                easing: 'easeOutExpo'
            });
            
            // 进度条动画
            const progressBars = document.querySelectorAll('.resource-bar');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                anime({
                    targets: bar,
                    width: width,
                    duration: 1500,
                    easing: 'easeOutQuart',
                    delay: 500
                });
            });
            
            // 状态点闪烁动画
            anime({
                targets: '.status-dot::before',
                scale: [1, 1.2, 1],
                duration: 2000,
                loop: true,
                easing: 'easeInOutSine'
            });
        });
        
        // 容器操作模拟
        document.querySelectorAll('.action-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('title');
                
                // 按钮点击动画
                anime({
                    targets: this,
                    scale: [1, 0.9, 1],
                    duration: 200,
                    easing: 'easeInOutQuad'
                });
                
                // 操作反馈
                const originalContent = this.innerHTML;
                const icon = this.querySelector('i');
                
                if (action === '停止') {
                    icon.className = 'fas fa-spinner fa-spin';
                    setTimeout(() => {
                        this.innerHTML = originalContent;
                    }, 1500);
                } else if (action === '启动' || action === '恢复') {
                    icon.className = 'fas fa-spinner fa-spin';
                    setTimeout(() => {
                        this.innerHTML = originalContent;
                    }, 2000);
                } else if (action === '重启') {
                    icon.className = 'fas fa-spinner fa-spin';
                    setTimeout(() => {
                        this.innerHTML = originalContent;
                    }, 3000);
                }
                
                console.log(`执行操作: ${action}`);
            });
        });
        
        // 批量操作按钮
        document.querySelectorAll('button[class*="bg-error-500"], button[class*="bg-success-500"], button[class*="bg-gray-500"]').forEach(button => {
            if (!button.classList.contains('action-btn')) {
                button.addEventListener('click', function(e) {
                    if (this.textContent.includes('批量') || this.textContent.includes('清理')) {
                        e.preventDefault();
                        
                        // 按钮反馈动画
                        anime({
                            targets: this,
                            scale: [1, 0.95, 1],
                            duration: 200,
                            easing: 'easeInOutQuad'
                        });
                        
                        console.log(`执行批量操作: ${this.textContent.trim()}`);
                    }
                });
            }
        });
    </script>

    <!-- Prism.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
