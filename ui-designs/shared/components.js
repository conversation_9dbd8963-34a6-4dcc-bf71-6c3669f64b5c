// 设计系统 - 公共组件库
class DesignSystem {
    // 生成颜色系统展示
    static generateColorSystem() {
        return `
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">颜色系统</h2>
            <div class="grid grid-cols-2 md:grid-cols-6 gap-6">
                <div class="text-center">
                    <div class="w-20 h-20 rounded-2xl bg-blue-500 mx-auto mb-3 shadow-lg"></div>
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white">主色调</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">#3B82F6</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 rounded-2xl bg-green-500 mx-auto mb-3 shadow-lg"></div>
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white">成功色</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">#22C55E</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 rounded-2xl bg-yellow-500 mx-auto mb-3 shadow-lg"></div>
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white">警告色</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">#EAB308</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 rounded-2xl bg-red-500 mx-auto mb-3 shadow-lg"></div>
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white">错误色</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">#EF4444</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 rounded-2xl bg-cyan-500 mx-auto mb-3 shadow-lg"></div>
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white">信息色</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">#06B6D4</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 rounded-2xl bg-gradient-to-br from-purple-500 to-pink-500 mx-auto mb-3 shadow-lg"></div>
                    <h3 class="font-medium text-sm text-gray-900 dark:text-white">渐变色</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Gradient</p>
                </div>
            </div>
        </div>
        `;
    }

    // 生成组件系统展示
    static generateComponentSystem() {
        return `
        <div class="mb-16">
            <h2 class="text-2xl font-semibold mb-8 text-gray-900 dark:text-white">组件系统</h2>
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- 按钮组件 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">按钮组件</h3>
                        <div class="space-y-3">
                            <button class="btn btn-primary w-full">
                                <i class="fas fa-rocket mr-2"></i>主要按钮
                            </button>
                            <button class="btn btn-success w-full">
                                <i class="fas fa-check mr-2"></i>成功按钮
                            </button>
                            <button class="btn btn-warning w-full">
                                <i class="fas fa-exclamation mr-2"></i>警告按钮
                            </button>
                            <button class="btn btn-ghost w-full">
                                <i class="fas fa-info mr-2"></i>次要按钮
                            </button>
                        </div>
                    </div>
                    
                    <!-- 徽章组件 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">状态徽章</h3>
                        <div class="space-y-3">
                            <div class="flex flex-wrap gap-2">
                                <span class="badge badge-success">Running</span>
                                <span class="badge badge-warning">Pending</span>
                                <span class="badge badge-error">Failed</span>
                                <span class="badge badge-info">Ready</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标签组件 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">版本标签</h3>
                        <div class="space-y-3">
                            <div class="flex flex-wrap gap-2">
                                <span class="tag tag-version">v1.21.0</span>
                                <span class="tag tag-latest">latest</span>
                                <span class="tag tag-version">stable</span>
                                <span class="tag tag-latest">alpine</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 进度条组件 -->
                    <div>
                        <h3 class="font-medium mb-4 text-gray-900 dark:text-white">进度条</h3>
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span>CPU</span>
                                    <span>75%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" style="width: 75%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span>Memory</span>
                                    <span>45%</span>
                                </div>
                                <div class="progress progress-success">
                                    <div class="progress-bar" style="width: 45%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;
    }

    // 生成浏览器标题栏
    static generateBrowserHeader(url) {
        return `
        <div class="bg-gray-200 dark:bg-gray-700 px-6 py-4 flex items-center space-x-2">
            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <div class="flex-1 mx-4">
                <div class="bg-white dark:bg-gray-600 rounded-lg px-4 py-2 text-sm text-gray-600 dark:text-gray-300">
                    ${url}
                </div>
            </div>
        </div>
        `;
    }

    // 生成面包屑导航
    static generateBreadcrumb(items) {
        const breadcrumbItems = items.map((item, index) => {
            const isLast = index === items.length - 1;
            return `
                <li class="flex items-center">
                    ${index > 0 ? '<i class="fas fa-chevron-right text-gray-400 mx-2"></i>' : ''}
                    <a href="${item.href}" class="${isLast ? 'text-gray-900 dark:text-white font-medium' : 'text-blue-600 dark:text-blue-400 hover:text-blue-800'} text-sm">
                        ${item.icon ? `<i class="${item.icon} mr-2"></i>` : ''}
                        ${item.label}
                    </a>
                </li>
            `;
        }).join('');

        return `
        <nav class="mb-6">
            <ol class="flex items-center space-x-1">
                ${breadcrumbItems}
            </ol>
        </nav>
        `;
    }

    // 生成统计卡片
    static generateStatCard(title, value, icon, color = 'blue', change = null) {
        const colors = {
            blue: 'from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 bg-blue-500 text-blue-600 dark:text-blue-400 text-blue-800 dark:text-blue-200',
            green: 'from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 bg-green-500 text-green-600 dark:text-green-400 text-green-800 dark:text-green-200',
            yellow: 'from-yellow-50 to-yellow-100 dark:from-yellow-900 dark:to-yellow-800 bg-yellow-500 text-yellow-600 dark:text-yellow-400 text-yellow-800 dark:text-yellow-200',
            red: 'from-red-50 to-red-100 dark:from-red-900 dark:to-red-800 bg-red-500 text-red-600 dark:text-red-400 text-red-800 dark:text-red-200',
            purple: 'from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 bg-purple-500 text-purple-600 dark:text-purple-400 text-purple-800 dark:text-purple-200'
        };
        
        const [bgGradient, iconBg, textColor, valueColor] = colors[color].split(' ');
        
        return `
        <div class="bg-gradient-to-br ${bgGradient} p-6 rounded-2xl hover-lift">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 ${iconBg} rounded-xl flex items-center justify-center">
                        <i class="${icon} text-white text-xl"></i>
                    </div>
                    <div>
                        <p class="${textColor} text-sm font-medium">${title}</p>
                        <p class="text-2xl font-bold ${valueColor}">${value}</p>
                        ${change ? `<p class="text-xs ${change.type === 'increase' ? 'text-green-600' : 'text-red-600'}">${change.value}</p>` : ''}
                    </div>
                </div>
            </div>
        </div>
        `;
    }

    // 生成表格
    static generateTable(headers, rows, options = {}) {
        const headerRow = headers.map(header => `
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                ${header}
            </th>
        `).join('');

        const bodyRows = rows.map(row => `
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                ${row.map(cell => `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">${cell}</td>`).join('')}
            </tr>
        `).join('');

        return `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>${headerRow}</tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    ${bodyRows}
                </tbody>
            </table>
        </div>
        `;
    }

    // 生成代码块
    static generateCodeBlock(code, language = 'yaml', showLineNumbers = true) {
        const lines = code.split('\n');
        const codeLines = lines.map((line, index) => `
            <div class="flex">
                ${showLineNumbers ? `<span class="line-number text-gray-500 mr-4 select-none w-8 text-right">${index + 1}</span>` : ''}
                <span class="line flex-1">${this.escapeHtml(line)}</span>
            </div>
        `).join('');

        return `
        <div class="code-block">
            <div class="flex items-center justify-between mb-4 pb-3 border-b border-gray-700">
                <span class="text-sm font-medium text-gray-300">${language.toUpperCase()}</span>
                <button class="btn btn-sm btn-ghost text-gray-400 hover:text-white" onclick="copyToClipboard(this.dataset.code)" data-code="${this.escapeHtml(code)}">
                    <i class="fas fa-copy mr-1"></i>复制
                </button>
            </div>
            <div class="font-mono text-sm leading-6">
                ${codeLines}
            </div>
        </div>
        `;
    }

    // HTML转义
    static escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 生成日志级别标签
    static generateLogLevelBadge(level, text = null) {
        const displayText = text || level.toUpperCase();
        const icons = {
            error: 'fas fa-times-circle',
            warn: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle',
            debug: 'fas fa-bug',
            success: 'fas fa-check-circle'
        };
        
        return `<span class="log-level-tag ${level}">
            <i class="${icons[level] || 'fas fa-circle'}"></i> ${displayText}
        </span>`;
    }

    // 生成日志行
    static generateLogLine(timestamp, level, message, data = {}) {
        const levelIndicator = `<div class="log-level-indicator ${level}"></div>`;
        const timestampElement = `<span class="log-timestamp">${timestamp}</span>`;
        const levelTag = this.generateLogLevelBadge(level);
        const messageElement = `<span class="log-message">${message}</span>`;
        
        return `<div class="log-line ${level}" data-level="${level}" ${data.searchable ? 'data-searchable="true"' : ''}>
            ${levelIndicator}
            ${timestampElement}
            ${levelTag}
            ${messageElement}
        </div>`;
    }

    // 生成日志统计卡片
    static generateLogStatsCard(level, count, icon = null) {
        const icons = {
            info: 'fas fa-info-circle',
            warn: 'fas fa-exclamation-triangle', 
            error: 'fas fa-times-circle',
            debug: 'fas fa-bug',
            success: 'fas fa-check-circle'
        };
        
        const colors = {
            info: 'blue',
            warn: 'yellow',
            error: 'red', 
            debug: 'gray',
            success: 'green'
        };
        
        const selectedIcon = icon || icons[level] || 'fas fa-circle';
        const color = colors[level] || 'gray';
        const animation = level === 'error' ? 'animate-pulse' : '';
        
        return `<div class="log-stats-card bg-white rounded-lg shadow-md p-6 border-l-4 border-${color}-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="${selectedIcon} text-2xl text-${color}-500 ${animation}"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">${level.toUpperCase()}</p>
                    <p class="text-2xl font-bold ${level === 'error' ? 'text-red-600' : 'text-gray-900'}">${count.toLocaleString()}</p>
                </div>
            </div>
        </div>`;
    }

    // 生成日志过滤器按钮
    static generateLogFilterButton(level, label, active = false) {
        const colors = {
            all: 'btn-secondary',
            error: 'btn-danger',
            warn: 'btn-warning', 
            info: 'btn-primary',
            debug: 'btn-secondary',
            success: 'btn-success'
        };
        
        const icons = {
            all: 'fas fa-list',
            error: 'fas fa-times-circle',
            warn: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle', 
            debug: 'fas fa-bug',
            success: 'fas fa-check-circle'
        };
        
        const buttonClass = `log-filter-btn btn btn-sm ${colors[level] || 'btn-secondary'} ${active ? 'active' : ''}`;
        const icon = icons[level] || 'fas fa-circle';
        
        return `<button class="${buttonClass}" data-level="${level}">
            <i class="${icon}"></i> ${label}
        </button>`;
    }

    // 生成日志搜索框
    static generateLogSearchBox(placeholder = '搜索日志内容...') {
        return `<div class="flex-1 relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text" 
                   id="log-search"
                   class="form-input block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="${placeholder}">
        </div>`;
    }

    // 生成完整的日志查看器容器
    static generateLogViewer(logs = [], options = {}) {
        const {
            height = 'calc(100vh - 300px)',
            minHeight = '400px',
            showTimestamp = true,
            showLevelIndicator = true,
            enableSearch = true
        } = options;
        
        const containerStyle = `style="height: ${height}; min-height: ${minHeight}"`;
        let logContent = '';
        
        if (logs.length > 0) {
            logContent = logs.map(log => 
                this.generateLogLine(log.timestamp, log.level, log.message, log.data || {})
            ).join('');
        } else {
            logContent = `<div class="flex items-center justify-center h-full text-gray-500">
                <div class="text-center">
                    <i class="fas fa-file-alt text-4xl mb-4"></i>
                    <p>暂无日志数据</p>
                </div>
            </div>`;
        }
        
        return `<div class="log-container" ${containerStyle}>
            ${logContent}
        </div>`;
    }
}

// 复制到剪贴板功能
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // 可以添加提示
        console.log('已复制到剪贴板');
    });
}

// 主题切换功能
function toggleTheme() {
    const html = document.documentElement;
    const isDark = html.classList.contains('dark');
    
    if (isDark) {
        html.classList.remove('dark');
        localStorage.setItem('theme', 'light');
    } else {
        html.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    }
}

// 初始化主题
function initTheme() {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
        document.documentElement.classList.add('dark');
    }
}

// 页面加载时初始化主题
document.addEventListener('DOMContentLoaded', initTheme);

// 全局函数，便于在HTML中直接调用
window.generateColorSystem = function() {
    const colorSystemElement = document.getElementById('color-system');
    if (colorSystemElement) {
        colorSystemElement.innerHTML = `
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-blue-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Primary Blue</h3>
                <p class="text-xs text-gray-500">#3B82F6</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-green-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Success Green</h3>
                <p class="text-xs text-gray-500">#22C55E</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-yellow-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Warning Yellow</h3>
                <p class="text-xs text-gray-500">#EAB308</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-red-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Error Red</h3>
                <p class="text-xs text-gray-500">#EF4444</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-cyan-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Info Cyan</h3>
                <p class="text-xs text-gray-500">#06B6D4</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-gray-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Debug Gray</h3>
                <p class="text-xs text-gray-500">#6B7280</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Gradient</h3>
                <p class="text-xs text-gray-500">Purple-Pink</p>
            </div>
            <div class="text-center">
                <div class="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-600 to-cyan-500 mx-auto mb-2 shadow-md"></div>
                <h3 class="font-medium text-xs text-gray-900">Log Gradient</h3>
                <p class="text-xs text-gray-500">Blue-Cyan</p>
            </div>
        `;
    }
};

window.generateComponentSystem = DesignSystem.generateComponentSystem;
window.generateStatCard = DesignSystem.generateStatCard;
window.generateBreadcrumb = DesignSystem.generateBreadcrumb;
window.generateCodeBlock = DesignSystem.generateCodeBlock;
window.generateLogLine = DesignSystem.generateLogLine;
window.generateLogLevelBadge = DesignSystem.generateLogLevelBadge;
window.generateLogStatsCard = DesignSystem.generateLogStatsCard;
window.generateLogFilterButton = DesignSystem.generateLogFilterButton;
window.generateLogSearchBox = DesignSystem.generateLogSearchBox;
window.generateLogViewer = DesignSystem.generateLogViewer;
