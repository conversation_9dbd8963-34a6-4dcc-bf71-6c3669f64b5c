/* =================
   设计系统 - 统一美观暗色主题
   ================= */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

/* 基础设计系统 */
:root {
    /* 8px基线网格系统 */
    --spacing-1: 8px;
    --spacing-2: 16px;
    --spacing-3: 24px;
    --spacing-4: 32px;
    --spacing-5: 40px;
    --spacing-6: 48px;
    
    /* 图标尺寸 */
    --icon-sm: 16px;
    --icon-md: 24px;
    --icon-lg: 32px;
    --icon-xl: 48px;
    
    /* 边框圆角 */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;
    
    /* 暗色主题专属阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
    
    /* 美观的颜色系统 - 暗色主题专用 */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;
    
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;
    
    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    --color-warning-400: #fbbf24;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #b45309;
    --color-warning-800: #92400e;
    --color-warning-900: #78350f;
    
    --color-error-50: #fef2f2;
    --color-error-100: #fee2e2;
    --color-error-400: #f87171;
    --color-error-500: #ef4444;
    --color-error-600: #dc2626;
    --color-error-700: #b91c1c;
    --color-error-800: #991b1b;
    --color-error-900: #7f1d1d;
    
    --color-info-50: #f0f9ff;
    --color-info-100: #e0f2fe;
    --color-info-400: #38bdf8;
    --color-info-500: #06b6d4;
    --color-info-600: #0891b2;
    --color-info-700: #0e7490;
    --color-info-800: #155e75;
    --color-info-900: #164e63;
    
    /* 暗色主题背景色系统 */
    --color-bg-primary: #0f172a;    /* 主背景 - 深蓝灰 */
    --color-bg-secondary: #1e293b;  /* 次要背景 - 中蓝灰 */
    --color-bg-tertiary: #334155;   /* 三级背景 - 浅蓝灰 */
    --color-bg-surface: #475569;    /* 表面背景 - 更浅蓝灰 */
    
    /* 边框色系统 */
    --color-border-primary: #334155;
    --color-border-secondary: #475569;
    --color-border-focus: #3b82f6;
    
    /* 文本色系统 */
    --color-text-primary: #f8fafc;    /* 主文本 - 几乎白色 */
    --color-text-secondary: #cbd5e1;  /* 次要文本 - 浅灰 */
    --color-text-tertiary: #94a3b8;   /* 三级文本 - 中灰 */
    --color-text-muted: #64748b;      /* 弱化文本 - 深灰 */

    /* 日志级别颜色系统 - 暗色主题优化 */
    --log-info-bg: rgba(59, 130, 246, 0.1);
    --log-info-border: #3b82f6;
    --log-info-text: #60a5fa;
    
    --log-success-bg: rgba(34, 197, 94, 0.1);
    --log-success-border: #22c55e;
    --log-success-text: #4ade80;
    
    --log-warning-bg: rgba(245, 158, 11, 0.1);
    --log-warning-border: #f59e0b;
    --log-warning-text: #fbbf24;
    
    --log-error-bg: rgba(239, 68, 68, 0.15);
    --log-error-border: #ef4444;
    --log-error-text: #f87171;
    
    --log-debug-bg: rgba(148, 163, 184, 0.1);
    --log-debug-border: #94a3b8;
    --log-debug-text: #cbd5e1;

    /* 字体系统 */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'SF Mono', Consolas, monospace;
    
    /* 动画缓动 */
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans);
    line-height: 1.6;
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* 美观的滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--color-bg-surface);
    border-radius: 4px;
    transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* 玻璃态效果 */
.glass-effect {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
}

/* 卡片样式 */
.card {
    background: var(--color-bg-secondary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all 0.3s var(--ease-out);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-border-secondary);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
    cursor: pointer;
    transition: all 0.2s var(--ease-out);
    border: none;
    text-decoration: none;
    gap: var(--spacing-1);
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--color-primary-700), var(--color-primary-800));
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--color-bg-tertiary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border-primary);
}

.btn-secondary:hover {
    background: var(--color-bg-surface);
    border-color: var(--color-border-secondary);
    transform: translateY(-1px);
}

/* 状态标签 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: 4px var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-running {
    background: var(--color-success-900);
    color: var(--color-success-400);
    border: 1px solid var(--color-success-800);
}

.status-pending {
    background: var(--color-warning-900);
    color: var(--color-warning-400);
    border: 1px solid var(--color-warning-800);
}

.status-error {
    background: var(--color-error-900);
    color: var(--color-error-400);
    border: 1px solid var(--color-error-800);
}

.status-stopped {
    background: var(--color-bg-tertiary);
    color: var(--color-text-tertiary);
    border: 1px solid var(--color-border-primary);
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--color-bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table th {
    background: var(--color-bg-tertiary);
    color: var(--color-text-primary);
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: var(--spacing-2) var(--spacing-3);
    text-align: left;
    border-bottom: 1px solid var(--color-border-primary);
}

.table td {
    padding: var(--spacing-2) var(--spacing-3);
    border-bottom: 1px solid var(--color-border-primary);
    color: var(--color-text-secondary);
}

.table tr:hover {
    background: var(--color-bg-tertiary);
}

/* 进度条 */
.progress-bar {
    height: 8px;
    background: var(--color-bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary-600), var(--color-primary-500));
    border-radius: var(--radius-sm);
    transition: width 0.3s var(--ease-out);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* 通用动画 */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

.animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.dark .card {
    background: #1e293b;
    border-color: #334155;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* 状态指示器 */
.status-dot {
    position: relative;
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-running {
    background-color: var(--color-success-500);
}

.status-pending {
    background-color: var(--color-warning-500);
}

.status-failed {
    background-color: var(--color-error-500);
}

.status-stopped {
    background-color: #6b7280;
}

/* 徽章组件 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
    color: white;
}

.badge-success {
    background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
    color: white;
}

.badge-warning {
    background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
    color: white;
}

.badge-error {
    background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
    color: white;
}

.badge-info {
    background: linear-gradient(135deg, var(--color-info-500), var(--color-info-600));
    color: white;
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    text-decoration: none;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1rem;
}

.btn-primary {
    background: var(--color-primary-500);
    color: white;
}

.btn-primary:hover {
    background: var(--color-primary-600);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--color-success-500);
    color: white;
}

.btn-success:hover {
    background: var(--color-success-600);
    transform: translateY(-1px);
}

.btn-warning {
    background: var(--color-warning-500);
    color: white;
}

.btn-warning:hover {
    background: var(--color-warning-600);
    transform: translateY(-1px);
}

.btn-error {
    background: var(--color-error-500);
    color: white;
}

.btn-error:hover {
    background: var(--color-error-600);
    transform: translateY(-1px);
}

.btn-ghost {
    background: transparent;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.dark .btn-ghost {
    color: #94a3b8;
    border-color: #334155;
}

.btn-ghost:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.dark .btn-ghost:hover {
    background: #334155;
    border-color: #475569;
}

/* 表格组件 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.dark .table {
    background: #1e293b;
}

.table th {
    background: #f8fafc;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    color: #475569;
    border-bottom: 1px solid #e2e8f0;
}

.dark .table th {
    background: #0f172a;
    color: #94a3b8;
    border-color: #334155;
}

.table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    color: #334155;
}

.dark .table td {
    border-color: #334155;
    color: #cbd5e1;
}

.table tr:hover {
    background: #f8fafc;
}

.dark .table tr:hover {
    background: #334155;
}

/* 标签组件 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    font-family: 'JetBrains Mono', monospace;
}

.tag-version {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.tag-latest {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* 进度条组件 */
.progress {
    width: 100%;
    height: 8px;
    background: #f1f5f9;
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.dark .progress {
    background: #334155;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary-500), var(--color-primary-600));
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
}

.progress-success .progress-bar {
    background: linear-gradient(90deg, var(--color-success-500), var(--color-success-600));
}

.progress-warning .progress-bar {
    background: linear-gradient(90deg, var(--color-warning-500), var(--color-warning-600));
}

.progress-error .progress-bar {
    background: linear-gradient(90deg, var(--color-error-500), var(--color-error-600));
}

/* 输入框样式 */
.input {
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--color-bg-secondary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-md);
    color: var(--color-text-primary);
    font-size: 14px;
    transition: all 0.2s var(--ease-out);
}

.input:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input::placeholder {
    color: var(--color-text-muted);
}

/* 代码块样式 */
.code-block {
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-3);
    font-family: var(--font-family-mono);
    font-size: 13px;
    line-height: 1.6;
    overflow-x: auto;
    color: var(--color-text-secondary);
}

.code-block pre {
    margin: 0;
    padding: 0;
}

/* 日志行样式 */
.log-line {
    padding: var(--spacing-1) var(--spacing-2);
    margin: 2px 0;
    border-left: 3px solid transparent;
    border-radius: var(--radius-sm);
    font-family: var(--font-family-mono);
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.2s var(--ease-out);
}

.log-line:hover {
    background: var(--color-bg-tertiary);
}

.log-line.log-info {
    background: var(--log-info-bg);
    border-left-color: var(--log-info-border);
    color: var(--log-info-text);
}

.log-line.log-success {
    background: var(--log-success-bg);
    border-left-color: var(--log-success-border);
    color: var(--log-success-text);
}

.log-line.log-warning {
    background: var(--log-warning-bg);
    border-left-color: var(--log-warning-border);
    color: var(--log-warning-text);
}

.log-line.log-error {
    background: var(--log-error-bg);
    border-left-color: var(--log-error-border);
    color: var(--log-error-text);
    font-weight: 500;
}

.log-line.log-debug {
    background: var(--log-debug-bg);
    border-left-color: var(--log-debug-border);
    color: var(--log-debug-text);
}

/* 面包屑导航 */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    color: var(--color-text-tertiary);
    font-size: 14px;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--color-text-muted);
    margin-left: var(--spacing-1);
}

.breadcrumb-link {
    color: var(--color-text-tertiary);
    text-decoration: none;
    transition: color 0.2s var(--ease-out);
}

.breadcrumb-link:hover {
    color: var(--color-primary-400);
}

.breadcrumb-current {
    color: var(--color-text-primary);
    font-weight: 500;
}

/* 统计卡片 */
.stats-card {
    background: var(--color-bg-secondary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-3) var(--spacing-4);
    position: relative;
    overflow: hidden;
    transition: all 0.3s var(--ease-out);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary-600), var(--color-primary-500));
}

.stats-card.success::before {
    background: linear-gradient(90deg, var(--color-success-600), var(--color-success-500));
}

.stats-card.warning::before {
    background: linear-gradient(90deg, var(--color-warning-600), var(--color-warning-500));
}

.stats-card.error::before {
    background: linear-gradient(90deg, var(--color-error-600), var(--color-error-500));
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-text-primary);
    line-height: 1;
    margin-bottom: var(--spacing-1);
}

.stats-label {
    font-size: 14px;
    color: var(--color-text-tertiary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 标签组 */
.tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px var(--spacing-2);
    background: var(--color-bg-tertiary);
    color: var(--color-text-secondary);
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 500;
    border: 1px solid var(--color-border-primary);
}

.tag.tag-primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--color-primary-400);
    border-color: var(--color-primary-600);
}

.tag.tag-success {
    background: rgba(34, 197, 94, 0.1);
    color: var(--color-success-400);
    border-color: var(--color-success-600);
}

.tag.tag-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-warning-400);
    border-color: var(--color-warning-600);
}

.tag.tag-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-error-400);
    border-color: var(--color-error-600);
}

/* 加载状态 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-border-primary);
    border-top: 2px solid var(--color-primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 脉动效果 */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s var(--ease-out);
    z-index: 1000;
    border: 1px solid var(--color-border-primary);
    box-shadow: var(--shadow-lg);
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    bottom: calc(100% + 8px);
}

/* 响应式工具类 */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full {
        width: 100% !important;
    }
    
    .stats-card {
        padding: var(--spacing-2) var(--spacing-3);
    }
    
    .stats-value {
        font-size: 1.5rem;
    }
}

/* 动画入场效果 */
.fade-in {
    animation: fadeIn 0.5s var(--ease-out);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s var(--ease-out);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 焦点可访问性 */
.focus-visible:focus {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
    :root {
        --color-text-primary: #ffffff;
        --color-text-secondary: #e2e8f0;
        --color-border-primary: #64748b;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
