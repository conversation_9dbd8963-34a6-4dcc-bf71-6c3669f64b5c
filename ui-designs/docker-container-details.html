<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docker Container Details - 高保真设计稿</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Anime.js 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
    
    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="./shared/design-system.css">

    <style>
        /* Docker 特有样式 */
        .docker-blue { color: #2496ed; }
        .docker-bg { background: linear-gradient(135deg, #2496ed, #0db7ed); }
        
        /* 容器状态动画 */
        .status-running {
            animation: pulse-green 2s infinite;
        }
        
        @keyframes pulse-green {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        /* 网络图表 */
        .network-stats {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }
        
        .network-stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: networkFlow 3s infinite;
        }
        
        @keyframes networkFlow {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        /* 端口映射表格 */
        .port-table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .port-table th,
        .port-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
        }
        
        .dark .port-table th,
        .dark .port-table td {
            border-color: #374151;
        }
        
        .port-table th:first-child,
        .port-table td:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }
        
        .port-table th:last-child,
        .port-table td:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }
        
        /* 文件系统树 */
        .fs-tree {
            font-family: 'JetBrains Mono', monospace;
            font-size: 14px;
        }
        
        .fs-item {
            padding: 4px 0;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .fs-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
        
        .fs-folder {
            color: #3b82f6;
        }
        
        .fs-file {
            color: #6b7280;
        }
        
        /* 环境变量表格 */
        .env-table {
            background: #1e293b;
            border-radius: 8px;
            padding: 16px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .env-var {
            display: flex;
            margin-bottom: 8px;
            padding: 8px;
            background: #334155;
            border-radius: 4px;
        }
        
        .env-key {
            color: #8b5cf6;
            font-weight: 600;
            min-width: 200px;
        }
        
        .env-value {
            color: #22c55e;
            flex: 1;
            word-break: break-all;
        }
    </style>
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        docker: {
                            500: '#2496ed',
                            600: '#0db7ed',
                        }
                    }
                }
            }
        }
    </script>
</head>

<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-300">
    
    <div class="container mx-auto px-8 py-16">
        <!-- 设计系统展示 -->
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Docker Container Details</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">高保真UI设计稿 - 容器详情页面</p>
        </div>

        <div id="color-system"></div>
        <div id="component-system"></div>

        <!-- 高保真界面设计 -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl overflow-hidden">
            <div id="browser-header"></div>
            
            <!-- 页面内容 -->
            <div class="p-8">
                <!-- 面包屑导航 -->
                <div id="breadcrumb"></div>
                
                <!-- 页面头部 -->
                <div class="flex justify-between items-start mb-8">
                    <div class="flex items-start space-x-4">
                        <div class="w-16 h-16 docker-bg rounded-2xl flex items-center justify-center">
                            <i class="fab fa-docker text-white text-2xl"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">nginx-web</h1>
                            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                                <span><i class="fas fa-cube mr-1"></i>Container ID: abc123456789</span>
                                <span><i class="fas fa-image mr-1"></i>nginx:1.21.0</span>
                                <span><i class="fas fa-clock mr-1"></i>运行 2小时</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-3">
                                <span class="badge badge-success status-running">
                                    <i class="fas fa-play mr-1"></i>Running
                                </span>
                                <span class="badge badge-info">
                                    <i class="fas fa-heart mr-1"></i>健康
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button class="btn btn-ghost">
                            <i class="fas fa-terminal mr-2"></i>终端
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-redo mr-2"></i>重启
                        </button>
                        <button class="btn btn-warning">
                            <i class="fas fa-pause mr-2"></i>暂停
                        </button>
                        <button class="btn btn-error">
                            <i class="fas fa-stop mr-2"></i>停止
                        </button>
                    </div>
                </div>

                <!-- 快速统计 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div id="stat-cpu"></div>
                    <div id="stat-memory"></div>
                    <div id="stat-network"></div>
                    <div id="stat-disk"></div>
                </div>

                <!-- 主要内容 -->
                <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
                    <!-- 左侧内容 -->
                    <div class="xl:col-span-2 space-y-8">
                        <!-- 实时性能监控 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">实时性能监控</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- CPU 使用率 -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <h3 class="font-semibold">CPU 使用率</h3>
                                        <span class="text-2xl font-bold text-blue-600">45%</span>
                                    </div>
                                    <div class="progress progress-warning">
                                        <div class="progress-bar" style="width: 45%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mt-2">
                                        <span>0.45 cores</span>
                                        <span>限制: 1.0 cores</span>
                                    </div>
                                </div>
                                
                                <!-- 内存使用 -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <h3 class="font-semibold">内存使用</h3>
                                        <span class="text-2xl font-bold text-green-600">256MB</span>
                                    </div>
                                    <div class="progress progress-success">
                                        <div class="progress-bar" style="width: 25%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mt-2">
                                        <span>256MB / 1GB</span>
                                        <span>25% 已用</span>
                                    </div>
                                </div>
                                
                                <!-- 网络 I/O -->
                                <div class="network-stats text-white">
                                    <div class="flex justify-between items-center mb-3">
                                        <h3 class="font-semibold">网络 I/O</h3>
                                        <div class="text-right">
                                            <div class="text-sm">↓ 2.3MB/s</div>
                                            <div class="text-sm">↑ 856KB/s</div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <div>接收: 1.2GB</div>
                                            <div>包数: 45,678</div>
                                        </div>
                                        <div>
                                            <div>发送: 438MB</div>
                                            <div>包数: 23,456</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 磁盘 I/O -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <h3 class="font-semibold">磁盘 I/O</h3>
                                        <div class="text-right">
                                            <div class="text-sm">读: 45MB/s</div>
                                            <div class="text-sm">写: 12MB/s</div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <div>总读取: 2.1GB</div>
                                            <div>读操作: 1,234</div>
                                        </div>
                                        <div>
                                            <div>总写入: 567MB</div>
                                            <div>写操作: 567</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 端口映射 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">端口映射</h2>
                            
                            <div class="overflow-x-auto">
                                <table class="port-table w-full">
                                    <thead>
                                        <tr class="bg-gray-50 dark:bg-gray-700">
                                            <th class="text-left">容器端口</th>
                                            <th class="text-left">协议</th>
                                            <th class="text-left">主机端口</th>
                                            <th class="text-left">主机 IP</th>
                                            <th class="text-left">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="font-mono">80/tcp</td>
                                            <td><span class="badge badge-info">HTTP</span></td>
                                            <td class="font-mono">8080</td>
                                            <td class="font-mono">0.0.0.0</td>
                                            <td><span class="badge badge-success">活动</span></td>
                                        </tr>
                                        <tr>
                                            <td class="font-mono">443/tcp</td>
                                            <td><span class="badge badge-success">HTTPS</span></td>
                                            <td class="font-mono">8443</td>
                                            <td class="font-mono">0.0.0.0</td>
                                            <td><span class="badge badge-success">活动</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 文件系统 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">文件系统</h2>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- 挂载点 -->
                                <div>
                                    <h3 class="font-semibold mb-4">挂载点</h3>
                                    <div class="space-y-3">
                                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                            <div>
                                                <div class="font-mono text-sm">/etc/nginx</div>
                                                <div class="text-xs text-gray-600 dark:text-gray-400">配置目录</div>
                                            </div>
                                            <span class="badge badge-primary">bind</span>
                                        </div>
                                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                            <div>
                                                <div class="font-mono text-sm">/var/log/nginx</div>
                                                <div class="text-xs text-gray-600 dark:text-gray-400">日志目录</div>
                                            </div>
                                            <span class="badge badge-info">volume</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 磁盘使用 -->
                                <div>
                                    <h3 class="font-semibold mb-4">磁盘使用</h3>
                                    <div class="space-y-4">
                                        <div>
                                            <div class="flex justify-between text-sm mb-1">
                                                <span>/</span>
                                                <span>1.2GB / 5GB</span>
                                            </div>
                                            <div class="progress progress-warning">
                                                <div class="progress-bar" style="width: 24%"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="flex justify-between text-sm mb-1">
                                                <span>/var/log</span>
                                                <span>45MB / 500MB</span>
                                            </div>
                                            <div class="progress progress-success">
                                                <div class="progress-bar" style="width: 9%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 进程列表 -->
                        <div class="card p-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">运行进程</h2>
                            
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200 dark:border-gray-700">
                                            <th class="text-left py-2">PID</th>
                                            <th class="text-left py-2">进程</th>
                                            <th class="text-left py-2">CPU %</th>
                                            <th class="text-left py-2">内存 %</th>
                                            <th class="text-left py-2">命令</th>
                                        </tr>
                                    </thead>
                                    <tbody class="font-mono text-sm">
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2">1</td>
                                            <td class="py-2">nginx</td>
                                            <td class="py-2 text-green-600">0.1</td>
                                            <td class="py-2 text-blue-600">2.3</td>
                                            <td class="py-2 text-gray-600">nginx: master process</td>
                                        </tr>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2">7</td>
                                            <td class="py-2">nginx</td>
                                            <td class="py-2 text-green-600">0.0</td>
                                            <td class="py-2 text-blue-600">1.8</td>
                                            <td class="py-2 text-gray-600">nginx: worker process</td>
                                        </tr>
                                        <tr class="border-b border-gray-100 dark:border-gray-700">
                                            <td class="py-2">8</td>
                                            <td class="py-2">nginx</td>
                                            <td class="py-2 text-green-600">0.0</td>
                                            <td class="py-2 text-blue-600">1.8</td>
                                            <td class="py-2 text-gray-600">nginx: worker process</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧边栏 -->
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">基本信息</h3>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">容器 ID</span>
                                    <span class="font-mono">abc123456789</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">镜像</span>
                                    <span class="font-mono">nginx:1.21.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">创建时间</span>
                                    <span>2天前</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">启动时间</span>
                                    <span>2小时前</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">重启次数</span>
                                    <span>0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">平台</span>
                                    <span>linux/amd64</span>
                                </div>
                            </div>
                        </div>

                        <!-- 网络信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">网络信息</h3>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">网络模式</span>
                                    <span>bridge</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">IP 地址</span>
                                    <span class="font-mono">**********</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">MAC 地址</span>
                                    <span class="font-mono">02:42:ac:11:00:02</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">网络名称</span>
                                    <span>bridge</span>
                                </div>
                            </div>
                        </div>

                        <!-- 资源限制 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">资源限制</h3>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">CPU 限制</span>
                                    <span>1.0 cores</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">内存限制</span>
                                    <span>1GB</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">交换空间</span>
                                    <span>2GB</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">磁盘限制</span>
                                    <span>10GB</span>
                                </div>
                            </div>
                        </div>

                        <!-- 标签 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">标签</h3>
                            <div class="flex flex-wrap gap-2">
                                <span class="tag tag-version">app=nginx</span>
                                <span class="tag tag-latest">env=prod</span>
                                <span class="tag tag-version">tier=web</span>
                                <span class="tag tag-latest">v1.21.0</span>
                            </div>
                        </div>

                        <!-- 健康检查 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">健康检查</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm">HTTP 检查</span>
                                    <span class="badge badge-success">正常</span>
                                </div>
                                <div class="text-xs text-gray-600 dark:text-gray-400">
                                    <div>路径: /health</div>
                                    <div>间隔: 30s</div>
                                    <div>超时: 5s</div>
                                    <div>重试: 3次</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 环境变量 -->
                <div class="mt-8">
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">环境变量</h2>
                            <span class="badge badge-info">12 个变量</span>
                        </div>
                        
                        <div class="env-table">
                            <div class="env-var">
                                <span class="env-key">NGINX_VERSION</span>
                                <span class="env-value">1.21.0</span>
                            </div>
                            <div class="env-var">
                                <span class="env-key">NGINX_PORT</span>
                                <span class="env-value">80</span>
                            </div>
                            <div class="env-var">
                                <span class="env-key">PATH</span>
                                <span class="env-value">/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin</span>
                            </div>
                            <div class="env-var">
                                <span class="env-key">NGINX_CONF_DIR</span>
                                <span class="env-value">/etc/nginx</span>
                            </div>
                            <div class="env-var">
                                <span class="env-key">NGINX_LOG_DIR</span>
                                <span class="env-value">/var/log/nginx</span>
                            </div>
                            <div class="env-var">
                                <span class="env-key">NGINX_USER</span>
                                <span class="env-value">nginx</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共组件 -->
    <script src="./shared/components.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 生成设计系统
            document.getElementById('color-system').innerHTML = DesignSystem.generateColorSystem();
            document.getElementById('component-system').innerHTML = DesignSystem.generateComponentSystem();
            
            // 生成浏览器头部
            document.getElementById('browser-header').innerHTML = DesignSystem.generateBrowserHeader('https://docker-manager.com/containers/nginx-web');
            
            // 生成面包屑
            document.getElementById('breadcrumb').innerHTML = DesignSystem.generateBreadcrumb([
                { label: '首页', href: '#', icon: 'fas fa-home' },
                { label: 'Docker', href: '#', icon: 'fab fa-docker' },
                { label: '容器', href: '#' },
                { label: 'nginx-web', href: '#' }
            ]);
            
            // 生成统计卡片
            document.getElementById('stat-cpu').innerHTML = DesignSystem.generateStatCard('CPU 使用率', '45%', 'fas fa-microchip', 'yellow');
            document.getElementById('stat-memory').innerHTML = DesignSystem.generateStatCard('内存使用', '256MB', 'fas fa-memory', 'green');
            document.getElementById('stat-network').innerHTML = DesignSystem.generateStatCard('网络 I/O', '2.3MB/s', 'fas fa-network-wired', 'blue');
            document.getElementById('stat-disk').innerHTML = DesignSystem.generateStatCard('磁盘 I/O', '45MB/s', 'fas fa-hdd', 'purple');
        });
    </script>
</body>
</html>
