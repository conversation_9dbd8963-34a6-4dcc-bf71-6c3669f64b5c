# 系统架构与设计模式

## 整体架构
N-Admin 采用经典的三层架构模式，结合领域驱动设计（DDD）理念：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │     Business    │    │   Persistence   │
│      Layer      │    │      Layer      │    │      Layer      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│  Controllers    │───▶│    Services     │───▶│  Repositories   │
│  Middlewares    │    │  Domain Logic   │    │     Models      │
│    Routes       │    │  Validations    │    │   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心组件
- **Controller层**：处理HTTP请求，数据验证，响应格式化
- **Service层**：业务逻辑处理，事务管理，领域规则
- **Repository层**：数据访问抽象，数据持久化
- **Model层**：数据模型定义，ORM映射
- **Middleware层**：横切关注点（认证、日志、CORS等）
- **Job层**：异步任务处理，定时任务调度
- **Utils层**：通用工具函数，辅助功能

## 关键设计决策
- **依赖注入**：使用Wire进行编译时依赖注入，提高性能和可测试性
- **代码生成**：使用Gen工具自动生成数据模型和查询代码，减少重复劳动
- **配置分离**：环境配置文件分离（local.yml/prod.yml），支持多环境部署
- **微服务支持**：情感分析服务独立部署，支持多语言技术栈集成
- **缓存策略**：Redis缓存热点数据，提升系统响应性能

## 设计模式应用
- **Repository模式**：数据访问层抽象，便于测试和数据源切换
- **Service模式**：业务逻辑封装，保持Controller层简洁
- **Factory模式**：Wire依赖注入工厂，自动化对象创建
- **Middleware模式**：横切关注点处理，职责分离
- **Strategy模式**：权限策略配置，支持灵活的权限规则

## 组件交互流程
```mermaid
flowchart TD
    A[HTTP Request] --> B[Gin Router]
    B --> C[Middleware Chain]
    C --> D[Controller]
    D --> E[Service Layer]
    E --> F[Repository Layer]
    F --> G[Database/Cache]
    
    H[Background Jobs] --> I[Job Scheduler]
    I --> J[Service Layer]
    
    K[External APIs] --> L[HTTP Client]
    L --> M[Service Layer]
    
    N[Casbin] --> O[RBAC Engine]
    O --> P[Permission Check]
    P --> D
```

## 关键路径实现
- **用户认证流程**：JWT token生成 → 中间件验证 → 权限检查 → 业务处理
- **权限控制流程**：Casbin策略加载 → 权限规则匹配 → 访问决策 → 资源授权
- **数据操作流程**：请求验证 → 业务逻辑 → 数据持久化 → 缓存更新 → 响应返回
- **异步任务流程**：任务队列 → 调度器触发 → 业务处理 → 结果记录 → 状态更新

## 目录结构设计
```
├── api/          # API定义和协议文件
├── cmd/          # 应用程序入口点
├── internal/     # 内部业务逻辑
│   ├── controller/   # 控制器层
│   ├── service/      # 业务服务层  
│   ├── repository/   # 数据访问层
│   ├── model/        # 数据模型
│   ├── middleware/   # 中间件
│   └── job/          # 后台任务
├── pkg/          # 可复用包
├── config/       # 配置文件
└── deploy/       # 部署相关文件
```

## 数据流向
1. **请求处理**：Client → Router → Middleware → Controller → Service → Repository → DB
2. **权限验证**：Request → JWT验证 → Casbin权限检查 → 业务逻辑
3. **缓存机制**：查询请求 → Redis缓存检查 → 数据库查询 → 缓存更新
4. **异步处理**：触发事件 → 任务队列 → 后台处理 → 状态更新
