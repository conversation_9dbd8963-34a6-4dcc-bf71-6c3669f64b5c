# 技术栈与环境

## 核心技术
- **后端框架**：Gin v1.10.0 - 高性能Go Web框架
- **权限控制**：Casbin v2.104.0 - RBAC权限管理
- **数据库ORM**：GORM + Gorm-Adapter - 数据访问层
- **数据库**：SQLite（开发）/ MySQL/PostgreSQL（生产）
- **缓存**：Redis v9.7.3 - 会话管理和缓存
- **任务调度**：GoCron v1.28.2 + Robfig Cron - 定时任务
- **JWT认证**：golang-jwt/jwt v5.2.2 - 用户认证
- **依赖注入**：Wire v0.5.0 - 依赖管理
- **工具库**：Lancet v2.3.0 - 通用工具函数

## 开发环境
- 操作系统：macOS（当前开发环境）
- Go版本：1.24.3
- 开发工具：VS Code 
- 依赖管理：Go Modules
- 版本控制：Git
- 容器化：Docker + Docker Compose

## 构建与部署
- 构建工具：Makefile - 自动化构建脚本
- 容器化：Docker - 容器化部署
- 编排工具：Docker Compose - 本地开发环境
- 部署方式：支持 Docker/Kubernetes 部署
- 配置管理：YAML配置文件（local.yml, prod.yml）

## 关键依赖
- **Web框架**：Gin - HTTP路由和中间件
- **权限引擎**：Casbin - 基于策略的访问控制
- **数据库**：GORM - ORM映射和数据操作
- **缓存**：go-redis - Redis客户端
- **JSON处理**：ByteDance Sonic - 高性能JSON处理
- **UUID生成**：Google UUID - 唯一标识符
- **协程池**：Ants v2.11.3 - 协程池管理
- **工具集**：Samber Lo - 函数式编程工具

## 开发工作流
1. 使用 Wire 进行依赖注入代码生成
2. 通过 gen 工具自动生成数据模型和查询代码
3. 使用 Makefile 执行构建、测试、部署任务
4. Docker Compose 启动本地开发环境
5. Git 进行版本控制和协作开发

## 测试策略
- 单元测试：使用 Go 内置 testing 包
- 集成测试：测试API接口和数据库交互
- 性能测试：压力测试和性能监控
- 安全测试：权限控制和安全漏洞扫描

## 性能考量
- 数据库连接池：优化数据库连接管理
- Redis缓存：减少数据库查询压力
- 协程池：控制并发数量，避免资源耗尽
- JSON优化：使用 Sonic 提升JSON处理性能
- 中间件优化：合理使用中间件，避免性能损耗

## 特殊功能
- **情感分析服务**：Python微服务，提供AI情感分析能力
- **工作流引擎**：支持复杂业务流程自动化
- **表达式引擎**：动态规则计算和条件判断
- **多租户支持**：支持SaaS模式的多租户架构
