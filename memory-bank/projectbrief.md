# 项目概述

## 项目名称
N-Admin - 现代化的Go后台管理系统

## 核心目标
- 提供企业级的权限管理平台
- 快速开发管理系统的基础架构
- 集成工作流引擎和表达式引擎
- 支持 RBAC 权限控制模型

## 关键功能
- 用户管理：用户CRUD、角色分配、权限控制
- 角色管理：角色定义、权限分配、层级管理
- 部门管理：组织架构、层级关系
- 岗位管理：职位定义、人员配置
- 路由管理：动态路由、菜单控制
- 权限控制：基于Casbin的RBAC模型
- 系统日志：操作日志、审计追踪
- 工作流引擎：业务流程自动化
- 表达式引擎：动态规则计算
- 情感分析：集成Python情感分析服务

## 主要约束
- 使用 Go 1.24.3 作为后端语言
- 前端基于 Vue3 + AntDesign Vue
- 数据库支持 SQLite（开发）和其他关系型数据库
- 必须支持 Docker 部署
- 遵循企业级安全标准

## 成功标准
- 完整的RBAC权限体系实现
- 高性能的API响应（<100ms）
- 完善的测试覆盖率（>80%）
- 清晰的文档和部署指南
- 支持水平扩展的架构设计
