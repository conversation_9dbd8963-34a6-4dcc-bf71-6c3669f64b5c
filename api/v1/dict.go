package v1

import (
	"time"

	"admin/internal/model"
)

// DictCreateReq 创建Dict 字典表请求
// @Description 创建Dict 字典表请求参数
type DictCreateReq struct {
	Sort *int64 `json:"sort" binding:"omitempty,gte=0"` // 排序字段
	Name string `json:"name" binding:"required,max=255"` // 字典名称
	Code string `json:"code" binding:"required,max=255"` // 字典键值
	ParentCode *string `json:"parentCode" binding:"omitempty,max=255"` // 父code
	Status *string `json:"status" binding:"omitempty,max=255"` // 状态，enable表示启用，disabled表示禁用

}

// DictUpdateReq 更新Dict 字典表请求
// @Description 更新Dict 字典表请求参数
type DictUpdateReq struct {
	ID string `json:"id" binding:"required"` // Dict 字典表UUID
	Sort *int64 `json:"sort" binding:"omitempty,gte=0"` // 排序字段
	Name string `json:"name" binding:"required,max=255"` // 字典名称
	Code string `json:"code" binding:"required,max=255"` // 字典键值
	ParentID *string `json:"parentId" binding:"omitempty,max=255"` // 父级字典
	ParentCode *string `json:"parentCode" binding:"omitempty,max=255"` // 父code
	Status *string `json:"status" binding:"omitempty,max=255"` // 状态，enable表示启用，disabled表示禁用

}

// DictPageReq 分页查询Dict 字典表请求
// @Description 分页查询Dict 字典表请求参数
type DictPageReq struct {
	*QueryPage
	*CreateTime
	*UpdateTime
	OrderBy   string `json:"orderBy" example:"created_at"` // 排序字段
	OrderDesc bool   `json:"orderDesc" example:"true"`     // 是否降序

	Name string `json:"name"` // 字典名称模糊搜索

	Code string `json:"code"` // 字典键值模糊搜索

	Status []string `json:"status"` // 状态，enable表示启用，disabled表示禁用多值查询

	ParentCode string `json:"parentCode"` // 父code模糊搜索

}

// DictTreeReq 查询Dict 字典表树形结构请求
// @Description 查询Dict 字典表树形结构请求参数
type DictTreeReq struct {
	Name string `json:"name"` // 字典名称模糊搜索

	Code string `json:"code"` // 字典键值模糊搜索

	Status string `json:"status"` // 状态，enable表示启用，disabled表示禁用模糊搜索

	ParentCode string `json:"parentCode"` // 父code模糊搜索

}

// DictResp Dict 字典表响应
// @Description Dict 字典表响应数据
type DictResp struct {
	ID int64 `json:"id"` // 主键ID

	UUID string `json:"uuid"` // 全局唯一ID

	CreatedAt time.Time `json:"createdAt"` // 创建时间

	UpdatedAt time.Time `json:"updatedAt"` // 更新时间

	Sort *int64 `json:"sort"` // 排序字段

	Name string `json:"name"` // 字典名称

	Code string `json:"code"` // 字典键值

	ParentID *string `json:"parentId"` // 父级字典

	ParentCode *string `json:"parentCode"` // 父code

	Status *string `json:"status"` // 状态，enable表示启用，disabled表示禁用

	Children []*DictResp `json:"children"` // Children

}

// ToDictResp 转换为Dict 字典表响应
// @Description 将数据库实体转换为API响应结构
func ToDictResp(entity *model.Dict) *DictResp {
	if entity == nil {
		return nil
	}

	resp := &DictResp{
		ID: entity.ID,

		UUID: entity.UUID,

		CreatedAt: entity.CreatedAt,

		UpdatedAt: entity.UpdatedAt,

		Sort: entity.Sort,

		Name: entity.Name,

		Code: entity.Code,

		ParentID: entity.ParentID,

		ParentCode: entity.ParentCode,

		Status: entity.Status,
	}

	// 转换子节点
	if len(entity.Children) > 0 {
		resp.Children = make([]*DictResp, len(entity.Children))
		for i, child := range entity.Children {
			resp.Children[i] = ToDictResp(child)
		}
	}

	return resp
}

// ToDictRespList 批量转换为Dict 字典表响应列表
// @Description 批量将数据库实体转换为API响应结构列表
func ToDictRespList(entities []*model.Dict) []*DictResp {
	if len(entities) == 0 {
		return []*DictResp{}
	}

	result := make([]*DictResp, len(entities))
	for i, entity := range entities {
		result[i] = ToDictResp(entity)
	}
	return result
}
