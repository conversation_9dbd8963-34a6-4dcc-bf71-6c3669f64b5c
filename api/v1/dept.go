package v1

type Dept struct {
	ID        string `json:"id"`        // 主键ID
	Name      string `json:"name"`      // 部门名称
	ParentID  string `json:"parentId"`  // 父级部门ID，顶级部门为NULL
	ManagerID string `json:"managerId"` // 部门负责人ID
	Status    string `json:"status"`    // 状态，enable表示启用，disabled表示禁用
	Sort      int64  `json:"sort"`      // 排序字段
	Remark    string ` json:"remark"`   // 备注或描述
}

type DeptPage struct {
	*QueryPage
	*CreateTime
	*UpdateTime
	*Dept
}
