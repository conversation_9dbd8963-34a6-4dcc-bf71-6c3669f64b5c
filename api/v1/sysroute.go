package v1

import (
	"admin/internal/model"
)

// SysRouteCreateReq 创建SysRoute 路由表请求
// @Description 创建SysRoute 路由表请求参数
type SysRouteCreateReq struct {
	Name string `json:"name" binding:"required,max=255"` // 路由唯一名称

	Path string `json:"path" binding:"required,max=255"` // 路由路径

	Component *string `json:"component" binding:"omitempty,max=255"` // 组件路径

	Redirect *string `json:"redirect" binding:"omitempty,max=255"` // 重定向路径

	ParentID *string `json:"parentId" binding:"omitempty,max=255"` // 父级ID

	Sort *int64 `json:"sort" binding:"omitempty,gte=0"` // 排序（越大越靠前）

	Status *string `json:"status" binding:"required,max=255"` // 状态（enable启用 disabled禁用）

	IsLayout bool `json:"isLayout" binding:"required"` // 是否一级路由（1是 0否）

	Title string `json:"title" binding:"required,max=255"` // 标题

	Icon *string `json:"icon" binding:"omitempty,max=255"` // Icon

	Hidden *bool `json:"hidden" binding:"omitempty"` // Hidden

	ActiveMenu *string `json:"activeMenu" binding:"omitempty,max=255"` // ActiveMenu

	KeepAlive *bool `json:"keepAlive" binding:"omitempty"` // KeepAlive

	Auth *string `json:"auth" binding:"omitempty,max=255"` // Auth

	Breadcrumb *bool `json:"breadcrumb" binding:"omitempty"` // Breadcrumb

	External *bool `json:"external" binding:"omitempty"` // External

	ExternalURL *string `json:"externalUrl" binding:"omitempty,url,max=255"` // 链接地址

	TenantID *string `json:"tenantId" binding:"omitempty,max=255"` // 租户ID
}

// SysRouteUpdateReq 更新SysRoute 路由表请求
// @Description 更新SysRoute 路由表请求参数
type SysRouteUpdateReq struct {
	ID string `json:"id" binding:"required"` // SysRoute 路由表UUID

	Name string `json:"name" binding:"required,max=255"` // 路由唯一名称

	Path string `json:"path" binding:"required,max=255"` // 路由路径

	Component *string `json:"component" binding:"omitempty,max=255"` // 组件路径

	Redirect *string `json:"redirect" binding:"omitempty,max=255"` // 重定向路径

	ParentID *string `json:"parentId" binding:"omitempty,max=255"` // 父级ID

	Sort *int64 `json:"sort" binding:"omitempty,gte=0"` // 排序（越大越靠前）

	Status *string `json:"status" binding:"required,max=255"` // 状态（enable启用 disabled禁用）

	IsLayout bool `json:"isLayout" binding:"required"` // 是否一级路由（1是 0否）

	Title string `json:"title" binding:"required,max=255"` // 标题

	Icon *string `json:"icon" binding:"omitempty,max=255"` // Icon

	Hidden *bool `json:"hidden" binding:"omitempty"` // Hidden

	ActiveMenu *string `json:"activeMenu" binding:"omitempty,max=255"` // ActiveMenu

	KeepAlive *bool `json:"keepAlive" binding:"omitempty"` // KeepAlive

	Auth *string `json:"auth" binding:"omitempty,max=255"` // Auth

	Breadcrumb *bool `json:"breadcrumb" binding:"omitempty"` // Breadcrumb

	External *bool `json:"external" binding:"omitempty"` // External

	ExternalURL *string `json:"externalUrl" binding:"omitempty,url,max=255"` // 链接地址

	TenantID *string `json:"tenantId" binding:"omitempty,max=255"` // 租户ID
}

// SysRoutePageReq 分页查询SysRoute 路由表请求
// @Description 分页查询SysRoute 路由表请求参数
type SysRoutePageReq struct {
	*QueryPage
	*CreateTime
	*UpdateTime
	OrderBy   string `json:"orderBy" example:"created_at"` // 排序字段
	OrderDesc bool   `json:"orderDesc" example:"true"`     // 是否降序

	Name string `json:"name"` // 路由唯一名称模糊搜索

	Title string `json:"title"` // 标题模糊搜索

	Status []string `json:"status"` // 状态（enable启用 disabled禁用）多值查询

	Redirect string `json:"redirect"` // 重定向路径模糊搜索

	Path string `json:"path"` // 路由路径模糊搜索

	Component string `json:"component"` // 组件路径模糊搜索

	Icon string `json:"icon"` // Icon模糊搜索

	ActiveMenu string `json:"activeMenu"` // ActiveMenu模糊搜索

	Auth string `json:"auth"` // Auth模糊搜索

	ExternalURL string `json:"externalUrl"` // 链接地址模糊搜索

	TenantID string `json:"tenantId"` // 租户ID模糊搜索
}

// SysRouteTreeReq 查询SysRoute 路由表树形结构请求
// @Description 查询SysRoute 路由表树形结构请求参数
type SysRouteTreeReq struct {
	Name string `json:"name"` // 路由唯一名称模糊搜索

	Title string `json:"title"` // 标题模糊搜索

	Status string `json:"status"` // 状态（enable启用 disabled禁用）模糊搜索

	Redirect string `json:"redirect"` // 重定向路径模糊搜索

	Path string `json:"path"` // 路由路径模糊搜索

	Component string `json:"component"` // 组件路径模糊搜索

	Icon string `json:"icon"` // Icon模糊搜索

	ActiveMenu string `json:"activeMenu"` // ActiveMenu模糊搜索

	Auth string `json:"auth"` // Auth模糊搜索

	ExternalURL string `json:"externalUrl"` // 链接地址模糊搜索

	TenantID string `json:"tenantId"` // 租户ID模糊搜索
}

// SysRouteResp SysRoute 路由表响应
// @Description SysRoute 路由表响应数据
type SysRouteResp struct {
	ID int64 `json:"id"` // 主键ID

	UUID string `json:"uuid"` // 全局唯一ID

	CreatedAt *model.TimeNormal `json:"createdAt"` // 创建时间
	UpdatedAt *model.TimeNormal `json:"updatedAt"` // 更新时间
	Name      string            `json:"name"`      // 路由唯一名称

	Path string `json:"path"` // 路由路径

	Component *string `json:"component"` // 组件路径

	Redirect *string `json:"redirect"` // 重定向路径

	ParentID *string `json:"parentId"` // 父级ID

	Sort *int64 `json:"sort"` // 排序（越大越靠前）

	Status *string `json:"status"` // 状态（enable启用 disabled禁用）

	IsLayout bool `json:"isLayout"` // 是否一级路由（1是 0否）

	Title string `json:"title"` // 标题

	Icon *string `json:"icon"` // Icon

	Hidden *bool `json:"hidden"` // Hidden

	ActiveMenu *string `json:"activeMenu"` // ActiveMenu

	KeepAlive *bool `json:"keepAlive"` // KeepAlive

	Auth *string `json:"auth"` // Auth

	Breadcrumb *bool `json:"breadcrumb"` // Breadcrumb

	External *bool `json:"external"` // External

	ExternalURL *string `json:"externalUrl"` // 链接地址

	TenantID *string `json:"tenantId"` // 租户ID

	Children []*SysRouteResp `json:"children"` // Children
}

// ToSysRouteResp 转换为SysRoute 路由表响应
// @Description 将数据库实体转换为API响应结构
func ToSysRouteResp(entity *model.SysRoute) *SysRouteResp {
	if entity == nil {
		return nil
	}

	resp := &SysRouteResp{
		ID:          entity.ID,
		UUID:        entity.UUID,
		CreatedAt:   TimeToModelTimeNormal(entity.CreatedAt),
		UpdatedAt:   TimeToModelTimeNormal(entity.UpdatedAt),
		Name:        entity.Name,
		Path:        entity.Path,
		Component:   entity.Component,
		Redirect:    entity.Redirect,
		ParentID:    entity.ParentID,
		Sort:        entity.Sort,
		Status:      entity.Status,
		IsLayout:    entity.IsLayout,
		Title:       entity.Title,
		Icon:        entity.Icon,
		Hidden:      entity.Hidden,
		ActiveMenu:  entity.ActiveMenu,
		KeepAlive:   entity.KeepAlive,
		Auth:        entity.Auth,
		Breadcrumb:  entity.Breadcrumb,
		External:    entity.External,
		ExternalURL: entity.ExternalURL,
	}
	// 转换子节点
	if len(entity.Children) > 0 {
		resp.Children = make([]*SysRouteResp, len(entity.Children))
		for i, child := range entity.Children {
			resp.Children[i] = ToSysRouteResp(child)
		}
	}
	return resp
}

// ToSysRouteRespList 批量转换为SysRoute 路由表响应列表
// @Description 批量将数据库实体转换为API响应结构列表
func ToSysRouteRespList(entities []*model.SysRoute) []*SysRouteResp {
	if len(entities) == 0 {
		return []*SysRouteResp{}
	}

	result := make([]*SysRouteResp, len(entities))
	for i, entity := range entities {
		result[i] = ToSysRouteResp(entity)
	}
	return result
}
