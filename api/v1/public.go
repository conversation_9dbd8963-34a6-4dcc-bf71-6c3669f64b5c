package v1

import "admin/internal/model"

// QIDs 多个id
type QIDs[T comparable] struct {
	Ids []T
}

// Page 分页
type Page[T any] struct {
	Current  int   `json:"current"`
	PageSize int   `json:"pageSize"`
	Total    int64 `json:"total"`
	List     []T   `json:"list,omitempty"`
}

// QID 查询id
type QID struct {
	ID int64 `json:"id" binding:"required"`
}

// QueryPage 查询分页
type QueryPage struct {
	Page      int    `json:"currentPage" binding:"required,gte=1"`
	PageSize  int    `json:"pageSize" binding:"required,gte=1,lte=100"`
	OrderBy   string `json:"orderBy" form:"orderBy"`     // 排序字段
	OrderDesc bool   `json:"orderDesc" form:"orderDesc"` // 是否降序
}

// GetPage 获取页码，默认为1
func (p *QueryPage) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

// GetPageSize 获取每页数量，默认为10
func (p *QueryPage) GetPageSize() int {
	if p.PageSize <= 0 {
		return 10
	}
	if p.PageSize > 100 {
		return 100
	}
	return p.PageSize
}

// CreateTime 通用的创建时间
type CreateTime struct {
	CreateTime model.TimeNormal `json:"createTime,omitempty" `
}

// UpdateTime 通用的更新时间
type UpdateTime struct {
	UpdateTime model.TimeNormal `json:"updateTime,omitempty" `
}

// NewPage 创建一个新的分页结果
func NewPage[T any](list []T, total int64, current, pageSize int) *Page[T] {
	return &Page[T]{
		Current:  current,
		PageSize: pageSize,
		Total:    total,
		List:     list,
	}
}
