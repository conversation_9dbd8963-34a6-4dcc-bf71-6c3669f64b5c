package v1

import (
	"net/http"

	"admin/internal/utils"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data,omitzero"`
	Succeed bool   `json:"succeed"`
}

func HandleSuccess(ctx *gin.Context, data any) {
	resp := Response{Code: Success.Code, Message: Success.Error(), Data: data, Succeed: true}
	ctx.JSON(http.StatusOK, resp)
}

func HandleError(ctx *gin.Context, err error) {
	var code, msg string
	if utils.IsValidationError(err) {
		code = ErrInvalidParams.Code
		msg = utils.TranslateValidationErrors(err)
	} else if e, ok := err.(*Error); ok {
		code = e.Code
		msg = e.Message
	} else {
		code = ErrInternalError.Code
		msg = err.Error()
	}
	resp := Response{Code: code, Message: msg, Succeed: false}
	ctx.JSON(http.StatusOK, resp)
}
