package v1

import (
	"admin/internal/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data,omitzero"`
	Succeed bool   `json:"succeed"`
}

func HandleSuccess(ctx *gin.Context, data any) {
	resp := Response{Code: Success, Message: GetMessageZH(Success), Data: data, Succeed: true}
	ctx.JSON(http.StatusOK, resp)
}

func HandleError(ctx *gin.Context, code string, err error) {
	resp := Response{Code: code, Message: GetMessageZH(code), Succeed: false}
	if err != nil {
		resp.Message += " 【" + err.Error() + "】"
	}
	ctx.JSON(http.StatusOK, resp)
}

// HandleValidationError 处理验证错误，将验证错误转换为中文
func HandleValidationError(ctx *gin.Context, err error) {
	resp := Response{Code: InvalidParams, Message: GetMessageZH(InvalidParams), Succeed: false}
	if err != nil {
		// 如果是验证错误，使用中文翻译
		if utils.IsValidationError(err) {
			translatedMsg := utils.TranslateValidationErrors(err)
			resp.Message = translatedMsg
		} else {
			resp.Message += " 【" + err.Error() + "】"
		}
	}
	ctx.JSON(http.StatusOK, resp)
}
