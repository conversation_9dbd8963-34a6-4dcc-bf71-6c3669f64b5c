package v1

import (
	"time"

	"gorm.io/datatypes"
)

// Role API请求结构体
type Role struct {
	ID        string         `json:"id,omitempty"`
	Name      string         `json:"name"`
	Key       string         `json:"key"`
	DataScope string         `json:"dataScope,omitempty"`
	Meta      datatypes.JSON `json:"meta,omitempty"`
	ParentID  string         `json:"parentId,omitempty"`
	Level     int64          `json:"level,omitempty"`
	Remark    string         `json:"remark,omitempty"`
	Status    string         `json:"status,omitempty"`
	Sort      int64          `json:"sort,omitempty"`
}

// RolePage 角色分页查询
type RolePage struct {
	*QueryPage
	Name       string    `json:"name,omitempty" form:"name"`
	CreateTime time.Time `json:"createTime,omitempty" form:"createTime"`
}

// RolePermission 角色权限
type RolePermission struct {
	RoleID      string   `json:"roleId"`
	Permissions []string `json:"permissions"`
}

// RoleRouter 角色路由
type RoleRouter struct {
	RoleID    string   `json:"roleId"`
	RouterIDS []string `json:"routerIds"`
}
