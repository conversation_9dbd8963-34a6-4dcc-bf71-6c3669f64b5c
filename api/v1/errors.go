package v1

import "golang.org/x/text/language"

const (
	Success            = "US00000" // 成功
	SuccessCreated     = "US00001" // 创建成功
	SuccessUpdated     = "US00002" // 更新成功
	SuccessDeleted     = "US00003" // 删除成功
	InternalError      = "US99901" // 内部服务器错误
	InvalidParams      = "US99902" // 参数错误
	Unauthorized       = "US99903" // 未授权
	Forbidden          = "US99904" // 禁止访问
	NotFound           = "US99905" // 资源不存在
	MethodNotAllowed   = "US99906" // 方法不允许
	RequestTimeout     = "US99907" // 请求超时
	TooManyRequests    = "US99908" // 请求过多
	ServiceUnavailable = "US99909" // 服务不可用
)

// 认证模块 (AU)
const (
	AuthInvalidCredentials = "USAU001" // 无效凭证
	AuthExpiredToken       = "USAU002" // Token过期
	AuthInvalidToken       = "USAU003" // 无效Token
	AuthUserDisabled       = "USAU004" // 用户被禁用
	AuthCaptchaError       = "USAU005" // 验证码错误
	AuthLoginFailed        = "USAU006" // 登录失败
	AuthNotLogin           = "USAU007" // 未登录
	// 用户不存在
	AuthUserNotExists = "USAU008"
	// 用户名或密码错误
	AuthUsernameOrPasswordError = "USAU009"
	// 用户名已存在
	AuthUsernameExists = "USAU010"
	// 邮箱已存在
	AuthEmailExists = "USAU011"
	// 手机号已存在
	AuthPhoneExists = "USAU012"
	// 用户被禁用
	AuthUserDisabledError = "USAU013"
)

// 权限模块 (RBAC)
const (
	RBACPermissionDenied                  = "USRB001" // 权限不足
	RBACPermissionExists                  = "USRB002" // 权限已存在
	RBACPermissionNotExists               = "USRB003" // 权限不存在
	RBACRoleExists                        = "USRB004" // 角色已存在
	RBACRoleNotExists                     = "USRB005" // 角色不存在
	RBACUserExists                        = "USRB006" // 用户已存在
	RBACUserNotExists                     = "USRB007" // 用户不存在
	RBACUserRoleExists                    = "USRB008" // 用户角色已存在
	RBACUserRoleNotExists                 = "USRB009" // 用户角色不存在
	RBACUserRoleUserExists                = "USRB010" // 用户角色用户已存在
	RBACUserRoleUserNotExists             = "USRB011" // 用户角色用户不存在
	RBACUserRoleRoleExists                = "USRB012" // 用户角色角色已存在
	RBACUserRoleRoleNotExists             = "USRB013" // 用户角色角色不存在
	RBACRolePermissionExists              = "USRB014" // 角色权限已存在
	RBACRolePermissionNotExists           = "USRB015" // 角色权限不存在
	RBACRolePermissionPermissionExists    = "USRB016" // 角色权限权限已存在
	RBACRolePermissionPermissionNotExists = "USRB017" // 角色权限权限不存在
	RBACRolePermissionRoleExists          = "USRB018" // 角色权限角色已存在
	RBACRolePermissionRoleNotExists       = "USRB019" // 角色权限角色不存在
	RBACRolePermissionUserExists          = "USRB020" // 角色权限用户已存在
	ErrRouteAlreadyExists                 = "USRB021" // 路由已存在
)

// 用户权限相关
const (
	UserPermissionExists              = "USUP001" // 用户权限已存在
	UserPermissionNotExists           = "USUP002" // 用户权限不存在
	UserPermissionPermissionExists    = "USUP003" // 用户权限权限已存在
	UserPermissionPermissionNotExists = "USUP004" // 用户权限权限不存在
	UserPermissionUserExists          = "USUP005" // 用户权限用户已存在
	UserPermissionUserNotExists       = "USUP006" // 用户权限用户不存在
	UserPermissionRoleExists          = "USUP007" // 用户权限角色已存在
	UserPermissionRoleNotExists       = "USUP008" // 用户权限角色不存在
	UserPermissionNotAllowed          = "USUP009" // 用户无权限
)

// 上传模块
const (
	UploadFileTooLarge       = "USUP001" // 文件过大
	UploadFileTypeNotAllowed = "USUP002" // 文件类型不允许
	UploadFileUploadFailed   = "USUP004" // 文件上传失败
	UploadFileNotFound       = "USUP005" // 文件不存在
	// 文件为空
	UploadFileEmpty = "USUP006" // 文件为空
	// 文件格式错误
	UploadFileFormatError = "USUP007" // 文件格式错误
	// 文件大小超过限制
	UploadFileSizeExceedsLimit = "USUP008" // 文件大小超过限制
)

// 工作流模块 (WF)
const (
	WFProcessDefNotFound          = "USWF001" // 流程定义不存在
	WFProcessDefAlreadyExists     = "USWF002" // 流程定义已存在
	WFProcessVersionNotFound      = "USWF003" // 流程版本不存在
	WFProcessVersionAlreadyActive = "USWF004" // 流程版本已激活
	WFProcessDefDisabled          = "USWF005" // 流程定义已禁用
	WFProcessDefHasInstance       = "USWF006" // 流程定义有关联的实例
	WFProcessDefKeyDuplicate      = "USWF007" // 流程定义标识重复
	WFCannotDeleteActiveVersion   = "USWF008" // 不能删除激活的版本
	WFNodeNotFound                = "USWF009" // 节点不存在
	WFProcessInstanceNotFound     = "USWF010" // 流程实例不存在
	WFProcessNotRunning           = "USWF011" // 流程不是运行状态
	WFProcessNotSuspended         = "USWF012" // 流程不是挂起状态
)

// 错误消息映射表
var codeMessages = map[string]map[language.Tag]string{
	Success: {
		language.English: "Success",
		language.Chinese: "成功",
	},
	SuccessCreated: {
		language.English: "Created successfully",
		language.Chinese: "创建成功",
	},
	SuccessUpdated: {
		language.English: "Updated successfully",
		language.Chinese: "更新成功",
	},
	SuccessDeleted: {
		language.English: "Deleted successfully",
		language.Chinese: "删除成功",
	},
	InternalError: {
		language.English: "Internal server error",
		language.Chinese: "内部服务器错误",
	},
	InvalidParams: {
		language.English: "Invalid parameters",
		language.Chinese: "参数错误",
	},
	Unauthorized: {
		language.English: "Unauthorized",
		language.Chinese: "未授权",
	},
	Forbidden: {
		language.English: "Forbidden",
		language.Chinese: "禁止访问",
	},
	NotFound: {
		language.English: "Resource not found",
		language.Chinese: "资源不存在",
	},
	MethodNotAllowed: {
		language.English: "Method not allowed",
		language.Chinese: "方法不允许",
	},
	RequestTimeout: {
		language.English: "Request timeout",
		language.Chinese: "请求超时",
	},
	TooManyRequests: {
		language.English: "Too many requests",
		language.Chinese: "请求过多",
	},
	ServiceUnavailable: {
		language.English: "Service unavailable",
		language.Chinese: "服务不可用",
	},
	AuthInvalidCredentials: {
		language.English: "Invalid credentials",
		language.Chinese: "无效凭证",
	},
	AuthExpiredToken: {
		language.English: "Token expired",
		language.Chinese: "Token过期",
	},
	AuthInvalidToken: {
		language.English: "Invalid token",
		language.Chinese: "无效Token",
	},
	AuthUserDisabled: {
		language.English: "User disabled",
		language.Chinese: "用户被禁用",
	},
	AuthCaptchaError: {
		language.English: "Captcha error",
		language.Chinese: "验证码错误",
	},
	AuthLoginFailed: {
		language.English: "Login failed",
		language.Chinese: "登录失败",
	},
	AuthNotLogin: {
		language.English: "Not login",
		language.Chinese: "未登录",
	},
	RBACPermissionDenied: {
		language.English: "Permission denied",
		language.Chinese: "权限不足",
	},
	RBACPermissionExists: {
		language.English: "Permission already exists",
		language.Chinese: "权限已存在",
	},
	UserPermissionNotAllowed: {
		language.English: "User permission not allowed",
		language.Chinese: "用户无权限",
	},
	// 用户不存在
	AuthUserNotExists: {
		language.English: "User not exists",
		language.Chinese: "用户不存在",
	},
	// 用户名或密码错误
	AuthUsernameOrPasswordError: {
		language.English: "Username or password error",
		language.Chinese: "用户名或密码错误",
	},
	// 用户名已存在
	AuthUsernameExists: {
		language.English: "Username already exists",
		language.Chinese: "用户名已存在",
	},
	// 邮箱已存在
	AuthEmailExists: {
		language.English: "Email already exists",
		language.Chinese: "邮箱已存在",
	},
	// 手机号已存在
	AuthPhoneExists: {
		language.English: "Phone already exists",
		language.Chinese: "手机号已存在",
	},
	// 用户被禁用
	AuthUserDisabledError: {
		language.English: "User disabled",
		language.Chinese: "用户被禁用",
	},
	// UploadFileTypeNotAllowed
	UploadFileTypeNotAllowed: {
		language.English: "File type not allowed",
		language.Chinese: "文件类型不允许",
	},
	// UploadFileTooLarge
	UploadFileTooLarge: {
		language.English: "File too large",
		language.Chinese: "文件过大",
	},
	// UploadFileUploadFailed
	UploadFileUploadFailed: {
		language.English: "File upload failed",
		language.Chinese: "文件上传失败",
	},
	// 工作流错误消息
	WFProcessDefNotFound: {
		language.English: "Process definition not found",
		language.Chinese: "流程定义不存在",
	},
	WFProcessDefAlreadyExists: {
		language.English: "Process definition already exists",
		language.Chinese: "流程定义已存在",
	},
	WFProcessVersionNotFound: {
		language.English: "Process version not found",
		language.Chinese: "流程版本不存在",
	},
	WFProcessVersionAlreadyActive: {
		language.English: "Process version already active",
		language.Chinese: "流程版本已激活",
	},
	WFProcessDefDisabled: {
		language.English: "Process definition is disabled",
		language.Chinese: "流程定义已禁用",
	},
	WFProcessDefHasInstance: {
		language.English: "Process definition has instances",
		language.Chinese: "流程定义有关联的实例",
	},
	WFProcessDefKeyDuplicate: {
		language.English: "Process definition key already exists",
		language.Chinese: "流程定义标识重复",
	},
	WFCannotDeleteActiveVersion: {
		language.English: "Cannot delete active version",
		language.Chinese: "不能删除激活的版本",
	},
	WFNodeNotFound: {
		language.English: "Process node not found",
		language.Chinese: "流程节点不存在",
	},
	WFProcessInstanceNotFound: {
		language.English: "Process instance not found",
		language.Chinese: "流程实例不存在",
	},
	WFProcessNotRunning: {
		language.English: "Process is not in running state",
		language.Chinese: "流程不是运行状态",
	},
	WFProcessNotSuspended: {
		language.English: "Process is not in suspended state",
		language.Chinese: "流程不是挂起状态",
	},
}

// GetMessage 获取错误消息（根据语言标签）
func GetMessage(code string, lang language.Tag) string {
	if messages, ok := codeMessages[code]; ok {
		if msg, ok := messages[lang]; ok {
			return msg
		}
		// 回退到英文
		if msg, ok := messages[language.English]; ok {
			return msg
		}
	}
	return "Unknown error"
}

// GetMessageEN 获取英文错误消息
func GetMessageEN(code string) string {
	return GetMessage(code, language.English)
}

// GetMessageZH 获取中文错误消息
func GetMessageZH(code string) string {
	return GetMessage(code, language.Chinese)
}
