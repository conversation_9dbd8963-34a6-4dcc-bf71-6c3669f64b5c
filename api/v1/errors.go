package v1

// 业务错误类型
type Error struct {
	Code    string
	Message string
}

func (e *Error) Error() string {
	return e.Message
}

func NewError(code, msg string) *Error {
	return &Error{Code: code, Message: msg}
}

// 预定义错误
var (
	Success               = NewError("S0000", "成功")
	ErrSuccessCreated     = NewError("S0001", "创建成功")
	ErrSuccessUpdated     = NewError("S0002", "更新成功")
	ErrSuccessDeleted     = NewError("S0003", "删除成功")
	ErrInternalError      = NewError("E9001", "内部服务器错误")
	ErrInvalidParams      = NewError("E9002", "参数错误")
	ErrUnauthorized       = NewError("E9003", "未授权")
	ErrForbidden          = NewError("E9004", "禁止访问")
	ErrNotFound           = NewError("E9005", "资源不存在")
	ErrMethodNotAllowed   = NewError("E9006", "方法不允许")
	ErrRequestTimeout     = NewError("E9007", "请求超时")
	ErrTooManyRequests    = NewError("E9008", "请求过多")
	ErrServiceUnavailable = NewError("E9009", "服务不可用")
	// 查询失败
	ErrQueryFailed = NewError("Q1001", "查询失败")
	// 创建失败
	ErrCreateFailed = NewError("C1001", "创建失败")
	// 更新失败
	ErrUpdateFailed = NewError("U1001", "更新失败")
	// 删除失败
	ErrDeleteFailed = NewError("D1001", "删除失败")

	// 数据验证模块 (VA)
	ErrValidationFailed = NewError("VA6001", "数据验证失败")
	ErrInvalidID        = NewError("VA6002", "无效的ID")
	ErrInvalidUUID      = NewError("VA6003", "无效的UUID")
	ErrRequiredField    = NewError("VA6004", "必填字段不能为空")
	ErrInvalidFormat    = NewError("VA6005", "格式不正确")
	ErrValueTooLong     = NewError("VA6006", "值过长")
	ErrValueTooShort    = NewError("VA6007", "值过短")
	ErrInvalidRange     = NewError("VA6008", "值超出范围")
	ErrDuplicateValue   = NewError("VA6009", "值重复")
	ErrInvalidEmail     = NewError("VA6010", "邮箱格式不正确")
	ErrInvalidPhone     = NewError("VA6011", "手机号格式不正确")

	// 数据库操作模块 (DB)
	ErrDatabaseConnection  = NewError("DB7001", "数据库连接失败")
	ErrRecordNotFound      = NewError("DB7002", "记录不存在")
	ErrRecordExists        = NewError("DB7003", "记录已存在")
	ErrTransactionFailed   = NewError("DB7004", "事务执行失败")
	ErrConstraintViolation = NewError("DB7005", "约束冲突")
	ErrForeignKeyViolation = NewError("DB7006", "外键约束冲突")
	ErrUniqueViolation     = NewError("DB7007", "唯一约束冲突")
	ErrDeadlock            = NewError("DB7008", "数据库死锁")

	// 业务逻辑模块 (BL)
	ErrBusinessLogic       = NewError("BL8001", "业务逻辑错误")
	ErrStatusInvalid       = NewError("BL8002", "状态无效")
	ErrOperationNotAllowed = NewError("BL8003", "操作不被允许")
	ErrResourceInUse       = NewError("BL8004", "资源正在使用中")
	ErrResourceLocked      = NewError("BL8005", "资源已锁定")
	ErrQuotaExceeded       = NewError("BL8006", "配额已超出")

	// 认证模块 (AU)
	ErrAuthInvalidCredentials      = NewError("AU1001", "无效凭证")
	ErrAuthExpiredToken            = NewError("AU1002", "Token过期")
	ErrAuthInvalidToken            = NewError("AU1003", "无效Token")
	ErrAuthUserDisabled            = NewError("AU1004", "用户被禁用")
	ErrAuthCaptchaError            = NewError("AU1005", "验证码错误")
	ErrAuthLoginFailed             = NewError("AU1006", "登录失败")
	ErrAuthNotLogin                = NewError("AU1007", "未登录")
	ErrAuthUserNotExists           = NewError("AU1008", "用户不存在")
	ErrAuthUsernameOrPasswordError = NewError("AU1009", "用户名或密码错误")
	ErrAuthUsernameExists          = NewError("AU1010", "用户名已存在")
	ErrAuthEmailExists             = NewError("AU1011", "邮箱已存在")
	ErrAuthPhoneExists             = NewError("AU1012", "手机号已存在")
	// 账号或密码错误
	ErrAuthAccountOrPasswordError = NewError("AU1013", "账号或密码错误")
	// 权限模块 (RBAC)
	ErrRBACPermissionDenied    = NewError("RB2001", "权限不足")
	ErrRBACPermissionExists    = NewError("RB2002", "权限已存在")
	ErrRBACPermissionNotExists = NewError("RB2003", "权限不存在")
	ErrRBACRoleExists          = NewError("RB2004", "角色已存在")
	ErrRBACRoleNotExists       = NewError("RB2005", "角色不存在")
	ErrRBACUserExists          = NewError("RB2006", "用户已存在")
	ErrRBACUserNotExists       = NewError("RB2007", "用户不存在")
	ErrRouteAlreadyExists      = NewError("RB2021", "路由已存在")

	// 用户权限相关
	ErrUserPermissionExists     = NewError("UP3001", "用户权限已存在")
	ErrUserPermissionNotExists  = NewError("UP3002", "用户权限不存在")
	ErrUserPermissionNotAllowed = NewError("UP3009", "用户无权限")

	// 上传模块
	ErrUploadFileTooLarge         = NewError("UP4001", "文件过大")
	ErrUploadFileTypeNotAllowed   = NewError("UP4002", "文件类型不允许")
	ErrUploadFileUploadFailed     = NewError("UP4004", "文件上传失败")
	ErrUploadFileNotFound         = NewError("UP4005", "文件不存在")
	ErrUploadFileEmpty            = NewError("UP4006", "文件为空")
	ErrUploadFileFormatError      = NewError("UP4007", "文件格式错误")
	ErrUploadFileSizeExceedsLimit = NewError("UP4008", "文件大小超过限制")

	// 业务
	ErrRolePageQueryFailed       = NewError("RL9001", "角色分页查询失败")
	ErrRoleQueryFailed           = NewError("RL9002", "角色查询失败")
	ErrRolePermNotImpl           = NewError("RL9003", "获取角色权限功能未实现")
	ErrRolePermUpdateNotImpl     = NewError("RL9004", "更新角色权限功能未实现")
	ErrPostPageQueryFailed       = NewError("PT9001", "岗位分页查询失败")
	ErrPostQueryFailed           = NewError("PT9002", "岗位查询失败")
	ErrUserDisableFailed         = NewError("US9001", "用户禁用失败")
	ErrUserQueryDetailFailed     = NewError("US9002", "查询用户详情失败")
	ErrUserCreateFailed          = NewError("US9003", "创建用户失败")
	ErrUserPostBatchCreateFailed = NewError("US9004", "插入关联岗位失败")
	ErrUserPageQueryFailed       = NewError("US9005", "用户分页查询失败")
	ErrUserDeleteFailed          = NewError("US9006", "删除用户失败")
	ErrUserPostDeleteFailed      = NewError("US9007", "删除关联岗位失败")
	ErrUserUpdateFailed          = NewError("US9008", "更新用户失败")
)
