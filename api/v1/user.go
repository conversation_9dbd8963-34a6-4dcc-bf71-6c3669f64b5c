package v1

type User struct {
	ID       string   `json:"id"`
	Account  string   `json:"account" binding:"omitempty,min=3,max=20"`
	Username string   `json:"username" binding:"omitempty,min=2,max=50"`
	Phone    string   `json:"phone" binding:"omitempty,len=11,numeric"`
	Email    string   `json:"email" binding:"omitempty,email"`
	PostIds  []string `json:"postIds"`
	Status   string   `json:"status" binding:"omitempty,oneof=enabled disabled"`
	RoleIds  []int64  `json:"roleIds"`
	Sex      string   `json:"sex" binding:"omitempty,oneof=male female"` // 枚举限制
	Remark   string   `json:"remark" binding:"omitempty,max=200"`
	Sort     int64    `json:"sort" binding:"omitempty,gte=0"`
}

// Account 邮件和手机号
type Account struct {
	// Account string `json:"account,omitempty"`
	Phone string `json:"phone,omitempty"`
	Email string `json:"email,omitempty"`
}
type Login struct {
	Account  string `json:"account" binding:"required,min=3,max=50"`
	Password string `json:"password" binding:"required,min=6,max=20"`
	IP       string `json:"-"`
}

// 登录后返回数据
type LoginResp struct {
	Token string `json:"token"`
}
type UserPage struct {
	QueryPage
	CreateTime
	UpdateTime
	User
}
type LoginUser struct {
	Account     string   `json:"username"`
	Username    string   `json:"realName"`
	Avatar      []byte   `json:"avatar"`
	Roles       []string `json:"roles"`
	RoleNames   string   `json:"roleNames"`
	DeptId      int64    `json:"deptId"`
	DeptName    string   `json:"deptName"`
	Permissions []string `json:"permissions"`
}
