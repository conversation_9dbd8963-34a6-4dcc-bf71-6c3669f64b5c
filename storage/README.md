# 情感词典和停用词文件说明

## sentiment_dict.txt
这是情感分析的核心词典文件，包含了中文情感词汇及其属性。

### 文件格式
每行包含一个词条，字段用制表符分隔，格式如下：
```
词语	词性	词义数	词义序号	情感分类	强度	极性	辅助情感分类	辅助强度	辅助极性
```

### 字段说明
- **词语**: 中文词汇
- **词性**: 词性标记（adj=形容词, verb=动词, noun=名词, adv=副词等）
- **词义数**: 该词的义项总数
- **词义序号**: 当前义项的序号
- **情感分类**: 情感类别（PA=积极, NE=消极, NN=中性）
- **强度**: 情感强度值（1-9，数值越高强度越大）
- **极性**: 情感极性（0=中性, 1=褒义, 2=贬义, 3=兼有褒贬）
- **辅助情感分类**: 辅助情感类别
- **辅助强度**: 辅助情感强度
- **辅助极性**: 辅助情感极性

### 词汇类别
- **正面词汇**: 好、喜欢、开心、满意、美丽、成功等
- **负面词汇**: 坏、讨厌、难过、痛苦、糟糕、失败等  
- **中性词汇**: 一般、普通、正常、平常等

## stop_words.txt
停用词文件，包含了在情感分析中应该被过滤掉的常见词汇。

### 包含的词汇类型
- **功能词**: 的、了、在、是、我、有、和等
- **代词**: 他、她、它、们、什么、怎么等
- **时间词**: 现在、以前、刚才、马上等
- **方位词**: 这里、那里、上面、下面等
- **常见动词**: 来、去、拿、放、开、关等
- **日常用语**: 谢谢、对不起、你好、再见等
- **英文常用词**: and, but, or, yes, no等

### 作用
这些词汇通常不携带明确的情感信息，过滤掉它们可以：
1. 提高情感分析的准确性
2. 减少噪音干扰
3. 突出真正的情感词汇
4. 提高处理效率

## 使用方法
这两个文件会被情感分析器自动加载和使用：
```go
analyzer, err := sentiment.NewAnalyzer(
    "storage/sentiment_dict.txt",
    "storage/stop_words.txt", 
)
```

## 扩展建议
1. **词典扩展**: 可以根据具体应用场景添加领域相关的情感词汇
2. **停用词调整**: 根据分析效果调整停用词列表
3. **强度校准**: 根据实际使用效果调整词汇的情感强度值
4. **多义词处理**: 为多义词添加更多的义项条目
