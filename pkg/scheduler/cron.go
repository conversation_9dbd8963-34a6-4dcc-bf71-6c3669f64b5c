package scheduler

import (
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// CronConfig 定时任务配置
type CronConfig struct {
	// 启用定时任务
	Enabled bool
	// 时区设置
	Location *time.Location
	// 是否启用秒级精度
	WithSeconds bool
}

// DefaultCronConfig 返回默认定时任务配置
func DefaultCronConfig() *CronConfig {
	return &CronConfig{
		Enabled:     false,
		Location:    time.UTC,
		WithSeconds: false,
	}
}

// CronTask 定时任务
type CronTask struct {
	ID          string       `json:"id"`
	Name        string       `json:"name"`
	Spec        string       `json:"spec"`
	TaskBuilder *TaskBuilder `json:"-"`
	NextRun     time.Time    `json:"next_run"`
	LastRun     time.Time    `json:"last_run"`
	RunCount    int64        `json:"run_count"`
	Active      bool         `json:"active"`
	CreatedAt   time.Time    `json:"created_at"`
	cronID      cron.EntryID `json:"-"`
}

// CronScheduler 定时任务调度器
type CronScheduler struct {
	config    *CronConfig
	scheduler *Scheduler
	cron      *cron.Cron
	tasks     map[string]*CronTask
	mu        sync.RWMutex
}

// NewCronScheduler 创建定时任务调度器
func NewCronScheduler(scheduler *Scheduler, config *CronConfig) *CronScheduler {
	if config == nil {
		config = DefaultCronConfig()
	}

	var cronOpts []cron.Option
	cronOpts = append(cronOpts, cron.WithLocation(config.Location))

	if config.WithSeconds {
		cronOpts = append(cronOpts, cron.WithSeconds())
	}

	cs := &CronScheduler{
		config:    config,
		scheduler: scheduler,
		cron:      cron.New(cronOpts...),
		tasks:     make(map[string]*CronTask),
	}

	return cs
}

// Start 启动定时任务调度器
func (cs *CronScheduler) Start() {
	if !cs.config.Enabled {
		return
	}

	cs.cron.Start()
	cs.scheduler.logger.Info("定时任务调度器已启动")
}

// Stop 停止定时任务调度器
func (cs *CronScheduler) Stop() {
	if !cs.config.Enabled {
		return
	}

	ctx := cs.cron.Stop()
	<-ctx.Done()
	cs.scheduler.logger.Info("定时任务调度器已停止")
}

// AddCronTask 添加定时任务
func (cs *CronScheduler) AddCronTask(id, name, spec string, taskBuilder *TaskBuilder) error {
	if !cs.config.Enabled {
		return fmt.Errorf("定时任务调度器未启用")
	}

	cs.mu.Lock()
	defer cs.mu.Unlock()

	// 检查任务ID是否已存在
	if _, exists := cs.tasks[id]; exists {
		return fmt.Errorf("定时任务ID %s 已存在", id)
	}

	// 创建定时任务
	cronTask := &CronTask{
		ID:          id,
		Name:        name,
		Spec:        spec,
		TaskBuilder: taskBuilder,
		Active:      true,
		CreatedAt:   time.Now(),
	}

	// 添加到cron调度器
	cronID, err := cs.cron.AddFunc(spec, func() {
		cs.executeCronTask(cronTask)
	})
	if err != nil {
		return fmt.Errorf("添加定时任务失败: %w", err)
	}

	cronTask.cronID = cronID
	cs.tasks[id] = cronTask

	// 获取下次执行时间
	if entry := cs.cron.Entry(cronID); entry.ID != 0 {
		cronTask.NextRun = entry.Next
	}

	cs.scheduler.logger.Info("定时任务已添加",
		zap.String("task_id", id),
		zap.String("task_name", name),
		zap.String("spec", spec),
		zap.Time("next_run", cronTask.NextRun))

	return nil
}

// RemoveCronTask 移除定时任务
func (cs *CronScheduler) RemoveCronTask(taskID string) error {
	if !cs.config.Enabled {
		return fmt.Errorf("定时任务调度器未启用")
	}

	cs.mu.Lock()
	defer cs.mu.Unlock()

	cronTask, exists := cs.tasks[taskID]
	if !exists {
		return fmt.Errorf("定时任务 %s 不存在", taskID)
	}

	// 从cron调度器中移除
	cs.cron.Remove(cronTask.cronID)
	delete(cs.tasks, taskID)

	cs.scheduler.logger.Info("定时任务已移除", zap.String("task_id", taskID))
	return nil
}

// GetCronTask 获取定时任务
func (cs *CronScheduler) GetCronTask(taskID string) (*CronTask, bool) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	cronTask, exists := cs.tasks[taskID]
	return cronTask, exists
}

// GetAllCronTasks 获取所有定时任务
func (cs *CronScheduler) GetAllCronTasks() map[string]*CronTask {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	tasks := make(map[string]*CronTask)
	for id, task := range cs.tasks {
		tasks[id] = task
	}
	return tasks
}

// PauseCronTask 暂停定时任务
func (cs *CronScheduler) PauseCronTask(taskID string) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	cronTask, exists := cs.tasks[taskID]
	if !exists {
		return fmt.Errorf("定时任务 %s 不存在", taskID)
	}

	cronTask.Active = false
	cs.scheduler.logger.Info("定时任务已暂停", zap.String("task_id", taskID))
	return nil
}

// ResumeCronTask 恢复定时任务
func (cs *CronScheduler) ResumeCronTask(taskID string) error {
	cs.mu.Lock()
	defer cs.mu.Unlock()

	cronTask, exists := cs.tasks[taskID]
	if !exists {
		return fmt.Errorf("定时任务 %s 不存在", taskID)
	}

	cronTask.Active = true
	cs.scheduler.logger.Info("定时任务已恢复", zap.String("task_id", taskID))
	return nil
}

// executeCronTask 执行定时任务
func (cs *CronScheduler) executeCronTask(cronTask *CronTask) {
	cs.mu.Lock()
	// 检查任务是否激活
	if !cronTask.Active {
		cs.mu.Unlock()
		return
	}

	// 更新执行统计
	cronTask.LastRun = time.Now()
	cronTask.RunCount++

	// 更新下次执行时间
	if entry := cs.cron.Entry(cronTask.cronID); entry.ID != 0 {
		cronTask.NextRun = entry.Next
	}
	cs.mu.Unlock()

	// 构建并提交任务
	task := cronTask.TaskBuilder.Build()
	// 为cron任务生成唯一ID
	task.ID = fmt.Sprintf("%s_%d", cronTask.ID, cronTask.RunCount)

	if err := cs.scheduler.Submit(task); err != nil {
		cs.scheduler.logger.Error("提交定时任务失败",
			zap.String("cron_task_id", cronTask.ID),
			zap.String("task_id", task.ID),
			zap.Error(err))
	} else {
		cs.scheduler.logger.Debug("定时任务已提交",
			zap.String("cron_task_id", cronTask.ID),
			zap.String("task_id", task.ID),
			zap.Int64("run_count", cronTask.RunCount))
	}
}

// GetNextSchedule 获取下次执行时间
func (cs *CronScheduler) GetNextSchedule(taskID string) (time.Time, error) {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	cronTask, exists := cs.tasks[taskID]
	if !exists {
		return time.Time{}, fmt.Errorf("定时任务 %s 不存在", taskID)
	}

	return cronTask.NextRun, nil
}

// GetStats 获取定时任务统计信息
func (cs *CronScheduler) GetStats() map[string]interface{} {
	cs.mu.RLock()
	defer cs.mu.RUnlock()

	stats := map[string]interface{}{
		"total_cron_tasks":  len(cs.tasks),
		"active_cron_tasks": 0,
		"total_executions":  int64(0),
	}

	activeTasks := 0
	totalExecutions := int64(0)

	for _, task := range cs.tasks {
		if task.Active {
			activeTasks++
		}
		totalExecutions += task.RunCount
	}

	stats["active_cron_tasks"] = activeTasks
	stats["total_executions"] = totalExecutions

	return stats
}
