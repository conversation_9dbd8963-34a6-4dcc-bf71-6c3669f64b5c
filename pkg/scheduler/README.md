# Task Scheduler

基于 ants 协程池的并行任务调度器，提供高效的任务执行和管理功能。

## 功能特性

- 🚀 **高性能**: 基于 ants 协程池，高效管理并发执行
- 📋 **任务优先级**: 支持4级任务优先级（紧急、高、普通、低）
- ⏰ **超时控制**: 支持任务执行超时自动取消
- 🔄 **自动重试**: 支持失败任务自动重试机制
- 📊 **状态监控**: 实时监控任务执行状态和统计信息
- 📦 **任务分组**: 支持任务分组管理和批量操作
- 🎯 **生命周期**: 提供任务执行生命周期回调
- 🛡️ **容错性**: 优雅处理任务错误和系统关闭

## 快速开始

### 安装依赖

```go
go get github.com/panjf2000/ants/v2
```

### 基本用法

```go
package main

import (
    "fmt"
    "time"
    "etl/pkg/scheduler"
)

func main() {
    // 创建调度器
    s, err := scheduler.NewScheduler(scheduler.DefaultConfig())
    if err != nil {
        panic(err)
    }
    defer s.Shutdown(10 * time.Second)

    // 创建简单任务
    task := scheduler.NewTaskBuilder("示例任务", scheduler.SimpleTask(func() error {
        fmt.Println("Hello, World!")
        return nil
    })).WithPriority(scheduler.PriorityHigh).Build()

    // 提交任务
    if err := s.Submit(task); err != nil {
        panic(err)
    }

    // 等待结果
    result := task.GetResult()
    fmt.Printf("任务状态: %s, 耗时: %v\n", result.Status, result.Duration)
}
```

### 高级用法

#### 任务配置

```go
task := scheduler.NewTaskBuilder("数据处理", processDataFunc).
    WithDescription("处理用户数据").
    WithPriority(scheduler.PriorityHigh).
    WithTimeout(30 * time.Second).
    WithMaxRetry(3).
    Build()
```

#### 任务分组

```go
group := scheduler.NewTaskGroup("data-group", "数据处理组")
group.SetScheduler(s)

// 添加多个任务到组
for i := 0; i < 10; i++ {
    task := scheduler.NewTaskBuilder(fmt.Sprintf("task-%d", i), taskFunc).Build()
    group.AddTask(task)
}

// 批量提交并等待完成
group.Submit()
results := group.Wait()
fmt.Printf("任务组进度: %.1f%%\n", group.GetProgress())
```

#### 并行和顺序任务

```go
// 并行执行多个子任务
parallelTask := scheduler.NewTaskBuilder("并行任务", scheduler.ParallelTask(
    func() error { return processStep1() },
    func() error { return processStep2() },
    func() error { return processStep3() },
)).Build()

// 顺序执行多个步骤
sequentialTask := scheduler.NewTaskBuilder("顺序任务", scheduler.SequentialTask(
    func() error { return initStep() },
    func() error { return processStep() },
    func() error { return cleanupStep() },
)).Build()
```

#### 监控和回调

```go
// 设置任务生命周期回调
s.SetOnTaskStart(func(task *scheduler.Task) {
    fmt.Printf("任务开始: %s\n", task.Name)
})

s.SetOnTaskFinish(func(task *scheduler.Task, result *scheduler.TaskResult) {
    fmt.Printf("任务完成: %s, 状态: %s\n", task.Name, result.Status)
})

s.SetOnTaskError(func(task *scheduler.Task, err error) {
    fmt.Printf("任务错误: %s, 错误: %v\n", task.Name, err)
})

// 获取统计信息
stats := s.GetStats()
fmt.Printf("总任务: %d, 运行中: %d, 完成: %d\n", 
    stats.TotalTasks, stats.RunningTasks, stats.CompletedTasks)
```

## 配置选项

```go
config := &scheduler.SchedulerConfig{
    PoolSize:        20,                    // 协程池大小
    QueueSize:       1000,                  // 任务队列大小
    Nonblocking:     false,                 // 非阻塞模式
    PreAlloc:        true,                  // 预分配协程
    EnableMonitor:   true,                  // 启用监控
    MonitorInterval: 10 * time.Second,      // 监控间隔
}

scheduler, err := scheduler.NewScheduler(config)
```

## 任务类型

### 基本任务类型

- **SimpleTask**: 简单的函数包装
- **DelayTask**: 延时执行任务
- **RetryTask**: 带重试逻辑的任务
- **ParallelTask**: 并行执行多个子任务
- **SequentialTask**: 顺序执行多个步骤

### 任务状态

- `TaskStatusPending`: 等待执行
- `TaskStatusRunning`: 正在执行
- `TaskStatusSuccess`: 执行成功
- `TaskStatusFailed`: 执行失败
- `TaskStatusCanceled`: 已取消

### 任务优先级

- `PriorityUrgent`: 紧急
- `PriorityHigh`: 高优先级
- `PriorityNormal`: 普通优先级
- `PriorityLow`: 低优先级

## 最佳实践

1. **合理设置协程池大小**: 根据CPU核心数和IO密集程度调整
2. **使用任务分组**: 对相关任务进行分组管理
3. **设置合适的超时**: 避免任务长时间阻塞
4. **错误处理**: 实现适当的错误处理和重试逻辑
5. **监控统计**: 定期检查调度器统计信息
6. **优雅关闭**: 确保在程序退出时正确关闭调度器

## 性能特点

- 使用 ants 协程池，内存使用更高效
- 支持动态调整协程池大小
- 提供详细的性能统计信息
- 支持高并发任务提交和执行

## 示例

查看 `example.go` 文件中的完整示例代码，包括：

- 基本任务执行示例
- 数据库任务示例  
- 并发处理示例
- 各种任务类型使用示例

## 测试

运行测试：

```bash
go test -v ./pkg/scheduler/
```

运行基准测试：

```bash
go test -bench=. ./pkg/scheduler/
```
