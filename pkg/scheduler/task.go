package scheduler

import (
	"context"
	"time"
)

// TaskStatus 任务状态
type TaskStatus int

const (
	TaskStatusPending  TaskStatus = iota // 等待中
	TaskStatusRunning                    // 运行中
	TaskStatusSuccess                    // 成功
	TaskStatusFailed                     // 失败
	TaskStatusCanceled                   // 已取消
)

func (s TaskStatus) String() string {
	switch s {
	case TaskStatusPending:
		return "pending"
	case TaskStatusRunning:
		return "running"
	case TaskStatusSuccess:
		return "success"
	case TaskStatusFailed:
		return "failed"
	case TaskStatusCanceled:
		return "canceled"
	default:
		return "unknown"
	}
}

// TaskPriority 任务优先级
type TaskPriority int

const (
	PriorityLow TaskPriority = iota
	PriorityNormal
	PriorityHigh
	PriorityUrgent
)

func (p TaskPriority) String() string {
	switch p {
	case PriorityLow:
		return "low"
	case PriorityNormal:
		return "normal"
	case PriorityHigh:
		return "high"
	case PriorityUrgent:
		return "urgent"
	default:
		return "unknown"
	}
}

// TaskFunc 任务执行函数类型
type TaskFunc func(ctx context.Context) error

// TaskResult 任务执行结果
type TaskResult struct {
	TaskID    string        `json:"task_id"`
	Status    TaskStatus    `json:"status"`
	Error     error         `json:"error,omitempty"`
	StartTime time.Time     `json:"start_time"`
	EndTime   time.Time     `json:"end_time"`
	Duration  time.Duration `json:"duration"`
}

// Task 任务定义
type Task struct {
	ID          string        `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Priority    TaskPriority  `json:"priority"`
	Timeout     time.Duration `json:"timeout"`
	Retry       int           `json:"retry"`
	MaxRetry    int           `json:"max_retry"`
	Fn          TaskFunc      `json:"-"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
	Status      TaskStatus    `json:"status"`
	Error       error         `json:"error,omitempty"`

	// 内部字段
	ctx    context.Context
	cancel context.CancelFunc
	result chan *TaskResult
}

// NewTask 创建新任务
func NewTask(id, name string, fn TaskFunc) *Task {
	now := time.Now()
	ctx, cancel := context.WithCancel(context.Background())
	return &Task{
		ID:        id,
		Name:      name,
		Priority:  PriorityNormal,
		Timeout:   30 * time.Minute, // 默认30分钟超时
		MaxRetry:  3,                // 默认最大重试3次
		Fn:        fn,
		CreatedAt: now,
		UpdatedAt: now,
		Status:    TaskStatusPending,
		ctx:       ctx,
		cancel:    cancel,
		result:    make(chan *TaskResult, 1),
	}
}

// SetPriority 设置任务优先级
func (t *Task) SetPriority(priority TaskPriority) *Task {
	t.Priority = priority
	t.UpdatedAt = time.Now()
	return t
}

// SetTimeout 设置任务超时时间
func (t *Task) SetTimeout(timeout time.Duration) *Task {
	t.Timeout = timeout
	t.UpdatedAt = time.Now()
	return t
}

// SetMaxRetry 设置最大重试次数
func (t *Task) SetMaxRetry(maxRetry int) *Task {
	t.MaxRetry = maxRetry
	t.UpdatedAt = time.Now()
	return t
}

// SetDescription 设置任务描述
func (t *Task) SetDescription(description string) *Task {
	t.Description = description
	t.UpdatedAt = time.Now()
	return t
}

// Cancel 取消任务
func (t *Task) Cancel() {
	if t.cancel != nil {
		t.cancel()
	}
	t.Status = TaskStatusCanceled
	t.UpdatedAt = time.Now()
}

// GetResult 获取任务执行结果（阻塞等待）
func (t *Task) GetResult() *TaskResult {
	return <-t.result
}

// GetResultWithTimeout 获取任务执行结果（带超时）
func (t *Task) GetResultWithTimeout(timeout time.Duration) (*TaskResult, bool) {
	select {
	case result := <-t.result:
		return result, true
	case <-time.After(timeout):
		return nil, false
	}
}

// IsCompleted 检查任务是否已完成
func (t *Task) IsCompleted() bool {
	return t.Status == TaskStatusSuccess || t.Status == TaskStatusFailed || t.Status == TaskStatusCanceled
}

// CanRetry 检查任务是否可以重试
func (t *Task) CanRetry() bool {
	return t.Status == TaskStatusFailed && t.Retry < t.MaxRetry
}
