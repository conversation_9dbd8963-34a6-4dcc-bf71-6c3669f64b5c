// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pkg/scheduler/proto/scheduler.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 多版本快照容器
type SnapshotArchive struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Entries       []*SnapshotEntry       `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
	Metadata      *ArchiveMetadata       `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SnapshotArchive) Reset() {
	*x = SnapshotArchive{}
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SnapshotArchive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotArchive) ProtoMessage() {}

func (x *SnapshotArchive) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotArchive.ProtoReflect.Descriptor instead.
func (*SnapshotArchive) Descriptor() ([]byte, []int) {
	return file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP(), []int{0}
}

func (x *SnapshotArchive) GetEntries() []*SnapshotEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *SnapshotArchive) GetMetadata() *ArchiveMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type SnapshotEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     int64                  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	SnapshotData  []byte                 `protobuf:"bytes,2,opt,name=snapshot_data,json=snapshotData,proto3" json:"snapshot_data,omitempty"` // 压缩的SchedulerSnapshot数据
	Checksum      string                 `protobuf:"bytes,3,opt,name=checksum,proto3" json:"checksum,omitempty"`
	SizeBytes     int32                  `protobuf:"varint,4,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SnapshotEntry) Reset() {
	*x = SnapshotEntry{}
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SnapshotEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapshotEntry) ProtoMessage() {}

func (x *SnapshotEntry) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapshotEntry.ProtoReflect.Descriptor instead.
func (*SnapshotEntry) Descriptor() ([]byte, []int) {
	return file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP(), []int{1}
}

func (x *SnapshotEntry) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SnapshotEntry) GetSnapshotData() []byte {
	if x != nil {
		return x.SnapshotData
	}
	return nil
}

func (x *SnapshotEntry) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *SnapshotEntry) GetSizeBytes() int32 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

type ArchiveMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatedAt     int64                  `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	LastUpdated   int64                  `protobuf:"varint,2,opt,name=last_updated,json=lastUpdated,proto3" json:"last_updated,omitempty"`
	EntryCount    int32                  `protobuf:"varint,3,opt,name=entry_count,json=entryCount,proto3" json:"entry_count,omitempty"`
	TotalSize     int64                  `protobuf:"varint,4,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
	Version       string                 `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArchiveMetadata) Reset() {
	*x = ArchiveMetadata{}
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveMetadata) ProtoMessage() {}

func (x *ArchiveMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveMetadata.ProtoReflect.Descriptor instead.
func (*ArchiveMetadata) Descriptor() ([]byte, []int) {
	return file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP(), []int{2}
}

func (x *ArchiveMetadata) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ArchiveMetadata) GetLastUpdated() int64 {
	if x != nil {
		return x.LastUpdated
	}
	return 0
}

func (x *ArchiveMetadata) GetEntryCount() int32 {
	if x != nil {
		return x.EntryCount
	}
	return 0
}

func (x *ArchiveMetadata) GetTotalSize() int64 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

func (x *ArchiveMetadata) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type SchedulerSnapshot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tasks         []*Task                `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
	Stats         *Stats                 `protobuf:"bytes,2,opt,name=stats,proto3" json:"stats,omitempty"`
	Timestamp     int64                  `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchedulerSnapshot) Reset() {
	*x = SchedulerSnapshot{}
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchedulerSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulerSnapshot) ProtoMessage() {}

func (x *SchedulerSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulerSnapshot.ProtoReflect.Descriptor instead.
func (*SchedulerSnapshot) Descriptor() ([]byte, []int) {
	return file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP(), []int{3}
}

func (x *SchedulerSnapshot) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *SchedulerSnapshot) GetStats() *Stats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *SchedulerSnapshot) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *SchedulerSnapshot) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type Task struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Priority      int32                  `protobuf:"varint,4,opt,name=priority,proto3" json:"priority,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	TimeoutNs     int64                  `protobuf:"varint,6,opt,name=timeout_ns,json=timeoutNs,proto3" json:"timeout_ns,omitempty"` // 纳秒
	RetryCount    int32                  `protobuf:"varint,7,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	MaxRetries    int32                  `protobuf:"varint,8,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	StartedAt     int64                  `protobuf:"varint,11,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	CompletedAt   int64                  `protobuf:"varint,12,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,13,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP(), []int{4}
}

func (x *Task) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Task) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Task) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Task) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *Task) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Task) GetTimeoutNs() int64 {
	if x != nil {
		return x.TimeoutNs
	}
	return 0
}

func (x *Task) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *Task) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *Task) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Task) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *Task) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *Task) GetCompletedAt() int64 {
	if x != nil {
		return x.CompletedAt
	}
	return 0
}

func (x *Task) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type Stats struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TotalTasks     int64                  `protobuf:"varint,1,opt,name=total_tasks,json=totalTasks,proto3" json:"total_tasks,omitempty"`
	PendingTasks   int64                  `protobuf:"varint,2,opt,name=pending_tasks,json=pendingTasks,proto3" json:"pending_tasks,omitempty"`
	RunningTasks   int64                  `protobuf:"varint,3,opt,name=running_tasks,json=runningTasks,proto3" json:"running_tasks,omitempty"`
	CompletedTasks int64                  `protobuf:"varint,4,opt,name=completed_tasks,json=completedTasks,proto3" json:"completed_tasks,omitempty"`
	FailedTasks    int64                  `protobuf:"varint,5,opt,name=failed_tasks,json=failedTasks,proto3" json:"failed_tasks,omitempty"`
	CanceledTasks  int64                  `protobuf:"varint,6,opt,name=canceled_tasks,json=canceledTasks,proto3" json:"canceled_tasks,omitempty"`
	PoolRunning    int32                  `protobuf:"varint,7,opt,name=pool_running,json=poolRunning,proto3" json:"pool_running,omitempty"`
	PoolIdle       int32                  `protobuf:"varint,8,opt,name=pool_idle,json=poolIdle,proto3" json:"pool_idle,omitempty"`
	PoolCapacity   int32                  `protobuf:"varint,9,opt,name=pool_capacity,json=poolCapacity,proto3" json:"pool_capacity,omitempty"`
	StartTime      int64                  `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	LastUpdateTime int64                  `protobuf:"varint,11,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Stats) Reset() {
	*x = Stats{}
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Stats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stats) ProtoMessage() {}

func (x *Stats) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_scheduler_proto_scheduler_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stats.ProtoReflect.Descriptor instead.
func (*Stats) Descriptor() ([]byte, []int) {
	return file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP(), []int{5}
}

func (x *Stats) GetTotalTasks() int64 {
	if x != nil {
		return x.TotalTasks
	}
	return 0
}

func (x *Stats) GetPendingTasks() int64 {
	if x != nil {
		return x.PendingTasks
	}
	return 0
}

func (x *Stats) GetRunningTasks() int64 {
	if x != nil {
		return x.RunningTasks
	}
	return 0
}

func (x *Stats) GetCompletedTasks() int64 {
	if x != nil {
		return x.CompletedTasks
	}
	return 0
}

func (x *Stats) GetFailedTasks() int64 {
	if x != nil {
		return x.FailedTasks
	}
	return 0
}

func (x *Stats) GetCanceledTasks() int64 {
	if x != nil {
		return x.CanceledTasks
	}
	return 0
}

func (x *Stats) GetPoolRunning() int32 {
	if x != nil {
		return x.PoolRunning
	}
	return 0
}

func (x *Stats) GetPoolIdle() int32 {
	if x != nil {
		return x.PoolIdle
	}
	return 0
}

func (x *Stats) GetPoolCapacity() int32 {
	if x != nil {
		return x.PoolCapacity
	}
	return 0
}

func (x *Stats) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Stats) GetLastUpdateTime() int64 {
	if x != nil {
		return x.LastUpdateTime
	}
	return 0
}

var File_pkg_scheduler_proto_scheduler_proto protoreflect.FileDescriptor

const file_pkg_scheduler_proto_scheduler_proto_rawDesc = "" +
	"\n" +
	"#pkg/scheduler/proto/scheduler.proto\x12\tscheduler\"}\n" +
	"\x0fSnapshotArchive\x122\n" +
	"\aentries\x18\x01 \x03(\v2\x18.scheduler.SnapshotEntryR\aentries\x126\n" +
	"\bmetadata\x18\x02 \x01(\v2\x1a.scheduler.ArchiveMetadataR\bmetadata\"\x8d\x01\n" +
	"\rSnapshotEntry\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp\x12#\n" +
	"\rsnapshot_data\x18\x02 \x01(\fR\fsnapshotData\x12\x1a\n" +
	"\bchecksum\x18\x03 \x01(\tR\bchecksum\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\x04 \x01(\x05R\tsizeBytes\"\xad\x01\n" +
	"\x0fArchiveMetadata\x12\x1d\n" +
	"\n" +
	"created_at\x18\x01 \x01(\x03R\tcreatedAt\x12!\n" +
	"\flast_updated\x18\x02 \x01(\x03R\vlastUpdated\x12\x1f\n" +
	"\ventry_count\x18\x03 \x01(\x05R\n" +
	"entryCount\x12\x1d\n" +
	"\n" +
	"total_size\x18\x04 \x01(\x03R\ttotalSize\x12\x18\n" +
	"\aversion\x18\x05 \x01(\tR\aversion\"\x9a\x01\n" +
	"\x11SchedulerSnapshot\x12%\n" +
	"\x05tasks\x18\x01 \x03(\v2\x0f.scheduler.TaskR\x05tasks\x12&\n" +
	"\x05stats\x18\x02 \x01(\v2\x10.scheduler.StatsR\x05stats\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\x03R\ttimestamp\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\"\x86\x03\n" +
	"\x04Task\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\bpriority\x18\x04 \x01(\x05R\bpriority\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"timeout_ns\x18\x06 \x01(\x03R\ttimeoutNs\x12\x1f\n" +
	"\vretry_count\x18\a \x01(\x05R\n" +
	"retryCount\x12\x1f\n" +
	"\vmax_retries\x18\b \x01(\x05R\n" +
	"maxRetries\x12\x1d\n" +
	"\n" +
	"created_at\x18\t \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\x03R\tupdatedAt\x12\x1d\n" +
	"\n" +
	"started_at\x18\v \x01(\x03R\tstartedAt\x12!\n" +
	"\fcompleted_at\x18\f \x01(\x03R\vcompletedAt\x12#\n" +
	"\rerror_message\x18\r \x01(\tR\ferrorMessage\"\x93\x03\n" +
	"\x05Stats\x12\x1f\n" +
	"\vtotal_tasks\x18\x01 \x01(\x03R\n" +
	"totalTasks\x12#\n" +
	"\rpending_tasks\x18\x02 \x01(\x03R\fpendingTasks\x12#\n" +
	"\rrunning_tasks\x18\x03 \x01(\x03R\frunningTasks\x12'\n" +
	"\x0fcompleted_tasks\x18\x04 \x01(\x03R\x0ecompletedTasks\x12!\n" +
	"\ffailed_tasks\x18\x05 \x01(\x03R\vfailedTasks\x12%\n" +
	"\x0ecanceled_tasks\x18\x06 \x01(\x03R\rcanceledTasks\x12!\n" +
	"\fpool_running\x18\a \x01(\x05R\vpoolRunning\x12\x1b\n" +
	"\tpool_idle\x18\b \x01(\x05R\bpoolIdle\x12#\n" +
	"\rpool_capacity\x18\t \x01(\x05R\fpoolCapacity\x12\x1d\n" +
	"\n" +
	"start_time\x18\n" +
	" \x01(\x03R\tstartTime\x12(\n" +
	"\x10last_update_time\x18\v \x01(\x03R\x0elastUpdateTimeB\x19Z\x17etl/pkg/scheduler/protob\x06proto3"

var (
	file_pkg_scheduler_proto_scheduler_proto_rawDescOnce sync.Once
	file_pkg_scheduler_proto_scheduler_proto_rawDescData []byte
)

func file_pkg_scheduler_proto_scheduler_proto_rawDescGZIP() []byte {
	file_pkg_scheduler_proto_scheduler_proto_rawDescOnce.Do(func() {
		file_pkg_scheduler_proto_scheduler_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_scheduler_proto_scheduler_proto_rawDesc), len(file_pkg_scheduler_proto_scheduler_proto_rawDesc)))
	})
	return file_pkg_scheduler_proto_scheduler_proto_rawDescData
}

var file_pkg_scheduler_proto_scheduler_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pkg_scheduler_proto_scheduler_proto_goTypes = []any{
	(*SnapshotArchive)(nil),   // 0: scheduler.SnapshotArchive
	(*SnapshotEntry)(nil),     // 1: scheduler.SnapshotEntry
	(*ArchiveMetadata)(nil),   // 2: scheduler.ArchiveMetadata
	(*SchedulerSnapshot)(nil), // 3: scheduler.SchedulerSnapshot
	(*Task)(nil),              // 4: scheduler.Task
	(*Stats)(nil),             // 5: scheduler.Stats
}
var file_pkg_scheduler_proto_scheduler_proto_depIdxs = []int32{
	1, // 0: scheduler.SnapshotArchive.entries:type_name -> scheduler.SnapshotEntry
	2, // 1: scheduler.SnapshotArchive.metadata:type_name -> scheduler.ArchiveMetadata
	4, // 2: scheduler.SchedulerSnapshot.tasks:type_name -> scheduler.Task
	5, // 3: scheduler.SchedulerSnapshot.stats:type_name -> scheduler.Stats
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pkg_scheduler_proto_scheduler_proto_init() }
func file_pkg_scheduler_proto_scheduler_proto_init() {
	if File_pkg_scheduler_proto_scheduler_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_scheduler_proto_scheduler_proto_rawDesc), len(file_pkg_scheduler_proto_scheduler_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pkg_scheduler_proto_scheduler_proto_goTypes,
		DependencyIndexes: file_pkg_scheduler_proto_scheduler_proto_depIdxs,
		MessageInfos:      file_pkg_scheduler_proto_scheduler_proto_msgTypes,
	}.Build()
	File_pkg_scheduler_proto_scheduler_proto = out.File
	file_pkg_scheduler_proto_scheduler_proto_goTypes = nil
	file_pkg_scheduler_proto_scheduler_proto_depIdxs = nil
}
