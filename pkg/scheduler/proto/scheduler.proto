syntax = "proto3";

package scheduler;
option go_package = "etl/pkg/scheduler/proto";

// 多版本快照容器
message SnapshotArchive {
  repeated SnapshotEntry entries = 1;
  ArchiveMetadata metadata = 2;
}

message SnapshotEntry {
  int64 timestamp = 1;
  bytes snapshot_data = 2;  // 压缩的SchedulerSnapshot数据
  string checksum = 3;
  int32 size_bytes = 4;
}

message ArchiveMetadata {
  int64 created_at = 1;
  int64 last_updated = 2;
  int32 entry_count = 3;
  int64 total_size = 4;
  string version = 5;
}

message SchedulerSnapshot {
  repeated Task tasks = 1;
  Stats stats = 2;
  int64 timestamp = 3;
  string version = 4;
}

message Task {
  string id = 1;
  string name = 2;
  string description = 3;
  int32 priority = 4;
  int32 status = 5;
  int64 timeout_ns = 6;  // 纳秒
  int32 retry_count = 7;
  int32 max_retries = 8;
  int64 created_at = 9;
  int64 updated_at = 10;
  int64 started_at = 11;
  int64 completed_at = 12;
  string error_message = 13;
}

message Stats {
  int64 total_tasks = 1;
  int64 pending_tasks = 2;
  int64 running_tasks = 3;
  int64 completed_tasks = 4;
  int64 failed_tasks = 5;
  int64 canceled_tasks = 6;
  int32 pool_running = 7;
  int32 pool_idle = 8;
  int32 pool_capacity = 9;
  int64 start_time = 10;
  int64 last_update_time = 11;
}
