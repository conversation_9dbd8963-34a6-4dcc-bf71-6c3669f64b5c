// Package scheduler 增强持久化集成
package scheduler

import (
	"context"
	"fmt"
	"time"

	"admin/pkg/scheduler/converter"
	"admin/pkg/scheduler/persistence"

	"go.uber.org/zap"
)

// 实现 persistence.SnapshotProvider 接口

// CreateSnapshot 创建快照 (实现 SnapshotProvider 接口)
func (s *Scheduler) CreateSnapshot() *converter.SchedulerSnapshot {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 转换任务
	taskInfos := make([]*converter.TaskInfo, 0, len(s.tasks))
	for _, task := range s.tasks {
		// 只保存未完成的任务
		if !task.IsCompleted() {
			taskInfo := &converter.TaskInfo{
				ID:          task.ID,
				Name:        task.Name,
				Description: task.Description,
				Priority:    int32(task.Priority),
				Status:      int32(task.Status),
				Timeout:     task.Timeout,
				RetryCount:  task.Retry,
				MaxRetries:  task.MaxRetry,
				CreatedAt:   task.CreatedAt,
				UpdatedAt:   task.UpdatedAt,
			}

			if task.Error != nil {
				taskInfo.ErrorMsg = task.Error.Error()
			}

			taskInfos = append(taskInfos, taskInfo)
		}
	}

	// 转换统计信息
	statsInfo := &converter.StatsInfo{
		TotalTasks:     s.stats.TotalTasks,
		PendingTasks:   s.stats.PendingTasks,
		RunningTasks:   s.stats.RunningTasks,
		CompletedTasks: s.stats.CompletedTasks,
		FailedTasks:    s.stats.FailedTasks,
		CanceledTasks:  s.stats.CanceledTasks,
		PoolRunning:    s.stats.PoolRunning,
		PoolIdle:       s.stats.PoolIdle,
		PoolCapacity:   s.stats.PoolCapacity,
		StartTime:      s.stats.StartTime,
		LastUpdateTime: s.stats.LastUpdateTime,
	}

	return &converter.SchedulerSnapshot{
		Tasks:     taskInfos,
		Stats:     statsInfo,
		Timestamp: time.Now(),
		Version:   "1.0",
	}
}

// RestoreFromSnapshot 从快照恢复 (实现 SnapshotProvider 接口)
func (s *Scheduler) RestoreFromSnapshot(snapshot *converter.SchedulerSnapshot) error {
	if snapshot == nil {
		return fmt.Errorf("快照为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	restoredCount := 0
	skippedCount := 0

	for _, taskInfo := range snapshot.Tasks {
		// 检查任务是否已存在
		if _, exists := s.tasks[taskInfo.ID]; exists {
			skippedCount++
			continue
		}

		// 创建新任务
		task := &Task{
			ID:          taskInfo.ID,
			Name:        taskInfo.Name,
			Description: taskInfo.Description,
			Priority:    TaskPriority(taskInfo.Priority),
			Status:      TaskStatus(taskInfo.Status),
			Timeout:     taskInfo.Timeout,
			Retry:       taskInfo.RetryCount,
			MaxRetry:    taskInfo.MaxRetries,
			CreatedAt:   taskInfo.CreatedAt,
			UpdatedAt:   taskInfo.UpdatedAt,
			result:      make(chan *TaskResult, 1),
		}

		// 创建占位执行函数
		task.Fn = func(ctx context.Context) error {
			s.logger.Warn("执行恢复的任务但未找到对应的执行函数",
				zap.String("task_id", task.ID),
				zap.String("task_name", task.Name))
			return fmt.Errorf("恢复的任务 %s (%s) 缺少执行函数", task.Name, task.ID)
		}

		// 添加到任务映射
		s.tasks[task.ID] = task

		// 更新统计信息
		s.updateStats(func(stats *Stats) {
			stats.TotalTasks++
			if task.Status == TaskStatusPending {
				stats.PendingTasks++
			}
		})

		restoredCount++
		s.logger.Debug("从快照恢复任务",
			zap.String("task_id", task.ID),
			zap.String("task_name", task.Name))
	}

	s.logger.Info("快照恢复完成",
		zap.Int("restored_tasks", restoredCount),
		zap.Int("skipped_tasks", skippedCount),
		zap.Time("snapshot_time", snapshot.Timestamp))

	return nil
}

// EnableEnhancedPersistence 启用增强持久化功能
func (s *Scheduler) EnableEnhancedPersistence(config *persistence.PersistenceConfig) error {
	if s.enhancedPersistence != nil {
		return fmt.Errorf("增强持久化已启用")
	}

	// 创建增强持久化管理器，Scheduler直接实现SnapshotProvider接口
	enhancedPM := persistence.NewEnhancedPersistenceManager(
		config,
		s, // 直接传入scheduler，它实现了SnapshotProvider接口
		s.logger,
	)

	s.enhancedPersistence = enhancedPM

	s.logger.Info("增强持久化已启用",
		zap.String("data_path", config.DataPath),
		zap.Duration("save_interval", config.SaveInterval))

	return nil
}

// GetEnhancedPersistenceStats 获取增强持久化统计信息
func (s *Scheduler) GetEnhancedPersistenceStats() map[string]interface{} {
	if s.enhancedPersistence == nil {
		return map[string]interface{}{"enabled": false}
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		return epm.GetStats()
	}

	return map[string]interface{}{"enabled": true, "error": "type_conversion_failed"}
}

// LoadSnapshotAt 加载特定时间的快照
func (s *Scheduler) LoadSnapshotAt(timestamp time.Time) error {
	if s.enhancedPersistence == nil {
		return fmt.Errorf("增强持久化未启用")
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		return epm.LoadSnapshotAt(timestamp)
	}

	return fmt.Errorf("增强持久化类型转换失败")
}

// ListSnapshots 列出所有可用快照
func (s *Scheduler) ListSnapshots() []time.Time {
	if s.enhancedPersistence == nil {
		return nil
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		return epm.ListSnapshots()
	}

	return nil
}

// CleanOldArchives 清理旧档案
func (s *Scheduler) CleanOldArchives(maxAge time.Duration) error {
	if s.enhancedPersistence == nil {
		return fmt.Errorf("增强持久化未启用")
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		return epm.CleanOldArchives(maxAge)
	}

	return fmt.Errorf("增强持久化类型转换失败")
}

// RestoreLatestSnapshot 恢复最新快照
func (s *Scheduler) RestoreLatestSnapshot() error {
	if s.enhancedPersistence == nil {
		return fmt.Errorf("增强持久化未启用")
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		return epm.LoadLatestSnapshot()
	}

	return fmt.Errorf("增强持久化类型转换失败")
}

// SaveSnapshot 手动保存快照
func (s *Scheduler) SaveSnapshot() error {
	if s.enhancedPersistence == nil {
		return fmt.Errorf("增强持久化未启用")
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		return epm.SaveSnapshot()
	}

	return fmt.Errorf("增强持久化类型转换失败")
}

// ShutdownEnhancedPersistence 关闭增强持久化
func (s *Scheduler) ShutdownEnhancedPersistence() error {
	if s.enhancedPersistence == nil {
		return nil
	}

	if epm, ok := s.enhancedPersistence.(*persistence.EnhancedPersistenceManager); ok {
		err := epm.Shutdown()
		s.enhancedPersistence = nil
		return err
	}

	s.enhancedPersistence = nil
	return nil
}
