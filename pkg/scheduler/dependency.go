package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// DependencyConfig 依赖管理配置
type DependencyConfig struct {
	// 启用依赖管理
	Enabled bool
	// 检查间隔
	CheckInterval time.Duration
	// 最大等待时间
	MaxWaitTime time.Duration
	// 是否启用循环依赖检测
	DetectCycles bool
}

// DefaultDependencyConfig 返回默认依赖配置
func DefaultDependencyConfig() *DependencyConfig {
	return &DependencyConfig{
		Enabled:       false,
		CheckInterval: 1 * time.Second,
		MaxWaitTime:   10 * time.Minute,
		DetectCycles:  true,
	}
}

// TaskDependency 任务依赖关系
type TaskDependency struct {
	TaskID       string    `json:"task_id"`
	Dependencies []string  `json:"dependencies"`
	CreatedAt    time.Time `json:"created_at"`
	WaitingSince time.Time `json:"waiting_since"`
}

// DependencyManager 依赖管理器
type DependencyManager struct {
	config       *DependencyConfig
	scheduler    *Scheduler
	dependencies map[string]*TaskDependency
	waiting      map[string]*Task
	dag          *DAG // 有向无环图
	mu           sync.RWMutex
	ctx          context.Context
	cancel       context.CancelFunc
	wg           sync.WaitGroup
}

// NewDependencyManager 创建依赖管理器
func NewDependencyManager(scheduler *Scheduler, config *DependencyConfig) *DependencyManager {
	if config == nil {
		config = DefaultDependencyConfig()
	} else {
		// 确保关键字段有默认值
		if config.CheckInterval <= 0 {
			config.CheckInterval = 1 * time.Second
		}
		if config.MaxWaitTime <= 0 {
			config.MaxWaitTime = 10 * time.Minute
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	dm := &DependencyManager{
		config:       config,
		scheduler:    scheduler,
		dependencies: make(map[string]*TaskDependency),
		waiting:      make(map[string]*Task),
		dag:          NewDAG(),
		ctx:          ctx,
		cancel:       cancel,
	}

	if config.Enabled {
		// 启动依赖检查器
		dm.wg.Add(1)
		go dm.dependencyChecker()
	}

	return dm
}

// SubmitTaskWithDependencies 提交带依赖的任务
func (dm *DependencyManager) SubmitTaskWithDependencies(task *Task, dependencies []string) error {
	if !dm.config.Enabled {
		return fmt.Errorf("依赖管理器未启用")
	}

	dm.mu.Lock()
	defer dm.mu.Unlock()

	// 将任务添加到DAG中
	if err := dm.dag.AddNode(task.ID, task.Name, task); err != nil {
		return fmt.Errorf("添加任务到DAG失败: %w", err)
	}

	// 添加依赖关系到DAG
	for _, depIDorName := range dependencies {
		// 先检查依赖任务是否存在于DAG中
		depTask := dm.resolveDependencyTask(depIDorName)
		if depTask == nil {
			// 依赖任务不存在，可能还未提交，创建一个占位节点
			placeholderTask := &Task{
				ID:     depIDorName,
				Name:   depIDorName,
				Status: TaskStatusPending,
			}
			if err := dm.dag.AddNode(depIDorName, depIDorName, placeholderTask); err != nil {
				// 如果添加失败，可能节点已存在，忽略错误
				dm.scheduler.logger.Debug("占位节点已存在于DAG中",
					zap.String("node_id", depIDorName),
					zap.Error(err))
				// 这里不返回错误，因为我们允许占位节点存在
			}

			// 添加依赖关系 (task 依赖 depIDorName)
			if err := dm.dag.AddDependency(task.ID, depIDorName); err != nil {
				// 如果添加依赖失败（比如会形成循环），移除已添加的任务节点并返回错误
				if err := dm.dag.RemoveNode(task.ID); err != nil {
					dm.scheduler.logger.Error("移除任务节点失败",
						zap.String("task_id", task.ID),
						zap.Error(err))
				}
				return fmt.Errorf("添加依赖关系失败: %w", err)
			}
		} else {
			// 依赖任务存在，确保它在DAG中
			// 如果该任务还未添加到DAG中，添加该任务
			if _, exists := dm.dag.GetNode(depTask.ID); !exists {
				if err := dm.dag.AddNode(depTask.ID, depTask.Name, depTask); err != nil {
					// 如果添加失败，可能节点已存在，忽略错误
					dm.scheduler.logger.Debug("节点已存在于DAG中",
						zap.String("node_id", depTask.ID),
						zap.Error(err))
				}
			}

			// 总是更新DAG中节点的状态（无论节点是否已存在）
			var dagStatus DAGNodeStatus
			switch depTask.Status {
			case TaskStatusSuccess:
				dagStatus = DAGNodeStatusCompleted
			case TaskStatusRunning:
				dagStatus = DAGNodeStatusRunning
			case TaskStatusFailed:
				dagStatus = DAGNodeStatusFailed
			default:
				dagStatus = DAGNodeStatusPending
			}
			// 更新依赖任务的状态
			if err := dm.dag.UpdateNodeStatus(depTask.ID, dagStatus); err != nil {
				dm.scheduler.logger.Error("更新DAG节点状态失败",
					zap.String("task_id", depTask.ID),
					zap.Error(err))
			}

			// 添加依赖关系 (task 依赖 depTask.ID)
			// 注意：这里使用的是依赖任务的ID，而不是用户提供的depIDorName
			if err := dm.dag.AddDependency(task.ID, depTask.ID); err != nil {
				// 如果添加依赖失败（比如会形成循环），移除已添加的任务节点并返回错误
				if err := dm.dag.RemoveNode(task.ID); err != nil {
					dm.scheduler.logger.Error("移除任务节点失败",
						zap.String("task_id", task.ID),
						zap.Error(err))
				}

				return fmt.Errorf("添加依赖关系失败: %w", err)
			}
		}
	}

	// 检查依赖任务状态
	for _, depID := range dependencies {
		depTask := dm.resolveDependencyTask(depID)
		if depTask != nil && depTask.IsCompleted() && depTask.Status != TaskStatusSuccess {
			// 依赖任务已完成但不是成功状态
			if err := dm.dag.RemoveNode(task.ID); err != nil {
				dm.scheduler.logger.Error("移除任务节点失败",
					zap.String("task_id", task.ID),
					zap.Error(err))
			}
			return fmt.Errorf("依赖任务 %s 执行失败，状态: %s", depID, depTask.Status)
		}
	}

	// 创建依赖关系
	dependency := &TaskDependency{
		TaskID:       task.ID,
		Dependencies: dependencies,
		CreatedAt:    time.Now(),
		WaitingSince: time.Now(),
	}

	dm.dependencies[task.ID] = dependency

	// 检查依赖是否已满足
	if dm.areDependenciesSatisfied(dependencies) {
		// 依赖已满足，直接提交任务
		if err := dm.dag.UpdateNodeStatus(task.ID, DAGNodeStatusReady); err != nil {
			dm.scheduler.logger.Error("更新DAG节点状态失败",
				zap.String("task_id", task.ID),
				zap.Error(err))
			// return fmt.Errorf("更新DAG节点状态失败: %w", err)
		}
		return dm.scheduler.Submit(task)
	} else {
		// 依赖未满足，加入等待队列
		dm.waiting[task.ID] = task
		if err := dm.dag.UpdateNodeStatus(task.ID, DAGNodeStatusPending); err != nil {
			dm.scheduler.logger.Error("更新DAG节点状态失败",
				zap.String("task_id", task.ID),
				zap.Error(err))
			// return fmt.Errorf("更新DAG节点状态失败: %w", err)
		}
		dm.scheduler.logger.Info("任务等待依赖完成",
			zap.String("task_id", task.ID),
			zap.Strings("dependencies", dependencies))
		return nil
	}
}

// GetDependencyGraph 获取依赖图（以任务名称为键）
func (dm *DependencyManager) GetDependencyGraph() map[string][]string {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	// 使用DAG获取依赖图
	return dm.dag.GetDependencyGraph()
}

// GetWaitingTasks 获取等待中的任务
func (dm *DependencyManager) GetWaitingTasks() []string {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var waiting []string
	for taskID := range dm.waiting {
		waiting = append(waiting, taskID)
	}
	return waiting
}

// Shutdown 关闭依赖管理器
func (dm *DependencyManager) Shutdown() {
	if !dm.config.Enabled {
		return
	}

	dm.cancel()
	dm.wg.Wait()
}

// areDependenciesSatisfied 检查依赖是否已满足
func (dm *DependencyManager) areDependenciesSatisfied(dependencies []string) bool {
	for _, depID := range dependencies {
		// 首先尝试按任务ID查找
		task, exists := dm.scheduler.GetTask(depID)
		if !exists {
			// 如果按ID找不到，尝试按任务名称查找
			task, exists = dm.scheduler.GetTaskByName(depID)
		}
		if !exists || task.Status != TaskStatusSuccess {
			return false
		}
	}
	return true
}

// dependencyChecker 依赖检查器
func (dm *DependencyManager) dependencyChecker() {
	defer dm.wg.Done()

	ticker := time.NewTicker(dm.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			dm.checkAndSubmitReadyTasks()
		case <-dm.ctx.Done():
			dm.scheduler.logger.Info("依赖检查器已停止")
			return
		}
	}
}

// checkAndSubmitReadyTasks 检查并提交就绪的任务
func (dm *DependencyManager) checkAndSubmitReadyTasks() {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	var readyTasks []string

	// 检查所有等待中的任务，而不仅仅是DAG中就绪的任务
	for taskID := range dm.waiting {
		dependency, exists := dm.dependencies[taskID]
		if !exists {
			continue
		}

		// 检查是否超时
		if time.Since(dependency.WaitingSince) > dm.config.MaxWaitTime {
			dm.scheduler.logger.Warn("任务等待依赖超时",
				zap.String("task_id", taskID),
				zap.Duration("waited", time.Since(dependency.WaitingSince)))
			readyTasks = append(readyTasks, taskID)
			continue
		}

		// 检查依赖是否满足
		if dm.areDependenciesSatisfied(dependency.Dependencies) {
			readyTasks = append(readyTasks, taskID)
		}
	}

	// 提交就绪的任务
	for _, taskID := range readyTasks {
		task := dm.waiting[taskID]
		delete(dm.waiting, taskID)

		// 更新DAG状态
		if err := dm.dag.UpdateNodeStatus(taskID, DAGNodeStatusReady); err != nil {
			dm.scheduler.logger.Error("更新DAG节点状态失败",
				zap.String("task_id", taskID),
				zap.Error(err))
			continue
		}

		if err := dm.scheduler.Submit(task); err != nil {
			dm.scheduler.logger.Error("提交就绪任务失败",
				zap.String("task_id", taskID),
				zap.Error(err))
		} else {
			dm.scheduler.logger.Info("依赖任务已就绪并提交",
				zap.String("task_id", taskID))
		}
	}
}

// CleanCompletedDependencies 清理已完成任务的依赖关系
func (dm *DependencyManager) CleanCompletedDependencies() {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	var toDelete []string
	for taskID := range dm.dependencies {
		if task, exists := dm.scheduler.GetTask(taskID); exists && task.IsCompleted() {
			toDelete = append(toDelete, taskID)
			// 更新DAG状态
			var dagStatus DAGNodeStatus
			switch task.Status {
			case TaskStatusSuccess:
				dagStatus = DAGNodeStatusCompleted
			case TaskStatusFailed:
				dagStatus = DAGNodeStatusFailed
			case TaskStatusCanceled:
				dagStatus = DAGNodeStatusSkipped
			default:
				dagStatus = DAGNodeStatusCompleted
			}
			// 更新DAG节点状态
			if err := dm.dag.UpdateNodeStatus(taskID, dagStatus); err != nil {
				dm.scheduler.logger.Error("更新DAG节点状态失败",
					zap.String("task_id", taskID),
					zap.Error(err))
			}
		}
	}

	for _, taskID := range toDelete {
		delete(dm.dependencies, taskID)
	}

	if len(toDelete) > 0 {
		dm.scheduler.logger.Debug("清理已完成任务的依赖关系", zap.Int("count", len(toDelete)))
	}
}

// GetTopologicalOrder 获取任务的拓扑排序
func (dm *DependencyManager) GetTopologicalOrder() ([]string, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()
	return dm.dag.GetTopologicalOrder()
}

// GetWaitingTasksFromDAG 从DAG获取等待中的任务
func (dm *DependencyManager) GetWaitingTasksFromDAG() []string {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var waiting []string
	nodes := dm.dag.GetAllNodes()
	for nodeID, node := range nodes {
		if node.Status == DAGNodeStatusPending {
			waiting = append(waiting, nodeID)
		}
	}
	return waiting
}

// UpdateTaskStatus 更新任务状态（同时更新DAG）
func (dm *DependencyManager) UpdateTaskStatus(taskID string, status TaskStatus) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	var dagStatus DAGNodeStatus
	switch status {
	case TaskStatusPending:
		dagStatus = DAGNodeStatusPending
	case TaskStatusRunning:
		dagStatus = DAGNodeStatusRunning
	case TaskStatusSuccess:
		dagStatus = DAGNodeStatusCompleted
	case TaskStatusFailed:
		dagStatus = DAGNodeStatusFailed
	case TaskStatusCanceled:
		dagStatus = DAGNodeStatusSkipped
	default:
		dagStatus = DAGNodeStatusPending
	}

	return dm.dag.UpdateNodeStatus(taskID, dagStatus)
}

// resolveDependencyTask 解析依赖任务（通过ID或名称）
func (dm *DependencyManager) resolveDependencyTask(idOrName string) *Task {
	// 首先尝试按任务ID查找
	if task, exists := dm.scheduler.GetTask(idOrName); exists {
		return task
	}
	// 如果按ID找不到，尝试按任务名称查找
	if task, exists := dm.scheduler.GetTaskByName(idOrName); exists {
		return task
	}
	return nil
}
