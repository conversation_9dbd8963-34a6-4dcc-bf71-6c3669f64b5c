// Package compression 提供数据压缩和解压缩功能
package compression

import (
	"bytes"
	"compress/gzip"
	"crypto/md5"
	"encoding/hex"
	"io"
	"sync"
)

// CompressorPool 压缩器对象池
type CompressorPool struct {
	pool sync.Pool
}

// NewCompressorPool 创建新的压缩器池
func NewCompressorPool() *CompressorPool {
	return &CompressorPool{
		pool: sync.Pool{
			New: func() interface{} {
				return gzip.NewWriter(nil)
			},
		},
	}
}

// Compress 压缩数据
func (cp *CompressorPool) Compress(data []byte) ([]byte, error) {
	gzWriter := cp.pool.Get().(*gzip.Writer)
	defer cp.pool.Put(gzWriter)
	var buf bytes.Buffer
	gzWriter.Reset(&buf)
	_, err := gzWriter.Write(data)
	if err != nil {
		return nil, err
	}
	err = gzWriter.Close()
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// Decompress 解压缩数据
func (cp *CompressorPool) Decompress(data []byte) ([]byte, error) {
	buf := bytes.NewReader(data)
	gzReader, err := gzip.NewReader(buf)
	if err != nil {
		return nil, err
	}
	defer gzReader.Close()
	return io.ReadAll(gzReader)
}

// CalculateChecksum 计算数据的MD5校验和
func CalculateChecksum(data []byte) string {
	hash := md5.Sum(data)
	return hex.EncodeToString(hash[:])
}

// CompareChecksum 比较两个校验和是否相同
func CompareChecksum(checksum1, checksum2 string) bool {
	return checksum1 == checksum2
}
