package scheduler

import (
	"fmt"
	"sort"
	"sync"
)

// DAGNode 有向无环图节点
type DAGNode struct {
	ID           string            // 节点ID（任务ID）
	Name         string            // 节点名称（任务名称）
	Dependencies []string          // 依赖的节点ID列表
	Dependents   []string          // 依赖此节点的节点ID列表
	Data         any               // 节点携带的数据（通常是Task对象）
	Status       DAGNodeStatus     // 节点状态
	Metadata     map[string]string // 元数据
}

// DAGNodeStatus 节点状态
type DAGNodeStatus int

const (
	DAGNodeStatusPending   DAGNodeStatus = iota // 等待中
	DAGNodeStatusReady                          // 就绪（依赖已满足）
	DAGNodeStatusRunning                        // 运行中
	DAGNodeStatusCompleted                      // 已完成
	DAGNodeStatusFailed                         // 失败
	DAGNodeStatusSkipped                        // 跳过
)

func (s DAGNodeStatus) String() string {
	switch s {
	case DAGNodeStatusPending:
		return "pending"
	case DAGNodeStatusReady:
		return "ready"
	case DAGNodeStatusRunning:
		return "running"
	case DAGNodeStatusCompleted:
		return "completed"
	case DAGNodeStatusFailed:
		return "failed"
	case DAGNodeStatusSkipped:
		return "skipped"
	default:
		return "unknown"
	}
}

// DAG 有向无环图
type DAG struct {
	nodes    map[string]*DAGNode // 节点映射（通过ID索引）
	nameToID map[string]string   // 名称到ID的映射
	mu       sync.RWMutex        // 读写锁
}

// NewDAG 创建新的DAG
func NewDAG() *DAG {
	return &DAG{
		nodes:    make(map[string]*DAGNode),
		nameToID: make(map[string]string),
	}
}

// AddNode 添加节点
func (d *DAG) AddNode(id, name string, data any) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 检查ID是否已存在
	if _, exists := d.nodes[id]; exists {
		return fmt.Errorf("节点ID已存在: %s", id)
	}

	// 检查名称是否已存在
	if existingID, exists := d.nameToID[name]; exists && existingID != id {
		return fmt.Errorf("节点名称已存在: %s (已被节点 %s 使用)", name, existingID)
	}

	node := &DAGNode{
		ID:           id,
		Name:         name,
		Dependencies: make([]string, 0),
		Dependents:   make([]string, 0),
		Data:         data,
		Status:       DAGNodeStatusPending,
		Metadata:     make(map[string]string),
	}

	d.nodes[id] = node
	d.nameToID[name] = id

	return nil
}

// RemoveNode 移除节点
func (d *DAG) RemoveNode(id string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	node, exists := d.nodes[id]
	if !exists {
		return fmt.Errorf("节点不存在: %s", id)
	}

	// 移除所有依赖关系
	for _, depID := range node.Dependencies {
		if depNode, exists := d.nodes[depID]; exists {
			d.removeDependentFromNode(depNode, id)
		}
	}

	for _, depID := range node.Dependents {
		if depNode, exists := d.nodes[depID]; exists {
			d.removeDependencyFromNode(depNode, id)
		}
	}

	// 删除节点
	delete(d.nodes, id)
	delete(d.nameToID, node.Name)

	return nil
}

// AddDependency 添加依赖关系（fromID 依赖 toID）
func (d *DAG) AddDependency(fromID, toID string) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	// 检查节点是否存在
	fromNode, exists := d.nodes[fromID]
	if !exists {
		return fmt.Errorf("源节点不存在: %s", fromID)
	}

	toNode, exists := d.nodes[toID]
	if !exists {
		return fmt.Errorf("目标节点不存在: %s", toID)
	}

	// 检查是否会形成循环
	if d.wouldCreateCycle(fromID, toID) {
		return fmt.Errorf("添加依赖会形成循环: %s -> %s", fromID, toID)
	}

	// 检查依赖是否已存在
	for _, dep := range fromNode.Dependencies {
		if dep == toID {
			return nil // 依赖已存在，直接返回
		}
	}

	// 添加依赖关系
	fromNode.Dependencies = append(fromNode.Dependencies, toID)
	toNode.Dependents = append(toNode.Dependents, fromID)

	return nil
}

// AddDependencyByName 通过名称添加依赖关系
func (d *DAG) AddDependencyByName(fromName, toName string) error {
	d.mu.RLock()
	fromID, fromExists := d.nameToID[fromName]
	toID, toExists := d.nameToID[toName]
	d.mu.RUnlock()

	if !fromExists {
		return fmt.Errorf("源节点名称不存在: %s", fromName)
	}
	if !toExists {
		return fmt.Errorf("目标节点名称不存在: %s", toName)
	}

	return d.AddDependency(fromID, toID)
}

// GetNodeByName 根据名称获取节点
func (d *DAG) GetNodeByName(name string) (*DAGNode, bool) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	id, exists := d.nameToID[name]
	if !exists {
		return nil, false
	}

	node, exists := d.nodes[id]
	return node, exists
}

// wouldCreateCycle 检查添加依赖是否会创建循环（内部方法，调用时已加锁）
func (d *DAG) wouldCreateCycle(fromID, toID string) bool {
	// 检查从 toID 开始，沿着依赖链是否能到达 fromID
	// 如果能到达，说明添加 fromID->toID 的依赖会形成循环
	visited := make(map[string]bool)
	var dfs func(string) bool

	dfs = func(nodeID string) bool {
		if nodeID == fromID {
			return true // 找到了从 toID 到 fromID 的路径，形成循环
		}
		if visited[nodeID] {
			return false
		}
		visited[nodeID] = true

		if node, exists := d.nodes[nodeID]; exists {
			// 沿着依赖链向下走（Dependencies方向）
			for _, depID := range node.Dependencies {
				if dfs(depID) {
					return true
				}
			}
		}
		return false
	}

	return dfs(toID)
}

// HasCycle 检查整个图是否有循环
func (d *DAG) HasCycle() bool {
	d.mu.RLock()
	defer d.mu.RUnlock()

	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	var dfs func(string) bool
	dfs = func(nodeID string) bool {
		visited[nodeID] = true
		recStack[nodeID] = true

		if node, exists := d.nodes[nodeID]; exists {
			for _, depID := range node.Dependencies {
				if !visited[depID] && dfs(depID) {
					return true
				} else if recStack[depID] {
					return true
				}
			}
		}

		recStack[nodeID] = false
		return false
	}

	for nodeID := range d.nodes {
		if !visited[nodeID] && dfs(nodeID) {
			return true
		}
	}

	return false
}

// GetTopologicalOrder 获取拓扑排序
func (d *DAG) GetTopologicalOrder() ([]string, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	// 使用 Kahn 算法进行拓扑排序
	inDegree := make(map[string]int)
	for nodeID := range d.nodes {
		inDegree[nodeID] = 0
	}

	// 计算入度
	for _, node := range d.nodes {
		for range node.Dependencies {
			inDegree[node.ID]++
		}
	}

	// 找到所有入度为0的节点
	queue := make([]string, 0)
	for nodeID, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, nodeID)
		}
	}

	result := make([]string, 0)
	processed := 0

	for len(queue) > 0 {
		// 取出一个入度为0的节点
		nodeID := queue[0]
		queue = queue[1:]
		result = append(result, nodeID)
		processed++

		// 减少其依赖者的入度
		if node, exists := d.nodes[nodeID]; exists {
			for _, depID := range node.Dependents {
				inDegree[depID]--
				if inDegree[depID] == 0 {
					queue = append(queue, depID)
				}
			}
		}
	}

	// 如果处理的节点数量不等于总节点数，说明有循环
	if processed != len(d.nodes) {
		return nil, fmt.Errorf("图中存在循环依赖")
	}

	return result, nil
}

// GetReadyNodes 获取所有就绪的节点（依赖已满足）
func (d *DAG) GetReadyNodes() []string {
	d.mu.RLock()
	defer d.mu.RUnlock()

	ready := make([]string, 0)
	for nodeID, node := range d.nodes {
		if node.Status == DAGNodeStatusPending && d.areDependenciesSatisfied(nodeID) {
			ready = append(ready, nodeID)
		}
	}

	// 按ID排序保证确定性
	sort.Strings(ready)
	return ready
}

// areDependenciesSatisfied 检查节点的依赖是否已满足（内部方法，调用时已加锁）
func (d *DAG) areDependenciesSatisfied(nodeID string) bool {
	node, exists := d.nodes[nodeID]
	if !exists {
		return false
	}

	for _, depID := range node.Dependencies {
		if depNode, exists := d.nodes[depID]; exists {
			if depNode.Status != DAGNodeStatusCompleted {
				return false
			}
		} else {
			return false // 依赖节点不存在
		}
	}

	return true
}

// UpdateNodeStatus 更新节点状态
func (d *DAG) UpdateNodeStatus(nodeID string, status DAGNodeStatus) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	node, exists := d.nodes[nodeID]
	if !exists {
		return fmt.Errorf("节点不存在: %s", nodeID)
	}

	node.Status = status
	return nil
}

// GetNode 获取节点
func (d *DAG) GetNode(nodeID string) (*DAGNode, bool) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	node, exists := d.nodes[nodeID]
	if !exists {
		return nil, false
	}

	// 返回节点的副本，避免外部修改
	nodeCopy := *node
	nodeCopy.Dependencies = make([]string, len(node.Dependencies))
	copy(nodeCopy.Dependencies, node.Dependencies)
	nodeCopy.Dependents = make([]string, len(node.Dependents))
	copy(nodeCopy.Dependents, node.Dependents)

	return &nodeCopy, true
}

// GetAllNodes 获取所有节点
func (d *DAG) GetAllNodes() map[string]*DAGNode {
	d.mu.RLock()
	defer d.mu.RUnlock()

	result := make(map[string]*DAGNode)
	for nodeID, node := range d.nodes {
		// 返回节点的副本
		nodeCopy := *node
		nodeCopy.Dependencies = make([]string, len(node.Dependencies))
		copy(nodeCopy.Dependencies, node.Dependencies)
		nodeCopy.Dependents = make([]string, len(node.Dependents))
		copy(nodeCopy.Dependents, node.Dependents)
		result[nodeID] = &nodeCopy
	}

	return result
}

// GetDependencyGraph 获取依赖图（名称映射）
func (d *DAG) GetDependencyGraph() map[string][]string {
	d.mu.RLock()
	defer d.mu.RUnlock()

	graph := make(map[string][]string)
	for _, node := range d.nodes {
		deps := make([]string, 0, len(node.Dependencies))
		for _, depID := range node.Dependencies {
			if depNode, exists := d.nodes[depID]; exists {
				deps = append(deps, depNode.Name)
			}
		}
		graph[node.Name] = deps
	}

	return graph
}

// Size 返回节点数量
func (d *DAG) Size() int {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return len(d.nodes)
}

// Clear 清空所有节点
func (d *DAG) Clear() {
	d.mu.Lock()
	defer d.mu.Unlock()

	d.nodes = make(map[string]*DAGNode)
	d.nameToID = make(map[string]string)
}

// removeDependencyFromNode 从节点移除依赖（内部方法，调用时已加锁）
func (d *DAG) removeDependencyFromNode(node *DAGNode, depID string) {
	for i, dep := range node.Dependencies {
		if dep == depID {
			node.Dependencies = append(node.Dependencies[:i], node.Dependencies[i+1:]...)
			break
		}
	}
}

// removeDependentFromNode 从节点移除依赖者（内部方法，调用时已加锁）
func (d *DAG) removeDependentFromNode(node *DAGNode, depID string) {
	for i, dep := range node.Dependents {
		if dep == depID {
			node.Dependents = append(node.Dependents[:i], node.Dependents[i+1:]...)
			break
		}
	}
}
