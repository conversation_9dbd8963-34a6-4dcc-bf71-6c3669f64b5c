// Package archive 提供多版本快照档案管理功能
package archive

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"time"

	"admin/pkg/scheduler/compression"
	schedulerproto "admin/pkg/scheduler/proto"

	"google.golang.org/protobuf/proto"
)

const (
	MaxArchiveSize       = 50 * 1024 * 1024 // 50MB 单个档案文件最大大小
	MaxEntriesPerArchive = 1000             // 每个档案最大条目数
	IndexCacheSize       = 1000             // 索引缓存大小
)

// ArchiveIndex 档案索引信息
type ArchiveIndex struct {
	Filename   string    `json:"filename"`
	EntryCount int32     `json:"entry_count"`
	TotalSize  int64     `json:"total_size"`
	LastUpdate time.Time `json:"last_update"`
	Timestamps []int64   `json:"timestamps"` // 快速查找时间戳
}

// ArchiveManager 档案管理器
type ArchiveManager struct {
	dataPath       string
	compressor     *compression.CompressorPool
	indexCache     map[string]*ArchiveIndex
	currentArchive *schedulerproto.SnapshotArchive
	currentFile    string
}

// NewArchiveManager 创建新的档案管理器
func NewArchiveManager(dataPath string) *ArchiveManager {
	am := &ArchiveManager{
		dataPath:   dataPath,
		compressor: compression.NewCompressorPool(),
		indexCache: make(map[string]*ArchiveIndex),
	}
	// 加载现有档案索引
	am.loadArchiveIndexes()
	return am
}

// AddSnapshot 添加快照到档案
func (am *ArchiveManager) AddSnapshot(snapshotData []byte, checksum string) error {
	// 如果没有当前档案或档案已满，创建新档案
	if am.currentArchive == nil || am.shouldCreateNewArchive() {
		if err := am.createNewArchive(); err != nil {
			return err
		}
	}

	// 压缩快照数据
	compressedData, err := am.compressor.Compress(snapshotData)
	if err != nil {
		return fmt.Errorf("压缩快照失败: %w", err)
	}

	// 创建快照条目
	entry := &schedulerproto.SnapshotEntry{
		Timestamp:    time.Now().Unix(),
		SnapshotData: compressedData,
		Checksum:     checksum,
		SizeBytes:    int32(len(compressedData)),
	}

	// 添加到档案
	am.currentArchive.Entries = append(am.currentArchive.Entries, entry)
	am.currentArchive.Metadata.EntryCount++
	am.currentArchive.Metadata.TotalSize += int64(len(compressedData))
	am.currentArchive.Metadata.LastUpdated = time.Now().Unix()

	// 保存档案
	return am.saveCurrentArchive()
}

// LoadLatestSnapshot 加载最新快照
func (am *ArchiveManager) LoadLatestSnapshot() ([]byte, error) {
	// 从索引缓存中找到最新的档案
	latestFile, latestTimestamp := am.findLatestArchive()
	if latestFile == "" {
		return nil, fmt.Errorf("未找到任何快照")
	}

	// 加载特定时间戳的快照
	return am.loadSnapshotFromArchive(latestFile, latestTimestamp)
}

// LoadSnapshotAt 加载特定时间的快照
func (am *ArchiveManager) LoadSnapshotAt(timestamp time.Time) ([]byte, error) {
	targetTimestamp := timestamp.Unix()

	// 从索引中找到包含此时间戳的档案
	for filename, index := range am.indexCache {
		for _, ts := range index.Timestamps {
			if ts == targetTimestamp {
				return am.loadSnapshotFromArchive(filename, targetTimestamp)
			}
		}
	}

	return nil, fmt.Errorf("未找到时间戳 %d 的快照", targetTimestamp)
}

// ListSnapshots 列出所有快照时间戳
func (am *ArchiveManager) ListSnapshots() []time.Time {
	var timestamps []int64

	for _, index := range am.indexCache {
		timestamps = append(timestamps, index.Timestamps...)
	}

	// 排序
	sort.Slice(timestamps, func(i, j int) bool {
		return timestamps[i] > timestamps[j]
	})

	// 转换为 time.Time
	result := make([]time.Time, len(timestamps))
	for i, ts := range timestamps {
		result[i] = time.Unix(ts, 0)
	}

	return result
}

// CleanOldArchives 清理旧档案
func (am *ArchiveManager) CleanOldArchives(maxAge time.Duration) error {
	files, err := filepath.Glob(filepath.Join(am.dataPath, "scheduler_archive_*.pb.gz"))
	if err != nil {
		return err
	}

	cutoff := time.Now().Add(-maxAge)
	deletedCount := 0

	for _, file := range files {
		info, err := os.Stat(file)
		if err != nil {
			continue
		}

		if info.ModTime().Before(cutoff) {
			if err := os.Remove(file); err == nil {
				deletedCount++
				// 从索引缓存中移除
				delete(am.indexCache, file)
			}
		}
	}

	return nil
}

// GetStats 获取档案统计信息
func (am *ArchiveManager) GetStats() map[string]interface{} {
	totalFiles := len(am.indexCache)
	totalEntries := int32(0)
	totalSize := int64(0)

	for _, index := range am.indexCache {
		totalEntries += index.EntryCount
		totalSize += index.TotalSize
	}

	return map[string]interface{}{
		"total_archive_files": totalFiles,
		"total_entries":       totalEntries,
		"total_size_bytes":    totalSize,
		"total_size_mb":       float64(totalSize) / (1024 * 1024),
	}
}

// shouldCreateNewArchive 判断是否需要创建新档案
func (am *ArchiveManager) shouldCreateNewArchive() bool {
	if am.currentArchive == nil {
		return true
	}

	metadata := am.currentArchive.Metadata
	return metadata.TotalSize > MaxArchiveSize ||
		metadata.EntryCount >= MaxEntriesPerArchive
}

// createNewArchive 创建新档案
func (am *ArchiveManager) createNewArchive() error {
	// 保存当前档案
	if am.currentArchive != nil {
		if err := am.saveCurrentArchive(); err != nil {
			return err
		}
	}

	// 创建新档案
	am.currentArchive = &schedulerproto.SnapshotArchive{
		Entries: make([]*schedulerproto.SnapshotEntry, 0),
		Metadata: &schedulerproto.ArchiveMetadata{
			CreatedAt:  time.Now().Unix(),
			EntryCount: 0,
			TotalSize:  0,
			Version:    "1.0",
		},
	}

	// 生成新文件名
	am.currentFile = filepath.Join(am.dataPath,
		fmt.Sprintf("scheduler_archive_%d.pb.gz", time.Now().Unix()))

	return nil
}

// saveCurrentArchive 保存当前档案
func (am *ArchiveManager) saveCurrentArchive() error {
	if am.currentArchive == nil {
		return nil
	}

	// 序列化档案
	archiveData, err := proto.Marshal(am.currentArchive)
	if err != nil {
		return err
	}

	// 压缩整个档案
	compressedArchive, err := am.compressor.Compress(archiveData)
	if err != nil {
		return err
	}

	// 确保目录存在
	if err := os.MkdirAll(filepath.Dir(am.currentFile), 0o755); err != nil {
		return err
	}

	// 保存到文件
	err = os.WriteFile(am.currentFile, compressedArchive, 0o644)
	if err != nil {
		return err
	}

	// 更新索引缓存
	am.updateIndexCache()

	return nil
}

// updateIndexCache 更新索引缓存
func (am *ArchiveManager) updateIndexCache() {
	if am.currentArchive == nil {
		return
	}

	timestamps := make([]int64, len(am.currentArchive.Entries))
	for i, entry := range am.currentArchive.Entries {
		timestamps[i] = entry.Timestamp
	}

	am.indexCache[am.currentFile] = &ArchiveIndex{
		Filename:   am.currentFile,
		EntryCount: am.currentArchive.Metadata.EntryCount,
		TotalSize:  am.currentArchive.Metadata.TotalSize,
		LastUpdate: time.Unix(am.currentArchive.Metadata.LastUpdated, 0),
		Timestamps: timestamps,
	}
}

// findLatestArchive 找到最新档案
func (am *ArchiveManager) findLatestArchive() (string, int64) {
	var latestFile string
	var latestTimestamp int64

	for filename, index := range am.indexCache {
		if len(index.Timestamps) > 0 {
			lastTS := index.Timestamps[len(index.Timestamps)-1]
			if lastTS > latestTimestamp {
				latestTimestamp = lastTS
				latestFile = filename
			}
		}
	}

	return latestFile, latestTimestamp
}

// loadSnapshotFromArchive 从档案中加载特定快照
func (am *ArchiveManager) loadSnapshotFromArchive(filename string, timestamp int64) ([]byte, error) {
	// 读取并解压档案
	compressedData, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	archiveData, err := am.compressor.Decompress(compressedData)
	if err != nil {
		return nil, err
	}

	// 反序列化档案
	archive := &schedulerproto.SnapshotArchive{}
	err = proto.Unmarshal(archiveData, archive)
	if err != nil {
		return nil, err
	}

	// 找到目标快照
	for _, entry := range archive.Entries {
		if entry.Timestamp == timestamp {
			// 解压快照数据
			return am.compressor.Decompress(entry.SnapshotData)
		}
	}

	return nil, fmt.Errorf("在档案中未找到时间戳 %d 的快照", timestamp)
}

// loadArchiveIndexes 加载档案索引
func (am *ArchiveManager) loadArchiveIndexes() {
	files, err := filepath.Glob(filepath.Join(am.dataPath, "scheduler_archive_*.pb.gz"))
	if err != nil {
		return
	}

	for _, file := range files {
		am.buildIndexForFile(file)
	}
}

// buildIndexForFile 为文件构建索引
func (am *ArchiveManager) buildIndexForFile(filename string) {
	// 读取档案文件
	compressedData, err := os.ReadFile(filename)
	if err != nil {
		return
	}

	// 解压
	archiveData, err := am.compressor.Decompress(compressedData)
	if err != nil {
		return
	}

	// 反序列化档案
	archive := &schedulerproto.SnapshotArchive{}
	err = proto.Unmarshal(archiveData, archive)
	if err != nil {
		return
	}

	// 提取时间戳
	timestamps := make([]int64, len(archive.Entries))
	for i, entry := range archive.Entries {
		timestamps[i] = entry.Timestamp
	}

	// 创建索引
	am.indexCache[filename] = &ArchiveIndex{
		Filename:   filename,
		EntryCount: archive.Metadata.EntryCount,
		TotalSize:  archive.Metadata.TotalSize,
		LastUpdate: time.Unix(archive.Metadata.LastUpdated, 0),
		Timestamps: timestamps,
	}
}
