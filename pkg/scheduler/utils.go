package scheduler

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"admin/pkg/sid"
)

var (
	// 全局 sid 生成器
	globalSidGenerator = sid.NewSid()
	sidMutex           sync.Mutex
)

// TaskBuilder 任务构建器
type TaskBuilder struct {
	task *Task
}

// NewTaskBuilder 创建任务构建器
func NewTaskBuilder(name string, fn TaskFunc) *TaskBuilder {
	// 使用全局雪花算法生成唯一ID
	sidMutex.Lock()
	id, err := globalSidGenerator.GenUint64()
	sidMutex.Unlock()

	if err != nil {
		// 如果生成ID失败，使用时间戳 + 随机数作为备选
		id = uint64(time.Now().UnixNano() + int64(rand.Intn(10000)))
	}
	taskID := fmt.Sprintf("task_%d", id)

	return &TaskBuilder{
		task: NewTask(taskID, name, fn),
	}
}

// NewTaskBuilderWithID 使用指定ID创建任务构建器
func NewTaskBuilderWithID(id, name string, fn TaskFunc) *TaskBuilder {
	return &TaskBuilder{
		task: NewTask(id, name, fn),
	}
}

// WithDescription 设置描述
func (b *TaskBuilder) WithDescription(description string) *TaskBuilder {
	b.task.SetDescription(description)
	return b
}

// WithPriority 设置优先级
func (b *TaskBuilder) WithPriority(priority TaskPriority) *TaskBuilder {
	b.task.SetPriority(priority)
	return b
}

// WithTimeout 设置超时时间
func (b *TaskBuilder) WithTimeout(timeout time.Duration) *TaskBuilder {
	b.task.SetTimeout(timeout)
	return b
}

// WithMaxRetry 设置最大重试次数
func (b *TaskBuilder) WithMaxRetry(maxRetry int) *TaskBuilder {
	b.task.SetMaxRetry(maxRetry)
	return b
}

// Build 构建任务
func (b *TaskBuilder) Build() *Task {
	return b.task
}

// TaskGroup 任务组，用于管理一组相关任务
type TaskGroup struct {
	ID          string
	Name        string
	Description string
	Tasks       []*Task
	CreatedAt   time.Time
	scheduler   *Scheduler
}

// NewTaskGroup 创建任务组
func NewTaskGroup(id, name string) *TaskGroup {
	return &TaskGroup{
		ID:        id,
		Name:      name,
		Tasks:     make([]*Task, 0),
		CreatedAt: time.Now(),
	}
}

// SetScheduler 设置调度器
func (tg *TaskGroup) SetScheduler(scheduler *Scheduler) {
	tg.scheduler = scheduler
}

// AddTask 添加任务到组
func (tg *TaskGroup) AddTask(task *Task) {
	tg.Tasks = append(tg.Tasks, task)
}

// Submit 提交任务组中的所有任务
func (tg *TaskGroup) Submit() []error {
	if tg.scheduler == nil {
		return []error{fmt.Errorf("调度器未设置")}
	}
	return tg.scheduler.SubmitBatch(tg.Tasks)
}

// Wait 等待任务组中所有任务完成
func (tg *TaskGroup) Wait() []*TaskResult {
	results := make([]*TaskResult, len(tg.Tasks))
	for i, task := range tg.Tasks {
		results[i] = task.GetResult()
	}
	return results
}

// WaitWithTimeout 等待任务组中所有任务完成（带超时）
func (tg *TaskGroup) WaitWithTimeout(timeout time.Duration) ([]*TaskResult, bool) {
	results := make([]*TaskResult, len(tg.Tasks))
	allCompleted := true

	for i, task := range tg.Tasks {
		result, completed := task.GetResultWithTimeout(timeout)
		results[i] = result
		if !completed {
			allCompleted = false
		}
	}

	return results, allCompleted
}

// GetCompletedCount 获取已完成任务数量
func (tg *TaskGroup) GetCompletedCount() int {
	count := 0
	for _, task := range tg.Tasks {
		if task.IsCompleted() {
			count++
		}
	}
	return count
}

// GetProgress 获取进度百分比
func (tg *TaskGroup) GetProgress() float64 {
	if len(tg.Tasks) == 0 {
		return 100.0
	}
	return float64(tg.GetCompletedCount()) / float64(len(tg.Tasks)) * 100.0
}

// CancelAll 取消组中所有任务
func (tg *TaskGroup) CancelAll() {
	for _, task := range tg.Tasks {
		task.Cancel()
	}
}

// 预定义的常用任务函数

// SimpleTask 简单任务，执行一个函数
func SimpleTask(fn func() error) TaskFunc {
	return func(ctx context.Context) error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			return fn()
		}
	}
}

// DelayTask 延时任务
func DelayTask(delay time.Duration, fn func() error) TaskFunc {
	return func(ctx context.Context) error {
		select {
		case <-time.After(delay):
			return fn()
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

// RetryTask 带重试的任务
func RetryTask(fn func() error, maxRetries int, retryDelay time.Duration) TaskFunc {
	return func(ctx context.Context) error {
		var lastErr error
		for i := 0; i <= maxRetries; i++ {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
			}

			err := fn()
			if err == nil {
				return nil
			}

			lastErr = err
			if i < maxRetries {
				select {
				case <-time.After(retryDelay):
				case <-ctx.Done():
					return ctx.Err()
				}
			}
		}
		return lastErr
	}
}

// ParallelTask 并行执行多个子任务
func ParallelTask(fns ...func() error) TaskFunc {
	return func(ctx context.Context) error {
		errChan := make(chan error, len(fns))

		for _, fn := range fns {
			go func(f func() error) {
				select {
				case <-ctx.Done():
					errChan <- ctx.Err()
				default:
					errChan <- f()
				}
			}(fn)
		}

		// 等待所有子任务完成
		var lastErr error
		for i := 0; i < len(fns); i++ {
			if err := <-errChan; err != nil {
				lastErr = err
			}
		}

		return lastErr
	}
}

// SequentialTask 顺序执行多个子任务
func SequentialTask(fns ...func() error) TaskFunc {
	return func(ctx context.Context) error {
		for _, fn := range fns {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
			}

			if err := fn(); err != nil {
				return err
			}
		}
		return nil
	}
}
