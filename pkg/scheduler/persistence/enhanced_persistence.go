// Package persistence 提供优化的持久化功能
package persistence

import (
	"context"
	"fmt"
	"sync"
	"time"

	"admin/pkg/log"
	"admin/pkg/scheduler/archive"
	"admin/pkg/scheduler/compression"
	"admin/pkg/scheduler/converter"
	schedulerproto "admin/pkg/scheduler/proto"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

// PersistenceConfig 持久化配置
type PersistenceConfig struct {
	// 启用持久化
	Enabled bool
	// 数据存储路径
	DataPath string
	// 保存间隔
	SaveInterval time.Duration
	// 是否在关闭时自动保存
	SaveOnShutdown bool
	// 是否在启动时自动恢复
	RestoreOnStart bool
	// 最大档案大小 (字节)
	MaxArchiveSize int64
	// 每个档案最大条目数
	MaxEntriesPerArchive int32
	// 旧档案清理间隔
	CleanupInterval time.Duration
	// 档案最大保留时间
	MaxArchiveAge time.Duration
}

// DefaultPersistenceConfig 返回默认持久化配置
func DefaultPersistenceConfig() *PersistenceConfig {
	return &PersistenceConfig{
		Enabled:              false,
		DataPath:             "./storage/scheduler",
		SaveInterval:         30 * time.Second,
		SaveOnShutdown:       true,
		RestoreOnStart:       true,
		MaxArchiveSize:       100 * 1024 * 1024, // 50MB
		MaxEntriesPerArchive: 1000,
		CleanupInterval:      1 * time.Hour,
		MaxArchiveAge:        24 * time.Hour,
	}
}

// SnapshotProvider 快照提供者接口
type SnapshotProvider interface {
	CreateSnapshot() *converter.SchedulerSnapshot
	RestoreFromSnapshot(*converter.SchedulerSnapshot) error
}

// EnhancedPersistenceManager 增强的持久化管理器
type EnhancedPersistenceManager struct {
	config     *PersistenceConfig
	provider   SnapshotProvider
	archive    *archive.ArchiveManager
	compressor *compression.CompressorPool
	logger     *log.Logger

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 优化相关
	lastChecksum string
	lastSaveTime time.Time
}

// NewEnhancedPersistenceManager 创建增强的持久化管理器
func NewEnhancedPersistenceManager(
	config *PersistenceConfig,
	provider SnapshotProvider,
	logger *log.Logger,
) *EnhancedPersistenceManager {
	if config == nil {
		config = DefaultPersistenceConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	pm := &EnhancedPersistenceManager{
		config:     config,
		provider:   provider,
		archive:    archive.NewArchiveManager(config.DataPath),
		compressor: compression.NewCompressorPool(),
		logger:     logger,
		ctx:        ctx,
		cancel:     cancel,
	}

	if config.Enabled {
		// 启动定期保存
		if pm.config.SaveInterval > 0 {
			pm.wg.Add(1)
			go pm.periodicSave()
		}

		// 启动定期清理
		if pm.config.CleanupInterval > 0 {
			pm.wg.Add(1)
			go pm.periodicCleanup()
		}
	}

	return pm
}

// SaveSnapshot 保存快照
func (pm *EnhancedPersistenceManager) SaveSnapshot() error {
	if !pm.config.Enabled {
		return nil
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 创建快照
	snapshot := pm.provider.CreateSnapshot()
	if snapshot == nil {
		return fmt.Errorf("无法创建快照")
	}

	// 转换为protobuf
	pbSnapshot := converter.ToProtobuf(snapshot)

	// 序列化
	snapshotData, err := proto.Marshal(pbSnapshot)
	if err != nil {
		return fmt.Errorf("序列化快照失败: %w", err)
	}

	// 检查是否有变化
	checksum := compression.CalculateChecksum(snapshotData)
	if pm.lastChecksum == checksum && time.Since(pm.lastSaveTime) < time.Minute {
		return nil // 没有变化，跳过保存
	}

	// 添加到档案
	err = pm.archive.AddSnapshot(snapshotData, checksum)
	if err != nil {
		return fmt.Errorf("添加快照到档案失败: %w", err)
	}

	pm.lastChecksum = checksum
	pm.lastSaveTime = time.Now()

	pm.logger.Debug("快照已保存",
		zap.String("checksum", checksum),
		zap.Int("tasks", len(snapshot.Tasks)))

	return nil
}

// LoadLatestSnapshot 加载最新快照
func (pm *EnhancedPersistenceManager) LoadLatestSnapshot() error {
	if !pm.config.Enabled || !pm.config.RestoreOnStart {
		return nil
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 从档案加载最新快照
	snapshotData, err := pm.archive.LoadLatestSnapshot()
	if err != nil {
		pm.logger.Info("未找到快照文件，跳过恢复", zap.Error(err))
		return nil
	}

	// 反序列化
	pbSnapshot := &schedulerproto.SchedulerSnapshot{}
	err = proto.Unmarshal(snapshotData, pbSnapshot)
	if err != nil {
		return fmt.Errorf("反序列化快照失败: %w", err)
	}

	// 转换为原始格式
	snapshot := converter.FromProtobuf(pbSnapshot)

	// 恢复快照
	err = pm.provider.RestoreFromSnapshot(snapshot)
	if err != nil {
		return fmt.Errorf("恢复快照失败: %w", err)
	}

	pm.logger.Info("快照恢复完成",
		zap.Int("tasks", len(snapshot.Tasks)),
		zap.Time("snapshot_time", snapshot.Timestamp))

	return nil
}

// LoadSnapshotAt 加载特定时间的快照
func (pm *EnhancedPersistenceManager) LoadSnapshotAt(timestamp time.Time) error {
	if !pm.config.Enabled {
		return fmt.Errorf("持久化未启用")
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// 从档案加载特定时间的快照
	snapshotData, err := pm.archive.LoadSnapshotAt(timestamp)
	if err != nil {
		return fmt.Errorf("加载快照失败: %w", err)
	}

	// 反序列化
	pbSnapshot := &schedulerproto.SchedulerSnapshot{}
	err = proto.Unmarshal(snapshotData, pbSnapshot)
	if err != nil {
		return fmt.Errorf("反序列化快照失败: %w", err)
	}

	// 转换为原始格式
	snapshot := converter.FromProtobuf(pbSnapshot)

	// 恢复快照
	err = pm.provider.RestoreFromSnapshot(snapshot)
	if err != nil {
		return fmt.Errorf("恢复快照失败: %w", err)
	}

	pm.logger.Info("特定时间快照恢复完成",
		zap.Time("target_time", timestamp),
		zap.Time("snapshot_time", snapshot.Timestamp))

	return nil
}

// ListSnapshots 列出所有快照
func (pm *EnhancedPersistenceManager) ListSnapshots() []time.Time {
	if !pm.config.Enabled {
		return nil
	}

	return pm.archive.ListSnapshots()
}

// GetStats 获取持久化统计信息
func (pm *EnhancedPersistenceManager) GetStats() map[string]interface{} {
	if !pm.config.Enabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	stats := pm.archive.GetStats()
	stats["enabled"] = true
	stats["last_save_time"] = pm.lastSaveTime
	stats["last_checksum"] = pm.lastChecksum

	return stats
}

// Shutdown 关闭持久化管理器
func (pm *EnhancedPersistenceManager) Shutdown() error {
	if !pm.config.Enabled {
		return nil
	}

	// 取消上下文
	pm.cancel()

	// 如果配置了关闭时保存，则保存快照
	if pm.config.SaveOnShutdown {
		if err := pm.SaveSnapshot(); err != nil {
			pm.logger.Error("关闭时保存快照失败", zap.Error(err))
		}
	}

	// 等待所有goroutine结束
	pm.wg.Wait()

	pm.logger.Info("持久化管理器已关闭")
	return nil
}

// periodicSave 定期保存
func (pm *EnhancedPersistenceManager) periodicSave() {
	defer pm.wg.Done()

	ticker := time.NewTicker(pm.config.SaveInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := pm.SaveSnapshot(); err != nil {
				pm.logger.Error("定期保存快照失败", zap.Error(err))
			}
		case <-pm.ctx.Done():
			pm.logger.Info("定期保存已停止")
			return
		}
	}
}

// periodicCleanup 定期清理
func (pm *EnhancedPersistenceManager) periodicCleanup() {
	defer pm.wg.Done()

	ticker := time.NewTicker(pm.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := pm.archive.CleanOldArchives(pm.config.MaxArchiveAge); err != nil {
				pm.logger.Error("定期清理档案失败", zap.Error(err))
			} else {
				pm.logger.Debug("档案清理完成")
			}
		case <-pm.ctx.Done():
			pm.logger.Info("定期清理已停止")
			return
		}
	}
}

// CleanOldArchives 手动清理旧档案
func (pm *EnhancedPersistenceManager) CleanOldArchives(maxAge time.Duration) error {
	if !pm.config.Enabled {
		return nil
	}

	return pm.archive.CleanOldArchives(maxAge)
}
