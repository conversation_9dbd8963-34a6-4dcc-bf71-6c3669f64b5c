package scheduler

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"admin/pkg/log"

	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
)

// SchedulerConfig 调度器配置
type SchedulerConfig struct {
	// 协程池大小，默认为CPU核心数的2倍
	PoolSize int
	// 是否阻塞模式，当协程池满时是否阻塞等待
	Nonblocking bool
	// 协程池的预分配大小
	PreAlloc bool
	// 任务队列大小
	QueueSize int
	// 是否启用任务监控
	EnableMonitor bool
	// 监控间隔
	MonitorInterval time.Duration
}

// DefaultConfig 返回默认配置
func DefaultConfig() *SchedulerConfig {
	return &SchedulerConfig{
		PoolSize:        runtime.NumCPU() * 4, // 默认协程池大小为CPU核心数的4倍
		Nonblocking:     false,
		PreAlloc:        false,
		QueueSize:       1000,
		EnableMonitor:   true,
		MonitorInterval: 10 * time.Second,
	}
}

// Scheduler 并行任务调度器
type Scheduler struct {
	config *SchedulerConfig
	pool   *ants.Pool

	// 任务管理
	tasks     map[string]*Task
	taskQueue chan *Task
	mu        sync.RWMutex

	// 生命周期管理
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// 统计信息
	stats *Stats

	// 日志接口
	logger *log.Logger

	// 钩子函数
	onTaskStart  func(*Task)
	onTaskFinish func(*Task, *TaskResult)
	onTaskError  func(*Task, error)

	// 增强功能 (可选)
	enhancedPersistence interface{} // 增强持久化管理器，使用interface{}避免循环导入
	dependency          *DependencyManager
	cronScheduler       *CronScheduler
}

// Stats 调度器统计信息
type Stats struct {
	mu             sync.RWMutex
	TotalTasks     int64     `json:"total_tasks"`
	PendingTasks   int64     `json:"pending_tasks"`
	RunningTasks   int64     `json:"running_tasks"`
	CompletedTasks int64     `json:"completed_tasks"`
	FailedTasks    int64     `json:"failed_tasks"`
	CanceledTasks  int64     `json:"canceled_tasks"`
	PoolRunning    int       `json:"pool_running"`
	PoolIdle       int       `json:"pool_idle"`
	PoolCapacity   int       `json:"pool_capacity"`
	StartTime      time.Time `json:"start_time"`
	LastUpdateTime time.Time `json:"last_update_time"`
}

// NewScheduler 创建新的任务调度器
func NewScheduler(logger *log.Logger, config *SchedulerConfig) (*Scheduler, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// 创建协程池
	poolOptions := ants.Options{
		Nonblocking: config.Nonblocking,
		PreAlloc:    config.PreAlloc,
	}

	pool, err := ants.NewPool(config.PoolSize, ants.WithOptions(poolOptions))
	if err != nil {
		return nil, fmt.Errorf("创建协程池失败: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	s := &Scheduler{
		config:    config,
		pool:      pool,
		tasks:     make(map[string]*Task),
		taskQueue: make(chan *Task, config.QueueSize),
		ctx:       ctx,
		cancel:    cancel,
		stats: &Stats{
			PoolCapacity: config.PoolSize,
			StartTime:    time.Now(),
		},
		logger: logger, // 需要外部设置logger
	}

	// 启动任务处理器
	s.wg.Add(1)
	go s.taskProcessor()

	// 启动监控器
	if config.EnableMonitor {
		s.wg.Add(1)
		go s.monitor()
	}

	return s, nil
}

// SetLogger 设置日志器
func (s *Scheduler) SetLogger(logger *log.Logger) {
	s.logger = logger
}

// SetOnTaskStart 设置任务开始回调
func (s *Scheduler) SetOnTaskStart(callback func(*Task)) {
	s.onTaskStart = callback
}

// SetOnTaskFinish 设置任务完成回调
func (s *Scheduler) SetOnTaskFinish(callback func(*Task, *TaskResult)) {
	s.onTaskFinish = callback
}

// SetOnTaskError 设置任务错误回调
func (s *Scheduler) SetOnTaskError(callback func(*Task, error)) {
	s.onTaskError = callback
}

// EnableDependencyManagement 启用依赖管理功能
func (s *Scheduler) EnableDependencyManagement(config *DependencyConfig) error {
	if s.dependency != nil {
		return fmt.Errorf("依赖管理功能已启用")
	}

	s.dependency = NewDependencyManager(s, config)
	s.logger.Info("依赖管理功能已启用")
	return nil
}

// EnableCronScheduling 启用定时任务功能
func (s *Scheduler) EnableCronScheduling(config *CronConfig) error {
	if s.cronScheduler != nil {
		return fmt.Errorf("定时任务功能已启用")
	}

	s.cronScheduler = NewCronScheduler(s, config)
	s.cronScheduler.Start()
	s.logger.Info("定时任务功能已启用")
	return nil
}

// SubmitWithDependency 提交带依赖的任务
func (s *Scheduler) SubmitWithDependency(task *Task, dependencies []string) error {
	if s.dependency == nil {
		return fmt.Errorf("依赖管理功能未启用")
	}
	return s.dependency.SubmitTaskWithDependencies(task, dependencies)
}

// AddCronTask 添加定时任务
func (s *Scheduler) AddCronTask(id, name, spec string, taskBuilder *TaskBuilder) error {
	if s.cronScheduler == nil {
		return fmt.Errorf("定时任务功能未启用")
	}
	return s.cronScheduler.AddCronTask(id, name, spec, taskBuilder)
}

// RemoveCronTask 移除定时任务
func (s *Scheduler) RemoveCronTask(taskID string) error {
	if s.cronScheduler == nil {
		return fmt.Errorf("定时任务功能未启用")
	}
	return s.cronScheduler.RemoveCronTask(taskID)
}

// PauseCronTask 暂停定时任务
func (s *Scheduler) PauseCronTask(taskID string) error {
	if s.cronScheduler == nil {
		return fmt.Errorf("定时任务功能未启用")
	}
	return s.cronScheduler.PauseCronTask(taskID)
}

// ResumeCronTask 恢复定时任务
func (s *Scheduler) ResumeCronTask(taskID string) error {
	if s.cronScheduler == nil {
		return fmt.Errorf("定时任务功能未启用")
	}
	return s.cronScheduler.ResumeCronTask(taskID)
}

// GetDependencyGraph 获取任务依赖图
func (s *Scheduler) GetDependencyGraph() map[string][]string {
	if s.dependency == nil {
		return nil
	}
	return s.dependency.GetDependencyGraph()
}

// GetWaitingTasks 获取等待中的依赖任务
func (s *Scheduler) GetWaitingTasks() []string {
	if s.dependency == nil {
		return nil
	}
	return s.dependency.GetWaitingTasks()
}

// GetCronTasks 获取所有定时任务
func (s *Scheduler) GetCronTasks() map[string]*CronTask {
	if s.cronScheduler == nil {
		return nil
	}
	return s.cronScheduler.GetAllCronTasks()
}

// Submit 提交任务
func (s *Scheduler) Submit(task *Task) error {
	if task == nil {
		return fmt.Errorf("任务不能为空")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查任务ID是否已存在
	if _, exists := s.tasks[task.ID]; exists {
		return fmt.Errorf("任务ID %s 已存在", task.ID)
	}

	// 添加到任务管理
	s.tasks[task.ID] = task
	s.updateStats(func(stats *Stats) {
		stats.TotalTasks++
		stats.PendingTasks++
	})

	// 提交到任务队列
	select {
	case s.taskQueue <- task:
		s.logger.Debug("任务已提交到队列", zap.String("task_id", task.ID), zap.String("task_name", task.Name))
		return nil
	case <-s.ctx.Done():
		return fmt.Errorf("调度器已关闭")
	default:
		return fmt.Errorf("任务队列已满")
	}
}

// SubmitBatch 批量提交任务
func (s *Scheduler) SubmitBatch(tasks []*Task) []error {
	errors := make([]error, len(tasks))
	for i, task := range tasks {
		errors[i] = s.Submit(task)
	}
	return errors
}

// GetTask 获取任务
func (s *Scheduler) GetTask(taskID string) (*Task, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	task, exists := s.tasks[taskID]
	return task, exists
}

// GetTaskByName 根据任务名称获取任务
func (s *Scheduler) GetTaskByName(taskName string) (*Task, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for _, task := range s.tasks {
		if task.Name == taskName {
			return task, true
		}
	}
	return nil, false
}

// GetTasksByStatus 根据状态获取任务
func (s *Scheduler) GetTasksByStatus(status TaskStatus) []*Task {
	s.mu.RLock()
	defer s.mu.RUnlock()

	var result []*Task
	for _, task := range s.tasks {
		if task.Status == status {
			result = append(result, task)
		}
	}
	return result
}

// CancelTask 取消任务
func (s *Scheduler) CancelTask(taskID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务 %s 不存在", taskID)
	}

	if task.IsCompleted() {
		return fmt.Errorf("任务 %s 已完成，无法取消", taskID)
	}

	// 记录取消前的状态
	oldStatus := task.Status

	// 取消任务
	task.Cancel()

	// 更新统计信息
	s.updateStats(func(stats *Stats) {
		if oldStatus == TaskStatusPending {
			stats.PendingTasks--
		} else if oldStatus == TaskStatusRunning {
			stats.RunningTasks--
		}
		stats.CanceledTasks++
	})

	s.logger.Info("任务已取消", zap.String("task_id", taskID))
	return nil
}

// getInternalStats 获取内部统计信息（用于monitor等内部方法）
func (s *Scheduler) getInternalStats() *Stats {
	s.stats.mu.RLock()
	defer s.stats.mu.RUnlock()

	// 更新协程池统计
	s.stats.PoolRunning = s.pool.Running()
	s.stats.PoolIdle = s.pool.Free()
	s.stats.LastUpdateTime = time.Now()

	// 深拷贝返回 - 手动复制避免锁拷贝
	return &Stats{
		TotalTasks:     s.stats.TotalTasks,
		PendingTasks:   s.stats.PendingTasks,
		RunningTasks:   s.stats.RunningTasks,
		CompletedTasks: s.stats.CompletedTasks,
		FailedTasks:    s.stats.FailedTasks,
		CanceledTasks:  s.stats.CanceledTasks,
		PoolRunning:    s.stats.PoolRunning,
		PoolIdle:       s.stats.PoolIdle,
		PoolCapacity:   s.stats.PoolCapacity,
		StartTime:      s.stats.StartTime,
		LastUpdateTime: s.stats.LastUpdateTime,
	}
}

// GetStats 获取调度器统计信息（公共接口）
func (s *Scheduler) GetStats() *Stats {
	return s.getInternalStats()
}

// Shutdown 关闭调度器
func (s *Scheduler) Shutdown(timeout time.Duration) error {
	s.logger.Info("开始关闭任务调度器...")

	// 关闭增强功能
	if s.cronScheduler != nil {
		s.cronScheduler.Stop()
	}

	if s.dependency != nil {
		s.dependency.Shutdown()
	}

	// 取消上下文
	s.cancel()

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("任务调度器已正常关闭")
	case <-time.After(timeout):
		s.logger.Warn("任务调度器关闭超时")
	}

	// 关闭协程池
	s.pool.Release()

	return nil
}

// taskProcessor 任务处理器
func (s *Scheduler) taskProcessor() {
	defer s.wg.Done()

	for {
		select {
		case task := <-s.taskQueue:
			if err := s.executeTask(task); err != nil {
				s.logger.Error("执行任务失败", zap.String("task_id", task.ID), zap.Error(err))
			}
		case <-s.ctx.Done():
			s.logger.Info("任务处理器已停止")
			return
		}
	}
}

// executeTask 执行任务
func (s *Scheduler) executeTask(task *Task) error {
	// 检查任务是否已被取消
	select {
	case <-task.ctx.Done():
		// 任务已被取消，需要更新统计信息并发送结果
		s.mu.Lock()
		if task.Status == TaskStatusPending {
			s.updateStats(func(stats *Stats) {
				stats.PendingTasks--
			})
		}
		s.mu.Unlock()

		// 发送取消结果
		result := &TaskResult{
			TaskID:    task.ID,
			Status:    TaskStatusCanceled,
			Error:     task.ctx.Err(),
			StartTime: time.Now(),
			EndTime:   time.Now(),
			Duration:  0,
		}

		select {
		case task.result <- result:
		default:
		}

		return nil
	default:
	}

	// 提交到协程池执行
	return s.pool.Submit(func() {
		s.runTask(task)
	})
}

// runTask 运行任务
func (s *Scheduler) runTask(task *Task) {
	startTime := time.Now()

	// 再次检查任务是否已被取消
	select {
	case <-task.ctx.Done():
		// 任务已被取消，直接返回
		s.logger.Debug("任务在执行前被取消", zap.String("task_id", task.ID), zap.String("task_name", task.Name))
		return
	default:
	}

	// 更新任务状态为运行中
	s.mu.Lock()
	task.Status = TaskStatusRunning
	task.UpdatedAt = startTime
	s.mu.Unlock()

	s.updateStats(func(stats *Stats) {
		stats.PendingTasks--
		stats.RunningTasks++
	})

	// 调用开始回调
	if s.onTaskStart != nil {
		s.onTaskStart(task)
	}

	s.logger.Debug("任务开始执行", zap.String("task_id", task.ID), zap.String("task_name", task.Name))

	var result *TaskResult
	var err error

	// 执行任务
	if task.Timeout > 0 {
		// 带超时执行
		ctx, cancel := context.WithTimeout(task.ctx, task.Timeout)
		defer cancel()
		err = task.Fn(ctx)
	} else {
		// 无超时执行
		err = task.Fn(task.ctx)
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 创建结果
	result = &TaskResult{
		TaskID:    task.ID,
		StartTime: startTime,
		EndTime:   endTime,
		Duration:  duration,
		Error:     err,
	}

	// 更新任务状态
	s.mu.Lock()

	// 检查任务是否已被取消
	if task.Status == TaskStatusCanceled {
		// 任务已被取消，直接发送取消结果
		result.Status = TaskStatusCanceled
		s.updateStats(func(stats *Stats) {
			stats.RunningTasks--
		})
		s.logger.Debug("任务已被取消", zap.String("task_id", task.ID), zap.Duration("duration", duration))
	} else if err != nil {
		task.Status = TaskStatusFailed
		task.Error = err
		task.Retry++
		result.Status = TaskStatusFailed

		s.updateStats(func(stats *Stats) {
			stats.RunningTasks--
			stats.FailedTasks++
		})

		// 调用错误回调
		if s.onTaskError != nil {
			s.onTaskError(task, err)
		}

		s.logger.Error("任务执行失败", zap.String("task_id", task.ID), zap.Error(err), zap.Int("retry", task.Retry))

		// 检查是否需要重试
		if task.CanRetry() && task.Status != TaskStatusCanceled {
			task.Status = TaskStatusPending
			s.updateStats(func(stats *Stats) {
				stats.FailedTasks--
				stats.PendingTasks++
			})
			s.mu.Unlock()

			// 重新提交任务
			go func() {
				time.Sleep(time.Second * time.Duration(task.Retry)) // 退避重试

				// 再次检查任务是否被取消
				select {
				case <-task.ctx.Done():
					// 任务已被取消，不再重试
					s.logger.Info("任务重试时发现已被取消", zap.String("task_id", task.ID), zap.Int("retry", task.Retry))
					return
				default:
				}

				select {
				case s.taskQueue <- task:
					s.logger.Info("任务重试已提交", zap.String("task_id", task.ID), zap.Int("retry", task.Retry))
				case <-s.ctx.Done():
				}
			}()
			return
		}
	} else {
		task.Status = TaskStatusSuccess
		result.Status = TaskStatusSuccess

		s.updateStats(func(stats *Stats) {
			stats.RunningTasks--
			stats.CompletedTasks++
		})

		s.logger.Debug("任务执行成功", zap.String("task_id", task.ID), zap.Duration("duration", duration))
	}

	task.UpdatedAt = endTime
	s.mu.Unlock()

	// 发送结果
	select {
	case task.result <- result:
	default:
		// 如果result channel已满，丢弃结果
	}

	// 调用完成回调
	if s.onTaskFinish != nil {
		s.onTaskFinish(task, result)
	}
}

// updateStats 更新统计信息
func (s *Scheduler) updateStats(fn func(*Stats)) {
	s.stats.mu.Lock()
	defer s.stats.mu.Unlock()
	fn(s.stats)
}

// monitor 监控器
func (s *Scheduler) monitor() {
	defer s.wg.Done()

	ticker := time.NewTicker(s.config.MonitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			stats := s.getInternalStats()
			s.logger.Info("调度器状态",
				zap.Int64("total_tasks", stats.TotalTasks),
				zap.Int64("pending_tasks", stats.PendingTasks),
				zap.Int64("running_tasks", stats.RunningTasks),
				zap.Int64("completed_tasks", stats.CompletedTasks),
				zap.Int64("failed_tasks", stats.FailedTasks),
				zap.Int64("canceled_tasks", stats.CanceledTasks),
				zap.Int("pool_running", stats.PoolRunning),
				zap.Int("pool_idle", stats.PoolIdle),
				zap.Int("pool_capacity", stats.PoolCapacity),
			)
		case <-s.ctx.Done():
			s.logger.Info("监控器已停止")
			return
		}
	}
}
