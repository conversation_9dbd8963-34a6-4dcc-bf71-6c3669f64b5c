package scheduler

import (
	"context"
	"fmt"
	"time"

	"admin/pkg/log"
	"admin/pkg/scheduler/persistence"

	"go.uber.org/zap"
)

// SchedulerJob 调度器任务接口
type SchedulerJob interface {
	// 启动调度器
	StartScheduler(ctx context.Context) error
	// 停止调度器
	StopScheduler(ctx context.Context) error
	// 提交任务
	SubmitTask(taskName string, taskFunc func() error) error
	// 提交带依赖的任务
	SubmitTaskWithDependency(taskName string, taskFunc func() error, dependencies []string) error
	// 添加定时任务
	AddCronTask(id, name, spec string, taskFunc func() error) error
	// 获取调度器统计信息
	GetSchedulerStats() *Stats
	// 获取调度器实例（用于高级操作）
	GetScheduler() *Scheduler

	// 任务管理功能
	// 取消任务
	CancelTask(taskID string) error
	// 获取任务信息
	GetTask(taskID string) (*Task, bool)
	// 根据状态获取任务列表
	GetTasksByStatus(status TaskStatus) []*Task
	// 批量提交任务
	SubmitBatchTasks(tasks []*Task) []error

	// 定时任务管理
	// 移除定时任务
	RemoveCronTask(taskID string) error
	// 暂停定时任务
	PauseCronTask(taskID string) error
	// 恢复定时任务
	ResumeCronTask(taskID string) error
	// 获取所有定时任务
	GetCronTasks() map[string]*CronTask

	// 依赖管理
	// 获取依赖图
	GetDependencyGraph() map[string][]string
	// 获取等待中的依赖任务
	GetWaitingTasks() []string

	// 持久化操作
	// 手动保存快照
	SaveSnapshot() error
	// 恢复最新快照
	RestoreLatestSnapshot() error
	// 获取持久化统计信息
	GetPersistenceStats() map[string]interface{}
}

// schedulerJob 调度器任务实现
type schedulerJob struct {
	log       *log.Logger
	scheduler *Scheduler
}

// NewSchedulerJob 创建新的调度器任务
func NewSchedulerJob(log *log.Logger) SchedulerJob {
	return &schedulerJob{
		log: log,
	}
}

// StartScheduler 启动调度器
func (s *schedulerJob) StartScheduler(ctx context.Context) error {
	if s.scheduler != nil {
		return nil // 已经启动
	}

	// 创建调度器配置
	config := &SchedulerConfig{
		PoolSize:        50,               // 协程池大小
		QueueSize:       1000,             // 任务队列大小
		Nonblocking:     false,            // 阻塞模式
		PreAlloc:        true,             // 预分配协程
		EnableMonitor:   true,             // 启用监控
		MonitorInterval: 30 * time.Minute, // 监控间隔
	}

	// 创建调度器
	sched, err := NewScheduler(s.log, config)
	if err != nil {
		s.log.Error("创建调度器失败", zap.Error(err))
		return err
	}

	// 启用依赖管理
	depConfig := &DependencyConfig{
		Enabled:     true,
		MaxWaitTime: 5 * time.Minute, // 最大等待依赖时间
	}
	if err := sched.EnableDependencyManagement(depConfig); err != nil {
		s.log.Error("启用依赖管理失败", zap.Error(err))
		return err
	}

	// 启用定时任务
	cronConfig := &CronConfig{
		Enabled:     true,
		Location:    time.Local,
		WithSeconds: true, // 支持秒级精度
	}
	if err := sched.EnableCronScheduling(cronConfig); err != nil {
		s.log.Error("启用定时任务失败", zap.Error(err))
		return err
	}

	// 启用增强持久化（可选）
	persistConfig := &persistence.PersistenceConfig{
		Enabled:              true,
		DataPath:             "./storage/scheduler",
		SaveInterval:         5 * time.Minute,    // 生产环境可以更长
		SaveOnShutdown:       true,               // 确保关闭时保存
		RestoreOnStart:       true,               // 启动时恢复
		MaxArchiveSize:       100 * 1024 * 1024,  // 100MB
		MaxEntriesPerArchive: 10000,              // 更大的条目数
		CleanupInterval:      6 * time.Hour,      // 定期清理
		MaxArchiveAge:        7 * 24 * time.Hour, // 保留7天
	}
	if err := sched.EnableEnhancedPersistence(persistConfig); err != nil {
		s.log.Warn("启用增强持久化失败", zap.Error(err))
		// 持久化失败不应该影响调度器启动
	}

	// 设置任务生命周期回调
	sched.SetOnTaskStart(func(task *Task) {
		s.log.Info("任务开始执行",
			zap.String("task_id", task.ID),
			zap.String("task_name", task.Name),
			zap.String("priority", task.Priority.String()))
	})

	sched.SetOnTaskFinish(func(task *Task, result *TaskResult) {
		s.log.Info("任务执行完成",
			zap.String("task_id", task.ID),
			zap.String("task_name", task.Name),
			zap.String("status", result.Status.String()),
			zap.Duration("duration", result.Duration))
	})

	sched.SetOnTaskError(func(task *Task, err error) {
		s.log.Error("任务执行错误",
			zap.String("task_id", task.ID),
			zap.String("task_name", task.Name),
			zap.Error(err),
			zap.Int("retry", task.Retry))
	})

	s.scheduler = sched
	s.log.Info("调度器启动成功")
	return nil
}

// StopScheduler 停止调度器
func (s *schedulerJob) StopScheduler(ctx context.Context) error {
	if s.scheduler == nil {
		return nil
	}

	// 获取超时时间，默认30秒
	timeout := 30 * time.Second
	if deadline, ok := ctx.Deadline(); ok {
		timeout = time.Until(deadline)
	}

	err := s.scheduler.Shutdown(timeout)
	if err != nil {
		s.log.Error("调度器关闭失败", zap.Error(err))
		return err
	}

	s.scheduler = nil
	s.log.Info("调度器已关闭")
	return nil
}

// SubmitTask 提交任务
func (s *schedulerJob) SubmitTask(taskName string, taskFunc func() error) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	// 创建任务
	task := NewTaskBuilder(taskName, SimpleTask(taskFunc)).
		WithDescription("Job模块提交的任务").
		WithPriority(PriorityNormal).
		WithTimeout(5 * time.Minute).
		WithMaxRetry(3).
		Build()

	// 提交任务
	if err := s.scheduler.Submit(task); err != nil {
		s.log.Error("提交任务失败",
			zap.String("task_name", taskName),
			zap.Error(err))
		return err
	}

	s.log.Info("任务提交成功",
		zap.String("task_id", task.ID),
		zap.String("task_name", taskName))
	return nil
}

// SubmitTaskWithDependency 提交带依赖的任务
func (s *schedulerJob) SubmitTaskWithDependency(taskName string, taskFunc func() error, dependencies []string) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	// 创建任务
	task := NewTaskBuilder(taskName, SimpleTask(taskFunc)).
		WithDescription("Job模块提交的依赖任务").
		WithPriority(PriorityNormal).
		WithTimeout(5 * time.Minute).
		WithMaxRetry(3).
		Build()

	// 提交带依赖的任务
	if err := s.scheduler.SubmitWithDependency(task, dependencies); err != nil {
		s.log.Error("提交依赖任务失败",
			zap.String("task_name", taskName),
			zap.Strings("dependencies", dependencies),
			zap.Error(err))
		return err
	}

	s.log.Info("依赖任务提交成功",
		zap.String("task_id", task.ID),
		zap.String("task_name", taskName),
		zap.Strings("dependencies", dependencies))
	return nil
}

// AddCronTask 添加定时任务
func (s *schedulerJob) AddCronTask(id, name, spec string, taskFunc func() error) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	// 创建任务构建器
	taskBuilder := NewTaskBuilder(name, SimpleTask(taskFunc)).
		WithDescription("Job模块的定时任务").
		WithPriority(PriorityNormal).
		WithTimeout(10 * time.Minute).
		WithMaxRetry(2)

	// 添加定时任务
	if err := s.scheduler.AddCronTask(id, name, spec, taskBuilder); err != nil {
		s.log.Error("添加定时任务失败",
			zap.String("cron_id", id),
			zap.String("cron_name", name),
			zap.String("spec", spec),
			zap.Error(err))
		return err
	}

	s.log.Info("定时任务添加成功",
		zap.String("cron_id", id),
		zap.String("cron_name", name),
		zap.String("spec", spec))
	return nil
}

// GetSchedulerStats 获取调度器统计信息
func (s *schedulerJob) GetSchedulerStats() *Stats {
	if s.scheduler == nil {
		return nil
	}
	return s.scheduler.GetStats()
}

// GetScheduler 获取调度器实例（用于高级操作）
func (s *schedulerJob) GetScheduler() *Scheduler {
	return s.scheduler
}

// CancelTask 取消任务
func (s *schedulerJob) CancelTask(taskID string) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}
	return s.scheduler.CancelTask(taskID)
}

// GetTask 获取任务信息
func (s *schedulerJob) GetTask(taskID string) (*Task, bool) {
	if s.scheduler == nil {
		return nil, false
	}
	return s.scheduler.GetTask(taskID)
}

// GetTasksByStatus 根据状态获取任务列表
func (s *schedulerJob) GetTasksByStatus(status TaskStatus) []*Task {
	if s.scheduler == nil {
		return nil
	}
	return s.scheduler.GetTasksByStatus(status)
}

// SubmitBatchTasks 批量提交任务
func (s *schedulerJob) SubmitBatchTasks(tasks []*Task) []error {
	if s.scheduler == nil {
		return []error{fmt.Errorf("调度器未启动")}
	}
	return s.scheduler.SubmitBatch(tasks)
}

// RemoveCronTask 移除定时任务
func (s *schedulerJob) RemoveCronTask(taskID string) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}
	return s.scheduler.RemoveCronTask(taskID)
}

// PauseCronTask 暂停定时任务
func (s *schedulerJob) PauseCronTask(taskID string) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	if err := s.scheduler.PauseCronTask(taskID); err != nil {
		s.log.Error("暂停定时任务失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return err
	}

	s.log.Info("定时任务已暂停", zap.String("task_id", taskID))
	return nil
}

// ResumeCronTask 恢复定时任务
func (s *schedulerJob) ResumeCronTask(taskID string) error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	if err := s.scheduler.ResumeCronTask(taskID); err != nil {
		s.log.Error("恢复定时任务失败",
			zap.String("task_id", taskID),
			zap.Error(err))
		return err
	}

	s.log.Info("定时任务已恢复", zap.String("task_id", taskID))
	return nil
}

// GetCronTasks 获取所有定时任务
func (s *schedulerJob) GetCronTasks() map[string]*CronTask {
	if s.scheduler == nil {
		return nil
	}
	return s.scheduler.GetCronTasks()
}

// GetDependencyGraph 获取依赖图
func (s *schedulerJob) GetDependencyGraph() map[string][]string {
	if s.scheduler == nil {
		return nil
	}
	return s.scheduler.GetDependencyGraph()
}

// GetWaitingTasks 获取等待中的依赖任务
func (s *schedulerJob) GetWaitingTasks() []string {
	if s.scheduler == nil {
		return nil
	}

	return s.scheduler.GetWaitingTasks()
}

// SaveSnapshot 手动保存快照
func (s *schedulerJob) SaveSnapshot() error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	// 目前调度器包已经提供了 SaveSnapshot 公共方法
	return s.scheduler.SaveSnapshot()
}

// RestoreLatestSnapshot 恢复最新快照
func (s *schedulerJob) RestoreLatestSnapshot() error {
	if s.scheduler == nil {
		return fmt.Errorf("调度器未启动")
	}

	// 目前调度器包已经提供了 RestoreLatestSnapshot 公共方法
	return s.scheduler.RestoreLatestSnapshot()
}

// GetPersistenceStats 获取持久化统计信息
func (s *schedulerJob) GetPersistenceStats() map[string]interface{} {
	if s.scheduler == nil {
		return nil
	}

	// 目前调度器包已经提供了获取增强持久化统计信息的方法
	return s.scheduler.GetEnhancedPersistenceStats()
}
