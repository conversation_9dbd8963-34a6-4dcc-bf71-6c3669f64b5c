// Package converter 提供数据结构转换功能
package converter

import (
	"time"

	"admin/pkg/scheduler/proto"
)

// 为了避免循环导入，我们定义简化的接口结构
// 实际使用时需要适配具体的scheduler类型

// Task 任务接口 - 用于适配不同的任务实现
type TaskInfo struct {
	ID          string
	Name        string
	Description string
	Priority    int32
	Status      int32
	Timeout     time.Duration
	RetryCount  int
	MaxRetries  int
	CreatedAt   time.Time
	UpdatedAt   time.Time
	StartedAt   time.Time
	CompletedAt time.Time
	ErrorMsg    string
}

// Stats 统计信息结构
type StatsInfo struct {
	TotalTasks     int64
	PendingTasks   int64
	RunningTasks   int64
	CompletedTasks int64
	FailedTasks    int64
	CanceledTasks  int64
	PoolRunning    int
	PoolIdle       int
	PoolCapacity   int
	StartTime      time.Time
	LastUpdateTime time.Time
}

// SchedulerSnapshot 调度器快照
type SchedulerSnapshot struct {
	Tasks     []*TaskInfo
	Stats     *StatsInfo
	Timestamp time.Time
	Version   string
}

// TaskConverter 任务转换器接口
type TaskConverter interface {
	ToTaskInfo() *TaskInfo
	FromTaskInfo(*TaskInfo) error
}

// StatsConverter 统计转换器接口
type StatsConverter interface {
	ToStatsInfo() *StatsInfo
	FromStatsInfo(*StatsInfo) error
}

// ToProtobuf 将调度器快照转换为protobuf格式
func ToProtobuf(snapshot *SchedulerSnapshot) *proto.SchedulerSnapshot {
	pbSnapshot := &proto.SchedulerSnapshot{
		Tasks:     make([]*proto.Task, 0, len(snapshot.Tasks)),
		Stats:     toProtobufStats(snapshot.Stats),
		Timestamp: snapshot.Timestamp.Unix(),
		Version:   snapshot.Version,
	}

	// 转换任务
	for _, task := range snapshot.Tasks {
		pbTask := &proto.Task{
			Id:          task.ID,
			Name:        task.Name,
			Description: task.Description,
			Priority:    task.Priority,
			Status:      task.Status,
			TimeoutNs:   int64(task.Timeout),
			RetryCount:  int32(task.RetryCount),
			MaxRetries:  int32(task.MaxRetries),
			CreatedAt:   task.CreatedAt.Unix(),
			UpdatedAt:   task.UpdatedAt.Unix(),
		}

		if !task.StartedAt.IsZero() {
			pbTask.StartedAt = task.StartedAt.Unix()
		}
		if !task.CompletedAt.IsZero() {
			pbTask.CompletedAt = task.CompletedAt.Unix()
		}
		if task.ErrorMsg != "" {
			pbTask.ErrorMessage = task.ErrorMsg
		}

		pbSnapshot.Tasks = append(pbSnapshot.Tasks, pbTask)
	}

	return pbSnapshot
}

// FromProtobuf 将protobuf格式转换为调度器快照
func FromProtobuf(pbSnapshot *proto.SchedulerSnapshot) *SchedulerSnapshot {
	snapshot := &SchedulerSnapshot{
		Tasks:     make([]*TaskInfo, 0, len(pbSnapshot.Tasks)),
		Stats:     fromProtobufStats(pbSnapshot.Stats),
		Timestamp: time.Unix(pbSnapshot.Timestamp, 0),
		Version:   pbSnapshot.Version,
	}

	// 转换任务
	for _, pbTask := range pbSnapshot.Tasks {
		task := &TaskInfo{
			ID:          pbTask.Id,
			Name:        pbTask.Name,
			Description: pbTask.Description,
			Priority:    pbTask.Priority,
			Status:      pbTask.Status,
			Timeout:     time.Duration(pbTask.TimeoutNs),
			RetryCount:  int(pbTask.RetryCount),
			MaxRetries:  int(pbTask.MaxRetries),
			CreatedAt:   time.Unix(pbTask.CreatedAt, 0),
			UpdatedAt:   time.Unix(pbTask.UpdatedAt, 0),
			ErrorMsg:    pbTask.ErrorMessage,
		}

		if pbTask.StartedAt > 0 {
			task.StartedAt = time.Unix(pbTask.StartedAt, 0)
		}
		if pbTask.CompletedAt > 0 {
			task.CompletedAt = time.Unix(pbTask.CompletedAt, 0)
		}

		snapshot.Tasks = append(snapshot.Tasks, task)
	}

	return snapshot
}

// toProtobufStats 转换统计信息为protobuf格式
func toProtobufStats(stats *StatsInfo) *proto.Stats {
	if stats == nil {
		return nil
	}

	return &proto.Stats{
		TotalTasks:     stats.TotalTasks,
		PendingTasks:   stats.PendingTasks,
		RunningTasks:   stats.RunningTasks,
		CompletedTasks: stats.CompletedTasks,
		FailedTasks:    stats.FailedTasks,
		CanceledTasks:  stats.CanceledTasks,
		PoolRunning:    int32(stats.PoolRunning),
		PoolIdle:       int32(stats.PoolIdle),
		PoolCapacity:   int32(stats.PoolCapacity),
		StartTime:      stats.StartTime.Unix(),
		LastUpdateTime: stats.LastUpdateTime.Unix(),
	}
}

// fromProtobufStats 从protobuf格式转换统计信息
func fromProtobufStats(pbStats *proto.Stats) *StatsInfo {
	if pbStats == nil {
		return nil
	}

	return &StatsInfo{
		TotalTasks:     pbStats.TotalTasks,
		PendingTasks:   pbStats.PendingTasks,
		RunningTasks:   pbStats.RunningTasks,
		CompletedTasks: pbStats.CompletedTasks,
		FailedTasks:    pbStats.FailedTasks,
		CanceledTasks:  pbStats.CanceledTasks,
		PoolRunning:    int(pbStats.PoolRunning),
		PoolIdle:       int(pbStats.PoolIdle),
		PoolCapacity:   int(pbStats.PoolCapacity),
		StartTime:      time.Unix(pbStats.StartTime, 0),
		LastUpdateTime: time.Unix(pbStats.LastUpdateTime, 0),
	}
}
