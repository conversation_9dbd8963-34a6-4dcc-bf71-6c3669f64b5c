package meta

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJobMeta_Validate(t *testing.T) {
	tests := []struct {
		name      string
		job       *JobMeta
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid job",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name:        "test-job",
					Description: "Test job",
					Version:     "1.0.0",
					Author:      "test",
					Created:     time.Now(),
					Modified:    time.Now(),
				},
				Parameters: map[string]string{
					"param1": "value1",
				},
				Variables: map[string]string{
					"var1": "value1",
				},
				Entries: []JobEntryMeta{
					{
						Name:        "START",
						Type:        JobEntryTypeStart,
						Description: "Start entry",
						Config:      map[string]interface{}{},
						Position:    Position{X: 100, Y: 100},
					},
					{
						Name:        "SUCCESS",
						Type:        JobEntryTypeSuccess,
						Description: "Success entry",
						Config:      map[string]interface{}{},
						Position:    Position{X: 300, Y: 100},
					},
				},
				Hops: []JobHopMeta{
					{
						From:          "START",
						To:            "SUCCESS",
						Evaluation:    true,
						Unconditional: true,
					},
				},
			},
			wantError: false,
		},
		{
			name: "empty job name",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name: "",
				},
				Entries: []JobEntryMeta{
					{
						Name:     "START",
						Type:     JobEntryTypeStart,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
				},
			},
			wantError: true,
			errorMsg:  "job name is required",
		},
		{
			name: "no entries",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name: "test-job",
				},
				Entries: []JobEntryMeta{},
			},
			wantError: true,
			errorMsg:  "job must have at least one entry",
		},
		{
			name: "duplicate entry names",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name: "test-job",
				},
				Entries: []JobEntryMeta{
					{
						Name:     "START",
						Type:     JobEntryTypeStart,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
					{
						Name:     "START",
						Type:     JobEntryTypeSuccess,
						Config:   map[string]interface{}{},
						Position: Position{X: 300, Y: 100},
					},
				},
			},
			wantError: true,
			errorMsg:  "duplicate entry name: START",
		},
		{
			name: "no start entry",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name: "test-job",
				},
				Entries: []JobEntryMeta{
					{
						Name:     "SUCCESS",
						Type:     JobEntryTypeSuccess,
						Config:   map[string]interface{}{},
						Position: Position{X: 300, Y: 100},
					},
				},
			},
			wantError: true,
			errorMsg:  "job must have a START entry",
		},
		{
			name: "invalid hop - from entry not found",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name: "test-job",
				},
				Entries: []JobEntryMeta{
					{
						Name:     "START",
						Type:     JobEntryTypeStart,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
				},
				Hops: []JobHopMeta{
					{
						From:          "INVALID",
						To:            "START",
						Evaluation:    true,
						Unconditional: true,
					},
				},
			},
			wantError: true,
			errorMsg:  "hop from entry not found: INVALID",
		},
		{
			name: "invalid hop - to entry not found",
			job: &JobMeta{
				Meta: JobMetaInfo{
					Name: "test-job",
				},
				Entries: []JobEntryMeta{
					{
						Name:     "START",
						Type:     JobEntryTypeStart,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
				},
				Hops: []JobHopMeta{
					{
						From:          "START",
						To:            "INVALID",
						Evaluation:    true,
						Unconditional: true,
					},
				},
			},
			wantError: true,
			errorMsg:  "hop to entry not found: INVALID",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.job.Validate()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestJobEntryMeta_Validate(t *testing.T) {
	tests := []struct {
		name      string
		entry     *JobEntryMeta
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid start entry",
			entry: &JobEntryMeta{
				Name:     "START",
				Type:     JobEntryTypeStart,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid trans entry",
			entry: &JobEntryMeta{
				Name: "TRANS",
				Type: JobEntryTypeTrans,
				Config: map[string]interface{}{
					"transformation_file": "test.json",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid mail entry",
			entry: &JobEntryMeta{
				Name: "MAIL",
				Type: JobEntryTypeMail,
				Config: map[string]interface{}{
					"to":      []string{"<EMAIL>"},
					"subject": "Test",
					"body":    "Test body",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid eval entry",
			entry: &JobEntryMeta{
				Name: "EVAL",
				Type: JobEntryTypeEval,
				Config: map[string]interface{}{
					"condition": "true",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "empty entry name",
			entry: &JobEntryMeta{
				Name:     "",
				Type:     JobEntryTypeStart,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "entry name is required",
		},
		{
			name: "empty entry type",
			entry: &JobEntryMeta{
				Name:     "TEST",
				Type:     "",
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "entry type is required",
		},
		{
			name: "trans entry without transformation_file",
			entry: &JobEntryMeta{
				Name:     "TRANS",
				Type:     JobEntryTypeTrans,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "TRANS entry requires transformation_file in config",
		},
		{
			name: "mail entry without to",
			entry: &JobEntryMeta{
				Name: "MAIL",
				Type: JobEntryTypeMail,
				Config: map[string]interface{}{
					"subject": "Test",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "MAIL entry requires 'to' in config",
		},
		{
			name: "eval entry without condition",
			entry: &JobEntryMeta{
				Name:     "EVAL",
				Type:     JobEntryTypeEval,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "EVAL entry requires 'condition' in config",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.entry.Validate()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestJobMeta_GetStartEntry(t *testing.T) {
	job := &JobMeta{
		Entries: []JobEntryMeta{
			{
				Name: "ENTRY1",
				Type: JobEntryTypeSuccess,
			},
			{
				Name: "START",
				Type: JobEntryTypeStart,
			},
			{
				Name: "ENTRY2",
				Type: JobEntryTypeError,
			},
		},
	}

	startEntry := job.GetStartEntry()
	require.NotNil(t, startEntry)
	assert.Equal(t, "START", startEntry.Name)
	assert.Equal(t, JobEntryTypeStart, startEntry.Type)
}

func TestJobMeta_GetStartEntry_NotFound(t *testing.T) {
	job := &JobMeta{
		Entries: []JobEntryMeta{
			{
				Name: "ENTRY1",
				Type: JobEntryTypeSuccess,
			},
		},
	}

	startEntry := job.GetStartEntry()
	assert.Nil(t, startEntry)
}

func TestJobMeta_GetEntryByName(t *testing.T) {
	job := &JobMeta{
		Entries: []JobEntryMeta{
			{
				Name: "ENTRY1",
				Type: JobEntryTypeSuccess,
			},
			{
				Name: "START",
				Type: JobEntryTypeStart,
			},
		},
	}

	// Test existing entry
	entry := job.GetEntryByName("ENTRY1")
	require.NotNil(t, entry)
	assert.Equal(t, "ENTRY1", entry.Name)

	// Test non-existing entry
	entry = job.GetEntryByName("NONEXISTENT")
	assert.Nil(t, entry)
}

func TestJobMeta_GetNextEntries(t *testing.T) {
	job := &JobMeta{
		Hops: []JobHopMeta{
			{From: "START", To: "ENTRY1"},
			{From: "START", To: "ENTRY2"},
			{From: "ENTRY1", To: "SUCCESS"},
		},
	}

	// Test entry with multiple next entries
	nextEntries := job.GetNextEntries("START")
	assert.Len(t, nextEntries, 2)
	assert.Contains(t, nextEntries, "ENTRY1")
	assert.Contains(t, nextEntries, "ENTRY2")

	// Test entry with single next entry
	nextEntries = job.GetNextEntries("ENTRY1")
	assert.Len(t, nextEntries, 1)
	assert.Contains(t, nextEntries, "SUCCESS")

	// Test entry with no next entries
	nextEntries = job.GetNextEntries("SUCCESS")
	assert.Len(t, nextEntries, 0)

	// Test non-existing entry
	nextEntries = job.GetNextEntries("NONEXISTENT")
	assert.Len(t, nextEntries, 0)
}

func TestJobMeta_GetHopCondition(t *testing.T) {
	job := &JobMeta{
		Hops: []JobHopMeta{
			{
				From:          "START",
				To:            "ENTRY1",
				Evaluation:    true,
				Unconditional: false,
				Condition:     "success",
			},
			{
				From:          "START",
				To:            "ERROR",
				Evaluation:    false,
				Unconditional: false,
				Condition:     "failure",
			},
		},
	}

	// Test existing hop
	hop := job.GetHopCondition("START", "ENTRY1")
	require.NotNil(t, hop)
	assert.Equal(t, "START", hop.From)
	assert.Equal(t, "ENTRY1", hop.To)
	assert.Equal(t, "success", hop.Condition)

	// Test non-existing hop
	hop = job.GetHopCondition("ENTRY1", "START")
	assert.Nil(t, hop)
}

func TestValidationError(t *testing.T) {
	err := NewValidationErrorf("test error with %s", "parameter")
	assert.Error(t, err)
	assert.Equal(t, "test error with parameter", err.Error())

	// Test type assertion
	validationErr, ok := err.(*ValidationError)
	assert.True(t, ok)
	assert.Equal(t, "test error with parameter", validationErr.Message)
}
