package meta

import "time"

// TransformationMeta 转换元数据定义
type TransformationMeta struct {
	Meta       TransMetaInfo     `json:"meta" yaml:"meta"`
	Parameters map[string]string `json:"parameters" yaml:"parameters"`
	Variables  map[string]string `json:"variables" yaml:"variables"`
	Steps      []StepMeta        `json:"steps" yaml:"steps"`
	Hops       []HopMeta         `json:"hops" yaml:"hops"`
}

// TransMetaInfo 转换基本信息
type TransMetaInfo struct {
	Name        string    `json:"name" yaml:"name"`
	Description string    `json:"description" yaml:"description"`
	Version     string    `json:"version" yaml:"version"`
	Author      string    `json:"author" yaml:"author"`
	Created     time.Time `json:"created" yaml:"created"`
	Modified    time.Time `json:"modified" yaml:"modified"`
}

// StepMeta 步骤元数据
type StepMeta struct {
	Name        string                 `json:"name" yaml:"name"`
	Type        StepType               `json:"type" yaml:"type"`
	Description string                 `json:"description" yaml:"description"`
	Config      map[string]interface{} `json:"config" yaml:"config"`
	Fields      []FieldMeta            `json:"fields,omitempty" yaml:"fields,omitempty"`
	Position    Position               `json:"position" yaml:"position"`
}

// HopMeta 数据流连接元数据
type HopMeta struct {
	From      string `json:"from" yaml:"from"`
	To        string `json:"to" yaml:"to"`
	Enabled   bool   `json:"enabled" yaml:"enabled"`
	Condition string `json:"condition,omitempty" yaml:"condition,omitempty"`
}

// FieldMeta 字段元数据
type FieldMeta struct {
	Name   string    `json:"name" yaml:"name"`
	Type   FieldType `json:"type" yaml:"type"`
	Length int       `json:"length,omitempty" yaml:"length,omitempty"`
	Format string    `json:"format,omitempty" yaml:"format,omitempty"`
}

// StepType 步骤类型
type StepType string

// 输入步骤类型
const (
	StepTypeTableInput    StepType = "TableInput"
	StepTypeCSVFileInput  StepType = "CSVFileInput"
	StepTypeJSONFileInput StepType = "JSONFileInput"
	StepTypeXMLFileInput  StepType = "XMLFileInput"
	StepTypeRestInput     StepType = "RestInput"
	StepTypeKafkaInput    StepType = "KafkaInput"
	StepTypeRedisInput    StepType = "RedisInput"
	StepTypeDataGenerator StepType = "DataGenerator"
	StepTypeDummyInput    StepType = "DummyInput"
	// Missing input step types referenced in validator.go
	StepTypeTextFileInput StepType = "TextFileInput"
	StepTypeExcelInput    StepType = "ExcelInput"
	StepTypeJSONInput     StepType = "JSONInput"
	StepTypeXMLInput      StepType = "XMLInput"
	StepTypeHTTPClient    StepType = "HTTPClient"
	StepTypeHTTPPost      StepType = "HTTPPost"
	StepTypeFTPGet        StepType = "FTPGet"
	StepTypeFTPPut        StepType = "FTPPut"
)

// 转换步骤类型
const (
	StepTypeSelectValues       StepType = "SelectValues"
	StepTypeFilterRows         StepType = "FilterRows"
	StepTypeCalculator         StepType = "Calculator"
	StepTypeValueMapper        StepType = "ValueMapper"
	StepTypeStringOps          StepType = "StringOperations"
	StepTypeDateOps            StepType = "DateOperations"
	StepTypeAddConstants       StepType = "AddConstants"
	StepTypeRegexEval          StepType = "RegexEval"
	StepTypeAggregator         StepType = "Aggregator"
	StepTypeJoin               StepType = "Join"
	StepTypeSort               StepType = "Sort"
	StepTypeUniqueRows         StepType = "UniqueRows"
	StepTypeFieldMapper        StepType = "FieldMapper"
	StepTypeEnhancedFilter     StepType = "EnhancedFilter"
	StepTypeEnhancedCalculator StepType = "EnhancedCalculator"
	// Missing transform step types referenced in validator.go
	StepTypeSortRows         StepType = "SortRows"
	StepTypeGroupBy          StepType = "GroupBy"
	StepTypeJoinRows         StepType = "JoinRows"
	StepTypeStringOperations StepType = "StringOperations"
	StepTypeScript           StepType = "Script"
	StepTypeValidator        StepType = "Validator"
	StepTypeDummy            StepType = "Dummy"
)

// 输出步骤类型
const (
	StepTypeTableOutput         StepType = "TableOutput"
	StepTypeCSVFileOutput       StepType = "CSVFileOutput"
	StepTypeJSONFileOutput      StepType = "JSONFileOutput"
	StepTypeXMLFileOutput       StepType = "XMLFileOutput"
	StepTypeRestOutput          StepType = "RestOutput"
	StepTypeKafkaOutput         StepType = "KafkaOutput"
	StepTypeRedisOutput         StepType = "RedisOutput"
	StepTypeElasticsearchOutput StepType = "ElasticsearchOutput"
	StepTypeDummyOutput         StepType = "DummyOutput"
	StepTypeTextFileOutput      StepType = "TextFileOutput"
	// Missing output step types referenced in validator.go
	StepTypeExcelOutput StepType = "ExcelOutput"
	StepTypeJSONOutput  StepType = "JSONOutput"
	StepTypeXMLOutput   StepType = "XMLOutput"
)

// FieldType 字段类型
type FieldType string

const (
	FieldTypeString  FieldType = "String"
	FieldTypeInteger FieldType = "Integer"
	FieldTypeLong    FieldType = "Long"
	FieldTypeFloat   FieldType = "Float"
	FieldTypeDouble  FieldType = "Double"
	FieldTypeDate    FieldType = "Date"
	FieldTypeBoolean FieldType = "Boolean"
	FieldTypeBinary  FieldType = "Binary"
	// Missing field types referenced in validator.go
	FieldTypeBigInt          FieldType = "BigInt"
	FieldTypeNumber          FieldType = "Number"
	FieldTypeTimestamp       FieldType = "Timestamp"
	FieldTypeInternetAddress FieldType = "InternetAddress"
	FieldTypeNone            FieldType = "None"
)

// Validate 验证转换元数据
func (t *TransformationMeta) Validate() error {
	if t.Meta.Name == "" {
		return NewValidationError("transformation name is required")
	}

	if len(t.Steps) == 0 {
		return NewValidationError("transformation must have at least one step")
	}

	// 验证步骤名称
	stepNames := make(map[string]bool)

	for _, step := range t.Steps {
		if step.Name == "" {
			return NewValidationError("step name is required")
		}

		if stepNames[step.Name] {
			return NewValidationErrorf("duplicate step name: %s", step.Name)
		}
		stepNames[step.Name] = true
	}

	// 验证连接
	for _, hop := range t.Hops {
		if !stepNames[hop.From] {
			return NewValidationErrorf("hop from step not found: %s", hop.From)
		}
		if !stepNames[hop.To] {
			return NewValidationErrorf("hop to step not found: %s", hop.To)
		}
	}

	// 最后验证步骤配置
	for _, step := range t.Steps {
		if err := step.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证步骤元数据
func (s *StepMeta) Validate() error {
	if s.Name == "" {
		return NewValidationError("step name is required")
	}

	if s.Type == "" {
		return NewValidationError("step type is required")
	}

	// 根据类型验证配置
	switch s.Type {
	case StepTypeTableInput:
		if s.Config["connection"] == nil || s.Config["sql"] == nil {
			return NewValidationError("TableInput step requires 'connection' and 'sql' in config")
		}
	case StepTypeTableOutput:
		if s.Config["connection"] == nil || s.Config["table"] == nil {
			return NewValidationError("TableOutput step requires 'connection' and 'table' in config")
		}
	case StepTypeCSVFileInput:
		if s.Config["filename"] == nil {
			return NewValidationError("CSVFileInput step requires 'filename' in config")
		}
	case StepTypeCSVFileOutput:
		if s.Config["filename"] == nil {
			return NewValidationError("CSVFileOutput step requires 'filename' in config")
		}
	case StepTypeJSONFileInput:
		if s.Config["filename"] == nil {
			return NewValidationError("JSONFileInput step requires 'filename' in config")
		}
	case StepTypeJSONFileOutput:
		if s.Config["filename"] == nil {
			return NewValidationError("JSONFileOutput step requires 'filename' in config")
		}
	case StepTypeRestInput:
		if s.Config["url"] == nil {
			return NewValidationError("RestInput step requires 'url' in config")
		}
	case StepTypeRestOutput:
		if s.Config["url"] == nil {
			return NewValidationError("RestOutput step requires 'url' in config")
		}
	case StepTypeDataGenerator:
		if s.Config["row_count"] == nil {
			return NewValidationError("DataGenerator step requires 'row_count' in config")
		}
	}

	return nil
}

// GetStepByName 根据名称获取步骤
func (t *TransformationMeta) GetStepByName(name string) *StepMeta {
	for i := range t.Steps {
		if t.Steps[i].Name == name {
			return &t.Steps[i]
		}
	}
	return nil
}

// GetNextSteps 获取指定步骤的下一个步骤列表
func (t *TransformationMeta) GetNextSteps(stepName string) []string {
	var nextSteps []string
	for _, hop := range t.Hops {
		if hop.From == stepName && hop.Enabled {
			nextSteps = append(nextSteps, hop.To)
		}
	}
	return nextSteps
}

// GetInputSteps 获取输入步骤
func (t *TransformationMeta) GetInputSteps() []StepMeta {
	var inputSteps []StepMeta
	for _, step := range t.Steps {
		if IsInputStep(step.Type) {
			inputSteps = append(inputSteps, step)
		}
	}
	return inputSteps
}

// GetOutputSteps 获取输出步骤
func (t *TransformationMeta) GetOutputSteps() []StepMeta {
	var outputSteps []StepMeta
	for _, step := range t.Steps {
		if IsOutputStep(step.Type) {
			outputSteps = append(outputSteps, step)
		}
	}
	return outputSteps
}

// IsInputStep 判断是否为输入步骤
func IsInputStep(stepType StepType) bool {
	inputSteps := []StepType{
		StepTypeTableInput, StepTypeCSVFileInput, StepTypeJSONFileInput,
		StepTypeXMLFileInput, StepTypeRestInput, StepTypeKafkaInput,
		StepTypeRedisInput, StepTypeDataGenerator, StepTypeDummyInput,
	}

	for _, inputStep := range inputSteps {
		if stepType == inputStep {
			return true
		}
	}
	return false
}

// IsOutputStep 判断是否为输出步骤
func IsOutputStep(stepType StepType) bool {
	outputSteps := []StepType{
		StepTypeTableOutput, StepTypeCSVFileOutput, StepTypeJSONFileOutput,
		StepTypeXMLFileOutput, StepTypeRestOutput, StepTypeKafkaOutput,
		StepTypeRedisOutput, StepTypeElasticsearchOutput, StepTypeDummyOutput,
		StepTypeTextFileOutput,
	}

	for _, outputStep := range outputSteps {
		if stepType == outputStep {
			return true
		}
	}
	return false
}

// IsTransformStep 判断是否为转换步骤
func IsTransformStep(stepType StepType) bool {
	return !IsInputStep(stepType) && !IsOutputStep(stepType)
}
