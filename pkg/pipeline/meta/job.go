package meta

import (
	"time"
)

// JobMeta 作业元数据定义
type JobMeta struct {
	Meta       JobMetaInfo       `json:"meta" yaml:"meta"`
	Parameters map[string]string `json:"parameters" yaml:"parameters"`
	Variables  map[string]string `json:"variables" yaml:"variables"`
	Entries    []JobEntryMeta    `json:"entries" yaml:"entries"`
	Hops       []JobHopMeta      `json:"hops" yaml:"hops"`
}

// JobMetaInfo 作业基本信息
type JobMetaInfo struct {
	Name        string    `json:"name" yaml:"name"`
	Description string    `json:"description" yaml:"description"`
	Version     string    `json:"version" yaml:"version"`
	Author      string    `json:"author" yaml:"author"`
	Created     time.Time `json:"created" yaml:"created"`
	Modified    time.Time `json:"modified" yaml:"modified"`
}

// JobEntryMeta 作业条目元数据
type JobEntryMeta struct {
	Name        string                 `json:"name" yaml:"name"`
	Type        JobEntryType           `json:"type" yaml:"type"`
	Description string                 `json:"description" yaml:"description"`
	Config      map[string]interface{} `json:"config" yaml:"config"`
	Position    Position               `json:"position" yaml:"position"`
}

// JobHopMeta 作业跳转连接元数据
type JobHopMeta struct {
	From          string `json:"from" yaml:"from"`
	To            string `json:"to" yaml:"to"`
	Evaluation    bool   `json:"evaluation" yaml:"evaluation"`
	Unconditional bool   `json:"unconditional" yaml:"unconditional"`
	Condition     string `json:"condition,omitempty" yaml:"condition,omitempty"`
}

// JobEntryType 作业条目类型
type JobEntryType string

const (
	JobEntryTypeStart   JobEntryType = "START"
	JobEntryTypeSuccess JobEntryType = "SUCCESS"
	JobEntryTypeError   JobEntryType = "ERROR"
	JobEntryTypeTrans   JobEntryType = "TRANS"
	JobEntryTypeJob     JobEntryType = "JOB"
	JobEntryTypeMail    JobEntryType = "MAIL"
	JobEntryTypeEval    JobEntryType = "EVAL"
	JobEntryTypeSQL     JobEntryType = "SQL"
	JobEntryTypeShell   JobEntryType = "SHELL"
	JobEntryTypeFile    JobEntryType = "FILE"
	JobEntryTypeWait    JobEntryType = "WAIT"
	// Missing constants referenced in validator.go
	JobEntryTypeScript JobEntryType = "SCRIPT"
	JobEntryTypeFTP    JobEntryType = "FTP"
	JobEntryTypeHTTP   JobEntryType = "HTTP"
	JobEntryTypeEmail  JobEntryType = "EMAIL"
	JobEntryTypeAbort  JobEntryType = "ABORT"
	JobEntryTypeDummy  JobEntryType = "DUMMY"
)

// Position 位置信息（用于图形化界面）
type Position struct {
	X int `json:"x" yaml:"x"`
	Y int `json:"y" yaml:"y"`
}

// Validate 验证作业元数据
func (j *JobMeta) Validate() error {
	if j.Meta.Name == "" {
		return NewValidationError("job name is required")
	}

	if len(j.Entries) == 0 {
		return NewValidationError("job must have at least one entry")
	}

	// 验证作业条目
	entryNames := make(map[string]bool)
	hasStart := false

	for _, entry := range j.Entries {
		if entry.Name == "" {
			return NewValidationError("entry name is required")
		}

		if entryNames[entry.Name] {
			return NewValidationErrorf("duplicate entry name: %s", entry.Name)
		}
		entryNames[entry.Name] = true

		if entry.Type == JobEntryTypeStart {
			hasStart = true
		}

		if err := entry.Validate(); err != nil {
			return err
		}
	}

	if !hasStart {
		return NewValidationError("job must have a START entry")
	}

	// 验证连接
	for _, hop := range j.Hops {
		if !entryNames[hop.From] {
			return NewValidationErrorf("hop from entry not found: %s", hop.From)
		}
		if !entryNames[hop.To] {
			return NewValidationErrorf("hop to entry not found: %s", hop.To)
		}
	}

	return nil
}

// Validate 验证作业条目
func (j *JobEntryMeta) Validate() error {
	if j.Name == "" {
		return NewValidationError("entry name is required")
	}

	if j.Type == "" {
		return NewValidationError("entry type is required")
	}

	// 根据类型验证配置
	switch j.Type {
	case JobEntryTypeTrans:
		if j.Config["transformation_file"] == nil {
			return NewValidationError("TRANS entry requires transformation_file in config")
		}
	case JobEntryTypeMail:
		if j.Config["to"] == nil {
			return NewValidationError("MAIL entry requires 'to' in config")
		}
	case JobEntryTypeEval:
		if j.Config["condition"] == nil {
			return NewValidationError("EVAL entry requires 'condition' in config")
		}
	}

	return nil
}

// GetStartEntry 获取开始条目
func (j *JobMeta) GetStartEntry() *JobEntryMeta {
	for i := range j.Entries {
		if j.Entries[i].Type == JobEntryTypeStart {
			return &j.Entries[i]
		}
	}
	return nil
}

// GetEntryByName 根据名称获取条目
func (j *JobMeta) GetEntryByName(name string) *JobEntryMeta {
	for i := range j.Entries {
		if j.Entries[i].Name == name {
			return &j.Entries[i]
		}
	}
	return nil
}

// GetNextEntries 获取指定条目的下一个条目列表
func (j *JobMeta) GetNextEntries(entryName string) []string {
	var nextEntries []string
	for _, hop := range j.Hops {
		if hop.From == entryName {
			nextEntries = append(nextEntries, hop.To)
		}
	}
	return nextEntries
}

// GetHopCondition 获取跳转条件
func (j *JobMeta) GetHopCondition(from, to string) *JobHopMeta {
	for i := range j.Hops {
		if j.Hops[i].From == from && j.Hops[i].To == to {
			return &j.Hops[i]
		}
	}
	return nil
}
