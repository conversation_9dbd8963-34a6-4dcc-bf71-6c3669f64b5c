package meta

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTransformationMeta_Validate(t *testing.T) {
	tests := []struct {
		name      string
		trans     *TransformationMeta
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid transformation",
			trans: &TransformationMeta{
				Meta: TransMetaInfo{
					Name:        "test-transformation",
					Description: "Test transformation",
					Version:     "1.0.0",
					Author:      "test",
					Created:     time.Now(),
					Modified:    time.Now(),
				},
				Parameters: map[string]string{
					"param1": "value1",
				},
				Variables: map[string]string{
					"var1": "value1",
				},
				Steps: []StepMeta{
					{
						Name:        "input_step",
						Type:        StepTypeTableInput,
						Description: "Input step",
						Config: map[string]interface{}{
							"connection": "test_conn",
							"sql":        "SELECT * FROM users",
						},
						Position: Position{X: 100, Y: 100},
					},
					{
						Name:        "output_step",
						Type:        StepTypeTableOutput,
						Description: "Output step",
						Config: map[string]interface{}{
							"connection": "test_conn",
							"table":      "output_table",
						},
						Position: Position{X: 300, Y: 100},
					},
				},
				Hops: []HopMeta{
					{
						From:    "input_step",
						To:      "output_step",
						Enabled: true,
					},
				},
			},
			wantError: false,
		},
		{
			name: "empty transformation name",
			trans: &TransformationMeta{
				Meta: TransMetaInfo{
					Name: "",
				},
				Steps: []StepMeta{
					{
						Name:     "step1",
						Type:     StepTypeTableInput,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
				},
			},
			wantError: true,
			errorMsg:  "transformation name is required",
		},
		{
			name: "no steps",
			trans: &TransformationMeta{
				Meta: TransMetaInfo{
					Name: "test-transformation",
				},
				Steps: []StepMeta{},
			},
			wantError: true,
			errorMsg:  "transformation must have at least one step",
		},
		{
			name: "duplicate step names",
			trans: &TransformationMeta{
				Meta: TransMetaInfo{
					Name: "test-transformation",
				},
				Steps: []StepMeta{
					{
						Name:     "step1",
						Type:     StepTypeTableInput,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
					{
						Name:     "step1",
						Type:     StepTypeTableOutput,
						Config:   map[string]interface{}{},
						Position: Position{X: 300, Y: 100},
					},
				},
			},
			wantError: true,
			errorMsg:  "duplicate step name: step1",
		},
		{
			name: "invalid hop - from step not found",
			trans: &TransformationMeta{
				Meta: TransMetaInfo{
					Name: "test-transformation",
				},
				Steps: []StepMeta{
					{
						Name:     "step1",
						Type:     StepTypeTableInput,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
				},
				Hops: []HopMeta{
					{
						From:    "invalid_step",
						To:      "step1",
						Enabled: true,
					},
				},
			},
			wantError: true,
			errorMsg:  "hop from step not found: invalid_step",
		},
		{
			name: "invalid hop - to step not found",
			trans: &TransformationMeta{
				Meta: TransMetaInfo{
					Name: "test-transformation",
				},
				Steps: []StepMeta{
					{
						Name:     "step1",
						Type:     StepTypeTableInput,
						Config:   map[string]interface{}{},
						Position: Position{X: 100, Y: 100},
					},
				},
				Hops: []HopMeta{
					{
						From:    "step1",
						To:      "invalid_step",
						Enabled: true,
					},
				},
			},
			wantError: true,
			errorMsg:  "hop to step not found: invalid_step",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.trans.Validate()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestStepMeta_Validate(t *testing.T) {
	tests := []struct {
		name      string
		step      *StepMeta
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid table input step",
			step: &StepMeta{
				Name: "input_step",
				Type: StepTypeTableInput,
				Config: map[string]interface{}{
					"connection": "test_conn",
					"sql":        "SELECT * FROM users",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid table output step",
			step: &StepMeta{
				Name: "output_step",
				Type: StepTypeTableOutput,
				Config: map[string]interface{}{
					"connection": "test_conn",
					"table":      "output_table",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid csv file input step",
			step: &StepMeta{
				Name: "csv_input",
				Type: StepTypeCSVFileInput,
				Config: map[string]interface{}{
					"filename": "/path/to/input.csv",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid csv file output step",
			step: &StepMeta{
				Name: "csv_output",
				Type: StepTypeCSVFileOutput,
				Config: map[string]interface{}{
					"filename": "/path/to/output.csv",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "valid transform step",
			step: &StepMeta{
				Name: "calculator",
				Type: StepTypeCalculator,
				Config: map[string]interface{}{
					"calculations": []interface{}{
						map[string]interface{}{
							"field":   "total",
							"formula": "field1 + field2",
						},
					},
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: false,
		},
		{
			name: "empty step name",
			step: &StepMeta{
				Name:     "",
				Type:     StepTypeTableInput,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "step name is required",
		},
		{
			name: "empty step type",
			step: &StepMeta{
				Name:     "test_step",
				Type:     "",
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "step type is required",
		},
		{
			name: "table input without connection",
			step: &StepMeta{
				Name: "input_step",
				Type: StepTypeTableInput,
				Config: map[string]interface{}{
					"sql": "SELECT * FROM users",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "TableInput step requires 'connection' and 'sql' in config",
		},
		{
			name: "table input without sql",
			step: &StepMeta{
				Name: "input_step",
				Type: StepTypeTableInput,
				Config: map[string]interface{}{
					"connection": "test_conn",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "TableInput step requires 'connection' and 'sql' in config",
		},
		{
			name: "table output without connection",
			step: &StepMeta{
				Name: "output_step",
				Type: StepTypeTableOutput,
				Config: map[string]interface{}{
					"table": "output_table",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "TableOutput step requires 'connection' and 'table' in config",
		},
		{
			name: "table output without table",
			step: &StepMeta{
				Name: "output_step",
				Type: StepTypeTableOutput,
				Config: map[string]interface{}{
					"connection": "test_conn",
				},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "TableOutput step requires 'connection' and 'table' in config",
		},
		{
			name: "csv file input without filename",
			step: &StepMeta{
				Name:     "csv_input",
				Type:     StepTypeCSVFileInput,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "CSVFileInput step requires 'filename' in config",
		},
		{
			name: "csv file output without filename",
			step: &StepMeta{
				Name:     "csv_output",
				Type:     StepTypeCSVFileOutput,
				Config:   map[string]interface{}{},
				Position: Position{X: 100, Y: 100},
			},
			wantError: true,
			errorMsg:  "CSVFileOutput step requires 'filename' in config",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.step.Validate()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTransformationMeta_GetStepByName(t *testing.T) {
	trans := &TransformationMeta{
		Steps: []StepMeta{
			{
				Name: "step1",
				Type: StepTypeTableInput,
			},
			{
				Name: "step2",
				Type: StepTypeTableOutput,
			},
		},
	}

	// Test existing step
	step := trans.GetStepByName("step1")
	require.NotNil(t, step)
	assert.Equal(t, "step1", step.Name)

	// Test non-existing step
	step = trans.GetStepByName("nonexistent")
	assert.Nil(t, step)
}

func TestTransformationMeta_GetNextSteps(t *testing.T) {
	trans := &TransformationMeta{
		Steps: []StepMeta{
			{Name: "step1", Type: StepTypeTableInput},
			{Name: "step2", Type: StepTypeCalculator},
			{Name: "step3", Type: StepTypeTableOutput},
		},
		Hops: []HopMeta{
			{From: "step1", To: "step2", Enabled: true},
			{From: "step2", To: "step3", Enabled: true},
			{From: "step1", To: "step3", Enabled: false}, // disabled hop
		},
	}

	// Test step with multiple next steps
	nextSteps := trans.GetNextSteps("step1")
	assert.Len(t, nextSteps, 1) // only enabled hops
	assert.Contains(t, nextSteps, "step2")

	// Test step with one next step
	nextSteps = trans.GetNextSteps("step2")
	assert.Len(t, nextSteps, 1)
	assert.Contains(t, nextSteps, "step3")

	// Test step with no next steps
	nextSteps = trans.GetNextSteps("step3")
	assert.Len(t, nextSteps, 0)

	// Test non-existing step
	nextSteps = trans.GetNextSteps("nonexistent")
	assert.Len(t, nextSteps, 0)
}

func TestTransformationMeta_GetInputSteps(t *testing.T) {
	trans := &TransformationMeta{
		Steps: []StepMeta{
			{Name: "table_input", Type: StepTypeTableInput},
			{Name: "csv_input", Type: StepTypeCSVFileInput},
			{Name: "calculator", Type: StepTypeCalculator},
			{Name: "table_output", Type: StepTypeTableOutput},
		},
	}

	inputSteps := trans.GetInputSteps()
	assert.Len(t, inputSteps, 2)

	stepNames := make([]string, len(inputSteps))
	for i, step := range inputSteps {
		stepNames[i] = step.Name
	}

	assert.Contains(t, stepNames, "table_input")
	assert.Contains(t, stepNames, "csv_input")
	assert.NotContains(t, stepNames, "calculator")
	assert.NotContains(t, stepNames, "table_output")
}

func TestTransformationMeta_GetOutputSteps(t *testing.T) {
	trans := &TransformationMeta{
		Steps: []StepMeta{
			{Name: "table_input", Type: StepTypeTableInput},
			{Name: "calculator", Type: StepTypeCalculator},
			{Name: "table_output", Type: StepTypeTableOutput},
			{Name: "csv_output", Type: StepTypeCSVFileOutput},
		},
	}

	outputSteps := trans.GetOutputSteps()
	assert.Len(t, outputSteps, 2)

	stepNames := make([]string, len(outputSteps))
	for i, step := range outputSteps {
		stepNames[i] = step.Name
	}

	assert.Contains(t, stepNames, "table_output")
	assert.Contains(t, stepNames, "csv_output")
	assert.NotContains(t, stepNames, "table_input")
	assert.NotContains(t, stepNames, "calculator")
}

func TestIsInputStep(t *testing.T) {
	tests := []struct {
		stepType StepType
		expected bool
	}{
		{StepTypeTableInput, true},
		{StepTypeCSVFileInput, true},
		{StepTypeJSONFileInput, true},
		{StepTypeXMLFileInput, true},
		{StepTypeRestInput, true},
		{StepTypeKafkaInput, true},
		{StepTypeRedisInput, true},
		{StepTypeDataGenerator, true},
		{StepTypeCalculator, false},
		{StepTypeTableOutput, false},
		{StepTypeFilterRows, false},
	}

	for _, tt := range tests {
		t.Run(string(tt.stepType), func(t *testing.T) {
			result := IsInputStep(tt.stepType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsOutputStep(t *testing.T) {
	tests := []struct {
		stepType StepType
		expected bool
	}{
		{StepTypeTableOutput, true},
		{StepTypeCSVFileOutput, true},
		{StepTypeJSONFileOutput, true},
		{StepTypeXMLFileOutput, true},
		{StepTypeRestOutput, true},
		{StepTypeKafkaOutput, true},
		{StepTypeRedisOutput, true},
		{StepTypeElasticsearchOutput, true},
		{StepTypeDummyOutput, true},
		{StepTypeTextFileOutput, true},
		{StepTypeTableInput, false},
		{StepTypeCalculator, false},
		{StepTypeFilterRows, false},
	}

	for _, tt := range tests {
		t.Run(string(tt.stepType), func(t *testing.T) {
			result := IsOutputStep(tt.stepType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsTransformStep(t *testing.T) {
	tests := []struct {
		stepType StepType
		expected bool
	}{
		{StepTypeCalculator, true},
		{StepTypeFilterRows, true},
		{StepTypeSelectValues, true},
		{StepTypeValueMapper, true},
		{StepTypeStringOps, true},
		{StepTypeDateOps, true},
		{StepTypeAddConstants, true},
		{StepTypeTableInput, false},
		{StepTypeCSVFileInput, false},
		{StepTypeTableOutput, false},
		{StepTypeCSVFileOutput, false},
	}

	for _, tt := range tests {
		t.Run(string(tt.stepType), func(t *testing.T) {
			result := IsTransformStep(tt.stepType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestComplexTransformationValidation(t *testing.T) {
	// Test a complex transformation with multiple step types
	trans := &TransformationMeta{
		Meta: TransMetaInfo{
			Name:        "complex-transformation",
			Description: "Complex transformation with multiple steps",
			Version:     "1.0.0",
		},
		Parameters: map[string]string{
			"batch_date": "2024-01-01",
			"source_db":  "production",
		},
		Variables: map[string]string{
			"timeout": "300",
		},
		Steps: []StepMeta{
			{
				Name: "user_input",
				Type: StepTypeTableInput,
				Config: map[string]interface{}{
					"connection": "source_db",
					"sql":        "SELECT * FROM users WHERE updated_at >= ?",
					"parameters": []string{"${batch_date}"},
				},
				Fields: []FieldMeta{
					{Name: "id", Type: FieldTypeInteger, Length: 10},
					{Name: "name", Type: FieldTypeString, Length: 100},
					{Name: "email", Type: FieldTypeString, Length: 255},
				},
				Position: Position{X: 100, Y: 100},
			},
			{
				Name: "email_validator",
				Type: StepTypeFilterRows,
				Config: map[string]interface{}{
					"condition": "email IS NOT NULL AND email LIKE '%@%'",
				},
				Position: Position{X: 300, Y: 100},
			},
			{
				Name: "add_metadata",
				Type: StepTypeAddConstants,
				Config: map[string]interface{}{
					"constants": []interface{}{
						map[string]interface{}{
							"field": "batch_date",
							"type":  "String",
							"value": "${batch_date}",
						},
					},
				},
				Position: Position{X: 500, Y: 100},
			},
			{
				Name: "user_output",
				Type: StepTypeTableOutput,
				Config: map[string]interface{}{
					"connection": "target_db",
					"table":      "processed_users",
				},
				Position: Position{X: 700, Y: 100},
			},
		},
		Hops: []HopMeta{
			{From: "user_input", To: "email_validator", Enabled: true},
			{From: "email_validator", To: "add_metadata", Enabled: true},
			{From: "add_metadata", To: "user_output", Enabled: true},
		},
	}

	err := trans.Validate()
	assert.NoError(t, err)

	// Test step retrieval
	inputSteps := trans.GetInputSteps()
	assert.Len(t, inputSteps, 1)
	assert.Equal(t, "user_input", inputSteps[0].Name)

	outputSteps := trans.GetOutputSteps()
	assert.Len(t, outputSteps, 1)
	assert.Equal(t, "user_output", outputSteps[0].Name)

	// Test hop navigation
	nextSteps := trans.GetNextSteps("user_input")
	assert.Len(t, nextSteps, 1)
	assert.Equal(t, "email_validator", nextSteps[0])
}

func TestTransformationWithFieldMetadata(t *testing.T) {
	trans := &TransformationMeta{
		Meta: TransMetaInfo{
			Name: "field-metadata-test",
		},
		Steps: []StepMeta{
			{
				Name: "input_step",
				Type: StepTypeTableInput,
				Config: map[string]interface{}{
					"connection": "test_conn",
					"sql":        "SELECT * FROM users",
				},
				Fields: []FieldMeta{
					{
						Name:   "id",
						Type:   FieldTypeInteger,
						Length: 10,
					},
					{
						Name:   "name",
						Type:   FieldTypeString,
						Length: 100,
					},
					{
						Name:   "birth_date",
						Type:   FieldTypeDate,
						Format: "yyyy-MM-dd",
					},
					{
						Name: "is_active",
						Type: FieldTypeBoolean,
					},
					{
						Name:   "salary",
						Type:   FieldTypeDouble,
						Length: 10,
					},
				},
				Position: Position{X: 100, Y: 100},
			},
		},
	}

	err := trans.Validate()
	assert.NoError(t, err)

	step := trans.GetStepByName("input_step")
	require.NotNil(t, step)
	assert.Len(t, step.Fields, 5)

	// Verify field metadata
	idField := step.Fields[0]
	assert.Equal(t, "id", idField.Name)
	assert.Equal(t, FieldTypeInteger, idField.Type)
	assert.Equal(t, 10, idField.Length)

	nameField := step.Fields[1]
	assert.Equal(t, "name", nameField.Name)
	assert.Equal(t, FieldTypeString, nameField.Type)
	assert.Equal(t, 100, nameField.Length)

	dateField := step.Fields[2]
	assert.Equal(t, "birth_date", dateField.Name)
	assert.Equal(t, FieldTypeDate, dateField.Type)
	assert.Equal(t, "yyyy-MM-dd", dateField.Format)

	boolField := step.Fields[3]
	assert.Equal(t, "is_active", boolField.Name)
	assert.Equal(t, FieldTypeBoolean, boolField.Type)

	doubleField := step.Fields[4]
	assert.Equal(t, "salary", doubleField.Name)
	assert.Equal(t, FieldTypeDouble, doubleField.Type)
	assert.Equal(t, 10, doubleField.Length)
}

func TestTransformationCycleDetection(t *testing.T) {
	// This test should be implemented when cycle detection is added
	// to the validation logic in the future
	trans := &TransformationMeta{
		Meta: TransMetaInfo{
			Name: "cycle-test",
		},
		Steps: []StepMeta{
			{Name: "step1", Type: StepTypeTableInput, Config: map[string]interface{}{"connection": "test", "sql": "SELECT 1"}, Position: Position{X: 100, Y: 100}},
			{Name: "step2", Type: StepTypeCalculator, Config: map[string]interface{}{}, Position: Position{X: 200, Y: 100}},
			{Name: "step3", Type: StepTypeTableOutput, Config: map[string]interface{}{"connection": "test", "table": "output"}, Position: Position{X: 300, Y: 100}},
		},
		Hops: []HopMeta{
			{From: "step1", To: "step2", Enabled: true},
			{From: "step2", To: "step3", Enabled: true},
			// This would create a cycle: {From: "step3", To: "step1", Enabled: true},
		},
	}

	// For now, this should pass validation
	// In the future, cycle detection can be added
	err := trans.Validate()
	assert.NoError(t, err)
}
