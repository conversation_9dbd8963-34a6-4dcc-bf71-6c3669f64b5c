package meta

// ConnectionMeta 连接元数据定义
type ConnectionMeta struct {
	Name        string                 `json:"name" yaml:"name"`
	Type        ConnectionType         `json:"type" yaml:"type"`
	Description string                 `json:"description" yaml:"description"`
	Config      map[string]interface{} `json:"config" yaml:"config"`
}

// ConnectionType 连接类型
type ConnectionType string

const (
	ConnectionTypeDatabase      ConnectionType = "DATABASE"
	ConnectionTypeRedis         ConnectionType = "REDIS"
	ConnectionTypeKafka         ConnectionType = "KAFKA"
	ConnectionTypeElasticsearch ConnectionType = "ELASTICSEARCH"
	ConnectionTypeFile          ConnectionType = "FILE"
	ConnectionTypeRest          ConnectionType = "REST"
	ConnectionTypeFTP           ConnectionType = "FTP"
	ConnectionTypeSFTP          ConnectionType = "SFTP"
	// Missing connection types referenced in validator.go
	ConnectionTypeMySQL      ConnectionType = "MYSQL"
	ConnectionTypePostgreSQL ConnectionType = "POSTGRESQL"
	ConnectionTypeOracle     ConnectionType = "ORACLE"
	ConnectionTypeSQLServer  ConnectionType = "SQLSERVER"
	ConnectionTypeMongoDB    ConnectionType = "MONGODB"
	ConnectionTypeRabbitMQ   ConnectionType = "RABBITMQ"
	ConnectionTypeHTTP       ConnectionType = "HTTP"
	ConnectionTypeHTTPS      ConnectionType = "HTTPS"
)

// DatabaseType 数据库类型
type DatabaseType string

const (
	DatabaseTypeMySQL      DatabaseType = "mysql"
	DatabaseTypePostgreSQL DatabaseType = "postgresql"
	DatabaseTypeSQLServer  DatabaseType = "sqlserver"
	DatabaseTypeOracle     DatabaseType = "oracle"
	DatabaseTypeSQLite     DatabaseType = "sqlite"
	DatabaseTypeClickHouse DatabaseType = "clickhouse"
)

// ConnectionsConfig 连接配置集合
type ConnectionsConfig struct {
	Connections []ConnectionMeta `json:"connections" yaml:"connections"`
}

// Validate 验证连接元数据
func (c *ConnectionMeta) Validate() error {
	if c.Name == "" {
		return NewValidationError("connection name is required")
	}

	if c.Type == "" {
		return NewValidationError("connection type is required")
	}

	// 根据连接类型验证配置
	switch c.Type {
	case ConnectionTypeDatabase:
		if err := c.validateDatabaseConfig(); err != nil {
			return err
		}
	case ConnectionTypeRedis:
		if err := c.validateRedisConfig(); err != nil {
			return err
		}
	case ConnectionTypeKafka:
		if err := c.validateKafkaConfig(); err != nil {
			return err
		}
	case ConnectionTypeElasticsearch:
		if err := c.validateElasticsearchConfig(); err != nil {
			return err
		}
	}

	return nil
}

// validateDatabaseConfig 验证数据库配置
func (c *ConnectionMeta) validateDatabaseConfig() error {
	// 首先验证数据库类型
	dbType, ok := c.Config["database_type"].(string)
	if !ok {
		if c.Config["database_type"] == nil {
			return NewValidationError("database connection requires 'database_type' in config")
		}
		return NewValidationError("database_type must be a string")
	}

	validTypes := []DatabaseType{
		DatabaseTypeMySQL, DatabaseTypePostgreSQL, DatabaseTypeSQLServer,
		DatabaseTypeOracle, DatabaseTypeSQLite, DatabaseTypeClickHouse,
	}

	isValid := false
	for _, validType := range validTypes {
		if DatabaseType(dbType) == validType {
			isValid = true
			break
		}
	}

	if !isValid {
		return NewValidationErrorf("unsupported database type: %s", dbType)
	}

	// 根据数据库类型验证必需字段
	switch DatabaseType(dbType) {
	case DatabaseTypeSQLite:
		// SQLite只需要database字段
		if c.Config["database"] == nil {
			return NewValidationError("sqlite connection requires 'database' in config")
		}
	default:
		// 其他数据库需要完整的连接参数
		requiredFields := []string{"hostname", "port", "database", "username"}
		for _, field := range requiredFields {
			if c.Config[field] == nil {
				return NewValidationErrorf("database connection requires '%s' in config", field)
			}
		}
	}

	return nil
}

// validateRedisConfig 验证Redis配置
func (c *ConnectionMeta) validateRedisConfig() error {
	requiredFields := []string{"hostname", "port"}

	for _, field := range requiredFields {
		if c.Config[field] == nil {
			return NewValidationErrorf("redis connection requires '%s' in config", field)
		}
	}

	return nil
}

// validateKafkaConfig 验证Kafka配置
func (c *ConnectionMeta) validateKafkaConfig() error {
	if c.Config["brokers"] == nil {
		return NewValidationError("kafka connection requires 'brokers' in config")
	}

	return nil
}

// validateElasticsearchConfig 验证Elasticsearch配置
func (c *ConnectionMeta) validateElasticsearchConfig() error {
	if c.Config["hosts"] == nil {
		return NewValidationError("elasticsearch connection requires 'hosts' in config")
	}

	return nil
}

// GetConnectionString 获取连接字符串
func (c *ConnectionMeta) GetConnectionString() (string, error) {
	switch c.Type {
	case ConnectionTypeDatabase:
		return c.getDatabaseConnectionString()
	case ConnectionTypeRedis:
		return c.getRedisConnectionString()
	default:
		return "", NewValidationErrorf("unsupported connection type for connection string: %s", c.Type)
	}
}

// getDatabaseConnectionString 获取数据库连接字符串
func (c *ConnectionMeta) getDatabaseConnectionString() (string, error) {
	dbType, _ := c.Config["database_type"].(string)
	hostname, _ := c.Config["hostname"].(string)
	port, _ := c.Config["port"].(string)
	database, _ := c.Config["database"].(string)
	username, _ := c.Config["username"].(string)
	password, _ := c.Config["password"].(string)

	switch DatabaseType(dbType) {
	case DatabaseTypeMySQL:
		return buildMySQLConnectionString(hostname, port, database, username, password), nil
	case DatabaseTypePostgreSQL:
		return buildPostgreSQLConnectionString(hostname, port, database, username, password), nil
	case DatabaseTypeSQLite:
		return database, nil
	default:
		return "", NewValidationErrorf("unsupported database type for connection string: %s", dbType)
	}
}

// getRedisConnectionString 获取Redis连接字符串
func (c *ConnectionMeta) getRedisConnectionString() (string, error) {
	hostname, _ := c.Config["hostname"].(string)
	port, _ := c.Config["port"].(string)
	password, hasPassword := c.Config["password"].(string)

	if hasPassword && password != "" {
		return "redis://:" + password + "@" + hostname + ":" + port, nil
	}
	return "redis://" + hostname + ":" + port, nil
}

// buildMySQLConnectionString 构建MySQL连接字符串
func buildMySQLConnectionString(hostname, port, database, username, password string) string {
	return username + ":" + password + "@tcp(" + hostname + ":" + port + ")/" + database
}

// buildPostgreSQLConnectionString 构建PostgreSQL连接字符串
func buildPostgreSQLConnectionString(hostname, port, database, username, password string) string {
	return "postgres://" + username + ":" + password + "@" + hostname + ":" + port + "/" + database
}

// Validate 验证连接配置集合
func (c *ConnectionsConfig) Validate() error {
	if len(c.Connections) == 0 {
		return NewValidationError("at least one connection is required")
	}

	names := make(map[string]bool)

	for _, conn := range c.Connections {
		if names[conn.Name] {
			return NewValidationErrorf("duplicate connection name: %s", conn.Name)
		}
		names[conn.Name] = true

		if err := conn.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// GetConnectionByName 根据名称获取连接
func (c *ConnectionsConfig) GetConnectionByName(name string) *ConnectionMeta {
	for i := range c.Connections {
		if c.Connections[i].Name == name {
			return &c.Connections[i]
		}
	}
	return nil
}
