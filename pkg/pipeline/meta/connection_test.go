package meta

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConnectionMeta_Validate(t *testing.T) {
	tests := []struct {
		name      string
		conn      *ConnectionMeta
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid mysql database connection",
			conn: &ConnectionMeta{
				Name:        "mysql_conn",
				Type:        ConnectionTypeDatabase,
				Description: "MySQL database connection",
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
					"port":          "3306",
					"database":      "test_db",
					"username":      "user",
					"password":      "password",
				},
			},
			wantError: false,
		},
		{
			name: "valid postgresql database connection",
			conn: &ConnectionMeta{
				Name:        "pg_conn",
				Type:        ConnectionTypeDatabase,
				Description: "PostgreSQL database connection",
				Config: map[string]interface{}{
					"database_type": "postgresql",
					"hostname":      "localhost",
					"port":          "5432",
					"database":      "test_db",
					"username":      "user",
					"password":      "password",
				},
			},
			wantError: false,
		},
		{
			name: "valid redis connection",
			conn: &ConnectionMeta{
				Name:        "redis_conn",
				Type:        ConnectionTypeRedis,
				Description: "Redis connection",
				Config: map[string]interface{}{
					"hostname": "localhost",
					"port":     "6379",
					"password": "redis_password",
					"database": 0,
				},
			},
			wantError: false,
		},
		{
			name: "valid kafka connection",
			conn: &ConnectionMeta{
				Name:        "kafka_conn",
				Type:        ConnectionTypeKafka,
				Description: "Kafka connection",
				Config: map[string]interface{}{
					"brokers": []string{"localhost:9092", "localhost:9093"},
				},
			},
			wantError: false,
		},
		{
			name: "valid elasticsearch connection",
			conn: &ConnectionMeta{
				Name:        "es_conn",
				Type:        ConnectionTypeElasticsearch,
				Description: "Elasticsearch connection",
				Config: map[string]interface{}{
					"hosts": []string{"http://localhost:9200"},
				},
			},
			wantError: false,
		},
		{
			name: "empty connection name",
			conn: &ConnectionMeta{
				Name: "",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
				},
			},
			wantError: true,
			errorMsg:  "connection name is required",
		},
		{
			name: "empty connection type",
			conn: &ConnectionMeta{
				Name:   "test_conn",
				Type:   "",
				Config: map[string]interface{}{},
			},
			wantError: true,
			errorMsg:  "connection type is required",
		},
		{
			name: "database connection missing database_type",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"hostname": "localhost",
					"port":     "3306",
					"database": "test_db",
					"username": "user",
				},
			},
			wantError: true,
			errorMsg:  "database connection requires 'database_type' in config",
		},
		{
			name: "database connection missing hostname",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"port":          "3306",
					"database":      "test_db",
					"username":      "user",
				},
			},
			wantError: true,
			errorMsg:  "database connection requires 'hostname' in config",
		},
		{
			name: "database connection missing port",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
					"database":      "test_db",
					"username":      "user",
				},
			},
			wantError: true,
			errorMsg:  "database connection requires 'port' in config",
		},
		{
			name: "database connection missing database",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
					"port":          "3306",
					"username":      "user",
				},
			},
			wantError: true,
			errorMsg:  "database connection requires 'database' in config",
		},
		{
			name: "database connection missing username",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
					"port":          "3306",
					"database":      "test_db",
				},
			},
			wantError: true,
			errorMsg:  "database connection requires 'username' in config",
		},
		{
			name: "database connection with invalid database type",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "invalid_db",
					"hostname":      "localhost",
					"port":          "3306",
					"database":      "test_db",
					"username":      "user",
				},
			},
			wantError: true,
			errorMsg:  "unsupported database type: invalid_db",
		},
		{
			name: "database connection with non-string database type",
			conn: &ConnectionMeta{
				Name: "db_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": 123,
					"hostname":      "localhost",
					"port":          "3306",
					"database":      "test_db",
					"username":      "user",
				},
			},
			wantError: true,
			errorMsg:  "database_type must be a string",
		},
		{
			name: "redis connection missing hostname",
			conn: &ConnectionMeta{
				Name: "redis_conn",
				Type: ConnectionTypeRedis,
				Config: map[string]interface{}{
					"port": "6379",
				},
			},
			wantError: true,
			errorMsg:  "redis connection requires 'hostname' in config",
		},
		{
			name: "redis connection missing port",
			conn: &ConnectionMeta{
				Name: "redis_conn",
				Type: ConnectionTypeRedis,
				Config: map[string]interface{}{
					"hostname": "localhost",
				},
			},
			wantError: true,
			errorMsg:  "redis connection requires 'port' in config",
		},
		{
			name: "kafka connection missing brokers",
			conn: &ConnectionMeta{
				Name:   "kafka_conn",
				Type:   ConnectionTypeKafka,
				Config: map[string]interface{}{},
			},
			wantError: true,
			errorMsg:  "kafka connection requires 'brokers' in config",
		},
		{
			name: "elasticsearch connection missing hosts",
			conn: &ConnectionMeta{
				Name:   "es_conn",
				Type:   ConnectionTypeElasticsearch,
				Config: map[string]interface{}{},
			},
			wantError: true,
			errorMsg:  "elasticsearch connection requires 'hosts' in config",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.conn.Validate()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConnectionMeta_GetConnectionString(t *testing.T) {
	tests := []struct {
		name           string
		conn           *ConnectionMeta
		expectedResult string
		wantError      bool
		errorMsg       string
	}{
		{
			name: "mysql connection string",
			conn: &ConnectionMeta{
				Name: "mysql_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
					"port":          "3306",
					"database":      "test_db",
					"username":      "user",
					"password":      "password",
				},
			},
			expectedResult: "user:password@tcp(localhost:3306)/test_db",
			wantError:      false,
		},
		{
			name: "postgresql connection string",
			conn: &ConnectionMeta{
				Name: "pg_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "postgresql",
					"hostname":      "localhost",
					"port":          "5432",
					"database":      "test_db",
					"username":      "user",
					"password":      "password",
				},
			},
			expectedResult: "postgres://user:password@localhost:5432/test_db",
			wantError:      false,
		},
		{
			name: "redis connection string",
			conn: &ConnectionMeta{
				Name: "redis_conn",
				Type: ConnectionTypeRedis,
				Config: map[string]interface{}{
					"hostname": "localhost",
					"port":     "6379",
					"password": "redis_password",
				},
			},
			expectedResult: "redis://:redis_password@localhost:6379",
			wantError:      false,
		},
		{
			name: "redis connection string without password",
			conn: &ConnectionMeta{
				Name: "redis_conn",
				Type: ConnectionTypeRedis,
				Config: map[string]interface{}{
					"hostname": "localhost",
					"port":     "6379",
				},
			},
			expectedResult: "redis://localhost:6379",
			wantError:      false,
		},
		{
			name: "unsupported connection type",
			conn: &ConnectionMeta{
				Name: "file_conn",
				Type: ConnectionTypeFile,
				Config: map[string]interface{}{
					"path": "/tmp",
				},
			},
			wantError: true,
			errorMsg:  "unsupported connection type for connection string: FILE",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := tt.conn.GetConnectionString()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

func TestConnectionsConfig_Validate(t *testing.T) {
	tests := []struct {
		name      string
		config    *ConnectionsConfig
		wantError bool
		errorMsg  string
	}{
		{
			name: "valid connections config",
			config: &ConnectionsConfig{
				Connections: []ConnectionMeta{
					{
						Name: "mysql_conn",
						Type: ConnectionTypeDatabase,
						Config: map[string]interface{}{
							"database_type": "mysql",
							"hostname":      "localhost",
							"port":          "3306",
							"database":      "test_db",
							"username":      "user",
							"password":      "password",
						},
					},
					{
						Name: "redis_conn",
						Type: ConnectionTypeRedis,
						Config: map[string]interface{}{
							"hostname": "localhost",
							"port":     "6379",
						},
					},
				},
			},
			wantError: false,
		},
		{
			name: "empty connections",
			config: &ConnectionsConfig{
				Connections: []ConnectionMeta{},
			},
			wantError: true,
			errorMsg:  "at least one connection is required",
		},
		{
			name: "duplicate connection names",
			config: &ConnectionsConfig{
				Connections: []ConnectionMeta{
					{
						Name: "duplicate_conn",
						Type: ConnectionTypeDatabase,
						Config: map[string]interface{}{
							"database_type": "mysql",
							"hostname":      "localhost",
							"port":          "3306",
							"database":      "test_db",
							"username":      "user",
						},
					},
					{
						Name: "duplicate_conn",
						Type: ConnectionTypeRedis,
						Config: map[string]interface{}{
							"hostname": "localhost",
							"port":     "6379",
						},
					},
				},
			},
			wantError: true,
			errorMsg:  "duplicate connection name: duplicate_conn",
		},
		{
			name: "invalid connection in config",
			config: &ConnectionsConfig{
				Connections: []ConnectionMeta{
					{
						Name: "", // invalid - empty name
						Type: ConnectionTypeDatabase,
						Config: map[string]interface{}{
							"database_type": "mysql",
						},
					},
				},
			},
			wantError: true,
			errorMsg:  "connection name is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.wantError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestConnectionsConfig_GetConnectionByName(t *testing.T) {
	config := &ConnectionsConfig{
		Connections: []ConnectionMeta{
			{
				Name: "mysql_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": "mysql",
					"hostname":      "localhost",
				},
			},
			{
				Name: "redis_conn",
				Type: ConnectionTypeRedis,
				Config: map[string]interface{}{
					"hostname": "localhost",
					"port":     "6379",
				},
			},
		},
	}

	// Test existing connection
	conn := config.GetConnectionByName("mysql_conn")
	require.NotNil(t, conn)
	assert.Equal(t, "mysql_conn", conn.Name)
	assert.Equal(t, ConnectionTypeDatabase, conn.Type)

	// Test another existing connection
	conn = config.GetConnectionByName("redis_conn")
	require.NotNil(t, conn)
	assert.Equal(t, "redis_conn", conn.Name)
	assert.Equal(t, ConnectionTypeRedis, conn.Type)

	// Test non-existing connection
	conn = config.GetConnectionByName("nonexistent")
	assert.Nil(t, conn)
}

func TestDatabaseConnectionTypes(t *testing.T) {
	validDatabaseTypes := []DatabaseType{
		DatabaseTypeMySQL,
		DatabaseTypePostgreSQL,
		DatabaseTypeSQLServer,
		DatabaseTypeOracle,
		DatabaseTypeSQLite,
		DatabaseTypeClickHouse,
	}

	for _, dbType := range validDatabaseTypes {
		t.Run(string(dbType), func(t *testing.T) {
			conn := &ConnectionMeta{
				Name: "test_conn",
				Type: ConnectionTypeDatabase,
				Config: map[string]interface{}{
					"database_type": string(dbType),
					"hostname":      "localhost",
					"port":          "3306",
					"database":      "test_db",
					"username":      "user",
				},
			}

			err := conn.Validate()
			assert.NoError(t, err)
		})
	}
}

func TestConnectionConfigWithAdditionalProperties(t *testing.T) {
	conn := &ConnectionMeta{
		Name:        "mysql_advanced",
		Type:        ConnectionTypeDatabase,
		Description: "MySQL connection with advanced configuration",
		Config: map[string]interface{}{
			"database_type": "mysql",
			"hostname":      "localhost",
			"port":          "3306",
			"database":      "test_db",
			"username":      "user",
			"password":      "password",
			"connection_pool": map[string]interface{}{
				"max_connections":    20,
				"min_connections":    5,
				"connection_timeout": "30s",
				"idle_timeout":       "300s",
			},
			"properties": map[string]interface{}{
				"useSSL":         "true",
				"serverTimezone": "Asia/Shanghai",
				"charset":        "utf8mb4",
			},
		},
	}

	err := conn.Validate()
	assert.NoError(t, err)

	// Verify additional properties are preserved
	connectionPool := conn.Config["connection_pool"]
	assert.NotNil(t, connectionPool)

	properties := conn.Config["properties"]
	assert.NotNil(t, properties)
}

func TestRedisConnectionWithDatabase(t *testing.T) {
	conn := &ConnectionMeta{
		Name: "redis_with_db",
		Type: ConnectionTypeRedis,
		Config: map[string]interface{}{
			"hostname": "localhost",
			"port":     "6379",
			"password": "password",
			"database": 5,
			"timeout":  "5s",
		},
	}

	err := conn.Validate()
	assert.NoError(t, err)

	// Test connection string generation
	connStr, err := conn.GetConnectionString()
	assert.NoError(t, err)
	assert.Equal(t, "redis://:password@localhost:6379", connStr)
}

func TestKafkaConnectionWithSecurity(t *testing.T) {
	conn := &ConnectionMeta{
		Name: "kafka_secure",
		Type: ConnectionTypeKafka,
		Config: map[string]interface{}{
			"brokers": []string{"kafka1:9092", "kafka2:9092", "kafka3:9092"},
			"security": map[string]interface{}{
				"protocol":  "SASL_SSL",
				"mechanism": "PLAIN",
				"username":  "user",
				"password":  "password",
			},
			"consumer": map[string]interface{}{
				"group_id":          "my-group",
				"auto_offset_reset": "earliest",
			},
		},
	}

	err := conn.Validate()
	assert.NoError(t, err)

	// Verify complex configuration is preserved
	security := conn.Config["security"]
	assert.NotNil(t, security)

	consumer := conn.Config["consumer"]
	assert.NotNil(t, consumer)
}

func TestElasticsearchConnectionWithAuth(t *testing.T) {
	conn := &ConnectionMeta{
		Name: "es_with_auth",
		Type: ConnectionTypeElasticsearch,
		Config: map[string]interface{}{
			"hosts": []string{"https://es1:9200", "https://es2:9200"},
			"auth": map[string]interface{}{
				"username": "elastic",
				"password": "password",
			},
			"tls": map[string]interface{}{
				"skip_verify": false,
				"ca_file":     "/path/to/ca.crt",
			},
		},
	}

	err := conn.Validate()
	assert.NoError(t, err)

	// Verify authentication and TLS configuration
	auth := conn.Config["auth"]
	assert.NotNil(t, auth)

	tls := conn.Config["tls"]
	assert.NotNil(t, tls)
}
