package meta

import "fmt"

// ValidationError 验证错误
type ValidationError struct {
	Message string
}

// NewValidationError 创建验证错误
func NewValidationError(message string) error {
	return &ValidationError{
		Message: message,
	}
}

// NewValidationErrorf 创建带格式的验证错误
func NewValidationErrorf(format string, args ...interface{}) error {
	return &ValidationError{
		Message: fmt.Sprintf(format, args...),
	}
}

// Error 实现error接口
func (e *ValidationError) Error() string {
	return e.Message
}

// Repository 元数据仓库接口
type Repository interface {
	// Job相关方法
	SaveJob(job *JobMeta) error
	LoadJob(name string) (*JobMeta, error)
	DeleteJob(name string) error
	ListJobs() ([]string, error)

	// Transformation相关方法
	SaveTransformation(trans *TransformationMeta) error
	LoadTransformation(name string) (*TransformationMeta, error)
	DeleteTransformation(name string) error
	ListTransformations() ([]string, error)

	// Connection相关方法
	SaveConnections(connections *ConnectionsConfig) error
	LoadConnections() (*ConnectionsConfig, error)
	SaveConnection(connection *ConnectionMeta) error
	LoadConnection(name string) (*ConnectionMeta, error)
	DeleteConnection(name string) error
	ListConnections() ([]string, error)

	// 通用方法
	Exists(objectType, name string) bool
	Close() error
}

// ObjectType 对象类型
type ObjectType string

const (
	ObjectTypeJob            ObjectType = "job"
	ObjectTypeTransformation ObjectType = "transformation"
	ObjectTypeConnection     ObjectType = "connection"
)

// RepositoryConfig 仓库配置
type RepositoryConfig struct {
	Type   string                 `json:"type" yaml:"type"`
	Config map[string]interface{} `json:"config" yaml:"config"`
}

// Validate 验证仓库配置
func (r *RepositoryConfig) Validate() error {
	if r.Type == "" {
		return NewValidationError("repository type is required")
	}

	validTypes := []string{"file", "database", "memory"}
	isValid := false
	for _, validType := range validTypes {
		if r.Type == validType {
			isValid = true
			break
		}
	}

	if !isValid {
		return NewValidationErrorf("unsupported repository type: %s", r.Type)
	}

	return nil
}
