{"job": {"meta": {"name": "comprehensive_etl_test", "description": "全面的ETL流程测试，涵盖数据提取、转换、加载和质量检查", "version": "1.0.0", "author": "ETL Test Team", "created": "2025-07-07T02:00:00Z", "modified": "2025-07-07T02:00:00Z"}, "parameters": {"source_db": "user_source", "target_db": "user_warehouse", "batch_date": "2025-07-07", "batch_size": "1000", "error_threshold": "0.05", "retry_count": "3", "timeout": "1800"}, "variables": {"log_level": "INFO", "temp_dir": "/Volumes/data/Code/Go/src/admin/temp", "backup_dir": "/Volumes/data/Code/Go/src/admin/backup", "notification_email": "<EMAIL>", "max_memory": "2GB", "parallel_workers": "4"}, "entries": [{"name": "START", "type": "START", "description": "ETL作业开始", "position": {"x": 100, "y": 100}}, {"name": "init_environment", "type": "SCRIPT", "description": "初始化执行环境", "config": {"script_type": "shell", "script": "mkdir -p ${temp_dir} ${backup_dir} && echo 'Environment initialized at $(date)' > ${temp_dir}/init.log", "timeout": "30s", "working_directory": "/tmp"}, "position": {"x": 300, "y": 100}}, {"name": "validate_connections", "type": "SCRIPT", "description": "验证数据库连接", "config": {"script_type": "sql", "connection": "${source_db}", "script": "SELECT 1 as connection_test, NOW() as test_time", "timeout": "10s"}, "position": {"x": 500, "y": 100}}, {"name": "check_source_data", "type": "SCRIPT", "description": "检查源数据状态", "config": {"script_type": "sql", "connection": "${source_db}", "script": "SELECT COUNT(*) as total_records, COUNT(CASE WHEN updated_at >= '${batch_date}' THEN 1 END) as new_records, MAX(updated_at) as last_update FROM users", "timeout": "30s"}, "position": {"x": 700, "y": 100}}, {"name": "evaluate_data_volume", "type": "EVAL", "description": "评估数据量是否需要处理", "config": {"condition": "${check_source_data.sql_result.new_records} > 0", "success_message": "发现 ${check_source_data.sql_result.new_records} 条新记录，开始处理", "failure_message": "没有新数据需要处理，跳过ETL流程"}, "position": {"x": 900, "y": 100}}, {"name": "backup_previous_data", "type": "SCRIPT", "description": "备份之前的数据", "config": {"script_type": "sql", "connection": "${target_db}", "script": "CREATE TABLE users_backup_${batch_date} AS SELECT * FROM users_processed WHERE process_date = '${batch_date}'", "timeout": "300s", "ignore_errors": true}, "position": {"x": 1100, "y": 100}}, {"name": "extract_user_data", "type": "TRANS", "description": "提取用户数据转换", "config": {"transformation_file": "transformations/extract_users.json", "parameters": {"source_db": "${source_db}", "batch_date": "${batch_date}", "batch_size": "${batch_size}"}, "timeout": "${timeout}s"}, "position": {"x": 1300, "y": 100}}, {"name": "validate_extracted_data", "type": "EVAL", "description": "验证提取的数据质量", "config": {"condition": "${extract_user_data.rows_read} > 0 AND ${extract_user_data.error_rate} < ${error_threshold}", "success_message": "数据提取成功，共 ${extract_user_data.rows_read} 条记录，错误率 ${extract_user_data.error_rate}", "failure_message": "数据提取失败或质量不达标"}, "position": {"x": 1500, "y": 100}}, {"name": "transform_user_data", "type": "TRANS", "description": "转换用户数据", "config": {"transformation_file": "transformations/transform_users.json", "parameters": {"target_db": "${target_db}", "batch_size": "${batch_size}", "parallel_workers": "${parallel_workers}"}, "timeout": "${timeout}s"}, "position": {"x": 1700, "y": 100}}, {"name": "load_to_warehouse", "type": "TRANS", "description": "加载数据到数据仓库", "config": {"transformation_file": "transformations/load_users.json", "parameters": {"target_db": "${target_db}", "batch_date": "${batch_date}", "upsert_mode": "true"}, "timeout": "${timeout}s"}, "position": {"x": 1900, "y": 100}}, {"name": "data_quality_check", "type": "SCRIPT", "description": "数据质量检查", "config": {"script_type": "sql", "connection": "${target_db}", "script": "SELECT COUNT(*) as loaded_records, COUNT(CASE WHEN email IS NULL OR email = '' THEN 1 END) as invalid_emails, COUNT(CASE WHEN phone IS NULL OR phone = '' THEN 1 END) as invalid_phones FROM users_processed WHERE process_date = '${batch_date}'", "timeout": "60s"}, "position": {"x": 2100, "y": 100}}, {"name": "generate_summary_report", "type": "SCRIPT", "description": "生成处理摘要报告", "config": {"script_type": "shell", "script": "cat > ${temp_dir}/etl_summary_${batch_date}.txt << EOF\nETL处理摘要报告\n==================\n处理日期: ${batch_date}\n源记录数: ${check_source_data.sql_result.new_records}\n提取记录数: ${extract_user_data.rows_read}\n转换记录数: ${transform_user_data.rows_written}\n加载记录数: ${load_to_warehouse.rows_written}\n数据质量:\n  - 无效邮箱: ${data_quality_check.sql_result.invalid_emails}\n  - 无效电话: ${data_quality_check.sql_result.invalid_phones}\n处理完成时间: $(date)\nEOF", "timeout": "30s"}, "position": {"x": 2300, "y": 100}}, {"name": "send_success_notification", "type": "MAIL", "description": "发送成功通知", "config": {"smtp_server": "smtp.company.com:587", "username": "<EMAIL>", "password": "${SMTP_PASSWORD}", "use_tls": true, "to": ["${notification_email}"], "cc": ["<EMAIL>"], "subject": "ETL作业成功完成 - ${batch_date}", "body": "ETL作业已成功完成。\n\n处理摘要:\n- 处理日期: ${batch_date}\n- 源记录数: ${check_source_data.sql_result.new_records}\n- 最终加载记录数: ${load_to_warehouse.rows_written}\n- 数据质量检查通过\n\n详细报告请查看附件。", "attachments": ["${temp_dir}/etl_summary_${batch_date}.txt"]}, "position": {"x": 2500, "y": 100}}, {"name": "cleanup_temp_files", "type": "SCRIPT", "description": "清理临时文件", "config": {"script_type": "shell", "script": "find ${temp_dir} -name '*.tmp' -mtime +1 -delete && echo 'Cleanup completed at $(date)'", "timeout": "60s", "ignore_errors": true}, "position": {"x": 2700, "y": 100}}, {"name": "update_job_status", "type": "SCRIPT", "description": "更新作业状态", "config": {"script_type": "sql", "connection": "${target_db}", "script": "INSERT INTO etl_job_log (job_name, batch_date, status, start_time, end_time, records_processed, created_at) VALUES ('${job.name}', '${batch_date}', 'SUCCESS', '${job.start_time}', NOW(), ${load_to_warehouse.rows_written}, NOW())", "timeout": "30s"}, "position": {"x": 2900, "y": 100}}, {"name": "SUCCESS", "type": "SUCCESS", "description": "作业成功完成", "position": {"x": 3100, "y": 100}}, {"name": "handle_no_data", "type": "SCRIPT", "description": "处理无数据情况", "config": {"script_type": "sql", "connection": "${target_db}", "script": "INSERT INTO etl_job_log (job_name, batch_date, status, start_time, end_time, records_processed, message, created_at) VALUES ('${job.name}', '${batch_date}', 'SKIPPED', '${job.start_time}', NOW(), 0, 'No new data to process', NOW())", "timeout": "30s"}, "position": {"x": 900, "y": 300}}, {"name": "send_no_data_notification", "type": "MAIL", "description": "发送无数据通知", "config": {"smtp_server": "smtp.company.com:587", "username": "<EMAIL>", "password": "${SMTP_PASSWORD}", "use_tls": true, "to": ["${notification_email}"], "subject": "ETL作业跳过 - ${batch_date} (无新数据)", "body": "ETL作业检查完成，${batch_date} 没有新数据需要处理。\n\n源数据状态:\n- 总记录数: ${check_source_data.sql_result.total_records}\n- 新记录数: ${check_source_data.sql_result.new_records}\n- 最后更新时间: ${check_source_data.sql_result.last_update}"}, "position": {"x": 1100, "y": 300}}, {"name": "SKIP_SUCCESS", "type": "SUCCESS", "description": "跳过处理成功", "position": {"x": 1300, "y": 300}}, {"name": "send_error_notification", "type": "MAIL", "description": "发送错误通知", "config": {"smtp_server": "smtp.company.com:587", "username": "<EMAIL>", "password": "${SMTP_PASSWORD}", "use_tls": true, "to": ["${notification_email}"], "cc": ["<EMAIL>"], "subject": "ETL作业失败 - ${batch_date}", "body": "ETL作业执行失败，请立即检查。\n\n错误信息:\n${error.message}\n\n失败步骤: ${error.step}\n失败时间: ${error.time}\n\n请查看日志文件获取详细信息。", "priority": "high"}, "position": {"x": 1500, "y": 500}}, {"name": "rollback_changes", "type": "SCRIPT", "description": "回滚数据变更", "config": {"script_type": "sql", "connection": "${target_db}", "script": "DELETE FROM users_processed WHERE process_date = '${batch_date}'; INSERT INTO users_processed SELECT * FROM users_backup_${batch_date} WHERE process_date = '${batch_date}';", "timeout": "300s", "ignore_errors": true}, "position": {"x": 1700, "y": 500}}, {"name": "log_error_details", "type": "SCRIPT", "description": "记录错误详情", "config": {"script_type": "sql", "connection": "${target_db}", "script": "INSERT INTO etl_job_log (job_name, batch_date, status, start_time, end_time, error_message, error_step, created_at) VALUES ('${job.name}', '${batch_date}', 'FAILED', '${job.start_time}', NOW(), '${error.message}', '${error.step}', NOW())", "timeout": "30s", "ignore_errors": true}, "position": {"x": 1900, "y": 500}}, {"name": "ERROR", "type": "ERROR", "description": "作业错误结束", "position": {"x": 2100, "y": 500}}], "hops": [{"from": "START", "to": "init_environment", "evaluation": true, "unconditional": true}, {"from": "init_environment", "to": "validate_connections", "evaluation": true, "unconditional": false}, {"from": "validate_connections", "to": "check_source_data", "evaluation": true, "unconditional": false}, {"from": "check_source_data", "to": "evaluate_data_volume", "evaluation": true, "unconditional": false}, {"from": "evaluate_data_volume", "to": "backup_previous_data", "evaluation": true, "condition": "${check_source_data.sql_result.new_records} > 0"}, {"from": "evaluate_data_volume", "to": "handle_no_data", "evaluation": false, "condition": "${check_source_data.sql_result.new_records} == 0"}, {"from": "backup_previous_data", "to": "extract_user_data", "evaluation": true, "unconditional": false}, {"from": "extract_user_data", "to": "validate_extracted_data", "evaluation": true, "unconditional": false}, {"from": "validate_extracted_data", "to": "transform_user_data", "evaluation": true, "unconditional": false}, {"from": "transform_user_data", "to": "load_to_warehouse", "evaluation": true, "unconditional": false}, {"from": "load_to_warehouse", "to": "data_quality_check", "evaluation": true, "unconditional": false}, {"from": "data_quality_check", "to": "generate_summary_report", "evaluation": true, "unconditional": false}, {"from": "generate_summary_report", "to": "send_success_notification", "evaluation": true, "unconditional": false}, {"from": "send_success_notification", "to": "cleanup_temp_files", "evaluation": true, "unconditional": false}, {"from": "cleanup_temp_files", "to": "update_job_status", "evaluation": true, "unconditional": false}, {"from": "update_job_status", "to": "SUCCESS", "evaluation": true, "unconditional": false}, {"from": "handle_no_data", "to": "send_no_data_notification", "evaluation": true, "unconditional": false}, {"from": "send_no_data_notification", "to": "SKIP_SUCCESS", "evaluation": true, "unconditional": false}, {"from": "init_environment", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "validate_connections", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "check_source_data", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "backup_previous_data", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "extract_user_data", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "validate_extracted_data", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "transform_user_data", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "load_to_warehouse", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "data_quality_check", "to": "send_error_notification", "evaluation": false, "unconditional": false}, {"from": "send_error_notification", "to": "rollback_changes", "evaluation": true, "unconditional": false}, {"from": "rollback_changes", "to": "log_error_details", "evaluation": true, "unconditional": false}, {"from": "log_error_details", "to": "ERROR", "evaluation": true, "unconditional": false}]}}