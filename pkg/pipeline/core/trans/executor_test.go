package trans

import (
	"context"
	"testing"
	"time"

	"admin/pkg/pipeline/core"
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewExecutor(t *testing.T) {
	registry := step.NewRegistry()
	transformation := meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name:        "test-transformation",
			Description: "Test transformation",
			Version:     "1.0.0",
		},
		Steps: []meta.StepMeta{},
	}

	executor := NewExecutor(transformation, registry)

	assert.NotNil(t, executor)
	assert.Equal(t, transformation, executor.transformation)
	assert.Equal(t, registry, executor.stepRegistry)
	assert.Empty(t, executor.steps)
}

func TestExecutor_Initialize(t *testing.T) {
	tests := []struct {
		name           string
		transformation meta.TransformationMeta
		setupRegistry  func(*step.Registry)
		expectError    bool
		errorContains  string
		expectedSteps  int
	}{
		{
			name: "successful initialization with basic steps",
			transformation: meta.TransformationMeta{
				Meta: meta.TransMetaInfo{
					Name:        "basic-test",
					Description: "Basic transformation test",
				},
				Steps: []meta.StepMeta{
					{
						Name: "input_step",
						Type: meta.StepTypeDummyInput,
						Config: map[string]interface{}{
							"row_count": 10,
						},
					},
					{
						Name: "transform_step",
						Type: meta.StepTypeCalculator,
						Config: map[string]interface{}{
							"calculations": []interface{}{
								map[string]interface{}{
									"target_field": "id",
									"expression":   "row_number()",
									"data_type":    "int",
								},
							},
						},
					},
					{
						Name: "output_step",
						Type: meta.StepTypeDummyOutput,
						Config: map[string]interface{}{
							"print_rows": true,
						},
					},
				},
			},
			setupRegistry: func(registry *step.Registry) {
				core.RegisterBasicSteps(registry)
			},
			expectError:   false,
			expectedSteps: 3,
		},
		{
			name: "unknown step type",
			transformation: meta.TransformationMeta{
				Meta: meta.TransMetaInfo{
					Name: "unknown-step-test",
				},
				Steps: []meta.StepMeta{
					{
						Name:   "unknown_step",
						Type:   "UnknownStepType",
						Config: map[string]interface{}{},
					},
				},
			},
			setupRegistry: func(registry *step.Registry) {
				// 不注册任何步骤
			},
			expectError:   true,
			errorContains: "unknown step type",
		},
		{
			name: "empty transformation",
			transformation: meta.TransformationMeta{
				Meta: meta.TransMetaInfo{
					Name: "empty-test",
				},
				Steps: []meta.StepMeta{},
			},
			setupRegistry: func(registry *step.Registry) {
				core.RegisterBasicSteps(registry)
			},
			expectError:   false,
			expectedSteps: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			registry := step.NewRegistry()
			tt.setupRegistry(registry)

			executor := NewExecutor(tt.transformation, registry)
			err := executor.Initialize()

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
				assert.Len(t, executor.steps, tt.expectedSteps)
			}
		})
	}
}

func TestExecutor_Execute(t *testing.T) {
	tests := []struct {
		name           string
		transformation meta.TransformationMeta
		expectError    bool
		errorContains  string
	}{
		{
			name: "successful execution with simple data flow",
			transformation: meta.TransformationMeta{
				Meta: meta.TransMetaInfo{
					Name:        "simple-flow",
					Description: "Simple data flow test",
				},
				Steps: []meta.StepMeta{
					{
						Name: "input_step",
						Type: meta.StepTypeDummyInput,
						Config: map[string]interface{}{
							"row_count": 5,
							"fields": []interface{}{
								map[string]interface{}{
									"name": "id",
									"type": "Integer",
								},
								map[string]interface{}{
									"name": "name",
									"type": "String",
								},
							},
						},
					},
					{
						Name: "filter_step",
						Type: meta.StepTypeFilterRows,
						Config: map[string]interface{}{
							"conditions": []interface{}{
								map[string]interface{}{
									"field":    "id",
									"operator": "gt",
									"value":    2,
									"logic_op": "and",
								},
							},
						},
					},
					{
						Name: "output_step",
						Type: meta.StepTypeDummyOutput,
						Config: map[string]interface{}{
							"print_rows": false,
						},
					},
				},
				Hops: []meta.HopMeta{
					{From: "input_step", To: "filter_step", Enabled: true},
					{From: "filter_step", To: "output_step", Enabled: true},
				},
			},
			expectError: false,
		},
		{
			name: "execution with no steps",
			transformation: meta.TransformationMeta{
				Meta: meta.TransMetaInfo{
					Name: "no-steps",
				},
				Steps: []meta.StepMeta{},
			},
			expectError:   true,
			errorContains: "no steps to execute",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			registry := step.NewRegistry()
			core.RegisterBasicSteps(registry)

			executor := NewExecutor(tt.transformation, registry)
			err := executor.Initialize()
			require.NoError(t, err)

			ctx := context.Background()
			err = executor.Execute(ctx)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestExecutor_ExecuteWithCancellation(t *testing.T) {
	registry := step.NewRegistry()
	core.RegisterBasicSteps(registry)

	transformation := meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name: "cancellation-test",
		},
		Steps: []meta.StepMeta{
			{
				Name: "input_step",
				Type: meta.StepTypeDummyInput,
				Config: map[string]interface{}{
					"row_count": 1000,
				},
			},
			{
				Name: "output_step",
				Type: meta.StepTypeDummyOutput,
				Config: map[string]interface{}{
					"print_rows": false,
				},
			},
		},
	}

	executor := NewExecutor(transformation, registry)
	err := executor.Initialize()
	require.NoError(t, err)

	// 创建一个已经取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	err = executor.Execute(ctx)
	assert.Error(t, err)
	assert.Equal(t, context.Canceled, err)
}

func TestExecutor_Cleanup(t *testing.T) {
	registry := step.NewRegistry()
	core.RegisterBasicSteps(registry)

	transformation := meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name: "cleanup-test",
		},
		Steps: []meta.StepMeta{
			{
				Name: "input_step",
				Type: meta.StepTypeDummyInput,
				Config: map[string]interface{}{
					"row_count": 5,
				},
			},
			{
				Name: "output_step",
				Type: meta.StepTypeDummyOutput,
				Config: map[string]interface{}{
					"print_rows": false,
				},
			},
		},
	}

	executor := NewExecutor(transformation, registry)
	err := executor.Initialize()
	require.NoError(t, err)

	// 清理应该不返回错误
	err = executor.Cleanup()
	assert.NoError(t, err)
}

func TestExecutor_GetMethods(t *testing.T) {
	registry := step.NewRegistry()
	transformation := meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name:        "get-methods-test",
			Description: "Test get methods",
		},
		Steps: []meta.StepMeta{},
	}

	executor := NewExecutor(transformation, registry)

	// 测试 GetTransformation
	result := executor.GetTransformation()
	assert.Equal(t, transformation, result)

	// 测试 GetSteps（初始化前应该为空）
	steps := executor.GetSteps()
	assert.Empty(t, steps)

	// 初始化后再测试 GetSteps
	core.RegisterBasicSteps(registry)
	transformation.Steps = []meta.StepMeta{
		{
			Name: "test_step",
			Type: meta.StepTypeDummyInput,
			Config: map[string]interface{}{
				"row_count": 1,
			},
		},
	}
	executor.transformation = transformation

	err := executor.Initialize()
	require.NoError(t, err)

	steps = executor.GetSteps()
	assert.Len(t, steps, 1)
	assert.Equal(t, "test_step", steps[0].GetMeta().Name)
}

func TestExecutor_FullLifecycle(t *testing.T) {
	// 完整的生命周期测试：Initialize -> Execute -> Cleanup
	registry := step.NewRegistry()
	core.RegisterBasicSteps(registry)

	transformation := meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name:        "lifecycle-test",
			Description: "Full lifecycle test",
			Version:     "1.0.0",
			Author:      "Test Suite",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Parameters: map[string]string{
			"source_db":  "test_db",
			"batch_size": "100",
		},
		Variables: map[string]string{
			"log_level": "INFO",
			"timeout":   "30s",
		},
		Steps: []meta.StepMeta{
			{
				Name:        "data_input",
				Type:        meta.StepTypeDummyInput,
				Description: "Generate test data",
				Config: map[string]interface{}{
					"row_count": 10,
					"fields": []interface{}{
						map[string]interface{}{
							"name": "id",
							"type": "Integer",
						},
						map[string]interface{}{
							"name": "name",
							"type": "String",
						},
						map[string]interface{}{
							"name": "value",
							"type": "Number",
						},
					},
				},
				Position: meta.Position{X: 100, Y: 100},
			},
			{
				Name:        "add_calculation",
				Type:        meta.StepTypeCalculator,
				Description: "Add calculated fields",
				Config: map[string]interface{}{
					"calculations": []interface{}{
						map[string]interface{}{
							"target_field": "doubled_value",
							"expression":   "${value} * 2",
							"data_type":    "float",
						},
						map[string]interface{}{
							"target_field": "row_number",
							"expression":   "row_number()",
							"data_type":    "int",
						},
					},
				},
				Position: meta.Position{X: 300, Y: 100},
			},
			{
				Name:        "filter_data",
				Type:        meta.StepTypeFilterRows,
				Description: "Filter valid records",
				Config: map[string]interface{}{
					"conditions": []interface{}{
						map[string]interface{}{
							"field":    "id",
							"operator": "gt",
							"value":    3,
							"logic_op": "and",
						},
						map[string]interface{}{
							"field":    "value",
							"operator": "is_not_null",
							"logic_op": "and",
						},
					},
				},
				Position: meta.Position{X: 500, Y: 100},
			},
			{
				Name:        "data_output",
				Type:        meta.StepTypeDummyOutput,
				Description: "Output processed data",
				Config: map[string]interface{}{
					"print_rows":   true,
					"print_schema": true,
				},
				Position: meta.Position{X: 700, Y: 100},
			},
		},
		Hops: []meta.HopMeta{
			{From: "data_input", To: "add_calculation", Enabled: true},
			{From: "add_calculation", To: "filter_data", Enabled: true},
			{From: "filter_data", To: "data_output", Enabled: true},
		},
	}

	// 验证转换元数据
	err := transformation.Validate()
	require.NoError(t, err)

	executor := NewExecutor(transformation, registry)

	// 步骤1：初始化
	err = executor.Initialize()
	assert.NoError(t, err)
	assert.Len(t, executor.GetSteps(), 4)

	// 步骤2：执行
	ctx := context.Background()
	err = executor.Execute(ctx)
	assert.NoError(t, err)

	// 步骤3：清理
	err = executor.Cleanup()
	assert.NoError(t, err)

	// 验证获取方法
	resultTransformation := executor.GetTransformation()
	assert.Equal(t, transformation.Meta.Name, resultTransformation.Meta.Name)
	assert.Len(t, resultTransformation.Steps, 4)
}
