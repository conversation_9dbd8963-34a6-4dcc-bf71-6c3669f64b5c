package trans

import (
	"context"
	"fmt"
	"log"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// Executor 转换执行器，负责按顺序执行转换步骤
type Executor struct {
	transformation meta.TransformationMeta
	steps          []step.Step
	stepRegistry   *step.Registry
}

// NewExecutor 创建一个新的转换执行器
func NewExecutor(transformation meta.TransformationMeta, registry *step.Registry) *Executor {
	return &Executor{
		transformation: transformation,
		steps:          []step.Step{},
		stepRegistry:   registry,
	}
}

// Initialize 初始化执行器，创建并初始化所有步骤
func (e *Executor) Initialize() error {
	log.Printf("Initializing transformation: %s", e.transformation.Meta.Name)

	// 创建所有步骤实例
	for _, stepMeta := range e.transformation.Steps {
		stepInstance, err := e.stepRegistry.Create(stepMeta)
		if err != nil {
			return fmt.Errorf("failed to create step '%s': %w", stepMeta.Name, err)
		}

		// 验证步骤配置
		if err := stepInstance.Validate(); err != nil {
			return fmt.Errorf("step '%s' validation failed: %w", stepMeta.Name, err)
		}

		// 初始化步骤
		if err := stepInstance.Initialize(); err != nil {
			return fmt.Errorf("step '%s' initialization failed: %w", stepMeta.Name, err)
		}

		e.steps = append(e.steps, stepInstance)
	}

	log.Printf("Initialized %d steps for transformation", len(e.steps))
	return nil
}

// Execute 执行转换
func (e *Executor) Execute(ctx context.Context) error {
	log.Printf("Starting transformation execution: %s", e.transformation.Meta.Name)

	if len(e.steps) == 0 {
		return fmt.Errorf("no steps to execute")
	}

	// 简单的顺序执行模式（Stage 1实现）
	// 后续可以根据Hops配置实现复杂的数据流
	var currentRowSet step.RowSet = step.NewEmptyRowSet([]meta.FieldMeta{})

	for i, stepInstance := range e.steps {
		// 检查是否需要取消（在每个步骤开始前检查）
		select {
		case <-ctx.Done():
			if currentRowSet != nil {
				currentRowSet.Close()
			}
			return ctx.Err()
		default:
		}

		stepMeta := stepInstance.GetMeta()
		log.Printf("Executing step %d: %s (%s)", i+1, stepMeta.Name, stepMeta.Type)

		// 执行步骤
		outputRowSet, err := stepInstance.Execute(ctx, currentRowSet)
		if err != nil {
			// 检查是否是上下文取消错误
			if ctx.Err() != nil {
				if currentRowSet != nil {
					currentRowSet.Close()
				}
				return ctx.Err()
			}
			return fmt.Errorf("step '%s' execution failed: %w", stepMeta.Name, err)
		}

		// 关闭前一个RowSet
		if currentRowSet != nil {
			currentRowSet.Close()
		}

		currentRowSet = outputRowSet
	}

	// 关闭最后的RowSet
	if currentRowSet != nil {
		currentRowSet.Close()
	}

	log.Printf("Transformation execution completed: %s", e.transformation.Meta.Name)
	return nil
}

// Cleanup 清理资源
func (e *Executor) Cleanup() error {
	log.Printf("Cleaning up transformation: %s", e.transformation.Meta.Name)

	var lastError error
	for _, stepInstance := range e.steps {
		if err := stepInstance.Cleanup(); err != nil {
			lastError = err
			log.Printf("Error cleaning up step '%s': %v", stepInstance.GetMeta().Name, err)
		}
	}

	return lastError
}

// GetTransformation 获取转换元数据
func (e *Executor) GetTransformation() meta.TransformationMeta {
	return e.transformation
}

// GetSteps 获取步骤列表
func (e *Executor) GetSteps() []step.Step {
	return e.steps
}
