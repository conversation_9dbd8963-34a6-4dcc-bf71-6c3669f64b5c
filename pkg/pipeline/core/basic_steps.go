package core

import (
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/core/step/input"
	"admin/pkg/pipeline/core/step/output"
	"admin/pkg/pipeline/core/step/transform"
	"admin/pkg/pipeline/meta"
)

// RegisterBasicSteps 注册基础步骤类型到注册表
func RegisterBasicSteps(registry *step.Registry) {
	// 注册输入步骤
	registry.Register(meta.StepTypeDummyInput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return input.NewDummyInput(stepMeta), nil
	})

	// 注册输出步骤
	registry.Register(meta.StepTypeDummyOutput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return output.NewDummyOutput(stepMeta), nil
	})

	// 注册转换步骤
	registry.Register(meta.StepTypeSelectValues, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewSelectValues(stepMeta), nil
	})

	registry.Register(meta.StepTypeFilterRows, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewFilterRows(stepMeta), nil
	})

	registry.Register(meta.StepTypeCalculator, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewCalculator(stepMeta), nil
	})

	registry.Register(meta.StepTypeValueMapper, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewValueMapper(stepMeta), nil
	})

	registry.Register(meta.StepTypeStringOperations, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewStringOperations(stepMeta), nil
	})

	registry.Register(meta.StepTypeSort, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewSort(stepMeta), nil
	})

	registry.Register(meta.StepTypeUniqueRows, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewUniqueRows(stepMeta), nil
	})

	registry.Register(meta.StepTypeDateOps, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewDateOperations(stepMeta), nil
	})

	registry.Register(meta.StepTypeAddConstants, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewAddConstants(stepMeta), nil
	})

	registry.Register(meta.StepTypeRegexEval, func(stepMeta meta.StepMeta) (step.Step, error) {
		return transform.NewRegexEval(stepMeta), nil
	})
}

// init 自动注册基础步骤到默认注册表
func init() {
	RegisterBasicSteps(step.DefaultRegistry)
}
