package job

import (
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
	"context"
	"testing"
	"time"

	"go.uber.org/zap/zaptest"
)

// TestJobControlFlowEngine 测试Job控制流引擎
func TestJobControlFlowEngine(t *testing.T) {
	logger := zaptest.NewLogger(t)
	stepRegistry := step.NewRegistry()

	// 创建Job元数据
	jobMeta := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "test_etl_job",
			Description: "测试ETL Job控制流引擎",
			Version:     "1.0.0",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Variables: map[string]string{
			"source_table": "users",
			"target_table": "users_processed",
		},
		Parameters: map[string]string{
			"batch_size": "1000",
			"timeout":    "30s",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:        "start",
				Type:        meta.JobEntryTypeStart,
				Description: "Job开始",
				Config:      map[string]interface{}{},
			},
			{
				Name:        "check_data",
				Type:        meta.JobEntryTypeScript,
				Description: "检查数据源",
				Config: map[string]interface{}{
					"script_type": "sql",
					"script":      "SELECT COUNT(*) as count FROM users WHERE status = 'active'",
					"connection":  "test_db",
					"timeout":     "10s",
				},
			},
			{
				Name:        "eval_data_count",
				Type:        meta.JobEntryTypeEval,
				Description: "评估数据量",
				Config: map[string]interface{}{
					"condition":       "check_data.sql_rows_returned > 0",
					"success_message": "数据源验证通过",
					"failure_message": "数据源为空，跳过处理",
				},
			},
			{
				Name:        "process_data",
				Type:        meta.JobEntryTypeScript,
				Description: "处理数据",
				Config: map[string]interface{}{
					"script_type": "sql",
					"script":      "INSERT INTO users_processed SELECT * FROM users WHERE status = 'active'",
					"connection":  "test_db",
					"timeout":     "30s",
				},
			},
			{
				Name:        "send_notification",
				Type:        meta.JobEntryTypeMail,
				Description: "发送完成通知",
				Config: map[string]interface{}{
					"smtp_server": "localhost:587",
					"to":          []string{"<EMAIL>"},
					"subject":     "ETL Job完成通知",
					"body":        "Job执行成功完成，处理了 ${process_data.sql_rows_affected} 条记录",
					"username":    "<EMAIL>",
					"password":    "password",
				},
			},
			{
				Name:        "cleanup",
				Type:        meta.JobEntryTypeScript,
				Description: "清理临时数据",
				Config: map[string]interface{}{
					"script_type": "shell",
					"script":      "echo '清理临时文件...' && rm -f /tmp/etl_*.tmp",
					"timeout":     "5s",
				},
			},
			{
				Name:        "success",
				Type:        meta.JobEntryTypeSuccess,
				Description: "成功完成",
				Config:      map[string]interface{}{},
			},
			{
				Name:        "error_handler",
				Type:        meta.JobEntryTypeError,
				Description: "错误处理",
				Config:      map[string]interface{}{},
			},
		},
		Hops: []meta.JobHopMeta{
			// 正常流程
			{From: "start", To: "check_data", Evaluation: true},
			{From: "check_data", To: "eval_data_count", Evaluation: true},
			{From: "eval_data_count", To: "process_data", Evaluation: true},
			{From: "process_data", To: "send_notification", Evaluation: true},
			{From: "send_notification", To: "cleanup", Evaluation: true},
			{From: "cleanup", To: "success", Evaluation: true},

			// 错误处理流程
			{From: "check_data", To: "error_handler", Evaluation: false},
			{From: "eval_data_count", To: "error_handler", Evaluation: false},
			{From: "process_data", To: "error_handler", Evaluation: false},
			{From: "send_notification", To: "cleanup", Evaluation: false}, // 邮件失败仍执行清理
			{From: "cleanup", To: "error_handler", Evaluation: false},
		},
	}

	// 创建Job执行器
	executor := NewExecutor(jobMeta, stepRegistry, logger)

	// 添加数据库连接配置
	dbConnection := &meta.ConnectionMeta{
		Name:        "test_db",
		Type:        meta.ConnectionTypeDatabase,
		Description: "测试数据库连接",
		Config: map[string]interface{}{
			"database_type":     "sqlite",
			"database":          ":memory:",
			"max_idle_conns":    5,
			"max_open_conns":    20,
			"conn_max_lifetime": "1h",
		},
	}

	err := executor.AddDatabaseConnection(dbConnection)
	if err != nil {
		t.Fatalf("添加数据库连接失败: %v", err)
	}

	// 执行Job
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	t.Log("开始执行Job控制流引擎测试...")
	err = executor.Execute(ctx)
	if err != nil {
		t.Logf("Job执行遇到错误: %v", err)
	}

	// 检查执行结果
	status := executor.GetStatus()
	results := executor.GetResults()
	variables := executor.GetVariables()

	t.Logf("Job执行状态: %s", status.String())
	t.Logf("执行结果数量: %d", len(results))
	t.Logf("变量数量: %d", len(variables))

	// 打印详细结果
	for entryName, result := range results {
		t.Logf("条目 %s: 状态=%s, 耗时=%v",
			entryName,
			result.Status.String(),
			result.EndTime.Sub(result.StartTime))
		if result.Error != nil {
			t.Logf("  错误: %v", result.Error)
		}
	}

	// 打印关键变量
	for key, value := range variables {
		if key == "source_table" || key == "target_table" ||
			key == "check_data.sql_status" || key == "process_data.sql_status" {
			t.Logf("变量 %s = %s", key, value)
		}
	}

	// 清理资源
	err = executor.Close()
	if err != nil {
		t.Errorf("关闭执行器失败: %v", err)
	}

	t.Log("Job控制流引擎测试完成")
}

// TestJobConditionFlow 测试条件流程控制
func TestJobConditionFlow(t *testing.T) {
	logger := zaptest.NewLogger(t)
	stepRegistry := step.NewRegistry()

	// 创建包含条件分支的Job
	jobMeta := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "conditional_job",
			Description: "条件分支Job测试",
			Version:     "1.0.0",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Variables: map[string]string{
			"process_mode": "batch",
		},
		Parameters: map[string]string{},
		Entries: []meta.JobEntryMeta{
			{
				Name:   "start",
				Type:   meta.JobEntryTypeStart,
				Config: map[string]interface{}{},
			},
			{
				Name: "check_mode",
				Type: meta.JobEntryTypeEval,
				Config: map[string]interface{}{
					"condition":       "process_mode == 'batch'",
					"success_message": "批处理模式",
					"failure_message": "实时处理模式",
				},
			},
			{
				Name: "batch_process",
				Type: meta.JobEntryTypeScript,
				Config: map[string]interface{}{
					"script_type": "shell",
					"script":      "echo 'Executing batch process...'",
				},
			},
			{
				Name: "realtime_process",
				Type: meta.JobEntryTypeScript,
				Config: map[string]interface{}{
					"script_type": "shell",
					"script":      "echo 'Executing realtime process...'",
				},
			},
			{
				Name:   "success",
				Type:   meta.JobEntryTypeSuccess,
				Config: map[string]interface{}{},
			},
		},
		Hops: []meta.JobHopMeta{
			{From: "start", To: "check_mode", Evaluation: true},
			// 条件分支：批处理模式
			{
				From: "check_mode", To: "batch_process", Evaluation: true,
				Condition: "check_mode.result == 'true'",
			},
			// 条件分支：实时处理模式
			{
				From: "check_mode", To: "realtime_process", Evaluation: true,
				Condition: "check_mode.result == 'false'",
			},
			// 汇合到成功节点
			{From: "batch_process", To: "success", Evaluation: true},
			{From: "realtime_process", To: "success", Evaluation: true},
		},
	}

	executor := NewExecutor(jobMeta, stepRegistry, logger)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := executor.Execute(ctx)
	if err != nil {
		t.Errorf("Job执行失败: %v", err)
	}

	// 验证正确的分支被执行
	results := executor.GetResults()

	if _, executed := results["batch_process"]; !executed {
		t.Error("预期批处理分支被执行，但未找到执行记录")
	}

	if _, executed := results["realtime_process"]; executed {
		t.Error("实时处理分支不应被执行")
	}

	executor.Close()
}

// TestJobErrorHandling 测试错误处理流程
func TestJobErrorHandling(t *testing.T) {
	logger := zaptest.NewLogger(t)
	stepRegistry := step.NewRegistry()

	jobMeta := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "error_handling_job",
			Description: "错误处理测试",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:   "start",
				Type:   meta.JobEntryTypeStart,
				Config: map[string]interface{}{},
			},
			{
				Name: "failing_script",
				Type: meta.JobEntryTypeScript,
				Config: map[string]interface{}{
					"script_type": "shell",
					"script":      "exit 1", // 故意失败
				},
			},
			{
				Name:   "error_handler",
				Type:   meta.JobEntryTypeError,
				Config: map[string]interface{}{},
			},
			{
				Name:   "success",
				Type:   meta.JobEntryTypeSuccess,
				Config: map[string]interface{}{},
			},
		},
		Hops: []meta.JobHopMeta{
			{From: "start", To: "failing_script", Evaluation: true},
			{From: "failing_script", To: "success", Evaluation: true},
			{From: "failing_script", To: "error_handler", Evaluation: false},
		},
	}

	executor := NewExecutor(jobMeta, stepRegistry, logger)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err := executor.Execute(ctx)
	// 预期会有错误，因为脚本故意失败
	if err == nil {
		t.Error("预期Job执行失败，但返回成功")
	}

	results := executor.GetResults()

	// 验证错误处理流程被触发
	if result, exists := results["error_handler"]; !exists {
		t.Error("错误处理条目未被执行")
	} else if result.Status != StatusSuccess {
		t.Error("错误处理条目执行失败")
	}

	executor.Close()
}
