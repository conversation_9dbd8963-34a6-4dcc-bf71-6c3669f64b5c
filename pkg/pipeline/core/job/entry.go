package job

import (
	"admin/pkg/pipeline/meta"
	"context"
	"fmt"
)

// JobEntry Job条目接口
type JobEntry interface {
	// Execute 执行条目
	Execute(ctx context.Context) error

	// GetMeta 获取元数据
	GetMeta() meta.JobEntryMeta

	// Validate 验证条目配置
	Validate() error

	// Initialize 初始化条目
	Initialize() error

	// Cleanup 清理资源
	Cleanup() error
}

// BaseEntry 基础条目实现
type BaseEntry struct {
	meta     meta.JobEntryMeta
	executor *Executor
}

// GetMeta 获取元数据
func (be *BaseEntry) GetMeta() meta.JobEntryMeta {
	return be.meta
}

// Validate 验证条目配置
func (be *BaseEntry) Validate() error {
	if be.meta.Name == "" {
		return fmt.Errorf("entry name is required")
	}
	return nil
}

// Initialize 初始化条目
func (be *BaseEntry) Initialize() error {
	return nil
}

// Cleanup 清理资源
func (be *BaseEntry) Cleanup() error {
	return nil
}
