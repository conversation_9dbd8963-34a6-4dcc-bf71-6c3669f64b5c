package job

import (
	"context"
	"fmt"
	"net/smtp"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

// StartEntry 开始条目
type StartEntry struct {
	BaseEntry
}

// Execute 执行开始条目
func (se *StartEntry) Execute(ctx context.Context) error {
	se.executor.logger.Info("Job开始执行",
		zap.String("job_name", se.executor.jobMeta.Meta.Name))
	return nil
}

// SuccessEntry 成功条目
type SuccessEntry struct {
	BaseEntry
}

// Execute 执行成功条目
func (se *SuccessEntry) Execute(ctx context.Context) error {
	se.executor.logger.Info("Job执行成功完成",
		zap.String("job_name", se.executor.jobMeta.Meta.Name))
	return nil
}

// ErrorEntry 错误条目
type ErrorEntry struct {
	BaseEntry
}

// Execute 执行错误条目
func (ee *ErrorEntry) Execute(ctx context.Context) error {
	ee.executor.logger.Error("Job执行遇到错误",
		zap.String("job_name", ee.executor.jobMeta.Meta.Name))
	return nil
}

// EvalEntry 条件评估条目
type EvalEntry struct {
	BaseEntry
}

// Execute 执行条件评估
func (ee *EvalEntry) Execute(ctx context.Context) error {
	condition, exists := ee.meta.Config["condition"]
	if !exists {
		return fmt.Errorf("eval entry requires 'condition' in config")
	}

	conditionStr, ok := condition.(string)
	if !ok {
		return fmt.Errorf("condition must be a string")
	}

	// 评估条件
	result, err := ee.executor.evaluateCondition(conditionStr)
	if err != nil {
		return fmt.Errorf("failed to evaluate condition: %w", err)
	}

	// 记录结果
	ee.executor.setVariable(fmt.Sprintf("%s.result", ee.meta.Name), fmt.Sprintf("%t", result))

	if result {
		if successMsg, exists := ee.meta.Config["success_message"]; exists {
			ee.executor.logger.Info("条件评估成功",
				zap.String("entry_name", ee.meta.Name),
				zap.String("message", fmt.Sprintf("%v", successMsg)))
		}
	} else {
		if failureMsg, exists := ee.meta.Config["failure_message"]; exists {
			ee.executor.logger.Info("条件评估失败",
				zap.String("entry_name", ee.meta.Name),
				zap.String("message", fmt.Sprintf("%v", failureMsg)))
		}
		// 条件不满足时返回错误，触发错误流程
		return fmt.Errorf("evaluation condition not met: %s", conditionStr)
	}

	return nil
}

// ScriptEntry 脚本执行条目
type ScriptEntry struct {
	BaseEntry
}

// Execute 执行脚本
func (se *ScriptEntry) Execute(ctx context.Context) error {
	scriptType, exists := se.meta.Config["script_type"]
	if !exists {
		return fmt.Errorf("script entry requires 'script_type' in config")
	}

	script, exists := se.meta.Config["script"]
	if !exists {
		return fmt.Errorf("script entry requires 'script' in config")
	}

	scriptTypeStr := fmt.Sprintf("%v", scriptType)
	scriptContent := fmt.Sprintf("%v", script)

	se.executor.logger.Info("执行脚本",
		zap.String("entry_name", se.meta.Name),
		zap.String("script_type", scriptTypeStr))

	// 获取超时配置，默认30秒
	timeout := 30 * time.Second
	if timeoutConfig, exists := se.meta.Config["timeout"]; exists {
		if timeoutStr, ok := timeoutConfig.(string); ok {
			if parsedTimeout, err := time.ParseDuration(timeoutStr); err == nil {
				timeout = parsedTimeout
			}
		}
	}

	// 创建带超时的上下文
	scriptCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	var err error
	switch strings.ToLower(scriptTypeStr) {
	case "shell", "bash", "sh":
		err = se.executeShellScript(scriptCtx, scriptContent)
	case "python", "python3", "py":
		err = se.executePythonScript(scriptCtx, scriptContent)
	case "sql":
		err = se.executeSQLScript(scriptCtx, scriptContent)
	default:
		return fmt.Errorf("unsupported script type: %s", scriptTypeStr)
	}

	if err != nil {
		se.executor.logger.Error("脚本执行失败",
			zap.String("entry_name", se.meta.Name),
			zap.String("script_type", scriptTypeStr),
			zap.Error(err))
		return fmt.Errorf("script execution failed: %w", err)
	}

	se.executor.logger.Info("脚本执行成功",
		zap.String("entry_name", se.meta.Name),
		zap.String("script_type", scriptTypeStr))

	return nil
}

// executeShellScript 执行Shell脚本
func (se *ScriptEntry) executeShellScript(ctx context.Context, script string) error {
	// 创建临时脚本文件
	tmpFile, err := os.CreateTemp("", "etl_script_*.sh")
	if err != nil {
		return fmt.Errorf("failed to create temp script file: %w", err)
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// 写入脚本内容
	if _, err := tmpFile.WriteString(script); err != nil {
		return fmt.Errorf("failed to write script content: %w", err)
	}

	// 设置执行权限
	if err := os.Chmod(tmpFile.Name(), 0755); err != nil {
		return fmt.Errorf("failed to set script permissions: %w", err)
	}

	// 执行脚本
	cmd := exec.CommandContext(ctx, "bash", tmpFile.Name())

	// 设置环境变量
	cmd.Env = os.Environ()
	for key, value := range se.executor.variables {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", key, value))
	}
	for key, value := range se.executor.parameters {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", key, value))
	}

	// 执行并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("shell script execution failed: %w, output: %s", err, string(output))
	}

	// 记录输出到日志
	if len(output) > 0 {
		se.executor.logger.Info("脚本输出",
			zap.String("entry_name", se.meta.Name),
			zap.String("output", string(output)))
	}

	return nil
}

// executePythonScript 执行Python脚本
func (se *ScriptEntry) executePythonScript(ctx context.Context, script string) error {
	// 创建临时脚本文件
	tmpFile, err := os.CreateTemp("", "etl_script_*.py")
	if err != nil {
		return fmt.Errorf("failed to create temp script file: %w", err)
	}
	defer os.Remove(tmpFile.Name())
	defer tmpFile.Close()

	// 写入脚本内容
	if _, err := tmpFile.WriteString(script); err != nil {
		return fmt.Errorf("failed to write script content: %w", err)
	}

	// 检查Python命令
	pythonCmd := "python3"
	if _, err := exec.LookPath(pythonCmd); err != nil {
		pythonCmd = "python"
		if _, err := exec.LookPath(pythonCmd); err != nil {
			return fmt.Errorf("python interpreter not found")
		}
	}

	// 执行脚本
	cmd := exec.CommandContext(ctx, pythonCmd, tmpFile.Name())

	// 设置环境变量
	cmd.Env = os.Environ()
	for key, value := range se.executor.variables {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", key, value))
	}
	for key, value := range se.executor.parameters {
		cmd.Env = append(cmd.Env, fmt.Sprintf("%s=%s", key, value))
	}

	// 执行并捕获输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("python script execution failed: %w, output: %s", err, string(output))
	}

	// 记录输出到日志
	if len(output) > 0 {
		se.executor.logger.Info("脚本输出",
			zap.String("entry_name", se.meta.Name),
			zap.String("output", string(output)))
	}

	return nil
}

// executeSQLScript 执行SQL脚本
func (se *ScriptEntry) executeSQLScript(ctx context.Context, script string) error {
	// 获取数据库连接配置
	connectionName, exists := se.meta.Config["connection"]
	if !exists {
		se.executor.logger.Info("SQL脚本执行（无数据库连接配置，跳过执行）",
			zap.String("entry_name", se.meta.Name),
			zap.String("sql", script))
		return nil
	}

	connectionNameStr, ok := connectionName.(string)
	if !ok {
		return fmt.Errorf("connection must be a string")
	}

	se.executor.logger.Info("执行SQL脚本",
		zap.String("entry_name", se.meta.Name),
		zap.String("connection", connectionNameStr),
		zap.String("sql", script))

	// 检查SQL类型：查询还是非查询
	trimmedScript := strings.TrimSpace(strings.ToLower(script))
	isQuery := strings.HasPrefix(trimmedScript, "select") ||
		strings.HasPrefix(trimmedScript, "show") ||
		strings.HasPrefix(trimmedScript, "describe") ||
		strings.HasPrefix(trimmedScript, "explain")

	if isQuery {
		// 执行查询
		results, err := se.executor.ExecuteSQL(ctx, connectionNameStr, script)
		if err != nil {
			return fmt.Errorf("SQL查询执行失败: %w", err)
		}

		// 记录查询结果
		se.executor.logger.Info("SQL查询执行成功",
			zap.String("entry_name", se.meta.Name),
			zap.String("connection", connectionNameStr),
			zap.Int("rows_returned", len(results)))

		// 设置结果变量
		se.executor.setVariable(fmt.Sprintf("%s.sql_connection", se.meta.Name), connectionNameStr)
		se.executor.setVariable(fmt.Sprintf("%s.sql_script", se.meta.Name), script)
		se.executor.setVariable(fmt.Sprintf("%s.sql_status", se.meta.Name), "executed")
		se.executor.setVariable(fmt.Sprintf("%s.sql_type", se.meta.Name), "query")
		se.executor.setVariable(fmt.Sprintf("%s.sql_rows_returned", se.meta.Name), fmt.Sprintf("%d", len(results)))

		// 如果结果较少，记录详细结果
		if len(results) <= 10 {
			for i, row := range results {
				for k, v := range row {
					se.executor.setVariable(fmt.Sprintf("%s.result_%d_%s", se.meta.Name, i, k), fmt.Sprintf("%v", v))
				}
			}
		}

	} else {
		// 执行非查询（INSERT, UPDATE, DELETE等）
		rowsAffected, err := se.executor.ExecuteNonQuery(ctx, connectionNameStr, script)
		if err != nil {
			return fmt.Errorf("SQL非查询执行失败: %w", err)
		}

		// 记录执行结果
		se.executor.logger.Info("SQL非查询执行成功",
			zap.String("entry_name", se.meta.Name),
			zap.String("connection", connectionNameStr),
			zap.Int64("rows_affected", rowsAffected))

		// 设置结果变量
		se.executor.setVariable(fmt.Sprintf("%s.sql_connection", se.meta.Name), connectionNameStr)
		se.executor.setVariable(fmt.Sprintf("%s.sql_script", se.meta.Name), script)
		se.executor.setVariable(fmt.Sprintf("%s.sql_status", se.meta.Name), "executed")
		se.executor.setVariable(fmt.Sprintf("%s.sql_type", se.meta.Name), "non_query")
		se.executor.setVariable(fmt.Sprintf("%s.sql_rows_affected", se.meta.Name), fmt.Sprintf("%d", rowsAffected))
	}

	se.executor.logger.Info("SQL脚本执行完成",
		zap.String("entry_name", se.meta.Name),
		zap.String("connection", connectionNameStr))

	return nil
}

// MailEntry 邮件发送条目
type MailEntry struct {
	BaseEntry
}

// Execute 发送邮件
func (me *MailEntry) Execute(ctx context.Context) error {
	smtpServer, exists := me.meta.Config["smtp_server"]
	if !exists {
		return fmt.Errorf("mail entry requires 'smtp_server' in config")
	}

	to, exists := me.meta.Config["to"]
	if !exists {
		return fmt.Errorf("mail entry requires 'to' in config")
	}

	subject, exists := me.meta.Config["subject"]
	if !exists {
		return fmt.Errorf("mail entry requires 'subject' in config")
	}

	body, exists := me.meta.Config["body"]
	if !exists {
		return fmt.Errorf("mail entry requires 'body' in config")
	}

	me.executor.logger.Info("发送邮件",
		zap.String("entry_name", me.meta.Name),
		zap.String("smtp_server", fmt.Sprintf("%v", smtpServer)),
		zap.Any("to", to))

	// 解析配置
	smtpServerStr := fmt.Sprintf("%v", smtpServer)
	subjectStr := fmt.Sprintf("%v", subject)
	bodyStr := fmt.Sprintf("%v", body)

	// 处理收件人列表
	var recipients []string
	switch v := to.(type) {
	case string:
		recipients = []string{v}
	case []interface{}:
		for _, recipient := range v {
			recipients = append(recipients, fmt.Sprintf("%v", recipient))
		}
	case []string:
		recipients = v
	default:
		return fmt.Errorf("invalid 'to' field format, expected string or array")
	}

	// 解析SMTP服务器地址和端口
	var smtpHost string
	var smtpPort int = 587 // 默认端口
	
	// 检查服务器地址是否已包含端口
	if strings.Contains(smtpServerStr, ":") {
		parts := strings.Split(smtpServerStr, ":")
		if len(parts) == 2 {
			smtpHost = parts[0]
			if port, err := strconv.Atoi(parts[1]); err == nil {
				smtpPort = port
			}
		} else {
			smtpHost = smtpServerStr
		}
	} else {
		smtpHost = smtpServerStr
		// 检查是否有单独的端口配置
		if portConfig, exists := me.meta.Config["smtp_port"]; exists {
			if portStr, ok := portConfig.(string); ok {
				if port, err := strconv.Atoi(portStr); err == nil {
					smtpPort = port
				}
			} else if port, ok := portConfig.(int); ok {
				smtpPort = port
			}
		}
	}

	username, _ := me.meta.Config["username"].(string)
	password, _ := me.meta.Config["password"].(string)
	from, _ := me.meta.Config["from"].(string)

	if from == "" {
		from = username // 默认使用用户名作为发件人
	}

	// 发送邮件
	err := me.sendEmail(smtpHost, smtpPort, username, password, from, recipients, subjectStr, bodyStr)
	if err != nil {
		me.executor.logger.Error("邮件发送失败",
			zap.String("entry_name", me.meta.Name),
			zap.Error(err))
		return fmt.Errorf("failed to send email: %w", err)
	}

	me.executor.logger.Info("邮件发送成功",
		zap.String("entry_name", me.meta.Name),
		zap.Int("recipient_count", len(recipients)))

	return nil
}

// sendEmail 发送邮件的具体实现
func (me *MailEntry) sendEmail(server string, port int, username, password, from string, to []string, subject, body string) error {
	// 构建邮件内容
	message := fmt.Sprintf("From: %s\r\nTo: %s\r\nSubject: %s\r\n\r\n%s",
		from,
		strings.Join(to, ", "),
		subject,
		body)

	// 如果没有配置认证信息，只记录日志不实际发送
	if username == "" || password == "" {
		me.executor.logger.Info("邮件模拟发送（未配置SMTP认证）",
			zap.String("entry_name", me.meta.Name),
			zap.String("server", server),
			zap.Int("port", port),
			zap.String("from", from),
			zap.Strings("to", to),
			zap.String("subject", subject),
			zap.String("body_preview", body[:min(100, len(body))]))
		return nil
	}

	// 实际发送邮件
	auth := smtp.PlainAuth("", username, password, server)
	addr := fmt.Sprintf("%s:%d", server, port)

	err := smtp.SendMail(addr, auth, from, to, []byte(message))
	return err
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
