package job

import (
	"context"
	"fmt"
	"maps"
	"strings"
	"sync"
	"time"

	"admin/pkg/expr"
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"

	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
)

// Executor Job执行器
type Executor struct {
	jobMeta       *meta.JobMeta
	logger        *zap.Logger
	variables     map[string]string
	parameters    map[string]string
	entries       map[string]JobEntry
	status        ExecutionStatus
	startTime     time.Time
	endTime       time.Time
	mutex         sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
	stepRegistry  *step.Registry
	results       map[string]*ExecutionResult
	exprEngine    expr.ExprEngine                 // 表达式引擎
	connections   map[string]*meta.ConnectionMeta // 连接配置
	dbConnections map[string]*gorm.DB             // 数据库连接池
	configDir     string                          // 配置文件基础目录
}

// ExecutionStatus Job执行状态
type ExecutionStatus int

const (
	StatusPending ExecutionStatus = iota
	StatusRunning
	StatusSuccess
	StatusFailed
	StatusCanceled
)

func (s ExecutionStatus) String() string {
	switch s {
	case StatusPending:
		return "PENDING"
	case StatusRunning:
		return "RUNNING"
	case StatusSuccess:
		return "SUCCESS"
	case StatusFailed:
		return "FAILED"
	case StatusCanceled:
		return "CANCELED"
	default:
		return "UNKNOWN"
	}
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	EntryName string
	Status    ExecutionStatus
	StartTime time.Time
	EndTime   time.Time
	Error     error
	Output    map[string]interface{}
}

// NewExecutor 创建Job执行器
func NewExecutor(jobMeta *meta.JobMeta, stepRegistry *step.Registry, logger *zap.Logger) *Executor {
	return NewExecutorWithConfigDir(jobMeta, stepRegistry, logger, ".")
}

// NewExecutorWithConfigDir 创建Job执行器并指定配置目录
func NewExecutorWithConfigDir(jobMeta *meta.JobMeta, stepRegistry *step.Registry, logger *zap.Logger, configDir string) *Executor {
	ctx, cancel := context.WithCancel(context.Background())

	executor := &Executor{
		jobMeta:       jobMeta,
		logger:        logger,
		variables:     make(map[string]string),
		parameters:    make(map[string]string),
		entries:       make(map[string]JobEntry),
		status:        StatusPending,
		ctx:           ctx,
		cancel:        cancel,
		stepRegistry:  stepRegistry,
		results:       make(map[string]*ExecutionResult),
		exprEngine:    expr.NewExprEngine(),                  // 初始化表达式引擎
		connections:   make(map[string]*meta.ConnectionMeta), // 初始化连接配置
		dbConnections: make(map[string]*gorm.DB),             // 初始化数据库连接池
		configDir:     configDir,                             // 设置配置目录
	}

	// 初始化变量和参数
	for k, v := range jobMeta.Variables {
		executor.variables[k] = v
	}
	for k, v := range jobMeta.Parameters {
		executor.parameters[k] = v
		// 参数也添加到variables中，便于统一访问
		executor.variables[k] = v
	}

	// 初始化Job Entry
	executor.initializeEntries()

	return executor
}

// Execute 执行Job
func (je *Executor) Execute(ctx context.Context) error {
	je.mutex.Lock()
	je.status = StatusRunning
	je.startTime = time.Now()
	je.mutex.Unlock()

	je.logger.Info("开始执行Job",
		zap.String("job_name", je.jobMeta.Meta.Name),
		zap.String("job_description", je.jobMeta.Meta.Description))

	defer func() {
		je.mutex.Lock()
		je.endTime = time.Now()
		je.mutex.Unlock()
	}()

	// 查找START条目
	startEntry := je.findStartEntry()
	if startEntry == nil {
		je.setStatus(StatusFailed)
		return fmt.Errorf("未找到START条目")
	}

	// 从START条目开始执行
	err := je.executeEntry(ctx, startEntry.GetMeta().Name)
	if err != nil {
		je.setStatus(StatusFailed)
		je.logger.Error("Job执行失败",
			zap.String("job_name", je.jobMeta.Meta.Name),
			zap.Error(err))
		return err
	}

	je.setStatus(StatusSuccess)

	// 计算执行时间（在defer执行后endTime才会被设置）
	endTime := time.Now()
	duration := endTime.Sub(je.startTime)

	je.logger.Info("Job执行成功",
		zap.String("job_name", je.jobMeta.Meta.Name),
		zap.Duration("duration", duration))

	return nil
}

// executeEntry 执行单个条目
func (je *Executor) executeEntry(ctx context.Context, entryName string) error {
	// 检查是否已经取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-je.ctx.Done():
		return je.ctx.Err()
	default:
	}

	entry, exists := je.entries[entryName]
	if !exists {
		return fmt.Errorf("条目不存在: %s", entryName)
	}

	entryMeta := entry.GetMeta()
	je.logger.Info("执行条目",
		zap.String("entry_name", entryName),
		zap.String("entry_type", string(entryMeta.Type)))

	// 记录开始时间
	result := &ExecutionResult{
		EntryName: entryName,
		Status:    StatusRunning,
		StartTime: time.Now(),
		Output:    make(map[string]interface{}),
	}
	je.results[entryName] = result

	// 执行条目
	err := entry.Execute(ctx)
	result.EndTime = time.Now()

	if err != nil {
		result.Status = StatusFailed
		result.Error = err
		je.logger.Error("条目执行失败",
			zap.String("entry_name", entryName),
			zap.Error(err))

		// 处理错误跳转
		return je.handleErrorFlow(ctx, entryName, err)
	}

	result.Status = StatusSuccess
	je.logger.Info("条目执行成功",
		zap.String("entry_name", entryName),
		zap.Duration("duration", result.EndTime.Sub(result.StartTime)))

	// 处理成功跳转
	return je.handleSuccessFlow(ctx, entryName)
}

// handleSuccessFlow 处理成功执行后的流程
func (je *Executor) handleSuccessFlow(ctx context.Context, fromEntry string) error {
	nextEntries := je.findNextEntries(fromEntry, true)

	for _, nextEntry := range nextEntries {
		// 检查跳转条件
		hop := je.jobMeta.GetHopCondition(fromEntry, nextEntry)
		if hop != nil && hop.Condition != "" {
			// 评估条件
			conditionMet, err := je.evaluateCondition(hop.Condition)
			if err != nil {
				je.logger.Warn("条件评估失败",
					zap.String("from", fromEntry),
					zap.String("to", nextEntry),
					zap.String("condition", hop.Condition),
					zap.Error(err))
				continue
			}
			if !conditionMet {
				je.logger.Debug("跳转条件不满足",
					zap.String("from", fromEntry),
					zap.String("to", nextEntry),
					zap.String("condition", hop.Condition))
				continue
			}
		}

		// 递归执行下一个条目
		if err := je.executeEntry(ctx, nextEntry); err != nil {
			return err
		}
	}

	return nil
}

// handleErrorFlow 处理错误执行后的流程
func (je *Executor) handleErrorFlow(ctx context.Context, fromEntry string, execErr error) error {
	nextEntries := je.findNextEntries(fromEntry, false)

	// 如果没有错误处理流程，直接返回错误
	if len(nextEntries) == 0 {
		return execErr
	}

	// 执行错误处理流程
	for _, nextEntry := range nextEntries {
		// 设置错误信息到变量中
		je.setVariable("error.message", execErr.Error())
		je.setVariable("error.entry", fromEntry)

		// 递归执行错误处理条目
		if err := je.executeEntry(ctx, nextEntry); err != nil {
			// 错误处理也失败了，返回组合错误
			return fmt.Errorf("error handling failed for entry %s: %w (original error: %v)",
				fromEntry, err, execErr)
		}
	}

	// 错误处理流程成功执行，但Job仍然应该被标记为失败
	// 因为原始条目执行失败了
	return fmt.Errorf("job failed due to entry '%s': %w", fromEntry, execErr)
}

// findNextEntries 查找下一个条目
func (je *Executor) findNextEntries(fromEntry string, success bool) []string {
	var nextEntries []string

	for _, hop := range je.jobMeta.Hops {
		if hop.From == fromEntry {
			// 如果是无条件跳转，那么只有在匹配Evaluation的情况下才执行
			// 如果不是无条件跳转，则还需要评估Condition
			if hop.Evaluation == success {
				nextEntries = append(nextEntries, hop.To)
			}
		}
	}

	return nextEntries
}

// findStartEntry 查找START条目
func (je *Executor) findStartEntry() JobEntry {
	for _, entry := range je.entries {
		if entry.GetMeta().Type == meta.JobEntryTypeStart {
			return entry
		}
	}
	return nil
}

// initializeEntries 初始化所有Job Entry
func (je *Executor) initializeEntries() {
	for _, entryMeta := range je.jobMeta.Entries {
		entry := je.createJobEntry(entryMeta)
		if entry != nil {
			// 初始化entry
			if err := entry.Initialize(); err != nil {
				je.logger.Error("初始化Entry失败",
					zap.String("entry_name", entryMeta.Name),
					zap.String("entry_type", string(entryMeta.Type)),
					zap.Error(err))
				continue // 跳过初始化失败的entry
			}
			je.entries[entryMeta.Name] = entry
		}
	}
}

// createJobEntry 创建Job Entry实例
func (je *Executor) createJobEntry(entryMeta meta.JobEntryMeta) JobEntry {
	switch entryMeta.Type {
	case meta.JobEntryTypeStart:
		return &StartEntry{BaseEntry{meta: entryMeta, executor: je}}
	case meta.JobEntryTypeSuccess:
		return &SuccessEntry{BaseEntry{meta: entryMeta, executor: je}}
	case meta.JobEntryTypeError:
		return &ErrorEntry{BaseEntry{meta: entryMeta, executor: je}}
	case meta.JobEntryTypeTrans:
		return &TransformationEntry{BaseEntry{meta: entryMeta, executor: je}, nil, nil, nil}
	case meta.JobEntryTypeScript:
		return &ScriptEntry{BaseEntry{meta: entryMeta, executor: je}}
	case meta.JobEntryTypeMail:
		return &MailEntry{BaseEntry{meta: entryMeta, executor: je}}
	case meta.JobEntryTypeEval:
		return &EvalEntry{BaseEntry{meta: entryMeta, executor: je}}
	default:
		je.logger.Warn("未知的Job Entry类型",
			zap.String("type", string(entryMeta.Type)),
			zap.String("name", entryMeta.Name))
		return nil
	}
}

// evaluateCondition 评估条件表达式
func (je *Executor) evaluateCondition(condition string) (bool, error) {
	if condition == "" {
		return true, nil
	}

	// 准备变量上下文
	variables := make(map[string]any)

	// 添加Job变量和参数
	je.mutex.RLock()
	allVars := make(map[string]string)
	for k, v := range je.variables {
		allVars[k] = v
	}
	for k, v := range je.parameters {
		allVars[k] = v
	}
	je.mutex.RUnlock()

	// 处理带点的变量名，转换为嵌套对象
	nestedVars := make(map[string]map[string]any)

	for k, v := range allVars {
		if strings.Contains(k, ".") {
			// 分解变量名，如 "check_mode.result" -> check_mode["result"]
			parts := strings.Split(k, ".")
			if len(parts) == 2 {
				objName := parts[0]
				propName := parts[1]

				if nestedVars[objName] == nil {
					nestedVars[objName] = make(map[string]any)
				}
				nestedVars[objName][propName] = v
			}
		} else {
			// 直接变量
			variables[k] = v
		}
	}

	// 将嵌套变量添加到context
	for objName, props := range nestedVars {
		variables[objName] = props
	}

	// 添加系统变量
	variables["JOB_NAME"] = je.jobMeta.Meta.Name
	variables["JOB_STATUS"] = je.status.String()

	// 使用表达式引擎评估条件
	result, err := je.exprEngine.EvaluateToBoolean(condition, variables)
	if err != nil {
		je.logger.Error("条件表达式评估失败",
			zap.String("condition", condition),
			zap.Error(err))
		return false, fmt.Errorf("failed to evaluate condition '%s': %w", condition, err)
	}

	je.logger.Debug("条件表达式评估结果",
		zap.String("condition", condition),
		zap.Bool("result", result))

	return result, nil
}

// setVariable 设置变量
func (je *Executor) setVariable(key, value string) {
	je.mutex.Lock()
	defer je.mutex.Unlock()
	je.variables[key] = value
}

// setStatus 设置状态
func (je *Executor) setStatus(status ExecutionStatus) {
	je.mutex.Lock()
	defer je.mutex.Unlock()
	je.status = status
}

// GetStatus 获取执行状态
func (je *Executor) GetStatus() ExecutionStatus {
	je.mutex.RLock()
	defer je.mutex.RUnlock()
	return je.status
}

// GetResults 获取执行结果
func (je *Executor) GetResults() map[string]*ExecutionResult {
	je.mutex.RLock()
	defer je.mutex.RUnlock()

	results := make(map[string]*ExecutionResult)
	maps.Copy(results, je.results)
	return results
}

// Cancel 取消执行
func (je *Executor) Cancel() {
	je.cancel()
	je.setStatus(StatusCanceled)
}

// GetVariable 获取变量值
func (je *Executor) GetVariable(key string) (string, bool) {
	je.mutex.RLock()
	defer je.mutex.RUnlock()
	value, exists := je.variables[key]
	return value, exists
}

// GetParameter 获取参数值
func (je *Executor) GetParameter(key string) (string, bool) {
	je.mutex.RLock()
	defer je.mutex.RUnlock()
	value, exists := je.parameters[key]
	return value, exists
}

// GetVariables 获取所有变量
func (je *Executor) GetVariables() map[string]string {
	je.mutex.RLock()
	defer je.mutex.RUnlock()

	variables := make(map[string]string)
	for k, v := range je.variables {
		variables[k] = v
	}
	return variables
}

// AddDatabaseConnection 添加数据库连接配置
func (je *Executor) AddDatabaseConnection(conn *meta.ConnectionMeta) error {
	je.mutex.Lock()
	defer je.mutex.Unlock()

	if err := conn.Validate(); err != nil {
		return fmt.Errorf("invalid connection config: %w", err)
	}

	je.connections[conn.Name] = conn
	je.logger.Info("添加数据库连接配置",
		zap.String("connection_name", conn.Name),
		zap.String("connection_type", string(conn.Type)))

	return nil
}

// GetDatabaseConnection 获取数据库连接
func (je *Executor) GetDatabaseConnection(connectionName string) (*gorm.DB, error) {
	je.mutex.RLock()
	defer je.mutex.RUnlock()

	// 检查是否已存在连接
	if db, exists := je.dbConnections[connectionName]; exists {
		return db, nil
	}

	// 获取连接配置
	connMeta, exists := je.connections[connectionName]
	if !exists {
		return nil, fmt.Errorf("connection '%s' not found", connectionName)
	}

	// 创建新连接
	db, err := je.createDatabaseConnection(connMeta)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection '%s': %w", connectionName, err)
	}

	// 缓存连接
	je.dbConnections[connectionName] = db

	return db, nil
}

// createDatabaseConnection 创建数据库连接
func (je *Executor) createDatabaseConnection(connMeta *meta.ConnectionMeta) (*gorm.DB, error) {
	dbType, _ := connMeta.Config["database_type"].(string)
	hostname, _ := connMeta.Config["hostname"].(string)
	port, _ := connMeta.Config["port"].(int)
	database, _ := connMeta.Config["database"].(string)
	username, _ := connMeta.Config["username"].(string)
	password, _ := connMeta.Config["password"].(string)

	var dsn string
	var dialector gorm.Dialector

	switch meta.DatabaseType(dbType) {
	case meta.DatabaseTypeMySQL:
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			username, password, hostname, port, database)
		dialector = mysql.Open(dsn)

	case meta.DatabaseTypePostgreSQL:
		dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai",
			hostname, username, password, database, port)
		dialector = postgres.Open(dsn)

	case meta.DatabaseTypeSQLite:
		dsn = database // SQLite只需要文件路径
		dialector = sqlite.Open(dsn)

	case meta.DatabaseTypeSQLServer:
		dsn = fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s",
			username, password, hostname, port, database)
		dialector = sqlserver.Open(dsn)

	default:
		return nil, fmt.Errorf("unsupported database type: %s", dbType)
	}

	db, err := gorm.Open(dialector, &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 设置连接池参数
	if maxIdle, ok := connMeta.Config["max_idle_conns"].(int); ok {
		sqlDB.SetMaxIdleConns(maxIdle)
	} else {
		sqlDB.SetMaxIdleConns(10)
	}

	if maxOpen, ok := connMeta.Config["max_open_conns"].(int); ok {
		sqlDB.SetMaxOpenConns(maxOpen)
	} else {
		sqlDB.SetMaxOpenConns(100)
	}

	if maxLifetime, ok := connMeta.Config["conn_max_lifetime"].(string); ok {
		if duration, err := time.ParseDuration(maxLifetime); err == nil {
			sqlDB.SetConnMaxLifetime(duration)
		}
	} else {
		sqlDB.SetConnMaxLifetime(time.Hour)
	}

	return db, nil
}

// ExecuteSQL 执行SQL查询
func (je *Executor) ExecuteSQL(ctx context.Context, connectionName, sqlQuery string) ([]map[string]interface{}, error) {
	db, err := je.GetDatabaseConnection(connectionName)
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}
	rows, err := db.WithContext(ctx).Raw(sqlQuery).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to execute query: %w", err)
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	// 读取结果
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("failed to scan row: %w", err)
		}

		result := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				result[col] = string(b)
			} else {
				result[col] = val
			}
		}
		results = append(results, result)
	}

	return results, nil
}

// ExecuteNonQuery 执行非查询SQL语句
func (je *Executor) ExecuteNonQuery(ctx context.Context, connectionName, sqlQuery string) (int64, error) {
	db, err := je.GetDatabaseConnection(connectionName)
	if err != nil {
		return 0, err
	}

	result := db.WithContext(ctx).Exec(sqlQuery)
	if result.Error != nil {
		return 0, fmt.Errorf("failed to execute non-query: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// Close 关闭执行器和所有资源
func (je *Executor) Close() error {
	je.Cancel()

	je.mutex.Lock()
	defer je.mutex.Unlock()

	// 关闭所有数据库连接
	for name, db := range je.dbConnections {
		if sqlDB, err := db.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				je.logger.Warn("关闭数据库连接失败",
					zap.String("connection_name", name),
					zap.Error(err))
			}
		}
	}

	// 清空连接映射
	je.dbConnections = make(map[string]*gorm.DB)

	return nil
}

// LoadConnectionsFromConfig 从配置加载连接定义
func (je *Executor) LoadConnectionsFromConfig(connectionsConfig *meta.ConnectionsConfig) error {
	je.mutex.Lock()
	defer je.mutex.Unlock()

	for _, conn := range connectionsConfig.Connections {
		if err := conn.Validate(); err != nil {
			je.logger.Warn("跳过无效连接配置",
				zap.String("connection_name", conn.Name),
				zap.Error(err))
			continue
		}

		je.connections[conn.Name] = &conn
		je.logger.Info("加载连接配置",
			zap.String("connection_name", conn.Name),
			zap.String("connection_type", string(conn.Type)))
	}

	return nil
}

// GetConnectionNames 获取所有可用连接名称
func (je *Executor) GetConnectionNames() []string {
	je.mutex.RLock()
	defer je.mutex.RUnlock()

	names := make([]string, 0, len(je.connections))
	for name := range je.connections {
		names = append(names, name)
	}
	return names
}
