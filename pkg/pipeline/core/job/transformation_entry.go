package job

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"

	"admin/pkg/pipeline/config"
	"admin/pkg/pipeline/core/trans"
	"admin/pkg/pipeline/meta"

	"go.uber.org/zap"
)

// TransformationEntry 转换执行条目
type TransformationEntry struct {
	BaseEntry
	transformation *meta.TransformationMeta
	executor_trans *trans.Executor
	configLoader   *config.FileLoader
}

// Initialize 初始化转换条目
func (te *TransformationEntry) Initialize() error {
	if err := te.BaseEntry.Initialize(); err != nil {
		return err
	}

	// 初始化配置加载器 - 使用executor的配置目录作为基础路径
	te.configLoader = config.NewFileLoader(te.executor.configDir)

	return te.loadTransformation()
}

// loadTransformation 加载转换定义
func (te *TransformationEntry) loadTransformation() error {
	transformationFile, exists := te.meta.Config["transformation_file"]
	if !exists {
		return fmt.Errorf("transformation entry requires 'transformation_file' in config")
	}

	transformationPath, ok := transformationFile.(string)
	if !ok {
		return fmt.Errorf("transformation_file must be a string")
	}

	// 如果是相对路径，假设从当前工作目录开始
	if !filepath.IsAbs(transformationPath) {
		// 可以根据实际需求调整基础路径
		transformationPath = filepath.Join(".", transformationPath)
	}

	// 加载转换配置
	transformation, err := te.configLoader.LoadTransformation(transformationPath)
	if err != nil {
		return fmt.Errorf("failed to load transformation from %s: %w", transformationPath, err)
	}

	te.transformation = transformation

	// 处理参数传递
	if err := te.processParameters(); err != nil {
		return fmt.Errorf("failed to process parameters: %w", err)
	}

	// 创建转换执行器
	te.executor_trans = trans.NewExecutor(*te.transformation, te.executor.stepRegistry)

	// 初始化转换执行器
	if err := te.executor_trans.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize transformation executor: %w", err)
	}

	return nil
}

// processParameters 处理参数传递
func (te *TransformationEntry) processParameters() error {
	// 获取传递给转换的参数
	parameters, exists := te.meta.Config["parameters"]
	if !exists {
		return nil // 没有参数传递是正常的
	}

	paramMap, ok := parameters.(map[string]interface{})
	if !ok {
		return fmt.Errorf("parameters must be a map")
	}

	// 处理参数值替换
	if te.transformation.Parameters == nil {
		te.transformation.Parameters = make(map[string]string)
	}

	for key, value := range paramMap {
		valueStr := fmt.Sprintf("%v", value)

		// 进行变量替换
		resolvedValue := te.resolveVariables(valueStr)
		te.transformation.Parameters[key] = resolvedValue

		te.executor.logger.Debug("设置转换参数",
			zap.String("entry_name", te.meta.Name),
			zap.String("param_key", key),
			zap.String("param_value", resolvedValue))
	}

	return nil
}

// resolveVariables 解析变量
func (te *TransformationEntry) resolveVariables(value string) string {
	result := value

	// 替换Job级别的变量
	for varName, varValue := range te.executor.variables {
		placeholder := fmt.Sprintf("${%s}", varName)
		result = strings.ReplaceAll(result, placeholder, varValue)
	}

	// 替换Job级别的参数
	for paramName, paramValue := range te.executor.parameters {
		placeholder := fmt.Sprintf("${%s}", paramName)
		result = strings.ReplaceAll(result, placeholder, paramValue)
	}

	return result
}

// Execute 执行转换
func (te *TransformationEntry) Execute(ctx context.Context) error {
	if te.executor_trans == nil {
		return fmt.Errorf("transformation executor not initialized")
	}

	te.executor.logger.Info("开始执行转换",
		zap.String("entry_name", te.meta.Name),
		zap.String("transformation_name", te.transformation.Meta.Name))

	// 执行转换
	err := te.executor_trans.Execute(ctx)
	if err != nil {
		te.executor.logger.Error("转换执行失败",
			zap.String("entry_name", te.meta.Name),
			zap.String("transformation_name", te.transformation.Meta.Name),
			zap.Error(err))
		return fmt.Errorf("transformation execution failed: %w", err)
	}

	// 记录执行统计信息
	te.recordExecutionMetrics()

	te.executor.logger.Info("转换执行成功",
		zap.String("entry_name", te.meta.Name),
		zap.String("transformation_name", te.transformation.Meta.Name))

	return nil
}

// recordExecutionMetrics 记录执行指标
func (te *TransformationEntry) recordExecutionMetrics() {
	// TODO: 实现详细的执行指标记录
	// 可以记录处理的行数、执行时间等

	// 暂时设置一些基本的结果变量
	entryName := te.meta.Name
	te.executor.setVariable(fmt.Sprintf("%s.status", entryName), "success")
	te.executor.setVariable(fmt.Sprintf("%s.transformation", entryName), te.transformation.Meta.Name)

	// 如果转换执行器提供了统计信息，也可以记录
	// te.executor.setVariable(fmt.Sprintf("%s.rows_processed", entryName), "...")
}

// Validate 验证转换条目配置
func (te *TransformationEntry) Validate() error {
	if err := te.BaseEntry.Validate(); err != nil {
		return err
	}

	// 检查必需的配置
	if _, exists := te.meta.Config["transformation_file"]; !exists {
		return fmt.Errorf("transformation entry '%s' requires 'transformation_file' in config", te.meta.Name)
	}

	return nil
}

// Cleanup 清理资源
func (te *TransformationEntry) Cleanup() error {
	if te.executor_trans != nil {
		if err := te.executor_trans.Cleanup(); err != nil {
			te.executor.logger.Warn("清理转换执行器失败",
				zap.String("entry_name", te.meta.Name),
				zap.Error(err))
		}
	}

	return te.BaseEntry.Cleanup()
}
