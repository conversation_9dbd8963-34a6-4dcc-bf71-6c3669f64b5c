package core

import (
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/core/step/input"
	"admin/pkg/pipeline/core/step/output"
	"admin/pkg/pipeline/core/step/transform/enhanced"
	"admin/pkg/pipeline/meta"
)

// RegisterEnhancedSteps 注册增强步骤类型到注册表
func RegisterEnhancedSteps(registry *step.Registry) {
	// 注册增强输入步骤
	registry.Register(meta.StepTypeTableInput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return input.NewDatabaseInput(stepMeta), nil
	})

	registry.Register(meta.StepTypeCSVFileInput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return input.NewCSVFileInput(stepMeta), nil
	})

	registry.Register(meta.StepTypeJSONFileInput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return input.NewJSONFileInput(stepMeta), nil
	})

	registry.Register(meta.StepTypeRestInput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return input.NewHTTPInput(stepMeta), nil
	})

	registry.Register(meta.StepTypeDataGenerator, func(stepMeta meta.StepMeta) (step.Step, error) {
		return input.NewDataGenerator(stepMeta), nil
	})

	// 注册增强输出步骤
	registry.Register(meta.StepTypeTableOutput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return output.NewDatabaseOutput(stepMeta), nil
	})

	registry.Register(meta.StepTypeCSVFileOutput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return output.NewCSVOutput(stepMeta), nil
	})

	registry.Register(meta.StepTypeJSONFileOutput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return output.NewJSONOutput(stepMeta), nil
	})

	registry.Register(meta.StepTypeRestOutput, func(stepMeta meta.StepMeta) (step.Step, error) {
		return output.NewHTTPOutput(stepMeta), nil
	})

	// 注册增强转换步骤
	registry.Register(meta.StepTypeAggregator, func(stepMeta meta.StepMeta) (step.Step, error) {
		return enhanced.NewEnhancedAggregator(stepMeta), nil
	})

	registry.Register(meta.StepTypeJoin, func(stepMeta meta.StepMeta) (step.Step, error) {
		return enhanced.NewEnhancedJoin(stepMeta), nil
	})

	registry.Register(meta.StepTypeFieldMapper, func(stepMeta meta.StepMeta) (step.Step, error) {
		return enhanced.NewEnhancedFieldMapper(stepMeta), nil
	})

	registry.Register(meta.StepTypeEnhancedFilter, func(stepMeta meta.StepMeta) (step.Step, error) {
		return enhanced.NewEnhancedFilter(stepMeta), nil
	})

	registry.Register(meta.StepTypeEnhancedCalculator, func(stepMeta meta.StepMeta) (step.Step, error) {
		return enhanced.NewEnhancedCalculator(stepMeta), nil
	})
}

// RegisterAllSteps 注册所有步骤类型
func RegisterAllSteps(registry *step.Registry) {
	RegisterBasicSteps(registry)
	RegisterEnhancedSteps(registry)
}

// init 自动注册所有步骤到默认注册表
func init() {
	RegisterEnhancedSteps(step.DefaultRegistry)
}
