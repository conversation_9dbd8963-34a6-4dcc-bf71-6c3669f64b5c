package step

import (
	"admin/pkg/pipeline/meta"
)

// Row 行数据接口，表示数据流中的一行记录
type Row interface {
	// Get 获取指定字段的值
	Get(field string) interface{}

	// Set 设置指定字段的值
	Set(field string, value interface{})

	// Fields 获取所有字段名称
	Fields() []string

	// Clone 创建行的副本
	Clone() Row

	// GetString 获取字符串类型的字段值
	GetString(field string) (string, bool)

	// GetInt 获取整数类型的字段值
	GetInt(field string) (int64, bool)

	// GetFloat 获取浮点数类型的字段值
	GetFloat(field string) (float64, bool)

	// GetBool 获取布尔类型的字段值
	GetBool(field string) (bool, bool)

	// IsNull 检查字段值是否为null
	IsNull(field string) bool
}

// RowSet 行数据集接口，表示数据流中的多行记录
type RowSet interface {
	// Next 获取下一行数据，返回行数据、是否还有更多数据、错误信息
	Next() (Row, bool, error)

	// Close 关闭数据集并释放资源
	Close() error

	// Schema 获取数据集的Schema定义
	Schema() []meta.FieldMeta

	// Count 获取数据集的行数（如果可用）
	Count() (int64, bool)
}

// SimpleRow 简单的行数据实现
type SimpleRow struct {
	data map[string]interface{}
}

// NewSimpleRow 创建一个新的简单行数据
func NewSimpleRow(data map[string]interface{}) *SimpleRow {
	if data == nil {
		data = make(map[string]interface{})
	}
	return &SimpleRow{data: data}
}

func (r *SimpleRow) Get(field string) interface{} {
	return r.data[field]
}

func (r *SimpleRow) Set(field string, value interface{}) {
	r.data[field] = value
}

func (r *SimpleRow) Fields() []string {
	fields := make([]string, 0, len(r.data))
	for field := range r.data {
		fields = append(fields, field)
	}
	return fields
}

func (r *SimpleRow) Clone() Row {
	newData := make(map[string]interface{})
	for k, v := range r.data {
		newData[k] = v
	}
	return NewSimpleRow(newData)
}

func (r *SimpleRow) GetString(field string) (string, bool) {
	if val, exists := r.data[field]; exists && val != nil {
		if s, ok := val.(string); ok {
			return s, true
		}
	}
	return "", false
}

func (r *SimpleRow) GetInt(field string) (int64, bool) {
	if val, exists := r.data[field]; exists && val != nil {
		switch v := val.(type) {
		case int64:
			return v, true
		case int:
			return int64(v), true
		case float64:
			return int64(v), true
		}
	}
	return 0, false
}

func (r *SimpleRow) GetFloat(field string) (float64, bool) {
	if val, exists := r.data[field]; exists && val != nil {
		switch v := val.(type) {
		case float64:
			return v, true
		case int64:
			return float64(v), true
		case int:
			return float64(v), true
		}
	}
	return 0.0, false
}

func (r *SimpleRow) GetBool(field string) (bool, bool) {
	if val, exists := r.data[field]; exists && val != nil {
		if b, ok := val.(bool); ok {
			return b, true
		}
	}
	return false, false
}

func (r *SimpleRow) IsNull(field string) bool {
	val, exists := r.data[field]
	return !exists || val == nil
}
