package validation

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/core/step/transform/enhanced"
	"admin/pkg/pipeline/meta"
)

// DataQualityValidator 数据质量验证组件
type DataQualityValidator struct {
	*enhanced.EnhancedTransformStep

	// 验证规则
	validationRules []ValidationRule // 验证规则列表
	globalRules     []GlobalRule     // 全局验证规则

	// 错误处理
	errorHandling ErrorHandlingMode // 错误处理模式
	maxErrorRate  float64           // 最大错误率
	maxErrors     int               // 最大错误数
	errorOutput   string            // 错误输出位置

	// 质量指标
	qualityMetrics *QualityMetrics       // 质量指标
	ruleStats      map[string]*RuleStats // 规则统计

	// 采样配置
	enableSampling bool    // 是否启用采样
	sampleRate     float64 // 采样率
	sampleSize     int     // 采样大小

	mutex sync.RWMutex // 读写锁
}

// ValidationRule 验证规则
type ValidationRule struct {
	RuleID       string                 `json:"rule_id"`       // 规则ID
	RuleName     string                 `json:"rule_name"`     // 规则名称
	RuleType     ValidationType         `json:"rule_type"`     // 验证类型
	Field        string                 `json:"field"`         // 验证字段
	Condition    string                 `json:"condition"`     // 验证条件
	Parameters   map[string]interface{} `json:"parameters"`    // 规则参数
	Severity     Severity               `json:"severity"`      // 严重程度
	ErrorMessage string                 `json:"error_message"` // 错误消息
	Enabled      bool                   `json:"enabled"`       // 是否启用
	Priority     int                    `json:"priority"`      // 优先级
}

// GlobalRule 全局验证规则
type GlobalRule struct {
	RuleID         string                 `json:"rule_id"`         // 规则ID
	RuleName       string                 `json:"rule_name"`       // 规则名称
	RuleType       GlobalValidationType   `json:"rule_type"`       // 全局验证类型
	Condition      string                 `json:"condition"`       // 验证条件
	Parameters     map[string]interface{} `json:"parameters"`      // 规则参数
	Severity       Severity               `json:"severity"`        // 严重程度
	ErrorMessage   string                 `json:"error_message"`   // 错误消息
	Enabled        bool                   `json:"enabled"`         // 是否启用
	CheckFrequency int                    `json:"check_frequency"` // 检查频率（每N行检查一次）
}

// ValidationType 验证类型
type ValidationType int

const (
	ValidationTypeNotNull ValidationType = iota
	ValidationTypeFormat
	ValidationTypeRange
	ValidationTypeLength
	ValidationTypePattern
	ValidationTypeEnum
	ValidationTypeUnique
	ValidationTypeDataType
	ValidationTypeCustom
	ValidationTypeReferential
	ValidationTypeBusinessRule
)

// GlobalValidationType 全局验证类型
type GlobalValidationType int

const (
	GlobalValidationTypeRecordCount GlobalValidationType = iota
	GlobalValidationTypeDataVolume
	GlobalValidationTypeTimeSeries
	GlobalValidationTypeStatistical
	GlobalValidationTypeConsistency
	GlobalValidationTypeCompleteness
)

// Severity 严重程度
type Severity int

const (
	SeverityInfo Severity = iota
	SeverityWarning
	SeverityError
	SeverityCritical
)

// ErrorHandlingMode 错误处理模式
type ErrorHandlingMode int

const (
	ErrorHandlingModeContinue   ErrorHandlingMode = iota // 继续处理
	ErrorHandlingModeStop                                // 停止处理
	ErrorHandlingModeQuarantine                          // 隔离错误数据
	ErrorHandlingModeCorrect                             // 尝试纠错
)

// QualityMetrics 质量指标
type QualityMetrics struct {
	TotalRecords     int64         // 总记录数
	ValidRecords     int64         // 有效记录数
	InvalidRecords   int64         // 无效记录数
	ErrorRecords     int64         // 错误记录数
	WarningRecords   int64         // 警告记录数
	QualityScore     float64       // 质量分数
	CompletenessRate float64       // 完整性率
	AccuracyRate     float64       // 准确性率
	ConsistencyRate  float64       // 一致性率
	ValidityRate     float64       // 有效性率
	UniquenessRate   float64       // 唯一性率
	TimelinessScore  float64       // 及时性分数
	StartTime        time.Time     // 开始时间
	EndTime          time.Time     // 结束时间
	ProcessingTime   time.Duration // 处理时间
	mutex            sync.RWMutex  // 读写锁
}

// RuleStats 规则统计
type RuleStats struct {
	RuleID         string        // 规则ID
	ExecutionCount int64         // 执行次数
	PassCount      int64         // 通过次数
	FailCount      int64         // 失败次数
	ErrorCount     int64         // 错误次数
	AverageTime    time.Duration // 平均执行时间
	LastExecuted   time.Time     // 最后执行时间
	mutex          sync.RWMutex  // 读写锁
}

// ValidationError 验证错误
type ValidationError struct {
	RuleID       string                 `json:"rule_id"`
	RuleName     string                 `json:"rule_name"`
	Field        string                 `json:"field"`
	Value        interface{}            `json:"value"`
	ErrorMessage string                 `json:"error_message"`
	Severity     Severity               `json:"severity"`
	RowIndex     int64                  `json:"row_index"`
	Timestamp    time.Time              `json:"timestamp"`
	Context      map[string]interface{} `json:"context"`
}

// ErrorReport 错误报告
type ErrorReport struct {
	ValidationErrors []ValidationError     `json:"validation_errors"`
	Summary          *QualityMetrics       `json:"summary"`
	RuleStatistics   map[string]*RuleStats `json:"rule_statistics"`
	GeneratedAt      time.Time             `json:"generated_at"`
}

// NewDataQualityValidator 创建数据质量验证组件
func NewDataQualityValidator(stepMeta meta.StepMeta) *DataQualityValidator {
	return &DataQualityValidator{
		EnhancedTransformStep: enhanced.NewEnhancedTransformStep(stepMeta),
		errorHandling:         ErrorHandlingModeContinue,
		maxErrorRate:          0.1, // 10%
		maxErrors:             1000,
		qualityMetrics:        &QualityMetrics{},
		ruleStats:             make(map[string]*RuleStats),
		enableSampling:        false,
		sampleRate:            1.0,
	}
}

// Initialize 初始化验证组件
func (dqv *DataQualityValidator) Initialize() error {
	if err := dqv.EnhancedTransformStep.Initialize(); err != nil {
		return err
	}

	// 解析验证规则
	if err := dqv.parseValidationRules(); err != nil {
		return fmt.Errorf("failed to parse validation rules: %w", err)
	}

	// 初始化规则统计
	dqv.initializeRuleStats()

	return nil
}

// Execute 执行数据质量验证
func (dqv *DataQualityValidator) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	dqv.qualityMetrics.StartTime = time.Now()
	defer func() {
		dqv.qualityMetrics.EndTime = time.Now()
		dqv.qualityMetrics.ProcessingTime = dqv.qualityMetrics.EndTime.Sub(dqv.qualityMetrics.StartTime)
		dqv.calculateQualityScores()
	}()

	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	var outputRows []step.Row
	var errorRows []step.Row
	var validationErrors []ValidationError
	rowIndex := int64(0)

	// 处理每一行数据
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			return nil, fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		rowIndex++
		dqv.qualityMetrics.TotalRecords++

		// 采样检查
		if dqv.enableSampling && !dqv.shouldSample(rowIndex) {
			outputRows = append(outputRows, row)
			continue
		}

		// 验证当前行
		rowErrors := dqv.validateRow(row, rowIndex)

		// 执行全局验证规则
		if dqv.shouldCheckGlobalRules(rowIndex) {
			globalErrors := dqv.validateGlobalRules(outputRows, rowIndex)
			rowErrors = append(rowErrors, globalErrors...)
		}

		// 处理验证结果
		if len(rowErrors) == 0 {
			// 无错误，添加到输出
			dqv.qualityMetrics.ValidRecords++
			outputRows = append(outputRows, row)
		} else {
			// 有错误，根据错误处理模式处理
			validationErrors = append(validationErrors, rowErrors...)

			switch dqv.errorHandling {
			case ErrorHandlingModeContinue:
				// 继续处理，将错误行也加入输出（可选）
				outputRows = append(outputRows, dqv.markRowWithErrors(row, rowErrors))
				dqv.qualityMetrics.InvalidRecords++

			case ErrorHandlingModeStop:
				// 停止处理
				return nil, fmt.Errorf("validation failed at row %d: %s", rowIndex, rowErrors[0].ErrorMessage)

			case ErrorHandlingModeQuarantine:
				// 隔离错误数据
				errorRows = append(errorRows, dqv.markRowWithErrors(row, rowErrors))
				dqv.qualityMetrics.InvalidRecords++

			case ErrorHandlingModeCorrect:
				// 尝试纠错
				correctedRow, corrected := dqv.attemptCorrection(row, rowErrors)
				if corrected {
					outputRows = append(outputRows, correctedRow)
					dqv.qualityMetrics.ValidRecords++
				} else {
					errorRows = append(errorRows, dqv.markRowWithErrors(row, rowErrors))
					dqv.qualityMetrics.InvalidRecords++
				}
			}
		}

		// 检查是否超过最大错误率或最大错误数
		if dqv.shouldStopDueToErrors() {
			return nil, fmt.Errorf("validation stopped due to high error rate or max errors reached")
		}
	}

	// 生成错误报告
	if len(validationErrors) > 0 {
		dqv.generateErrorReport(validationErrors)
	}

	// 返回结果
	return step.NewMemoryRowSetFromRows(outputRows, input.Schema()), nil
}

// validateRow 验证单行数据
func (dqv *DataQualityValidator) validateRow(row step.Row, rowIndex int64) []ValidationError {
	var errors []ValidationError

	for _, rule := range dqv.validationRules {
		if !rule.Enabled {
			continue
		}

		startTime := time.Now()
		ruleStats := dqv.getRuleStats(rule.RuleID)

		// 更新统计
		ruleStats.mutex.Lock()
		ruleStats.ExecutionCount++
		ruleStats.LastExecuted = startTime
		ruleStats.mutex.Unlock()

		// 执行验证
		err := dqv.executeValidationRule(rule, row, rowIndex)

		// 更新平均执行时间
		executionTime := time.Since(startTime)
		ruleStats.mutex.Lock()
		ruleStats.AverageTime = (ruleStats.AverageTime*time.Duration(ruleStats.ExecutionCount-1) + executionTime) / time.Duration(ruleStats.ExecutionCount)

		if err != nil {
			// 验证失败
			ruleStats.FailCount++
			errors = append(errors, *err)

			// 更新质量指标
			switch err.Severity {
			case SeverityError, SeverityCritical:
				dqv.qualityMetrics.ErrorRecords++
			case SeverityWarning:
				dqv.qualityMetrics.WarningRecords++
			}
		} else {
			// 验证通过
			ruleStats.PassCount++
		}

		ruleStats.mutex.Unlock()
	}

	return errors
}

// executeValidationRule 执行验证规则
func (dqv *DataQualityValidator) executeValidationRule(rule ValidationRule, row step.Row, rowIndex int64) *ValidationError {
	fieldValue := row.Get(rule.Field)

	var isValid bool
	var err error

	switch rule.RuleType {
	case ValidationTypeNotNull:
		isValid = dqv.validateNotNull(fieldValue)

	case ValidationTypeFormat:
		isValid, err = dqv.validateFormat(fieldValue, rule.Parameters)

	case ValidationTypeRange:
		isValid, err = dqv.validateRange(fieldValue, rule.Parameters)

	case ValidationTypeLength:
		isValid, err = dqv.validateLength(fieldValue, rule.Parameters)

	case ValidationTypePattern:
		isValid, err = dqv.validatePattern(fieldValue, rule.Parameters)

	case ValidationTypeEnum:
		isValid, err = dqv.validateEnum(fieldValue, rule.Parameters)

	case ValidationTypeDataType:
		isValid, err = dqv.validateDataType(fieldValue, rule.Parameters)

	case ValidationTypeCustom:
		isValid, err = dqv.validateCustom(fieldValue, rule.Condition, row)

	default:
		err = fmt.Errorf("unsupported validation type: %v", rule.RuleType)
	}

	if err != nil {
		return &ValidationError{
			RuleID:       rule.RuleID,
			RuleName:     rule.RuleName,
			Field:        rule.Field,
			Value:        fieldValue,
			ErrorMessage: fmt.Sprintf("Validation error: %s", err.Error()),
			Severity:     SeverityError,
			RowIndex:     rowIndex,
			Timestamp:    time.Now(),
		}
	}

	if !isValid {
		return &ValidationError{
			RuleID:       rule.RuleID,
			RuleName:     rule.RuleName,
			Field:        rule.Field,
			Value:        fieldValue,
			ErrorMessage: rule.ErrorMessage,
			Severity:     rule.Severity,
			RowIndex:     rowIndex,
			Timestamp:    time.Now(),
		}
	}

	return nil
}

// 具体验证方法实现

func (dqv *DataQualityValidator) validateNotNull(value interface{}) bool {
	return value != nil && value != ""
}

func (dqv *DataQualityValidator) validateFormat(value interface{}, params map[string]interface{}) (bool, error) {
	if value == nil {
		return false, nil
	}

	str := fmt.Sprintf("%v", value)
	format, ok := params["format"].(string)
	if !ok {
		return false, fmt.Errorf("format parameter is required")
	}

	switch format {
	case "email":
		return dqv.isValidEmail(str), nil
	case "phone":
		return dqv.isValidPhone(str), nil
	case "date":
		return dqv.isValidDate(str, params), nil
	case "url":
		return dqv.isValidURL(str), nil
	default:
		return false, fmt.Errorf("unsupported format: %s", format)
	}
}

func (dqv *DataQualityValidator) validateRange(value interface{}, params map[string]interface{}) (bool, error) {
	if value == nil {
		return false, nil
	}

	// 转换为数值类型
	var numValue float64
	var err error

	switch v := value.(type) {
	case int:
		numValue = float64(v)
	case int64:
		numValue = float64(v)
	case float32:
		numValue = float64(v)
	case float64:
		numValue = v
	case string:
		numValue, err = strconv.ParseFloat(v, 64)
		if err != nil {
			return false, fmt.Errorf("cannot convert to number: %v", value)
		}
	default:
		return false, fmt.Errorf("unsupported type for range validation: %T", value)
	}

	// 检查范围
	if min, ok := params["min"]; ok {
		if minVal, ok := min.(float64); ok && numValue < minVal {
			return false, nil
		}
	}

	if max, ok := params["max"]; ok {
		if maxVal, ok := max.(float64); ok && numValue > maxVal {
			return false, nil
		}
	}

	return true, nil
}

func (dqv *DataQualityValidator) validateLength(value interface{}, params map[string]interface{}) (bool, error) {
	if value == nil {
		return false, nil
	}

	str := fmt.Sprintf("%v", value)
	length := len(str)

	if minLen, ok := params["min_length"]; ok {
		if minVal, ok := minLen.(int); ok && length < minVal {
			return false, nil
		}
	}

	if maxLen, ok := params["max_length"]; ok {
		if maxVal, ok := maxLen.(int); ok && length > maxVal {
			return false, nil
		}
	}

	if exactLen, ok := params["exact_length"]; ok {
		if exactVal, ok := exactLen.(int); ok && length != exactVal {
			return false, nil
		}
	}

	return true, nil
}

func (dqv *DataQualityValidator) validatePattern(value interface{}, params map[string]interface{}) (bool, error) {
	if value == nil {
		return false, nil
	}

	str := fmt.Sprintf("%v", value)
	pattern, ok := params["pattern"].(string)
	if !ok {
		return false, fmt.Errorf("pattern parameter is required")
	}

	regex, err := regexp.Compile(pattern)
	if err != nil {
		return false, fmt.Errorf("invalid regex pattern: %s", pattern)
	}

	return regex.MatchString(str), nil
}

func (dqv *DataQualityValidator) validateEnum(value interface{}, params map[string]interface{}) (bool, error) {
	if value == nil {
		return false, nil
	}

	allowedValues, ok := params["allowed_values"].([]interface{})
	if !ok {
		return false, fmt.Errorf("allowed_values parameter is required")
	}

	valueStr := fmt.Sprintf("%v", value)
	for _, allowed := range allowedValues {
		if fmt.Sprintf("%v", allowed) == valueStr {
			return true, nil
		}
	}

	return false, nil
}

func (dqv *DataQualityValidator) validateDataType(value interface{}, params map[string]interface{}) (bool, error) {
	if value == nil {
		return true, nil // null值通过数据类型验证
	}

	expectedType, ok := params["type"].(string)
	if !ok {
		return false, fmt.Errorf("type parameter is required")
	}

	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok, nil
	case "int", "integer":
		return dqv.isInteger(value), nil
	case "float", "decimal":
		return dqv.isFloat(value), nil
	case "bool", "boolean":
		return dqv.isBoolean(value), nil
	case "date":
		return dqv.isDate(value), nil
	default:
		return false, fmt.Errorf("unsupported data type: %s", expectedType)
	}
}

func (dqv *DataQualityValidator) validateCustom(value interface{}, condition string, row step.Row) (bool, error) {
	// 使用表达式引擎评估自定义条件
	result, err := dqv.EvaluateExpression(condition, row)
	if err != nil {
		return false, fmt.Errorf("custom validation expression error: %w", err)
	}

	// 转换结果为布尔值
	if boolResult, ok := result.(bool); ok {
		return boolResult, nil
	}

	return false, fmt.Errorf("custom validation expression must return boolean")
}

// 全局验证规则
func (dqv *DataQualityValidator) validateGlobalRules(processedRows []step.Row, currentRowIndex int64) []ValidationError {
	var errors []ValidationError

	for _, rule := range dqv.globalRules {
		if !rule.Enabled {
			continue
		}

		err := dqv.executeGlobalRule(rule, processedRows, currentRowIndex)
		if err != nil {
			errors = append(errors, *err)
		}
	}

	return errors
}

func (dqv *DataQualityValidator) executeGlobalRule(rule GlobalRule, processedRows []step.Row, currentRowIndex int64) *ValidationError {
	var isValid bool
	var err error

	switch rule.RuleType {
	case GlobalValidationTypeRecordCount:
		isValid, err = dqv.validateRecordCount(processedRows, rule.Parameters)

	case GlobalValidationTypeDataVolume:
		isValid, err = dqv.validateDataVolume(processedRows, rule.Parameters)

	case GlobalValidationTypeStatistical:
		isValid, err = dqv.validateStatistical(processedRows, rule.Parameters)

	case GlobalValidationTypeConsistency:
		isValid, err = dqv.validateConsistency(processedRows, rule.Parameters)

	default:
		err = fmt.Errorf("unsupported global validation type: %v", rule.RuleType)
	}

	if err != nil || !isValid {
		return &ValidationError{
			RuleID:       rule.RuleID,
			RuleName:     rule.RuleName,
			Field:        "GLOBAL",
			Value:        len(processedRows),
			ErrorMessage: rule.ErrorMessage,
			Severity:     rule.Severity,
			RowIndex:     currentRowIndex,
			Timestamp:    time.Now(),
		}
	}

	return nil
}

// 辅助方法

func (dqv *DataQualityValidator) shouldSample(rowIndex int64) bool {
	if !dqv.enableSampling {
		return true
	}

	if dqv.sampleSize > 0 {
		return rowIndex <= int64(dqv.sampleSize)
	}

	// 基于采样率
	return (float64(rowIndex) * dqv.sampleRate) >= float64(int64(float64(rowIndex)*dqv.sampleRate))
}

func (dqv *DataQualityValidator) shouldCheckGlobalRules(rowIndex int64) bool {
	for _, rule := range dqv.globalRules {
		if rule.Enabled && rule.CheckFrequency > 0 && rowIndex%int64(rule.CheckFrequency) == 0 {
			return true
		}
	}
	return false
}

func (dqv *DataQualityValidator) shouldStopDueToErrors() bool {
	if dqv.qualityMetrics.TotalRecords == 0 {
		return false
	}

	errorRate := float64(dqv.qualityMetrics.InvalidRecords) / float64(dqv.qualityMetrics.TotalRecords)

	return errorRate > dqv.maxErrorRate || dqv.qualityMetrics.InvalidRecords >= int64(dqv.maxErrors)
}

func (dqv *DataQualityValidator) markRowWithErrors(row step.Row, errors []ValidationError) step.Row {
	// 为行添加错误标记
	newRow := row.Clone()

	var errorMessages []string
	for _, err := range errors {
		errorMessages = append(errorMessages, err.ErrorMessage)
	}

	newRow.Set("__validation_errors__", strings.Join(errorMessages, "; "))
	newRow.Set("__validation_status__", "INVALID")

	return newRow
}

func (dqv *DataQualityValidator) attemptCorrection(row step.Row, errors []ValidationError) (step.Row, bool) {
	// 简化的自动纠错实现
	correctedRow := row.Clone()
	correctionsMade := 0

	for _, err := range errors {
		// 尝试基于规则类型进行自动纠错
		if dqv.attemptFieldCorrection(correctedRow, err) {
			correctionsMade++
		}
	}

	return correctedRow, correctionsMade > 0
}

func (dqv *DataQualityValidator) attemptFieldCorrection(row step.Row, err ValidationError) bool {
	// 简化的字段纠错逻辑
	switch err.RuleID {
	case "trim_whitespace":
		if str, ok := err.Value.(string); ok {
			row.Set(err.Field, strings.TrimSpace(str))
			return true
		}
	case "fix_case":
		if str, ok := err.Value.(string); ok {
			row.Set(err.Field, strings.ToUpper(str))
			return true
		}
	}

	return false
}

func (dqv *DataQualityValidator) calculateQualityScores() {
	if dqv.qualityMetrics.TotalRecords == 0 {
		return
	}

	total := float64(dqv.qualityMetrics.TotalRecords)

	// 计算各种质量指标
	dqv.qualityMetrics.ValidityRate = float64(dqv.qualityMetrics.ValidRecords) / total
	dqv.qualityMetrics.CompletenessRate = 1.0 - (float64(dqv.qualityMetrics.InvalidRecords) / total)
	dqv.qualityMetrics.AccuracyRate = dqv.qualityMetrics.ValidityRate

	// 综合质量分数
	dqv.qualityMetrics.QualityScore = (dqv.qualityMetrics.ValidityRate +
		dqv.qualityMetrics.CompletenessRate +
		dqv.qualityMetrics.AccuracyRate) / 3.0 * 100
}

func (dqv *DataQualityValidator) generateErrorReport(errors []ValidationError) {
	report := &ErrorReport{
		ValidationErrors: errors,
		Summary:          dqv.qualityMetrics,
		RuleStatistics:   dqv.ruleStats,
		GeneratedAt:      time.Now(),
	}

	// 输出错误报告（简化实现）
	if dqv.errorOutput != "" {
		dqv.saveErrorReport(report)
	}
}

func (dqv *DataQualityValidator) saveErrorReport(report *ErrorReport) {
	// 简化实现：序列化为JSON
	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return
	}

	// 这里可以保存到文件或发送到监控系统
	_ = data
}

// 格式验证辅助方法
func (dqv *DataQualityValidator) isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func (dqv *DataQualityValidator) isValidPhone(phone string) bool {
	phoneRegex := regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
	return phoneRegex.MatchString(phone)
}

func (dqv *DataQualityValidator) isValidDate(dateStr string, params map[string]interface{}) bool {
	format, ok := params["date_format"].(string)
	if !ok {
		format = "2006-01-02" // 默认格式
	}

	_, err := time.Parse(format, dateStr)
	return err == nil
}

func (dqv *DataQualityValidator) isValidURL(url string) bool {
	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	return urlRegex.MatchString(url)
}

func (dqv *DataQualityValidator) isInteger(value interface{}) bool {
	switch value.(type) {
	case int, int8, int16, int32, int64:
		return true
	case string:
		_, err := strconv.Atoi(value.(string))
		return err == nil
	}
	return false
}

func (dqv *DataQualityValidator) isFloat(value interface{}) bool {
	switch value.(type) {
	case float32, float64:
		return true
	case int, int8, int16, int32, int64:
		return true
	case string:
		_, err := strconv.ParseFloat(value.(string), 64)
		return err == nil
	}
	return false
}

func (dqv *DataQualityValidator) isBoolean(value interface{}) bool {
	switch value.(type) {
	case bool:
		return true
	case string:
		str := strings.ToLower(value.(string))
		return str == "true" || str == "false" || str == "1" || str == "0"
	}
	return false
}

func (dqv *DataQualityValidator) isDate(value interface{}) bool {
	switch v := value.(type) {
	case time.Time:
		return true
	case string:
		// 尝试多种日期格式
		formats := []string{
			"2006-01-02",
			"2006-01-02 15:04:05",
			"01/02/2006",
			"02-01-2006",
		}
		for _, format := range formats {
			if _, err := time.Parse(format, v); err == nil {
				return true
			}
		}
	}
	return false
}

// 全局验证实现
func (dqv *DataQualityValidator) validateRecordCount(rows []step.Row, params map[string]interface{}) (bool, error) {
	count := len(rows)

	if expectedCount, ok := params["expected_count"]; ok {
		if expected, ok := expectedCount.(int); ok {
			return count == expected, nil
		}
	}

	if minCount, ok := params["min_count"]; ok {
		if min, ok := minCount.(int); ok && count < min {
			return false, nil
		}
	}

	if maxCount, ok := params["max_count"]; ok {
		if max, ok := maxCount.(int); ok && count > max {
			return false, nil
		}
	}

	return true, nil
}

func (dqv *DataQualityValidator) validateDataVolume(rows []step.Row, params map[string]interface{}) (bool, error) {
	// 简化实现：检查数据量是否在预期范围内
	volume := len(rows)

	if minVolume, ok := params["min_volume"]; ok {
		if min, ok := minVolume.(int); ok && volume < min {
			return false, nil
		}
	}

	if maxVolume, ok := params["max_volume"]; ok {
		if max, ok := maxVolume.(int); ok && volume > max {
			return false, nil
		}
	}

	return true, nil
}

func (dqv *DataQualityValidator) validateStatistical(rows []step.Row, params map[string]interface{}) (bool, error) {
	// 简化实现：统计验证
	field, ok := params["field"].(string)
	if !ok {
		return false, fmt.Errorf("field parameter is required for statistical validation")
	}

	var values []float64
	for _, row := range rows {
		if value := row.Get(field); value != nil {
			if numValue, err := strconv.ParseFloat(fmt.Sprintf("%v", value), 64); err == nil {
				values = append(values, numValue)
			}
		}
	}

	if len(values) == 0 {
		return false, fmt.Errorf("no numeric values found for field %s", field)
	}

	// 计算统计量
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	avg := sum / float64(len(values))

	// 检查平均值范围
	if minAvg, ok := params["min_average"]; ok {
		if min, ok := minAvg.(float64); ok && avg < min {
			return false, nil
		}
	}

	if maxAvg, ok := params["max_average"]; ok {
		if max, ok := maxAvg.(float64); ok && avg > max {
			return false, nil
		}
	}

	return true, nil
}

func (dqv *DataQualityValidator) validateConsistency(rows []step.Row, params map[string]interface{}) (bool, error) {
	// 简化实现：一致性验证
	field, ok := params["field"].(string)
	if !ok {
		return false, fmt.Errorf("field parameter is required for consistency validation")
	}

	uniqueValues := make(map[string]int)
	for _, row := range rows {
		if value := row.Get(field); value != nil {
			valueStr := fmt.Sprintf("%v", value)
			uniqueValues[valueStr]++
		}
	}

	// 检查唯一值数量
	if maxUnique, ok := params["max_unique_values"]; ok {
		if max, ok := maxUnique.(int); ok && len(uniqueValues) > max {
			return false, nil
		}
	}

	return true, nil
}

// 初始化和配置方法
func (dqv *DataQualityValidator) parseValidationRules() error {
	// 从配置中解析验证规则
	if rulesConfig, exists := dqv.GetConfigSlice("validation_rules"); exists {
		for i, ruleConfig := range rulesConfig {
			rule, err := dqv.parseValidationRule(ruleConfig, i)
			if err != nil {
				return fmt.Errorf("failed to parse validation rule %d: %w", i, err)
			}
			dqv.validationRules = append(dqv.validationRules, rule)
		}
	}

	// 解析全局规则
	if globalRulesConfig, exists := dqv.GetConfigSlice("global_rules"); exists {
		for i, globalRuleConfig := range globalRulesConfig {
			globalRule, err := dqv.parseGlobalRule(globalRuleConfig, i)
			if err != nil {
				return fmt.Errorf("failed to parse global rule %d: %w", i, err)
			}
			dqv.globalRules = append(dqv.globalRules, globalRule)
		}
	}

	return nil
}

// parseValidationRule 解析单个验证规则
func (dqv *DataQualityValidator) parseValidationRule(ruleConfig interface{}, index int) (ValidationRule, error) {
	ruleMap, ok := ruleConfig.(map[string]interface{})
	if !ok {
		return ValidationRule{}, fmt.Errorf("validation rule %d: invalid format", index)
	}

	rule := ValidationRule{}

	// 解析规则ID
	if ruleID, ok := ruleMap["rule_id"].(string); ok {
		rule.RuleID = ruleID
	} else {
		rule.RuleID = fmt.Sprintf("rule_%d", index)
	}

	// 解析规则名称
	if ruleName, ok := ruleMap["rule_name"].(string); ok {
		rule.RuleName = ruleName
	}

	// 解析规则类型
	if ruleTypeStr, ok := ruleMap["rule_type"].(string); ok {
		switch ruleTypeStr {
		case "not_null":
			rule.RuleType = ValidationTypeNotNull
		case "data_type":
			rule.RuleType = ValidationTypeDataType
		case "range":
			rule.RuleType = ValidationTypeRange
		case "pattern":
			rule.RuleType = ValidationTypePattern
		case "custom":
			rule.RuleType = ValidationTypeCustom
		default:
			return ValidationRule{}, fmt.Errorf("validation rule %d: invalid rule_type '%s'", index, ruleTypeStr)
		}
	} else {
		return ValidationRule{}, fmt.Errorf("validation rule %d: rule_type is required", index)
	}

	// 解析字段名
	if field, ok := ruleMap["field"].(string); ok {
		rule.Field = field
	}

	// 解析条件
	if condition, ok := ruleMap["condition"].(string); ok {
		rule.Condition = condition
	}

	// 解析参数
	if parameters, ok := ruleMap["parameters"].(map[string]interface{}); ok {
		rule.Parameters = parameters
	} else {
		rule.Parameters = make(map[string]interface{})
	}

	return rule, nil
}

// parseGlobalRule 解析全局规则
func (dqv *DataQualityValidator) parseGlobalRule(globalRuleConfig interface{}, index int) (GlobalRule, error) {
	ruleMap, ok := globalRuleConfig.(map[string]interface{})
	if !ok {
		return GlobalRule{}, fmt.Errorf("global rule %d: invalid format", index)
	}

	globalRule := GlobalRule{}

	// 解析规则ID
	if ruleID, ok := ruleMap["rule_id"].(string); ok {
		globalRule.RuleID = ruleID
	} else {
		globalRule.RuleID = fmt.Sprintf("global_rule_%d", index)
	}

	// 解析规则名称
	if ruleName, ok := ruleMap["rule_name"].(string); ok {
		globalRule.RuleName = ruleName
	}

	// 解析规则类型
	if ruleTypeStr, ok := ruleMap["rule_type"].(string); ok {
		switch ruleTypeStr {
		case "not_null":
			globalRule.RuleType = GlobalValidationTypeCompleteness
		case "uniqueness":
			globalRule.RuleType = GlobalValidationTypeCompleteness
		case "consistency":
			globalRule.RuleType = GlobalValidationTypeConsistency
		case "completeness":
			globalRule.RuleType = GlobalValidationTypeCompleteness
		case "record_count":
			globalRule.RuleType = GlobalValidationTypeRecordCount
		case "data_volume":
			globalRule.RuleType = GlobalValidationTypeDataVolume
		case "time_series":
			globalRule.RuleType = GlobalValidationTypeTimeSeries
		case "statistical":
			globalRule.RuleType = GlobalValidationTypeStatistical
		default:
			return GlobalRule{}, fmt.Errorf("global rule %d: invalid rule_type '%s'", index, ruleTypeStr)
		}
	} else {
		return GlobalRule{}, fmt.Errorf("global rule %d: rule_type is required", index)
	}

	// 解析条件表达式
	if condition, ok := ruleMap["condition"].(string); ok {
		globalRule.Condition = condition
	}

	// 解析参数
	if parameters, ok := ruleMap["parameters"].(map[string]interface{}); ok {
		globalRule.Parameters = parameters
	} else {
		globalRule.Parameters = make(map[string]interface{})
	}

	return globalRule, nil
}

// initializeRuleStats 初始化规则统计
func (dqv *DataQualityValidator) initializeRuleStats() {
	dqv.mutex.Lock()
	defer dqv.mutex.Unlock()

	if dqv.ruleStats == nil {
		dqv.ruleStats = make(map[string]*RuleStats)
	}

	for _, rule := range dqv.validationRules {
		dqv.ruleStats[rule.RuleID] = &RuleStats{
			RuleID: rule.RuleID,
		}
	}

	for _, rule := range dqv.globalRules {
		dqv.ruleStats[rule.RuleID] = &RuleStats{
			RuleID: rule.RuleID,
		}
	}
}

// getRuleStats 获取规则统计信息
func (dqv *DataQualityValidator) getRuleStats(ruleID string) *RuleStats {
	dqv.mutex.RLock()
	defer dqv.mutex.RUnlock()

	if stats, exists := dqv.ruleStats[ruleID]; exists {
		return stats
	}

	// 创建新的统计
	stats := &RuleStats{RuleID: ruleID}
	dqv.ruleStats[ruleID] = stats
	return stats
}
