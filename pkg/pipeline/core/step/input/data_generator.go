package input

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// DataGenerator 数据生成器输入步骤
type DataGenerator struct {
	*step.BaseStep
	RowCount   int           `json:"row_count"`
	Fields     []FieldConfig `json:"fields"`
	current    int
	randSource *rand.Rand
}

// FieldConfig 字段配置
type FieldConfig struct {
	Name     string      `json:"name"`
	Type     string      `json:"type"` // string, int, float, bool, date, uuid
	MinValue interface{} `json:"min_value"`
	MaxValue interface{} `json:"max_value"`
	Values   []string    `json:"values"`  // 预定义值列表
	Pattern  string      `json:"pattern"` // 字符串模式
	Length   int         `json:"length"`  // 字符串长度
}

// NewDataGenerator 创建新的数据生成器
func NewDataGenerator(stepMeta meta.StepMeta) *DataGenerator {
	return &DataGenerator{
		BaseStep:   step.NewBaseStep(stepMeta),
		RowCount:   100,
		Fields:     []FieldConfig{},
		current:    0,
		randSource: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// Execute 执行数据生成
func (d *DataGenerator) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	var rows []map[string]interface{}

	for i := 0; i < d.RowCount; i++ {
		// 检查上下文是否取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		rowData := map[string]interface{}{
			"row_id": i + 1,
		}

		// 生成每个字段的数据
		for _, field := range d.Fields {
			value, err := d.generateFieldValue(field)
			if err != nil {
				return nil, fmt.Errorf("failed to generate value for field %s: %w", field.Name, err)
			}
			rowData[field.Name] = value
		}

		rows = append(rows, rowData)
		d.current++
	}

	return step.NewMemoryRowSet(rows, nil), nil
}

// generateFieldValue 生成字段值
func (d *DataGenerator) generateFieldValue(field FieldConfig) (interface{}, error) {
	switch field.Type {
	case "string":
		return d.generateString(field), nil
	case "int":
		return d.generateInt(field), nil
	case "float":
		return d.generateFloat(field), nil
	case "bool":
		return d.randSource.Intn(2) == 1, nil
	case "date":
		return d.generateDate(field), nil
	case "uuid":
		return d.generateUUID(), nil
	default:
		return fmt.Sprintf("value_%d", d.current), nil
	}
}

// generateString 生成字符串
func (d *DataGenerator) generateString(field FieldConfig) string {
	// 如果有预定义值，随机选择一个
	if len(field.Values) > 0 {
		return field.Values[d.randSource.Intn(len(field.Values))]
	}

	// 根据模式生成
	if field.Pattern != "" {
		return d.generateFromPattern(field.Pattern)
	}

	// 生成随机字符串
	length := field.Length
	if length <= 0 {
		length = 10
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[d.randSource.Intn(len(charset))]
	}
	return string(result)
}

// generateInt 生成整数
func (d *DataGenerator) generateInt(field FieldConfig) int {
	min := 0
	max := 100

	if field.MinValue != nil {
		if v, ok := field.MinValue.(int); ok {
			min = v
		}
	}

	if field.MaxValue != nil {
		if v, ok := field.MaxValue.(int); ok {
			max = v
		}
	}

	if max <= min {
		return min
	}

	return min + d.randSource.Intn(max-min)
}

// generateFloat 生成浮点数
func (d *DataGenerator) generateFloat(field FieldConfig) float64 {
	min := 0.0
	max := 100.0

	if field.MinValue != nil {
		if v, ok := field.MinValue.(float64); ok {
			min = v
		}
	}

	if field.MaxValue != nil {
		if v, ok := field.MaxValue.(float64); ok {
			max = v
		}
	}

	return min + d.randSource.Float64()*(max-min)
}

// generateDate 生成日期
func (d *DataGenerator) generateDate(field FieldConfig) string {
	// 生成过去一年内的随机日期
	now := time.Now()
	oneYearAgo := now.AddDate(-1, 0, 0)

	// 计算时间范围内的随机时间
	diff := now.Unix() - oneYearAgo.Unix()
	randomUnix := oneYearAgo.Unix() + d.randSource.Int63n(diff)
	randomTime := time.Unix(randomUnix, 0)

	return randomTime.Format("2006-01-02")
}

// generateUUID 生成简单的UUID格式字符串
func (d *DataGenerator) generateUUID() string {
	return fmt.Sprintf("%08x-%04x-%04x-%04x-%12x",
		d.randSource.Uint32(),
		d.randSource.Uint32()&0xffff,
		d.randSource.Uint32()&0xffff,
		d.randSource.Uint32()&0xffff,
		d.randSource.Uint64()&0xffffffffffff,
	)
}

// generateFromPattern 根据模式生成字符串
func (d *DataGenerator) generateFromPattern(pattern string) string {
	// 简单的模式替换
	result := pattern

	// 替换一些常见模式
	for i := 0; i < 10; i++ {
		result = replaceFirst(result, "#", fmt.Sprintf("%d", d.randSource.Intn(10)))
		result = replaceFirst(result, "?", string(rune('A'+d.randSource.Intn(26))))
	}

	return result
}

// replaceFirst 替换第一个匹配的字符串
func replaceFirst(s, old, new string) string {
	i := len(s)
	for j := 0; j < len(s); j++ {
		if j+len(old) <= len(s) && s[j:j+len(old)] == old {
			i = j
			break
		}
	}
	if i == len(s) {
		return s
	}
	return s[:i] + new + s[i+len(old):]
}

// Initialize 初始化数据生成器
func (d *DataGenerator) Initialize() error {
	if err := d.BaseStep.Initialize(); err != nil {
		return err
	}

	if d.RowCount <= 0 {
		return fmt.Errorf("row count must be greater than 0")
	}

	// 如果没有配置字段，使用默认字段
	if len(d.Fields) == 0 {
		d.Fields = []FieldConfig{
			{Name: "id", Type: "int", MinValue: 1, MaxValue: 1000000},
			{Name: "name", Type: "string", Length: 8},
			{Name: "email", Type: "string", Pattern: "user###@example.com"},
			{Name: "active", Type: "bool"},
			{Name: "created_at", Type: "date"},
		}
	}

	return nil
}

// Cleanup 清理数据生成器资源
func (d *DataGenerator) Cleanup() error {
	return d.BaseStep.Cleanup()
}
