package input

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// APIInput 从API接口读取数据
type APIInput struct {
	Type           string            `json:"type" yaml:"type"`
	URL            string            `json:"url" yaml:"url"`
	Method         string            `json:"method" yaml:"method"`
	Headers        map[string]string `json:"headers" yaml:"headers"`
	QueryParams    map[string]string `json:"query_params" yaml:"query_params"`
	Body           string            `json:"body" yaml:"body"`
	Timeout        int               `json:"timeout" yaml:"timeout"`         // 超时时间（秒）
	RetryCount     int               `json:"retry_count" yaml:"retry_count"` // 重试次数
	Authentication struct {
		Type     string `json:"type" yaml:"type"`         // basic, bearer, api_key
		Username string `json:"username" yaml:"username"` // 用于basic认证
		Password string `json:"password" yaml:"password"` // 用于basic认证
		Token    string `json:"token" yaml:"token"`       // 用于bearer认证
		APIKey   string `json:"api_key" yaml:"api_key"`   // 用于api_key认证
		Header   string `json:"header" yaml:"header"`     // API Key的Header名称
	} `json:"authentication" yaml:"authentication"`
}

// NewAPIInput 创建新的API输入
func NewAPIInput() *APIInput {
	return &APIInput{
		Type:        "api_input",
		Method:      "GET",
		Headers:     make(map[string]string),
		QueryParams: make(map[string]string),
		Timeout:     30,
		RetryCount:  3,
	}
}

// Execute 执行API输入
func (a *APIInput) Execute(ctx context.Context, input interface{}) (interface{}, error) {
	if a.URL == "" {
		return nil, fmt.Errorf("URL is required")
	}

	client := &http.Client{
		Timeout: time.Duration(a.Timeout) * time.Second,
	}

	var response *http.Response
	var err error

	// 重试逻辑
	for i := 0; i <= a.RetryCount; i++ {
		response, err = a.makeRequest(ctx, client)
		if err == nil && response.StatusCode < 500 {
			break
		}

		if i < a.RetryCount {
			// 等待一段时间后重试
			time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to make API request after %d retries: %w", a.RetryCount, err)
	}
	defer response.Body.Close()

	if response.StatusCode >= 400 {
		return nil, fmt.Errorf("API request failed with status code: %d", response.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 尝试解析JSON
	var result interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		// 如果不是JSON，返回原始字符串
		return string(body), nil
	}

	return result, nil
}

// makeRequest 发起HTTP请求
func (a *APIInput) makeRequest(ctx context.Context, client *http.Client) (*http.Response, error) {
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, a.Method, a.URL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加查询参数
	if len(a.QueryParams) > 0 {
		q := req.URL.Query()
		for key, value := range a.QueryParams {
			q.Add(key, value)
		}
		req.URL.RawQuery = q.Encode()
	}

	// 添加头部
	for key, value := range a.Headers {
		req.Header.Set(key, value)
	}

	// 添加认证
	if err := a.addAuthentication(req); err != nil {
		return nil, fmt.Errorf("failed to add authentication: %w", err)
	}

	// 发起请求
	return client.Do(req)
}

// addAuthentication 添加认证信息
func (a *APIInput) addAuthentication(req *http.Request) error {
	switch a.Authentication.Type {
	case "basic":
		if a.Authentication.Username == "" || a.Authentication.Password == "" {
			return fmt.Errorf("username and password are required for basic authentication")
		}
		req.SetBasicAuth(a.Authentication.Username, a.Authentication.Password)

	case "bearer":
		if a.Authentication.Token == "" {
			return fmt.Errorf("token is required for bearer authentication")
		}
		req.Header.Set("Authorization", "Bearer "+a.Authentication.Token)

	case "api_key":
		if a.Authentication.APIKey == "" {
			return fmt.Errorf("api_key is required for api_key authentication")
		}
		header := a.Authentication.Header
		if header == "" {
			header = "X-API-Key"
		}
		req.Header.Set(header, a.Authentication.APIKey)
	}

	return nil
}

// Validate 验证API输入配置
func (a *APIInput) Validate() error {
	if a.URL == "" {
		return fmt.Errorf("url is required")
	}

	if a.Method == "" {
		return fmt.Errorf("method is required")
	}

	if a.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive")
	}

	if a.RetryCount < 0 {
		return fmt.Errorf("retry_count must be non-negative")
	}

	// 验证认证配置
	if a.Authentication.Type != "" {
		switch a.Authentication.Type {
		case "basic":
			if a.Authentication.Username == "" || a.Authentication.Password == "" {
				return fmt.Errorf("username and password are required for basic authentication")
			}
		case "bearer":
			if a.Authentication.Token == "" {
				return fmt.Errorf("token is required for bearer authentication")
			}
		case "api_key":
			if a.Authentication.APIKey == "" {
				return fmt.Errorf("api_key is required for api_key authentication")
			}
		default:
			return fmt.Errorf("unsupported authentication type: %s", a.Authentication.Type)
		}
	}

	return nil
}

// GetType 获取步骤类型
func (a *APIInput) GetType() string {
	return a.Type
}

// GetConfig 获取步骤配置
func (a *APIInput) GetConfig() map[string]interface{} {
	return map[string]interface{}{
		"url":            a.URL,
		"method":         a.Method,
		"headers":        a.Headers,
		"query_params":   a.QueryParams,
		"body":           a.Body,
		"timeout":        a.Timeout,
		"retry_count":    a.RetryCount,
		"authentication": a.Authentication,
	}
}

// SetConfig 设置步骤配置
func (a *APIInput) SetConfig(config map[string]interface{}) error {
	if url, ok := config["url"].(string); ok {
		a.URL = url
	}
	if method, ok := config["method"].(string); ok {
		a.Method = method
	}
	if headers, ok := config["headers"].(map[string]string); ok {
		a.Headers = headers
	}
	if queryParams, ok := config["query_params"].(map[string]string); ok {
		a.QueryParams = queryParams
	}
	if body, ok := config["body"].(string); ok {
		a.Body = body
	}
	if timeout, ok := config["timeout"].(int); ok {
		a.Timeout = timeout
	}
	if retryCount, ok := config["retry_count"].(int); ok {
		a.RetryCount = retryCount
	}
	// TODO: 设置authentication配置
	return nil
}

// Clone 克隆步骤
func (a *APIInput) Clone() interface{} {
	clone := *a
	return &clone
}
