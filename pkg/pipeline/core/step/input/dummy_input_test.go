package input

import (
	"context"
	"testing"

	"admin/pkg/pipeline/meta"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDummyInput_Execute(t *testing.T) {
	// 创建步骤元数据
	stepMeta := meta.StepMeta{
		Name: "test_dummy_input",
		Type: meta.StepTypeDummyInput,
		Config: map[string]interface{}{
			"row_count": "5",
		},
	}

	// 创建DummyInput实例
	dummyInput := NewDummyInput(stepMeta)

	// 初始化
	err := dummyInput.Initialize()
	require.NoError(t, err)

	// 执行步骤
	ctx := context.Background()
	output, err := dummyInput.Execute(ctx, nil)
	require.NoError(t, err)
	require.NotNil(t, output)

	// 验证输出
	defer output.Close()

	rowCount := 0
	for {
		row, _, err := output.Next()
		require.NoError(t, err)
		if row == nil {
			break
		}
		require.NotNil(t, row)

		// 验证行数据包含期望的字段
		fields := row.Fields()
		assert.Contains(t, fields, "id")
		assert.Contains(t, fields, "name")
		assert.Contains(t, fields, "age")
		assert.Contains(t, fields, "email")
		assert.Contains(t, fields, "active")
		assert.Contains(t, fields, "score")
		assert.Contains(t, fields, "department")
		assert.Contains(t, fields, "join_date")

		// 验证字段值类型
		id, ok := row.GetInt("id")
		assert.True(t, ok)
		assert.Equal(t, int64(rowCount+1), id)

		name, ok := row.GetString("name")
		assert.True(t, ok)
		assert.Contains(t, name, "Test User")

		age, ok := row.GetInt("age")
		assert.True(t, ok)
		assert.GreaterOrEqual(t, age, int64(20))
		assert.LessOrEqual(t, age, int64(79))

		active, ok := row.GetBool("active")
		assert.True(t, ok)
		expectedActive := rowCount%2 == 0
		assert.Equal(t, expectedActive, active)

		rowCount++
	}

	// 验证行数
	assert.Equal(t, 5, rowCount)
}

func TestDummyInput_DefaultRowCount(t *testing.T) {
	// 不指定row_count的情况
	stepMeta := meta.StepMeta{
		Name:   "test_dummy_input_default",
		Type:   meta.StepTypeDummyInput,
		Config: map[string]interface{}{},
	}

	dummyInput := NewDummyInput(stepMeta)
	err := dummyInput.Initialize()
	require.NoError(t, err)

	ctx := context.Background()
	output, err := dummyInput.Execute(ctx, nil)
	require.NoError(t, err)
	require.NotNil(t, output)

	defer output.Close()

	rowCount := 0
	for {
		row, _, err := output.Next()
		require.NoError(t, err)
		if row == nil {
			break
		}
		rowCount++
	}

	// 默认应该是10行
	assert.Equal(t, 10, rowCount)
}

func TestDummyInput_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
	}{
		{
			name:        "valid config",
			config:      map[string]interface{}{"row_count": "5"},
			expectError: false,
		},
		{
			name:        "valid empty config",
			config:      map[string]interface{}{},
			expectError: false,
		},
		{
			name:        "invalid row_count - negative",
			config:      map[string]interface{}{"row_count": "-1"},
			expectError: true,
		},
		{
			name:        "invalid row_count - zero",
			config:      map[string]interface{}{"row_count": "0"},
			expectError: true,
		},
		{
			name:        "invalid row_count - not a number",
			config:      map[string]interface{}{"row_count": "abc"},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_dummy_input",
				Type:   meta.StepTypeDummyInput,
				Config: tt.config,
			}

			dummyInput := NewDummyInput(stepMeta)
			err := dummyInput.Validate()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestDummyInput_CancelContext(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_dummy_input_cancel",
		Type: meta.StepTypeDummyInput,
		Config: map[string]interface{}{
			"row_count": "1000", // 大量数据用于测试取消
		},
	}

	dummyInput := NewDummyInput(stepMeta)
	err := dummyInput.Initialize()
	require.NoError(t, err)

	// 创建可取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	output, err := dummyInput.Execute(ctx, nil)

	// 应该返回错误或空结果
	if err != nil {
		assert.Equal(t, context.Canceled, err)
	} else {
		assert.NotNil(t, output)
		output.Close()
	}
}
