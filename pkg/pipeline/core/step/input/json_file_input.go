package input

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// JSONFileInput JSON文件输入步骤
type JSONFileInput struct {
	*step.BaseStep
	filePath   string
	jsonPath   string // JSONPath表达式
	arrayMode  bool   // 是否为数组模式
	bufferSize int
}

// NewJSONFileInput 创建JSON文件输入步骤
func NewJSONFileInput(stepMeta meta.StepMeta) *JSONFileInput {
	return &JSONFileInput{
		BaseStep:   step.NewBaseStep(stepMeta),
		arrayMode:  true,
		bufferSize: 4096,
	}
}

// Initialize 初始化JSON读取器
func (jfi *JSONFileInput) Initialize() error {
	if err := jfi.BaseStep.Initialize(); err != nil {
		return err
	}

	if filePath, ok := jfi.GetConfigString("file_path"); ok {
		jfi.filePath = filePath
	} else {
		return fmt.Errorf("file_path is required")
	}

	if jsonPath, ok := jfi.GetConfigString("json_path"); ok {
		jfi.jsonPath = jsonPath
	}

	if arrayMode, ok := jfi.GetConfigBool("array_mode"); ok {
		jfi.arrayMode = arrayMode
	}

	return nil
}

// Execute 执行JSON文件读取
func (jfi *JSONFileInput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	file, err := os.Open(jfi.filePath)
	if err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	var data interface{}
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&data); err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to decode JSON: %w", err)
	}

	var outputRows []map[string]interface{}
	var schema []meta.FieldMeta

	if jfi.arrayMode {
		// 数组模式：期望JSON是对象数组
		if array, ok := data.([]interface{}); ok {
			for _, item := range array {
				if obj, ok := item.(map[string]interface{}); ok {
					outputRows = append(outputRows, obj)

					// 构建schema（使用第一个对象的字段）
					if len(schema) == 0 {
						for key := range obj {
							schema = append(schema, meta.FieldMeta{
								Name: key,
								Type: jfi.inferFieldType(obj[key]),
							})
						}
					}
				}
			}
		}
	} else {
		// 对象模式：将JSON对象转换为单行
		if obj, ok := data.(map[string]interface{}); ok {
			outputRows = append(outputRows, obj)

			// 构建schema
			for key := range obj {
				schema = append(schema, meta.FieldMeta{
					Name: key,
					Type: jfi.inferFieldType(obj[key]),
				})
			}
		}
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// inferFieldType 推断字段类型
func (jfi *JSONFileInput) inferFieldType(value interface{}) meta.FieldType {
	switch value.(type) {
	case string:
		return meta.FieldTypeString
	case float64:
		return meta.FieldTypeFloat
	case bool:
		return meta.FieldTypeBoolean
	case nil:
		return meta.FieldTypeString
	default:
		return meta.FieldTypeString
	}
}
