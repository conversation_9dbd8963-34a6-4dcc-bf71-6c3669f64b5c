package input

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// HTTPInput HTTP请求输入步骤
type HTTPInput struct {
	*step.BaseStep
	URL         string            `json:"url"`
	Method      string            `json:"method"`
	Headers     map[string]string `json:"headers"`
	Body        string            `json:"body"`
	Timeout     time.Duration     `json:"timeout"`
	ContentType string            `json:"content_type"`
}

// NewHTTPInput 创建新的HTTP输入步骤
func NewHTTPInput(stepMeta meta.StepMeta) *HTTPInput {
	return &HTTPInput{
		BaseStep:    step.NewBaseStep(stepMeta),
		Method:      "GET",
		Headers:     make(map[string]string),
		Timeout:     30 * time.Second,
		ContentType: "application/json",
	}
}

// Execute 执行HTTP请求
func (h *HTTPInput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if h.URL == "" {
		return nil, fmt.Errorf("URL is required")
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: h.Timeout,
	}

	// 创建请求
	var reqBody io.Reader
	if h.Body != "" {
		reqBody = strings.NewReader(h.Body)
	}

	req, err := http.NewRequestWithContext(ctx, h.Method, h.URL, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置头部
	for key, value := range h.Headers {
		req.Header.Set(key, value)
	}

	if h.ContentType != "" && h.Body != "" {
		req.Header.Set("Content-Type", h.ContentType)
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查状态码
	if resp.StatusCode >= 400 {
		return nil, fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应数据
	return h.parseResponse(respBody, resp.Header.Get("Content-Type"))
}

// parseResponse 解析HTTP响应
func (h *HTTPInput) parseResponse(data []byte, contentType string) (step.RowSet, error) {
	var rows []map[string]interface{}

	// 根据内容类型解析数据
	if strings.Contains(contentType, "application/json") {
		return h.parseJSONResponse(data)
	}

	// 默认按行处理
	lines := strings.Split(string(data), "\n")
	for i, line := range lines {
		if strings.TrimSpace(line) != "" {
			rowData := map[string]interface{}{
				"line_number": i + 1,
				"content":     line,
			}
			rows = append(rows, rowData)
		}
	}

	return step.NewMemoryRowSet(rows, nil), nil
}

// parseJSONResponse 解析JSON响应
func (h *HTTPInput) parseJSONResponse(data []byte) (step.RowSet, error) {
	var rows []map[string]interface{}

	var jsonData interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 处理JSON数组
	if array, ok := jsonData.([]interface{}); ok {
		for i, item := range array {
			rowData := map[string]interface{}{
				"index": i,
			}
			if itemMap, ok := item.(map[string]interface{}); ok {
				for key, value := range itemMap {
					rowData[key] = value
				}
			} else {
				rowData["item"] = item
			}
			rows = append(rows, rowData)
		}
	} else if objMap, ok := jsonData.(map[string]interface{}); ok {
		// 处理单个JSON对象
		rows = append(rows, objMap)
	} else {
		// 处理基本类型
		rowData := map[string]interface{}{
			"value": jsonData,
		}
		rows = append(rows, rowData)
	}

	return step.NewMemoryRowSet(rows, nil), nil
}

// Initialize 初始化HTTP输入
func (h *HTTPInput) Initialize() error {
	if err := h.BaseStep.Initialize(); err != nil {
		return err
	}

	if h.URL == "" {
		return fmt.Errorf("URL is required")
	}

	return nil
}

// Cleanup 清理HTTP输入资源
func (h *HTTPInput) Cleanup() error {
	return h.BaseStep.Cleanup()
}
