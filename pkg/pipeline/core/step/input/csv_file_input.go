package input

import (
	"bufio"
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"strings"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// CSVFileInput 增强的CSV文件输入步骤
type CSVFileInput struct {
	*step.BaseStep
	filePath   string
	delimiter  rune
	hasHeader  bool
	encoding   string
	skipLines  int
	maxLines   int
	bufferSize int
}

// NewCSVFileInput 创建CSV文件输入步骤
func NewCSVFileInput(stepMeta meta.StepMeta) *CSVFileInput {
	return &CSVFileInput{
		BaseStep:   step.NewBaseStep(stepMeta),
		delimiter:  ',',
		hasHeader:  true,
		encoding:   "UTF-8",
		skipLines:  0,
		maxLines:   -1,
		bufferSize: 4096,
	}
}

// Initialize 初始化CSV读取器
func (cfi *CSVFileInput) Initialize() error {
	if err := cfi.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中获取参数
	if filePath, ok := cfi.GetConfigString("file_path"); ok {
		cfi.filePath = filePath
	} else {
		return fmt.Errorf("file_path is required")
	}

	if delimiterStr, ok := cfi.GetConfigString("delimiter"); ok && len(delimiterStr) > 0 {
		cfi.delimiter = rune(delimiterStr[0])
	}

	if hasHeader, ok := cfi.GetConfigBool("has_header"); ok {
		cfi.hasHeader = hasHeader
	}

	if skipLines, ok := cfi.GetConfigInt("skip_lines"); ok {
		cfi.skipLines = int(skipLines)
	}

	if maxLines, ok := cfi.GetConfigInt("max_lines"); ok {
		cfi.maxLines = int(maxLines)
	}

	return nil
}

// Execute 执行CSV文件读取
func (cfi *CSVFileInput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	file, err := os.Open(cfi.filePath)
	if err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(bufio.NewReaderSize(file, cfi.bufferSize))
	reader.Comma = cfi.delimiter

	// 跳过指定行数
	for i := 0; i < cfi.skipLines; i++ {
		if _, err := reader.Read(); err != nil {
			if err == io.EOF {
				break
			}
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to skip line %d: %w", i, err)
		}
	}

	var headers []string
	var schema []meta.FieldMeta

	// 读取表头
	if cfi.hasHeader {
		headers, err = reader.Read()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to read header: %w", err)
		}
	} else {
		// 保存当前文件位置
		pos, err := file.Seek(0, io.SeekCurrent)
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to get file position: %w", err)
		}

		// 读取第一行数据来确定列数
		firstRecord, err := reader.Read()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to read first record: %w", err)
		}

		// 生成默认列名
		headers = make([]string, len(firstRecord))
		for i := range headers {
			headers[i] = fmt.Sprintf("col_%d", i+1)
		}

		// 重置文件指针到读取第一行前的位置
		if _, err := file.Seek(pos, io.SeekStart); err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to reset file position: %w", err)
		}
		// 重新创建reader以重置内部状态
		reader = csv.NewReader(bufio.NewReaderSize(file, cfi.bufferSize))
		reader.Comma = cfi.delimiter
	}

	// 构建schema
	schema = make([]meta.FieldMeta, len(headers))
	for i, header := range headers {
		schema[i] = meta.FieldMeta{
			Name: header,
			Type: meta.FieldTypeString,
		}
	}

	var outputRows []map[string]interface{}
	lineCount := 0

	// 读取数据行
	for {
		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to read record: %w", err)
		}

		lineCount++
		if cfi.maxLines > 0 && lineCount > cfi.maxLines {
			break
		}

		// 构建行数据
		rowData := make(map[string]interface{})
		for i, value := range record {
			if i < len(headers) {
				rowData[headers[i]] = strings.TrimSpace(value)
			}
		}

		outputRows = append(outputRows, rowData)
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}
