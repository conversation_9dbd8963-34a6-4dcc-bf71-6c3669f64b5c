package input

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// DatabaseInput 数据库输入步骤
type DatabaseInput struct {
	*step.BaseStep
	connectionString string
	query            string
	batchSize        int
	db               *sql.DB
	pageSize         int
	currentOffset    int
	enablePaging     bool  // 是否启用分页
	totalRows        int64 // 总行数（如果可获取）
	hasMoreData      bool  // 是否还有更多数据
}

// NewDatabaseInput 创建数据库输入步骤
func NewDatabaseInput(stepMeta meta.StepMeta) *DatabaseInput {
	return &DatabaseInput{
		BaseStep:      step.NewBaseStep(stepMeta),
		batchSize:     1000,
		pageSize:      1000,
		currentOffset: 0,
		enablePaging:  false,
		hasMoreData:   true,
	}
}

// Initialize 初始化数据库连接
func (di *DatabaseInput) Initialize() error {
	if err := di.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中获取连接字符串和查询
	if connStr, ok := di.GetConfigString("connection_string"); ok {
		di.connectionString = connStr
	} else {
		return fmt.Errorf("connection_string is required")
	}

	if query, ok := di.GetConfigString("query"); ok {
		di.query = query
	} else {
		return fmt.Errorf("query is required")
	}

	// 获取分页配置
	if pageSize, ok := di.GetConfigInt("page_size"); ok {
		di.pageSize = int(pageSize)
		di.enablePaging = true
	}

	if batchSize, ok := di.GetConfigInt("batch_size"); ok {
		di.batchSize = int(batchSize)
	}

	if batchSize, ok := di.GetConfigInt("batch_size"); ok {
		di.batchSize = int(batchSize)
	}

	// 建立数据库连接
	// 确保DSN格式正确
	connStr := di.connectionString

	// 输出连接字符串供调试
	fmt.Printf("正在连接数据库: %s\n", connStr)

	db, err := sql.Open("mysql", connStr)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 测试连接是否有效
	if err := db.Ping(); err != nil {
		return fmt.Errorf("database connection test failed: %w", err)
	}

	di.db = db
	return nil
}

// Execute 执行数据库查询（支持分页）
func (di *DatabaseInput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if di.db == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("database not initialized")
	}

	if !di.hasMoreData {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	var finalQuery string
	var rows *sql.Rows
	var err error

	if di.enablePaging {
		// 构建分页查询
		finalQuery = di.buildPaginatedQuery()
		rows, err = di.db.QueryContext(ctx, finalQuery)
	} else {
		// 执行原始查询
		rows, err = di.db.QueryContext(ctx, di.query)
	}

	if err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("query execution failed: %w", err)
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to get columns: %w", err)
	}

	// 构建schema
	schema := make([]meta.FieldMeta, len(columns))
	for i, column := range columns {
		schema[i] = meta.FieldMeta{
			Name: column,
			Type: meta.FieldTypeString, // 简化处理，都当作字符串
		}
	}

	var outputRows []map[string]interface{}
	rowCount := 0

	// 读取数据行
	for rows.Next() {
		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 创建值容器
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行数据
		if err := rows.Scan(valuePtrs...); err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to scan row: %w", err)
		}

		// 构建行数据
		rowData := make(map[string]interface{})
		for i, column := range columns {
			if values[i] != nil {
				// 根据不同类型进行处理
				switch v := values[i].(type) {
				case []byte:
					rowData[column] = string(v)
				case time.Time:
					rowData[column] = v.Format("2006-01-02 15:04:05")
				default:
					// 其他类型直接使用
					rowData[column] = v
				}
			} else {
				rowData[column] = nil
			}
		}

		outputRows = append(outputRows, rowData)
		rowCount++
	}

	if err := rows.Err(); err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("row iteration error: %w", err)
	}

	// 更新分页状态
	if di.enablePaging {
		di.updatePagingState(rowCount)
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// buildPaginatedQuery 构建分页查询
func (di *DatabaseInput) buildPaginatedQuery() string {
	// 这里简化处理，假设使用MySQL的LIMIT OFFSET语法
	// 实际项目中可能需要根据不同数据库使用不同的分页语法
	return fmt.Sprintf("%s LIMIT %d OFFSET %d", di.query, di.pageSize, di.currentOffset)
}

// updatePagingState 更新分页状态
func (di *DatabaseInput) updatePagingState(rowCount int) {
	if rowCount < di.pageSize {
		// 如果返回的行数少于页大小，说明没有更多数据了
		di.hasMoreData = false
	} else {
		// 更新偏移量，准备下次查询
		di.currentOffset += di.pageSize
	}
}

// HasMoreData 检查是否还有更多数据
func (di *DatabaseInput) HasMoreData() bool {
	return di.hasMoreData
}

// ResetPaging 重置分页状态
func (di *DatabaseInput) ResetPaging() {
	di.currentOffset = 0
	di.hasMoreData = true
}

// GetCurrentOffset 获取当前偏移量
func (di *DatabaseInput) GetCurrentOffset() int {
	return di.currentOffset
}

// SetPageSize 设置页大小
func (di *DatabaseInput) SetPageSize(pageSize int) {
	di.pageSize = pageSize
	di.enablePaging = pageSize > 0
}

// EnablePaging 启用分页
func (di *DatabaseInput) EnablePaging(pageSize int) {
	di.enablePaging = true
	di.pageSize = pageSize
	di.currentOffset = 0
	di.hasMoreData = true
}

// Cleanup 清理资源
func (di *DatabaseInput) Cleanup() error {
	if di.db != nil {
		return di.db.Close()
	}
	return nil
}

// GetTotalRows 获取查询的总行数
func (di *DatabaseInput) GetTotalRows(ctx context.Context) (int64, error) {
	if di.db == nil {
		return 0, fmt.Errorf("database not initialized")
	}

	// 构建count查询
	countQuery := di.buildCountQuery()

	var count int64
	err := di.db.QueryRowContext(ctx, countQuery).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get total rows: %w", err)
	}

	di.totalRows = count
	return count, nil
}

// buildCountQuery 构建计数查询
func (di *DatabaseInput) buildCountQuery() string {
	// 简单的实现：将SELECT * 替换为 SELECT COUNT(*)
	// 实际项目中可能需要更复杂的解析
	query := di.query

	// 查找SELECT关键字（大小写不敏感）
	if strings.Contains(strings.ToUpper(query), "SELECT") {
		// 找到FROM的位置
		fromIndex := strings.Index(strings.ToUpper(query), "FROM")
		if fromIndex != -1 {
			// 构建count查询
			fromPart := query[fromIndex:]
			return fmt.Sprintf("SELECT COUNT(*) %s", fromPart)
		}
	}

	// 如果无法解析，返回包装的子查询
	return fmt.Sprintf("SELECT COUNT(*) FROM (%s) AS count_query", query)
}

// GetPagingInfo 获取分页信息
func (di *DatabaseInput) GetPagingInfo() map[string]interface{} {
	return map[string]interface{}{
		"current_offset": di.currentOffset,
		"page_size":      di.pageSize,
		"total_rows":     di.totalRows,
		"has_more_data":  di.hasMoreData,
		"enable_paging":  di.enablePaging,
	}
}
