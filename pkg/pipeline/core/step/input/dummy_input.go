package input

import (
	"context"
	"fmt"
	"strconv"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// DummyInput 虚拟输入步骤，用于测试，生成指定数量的测试数据
type DummyInput struct {
	*step.BaseStep
	rowCount int
	current  int
}

// NewDummyInput 创建一个新的虚拟输入步骤
func NewDummyInput(stepMeta meta.StepMeta) *DummyInput {
	return &DummyInput{
		BaseStep: step.NewBaseStep(stepMeta),
		rowCount: 10, // 默认生成10行数据
		current:  0,
	}
}

// Initialize 初始化步骤
func (d *DummyInput) Initialize() error {
	if err := d.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中获取行数
	if rowCountStr, exists := d.GetConfigString("row_count"); exists {
		if rowCount, err := strconv.Atoi(rowCountStr); err == nil && rowCount > 0 {
			d.rowCount = rowCount
		}
	}

	d.current = 0
	return nil
}

// Execute 执行步骤，生成测试数据
func (d *DummyInput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	// 创建schema
	schema := d.getSchema()

	// 创建数据行切片
	var rows []map[string]interface{}

	for i := 0; i < d.rowCount; i++ {
		// 每100行检查一次取消信号，或者在少量数据时每行检查
		if i%100 == 0 || d.rowCount < 1000 {
			select {
			case <-ctx.Done():
				return step.NewEmptyRowSet(schema), ctx.Err()
			default:
			}
		}

		// 创建测试数据行
		rowData := map[string]interface{}{
			"id":         int64(i + 1),
			"name":       fmt.Sprintf("Test User %d", i+1),
			"age":        int64(20 + (i % 60)), // 年龄在20-79之间
			"email":      fmt.Sprintf("<EMAIL>", i+1),
			"active":     i%2 == 0,               // 交替true/false
			"score":      float64(50 + (i % 50)), // 分数在50-99之间
			"department": getDepartment(i),
			"join_date":  fmt.Sprintf("2023-%02d-01", (i%12)+1), // 2023年不同月份
		}

		rows = append(rows, rowData)
	}

	return step.NewMemoryRowSet(rows, schema), nil
}

// Validate 验证步骤配置
func (d *DummyInput) Validate() error {
	if err := d.BaseStep.Validate(); err != nil {
		return err
	}

	// 验证行数配置
	if rowCountStr, exists := d.GetConfigString("row_count"); exists {
		if rowCount, err := strconv.Atoi(rowCountStr); err != nil || rowCount <= 0 {
			return meta.NewValidationError("row_count must be a positive integer")
		}
	}

	return nil
}

// getSchema 获取DummyInput的数据schema
func (d *DummyInput) getSchema() []meta.FieldMeta {
	return []meta.FieldMeta{
		{Name: "id", Type: meta.FieldTypeInteger},
		{Name: "name", Type: meta.FieldTypeString},
		{Name: "age", Type: meta.FieldTypeInteger},
		{Name: "email", Type: meta.FieldTypeString},
		{Name: "active", Type: meta.FieldTypeBoolean},
		{Name: "score", Type: meta.FieldTypeFloat},
		{Name: "department", Type: meta.FieldTypeString},
		{Name: "join_date", Type: meta.FieldTypeString},
	}
}

// getDepartment 根据索引返回部门名称
func getDepartment(index int) string {
	departments := []string{
		"Engineering",
		"Sales",
		"Marketing",
		"HR",
		"Finance",
	}
	return departments[index%len(departments)]
}
