package step

import (
	"fmt"

	"admin/pkg/pipeline/meta"
)

// MemoryRowSet 内存中的行数据集实现
type MemoryRowSet struct {
	rows   []Row
	schema []meta.FieldMeta
	index  int
	closed bool
}

// NewMemoryRowSet 创建一个新的内存行数据集
func NewMemoryRowSet(data []map[string]interface{}, schema []meta.FieldMeta) *MemoryRowSet {
	rows := make([]Row, len(data))
	for i, rowData := range data {
		rows[i] = NewSimpleRow(rowData)
	}

	return &MemoryRowSet{
		rows:   rows,
		schema: schema,
		index:  0,
		closed: false,
	}
}

// NewMemoryRowSetFromRows 从Row切片创建内存行数据集
func NewMemoryRowSetFromRows(rows []Row, schema []meta.FieldMeta) *MemoryRowSet {
	return &MemoryRowSet{
		rows:   rows,
		schema: schema,
		index:  0,
		closed: false,
	}
}

func (m *MemoryRowSet) Next() (Row, bool, error) {
	if m.closed {
		return nil, false, fmt.Errorf("rowset is closed")
	}

	if m.index >= len(m.rows) {
		return nil, false, nil
	}

	row := m.rows[m.index]
	m.index++
	hasMore := m.index < len(m.rows)
	return row, hasMore, nil
}

func (m *MemoryRowSet) Close() error {
	m.closed = true
	return nil
}

func (m *MemoryRowSet) Schema() []meta.FieldMeta {
	return m.schema
}

func (m *MemoryRowSet) Count() (int64, bool) {
	return int64(len(m.rows)), true
}

// EmptyRowSet 空的行数据集
type EmptyRowSet struct {
	schema []meta.FieldMeta
}

// NewEmptyRowSet 创建一个空的行数据集
func NewEmptyRowSet(schema []meta.FieldMeta) *EmptyRowSet {
	return &EmptyRowSet{schema: schema}
}

func (e *EmptyRowSet) Next() (Row, bool, error) {
	return nil, false, nil
}

func (e *EmptyRowSet) Close() error {
	return nil
}

func (e *EmptyRowSet) Schema() []meta.FieldMeta {
	return e.schema
}

func (e *EmptyRowSet) Count() (int64, bool) {
	return 0, true
}
