package step

import (
	"compress/gzip"
	"context"
	"encoding/gob"
	"fmt"
	"io"
	"sync"
	"time"

	"admin/pkg/pipeline/meta"
)

// StreamingRowSet 流式行数据集，支持大数据集的分块处理
type StreamingRowSet struct {
	channel     chan RowChunk      // 数据通道
	schema      []meta.FieldMeta   // 数据模式
	closed      bool               // 是否已关闭
	mutex       sync.RWMutex       // 读写锁
	ctx         context.Context    // 上下文
	cancel      context.CancelFunc // 取消函数
	chunkSize   int                // 块大小
	currentRow  Row                // 当前行
	hasMore     bool               // 是否还有更多数据
	producer    RowProducer        // 数据生产者
	metrics     *StreamingMetrics  // 流式指标
	compression bool               // 是否启用压缩
}

// RowChunk 数据块，用于分批传输数据
type RowChunk struct {
	Rows      []Row     // 行数据
	HasMore   bool      // 是否还有更多数据
	Error     error     // 错误信息
	Timestamp time.Time // 时间戳
	ChunkID   int64     // 块ID
}

// RowProducer 数据生产者接口
type RowProducer interface {
	// Produce 生产数据块
	Produce(ctx context.Context, chunkChan chan<- RowChunk) error
	// EstimateSize 估计数据大小
	EstimateSize() (int64, bool)
	// Schema 获取数据模式
	Schema() []meta.FieldMeta
}

// StreamingMetrics 流式处理指标
type StreamingMetrics struct {
	TotalRows      int64        // 总行数
	TotalChunks    int64        // 总块数
	ProcessedRows  int64        // 已处理行数
	ProcessedBytes int64        // 已处理字节数
	StartTime      time.Time    // 开始时间
	LastActivity   time.Time    // 最后活动时间
	Errors         []error      // 错误列表
	mutex          sync.RWMutex // 读写锁
}

// StreamingOptions 流式处理选项
type StreamingOptions struct {
	ChunkSize     int           // 块大小，默认1000
	BufferSize    int           // 缓冲区大小，默认10
	Timeout       time.Duration // 超时时间，默认30秒
	EnableMetrics bool          // 是否启用指标收集
	Compression   bool          // 是否启用压缩
	BackPressure  bool          // 是否启用背压控制
}

// DefaultStreamingOptions 默认流式处理选项
var DefaultStreamingOptions = StreamingOptions{
	ChunkSize:     1000,
	BufferSize:    10,
	Timeout:       30 * time.Second,
	EnableMetrics: true,
	Compression:   false,
	BackPressure:  true,
}

// NewStreamingRowSet 创建新的流式行数据集
func NewStreamingRowSet(producer RowProducer, options *StreamingOptions) *StreamingRowSet {
	if options == nil {
		options = &DefaultStreamingOptions
	}

	ctx, cancel := context.WithCancel(context.Background())

	srs := &StreamingRowSet{
		channel:     make(chan RowChunk, options.BufferSize),
		schema:      producer.Schema(),
		closed:      false,
		ctx:         ctx,
		cancel:      cancel,
		chunkSize:   options.ChunkSize,
		producer:    producer,
		compression: options.Compression,
	}

	if options.EnableMetrics {
		srs.metrics = &StreamingMetrics{
			StartTime:    time.Now(),
			LastActivity: time.Now(),
			Errors:       []error{},
		}
	}

	// 启动数据生产者
	go srs.startProducer()

	return srs
}

// startProducer 启动数据生产者
func (srs *StreamingRowSet) startProducer() {
	defer close(srs.channel)

	if err := srs.producer.Produce(srs.ctx, srs.channel); err != nil {
		// 发送错误块
		select {
		case srs.channel <- RowChunk{Error: err}:
		case <-srs.ctx.Done():
		}
	}
}

// Next 获取下一行数据
func (srs *StreamingRowSet) Next() (Row, bool, error) {
	srs.mutex.Lock()
	defer srs.mutex.Unlock()

	if srs.closed {
		return nil, false, fmt.Errorf("rowset is closed")
	}

	// 如果当前有数据，直接返回
	if srs.currentRow != nil {
		row := srs.currentRow
		srs.currentRow = nil
		srs.updateMetrics(1, 0)
		return row, srs.hasMore, nil
	}

	// 从通道读取新的数据块
	select {
	case chunk, ok := <-srs.channel:
		if !ok {
			// 通道已关闭
			return nil, false, nil
		}

		if chunk.Error != nil {
			srs.recordError(chunk.Error)
			return nil, false, chunk.Error
		}

		if len(chunk.Rows) == 0 {
			return nil, false, nil
		}

		// 返回第一行，缓存剩余行
		srs.hasMore = chunk.HasMore || len(chunk.Rows) > 1

		if len(chunk.Rows) > 1 {
			// 将剩余行重新放入通道
			remainingChunk := RowChunk{
				Rows:      chunk.Rows[1:],
				HasMore:   chunk.HasMore,
				Timestamp: chunk.Timestamp,
				ChunkID:   chunk.ChunkID,
			}
			go func() {
				select {
				case srs.channel <- remainingChunk:
				case <-srs.ctx.Done():
				}
			}()
		}

		srs.updateMetrics(1, 0)
		return chunk.Rows[0], srs.hasMore, nil

	case <-srs.ctx.Done():
		return nil, false, srs.ctx.Err()
	}
}

// Close 关闭数据集
func (srs *StreamingRowSet) Close() error {
	srs.mutex.Lock()
	defer srs.mutex.Unlock()

	if srs.closed {
		return nil
	}

	srs.closed = true
	srs.cancel()

	// 清空通道
	go func() {
		for range srs.channel {
			// 消费剩余数据
		}
	}()

	return nil
}

// Schema 获取数据模式
func (srs *StreamingRowSet) Schema() []meta.FieldMeta {
	return srs.schema
}

// Count 获取数据行数估计
func (srs *StreamingRowSet) Count() (int64, bool) {
	if srs.producer != nil {
		return srs.producer.EstimateSize()
	}
	return 0, false
}

// GetMetrics 获取流式处理指标
func (srs *StreamingRowSet) GetMetrics() *StreamingMetrics {
	if srs.metrics == nil {
		return nil
	}

	srs.metrics.mutex.RLock()
	defer srs.metrics.mutex.RUnlock()

	// 安全复制指标数据，避免复制锁
	metricsCopy := StreamingMetrics{
		TotalRows:      srs.metrics.TotalRows,
		TotalChunks:    srs.metrics.TotalChunks,
		ProcessedRows:  srs.metrics.ProcessedRows,
		ProcessedBytes: srs.metrics.ProcessedBytes,
		StartTime:      srs.metrics.StartTime,
		LastActivity:   srs.metrics.LastActivity,
		Errors:         make([]error, len(srs.metrics.Errors)),
	}
	copy(metricsCopy.Errors, srs.metrics.Errors)

	return &metricsCopy
}

// updateMetrics 更新处理指标
func (srs *StreamingRowSet) updateMetrics(rows int64, bytes int64) {
	if srs.metrics == nil {
		return
	}

	srs.metrics.mutex.Lock()
	defer srs.metrics.mutex.Unlock()

	srs.metrics.ProcessedRows += rows
	srs.metrics.ProcessedBytes += bytes
	srs.metrics.LastActivity = time.Now()
}

// recordError 记录错误
func (srs *StreamingRowSet) recordError(err error) {
	if srs.metrics == nil {
		return
	}

	srs.metrics.mutex.Lock()
	defer srs.metrics.mutex.Unlock()

	srs.metrics.Errors = append(srs.metrics.Errors, err)
}

// 指标获取方法

// GetProcessedRows 获取已处理的行数
func (sm *StreamingMetrics) GetProcessedRows() int64 {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return sm.ProcessedRows
}

// GetProcessingRate 获取处理速率（行/秒）
func (sm *StreamingMetrics) GetProcessingRate() float64 {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	duration := time.Since(sm.StartTime).Seconds()
	if duration == 0 {
		return 0
	}
	return float64(sm.ProcessedRows) / duration
}

// GetErrorCount 获取错误数量
func (sm *StreamingMetrics) GetErrorCount() int {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	return len(sm.Errors)
}

// GetLastErrors 获取最近的错误（最多10个）
func (sm *StreamingMetrics) GetLastErrors() []error {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	errorCount := len(sm.Errors)
	if errorCount == 0 {
		return []error{}
	}

	start := 0
	if errorCount > 10 {
		start = errorCount - 10
	}

	errors := make([]error, errorCount-start)
	copy(errors, sm.Errors[start:])
	return errors
}

// CompressedRowSet 压缩的行数据集包装器
type CompressedRowSet struct {
	underlying RowSet
	reader     io.Reader
	writer     io.Writer
	compressor *gzip.Writer
}

// NewCompressedRowSet 创建压缩的行数据集
func NewCompressedRowSet(underlying RowSet) (*CompressedRowSet, error) {
	return &CompressedRowSet{
		underlying: underlying,
	}, nil
}

// CompressRowChunk 压缩数据块
func CompressRowChunk(chunk RowChunk) ([]byte, error) {
	var compressed []byte

	// 使用gob编码+gzip压缩
	encoder := gob.NewEncoder(&gzipWriter{data: &compressed})
	if err := encoder.Encode(chunk); err != nil {
		return nil, fmt.Errorf("failed to encode chunk: %w", err)
	}

	return compressed, nil
}

// DecompressRowChunk 解压缩数据块
func DecompressRowChunk(data []byte) (RowChunk, error) {
	var chunk RowChunk

	// 使用gzip解压缩+gob解码
	decoder := gob.NewDecoder(&gzipReader{data: data})
	if err := decoder.Decode(&chunk); err != nil {
		return chunk, fmt.Errorf("failed to decode chunk: %w", err)
	}

	return chunk, nil
}

// 辅助类型用于压缩
type gzipWriter struct {
	data *[]byte
}

func (gw *gzipWriter) Write(p []byte) (n int, err error) {
	*gw.data = append(*gw.data, p...)
	return len(p), nil
}

type gzipReader struct {
	data []byte
	pos  int
}

func (gr *gzipReader) Read(p []byte) (n int, err error) {
	if gr.pos >= len(gr.data) {
		return 0, io.EOF
	}

	n = copy(p, gr.data[gr.pos:])
	gr.pos += n
	return n, nil
}
