package step

import (
	"fmt"
	"sync"

	"admin/pkg/pipeline/meta"
)

// StepFactory 步骤创建函数类型
type StepFactory func(stepMeta meta.StepMeta) (Step, error)

// Registry 步骤注册器，管理所有可用的步骤类型
type Registry struct {
	factories map[meta.StepType]StepFactory
	mutex     sync.RWMutex
}

// DefaultRegistry 默认的全局步骤注册器
var DefaultRegistry = NewRegistry()

// NewRegistry 创建一个新的步骤注册器
func NewRegistry() *Registry {
	return &Registry{
		factories: make(map[meta.StepType]StepFactory),
	}
}

// Register 注册一个步骤类型和对应的创建函数
func (r *Registry) Register(stepType meta.StepType, factory StepFactory) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.factories[stepType] = factory
}

// Unregister 注销一个步骤类型
func (r *Registry) Unregister(stepType meta.StepType) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	delete(r.factories, stepType)
}

// Create 根据步骤元数据创建步骤实例
func (r *Registry) Create(stepMeta meta.StepMeta) (Step, error) {
	r.mutex.RLock()
	factory, exists := r.factories[stepMeta.Type]
	r.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("unknown step type: %s", stepMeta.Type)
	}

	return factory(stepMeta)
}

// GetSupportedTypes 获取所有支持的步骤类型
func (r *Registry) GetSupportedTypes() []meta.StepType {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	types := make([]meta.StepType, 0, len(r.factories))
	for stepType := range r.factories {
		types = append(types, stepType)
	}
	return types
}

// IsSupported 检查是否支持指定的步骤类型
func (r *Registry) IsSupported(stepType meta.StepType) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	_, exists := r.factories[stepType]
	return exists
}

// GetRegisteredCount 获取已注册步骤类型的数量
func (r *Registry) GetRegisteredCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return len(r.factories)
}

// 便捷函数，使用默认注册器

// Register 在默认注册器中注册步骤类型
func Register(stepType meta.StepType, factory StepFactory) {
	DefaultRegistry.Register(stepType, factory)
}

// Unregister 从默认注册器中注销步骤类型
func Unregister(stepType meta.StepType) {
	DefaultRegistry.Unregister(stepType)
}

// Create 使用默认注册器创建步骤实例
func Create(stepMeta meta.StepMeta) (Step, error) {
	return DefaultRegistry.Create(stepMeta)
}

// GetSupportedTypes 获取默认注册器支持的所有步骤类型
func GetSupportedTypes() []meta.StepType {
	return DefaultRegistry.GetSupportedTypes()
}

// IsSupported 检查默认注册器是否支持指定的步骤类型
func IsSupported(stepType meta.StepType) bool {
	return DefaultRegistry.IsSupported(stepType)
}
