package step

import (
	"context"
	"fmt"
	"sync"
	"time"

	"admin/pkg/pipeline/meta"
)

// Pipeline 数据管道，支持异步和并行处理
type Pipeline struct {
	steps    []Step            // 步骤列表
	executor *PipelineExecutor // 执行器
	config   PipelineConfig    // 配置
	metrics  *PipelineMetrics  // 指标
	status   PipelineStatus    // 状态
	mutex    sync.RWMutex      // 读写锁
}

// PipelineConfig 管道配置
type PipelineConfig struct {
	MaxConcurrency     int           // 最大并发数
	BufferSize         int           // 缓冲区大小
	Timeout            time.Duration // 超时时间
	EnableBackPressure bool          // 是否启用背压控制
	EnableMetrics      bool          // 是否启用指标收集
	RetryPolicy        RetryPolicy   // 重试策略
}

// PipelineStatus 管道状态
type PipelineStatus int

const (
	PipelineStatusIdle    PipelineStatus = iota // 空闲
	PipelineStatusRunning                       // 运行中
	PipelineStatusPaused                        // 暂停
	PipelineStatusStopped                       // 停止
	PipelineStatusFailed                        // 失败
)

// String 返回状态的字符串表示
func (s PipelineStatus) String() string {
	switch s {
	case PipelineStatusIdle:
		return "IDLE"
	case PipelineStatusRunning:
		return "RUNNING"
	case PipelineStatusPaused:
		return "PAUSED"
	case PipelineStatusStopped:
		return "STOPPED"
	case PipelineStatusFailed:
		return "FAILED"
	default:
		return "UNKNOWN"
	}
}

// PipelineMetrics 管道指标
type PipelineMetrics struct {
	StartTime      time.Time               // 开始时间
	EndTime        time.Time               // 结束时间
	TotalRows      int64                   // 总行数
	ProcessedRows  int64                   // 已处理行数
	FailedRows     int64                   // 失败行数
	StepMetrics    map[string]*StepMetrics // 步骤指标
	ThroughputRate float64                 // 吞吐率
	ErrorRate      float64                 // 错误率
	mutex          sync.RWMutex            // 读写锁
}

// StepMetrics 步骤指标
type StepMetrics struct {
	Name        string        // 步骤名称
	Type        meta.StepType // 步骤类型
	InputRows   int64         // 输入行数
	OutputRows  int64         // 输出行数
	ErrorRows   int64         // 错误行数
	Duration    time.Duration // 执行时长
	MemoryUsage int64         // 内存使用量
	CPUUsage    float64       // CPU使用率
}

// RetryPolicy 重试策略
type RetryPolicy struct {
	MaxRetries    int           // 最大重试次数
	RetryDelay    time.Duration // 重试延迟
	BackoffFactor float64       // 退避因子
}

// DefaultPipelineConfig 默认管道配置
var DefaultPipelineConfig = PipelineConfig{
	MaxConcurrency:     10,
	BufferSize:         1000,
	Timeout:            5 * time.Minute,
	EnableBackPressure: true,
	EnableMetrics:      true,
	RetryPolicy: RetryPolicy{
		MaxRetries:    3,
		RetryDelay:    1 * time.Second,
		BackoffFactor: 2.0,
	},
}

// NewPipeline 创建新的数据管道
func NewPipeline(steps []Step, config *PipelineConfig) *Pipeline {
	if config == nil {
		config = &DefaultPipelineConfig
	}

	pipeline := &Pipeline{
		steps:  steps,
		config: *config,
		status: PipelineStatusIdle,
	}

	if config.EnableMetrics {
		pipeline.metrics = &PipelineMetrics{
			StepMetrics: make(map[string]*StepMetrics),
		}
	}

	pipeline.executor = NewPipelineExecutor(pipeline)
	return pipeline
}

// Execute 执行管道
func (p *Pipeline) Execute(ctx context.Context, input RowSet) (RowSet, error) {
	p.mutex.Lock()
	if p.status == PipelineStatusRunning {
		p.mutex.Unlock()
		return nil, fmt.Errorf("pipeline is already running")
	}
	p.status = PipelineStatusRunning
	p.mutex.Unlock()

	defer func() {
		p.mutex.Lock()
		p.status = PipelineStatusStopped
		p.mutex.Unlock()
	}()

	if p.metrics != nil {
		p.metrics.StartTime = time.Now()
	}

	result, err := p.executor.Execute(ctx, input)

	if p.metrics != nil {
		p.metrics.EndTime = time.Now()
		p.updateThroughputMetrics()
	}

	if err != nil {
		p.mutex.Lock()
		p.status = PipelineStatusFailed
		p.mutex.Unlock()
	}

	return result, err
}

// GetStatus 获取管道状态
func (p *Pipeline) GetStatus() PipelineStatus {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.status
}

// GetMetrics 获取管道指标
func (p *Pipeline) GetMetrics() *PipelineMetrics {
	if p.metrics == nil {
		return nil
	}

	p.metrics.mutex.RLock()
	defer p.metrics.mutex.RUnlock()

	// 手动创建指标副本，避免复制mutex
	metricsCopy := &PipelineMetrics{
		StartTime:      p.metrics.StartTime,
		EndTime:        p.metrics.EndTime,
		TotalRows:      p.metrics.TotalRows,
		ProcessedRows:  p.metrics.ProcessedRows,
		FailedRows:     p.metrics.FailedRows,
		ThroughputRate: p.metrics.ThroughputRate,
		ErrorRate:      p.metrics.ErrorRate,
		StepMetrics:    make(map[string]*StepMetrics),
	}

	for k, v := range p.metrics.StepMetrics {
		stepMetricsCopy := *v
		metricsCopy.StepMetrics[k] = &stepMetricsCopy
	}

	return metricsCopy
}

// updateThroughputMetrics 更新吞吐量指标
func (p *Pipeline) updateThroughputMetrics() {
	if p.metrics == nil {
		return
	}

	duration := p.metrics.EndTime.Sub(p.metrics.StartTime).Seconds()
	if duration > 0 {
		p.metrics.ThroughputRate = float64(p.metrics.ProcessedRows) / duration
		if p.metrics.TotalRows > 0 {
			p.metrics.ErrorRate = float64(p.metrics.FailedRows) / float64(p.metrics.TotalRows)
		}
	}
}

// PipelineExecutor 管道执行器
type PipelineExecutor struct {
	pipeline   *Pipeline
	workerPool *WorkerPool
}

// NewPipelineExecutor 创建管道执行器
func NewPipelineExecutor(pipeline *Pipeline) *PipelineExecutor {
	return &PipelineExecutor{
		pipeline:   pipeline,
		workerPool: NewWorkerPool(pipeline.config.MaxConcurrency),
	}
}

// Execute 执行管道
func (pe *PipelineExecutor) Execute(ctx context.Context, input RowSet) (RowSet, error) {
	if len(pe.pipeline.steps) == 0 {
		return input, nil
	}

	// 启动工作池
	pe.workerPool.Start(ctx)
	defer pe.workerPool.Stop()

	currentRowSet := input

	// 顺序执行步骤
	for i, step := range pe.pipeline.steps {
		stepStartTime := time.Now()

		// 记录步骤开始
		if pe.pipeline.metrics != nil {
			pe.recordStepStart(step, stepStartTime)
		}

		// 执行步骤
		output, err := pe.executeStepWithRetry(ctx, step, currentRowSet)
		if err != nil {
			if pe.pipeline.metrics != nil {
				pe.recordStepError(step, err, time.Since(stepStartTime))
			}
			return nil, fmt.Errorf("step %d (%s) failed: %w", i, step.GetMeta().Name, err)
		}

		// 记录步骤完成
		if pe.pipeline.metrics != nil {
			pe.recordStepComplete(step, currentRowSet, output, time.Since(stepStartTime))
		}

		// 关闭前一个RowSet（除了初始输入）
		if i > 0 && currentRowSet != nil {
			currentRowSet.Close()
		}

		currentRowSet = output
	}

	return currentRowSet, nil
}

// executeStepWithRetry 带重试的步骤执行
func (pe *PipelineExecutor) executeStepWithRetry(ctx context.Context, step Step, input RowSet) (RowSet, error) {
	retryPolicy := pe.pipeline.config.RetryPolicy
	var lastErr error

	for attempt := 0; attempt <= retryPolicy.MaxRetries; attempt++ {
		if attempt > 0 {
			// 计算退避延迟
			delay := time.Duration(float64(retryPolicy.RetryDelay) *
				(retryPolicy.BackoffFactor * float64(attempt-1)))

			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}

		output, err := step.Execute(ctx, input)
		if err == nil {
			return output, nil
		}

		lastErr = err

		// 检查是否应该重试
		if !pe.shouldRetry(err) {
			break
		}
	}

	return nil, fmt.Errorf("step failed after %d attempts: %w",
		retryPolicy.MaxRetries+1, lastErr)
}

// shouldRetry 判断是否应该重试
func (pe *PipelineExecutor) shouldRetry(err error) bool {
	// 这里可以根据错误类型决定是否重试
	// 目前简单处理，所有错误都重试
	return true
}

// recordStepStart 记录步骤开始
func (pe *PipelineExecutor) recordStepStart(step Step, startTime time.Time) {
	stepMeta := step.GetMeta()
	pe.pipeline.metrics.mutex.Lock()
	defer pe.pipeline.metrics.mutex.Unlock()

	if _, exists := pe.pipeline.metrics.StepMetrics[stepMeta.Name]; !exists {
		pe.pipeline.metrics.StepMetrics[stepMeta.Name] = &StepMetrics{
			Name: stepMeta.Name,
			Type: stepMeta.Type,
		}
	}
}

// recordStepComplete 记录步骤完成
func (pe *PipelineExecutor) recordStepComplete(step Step, input, output RowSet, duration time.Duration) {
	stepMeta := step.GetMeta()
	pe.pipeline.metrics.mutex.Lock()
	defer pe.pipeline.metrics.mutex.Unlock()

	stepMetrics := pe.pipeline.metrics.StepMetrics[stepMeta.Name]
	stepMetrics.Duration = duration

	// 统计输入输出行数
	if input != nil {
		if count, ok := input.Count(); ok {
			stepMetrics.InputRows = count
		}
	}
	if output != nil {
		if count, ok := output.Count(); ok {
			stepMetrics.OutputRows = count
		}
	}
}

// recordStepError 记录步骤错误
func (pe *PipelineExecutor) recordStepError(step Step, err error, duration time.Duration) {
	stepMeta := step.GetMeta()
	pe.pipeline.metrics.mutex.Lock()
	defer pe.pipeline.metrics.mutex.Unlock()

	stepMetrics := pe.pipeline.metrics.StepMetrics[stepMeta.Name]
	stepMetrics.Duration = duration
	stepMetrics.ErrorRows++
}

// WorkerPool 工作池
type WorkerPool struct {
	maxWorkers int
	taskQueue  chan func()
	workers    []*Worker
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
}

// Worker 工作者
type Worker struct {
	id       int
	taskChan chan func()
	quit     chan bool
	wg       *sync.WaitGroup
}

// NewWorkerPool 创建工作池
func NewWorkerPool(maxWorkers int) *WorkerPool {
	return &WorkerPool{
		maxWorkers: maxWorkers,
		taskQueue:  make(chan func(), maxWorkers*2),
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start(ctx context.Context) {
	wp.ctx, wp.cancel = context.WithCancel(ctx)
	wp.workers = make([]*Worker, wp.maxWorkers)

	for i := 0; i < wp.maxWorkers; i++ {
		worker := &Worker{
			id:       i,
			taskChan: make(chan func()),
			quit:     make(chan bool),
			wg:       &wp.wg,
		}
		wp.workers[i] = worker
		wp.wg.Add(1)
		go worker.start()
	}

	// 启动任务分发器
	go wp.dispatchTasks()
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() {
	if wp.cancel != nil {
		wp.cancel()
	}

	// 停止所有工作者
	for _, worker := range wp.workers {
		close(worker.quit)
	}

	// 等待所有工作者完成
	wp.wg.Wait()
}

// Submit 提交任务
func (wp *WorkerPool) Submit(task func()) {
	select {
	case wp.taskQueue <- task:
	case <-wp.ctx.Done():
	}
}

// dispatchTasks 分发任务
func (wp *WorkerPool) dispatchTasks() {
	defer close(wp.taskQueue)

	for {
		select {
		case task := <-wp.taskQueue:
			// 将任务分发给空闲的工作者
			select {
			case wp.workers[0].taskChan <- task:
			case <-wp.ctx.Done():
				return
			}
		case <-wp.ctx.Done():
			return
		}
	}
}

// start 启动工作者
func (w *Worker) start() {
	defer w.wg.Done()

	for {
		select {
		case task := <-w.taskChan:
			if task != nil {
				task()
			}
		case <-w.quit:
			return
		}
	}
}

// 管道构建器

// PipelineBuilder 管道构建器
type PipelineBuilder struct {
	steps  []Step
	config PipelineConfig
}

// NewPipelineBuilder 创建管道构建器
func NewPipelineBuilder() *PipelineBuilder {
	return &PipelineBuilder{
		steps:  []Step{},
		config: DefaultPipelineConfig,
	}
}

// AddStep 添加步骤
func (pb *PipelineBuilder) AddStep(step Step) *PipelineBuilder {
	pb.steps = append(pb.steps, step)
	return pb
}

// SetConfig 设置配置
func (pb *PipelineBuilder) SetConfig(config PipelineConfig) *PipelineBuilder {
	pb.config = config
	return pb
}

// SetMaxConcurrency 设置最大并发数
func (pb *PipelineBuilder) SetMaxConcurrency(maxConcurrency int) *PipelineBuilder {
	pb.config.MaxConcurrency = maxConcurrency
	return pb
}

// SetBufferSize 设置缓冲区大小
func (pb *PipelineBuilder) SetBufferSize(bufferSize int) *PipelineBuilder {
	pb.config.BufferSize = bufferSize
	return pb
}

// SetTimeout 设置超时时间
func (pb *PipelineBuilder) SetTimeout(timeout time.Duration) *PipelineBuilder {
	pb.config.Timeout = timeout
	return pb
}

// Build 构建管道
func (pb *PipelineBuilder) Build() *Pipeline {
	return NewPipeline(pb.steps, &pb.config)
}
