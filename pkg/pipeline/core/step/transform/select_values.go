package transform

import (
	"context"
	"fmt"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// SelectValues 字段选择步骤，用于选择指定的字段并可进行重命名
type SelectValues struct {
	*step.BaseStep
	fieldConfigs []FieldSelection // 选择的字段配置
}

// FieldSelection 字段选择配置
type FieldSelection struct {
	SourceField string `json:"source_field"` // 源字段名
	TargetField string `json:"target_field"` // 目标字段名（重命名）
	Remove      bool   `json:"remove"`       // 是否移除字段
}

// NewSelectValues 创建一个新的字段选择步骤
func NewSelectValues(stepMeta meta.StepMeta) *SelectValues {
	return &SelectValues{
		BaseStep:     step.NewBaseStep(stepMeta),
		fieldConfigs: []FieldSelection{},
	}
}

// Initialize 初始化步骤
func (s *SelectValues) Initialize() error {
	if err := s.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中解析字段选择规则
	if err := s.parseFieldSelections(); err != nil {
		return fmt.Errorf("failed to parse field selections: %w", err)
	}

	return nil
}

// Execute 执行字段选择
func (s *SelectValues) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	var outputRows []map[string]interface{}

	// 处理每一行数据
	for {
		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		// 应用字段选择规则
		// 将Row转换为map[string]interface{}
		rowData := make(map[string]interface{})
		for _, field := range row.Fields() {
			rowData[field] = row.Get(field)
		}

		outputRow := s.applyFieldSelections(rowData)
		if outputRow != nil {
			outputRows = append(outputRows, outputRow)
		}
	}

	// 构建输出 schema
	outputSchema := s.buildOutputSchema()
	return step.NewMemoryRowSet(outputRows, outputSchema), nil
}

// Validate 验证步骤配置
func (s *SelectValues) Validate() error {
	if err := s.BaseStep.Validate(); err != nil {
		return err
	}

	// 验证字段配置存在
	if len(s.fieldConfigs) == 0 {
		// 检查配置中是否有字段定义
		if _, exists := s.GetConfigSlice("fields"); !exists {
			return meta.NewValidationError("SelectValues step requires 'fields' configuration")
		}
	}

	// 验证字段选择配置
	for i, field := range s.fieldConfigs {
		if field.SourceField == "" {
			return meta.NewValidationErrorf("field selection %d: source_field is required", i)
		}
		// 如果没有指定目标字段名，使用源字段名
		if field.TargetField == "" && !field.Remove {
			s.fieldConfigs[i].TargetField = field.SourceField
		}
	}

	return nil
}

// parseFieldSelections 解析字段选择配置
func (s *SelectValues) parseFieldSelections() error {
	// 从配置中获取字段列表
	fieldsConfig, exists := s.GetConfigSlice("fields")
	if !exists {
		return fmt.Errorf("fields configuration not found")
	}

	s.fieldConfigs = []FieldSelection{}

	for i, fieldConfig := range fieldsConfig {
		fieldMap, ok := fieldConfig.(map[string]interface{})
		if !ok {
			return fmt.Errorf("field %d: invalid field configuration format", i)
		}

		selection := FieldSelection{}

		// 解析源字段名
		if sourceField, ok := fieldMap["source_field"].(string); ok {
			selection.SourceField = sourceField
		} else {
			return fmt.Errorf("field %d: source_field is required and must be a string", i)
		}

		// 解析目标字段名（可选，默认与源字段相同）
		if targetField, ok := fieldMap["target_field"].(string); ok {
			selection.TargetField = targetField
		} else {
			selection.TargetField = selection.SourceField
		}

		// 解析是否移除字段（可选，默认false）
		if remove, ok := fieldMap["remove"].(bool); ok {
			selection.Remove = remove
		}

		s.fieldConfigs = append(s.fieldConfigs, selection)
	}

	return nil
}

// applyFieldSelections 应用字段选择规则到单行数据
func (s *SelectValues) applyFieldSelections(inputRow map[string]interface{}) map[string]interface{} {
	outputData := make(map[string]interface{})

	// 如果没有配置字段选择规则，复制所有字段
	if len(s.fieldConfigs) == 0 {
		for field, value := range inputRow {
			outputData[field] = value
		}
		return outputData
	}

	// 应用字段选择规则
	for _, selection := range s.fieldConfigs {
		// 如果标记为移除，跳过该字段
		if selection.Remove {
			continue
		}

		// 获取源字段值
		if sourceValue, exists := inputRow[selection.SourceField]; exists {
			// 设置到目标字段
			outputData[selection.TargetField] = sourceValue
		}
	}

	return outputData
}

// buildOutputSchema 构建输出数据的schema
func (s *SelectValues) buildOutputSchema() []meta.FieldMeta {
	var outputSchema []meta.FieldMeta

	for _, selection := range s.fieldConfigs {
		if !selection.Remove {
			outputSchema = append(outputSchema, meta.FieldMeta{
				Name: selection.TargetField,
				Type: meta.FieldTypeString, // 默认为字符串类型
			})
		}
	}

	return outputSchema
}
