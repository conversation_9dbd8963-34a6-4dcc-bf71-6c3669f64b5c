package transform

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// RegexEval 正则表达式评估转换步骤
type RegexEval struct {
	*step.BaseStep
	config     RegexEvalConfig
	compiledRe *regexp.Regexp
}

// RegexEvalConfig 正则评估配置
type RegexEvalConfig struct {
	FieldName     string                   `json:"field_name" yaml:"field_name"`             // 要处理的字段名
	Pattern       string                   `json:"pattern" yaml:"pattern"`                   // 正则表达式模式
	OutputFields  []RegexOutputFieldConfig `json:"output_fields" yaml:"output_fields"`       // 输出字段配置
	ReplaceWith   string                   `json:"replace_with" yaml:"replace_with"`         // 替换内容(仅在操作为replace时使用)
	Operation     string                   `json:"operation" yaml:"operation"`               // 操作类型: match, extract, replace, split
	FailOnNoMatch bool                     `json:"fail_on_no_match" yaml:"fail_on_no_match"` // 不匹配时是否失败
}

// RegexOutputFieldConfig 正则输出字段配置
type RegexOutputFieldConfig struct {
	Name         string `json:"name" yaml:"name"`                   // 字段名
	Type         string `json:"type" yaml:"type"`                   // 字段类型
	GroupIndex   int    `json:"group_index" yaml:"group_index"`     // 捕获组索引
	DefaultValue string `json:"default_value" yaml:"default_value"` // 默认值
}

// NewRegexEval 创建新的正则表达式评估步骤
func NewRegexEval(stepMeta meta.StepMeta) step.Step {
	return &RegexEval{
		BaseStep: step.NewBaseStep(stepMeta),
	}
}

// Initialize 初始化步骤
func (s *RegexEval) Initialize() error {
	// 解析配置
	if err := s.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse regex eval config: %w", err)
	}

	// 验证配置
	if err := s.validateConfig(); err != nil {
		return fmt.Errorf("invalid regex eval config: %w", err)
	}

	// 编译正则表达式
	if err := s.compileRegex(); err != nil {
		return fmt.Errorf("failed to compile regex: %w", err)
	}

	return nil
}

// Execute 执行正则表达式评估转换
func (s *RegexEval) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input rowset is nil")
	}

	// 检查字段是否存在
	var fieldFound bool
	for _, field := range input.Schema() {
		if field.Name == s.config.FieldName {
			fieldFound = true
			break
		}
	}
	if !fieldFound {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("field %s not found in input", s.config.FieldName)
	}

	// 构建输出字段
	outputFields := s.buildOutputFields(input.Schema())

	var outputRows []map[string]interface{}

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), err
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		outputRowData, err := s.processRow(row)
		if err != nil {
			if s.config.FailOnNoMatch {
				return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("regex eval error: %w", err)
			}
			// 不失败模式下，跳过该行或使用默认值
			continue
		}

		if outputRowData != nil {
			outputRows = append(outputRows, outputRowData)
		}
	}

	return step.NewMemoryRowSet(outputRows, outputFields), nil
}

// parseConfig 解析配置
func (s *RegexEval) parseConfig() error {
	configData, err := json.Marshal(s.GetMeta().Config)
	if err != nil {
		return err
	}

	return json.Unmarshal(configData, &s.config)
}

// validateConfig 验证配置
func (s *RegexEval) validateConfig() error {
	if s.config.FieldName == "" {
		return fmt.Errorf("field_name is required")
	}

	if s.config.Pattern == "" {
		return fmt.Errorf("pattern is required")
	}

	if s.config.Operation == "" {
		s.config.Operation = "match" // 默认操作
	}

	validOperations := map[string]bool{
		"match": true, "extract": true, "replace": true, "split": true,
	}
	if !validOperations[s.config.Operation] {
		return fmt.Errorf("invalid operation %s, must be one of: match, extract, replace, split", s.config.Operation)
	}

	// 验证输出字段配置
	for i, outputField := range s.config.OutputFields {
		if outputField.Name == "" {
			return fmt.Errorf("output field %d: name is required", i)
		}
		if outputField.Type == "" {
			outputField.Type = "string" // 默认类型
		}
	}

	return nil
}

// compileRegex 编译正则表达式
func (s *RegexEval) compileRegex() error {
	re, err := regexp.Compile(s.config.Pattern)
	if err != nil {
		return fmt.Errorf("invalid regex pattern: %w", err)
	}
	s.compiledRe = re
	return nil
}

// buildOutputFields 构建输出字段
func (s *RegexEval) buildOutputFields(inputFields []meta.FieldMeta) []meta.FieldMeta {
	var outputFields []meta.FieldMeta

	// 复制原有字段
	outputFields = append(outputFields, inputFields...)

	// 添加输出字段
	for _, outputField := range s.config.OutputFields {
		// 检查字段是否已存在
		fieldExists := false
		for _, existing := range inputFields {
			if existing.Name == outputField.Name {
				fieldExists = true
				break
			}
		}
		if !fieldExists {
			outputFields = append(outputFields, meta.FieldMeta{
				Name: outputField.Name,
				Type: s.mapTypeToFieldType(outputField.Type),
			})
		}
	}

	return outputFields
}

// mapTypeToFieldType 映射类型到字段类型
func (s *RegexEval) mapTypeToFieldType(configType string) meta.FieldType {
	switch configType {
	case "string":
		return meta.FieldTypeString
	case "int":
		return meta.FieldTypeInteger
	case "float":
		return meta.FieldTypeFloat
	case "bool":
		return meta.FieldTypeBoolean
	default:
		return meta.FieldTypeString
	}
}

// processRow 处理单行数据
func (s *RegexEval) processRow(row step.Row) (map[string]interface{}, error) {
	// 复制原有字段
	outputRow := make(map[string]interface{})
	for _, field := range row.Fields() {
		outputRow[field] = row.Get(field)
	}

	// 获取字段值
	fieldValue, ok := row.GetString(s.config.FieldName)
	if !ok || fieldValue == "" {
		// 字段为空或不存在，使用默认值
		s.setDefaultValues(outputRow)
		return outputRow, nil
	}

	// 执行正则操作
	switch s.config.Operation {
	case "match":
		return s.processMatch(outputRow, fieldValue)
	case "extract":
		return s.processExtract(outputRow, fieldValue)
	case "replace":
		return s.processReplace(outputRow, fieldValue)
	case "split":
		return s.processSplit(outputRow, fieldValue)
	default:
		return nil, fmt.Errorf("unknown operation: %s", s.config.Operation)
	}
}

// processMatch 处理匹配操作
func (s *RegexEval) processMatch(outputRow map[string]interface{}, fieldValue string) (map[string]interface{}, error) {
	matched := s.compiledRe.MatchString(fieldValue)

	for _, outputField := range s.config.OutputFields {
		if outputField.Name != "" {
			outputRow[outputField.Name] = matched
		}
	}

	return outputRow, nil
}

// processExtract 处理提取操作
func (s *RegexEval) processExtract(outputRow map[string]interface{}, fieldValue string) (map[string]interface{}, error) {
	matches := s.compiledRe.FindStringSubmatch(fieldValue)

	if len(matches) == 0 {
		// 没有匹配，使用默认值
		s.setDefaultValues(outputRow)
		return outputRow, nil
	}

	for _, outputField := range s.config.OutputFields {
		var value interface{}

		// 确定值
		if outputField.GroupIndex >= 0 && outputField.GroupIndex < len(matches) {
			value = matches[outputField.GroupIndex]
		} else {
			value = outputField.DefaultValue
		}

		// 类型转换
		convertedValue, err := s.convertValue(value, outputField.Type)
		if err != nil {
			convertedValue = outputField.DefaultValue
		}

		outputRow[outputField.Name] = convertedValue
	}

	return outputRow, nil
}

// processReplace 处理替换操作
func (s *RegexEval) processReplace(outputRow map[string]interface{}, fieldValue string) (map[string]interface{}, error) {
	result := s.compiledRe.ReplaceAllString(fieldValue, s.config.ReplaceWith)

	for _, outputField := range s.config.OutputFields {
		if outputField.Name != "" {
			outputRow[outputField.Name] = result
		}
	}

	return outputRow, nil
}

// processSplit 处理分割操作
func (s *RegexEval) processSplit(outputRow map[string]interface{}, fieldValue string) (map[string]interface{}, error) {
	parts := s.compiledRe.Split(fieldValue, -1)

	for i, outputField := range s.config.OutputFields {
		var value interface{}

		if i < len(parts) {
			value = parts[i]
		} else {
			value = outputField.DefaultValue
		}

		convertedValue, err := s.convertValue(value, outputField.Type)
		if err != nil {
			convertedValue = outputField.DefaultValue
		}

		outputRow[outputField.Name] = convertedValue
	}

	return outputRow, nil
}

// setDefaultValues 设置默认值
func (s *RegexEval) setDefaultValues(outputRow map[string]interface{}) {
	for _, outputField := range s.config.OutputFields {
		convertedValue, err := s.convertValue(outputField.DefaultValue, outputField.Type)
		if err != nil {
			convertedValue = nil
		}
		outputRow[outputField.Name] = convertedValue
	}
}

// convertValue 转换值类型
func (s *RegexEval) convertValue(value interface{}, targetType string) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	strValue := fmt.Sprintf("%v", value)

	switch targetType {
	case "string":
		return strValue, nil
	case "int":
		return strconv.ParseInt(strValue, 10, 64)
	case "float":
		return strconv.ParseFloat(strValue, 64)
	case "bool":
		return strconv.ParseBool(strValue)
	default:
		return strValue, nil
	}
}

// GetType 返回步骤类型
func (s *RegexEval) GetType() meta.StepType {
	return meta.StepTypeRegexEval
}
