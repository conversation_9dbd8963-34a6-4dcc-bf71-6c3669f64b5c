package transform

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// Sort 排序转换步骤
type Sort struct {
	*step.BaseStep
	sortFields      []SortField
	caseInsensitive bool
	preSort         bool
	maxRowsInMemory int
}

// SortField 排序字段配置
type SortField struct {
	FieldName string    `json:"field_name" yaml:"field_name"`
	SortOrder SortOrder `json:"sort_order" yaml:"sort_order"`
}

// SortOrder 排序方向
type SortOrder string

const (
	SortOrderAsc  SortOrder = "asc"
	SortOrderDesc SortOrder = "desc"
)

// SortableRow 可排序的行数据
type SortableRow struct {
	Row        step.Row
	SortValues []interface{}
}

// SortableRows 可排序的行数据集合
type SortableRows struct {
	Rows            []SortableRow
	SortFields      []SortField
	CaseInsensitive bool
}

// NewSort 创建新的排序步骤
func NewSort(stepMeta meta.StepMeta) *Sort {
	s := &Sort{
		BaseStep:        step.NewBaseStep(stepMeta),
		sortFields:      []SortField{},
		caseInsensitive: false,
		preSort:         false,
		maxRowsInMemory: 100000, // 默认最大内存行数
	}

	// 解析排序字段配置
	if sortFields, ok := stepMeta.Config["sort_fields"].([]interface{}); ok {
		for _, field := range sortFields {
			if fieldMap, ok := field.(map[string]interface{}); ok {
				sortField := SortField{}

				if fieldName, ok := fieldMap["field_name"].(string); ok {
					sortField.FieldName = fieldName
				}

				if sortOrder, ok := fieldMap["sort_order"].(string); ok {
					sortField.SortOrder = SortOrder(sortOrder)
				} else {
					sortField.SortOrder = SortOrderAsc // 默认升序
				}

				s.sortFields = append(s.sortFields, sortField)
			}
		}
	}

	// 解析其他配置参数
	if caseInsensitive, ok := stepMeta.Config["case_insensitive"].(bool); ok {
		s.caseInsensitive = caseInsensitive
	}

	if preSort, ok := stepMeta.Config["pre_sort"].(bool); ok {
		s.preSort = preSort
	}

	if maxRows, ok := stepMeta.Config["max_rows_in_memory"].(float64); ok {
		s.maxRowsInMemory = int(maxRows)
	}

	return s
}

// Execute 执行排序转换
func (s *Sort) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	if len(s.sortFields) == 0 {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("at least one sort field is required for Sort step")
	}

	var allRows []SortableRow
	var schema []meta.FieldMeta

	// 复制输入schema
	if input.Schema() != nil {
		schema = make([]meta.FieldMeta, len(input.Schema()))
		copy(schema, input.Schema())
	}

	// 收集所有行数据
	rowCount := 0
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		rowCount++
		if rowCount > s.maxRowsInMemory {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("too many rows (%d) exceed max_rows_in_memory (%d)", rowCount, s.maxRowsInMemory)
		}

		// 验证排序字段是否存在
		if len(allRows) == 0 {
			if err := s.validateSortFields(row); err != nil {
				return step.NewEmptyRowSet([]meta.FieldMeta{}), err
			}
		}

		// 提取排序值
		sortValues, err := s.extractSortValues(row)
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to extract sort values: %w", err)
		}

		allRows = append(allRows, SortableRow{
			Row:        row,
			SortValues: sortValues,
		})
	}

	// 执行排序
	sortableRows := &SortableRows{
		Rows:            allRows,
		SortFields:      s.sortFields,
		CaseInsensitive: s.caseInsensitive,
	}

	sort.Sort(sortableRows)

	// 转换为输出行数据
	outputRows := make([]step.Row, len(allRows))
	for i, sortableRow := range allRows {
		outputRows[i] = sortableRow.Row
	}

	return step.NewMemoryRowSetFromRows(outputRows, schema), nil
}

// validateSortFields 验证排序字段是否存在
func (s *Sort) validateSortFields(row step.Row) error {
	rowFields := row.Fields()
	fieldSet := make(map[string]bool)
	for _, field := range rowFields {
		fieldSet[field] = true
	}

	for _, sortField := range s.sortFields {
		if !fieldSet[sortField.FieldName] {
			return fmt.Errorf("sort field '%s' not found in input data", sortField.FieldName)
		}
	}

	return nil
}

// extractSortValues 提取排序值
func (s *Sort) extractSortValues(row step.Row) ([]interface{}, error) {
	sortValues := make([]interface{}, len(s.sortFields))

	for i, sortField := range s.sortFields {
		value := row.Get(sortField.FieldName)

		// 对字符串进行大小写处理
		if s.caseInsensitive {
			if strValue, ok := value.(string); ok {
				value = strings.ToLower(strValue)
			}
		}

		sortValues[i] = value
	}

	return sortValues, nil
}

// SortableRows 实现 sort.Interface

// Len 返回元素数量
func (sr *SortableRows) Len() int {
	return len(sr.Rows)
}

// Swap 交换两个元素
func (sr *SortableRows) Swap(i, j int) {
	sr.Rows[i], sr.Rows[j] = sr.Rows[j], sr.Rows[i]
}

// Less 比较两个元素
func (sr *SortableRows) Less(i, j int) bool {
	rowI := sr.Rows[i]
	rowJ := sr.Rows[j]

	for idx, sortField := range sr.SortFields {
		valueI := rowI.SortValues[idx]
		valueJ := rowJ.SortValues[idx]

		// 处理 nil 值
		if valueI == nil && valueJ == nil {
			continue
		}
		if valueI == nil {
			return sortField.SortOrder == SortOrderAsc
		}
		if valueJ == nil {
			return sortField.SortOrder == SortOrderDesc
		}

		// 比较值
		cmp := sr.compareValues(valueI, valueJ)
		if cmp == 0 {
			continue // 值相等，比较下一个字段
		}

		// 根据排序方向返回结果
		if sortField.SortOrder == SortOrderDesc {
			return cmp > 0
		}
		return cmp < 0
	}

	return false // 所有字段都相等
}

// compareValues 比较两个值
func (sr *SortableRows) compareValues(a, b interface{}) int {
	// 尝试数值比较
	if numA, okA := sr.getNumericValue(a); okA {
		if numB, okB := sr.getNumericValue(b); okB {
			if numA < numB {
				return -1
			} else if numA > numB {
				return 1
			}
			return 0
		}
	}

	// 字符串比较
	strA := sr.getStringValue(a)
	strB := sr.getStringValue(b)

	if sr.CaseInsensitive {
		strA = strings.ToLower(strA)
		strB = strings.ToLower(strB)
	}

	if strA < strB {
		return -1
	} else if strA > strB {
		return 1
	}
	return 0
}

// getNumericValue 尝试获取数值
func (sr *SortableRows) getNumericValue(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case float64:
		return v, true
	case float32:
		return float64(v), true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		}
	}
	return 0, false
}

// getStringValue 获取字符串值
func (sr *SortableRows) getStringValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// GetType 获取步骤类型
func (s *Sort) GetType() meta.StepType {
	return meta.StepTypeSort
}

// Validate 验证步骤配置
func (s *Sort) Validate() error {
	if len(s.sortFields) == 0 {
		return fmt.Errorf("at least one sort field is required for Sort step")
	}

	for i, sortField := range s.sortFields {
		if sortField.FieldName == "" {
			return fmt.Errorf("sort field %d: field_name is required", i)
		}

		if sortField.SortOrder != SortOrderAsc && sortField.SortOrder != SortOrderDesc {
			return fmt.Errorf("sort field %d: invalid sort_order '%s', must be 'asc' or 'desc'", i, sortField.SortOrder)
		}
	}

	if s.maxRowsInMemory <= 0 {
		return fmt.Errorf("max_rows_in_memory must be positive")
	}

	return nil
}
