package transform

import (
	"context"
	"fmt"
	"strings"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// FilterRows 行过滤步骤，根据条件过滤数据行
type FilterRows struct {
	*step.BaseStep
	conditions []FilterCondition // 过滤条件
}

// FilterCondition 过滤条件定义
type FilterCondition struct {
	Field    string      `json:"field"`    // 字段名
	Operator string      `json:"operator"` // 操作符: eq, ne, gt, ge, lt, le, contains, starts_with, ends_with, is_null, is_not_null
	Value    interface{} `json:"value"`    // 比较值
	LogicOp  string      `json:"logic_op"` // 逻辑操作符: and, or (默认and)
}

// NewFilterRows 创建一个新的行过滤步骤
func NewFilterRows(stepMeta meta.StepMeta) *FilterRows {
	return &FilterRows{
		BaseStep:   step.NewBaseStep(stepMeta),
		conditions: []FilterCondition{},
	}
}

// Initialize 初始化步骤
func (f *FilterRows) Initialize() error {
	if err := f.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中解析过滤条件
	if err := f.parseFilterConditions(); err != nil {
		return fmt.Errorf("failed to parse filter conditions: %w", err)
	}

	return nil
}

// Execute 执行行过滤
func (f *FilterRows) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	var outputRows []map[string]interface{}
	var schema []meta.FieldMeta

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 检查行是否满足过滤条件
		if f.evaluateConditions(row) {
			// 将Row转换为map[string]interface{}
			rowData := make(map[string]interface{})
			for _, field := range row.Fields() {
				rowData[field] = row.Get(field)
			}
			outputRows = append(outputRows, rowData)
		}
	}

	// 使用输入的schema
	if input.Schema() != nil {
		schema = input.Schema()
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// Validate 验证步骤配置
func (f *FilterRows) Validate() error {
	if err := f.BaseStep.Validate(); err != nil {
		return err
	}

	// 验证过滤条件配置
	if len(f.conditions) == 0 {
		if _, exists := f.GetConfigSlice("conditions"); !exists {
			return meta.NewValidationError("FilterRows step requires 'conditions' configuration")
		}
	}

	// 验证每个过滤条件
	for i, condition := range f.conditions {
		if condition.Field == "" {
			return meta.NewValidationErrorf("condition %d: field is required", i)
		}
		if condition.Operator == "" {
			return meta.NewValidationErrorf("condition %d: operator is required", i)
		}
		if !isValidOperator(condition.Operator) {
			return meta.NewValidationErrorf("condition %d: invalid operator '%s'", i, condition.Operator)
		}
	}

	return nil
}

// parseFilterConditions 解析过滤条件配置
func (f *FilterRows) parseFilterConditions() error {
	// 从配置中获取条件列表
	conditionsConfig, exists := f.GetConfigSlice("conditions")
	if !exists {
		return fmt.Errorf("conditions configuration not found")
	}

	f.conditions = []FilterCondition{}

	for i, conditionConfig := range conditionsConfig {
		conditionMap, ok := conditionConfig.(map[string]interface{})
		if !ok {
			return fmt.Errorf("condition %d: invalid condition configuration format", i)
		}

		condition := FilterCondition{}

		// 解析字段名
		if field, ok := conditionMap["field"].(string); ok {
			condition.Field = field
		} else {
			return fmt.Errorf("condition %d: field is required and must be a string", i)
		}

		// 解析操作符
		if operator, ok := conditionMap["operator"].(string); ok {
			condition.Operator = operator
		} else {
			return fmt.Errorf("condition %d: operator is required and must be a string", i)
		}

		// 解析比较值
		condition.Value = conditionMap["value"]

		// 解析逻辑操作符（可选，默认为and）
		if logicOp, ok := conditionMap["logic_op"].(string); ok {
			condition.LogicOp = logicOp
		} else {
			condition.LogicOp = "and"
		}

		f.conditions = append(f.conditions, condition)
	}

	return nil
}

// evaluateConditions 评估所有过滤条件
func (f *FilterRows) evaluateConditions(row step.Row) bool {
	if len(f.conditions) == 0 {
		return true // 没有条件，通过所有行
	}

	result := true
	firstCondition := true

	for _, condition := range f.conditions {
		conditionResult := f.evaluateCondition(row, condition)

		if firstCondition {
			result = conditionResult
			firstCondition = false
		} else {
			switch strings.ToLower(condition.LogicOp) {
			case "or":
				result = result || conditionResult
			default: // "and"
				result = result && conditionResult
			}
		}
	}

	return result
}

// evaluateCondition 评估单个过滤条件
func (f *FilterRows) evaluateCondition(row step.Row, condition FilterCondition) bool {
	fieldValue := row.Get(condition.Field)

	switch strings.ToLower(condition.Operator) {
	case "is_null":
		return fieldValue == nil
	case "is_not_null":
		return fieldValue != nil
	case "eq": // 等于
		return compareValues(fieldValue, condition.Value) == 0
	case "ne": // 不等于
		return compareValues(fieldValue, condition.Value) != 0
	case "gt": // 大于
		return compareValues(fieldValue, condition.Value) > 0
	case "ge": // 大于等于
		return compareValues(fieldValue, condition.Value) >= 0
	case "lt": // 小于
		return compareValues(fieldValue, condition.Value) < 0
	case "le": // 小于等于
		return compareValues(fieldValue, condition.Value) <= 0
	case "contains": // 包含
		return containsValue(fieldValue, condition.Value)
	case "starts_with": // 开头匹配
		return startsWithValue(fieldValue, condition.Value)
	case "ends_with": // 结尾匹配
		return endsWithValue(fieldValue, condition.Value)
	default:
		return false
	}
}

// compareValues 比较两个值
func compareValues(v1, v2 interface{}) int {
	// 处理nil值
	if v1 == nil && v2 == nil {
		return 0
	}
	if v1 == nil {
		return -1
	}
	if v2 == nil {
		return 1
	}

	// 尝试数字比较
	if f1, ok1 := toFloat64(v1); ok1 {
		if f2, ok2 := toFloat64(v2); ok2 {
			if f1 < f2 {
				return -1
			} else if f1 > f2 {
				return 1
			}
			return 0
		}
	}

	// 字符串比较
	s1 := fmt.Sprintf("%v", v1)
	s2 := fmt.Sprintf("%v", v2)
	if s1 < s2 {
		return -1
	} else if s1 > s2 {
		return 1
	}
	return 0
}

// containsValue 检查字符串包含
func containsValue(fieldValue, conditionValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	conditionStr := fmt.Sprintf("%v", conditionValue)
	return strings.Contains(fieldStr, conditionStr)
}

// startsWithValue 检查字符串开头
func startsWithValue(fieldValue, conditionValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	conditionStr := fmt.Sprintf("%v", conditionValue)
	return strings.HasPrefix(fieldStr, conditionStr)
}

// endsWithValue 检查字符串结尾
func endsWithValue(fieldValue, conditionValue interface{}) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	conditionStr := fmt.Sprintf("%v", conditionValue)
	return strings.HasSuffix(fieldStr, conditionStr)
}

// isValidOperator 检查操作符是否有效
func isValidOperator(operator string) bool {
	validOperators := []string{
		"eq", "ne", "gt", "ge", "lt", "le",
		"contains", "starts_with", "ends_with",
		"is_null", "is_not_null",
	}

	for _, valid := range validOperators {
		if strings.ToLower(operator) == valid {
			return true
		}
	}
	return false
}
