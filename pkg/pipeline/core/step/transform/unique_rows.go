package transform

import (
	"context"
	"crypto/md5"
	"fmt"
	"strconv"
	"strings"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// UniqueRows 去重转换步骤
type UniqueRows struct {
	*step.BaseStep
	compareFields   []string
	errorHandling   string
	caseInsensitive bool
	nullHandling    string
	storeDuplicates bool
	duplicateField  string
	keepFirst       bool
}

// UniqueRowsMetrics 去重统计信息
type UniqueRowsMetrics struct {
	InputRows     int64
	OutputRows    int64
	DuplicateRows int64
	UniqueKeys    int64
}

// NewUniqueRows 创建新的去重步骤
func NewUniqueRows(stepMeta meta.StepMeta) *UniqueRows {
	ur := &UniqueRows{
		BaseStep:        step.NewBaseStep(stepMeta),
		compareFields:   []string{},
		errorHandling:   "skip",
		caseInsensitive: false,
		nullHandling:    "keep",
		storeDuplicates: false,
		duplicateField:  "_is_duplicate",
		keepFirst:       true,
	}

	// 解析比较字段配置
	if compareFields, ok := stepMeta.Config["compare_fields"].([]interface{}); ok {
		for _, field := range compareFields {
			if fieldName, ok := field.(string); ok {
				ur.compareFields = append(ur.compareFields, fieldName)
			}
		}
	}

	// 解析错误处理策略
	if errorHandling, ok := stepMeta.Config["error_handling"].(string); ok {
		ur.errorHandling = errorHandling
	}

	// 解析大小写敏感性
	if caseInsensitive, ok := stepMeta.Config["case_insensitive"].(bool); ok {
		ur.caseInsensitive = caseInsensitive
	}

	// 解析null值处理策略
	if nullHandling, ok := stepMeta.Config["null_handling"].(string); ok {
		ur.nullHandling = nullHandling
	}

	// 解析是否存储重复标记
	if storeDuplicates, ok := stepMeta.Config["store_duplicates"].(bool); ok {
		ur.storeDuplicates = storeDuplicates
	}

	// 解析重复标记字段名
	if duplicateField, ok := stepMeta.Config["duplicate_field"].(string); ok {
		ur.duplicateField = duplicateField
	}

	// 解析保留策略
	if keepFirst, ok := stepMeta.Config["keep_first"].(bool); ok {
		ur.keepFirst = keepFirst
	} else if keepLast, ok := stepMeta.Config["keep_last"].(bool); ok {
		ur.keepFirst = !keepLast
	}

	return ur
}

// Execute 执行去重转换
func (ur *UniqueRows) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	var schema []meta.FieldMeta
	var outputRows []map[string]interface{}

	// 复制输入schema
	if input.Schema() != nil {
		schema = make([]meta.FieldMeta, len(input.Schema()))
		copy(schema, input.Schema())

		// 如果需要存储重复标记，添加字段到schema
		if ur.storeDuplicates {
			duplicateFieldExists := false
			for _, field := range schema {
				if field.Name == ur.duplicateField {
					duplicateFieldExists = true
					break
				}
			}
			if !duplicateFieldExists {
				schema = append(schema, meta.FieldMeta{
					Name: ur.duplicateField,
					Type: meta.FieldTypeBoolean,
				})
			}
		}
	}

	// 如果没有指定比较字段，使用所有字段
	if len(ur.compareFields) == 0 && len(schema) > 0 {
		for _, field := range schema {
			if field.Name != ur.duplicateField {
				ur.compareFields = append(ur.compareFields, field.Name)
			}
		}
	}

	if len(ur.compareFields) == 0 {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("no compare fields specified for UniqueRows step")
	}

	// 收集所有行数据并构建唯一性映射
	seenKeys := make(map[string][]map[string]interface{})
	metrics := &UniqueRowsMetrics{}

	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		metrics.InputRows++

		// 验证比较字段是否存在
		if metrics.InputRows == 1 {
			if err := ur.validateCompareFields(row); err != nil {
				if ur.errorHandling == "abort" {
					return step.NewEmptyRowSet([]meta.FieldMeta{}), err
				}
				continue // 跳过这一行
			}
		}

		// 生成唯一键
		uniqueKey, err := ur.generateUniqueKey(row)
		if err != nil {
			if ur.errorHandling == "abort" {
				return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to generate unique key: %w", err)
			}
			continue // 跳过这一行
		}

		// 转换行数据为map
		rowData := ur.rowToMap(row)

		// 检查是否已存在相同键
		if existingRows, exists := seenKeys[uniqueKey]; exists {
			// 是重复行
			metrics.DuplicateRows++

			if ur.storeDuplicates {
				rowData[ur.duplicateField] = true
				if !ur.keepFirst {
					// 保留最后一个，替换现有的
					seenKeys[uniqueKey] = []map[string]interface{}{rowData}
				} else {
					// 保留第一个，只标记后续为重复
					seenKeys[uniqueKey] = append(existingRows, rowData)
				}
			} else {
				if !ur.keepFirst {
					// 保留最后一个，替换现有的
					seenKeys[uniqueKey] = []map[string]interface{}{rowData}
				}
				// 如果保留第一个且不存储重复，则忽略当前行
			}
		} else {
			// 新的唯一行
			metrics.UniqueKeys++
			if ur.storeDuplicates {
				rowData[ur.duplicateField] = false
			}
			seenKeys[uniqueKey] = []map[string]interface{}{rowData}
		}
	}

	// 生成输出行
	if ur.storeDuplicates {
		// 如果存储重复标记，输出所有行
		for _, rowList := range seenKeys {
			if ur.keepFirst {
				// 保留第一个和所有重复的
				outputRows = append(outputRows, rowList...)
			} else {
				// 只保留最后一个
				if len(rowList) > 0 {
					outputRows = append(outputRows, rowList[len(rowList)-1])
				}
			}
		}
	} else {
		// 只输出唯一行
		for _, rowList := range seenKeys {
			if len(rowList) > 0 {
				if ur.keepFirst {
					outputRows = append(outputRows, rowList[0])
				} else {
					outputRows = append(outputRows, rowList[len(rowList)-1])
				}
			}
		}
	}

	metrics.OutputRows = int64(len(outputRows))

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// validateCompareFields 验证比较字段是否存在
func (ur *UniqueRows) validateCompareFields(row step.Row) error {
	rowFields := row.Fields()
	fieldSet := make(map[string]bool)
	for _, field := range rowFields {
		fieldSet[field] = true
	}

	for _, compareField := range ur.compareFields {
		if !fieldSet[compareField] {
			return fmt.Errorf("compare field '%s' not found in input data", compareField)
		}
	}

	return nil
}

// generateUniqueKey 生成唯一键
func (ur *UniqueRows) generateUniqueKey(row step.Row) (string, error) {
	var keyParts []string

	for _, fieldName := range ur.compareFields {
		value := row.Get(fieldName)

		// 处理null值
		if value == nil || row.IsNull(fieldName) {
			switch ur.nullHandling {
			case "skip":
				return "", fmt.Errorf("null value in field '%s', skipping row", fieldName)
			case "keep":
				keyParts = append(keyParts, "<NULL>")
			case "empty":
				keyParts = append(keyParts, "")
			default:
				keyParts = append(keyParts, "<NULL>")
			}
			continue
		}

		// 转换为字符串
		strValue := ur.getStringValue(value)

		// 处理大小写
		if ur.caseInsensitive {
			strValue = strings.ToLower(strValue)
		}

		keyParts = append(keyParts, strValue)
	}

	// 使用分隔符连接字段值
	combinedKey := strings.Join(keyParts, "|")

	// 为了避免键过长，使用MD5哈希
	if len(combinedKey) > 200 {
		hash := md5.Sum([]byte(combinedKey))
		return fmt.Sprintf("%x", hash), nil
	}

	return combinedKey, nil
}

// rowToMap 将Row转换为map
func (ur *UniqueRows) rowToMap(row step.Row) map[string]interface{} {
	result := make(map[string]interface{})
	for _, field := range row.Fields() {
		result[field] = row.Get(field)
	}
	return result
}

// getStringValue 将任意类型转换为字符串
func (ur *UniqueRows) getStringValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// GetType 获取步骤类型
func (ur *UniqueRows) GetType() meta.StepType {
	return meta.StepTypeUniqueRows
}

// Validate 验证步骤配置
func (ur *UniqueRows) Validate() error {
	// 验证错误处理策略
	if ur.errorHandling != "skip" && ur.errorHandling != "abort" {
		return fmt.Errorf("invalid error_handling '%s', must be 'skip' or 'abort'", ur.errorHandling)
	}

	// 验证null值处理策略
	if ur.nullHandling != "keep" && ur.nullHandling != "skip" && ur.nullHandling != "empty" {
		return fmt.Errorf("invalid null_handling '%s', must be 'keep', 'skip', or 'empty'", ur.nullHandling)
	}

	// 验证重复字段名
	if ur.storeDuplicates && ur.duplicateField == "" {
		return fmt.Errorf("duplicate_field is required when store_duplicates is true")
	}

	// 检查重复字段名是否与比较字段冲突
	if ur.storeDuplicates {
		for _, compareField := range ur.compareFields {
			if compareField == ur.duplicateField {
				return fmt.Errorf("duplicate_field '%s' cannot be the same as compare field", ur.duplicateField)
			}
		}
	}

	return nil
}

// GetMetrics 获取去重统计信息（用于调试和监控）
func (ur *UniqueRows) GetMetrics() *UniqueRowsMetrics {
	// 这里应该从实际执行中获取统计信息
	// 简化实现，返回空的统计信息
	return &UniqueRowsMetrics{}
}
