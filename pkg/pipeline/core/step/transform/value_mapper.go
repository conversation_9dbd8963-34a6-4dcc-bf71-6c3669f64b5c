package transform

import (
	"context"
	"fmt"
	"strconv"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// ValueMapper 值映射转换步骤
type ValueMapper struct {
	*step.BaseStep
	fieldName    string
	mappingRules map[string]string
	defaultValue string
	nullValue    string
}

// ValueMappingRule 映射规则
type ValueMappingRule struct {
	SourceValue string `json:"source_value" yaml:"source_value"`
	TargetValue string `json:"target_value" yaml:"target_value"`
}

// NewValueMapper 创建新的值映射步骤
func NewValueMapper(stepMeta meta.StepMeta) *ValueMapper {
	vm := &ValueMapper{
		BaseStep:     step.NewBaseStep(stepMeta),
		mappingRules: make(map[string]string),
		defaultValue: "",
		nullValue:    "",
	}

	// 解析配置
	if fieldName, ok := stepMeta.Config["field_name"].(string); ok {
		vm.fieldName = fieldName
	}

	if defaultVal, ok := stepMeta.Config["default_value"].(string); ok {
		vm.defaultValue = defaultVal
	}

	if nullVal, ok := stepMeta.Config["null_value"].(string); ok {
		vm.nullValue = nullVal
	}

	// 解析映射规则
	if rules, ok := stepMeta.Config["mapping_rules"].([]interface{}); ok {
		for _, rule := range rules {
			if ruleMap, ok := rule.(map[string]interface{}); ok {
				if sourceVal, ok := ruleMap["source_value"].(string); ok {
					if targetVal, ok := ruleMap["target_value"].(string); ok {
						vm.mappingRules[sourceVal] = targetVal
					}
				}
			}
		}
	}

	return vm
}

// Execute 执行值映射转换
func (vm *ValueMapper) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	if vm.fieldName == "" {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("field_name is required for ValueMapper step")
	}

	var outputRows []map[string]interface{}
	var schema []meta.FieldMeta

	// 复制输入schema
	if input.Schema() != nil {
		schema = make([]meta.FieldMeta, len(input.Schema()))
		copy(schema, input.Schema())
	}

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 执行映射转换
		outputRowData, err := vm.mapRowValues(row)
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("mapping error: %w", err)
		}

		if outputRowData != nil {
			outputRows = append(outputRows, outputRowData)
		}
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// mapRowValues 处理单行数据的值映射
func (vm *ValueMapper) mapRowValues(row step.Row) (map[string]interface{}, error) {
	// 检查字段是否存在
	if row.Get(vm.fieldName) == nil && !row.IsNull(vm.fieldName) {
		// 字段不存在且非null值，跳过映射
		outputRow := make(map[string]interface{})
		for _, field := range row.Fields() {
			outputRow[field] = row.Get(field)
		}
		return outputRow, nil
	}

	// 复制所有字段
	outputRow := make(map[string]interface{})
	for _, field := range row.Fields() {
		outputRow[field] = row.Get(field)
	}

	// 处理目标字段的值映射
	originalValue := vm.getStringValue(row.Get(vm.fieldName))
	mappedValue := vm.mapValue(originalValue)
	outputRow[vm.fieldName] = mappedValue

	return outputRow, nil
}

// mapValue 执行值映射
func (vm *ValueMapper) mapValue(value string) string {
	// 处理 null 值
	if value == "" && vm.nullValue != "" {
		return vm.nullValue
	}

	// 查找映射规则
	if mappedValue, exists := vm.mappingRules[value]; exists {
		return mappedValue
	}

	// 返回默认值或原值
	if vm.defaultValue != "" {
		return vm.defaultValue
	}

	return value
}

// getStringValue 将任意类型转换为字符串
func (vm *ValueMapper) getStringValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// GetType 获取步骤类型
func (vm *ValueMapper) GetType() meta.StepType {
	return meta.StepTypeValueMapper
}

// Validate 验证步骤配置
func (vm *ValueMapper) Validate() error {
	if vm.fieldName == "" {
		return fmt.Errorf("field_name is required for ValueMapper step")
	}

	if len(vm.mappingRules) == 0 {
		return fmt.Errorf("at least one mapping rule is required for ValueMapper step")
	}

	return nil
}
