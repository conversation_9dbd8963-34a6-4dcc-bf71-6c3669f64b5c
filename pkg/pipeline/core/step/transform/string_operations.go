package transform

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// StringOperations 字符串操作转换步骤
type StringOperations struct {
	*step.BaseStep
	fieldName      string
	operation      string
	parameters     map[string]interface{}
	targetField    string
	removeOriginal bool
}

// StringOperation 字符串操作类型
type StringOperation string

const (
	StringOpUpperCase    StringOperation = "upper_case"
	StringOpLowerCase    StringOperation = "lower_case"
	StringOpTrim         StringOperation = "trim"
	StringOpSubstring    StringOperation = "substring"
	StringOpReplace      StringOperation = "replace"
	StringOpRegexReplace StringOperation = "regex_replace"
	StringOpSplit        StringOperation = "split"
	StringOpConcat       StringOperation = "concat"
	StringOpPadLeft      StringOperation = "pad_left"
	StringOpPadRight     StringOperation = "pad_right"
	StringOpLength       StringOperation = "length"
	StringOpReverse      StringOperation = "reverse"
)

// NewStringOperations 创建新的字符串操作步骤
func NewStringOperations(stepMeta meta.StepMeta) *StringOperations {
	so := &StringOperations{
		BaseStep:       step.NewBaseStep(stepMeta),
		parameters:     make(map[string]interface{}),
		removeOriginal: false,
	}

	// 解析配置
	if fieldName, ok := stepMeta.Config["field_name"].(string); ok {
		so.fieldName = fieldName
	}

	if operation, ok := stepMeta.Config["operation"].(string); ok {
		so.operation = operation
	}

	if targetField, ok := stepMeta.Config["target_field"].(string); ok {
		so.targetField = targetField
	} else {
		so.targetField = so.fieldName // 默认使用原字段名
	}

	if removeOriginal, ok := stepMeta.Config["remove_original"].(bool); ok {
		so.removeOriginal = removeOriginal
	}

	// 解析操作参数
	if params, ok := stepMeta.Config["parameters"].(map[string]interface{}); ok {
		so.parameters = params
	}

	return so
}

// Execute 执行字符串操作转换
func (so *StringOperations) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	if so.fieldName == "" {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("field_name is required for StringOperations step")
	}

	if so.operation == "" {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("operation is required for StringOperations step")
	}

	var outputRows []map[string]interface{}
	var schema []meta.FieldMeta

	// 构建输出schema
	if input.Schema() != nil {
		schema = make([]meta.FieldMeta, len(input.Schema()))
		copy(schema, input.Schema())

		// 如果目标字段与原字段不同，添加新字段到schema
		if so.targetField != so.fieldName {
			targetFieldExists := false
			for _, field := range schema {
				if field.Name == so.targetField {
					targetFieldExists = true
					break
				}
			}
			if !targetFieldExists {
				schema = append(schema, meta.FieldMeta{
					Name: so.targetField,
					Type: so.getOutputFieldType(),
				})
			}
		}
	}

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 执行字符串操作
		outputRowData, err := so.processRow(row)
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("string operation error: %w", err)
		}

		if outputRowData != nil {
			outputRows = append(outputRows, outputRowData)
		}
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// processRow 处理单行数据
func (so *StringOperations) processRow(row step.Row) (map[string]interface{}, error) {
	// 复制所有字段
	outputRow := make(map[string]interface{})
	for _, field := range row.Fields() {
		outputRow[field] = row.Get(field)
	}

	// 获取原始值
	originalValue := so.getStringValue(row.Get(so.fieldName))

	// 执行字符串操作
	result, err := so.performOperation(originalValue)
	if err != nil {
		return nil, fmt.Errorf("operation %s failed: %w", so.operation, err)
	}

	// 设置结果到目标字段
	outputRow[so.targetField] = result

	// 如果需要移除原字段且目标字段不同
	if so.removeOriginal && so.targetField != so.fieldName {
		delete(outputRow, so.fieldName)
	}

	return outputRow, nil
}

// performOperation 执行具体的字符串操作
func (so *StringOperations) performOperation(value string) (interface{}, error) {
	switch StringOperation(so.operation) {
	case StringOpUpperCase:
		return strings.ToUpper(value), nil

	case StringOpLowerCase:
		return strings.ToLower(value), nil

	case StringOpTrim:
		return strings.TrimSpace(value), nil

	case StringOpSubstring:
		return so.performSubstring(value)

	case StringOpReplace:
		return so.performReplace(value)

	case StringOpRegexReplace:
		return so.performRegexReplace(value)

	case StringOpSplit:
		return so.performSplit(value)

	case StringOpConcat:
		return so.performConcat(value)

	case StringOpPadLeft:
		return so.performPadLeft(value)

	case StringOpPadRight:
		return so.performPadRight(value)

	case StringOpLength:
		return len(value), nil

	case StringOpReverse:
		return so.reverseString(value), nil

	default:
		return nil, fmt.Errorf("unsupported string operation: %s", so.operation)
	}
}

// performSubstring 执行子字符串操作
func (so *StringOperations) performSubstring(value string) (string, error) {
	start, ok := so.parameters["start"].(float64)
	if !ok {
		return "", fmt.Errorf("start parameter is required for substring operation")
	}

	startIdx := int(start)
	if startIdx < 0 {
		startIdx = 0
	}
	if startIdx >= len(value) {
		return "", nil
	}

	if length, ok := so.parameters["length"].(float64); ok {
		lengthInt := int(length)
		endIdx := startIdx + lengthInt
		if endIdx > len(value) {
			endIdx = len(value)
		}
		return value[startIdx:endIdx], nil
	}

	return value[startIdx:], nil
}

// performReplace 执行字符串替换
func (so *StringOperations) performReplace(value string) (string, error) {
	oldStr, ok := so.parameters["old"].(string)
	if !ok {
		return "", fmt.Errorf("old parameter is required for replace operation")
	}

	newStr, ok := so.parameters["new"].(string)
	if !ok {
		newStr = ""
	}

	count := -1 // 默认替换所有
	if c, ok := so.parameters["count"].(float64); ok {
		count = int(c)
	}

	if count == -1 {
		return strings.ReplaceAll(value, oldStr, newStr), nil
	}

	return strings.Replace(value, oldStr, newStr, count), nil
}

// performRegexReplace 执行正则表达式替换
func (so *StringOperations) performRegexReplace(value string) (string, error) {
	pattern, ok := so.parameters["pattern"].(string)
	if !ok {
		return "", fmt.Errorf("pattern parameter is required for regex_replace operation")
	}

	replacement, ok := so.parameters["replacement"].(string)
	if !ok {
		replacement = ""
	}

	regex, err := regexp.Compile(pattern)
	if err != nil {
		return "", fmt.Errorf("invalid regex pattern: %w", err)
	}

	return regex.ReplaceAllString(value, replacement), nil
}

// performSplit 执行字符串分割
func (so *StringOperations) performSplit(value string) ([]string, error) {
	delimiter, ok := so.parameters["delimiter"].(string)
	if !ok {
		return nil, fmt.Errorf("delimiter parameter is required for split operation")
	}

	maxSplit := -1
	if max, ok := so.parameters["max_split"].(float64); ok {
		maxSplit = int(max)
	}

	if maxSplit == -1 {
		return strings.Split(value, delimiter), nil
	}

	return strings.SplitN(value, delimiter, maxSplit+1), nil
}

// performConcat 执行字符串连接
func (so *StringOperations) performConcat(value string) (string, error) {
	suffix, ok := so.parameters["suffix"].(string)
	if !ok {
		return value, nil
	}

	prefix, ok := so.parameters["prefix"].(string)
	if !ok {
		prefix = ""
	}

	return prefix + value + suffix, nil
}

// performPadLeft 执行左填充
func (so *StringOperations) performPadLeft(value string) (string, error) {
	length, ok := so.parameters["length"].(float64)
	if !ok {
		return "", fmt.Errorf("length parameter is required for pad_left operation")
	}

	padChar, ok := so.parameters["pad_char"].(string)
	if !ok {
		padChar = " "
	}

	targetLength := int(length)
	if len(value) >= targetLength {
		return value, nil
	}

	padLength := targetLength - len(value)
	padding := strings.Repeat(padChar, padLength/len(padChar)+1)[:padLength]
	return padding + value, nil
}

// performPadRight 执行右填充
func (so *StringOperations) performPadRight(value string) (string, error) {
	length, ok := so.parameters["length"].(float64)
	if !ok {
		return "", fmt.Errorf("length parameter is required for pad_right operation")
	}

	padChar, ok := so.parameters["pad_char"].(string)
	if !ok {
		padChar = " "
	}

	targetLength := int(length)
	if len(value) >= targetLength {
		return value, nil
	}

	padLength := targetLength - len(value)
	padding := strings.Repeat(padChar, padLength/len(padChar)+1)[:padLength]
	return value + padding, nil
}

// reverseString 反转字符串
func (so *StringOperations) reverseString(value string) string {
	runes := []rune(value)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

// getStringValue 将任意类型转换为字符串
func (so *StringOperations) getStringValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// getOutputFieldType 获取输出字段类型
func (so *StringOperations) getOutputFieldType() meta.FieldType {
	switch StringOperation(so.operation) {
	case StringOpLength:
		return meta.FieldTypeInteger
	case StringOpSplit:
		return meta.FieldTypeString // 简化处理，返回字符串类型
	default:
		return meta.FieldTypeString
	}
}

// GetType 获取步骤类型
func (so *StringOperations) GetType() meta.StepType {
	return meta.StepTypeStringOperations
}

// Validate 验证步骤配置
func (so *StringOperations) Validate() error {
	if so.fieldName == "" {
		return fmt.Errorf("field_name is required for StringOperations step")
	}

	if so.operation == "" {
		return fmt.Errorf("operation is required for StringOperations step")
	}

	// 验证操作特定的参数
	switch StringOperation(so.operation) {
	case StringOpSubstring:
		if _, ok := so.parameters["start"]; !ok {
			return fmt.Errorf("start parameter is required for substring operation")
		}
	case StringOpReplace:
		if _, ok := so.parameters["old"]; !ok {
			return fmt.Errorf("old parameter is required for replace operation")
		}
	case StringOpRegexReplace:
		if _, ok := so.parameters["pattern"]; !ok {
			return fmt.Errorf("pattern parameter is required for regex_replace operation")
		}
	case StringOpSplit:
		if _, ok := so.parameters["delimiter"]; !ok {
			return fmt.Errorf("delimiter parameter is required for split operation")
		}
	case StringOpPadLeft, StringOpPadRight:
		if _, ok := so.parameters["length"]; !ok {
			return fmt.Errorf("length parameter is required for padding operations")
		}
	}

	return nil
}
