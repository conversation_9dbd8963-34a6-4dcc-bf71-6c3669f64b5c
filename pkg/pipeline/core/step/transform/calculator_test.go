package transform

import (
	"context"
	"testing"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

func TestNewCalculator(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_calculator",
		Type: meta.StepTypeCalculator,
		Config: map[string]interface{}{
			"calculations": []interface{}{
				map[string]interface{}{
					"target_field": "total",
					"expression":   "{price} * {quantity}",
					"data_type":    "float",
				},
			},
		},
	}

	calcStep := NewCalculator(stepMeta)
	if calcStep == nil {
		t.Fatal("NewCalculator should not return nil")
	}

	if calcStep.GetMeta().Name != "test_calculator" {
		t.<PERSON>("Expected step name 'test_calculator', got '%s'", calcStep.GetMeta().Name)
	}
}

func TestCalculator_Initialize(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
	}{
		{
			name: "valid single calculation",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"expression":   "{price} * {quantity}",
						"data_type":    "float",
					},
				},
			},
			expectError: false,
		},
		{
			name: "valid multiple calculations",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"expression":   "{price} * {quantity}",
						"data_type":    "float",
					},
					map[string]interface{}{
						"target_field": "discount_total",
						"expression":   "{total} * 0.9",
						"data_type":    "float",
					},
				},
			},
			expectError: false,
		},
		{
			name: "missing calculations",
			config: map[string]interface{}{
				"other_config": "value",
			},
			expectError: true,
		},
		{
			name: "invalid calculation format",
			config: map[string]interface{}{
				"calculations": []interface{}{
					"invalid_calculation",
				},
			},
			expectError: true,
		},
		{
			name: "missing target_field",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"expression": "{price} * {quantity}",
						"data_type":  "float",
					},
				},
			},
			expectError: true,
		},
		{
			name: "missing expression",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"data_type":    "float",
					},
				},
			},
			expectError: true,
		},
		{
			name: "default data type",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "full_name",
						"expression":   "{first_name} + ' ' + {last_name}",
					},
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_calculator",
				Type:   meta.StepTypeCalculator,
				Config: tt.config,
			}

			calcStep := NewCalculator(stepMeta)
			err := calcStep.Initialize()

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestCalculator_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid configuration",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"expression":   "{price} * {quantity}",
						"data_type":    "float",
					},
				},
			},
			expectError: false,
		},
		{
			name: "invalid data type",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"expression":   "{price} * {quantity}",
						"data_type":    "invalid_type",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid data_type",
		},
		{
			name: "empty target field",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "",
						"expression":   "{price} * {quantity}",
						"data_type":    "float",
					},
				},
			},
			expectError: true,
			errorMsg:    "target_field is required",
		},
		{
			name: "empty expression",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"expression":   "",
						"data_type":    "float",
					},
				},
			},
			expectError: true,
			errorMsg:    "expression is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_calculator",
				Type:   meta.StepTypeCalculator,
				Config: tt.config,
			}

			calcStep := NewCalculator(stepMeta)
			_ = calcStep.Initialize()
			err := calcStep.Validate()

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if tt.expectError && err != nil && tt.errorMsg != "" {
				if !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
				}
			}
		})
	}
}

func TestCalculator_Execute(t *testing.T) {
	tests := []struct {
		name            string
		config          map[string]interface{}
		inputRows       []map[string]interface{}
		expectedResults []map[string]interface{}
		description     string
	}{
		{
			name: "simple arithmetic calculation",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "total",
						"expression":   "{price} * {quantity}",
						"data_type":    "float",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"price": 10.5, "quantity": 2},
				{"price": 5.0, "quantity": 3},
			},
			expectedResults: []map[string]interface{}{
				{"price": 10.5, "quantity": 2, "total": 21.0},
				{"price": 5.0, "quantity": 3, "total": 15.0},
			},
			description: "Should calculate total as price * quantity",
		},
		{
			name: "string concatenation",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "full_name",
						"expression":   "{first_name} {last_name}",
						"data_type":    "string",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"first_name": "John", "last_name": "Doe"},
				{"first_name": "Jane", "last_name": "Smith"},
			},
			expectedResults: []map[string]interface{}{
				{"first_name": "John", "last_name": "Doe", "full_name": "John Doe"},
				{"first_name": "Jane", "last_name": "Smith", "full_name": "Jane Smith"},
			},
			description: "Should concatenate first and last name",
		},
		{
			name: "addition calculation",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "sum",
						"expression":   "{a} + {b}",
						"data_type":    "int",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"a": 10, "b": 20},
				{"a": 5, "b": 15},
			},
			expectedResults: []map[string]interface{}{
				{"a": 10, "b": 20, "sum": int64(30)},
				{"a": 5, "b": 15, "sum": int64(20)},
			},
			description: "Should add two numbers",
		},
		{
			name: "subtraction calculation",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "difference",
						"expression":   "{a} - {b}",
						"data_type":    "float",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"a": 100, "b": 30},
				{"a": 50, "b": 20},
			},
			expectedResults: []map[string]interface{}{
				{"a": 100, "b": 30, "difference": 70.0},
				{"a": 50, "b": 20, "difference": 30.0},
			},
			description: "Should subtract two numbers",
		},
		{
			name: "division calculation",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "ratio",
						"expression":   "{a} / {b}",
						"data_type":    "float",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"a": 100, "b": 4},
				{"a": 75, "b": 3},
			},
			expectedResults: []map[string]interface{}{
				{"a": 100, "b": 4, "ratio": 25.0},
				{"a": 75, "b": 3, "ratio": 25.0},
			},
			description: "Should divide two numbers",
		},
		{
			name: "multiple calculations",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "subtotal",
						"expression":   "{price} * {quantity}",
						"data_type":    "float",
					},
					map[string]interface{}{
						"target_field": "total",
						"expression":   "{subtotal} * 1.1",
						"data_type":    "float",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"price": 10.0, "quantity": 2},
			},
			expectedResults: []map[string]interface{}{
				{"price": 10.0, "quantity": 2, "subtotal": 20.0, "total": 22.0},
			},
			description: "Should perform multiple calculations in sequence",
		},
		{
			name: "boolean conversion",
			config: map[string]interface{}{
				"calculations": []interface{}{
					map[string]interface{}{
						"target_field": "is_high_value",
						"expression":   "{price}",
						"data_type":    "bool",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"price": 100},
				{"price": 0},
			},
			expectedResults: []map[string]interface{}{
				{"price": 100, "is_high_value": true},
				{"price": 0, "is_high_value": false},
			},
			description: "Should convert numeric values to boolean",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_calculator",
				Type:   meta.StepTypeCalculator,
				Config: tt.config,
			}

			calcStep := NewCalculator(stepMeta)
			err := calcStep.Initialize()
			if err != nil {
				t.Fatalf("Failed to initialize: %v", err)
			}

			// 创建输入数据的 schema
			inputSchema := []meta.FieldMeta{
				{Name: "price", Type: meta.FieldTypeFloat},
				{Name: "quantity", Type: meta.FieldTypeInteger},
				{Name: "a", Type: meta.FieldTypeInteger},
				{Name: "b", Type: meta.FieldTypeInteger},
				{Name: "first_name", Type: meta.FieldTypeString},
				{Name: "last_name", Type: meta.FieldTypeString},
			}

			input := step.NewMemoryRowSet(tt.inputRows, inputSchema)
			ctx := context.Background()

			output, err := calcStep.Execute(ctx, input)
			if err != nil {
				t.Fatalf("Execute failed: %v", err)
			}

			// Collect output rows using new interface
			var outputRows []map[string]interface{}
			for {
				row, hasMore, err := output.Next()
				if err != nil {
					t.Fatalf("Error reading output: %v", err)
				}
				if row == nil {
					break
				}
				// Convert Row to map[string]interface{}
				rowData := make(map[string]interface{})
				for _, field := range row.Fields() {
					rowData[field] = row.Get(field)
				}
				outputRows = append(outputRows, rowData)
				if !hasMore {
					break
				}
			}
			output.Close()

			if len(outputRows) != len(tt.expectedResults) {
				t.Fatalf("Expected %d rows, got %d. %s", len(tt.expectedResults), len(outputRows), tt.description)
			}

			// Verify each row
			for i, expectedRow := range tt.expectedResults {
				outputRow := outputRows[i]
				for key, expectedValue := range expectedRow {
					actualValue := outputRow[key]
					if !valuesEqual(actualValue, expectedValue) {
						t.Errorf("Row %d, field '%s': expected %v (%T), got %v (%T). %s",
							i, key, expectedValue, expectedValue, actualValue, actualValue, tt.description)
					}
				}
			}
		})
	}
}

func TestCalculator_ExecuteWithNilInput(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_calculator",
		Type: meta.StepTypeCalculator,
		Config: map[string]interface{}{
			"calculations": []interface{}{
				map[string]interface{}{
					"target_field": "total",
					"expression":   "{price} * {quantity}",
					"data_type":    "float",
				},
			},
		},
	}

	calcStep := NewCalculator(stepMeta)
	err := calcStep.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	ctx := context.Background()
	output, err := calcStep.Execute(ctx, nil)
	if err != nil {
		t.Fatalf("Execute with nil input failed: %v", err)
	}

	// Should return empty rowset
	row, _, _ := output.Next()
	if row != nil {
		t.Error("Expected empty rowset for nil input")
	}
	output.Close()
}

func TestCalculator_ExecuteWithContextCancellation(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_calculator",
		Type: meta.StepTypeCalculator,
		Config: map[string]interface{}{
			"calculations": []interface{}{
				map[string]interface{}{
					"target_field": "total",
					"expression":   "{price} * {quantity}",
					"data_type":    "float",
				},
			},
		},
	}

	calcStep := NewCalculator(stepMeta)
	err := calcStep.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	// Create a cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	inputRows := []map[string]interface{}{
		{"price": 10.0, "quantity": 2},
	}
	inputSchema := []meta.FieldMeta{
		{Name: "price", Type: meta.FieldTypeFloat},
		{Name: "quantity", Type: meta.FieldTypeInteger},
	}
	input := step.NewMemoryRowSet(inputRows, inputSchema)

	_, err = calcStep.Execute(ctx, input)
	if err != context.Canceled {
		t.Errorf("Expected context.Canceled error, got: %v", err)
	}
}

func TestCalculator_DataTypeConversion(t *testing.T) {
	tests := []struct {
		name          string
		calculation   map[string]interface{}
		inputValue    interface{}
		expectedValue interface{}
		expectedType  string
	}{
		{
			name: "convert to int",
			calculation: map[string]interface{}{
				"target_field": "int_value",
				"expression":   "{value}",
				"data_type":    "int",
			},
			inputValue:    "123",
			expectedValue: int64(123),
			expectedType:  "int64",
		},
		{
			name: "convert to float",
			calculation: map[string]interface{}{
				"target_field": "float_value",
				"expression":   "{value}",
				"data_type":    "float",
			},
			inputValue:    "123.45",
			expectedValue: 123.45,
			expectedType:  "float64",
		},
		{
			name: "convert to bool from string",
			calculation: map[string]interface{}{
				"target_field": "bool_value",
				"expression":   "{value}",
				"data_type":    "bool",
			},
			inputValue:    "true",
			expectedValue: true,
			expectedType:  "bool",
		},
		{
			name: "convert to string",
			calculation: map[string]interface{}{
				"target_field": "string_value",
				"expression":   "{value}",
				"data_type":    "string",
			},
			inputValue:    123,
			expectedValue: "123",
			expectedType:  "string",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name: "test_calculator",
				Type: meta.StepTypeCalculator,
				Config: map[string]interface{}{
					"calculations": []interface{}{tt.calculation},
				},
			}

			calcStep := NewCalculator(stepMeta)
			err := calcStep.Initialize()
			if err != nil {
				t.Fatalf("Failed to initialize: %v", err)
			}

			inputRows := []map[string]interface{}{
				{"value": tt.inputValue},
			}
			inputSchema := []meta.FieldMeta{
				{Name: "value", Type: meta.FieldTypeString},
			}
			input := step.NewMemoryRowSet(inputRows, inputSchema)
			ctx := context.Background()

			output, err := calcStep.Execute(ctx, input)
			if err != nil {
				t.Fatalf("Execute failed: %v", err)
			}

			row, _, err := output.Next()
			if err != nil {
				t.Fatalf("Error reading output: %v", err)
			}
			if row == nil {
				t.Fatal("Expected a row but got nil")
			}
			actualValue := row.Get(tt.calculation["target_field"].(string))
			output.Close()

			if !valuesEqual(actualValue, tt.expectedValue) {
				t.Errorf("Expected %v (%T), got %v (%T)", tt.expectedValue, tt.expectedValue, actualValue, actualValue)
			}
		})
	}
}

// Helper function to compare values with type consideration
func valuesEqual(v1, v2 interface{}) bool {
	if v1 == nil && v2 == nil {
		return true
	}
	if v1 == nil || v2 == nil {
		return false
	}

	// Handle numeric comparisons
	if f1, ok1 := toFloat64(v1); ok1 {
		if f2, ok2 := toFloat64(v2); ok2 {
			return f1 == f2
		}
	}

	// Direct comparison
	return v1 == v2
}
