package transform

import (
	"context"
	"testing"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

func TestNewFilterRows(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_filter",
		Type: meta.StepTypeFilterRows,
		Config: map[string]interface{}{
			"conditions": []interface{}{
				map[string]interface{}{
					"field":    "age",
					"operator": "gt",
					"value":    30,
				},
			},
		},
	}

	filterStep := NewFilterRows(stepMeta)
	if filterStep == nil {
		t.Fatal("NewFilterRows should not return nil")
	}

	if filterStep.GetMeta().Name != "test_filter" {
		t.<PERSON>("Expected step name 'test_filter', got '%s'", filterStep.GetMeta().Name)
	}
}

func TestFilterRows_Initialize(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
	}{
		{
			name: "valid single condition",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "gt",
						"value":    30,
					},
				},
			},
			expectError: false,
		},
		{
			name: "valid multiple conditions",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "gt",
						"value":    30,
					},
					map[string]interface{}{
						"field":    "department",
						"operator": "eq",
						"value":    "Engineering",
						"logic_op": "and",
					},
				},
			},
			expectError: false,
		},
		{
			name: "missing conditions",
			config: map[string]interface{}{
				"other_config": "value",
			},
			expectError: true,
		},
		{
			name: "invalid condition format",
			config: map[string]interface{}{
				"conditions": []interface{}{
					"invalid_condition",
				},
			},
			expectError: true,
		},
		{
			name: "missing field in condition",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"operator": "gt",
						"value":    30,
					},
				},
			},
			expectError: true,
		},
		{
			name: "missing operator in condition",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field": "age",
						"value": 30,
					},
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_filter",
				Type:   meta.StepTypeFilterRows,
				Config: tt.config,
			}

			filterStep := NewFilterRows(stepMeta)
			err := filterStep.Initialize()

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestFilterRows_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid configuration",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "gt",
						"value":    30,
					},
				},
			},
			expectError: false,
		},
		{
			name: "invalid operator",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "invalid_op",
						"value":    30,
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid operator",
		},
		{
			name: "empty field",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "",
						"operator": "gt",
						"value":    30,
					},
				},
			},
			expectError: true,
			errorMsg:    "field is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_filter",
				Type:   meta.StepTypeFilterRows,
				Config: tt.config,
			}

			filterStep := NewFilterRows(stepMeta)
			_ = filterStep.Initialize()
			err := filterStep.Validate()

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if tt.expectError && err != nil && tt.errorMsg != "" {
				if !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
				}
			}
		})
	}
}

func TestFilterRows_Execute(t *testing.T) {
	tests := []struct {
		name         string
		config       map[string]interface{}
		inputRows    []map[string]interface{}
		expectedRows int
		description  string
	}{
		{
			name: "filter by age greater than 30",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "gt",
						"value":    30,
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "age": 25},
				{"name": "Jane", "age": 35},
				{"name": "Bob", "age": 40},
			},
			expectedRows: 2,
			description:  "Should filter rows with age > 30",
		},
		{
			name: "filter by string equality",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "department",
						"operator": "eq",
						"value":    "Engineering",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "department": "Engineering"},
				{"name": "Jane", "department": "Marketing"},
				{"name": "Bob", "department": "Engineering"},
			},
			expectedRows: 2,
			description:  "Should filter rows with department = Engineering",
		},
		{
			name: "filter with contains operator",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "email",
						"operator": "contains",
						"value":    "@company.com",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "email": "<EMAIL>"},
				{"name": "Jane", "email": "<EMAIL>"},
				{"name": "Bob", "email": "<EMAIL>"},
			},
			expectedRows: 2,
			description:  "Should filter rows with email containing @company.com",
		},
		{
			name: "filter with is_null operator",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "manager",
						"operator": "is_null",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "manager": nil},
				{"name": "Jane", "manager": "Alice"},
				{"name": "Bob", "manager": nil},
			},
			expectedRows: 2,
			description:  "Should filter rows with null manager",
		},
		{
			name: "multiple conditions with AND logic",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "gt",
						"value":    30,
					},
					map[string]interface{}{
						"field":    "department",
						"operator": "eq",
						"value":    "Engineering",
						"logic_op": "and",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "age": 25, "department": "Engineering"},
				{"name": "Jane", "age": 35, "department": "Engineering"},
				{"name": "Bob", "age": 40, "department": "Marketing"},
			},
			expectedRows: 1,
			description:  "Should filter rows with age > 30 AND department = Engineering",
		},
		{
			name: "multiple conditions with OR logic",
			config: map[string]interface{}{
				"conditions": []interface{}{
					map[string]interface{}{
						"field":    "age",
						"operator": "lt",
						"value":    25,
					},
					map[string]interface{}{
						"field":    "department",
						"operator": "eq",
						"value":    "Engineering",
						"logic_op": "or",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "age": 20, "department": "Marketing"},
				{"name": "Jane", "age": 35, "department": "Engineering"},
				{"name": "Bob", "age": 40, "department": "Marketing"},
			},
			expectedRows: 2,
			description:  "Should filter rows with age < 25 OR department = Engineering",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_filter",
				Type:   meta.StepTypeFilterRows,
				Config: tt.config,
			}

			filterStep := NewFilterRows(stepMeta)
			err := filterStep.Initialize()
			if err != nil {
				t.Fatalf("Failed to initialize: %v", err)
			}

			// 创建输入数据的 schema
			inputSchema := []meta.FieldMeta{
				{Name: "name", Type: meta.FieldTypeString},
				{Name: "age", Type: meta.FieldTypeInteger},
				{Name: "department", Type: meta.FieldTypeString},
				{Name: "email", Type: meta.FieldTypeString},
				{Name: "manager", Type: meta.FieldTypeString},
			}

			input := step.NewMemoryRowSet(tt.inputRows, inputSchema)
			ctx := context.Background()

			output, err := filterStep.Execute(ctx, input)
			if err != nil {
				t.Fatalf("Execute failed: %v", err)
			}

			// Count output rows
			rowCount := 0
			for {
				row, _, err := output.Next()
				if err != nil {
					t.Fatalf("Error reading output: %v", err)
				}
				if row == nil {
					break
				}
				rowCount++
			}
			output.Close()

			if rowCount != tt.expectedRows {
				t.Errorf("Expected %d rows, got %d. %s", tt.expectedRows, rowCount, tt.description)
			}
		})
	}
}

func TestFilterRows_ExecuteWithNilInput(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_filter",
		Type: meta.StepTypeFilterRows,
		Config: map[string]interface{}{
			"conditions": []interface{}{
				map[string]interface{}{
					"field":    "age",
					"operator": "gt",
					"value":    30,
				},
			},
		},
	}

	filterStep := NewFilterRows(stepMeta)
	err := filterStep.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	ctx := context.Background()
	output, err := filterStep.Execute(ctx, nil)
	if err != nil {
		t.Fatalf("Execute with nil input failed: %v", err)
	}

	// Should return empty rowset
	row, _, _ := output.Next()
	if row != nil {
		t.Error("Expected empty rowset for nil input")
	}
	output.Close()
}

func TestFilterRows_ExecuteWithContextCancellation(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_filter",
		Type: meta.StepTypeFilterRows,
		Config: map[string]interface{}{
			"conditions": []interface{}{
				map[string]interface{}{
					"field":    "age",
					"operator": "gt",
					"value":    30,
				},
			},
		},
	}

	filterStep := NewFilterRows(stepMeta)
	err := filterStep.Initialize()
	if err != nil {
		t.Fatalf("Failed to initialize: %v", err)
	}

	// Create a cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel()

	inputRows := []map[string]interface{}{
		{"name": "John", "age": 35},
	}
	inputSchema := []meta.FieldMeta{
		{Name: "name", Type: meta.FieldTypeString},
		{Name: "age", Type: meta.FieldTypeInteger},
	}
	input := step.NewMemoryRowSet(inputRows, inputSchema)

	_, err = filterStep.Execute(ctx, input)
	if err != context.Canceled {
		t.Errorf("Expected context.Canceled error, got: %v", err)
	}
}
