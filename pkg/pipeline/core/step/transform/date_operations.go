package transform

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// DateOperations 日期操作转换步骤
type DateOperations struct {
	*step.BaseStep
	fieldName      string
	operation      string
	parameters     map[string]interface{}
	targetField    string
	removeOriginal bool
	inputFormat    string
	outputFormat   string
	timezone       string
}

// DateOperation 日期操作类型
type DateOperation string

const (
	DateOpFormat     DateOperation = "format"      // 格式化日期
	DateOpParse      DateOperation = "parse"       // 解析日期字符串
	DateOpAdd        DateOperation = "add"         // 添加时间
	DateOpSubtract   DateOperation = "subtract"    // 减去时间
	DateOpDiff       DateOperation = "diff"        // 计算时间差
	DateOpExtract    DateOperation = "extract"     // 提取日期部分
	DateOpTruncate   DateOperation = "truncate"    // 截断到指定精度
	DateOpToEpoch    DateOperation = "to_epoch"    // 转换为时间戳
	DateOpFromEpoch  DateOperation = "from_epoch"  // 从时间戳转换
	DateOpToTimezone DateOperation = "to_timezone" // 时区转换
	DateOpValidate   DateOperation = "validate"    // 验证日期格式
	DateOpCurrent    DateOperation = "current"     // 获取当前日期时间
)

// NewDateOperations 创建新的日期操作步骤
func NewDateOperations(stepMeta meta.StepMeta) *DateOperations {
	do := &DateOperations{
		BaseStep:       step.NewBaseStep(stepMeta),
		parameters:     make(map[string]interface{}),
		removeOriginal: false,
		inputFormat:    "2006-01-02 15:04:05", // 默认格式
		outputFormat:   "2006-01-02 15:04:05", // 默认格式
		timezone:       "UTC",                 // 默认时区
	}

	// 解析配置
	if fieldName, ok := stepMeta.Config["field_name"].(string); ok {
		do.fieldName = fieldName
	}

	if operation, ok := stepMeta.Config["operation"].(string); ok {
		do.operation = operation
	}

	if targetField, ok := stepMeta.Config["target_field"].(string); ok {
		do.targetField = targetField
	} else {
		do.targetField = do.fieldName // 默认使用原字段名
	}

	if removeOriginal, ok := stepMeta.Config["remove_original"].(bool); ok {
		do.removeOriginal = removeOriginal
	}

	if inputFormat, ok := stepMeta.Config["input_format"].(string); ok {
		do.inputFormat = inputFormat
	}

	if outputFormat, ok := stepMeta.Config["output_format"].(string); ok {
		do.outputFormat = outputFormat
	}

	if timezone, ok := stepMeta.Config["timezone"].(string); ok {
		do.timezone = timezone
	}

	// 解析操作参数
	if params, ok := stepMeta.Config["parameters"].(map[string]interface{}); ok {
		do.parameters = params
	}

	return do
}

// Execute 执行日期操作转换
func (do *DateOperations) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	if do.fieldName == "" && do.operation != string(DateOpCurrent) {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("field_name is required for DateOperations step")
	}

	if do.operation == "" {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("operation is required for DateOperations step")
	}

	var outputRows []map[string]interface{}
	var schema []meta.FieldMeta

	// 构建输出schema
	if input.Schema() != nil {
		schema = make([]meta.FieldMeta, len(input.Schema()))
		copy(schema, input.Schema())

		// 如果目标字段与原字段不同，添加新字段到schema
		if do.targetField != do.fieldName {
			targetFieldExists := false
			for _, field := range schema {
				if field.Name == do.targetField {
					targetFieldExists = true
					break
				}
			}
			if !targetFieldExists {
				schema = append(schema, meta.FieldMeta{
					Name: do.targetField,
					Type: do.getOutputFieldType(),
				})
			}
		}
	}

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 执行日期操作
		outputRowData, err := do.processRow(row)
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("date operation error: %w", err)
		}

		if outputRowData != nil {
			outputRows = append(outputRows, outputRowData)
		}
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// processRow 处理单行数据
func (do *DateOperations) processRow(row step.Row) (map[string]interface{}, error) {
	// 复制所有字段
	outputRow := make(map[string]interface{})
	for _, field := range row.Fields() {
		outputRow[field] = row.Get(field)
	}

	// 执行日期操作
	result, err := do.performOperation(row)
	if err != nil {
		return nil, fmt.Errorf("operation %s failed: %w", do.operation, err)
	}

	// 设置结果到目标字段
	outputRow[do.targetField] = result

	// 如果需要移除原字段且目标字段不同
	if do.removeOriginal && do.targetField != do.fieldName {
		delete(outputRow, do.fieldName)
	}

	return outputRow, nil
}

// performOperation 执行具体的日期操作
func (do *DateOperations) performOperation(row step.Row) (interface{}, error) {
	switch DateOperation(do.operation) {
	case DateOpCurrent:
		return do.performCurrent()

	case DateOpFormat:
		return do.performFormat(row)

	case DateOpParse:
		return do.performParse(row)

	case DateOpAdd:
		return do.performAdd(row)

	case DateOpSubtract:
		return do.performSubtract(row)

	case DateOpDiff:
		return do.performDiff(row)

	case DateOpExtract:
		return do.performExtract(row)

	case DateOpTruncate:
		return do.performTruncate(row)

	case DateOpToEpoch:
		return do.performToEpoch(row)

	case DateOpFromEpoch:
		return do.performFromEpoch(row)

	case DateOpToTimezone:
		return do.performToTimezone(row)

	case DateOpValidate:
		return do.performValidate(row)

	default:
		return nil, fmt.Errorf("unsupported date operation: %s", do.operation)
	}
}

// performCurrent 获取当前日期时间
func (do *DateOperations) performCurrent() (interface{}, error) {
	now := time.Now()

	// 处理时区
	if do.timezone != "UTC" {
		loc, err := time.LoadLocation(do.timezone)
		if err != nil {
			return nil, fmt.Errorf("invalid timezone: %w", err)
		}
		now = now.In(loc)
	}

	return now.Format(do.outputFormat), nil
}

// performFormat 格式化日期
func (do *DateOperations) performFormat(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	return t.Format(do.outputFormat), nil
}

// performParse 解析日期字符串
func (do *DateOperations) performParse(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	str := fmt.Sprintf("%v", value)
	t, err := time.Parse(do.inputFormat, str)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date string '%s' with format '%s': %w", str, do.inputFormat, err)
	}

	return t, nil
}

// performAdd 添加时间
func (do *DateOperations) performAdd(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// 获取要添加的时间
	duration, err := do.getDuration()
	if err != nil {
		return nil, fmt.Errorf("failed to get duration: %w", err)
	}

	result := t.Add(duration)
	return result.Format(do.outputFormat), nil
}

// performSubtract 减去时间
func (do *DateOperations) performSubtract(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// 获取要减去的时间
	duration, err := do.getDuration()
	if err != nil {
		return nil, fmt.Errorf("failed to get duration: %w", err)
	}

	result := t.Add(-duration)
	return result.Format(do.outputFormat), nil
}

// performDiff 计算时间差
func (do *DateOperations) performDiff(row step.Row) (interface{}, error) {
	value1 := row.Get(do.fieldName)
	if value1 == nil {
		return nil, nil
	}

	t1, err := do.parseTime(value1)
	if err != nil {
		return nil, fmt.Errorf("failed to parse first date: %w", err)
	}

	// 获取第二个时间字段
	compareField, ok := do.parameters["compare_field"].(string)
	if !ok {
		return nil, fmt.Errorf("compare_field parameter is required for diff operation")
	}

	value2 := row.Get(compareField)
	if value2 == nil {
		return nil, nil
	}

	t2, err := do.parseTime(value2)
	if err != nil {
		return nil, fmt.Errorf("failed to parse second date: %w", err)
	}

	// 计算时间差
	diff := t1.Sub(t2)

	// 返回不同的单位
	unit, ok := do.parameters["unit"].(string)
	if !ok {
		unit = "seconds"
	}

	switch unit {
	case "nanoseconds":
		return diff.Nanoseconds(), nil
	case "microseconds":
		return diff.Microseconds(), nil
	case "milliseconds":
		return diff.Milliseconds(), nil
	case "seconds":
		return int64(diff.Seconds()), nil
	case "minutes":
		return int64(diff.Minutes()), nil
	case "hours":
		return int64(diff.Hours()), nil
	case "days":
		return int64(diff.Hours() / 24), nil
	default:
		return int64(diff.Seconds()), nil
	}
}

// performExtract 提取日期部分
func (do *DateOperations) performExtract(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// 获取要提取的部分
	part, ok := do.parameters["part"].(string)
	if !ok {
		return nil, fmt.Errorf("part parameter is required for extract operation")
	}

	switch strings.ToLower(part) {
	case "year":
		return t.Year(), nil
	case "month":
		return int(t.Month()), nil
	case "day":
		return t.Day(), nil
	case "hour":
		return t.Hour(), nil
	case "minute":
		return t.Minute(), nil
	case "second":
		return t.Second(), nil
	case "weekday":
		return int(t.Weekday()), nil
	case "yearday":
		return t.YearDay(), nil
	case "week":
		_, week := t.ISOWeek()
		return week, nil
	case "quarter":
		month := t.Month()
		return (int(month)-1)/3 + 1, nil
	default:
		return nil, fmt.Errorf("unsupported date part: %s", part)
	}
}

// performTruncate 截断到指定精度
func (do *DateOperations) performTruncate(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// 获取截断精度
	precision, ok := do.parameters["precision"].(string)
	if !ok {
		return nil, fmt.Errorf("precision parameter is required for truncate operation")
	}

	var result time.Time
	switch strings.ToLower(precision) {
	case "year":
		result = time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
	case "month":
		result = time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
	case "day":
		result = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	case "hour":
		result = time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), 0, 0, 0, t.Location())
	case "minute":
		result = time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	case "second":
		result = time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second(), 0, t.Location())
	default:
		return nil, fmt.Errorf("unsupported truncate precision: %s", precision)
	}

	return result.Format(do.outputFormat), nil
}

// performToEpoch 转换为时间戳
func (do *DateOperations) performToEpoch(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// 获取时间戳单位
	unit, ok := do.parameters["unit"].(string)
	if !ok {
		unit = "seconds"
	}

	switch strings.ToLower(unit) {
	case "seconds":
		return t.Unix(), nil
	case "milliseconds":
		return t.UnixMilli(), nil
	case "microseconds":
		return t.UnixMicro(), nil
	case "nanoseconds":
		return t.UnixNano(), nil
	default:
		return t.Unix(), nil
	}
}

// performFromEpoch 从时间戳转换
func (do *DateOperations) performFromEpoch(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	// 转换为数值
	var timestamp int64
	switch v := value.(type) {
	case int64:
		timestamp = v
	case int:
		timestamp = int64(v)
	case float64:
		timestamp = int64(v)
	case string:
		var err error
		timestamp, err = strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("failed to parse timestamp: %w", err)
		}
	default:
		return nil, fmt.Errorf("invalid timestamp type: %T", value)
	}

	// 获取时间戳单位
	unit, ok := do.parameters["unit"].(string)
	if !ok {
		unit = "seconds"
	}

	var t time.Time
	switch strings.ToLower(unit) {
	case "seconds":
		t = time.Unix(timestamp, 0)
	case "milliseconds":
		t = time.UnixMilli(timestamp)
	case "microseconds":
		t = time.UnixMicro(timestamp)
	case "nanoseconds":
		t = time.Unix(0, timestamp)
	default:
		t = time.Unix(timestamp, 0)
	}

	return t.Format(do.outputFormat), nil
}

// performToTimezone 时区转换
func (do *DateOperations) performToTimezone(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return nil, nil
	}

	t, err := do.parseTime(value)
	if err != nil {
		return nil, fmt.Errorf("failed to parse date: %w", err)
	}

	// 获取目标时区
	targetTimezone, ok := do.parameters["target_timezone"].(string)
	if !ok {
		return nil, fmt.Errorf("target_timezone parameter is required for to_timezone operation")
	}

	loc, err := time.LoadLocation(targetTimezone)
	if err != nil {
		return nil, fmt.Errorf("invalid target timezone: %w", err)
	}

	result := t.In(loc)
	return result.Format(do.outputFormat), nil
}

// performValidate 验证日期格式
func (do *DateOperations) performValidate(row step.Row) (interface{}, error) {
	value := row.Get(do.fieldName)
	if value == nil {
		return false, nil
	}

	str := fmt.Sprintf("%v", value)
	_, err := time.Parse(do.inputFormat, str)
	return err == nil, nil
}

// parseTime 解析时间值
func (do *DateOperations) parseTime(value interface{}) (time.Time, error) {
	switch v := value.(type) {
	case time.Time:
		return v, nil
	case string:
		return time.Parse(do.inputFormat, v)
	case int64:
		// 尝试作为时间戳解析
		return time.Unix(v, 0), nil
	case float64:
		// 尝试作为时间戳解析
		return time.Unix(int64(v), 0), nil
	default:
		str := fmt.Sprintf("%v", value)
		return time.Parse(do.inputFormat, str)
	}
}

// getDuration 获取时间段
func (do *DateOperations) getDuration() (time.Duration, error) {
	// 支持多种时间单位
	years, _ := do.parameters["years"].(float64)
	months, _ := do.parameters["months"].(float64)
	days, _ := do.parameters["days"].(float64)
	hours, _ := do.parameters["hours"].(float64)
	minutes, _ := do.parameters["minutes"].(float64)
	seconds, _ := do.parameters["seconds"].(float64)

	// 如果有duration参数，直接解析
	if durationStr, ok := do.parameters["duration"].(string); ok {
		return time.ParseDuration(durationStr)
	}

	// 计算总的时间段（简化处理，年月按固定天数计算）
	totalDays := years*365 + months*30 + days
	totalDuration := time.Duration(totalDays*24)*time.Hour +
		time.Duration(hours)*time.Hour +
		time.Duration(minutes)*time.Minute +
		time.Duration(seconds)*time.Second

	return totalDuration, nil
}

// getOutputFieldType 获取输出字段类型
func (do *DateOperations) getOutputFieldType() meta.FieldType {
	switch DateOperation(do.operation) {
	case DateOpExtract:
		return meta.FieldTypeInteger
	case DateOpDiff:
		return meta.FieldTypeInteger
	case DateOpToEpoch:
		return meta.FieldTypeInteger
	case DateOpValidate:
		return meta.FieldTypeBoolean
	default:
		return meta.FieldTypeString
	}
}

// GetType 获取步骤类型
func (do *DateOperations) GetType() meta.StepType {
	return meta.StepTypeDateOps
}

// Validate 验证步骤配置
func (do *DateOperations) Validate() error {
	if do.operation == "" {
		return fmt.Errorf("operation is required for DateOperations step")
	}

	if do.fieldName == "" && do.operation != string(DateOpCurrent) {
		return fmt.Errorf("field_name is required for DateOperations step (except current operation)")
	}

	// 验证操作特定的参数
	switch DateOperation(do.operation) {
	case DateOpDiff:
		if _, ok := do.parameters["compare_field"]; !ok {
			return fmt.Errorf("compare_field parameter is required for diff operation")
		}
	case DateOpExtract:
		if _, ok := do.parameters["part"]; !ok {
			return fmt.Errorf("part parameter is required for extract operation")
		}
	case DateOpTruncate:
		if _, ok := do.parameters["precision"]; !ok {
			return fmt.Errorf("precision parameter is required for truncate operation")
		}
	case DateOpToTimezone:
		if _, ok := do.parameters["target_timezone"]; !ok {
			return fmt.Errorf("target_timezone parameter is required for to_timezone operation")
		}
	}

	return nil
}
