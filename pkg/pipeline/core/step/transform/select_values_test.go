package transform

import (
	"context"
	"testing"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

func TestNewSelectValues(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_select",
		Type: meta.StepTypeSelectValues,
		Config: map[string]interface{}{
			"fields": []interface{}{
				map[string]interface{}{
					"source_field": "name",
					"target_field": "full_name",
				},
			},
		},
	}

	selectStep := NewSelectValues(stepMeta)
	if selectStep == nil {
		t.Fatal("NewSelectValues should not return nil")
	}

	if selectStep.GetMeta().Name != "test_select" {
		t.<PERSON>("Expected step name 'test_select', got '%s'", selectStep.GetMeta().Name)
	}
}

func TestSelectValues_Initialize(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
	}{
		{
			name: "valid field selection",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "name",
						"target_field": "full_name",
					},
				},
			},
			expectError: false,
		},
		{
			name: "multiple field selections",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "first_name",
						"target_field": "fname",
					},
					map[string]interface{}{
						"source_field": "last_name",
						"target_field": "lname",
					},
				},
			},
			expectError: false,
		},
		{
			name: "field with remove flag",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "password",
						"remove":       true,
					},
				},
			},
			expectError: false,
		},
		{
			name: "missing fields config",
			config: map[string]interface{}{
				"other_config": "value",
			},
			expectError: true,
		},
		{
			name: "invalid field format",
			config: map[string]interface{}{
				"fields": []interface{}{
					"invalid_field",
				},
			},
			expectError: true,
		},
		{
			name: "missing source_field",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"target_field": "name",
					},
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_select",
				Type:   meta.StepTypeSelectValues,
				Config: tt.config,
			}

			selectStep := NewSelectValues(stepMeta)
			err := selectStep.Initialize()

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestSelectValues_Execute(t *testing.T) {
	tests := []struct {
		name          string
		config        map[string]interface{}
		inputRows     []map[string]interface{}
		expectedRows  int
		expectedField string
		description   string
	}{
		{
			name: "select single field",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "name",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "age": 30, "email": "<EMAIL>"},
				{"name": "Jane", "age": 25, "email": "<EMAIL>"},
			},
			expectedRows:  2,
			expectedField: "name",
			description:   "Should select only name field",
		},
		{
			name: "select and rename field",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "name",
						"target_field": "full_name",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "age": 30},
				{"name": "Jane", "age": 25},
			},
			expectedRows:  2,
			expectedField: "full_name",
			description:   "Should rename name to full_name",
		},
		{
			name: "select multiple fields",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "name",
					},
					map[string]interface{}{
						"source_field": "age",
					},
				},
			},
			inputRows: []map[string]interface{}{
				{"name": "John", "age": 30, "email": "<EMAIL>"},
				{"name": "Jane", "age": 25, "email": "<EMAIL>"},
			},
			expectedRows:  2,
			expectedField: "name",
			description:   "Should select name and age fields",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_select",
				Type:   meta.StepTypeSelectValues,
				Config: tt.config,
			}

			selectStep := NewSelectValues(stepMeta)
			err := selectStep.Initialize()
			if err != nil {
				t.Fatalf("Failed to initialize step: %v", err)
			}

			// 创建输入数据的 schema
			inputSchema := []meta.FieldMeta{
				{Name: "name", Type: meta.FieldTypeString},
				{Name: "age", Type: meta.FieldTypeInteger},
				{Name: "email", Type: meta.FieldTypeString},
			}

			input := step.NewMemoryRowSet(tt.inputRows, inputSchema)
			output, err := selectStep.Execute(context.Background(), input)
			if err != nil {
				t.Fatalf("Execute failed: %v", err)
			}

			// 验证输出行数
			rowCount := 0
			for {
				row, _, err := output.Next()
				if err != nil {
					t.Fatalf("Error reading output: %v", err)
				}
				if row == nil {
					break
				}
				rowCount++

				// 验证预期字段存在
				if !row.IsNull(tt.expectedField) || row.Get(tt.expectedField) != nil {
					// Field exists, which is what we expect
				} else {
					t.Errorf("Expected field '%s' not found in output row", tt.expectedField)
				}
			}

			if rowCount != tt.expectedRows {
				t.Errorf("Expected %d rows, got %d", tt.expectedRows, rowCount)
			}
		})
	}
}

func TestSelectValues_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      map[string]interface{}
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid configuration",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "name",
					},
				},
			},
			expectError: false,
		},
		{
			name: "missing fields configuration",
			config: map[string]interface{}{
				"other": "value",
			},
			expectError: true,
			errorMsg:    "fields",
		},
		{
			name: "empty source field",
			config: map[string]interface{}{
				"fields": []interface{}{
					map[string]interface{}{
						"source_field": "",
					},
				},
			},
			expectError: true,
			errorMsg:    "source_field is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_select",
				Type:   meta.StepTypeSelectValues,
				Config: tt.config,
			}

			selectStep := NewSelectValues(stepMeta)
			_ = selectStep.Initialize()
			err := selectStep.Validate()

			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
			if tt.expectError && err != nil && tt.errorMsg != "" {
				if !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("Expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
				}
			}
		})
	}
}

// containsString 检查字符串是否包含子字符串
func containsString(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr ||
		(len(substr) > 0 && len(s) > len(substr) &&
			(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
				func() bool {
					for i := 1; i <= len(s)-len(substr); i++ {
						if s[i:i+len(substr)] == substr {
							return true
						}
					}
					return false
				}())))
}
