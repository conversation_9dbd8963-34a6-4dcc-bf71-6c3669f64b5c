package transform

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// AddConstants 添加常量字段转换步骤
type AddConstants struct {
	*step.BaseStep
	config AddConstantsConfig
}

// AddConstantsConfig 添加常量配置
type AddConstantsConfig struct {
	Constants []ConstantFieldConfig `json:"constants" yaml:"constants"`
}

// ConstantFieldConfig 常量字段配置
type ConstantFieldConfig struct {
	Name     string      `json:"name" yaml:"name"`                             // 字段名
	Type     string      `json:"type" yaml:"type"`                             // 字段类型: string, int, float, bool, date
	Value    interface{} `json:"value" yaml:"value"`                           // 常量值
	Format   string      `json:"format,omitempty" yaml:"format,omitempty"`     // 日期格式(仅对date类型有效)
	Position int         `json:"position,omitempty" yaml:"position,omitempty"` // 插入位置，-1表示末尾
}

// NewAddConstants 创建新的添加常量步骤
func NewAddConstants(stepMeta meta.StepMeta) step.Step {
	return &AddConstants{
		BaseStep: step.NewBaseStep(stepMeta),
	}
}

// Initialize 初始化步骤
func (s *AddConstants) Initialize() error {
	// 解析配置
	if err := s.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse add constants config: %w", err)
	}

	// 验证配置
	if err := s.validateConfig(); err != nil {
		return fmt.Errorf("invalid add constants config: %w", err)
	}

	return nil
}

// Execute 执行添加常量转换
func (s *AddConstants) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input rowset is nil")
	}

	// 构建新的字段结构
	newFields, err := s.buildNewFields(input.Schema())
	if err != nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to build new fields: %w", err)
	}

	var outputRows []map[string]interface{}

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), err
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		newRowData, err := s.addConstantsToRow(row, input.Schema(), newFields)
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("failed to add constants to row: %w", err)
		}

		outputRows = append(outputRows, newRowData)
	}

	return step.NewMemoryRowSet(outputRows, newFields), nil
}

// parseConfig 解析配置
func (s *AddConstants) parseConfig() error {
	configData, err := json.Marshal(s.GetMeta().Config)
	if err != nil {
		return err
	}

	return json.Unmarshal(configData, &s.config)
}

// validateConfig 验证配置
func (s *AddConstants) validateConfig() error {
	if len(s.config.Constants) == 0 {
		return fmt.Errorf("no constants defined")
	}

	fieldNames := make(map[string]bool)
	for i, constant := range s.config.Constants {
		if constant.Name == "" {
			return fmt.Errorf("constant %d: field name is required", i)
		}

		if fieldNames[constant.Name] {
			return fmt.Errorf("duplicate constant field name: %s", constant.Name)
		}
		fieldNames[constant.Name] = true

		// 验证字段类型
		validTypes := map[string]bool{
			"string": true, "int": true, "float": true, "bool": true, "date": true,
		}
		if !validTypes[constant.Type] {
			return fmt.Errorf("constant %s: invalid type %s, must be one of: string, int, float, bool, date",
				constant.Name, constant.Type)
		}

		// 验证值类型匹配
		if err := s.validateConstantValue(constant); err != nil {
			return fmt.Errorf("constant %s: %w", constant.Name, err)
		}
	}

	return nil
}

// validateConstantValue 验证常量值类型
func (s *AddConstants) validateConstantValue(constant ConstantFieldConfig) error {
	if constant.Value == nil {
		return nil // null值总是有效的
	}

	switch constant.Type {
	case "string":
		// 任何值都可以转换为字符串
		return nil

	case "int":
		switch v := constant.Value.(type) {
		case int, int32, int64, float64:
			return nil
		case string:
			_, err := strconv.ParseInt(v, 10, 64)
			return err
		default:
			return fmt.Errorf("value cannot be converted to int")
		}

	case "float":
		switch v := constant.Value.(type) {
		case int, int32, int64, float32, float64:
			return nil
		case string:
			_, err := strconv.ParseFloat(v, 64)
			return err
		default:
			return fmt.Errorf("value cannot be converted to float")
		}

	case "bool":
		switch v := constant.Value.(type) {
		case bool:
			return nil
		case string:
			_, err := strconv.ParseBool(v)
			return err
		default:
			return fmt.Errorf("value cannot be converted to bool")
		}

	case "date":
		switch v := constant.Value.(type) {
		case string:
			format := constant.Format
			if format == "" {
				format = "2006-01-02 15:04:05" // 默认格式
			}
			_, err := time.Parse(format, v)
			return err
		case time.Time:
			return nil
		default:
			return fmt.Errorf("date value must be string or time.Time")
		}

	default:
		return fmt.Errorf("unknown type: %s", constant.Type)
	}
}

// buildNewFields 构建新的字段结构
func (s *AddConstants) buildNewFields(originalFields []meta.FieldMeta) ([]meta.FieldMeta, error) {
	fieldMap := make(map[string]meta.FieldMeta)
	var fieldOrder []string

	// 添加原有字段
	for _, field := range originalFields {
		fieldMap[field.Name] = field
		fieldOrder = append(fieldOrder, field.Name)
	}

	// 按位置插入常量字段
	for _, constant := range s.config.Constants {
		// 检查字段名冲突
		if _, exists := fieldMap[constant.Name]; exists {
			return nil, fmt.Errorf("field name conflict: %s already exists", constant.Name)
		}

		// 创建新字段
		newField := meta.FieldMeta{
			Name: constant.Name,
			Type: s.mapTypeToFieldType(constant.Type),
		}
		fieldMap[constant.Name] = newField

		// 确定插入位置
		position := constant.Position
		if position < 0 || position >= len(fieldOrder) {
			// 添加到末尾
			fieldOrder = append(fieldOrder, constant.Name)
		} else {
			// 插入到指定位置
			fieldOrder = append(fieldOrder[:position], append([]string{constant.Name}, fieldOrder[position:]...)...)
		}
	}

	// 构建最终字段列表
	var newFields []meta.FieldMeta
	for _, name := range fieldOrder {
		newFields = append(newFields, fieldMap[name])
	}

	return newFields, nil
}

// mapTypeToFieldType 映射类型到字段类型
func (s *AddConstants) mapTypeToFieldType(configType string) meta.FieldType {
	switch configType {
	case "string":
		return meta.FieldTypeString
	case "int":
		return meta.FieldTypeInteger
	case "float":
		return meta.FieldTypeFloat
	case "bool":
		return meta.FieldTypeBoolean
	case "date":
		return meta.FieldTypeDate
	default:
		return meta.FieldTypeString
	}
}

// addConstantsToRow 向行数据中添加常量字段
func (s *AddConstants) addConstantsToRow(row step.Row, originalFields []meta.FieldMeta, newFields []meta.FieldMeta) (map[string]interface{}, error) {
	// 创建新行数据
	newRowData := make(map[string]interface{})

	// 复制原有字段数据
	for _, field := range originalFields {
		newRowData[field.Name] = row.Get(field.Name)
	}

	// 添加常量字段数据
	for _, constant := range s.config.Constants {
		value, err := s.convertConstantValue(constant)
		if err != nil {
			return nil, fmt.Errorf("failed to convert constant %s: %w", constant.Name, err)
		}
		newRowData[constant.Name] = value
	}

	return newRowData, nil
}

// convertConstantValue 转换常量值
func (s *AddConstants) convertConstantValue(constant ConstantFieldConfig) (interface{}, error) {
	if constant.Value == nil {
		return nil, nil
	}

	switch constant.Type {
	case "string":
		return fmt.Sprintf("%v", constant.Value), nil

	case "int":
		switch v := constant.Value.(type) {
		case int:
			return int64(v), nil
		case int32:
			return int64(v), nil
		case int64:
			return v, nil
		case float64:
			return int64(v), nil
		case string:
			return strconv.ParseInt(v, 10, 64)
		default:
			return nil, fmt.Errorf("cannot convert %T to int", v)
		}

	case "float":
		switch v := constant.Value.(type) {
		case int:
			return float64(v), nil
		case int32:
			return float64(v), nil
		case int64:
			return float64(v), nil
		case float32:
			return float64(v), nil
		case float64:
			return v, nil
		case string:
			return strconv.ParseFloat(v, 64)
		default:
			return nil, fmt.Errorf("cannot convert %T to float", v)
		}

	case "bool":
		switch v := constant.Value.(type) {
		case bool:
			return v, nil
		case string:
			return strconv.ParseBool(v)
		default:
			return nil, fmt.Errorf("cannot convert %T to bool", v)
		}

	case "date":
		switch v := constant.Value.(type) {
		case string:
			format := constant.Format
			if format == "" {
				format = "2006-01-02 15:04:05"
			}
			return time.Parse(format, v)
		case time.Time:
			return v, nil
		default:
			return nil, fmt.Errorf("cannot convert %T to date", v)
		}

	default:
		return nil, fmt.Errorf("unknown type: %s", constant.Type)
	}
}

// GetType 返回步骤类型
func (s *AddConstants) GetType() meta.StepType {
	return meta.StepTypeAddConstants
}
