package transform

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"admin/pkg/expr"
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// Calculator 计算器步骤，用于计算新字段或修改现有字段
type Calculator struct {
	*step.BaseStep
	calculations []Calculation   // 计算配置
	exprEngine   expr.ExprEngine // 表达式引擎
}

// Calculation 单个计算配置
type Calculation struct {
	TargetField string `json:"target_field"` // 目标字段名
	Expression  string `json:"expression"`   // 计算表达式
	DataType    string `json:"data_type"`    // 数据类型: string, int, float, bool
}

// NewCalculator 创建一个新的计算器步骤
func NewCalculator(stepMeta meta.StepMeta) *Calculator {
	return &Calculator{
		BaseStep:     step.NewBaseStep(stepMeta),
		calculations: []Calculation{},
		exprEngine:   expr.NewExprEngine(),
	}
}

// Initialize 初始化步骤
func (c *Calculator) Initialize() error {
	if err := c.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中解析计算规则
	if err := c.parseCalculations(); err != nil {
		return fmt.Errorf("failed to parse calculations: %w", err)
	}

	return nil
}

// Execute 执行字段计算
func (c *Calculator) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	var outputRows []map[string]interface{}
	var schema []meta.FieldMeta

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 执行计算并创建输出行
		outputRowData := c.calculateFields(row)
		if outputRowData != nil {
			outputRows = append(outputRows, outputRowData)
		}
	}

	// 构建输出schema，包含原始字段和新计算的字段
	if input.Schema() != nil {
		schema = input.Schema()
		// 添加计算字段到schema
		for _, calc := range c.calculations {
			// 检查是否已经存在该字段
			fieldExists := false
			for _, field := range schema {
				if field.Name == calc.TargetField {
					fieldExists = true
					break
				}
			}

			// 如果字段不存在，添加到schema
			if !fieldExists {
				fieldType := meta.FieldTypeString // 默认类型
				switch calc.DataType {
				case "int", "integer":
					fieldType = meta.FieldTypeInteger
				case "float", "double":
					fieldType = meta.FieldTypeFloat
				case "bool", "boolean":
					fieldType = meta.FieldTypeBoolean
				}

				schema = append(schema, meta.FieldMeta{
					Name: calc.TargetField,
					Type: fieldType,
				})
			}
		}
	}

	return step.NewMemoryRowSet(outputRows, schema), nil
}

// Validate 验证步骤配置
func (c *Calculator) Validate() error {
	if err := c.BaseStep.Validate(); err != nil {
		return err
	}

	// 验证计算配置
	if len(c.calculations) == 0 {
		if _, exists := c.GetConfigSlice("calculations"); !exists {
			return meta.NewValidationError("Calculator step requires 'calculations' configuration")
		}
	}

	// 验证每个计算配置
	for i, calc := range c.calculations {
		if calc.TargetField == "" {
			return meta.NewValidationErrorf("calculation %d: target_field is required", i)
		}
		if calc.Expression == "" {
			return meta.NewValidationErrorf("calculation %d: expression is required", i)
		}
		if calc.DataType != "" && !isValidDataType(calc.DataType) {
			return meta.NewValidationErrorf("calculation %d: invalid data_type '%s'", i, calc.DataType)
		}
	}

	return nil
}

// parseCalculations 解析计算配置
func (c *Calculator) parseCalculations() error {
	// 从配置中获取计算列表
	calculationsConfig, exists := c.GetConfigSlice("calculations")
	if !exists {
		return fmt.Errorf("calculations configuration not found")
	}

	c.calculations = []Calculation{}

	for i, calcConfig := range calculationsConfig {
		calcMap, ok := calcConfig.(map[string]interface{})
		if !ok {
			return fmt.Errorf("calculation %d: invalid calculation configuration format", i)
		}

		calculation := Calculation{}

		// 解析目标字段名
		if targetField, ok := calcMap["target_field"].(string); ok {
			calculation.TargetField = targetField
		} else {
			return fmt.Errorf("calculation %d: target_field is required and must be a string", i)
		}

		// 解析表达式
		if expression, ok := calcMap["expression"].(string); ok {
			calculation.Expression = expression
		} else {
			return fmt.Errorf("calculation %d: expression is required and must be a string", i)
		}

		// 解析数据类型（可选）
		if dataType, ok := calcMap["data_type"].(string); ok {
			calculation.DataType = dataType
		} else {
			calculation.DataType = "string" // 默认为字符串类型
		}

		c.calculations = append(c.calculations, calculation)
	}

	return nil
}

// calculateFields 对单行数据执行所有计算
func (c *Calculator) calculateFields(inputRow step.Row) map[string]interface{} {
	// 复制原始数据
	outputData := make(map[string]interface{})
	for _, field := range inputRow.Fields() {
		outputData[field] = inputRow.Get(field)
	}

	// 创建一个临时行，包含已计算的字段，用于后续计算的字段引用
	tempRow := step.NewSimpleRow(outputData)

	// 执行每个计算
	for _, calc := range c.calculations {
		result, err := c.evaluateExpression(calc.Expression, tempRow)
		if err != nil {
			// 计算出错时，设置为nil或保持原值
			result = nil
		}

		// 转换数据类型
		convertedResult := c.convertDataType(result, calc.DataType)
		outputData[calc.TargetField] = convertedResult

		// 更新临时行，这样后续计算可以引用这个新字段
		tempRow.Set(calc.TargetField, convertedResult)
	}

	return outputData
}

// evaluateExpression 计算表达式（使用完整的表达式引擎）
func (c *Calculator) evaluateExpression(expression string, row step.Row) (interface{}, error) {
	// 处理特殊情况：字符串连接与单纯字段引用
	// 如果表达式只包含字段引用和空格，例如 "{first_name} {last_name}"，则直接处理为字符串连接
	if isSimpleStringConcat(expression) {
		return c.directStringHandling(expression, row)
	}

	// 提取字段引用
	fieldRefs := extractFieldRefs(expression)

	// 准备变量映射
	variables := make(map[string]any)

	// 将所有字段添加到变量映射中
	for _, field := range row.Fields() {
		variables[field] = row.Get(field)
	}

	// 转换表达式中的字段引用 {field_name} -> field_name
	convertedExpression := expression
	for _, fieldRef := range fieldRefs {
		convertedExpression = strings.Replace(convertedExpression, "{"+fieldRef+"}", fieldRef, -1)
	}

	// 使用表达式引擎计算
	result, err := c.exprEngine.Evaluate(convertedExpression, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate expression '%s' (converted: '%s'): %w", expression, convertedExpression, err)
	}

	return result, nil
}

// extractFieldRefs 从表达式中提取所有字段引用
func extractFieldRefs(expression string) []string {
	var refs []string
	inBrace := false
	current := ""

	for _, char := range expression {
		if char == '{' {
			inBrace = true
			current = ""
		} else if char == '}' && inBrace {
			inBrace = false
			refs = append(refs, current)
		} else if inBrace {
			current += string(char)
		}
	}

	return refs
}

// directStringHandling 直接处理字符串连接或简单字段引用
func (c *Calculator) directStringHandling(expression string, row step.Row) (interface{}, error) {
	// 去除表达式中的所有大括号，得到字段名列表
	result := ""
	current := ""
	inBrace := false

	for _, char := range expression {
		switch char {
		case '{':
			inBrace = true
		case '}':
			inBrace = false
			// 从row中获取字段值
			if fieldValue := row.Get(current); fieldValue != nil {
				result += fmt.Sprintf("%v", fieldValue)
			}
			current = ""
		default:
			if inBrace {
				current += string(char)
			} else if char != ' ' {
				// 保留非空格字符
				result += string(char)
			} else {
				// 保留空格作为连接符
				result += " "
			}
		}
	}

	return result, nil
}

// isSimpleStringConcat 检查表达式是否只是简单的字符串连接
func isSimpleStringConcat(expression string) bool {
	// 如果表达式中包含运算符，则不是简单的字符串连接
	operators := []string{"+", "-", "*", "/", "%", "==", "!=", ">", "<", ">=", "<=", "&&", "||"}
	for _, op := range operators {
		if strings.Contains(expression, op) {
			return false
		}
	}

	// 检查是否是简单的字段引用和空格组合
	inBrace := false
	for _, char := range expression {
		switch {
		case char == '{':
			inBrace = true
		case char == '}':
			inBrace = false
		case char == ' ':
			// 允许空格
			continue
		default:
			if !inBrace {
				// 如果不在大括号内且不是空格，则不是简单字段引用
				return false
			}
		}
	}
	return true
}

// convertDataType 转换数据类型
func (c *Calculator) convertDataType(value interface{}, dataType string) interface{} {
	if value == nil {
		return nil
	}

	switch strings.ToLower(dataType) {
	case "int", "integer":
		if f, ok := toFloat64(value); ok {
			return int64(f)
		}
		if s, ok := value.(string); ok {
			if i, err := strconv.ParseInt(s, 10, 64); err == nil {
				return i
			}
		}
		return int64(0)

	case "float", "double":
		if f, ok := toFloat64(value); ok {
			return f
		}
		if s, ok := value.(string); ok {
			if f, err := strconv.ParseFloat(s, 64); err == nil {
				return f
			}
		}
		return 0.0

	case "bool", "boolean":
		if b, ok := value.(bool); ok {
			return b
		}
		if s, ok := value.(string); ok {
			if b, err := strconv.ParseBool(s); err == nil {
				return b
			}
		}
		if f, ok := toFloat64(value); ok {
			return f != 0
		}
		return false

	default: // "string"
		return fmt.Sprintf("%v", value)
	}
}

// isValidDataType 检查数据类型是否有效
func isValidDataType(dataType string) bool {
	validTypes := []string{"string", "int", "integer", "float", "double", "bool", "boolean"}
	for _, valid := range validTypes {
		if strings.ToLower(dataType) == valid {
			return true
		}
	}
	return false
}

// toFloat64 尝试将值转换为float64
func toFloat64(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case float64:
		return v, true
	case float32:
		return float64(v), true
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case int32:
		return float64(v), true
	case int16:
		return float64(v), true
	case int8:
		return float64(v), true
	case uint:
		return float64(v), true
	case uint64:
		return float64(v), true
	case uint32:
		return float64(v), true
	case uint16:
		return float64(v), true
	case uint8:
		return float64(v), true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		}
	case bool:
		if v {
			return 1, true
		}
		return 0, true
	}
	return 0, false
}
