package enhanced

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// EnhancedFieldMapper 增强字段映射器
type EnhancedFieldMapper struct {
	*EnhancedTransformStep
	fieldMappings    []FieldMapping
	renameMappings   map[string]string
	removedFields    []string
	addedFields      []FieldMapping
	validationRules  []FieldValidationRule
	transformRules   []FieldTransformRule
	outputSchema     *step.Schema
	preserveUnmapped bool
	strictMode       bool
}

// FieldValidationRule 字段验证规则
type FieldValidationRule struct {
	Field        string `json:"field"`         // 字段名
	Required     bool   `json:"required"`      // 是否必填
	MinLength    int    `json:"min_length"`    // 最小长度
	MaxLength    int    `json:"max_length"`    // 最大长度
	Pattern      string `json:"pattern"`       // 正则表达式模式
	ValueSet     []any  `json:"value_set"`     // 允许的值集合
	MinValue     any    `json:"min_value"`     // 最小值
	MaxValue     any    `json:"max_value"`     // 最大值
	CustomRule   string `json:"custom_rule"`   // 自定义验证表达式
	ErrorMessage string `json:"error_message"` // 自定义错误信息
	DefaultValue any    `json:"default_value"` // 验证失败时的默认值
}

// FieldTransformRule 字段转换规则
type FieldTransformRule struct {
	Field       string         `json:"field"`        // 源字段名
	Operation   string         `json:"operation"`    // 操作类型
	Parameters  map[string]any `json:"parameters"`   // 操作参数
	TargetField string         `json:"target_field"` // 目标字段名（可选）
	Condition   string         `json:"condition"`    // 执行条件
}

// TransformOperation 转换操作类型
const (
	OpTrim           = "trim"
	OpUpperCase      = "upper_case"
	OpLowerCase      = "lower_case"
	OpTitleCase      = "title_case"
	OpPadLeft        = "pad_left"
	OpPadRight       = "pad_right"
	OpSubstring      = "substring"
	OpReplace        = "replace"
	OpRegexReplace   = "regex_replace"
	OpConcat         = "concat"
	OpSplit          = "split"
	OpJoin           = "join"
	OpFormatNumber   = "format_number"
	OpFormatDate     = "format_date"
	OpTypeConvert    = "type_convert"
	OpCustomFunction = "custom_function"
)

// NewEnhancedFieldMapper 创建增强字段映射器
func NewEnhancedFieldMapper(stepMeta meta.StepMeta) *EnhancedFieldMapper {
	return &EnhancedFieldMapper{
		EnhancedTransformStep: NewEnhancedTransformStep(stepMeta),
		fieldMappings:         []FieldMapping{},
		renameMappings:        make(map[string]string),
		removedFields:         []string{},
		addedFields:           []FieldMapping{},
		validationRules:       []FieldValidationRule{},
		transformRules:        []FieldTransformRule{},
		preserveUnmapped:      true,
		strictMode:            false,
	}
}

// Initialize 初始化字段映射器
func (efm *EnhancedFieldMapper) Initialize() error {
	if err := efm.BaseStep.Initialize(); err != nil {
		return err
	}

	return efm.parseFieldMappingConfiguration()
}

// Execute 执行字段映射
func (efm *EnhancedFieldMapper) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	efm.metrics.StartTime = getCurrentNano()

	// 如果有输出模式定义，使用它；否则根据映射规则推断
	if efm.outputSchema == nil {
		inputSchema := efm.convertSchemaFromMeta(input.Schema())
		efm.outputSchema = efm.inferOutputSchema(inputSchema)
	}

	return efm.ExecuteWithContext(ctx, input, func(row step.Row) (step.Row, error) {
		return efm.mapRow(row)
	})
}

// parseFieldMappingConfiguration 解析字段映射配置
func (efm *EnhancedFieldMapper) parseFieldMappingConfiguration() error {
	// 解析字段映射
	if mappingsConfig, exists := efm.GetConfigSlice("field_mappings"); exists {
		for i, mappingConfig := range mappingsConfig {
			mapping, err := efm.parseFieldMapping(mappingConfig, i)
			if err != nil {
				return err
			}
			efm.fieldMappings = append(efm.fieldMappings, mapping)
		}
	}

	// 解析重命名映射
	if renameConfig, exists := efm.GetConfigMap("rename_mappings"); exists {
		for oldName, newName := range renameConfig {
			if newNameStr, ok := newName.(string); ok {
				efm.renameMappings[oldName] = newNameStr
			}
		}
	}

	// 解析移除字段
	if removedConfig, exists := efm.GetConfigSlice("removed_fields"); exists {
		for _, fieldConfig := range removedConfig {
			if fieldName, ok := fieldConfig.(string); ok {
				efm.removedFields = append(efm.removedFields, fieldName)
			}
		}
	}

	// 解析添加字段
	if addedConfig, exists := efm.GetConfigSlice("added_fields"); exists {
		for i, fieldConfig := range addedConfig {
			field, err := efm.parseFieldMapping(fieldConfig, i)
			if err != nil {
				return fmt.Errorf("added field %d: %w", i, err)
			}
			efm.addedFields = append(efm.addedFields, field)
		}
	}

	// 解析验证规则
	if validationConfig, exists := efm.GetConfigSlice("validation_rules"); exists {
		for i, ruleConfig := range validationConfig {
			rule, err := efm.parseValidationRule(ruleConfig, i)
			if err != nil {
				return err
			}
			efm.validationRules = append(efm.validationRules, rule)
		}
	}

	// 解析转换规则
	if transformConfig, exists := efm.GetConfigSlice("transform_rules"); exists {
		for i, ruleConfig := range transformConfig {
			rule, err := efm.parseTransformRule(ruleConfig, i)
			if err != nil {
				return err
			}
			efm.transformRules = append(efm.transformRules, rule)
		}
	}

	// 解析选项
	if preserve, exists := efm.GetConfigBool("preserve_unmapped"); exists {
		efm.preserveUnmapped = preserve
	}

	if strict, exists := efm.GetConfigBool("strict_mode"); exists {
		efm.strictMode = strict
	}

	return efm.validateConfiguration()
}

// parseFieldMapping 解析字段映射
func (efm *EnhancedFieldMapper) parseFieldMapping(mappingConfig interface{}, index int) (FieldMapping, error) {
	mappingMap, ok := mappingConfig.(map[string]interface{})
	if !ok {
		return FieldMapping{}, fmt.Errorf("field mapping %d: invalid format", index)
	}

	mapping := FieldMapping{}

	// 源字段
	if sourceField, ok := mappingMap["source_field"].(string); ok {
		mapping.SourceField = sourceField
	} else {
		return FieldMapping{}, fmt.Errorf("field mapping %d: source_field is required", index)
	}

	// 目标字段
	if targetField, ok := mappingMap["target_field"].(string); ok {
		mapping.TargetField = targetField
	} else {
		mapping.TargetField = mapping.SourceField // 默认使用源字段名
	}

	// 转换表达式
	if expression, ok := mappingMap["expression"].(string); ok {
		mapping.Expression = expression
	}

	// 数据类型
	if dataType, ok := mappingMap["data_type"].(string); ok {
		mapping.DataType = dataType
	}

	// 默认值
	mapping.DefaultValue = mappingMap["default_value"]

	// 是否必填
	if required, ok := mappingMap["required"].(bool); ok {
		mapping.Required = required
	}

	return mapping, nil
}

// parseValidationRule 解析验证规则
func (efm *EnhancedFieldMapper) parseValidationRule(ruleConfig interface{}, index int) (FieldValidationRule, error) {
	ruleMap, ok := ruleConfig.(map[string]interface{})
	if !ok {
		return FieldValidationRule{}, fmt.Errorf("validation rule %d: invalid format", index)
	}

	rule := FieldValidationRule{}

	// 字段名
	if field, ok := ruleMap["field"].(string); ok {
		rule.Field = field
	} else {
		return FieldValidationRule{}, fmt.Errorf("validation rule %d: field is required", index)
	}

	// 必填
	if required, ok := ruleMap["required"].(bool); ok {
		rule.Required = required
	}

	// 长度限制
	if minLen, ok := ruleMap["min_length"].(float64); ok {
		rule.MinLength = int(minLen)
	}
	if maxLen, ok := ruleMap["max_length"].(float64); ok {
		rule.MaxLength = int(maxLen)
	}

	// 正则模式
	if pattern, ok := ruleMap["pattern"].(string); ok {
		rule.Pattern = pattern
	}

	// 值集合
	if valueSet, ok := ruleMap["value_set"].([]interface{}); ok {
		rule.ValueSet = valueSet
	}

	// 值范围
	rule.MinValue = ruleMap["min_value"]
	rule.MaxValue = ruleMap["max_value"]

	// 自定义规则
	if customRule, ok := ruleMap["custom_rule"].(string); ok {
		rule.CustomRule = customRule
	}

	// 错误信息
	if errorMsg, ok := ruleMap["error_message"].(string); ok {
		rule.ErrorMessage = errorMsg
	}

	// 默认值
	rule.DefaultValue = ruleMap["default_value"]

	return rule, nil
}

// parseTransformRule 解析转换规则
func (efm *EnhancedFieldMapper) parseTransformRule(ruleConfig interface{}, index int) (FieldTransformRule, error) {
	ruleMap, ok := ruleConfig.(map[string]interface{})
	if !ok {
		return FieldTransformRule{}, fmt.Errorf("transform rule %d: invalid format", index)
	}

	rule := FieldTransformRule{}

	// 字段名
	if field, ok := ruleMap["field"].(string); ok {
		rule.Field = field
	} else {
		return FieldTransformRule{}, fmt.Errorf("transform rule %d: field is required", index)
	}

	// 操作类型
	if operation, ok := ruleMap["operation"].(string); ok {
		rule.Operation = operation
	} else {
		return FieldTransformRule{}, fmt.Errorf("transform rule %d: operation is required", index)
	}

	// 参数
	if parameters, ok := ruleMap["parameters"].(map[string]interface{}); ok {
		rule.Parameters = parameters
	} else {
		rule.Parameters = make(map[string]any)
	}

	// 目标字段
	if targetField, ok := ruleMap["target_field"].(string); ok {
		rule.TargetField = targetField
	} else {
		rule.TargetField = rule.Field // 默认使用源字段
	}

	// 执行条件
	if condition, ok := ruleMap["condition"].(string); ok {
		rule.Condition = condition
	}

	return rule, nil
}

// mapRow 映射单行数据
func (efm *EnhancedFieldMapper) mapRow(row step.Row) (step.Row, error) {
	outputData := make(map[string]interface{})

	// 1. 处理保留的未映射字段
	if efm.preserveUnmapped {
		for _, fieldName := range row.Fields() {
			// 检查是否应该被移除
			if efm.shouldRemoveField(fieldName) {
				continue
			}
			outputData[fieldName] = row.Get(fieldName)
		}
	}

	// 2. 应用字段映射
	for _, mapping := range efm.fieldMappings {
		value, err := efm.applyFieldMapping(row, mapping)
		if err != nil {
			if efm.strictMode {
				return nil, err
			}
			// 在非严格模式下，记录错误但继续处理
			efm.recordError(fmt.Errorf("field mapping error for %s: %w", mapping.SourceField, err))
			continue
		}
		outputData[mapping.TargetField] = value
	}

	// 3. 应用重命名映射
	for oldName, newName := range efm.renameMappings {
		if value, exists := outputData[oldName]; exists {
			delete(outputData, oldName)
			outputData[newName] = value
		}
	}

	// 4. 添加新字段
	for _, addedField := range efm.addedFields {
		value, err := efm.applyFieldMapping(row, addedField)
		if err != nil {
			if efm.strictMode {
				return nil, err
			}
			efm.recordError(fmt.Errorf("added field error for %s: %w", addedField.TargetField, err))
			continue
		}
		outputData[addedField.TargetField] = value
	}

	// 5. 应用转换规则
	for _, rule := range efm.transformRules {
		if err := efm.applyTransformRule(outputData, rule); err != nil {
			if efm.strictMode {
				return nil, err
			}
			efm.recordError(fmt.Errorf("transform rule error for %s: %w", rule.Field, err))
		}
	}

	// 6. 应用验证规则
	for _, rule := range efm.validationRules {
		if err := efm.applyValidationRule(outputData, rule); err != nil {
			if efm.strictMode {
				return nil, err
			}
			efm.recordError(fmt.Errorf("validation error for %s: %w", rule.Field, err))
		}
	}

	// 7. 最终清理：移除标记为删除的字段
	for _, fieldName := range efm.removedFields {
		delete(outputData, fieldName)
	}

	return step.NewSimpleRow(outputData), nil
}

// applyFieldMapping 应用字段映射
func (efm *EnhancedFieldMapper) applyFieldMapping(row step.Row, mapping FieldMapping) (interface{}, error) {
	var value interface{}
	var err error

	// 如果有表达式，使用表达式计算
	if mapping.Expression != "" {
		value, err = efm.EvaluateExpression(mapping.Expression, row)
		if err != nil {
			return nil, fmt.Errorf("expression evaluation failed: %w", err)
		}
	} else {
		// 否则直接获取源字段值
		value = row.Get(mapping.SourceField)
	}

	// 处理空值
	if value == nil {
		if mapping.Required {
			return nil, fmt.Errorf("required field %s is null", mapping.SourceField)
		}
		if mapping.DefaultValue != nil {
			value = mapping.DefaultValue
		}
	}

	// 类型转换
	if mapping.DataType != "" && value != nil {
		convertedValue, err := efm.convertType(value, mapping.DataType)
		if err != nil {
			return nil, fmt.Errorf("type conversion failed: %w", err)
		}
		value = convertedValue
	}

	return value, nil
}

// applyTransformRule 应用转换规则
func (efm *EnhancedFieldMapper) applyTransformRule(data map[string]interface{}, rule FieldTransformRule) error {
	// 检查执行条件
	if rule.Condition != "" {
		shouldApply, err := efm.evaluateCondition(data, rule.Condition)
		if err != nil {
			return fmt.Errorf("condition evaluation failed: %w", err)
		}
		if !shouldApply {
			return nil // 跳过这个规则
		}
	}

	fieldValue, exists := data[rule.Field]
	if !exists {
		return nil // 字段不存在，跳过
	}

	transformedValue, err := efm.applyTransformation(fieldValue, rule.Operation, rule.Parameters)
	if err != nil {
		return err
	}

	// 设置到目标字段
	data[rule.TargetField] = transformedValue

	// 如果目标字段不同于源字段，可以选择是否删除源字段
	// 这里保留源字段，让用户通过 removed_fields 配置来控制

	return nil
}

// applyTransformation 应用具体转换操作
func (efm *EnhancedFieldMapper) applyTransformation(value interface{}, operation string, parameters map[string]any) (interface{}, error) {
	if value == nil {
		return value, nil
	}

	str := fmt.Sprintf("%v", value)

	switch operation {
	case OpTrim:
		return strings.TrimSpace(str), nil

	case OpUpperCase:
		return strings.ToUpper(str), nil

	case OpLowerCase:
		return strings.ToLower(str), nil

	case OpTitleCase:
		return strings.Title(strings.ToLower(str)), nil

	case OpPadLeft:
		if lengthParam, ok := parameters["length"].(float64); ok {
			if padChar, ok := parameters["char"].(string); ok {
				length := int(lengthParam)
				if len(padChar) == 0 {
					padChar = " "
				}
				return efm.padLeft(str, length, padChar), nil
			}
		}
		return nil, fmt.Errorf("pad_left requires 'length' and 'char' parameters")

	case OpPadRight:
		if lengthParam, ok := parameters["length"].(float64); ok {
			if padChar, ok := parameters["char"].(string); ok {
				length := int(lengthParam)
				if len(padChar) == 0 {
					padChar = " "
				}
				return efm.padRight(str, length, padChar), nil
			}
		}
		return nil, fmt.Errorf("pad_right requires 'length' and 'char' parameters")

	case OpSubstring:
		if startParam, ok := parameters["start"].(float64); ok {
			start := int(startParam)
			if endParam, ok := parameters["end"].(float64); ok {
				end := int(endParam)
				return efm.substring(str, start, end), nil
			} else if lengthParam, ok := parameters["length"].(float64); ok {
				length := int(lengthParam)
				return efm.substring(str, start, start+length), nil
			}
		}
		return nil, fmt.Errorf("substring requires 'start' and ('end' or 'length') parameters")

	case OpReplace:
		if oldStr, ok := parameters["old"].(string); ok {
			if newStr, ok := parameters["new"].(string); ok {
				return strings.ReplaceAll(str, oldStr, newStr), nil
			}
		}
		return nil, fmt.Errorf("replace requires 'old' and 'new' parameters")

	case OpRegexReplace:
		if pattern, ok := parameters["pattern"].(string); ok {
			if replacement, ok := parameters["replacement"].(string); ok {
				regex, err := regexp.Compile(pattern)
				if err != nil {
					return nil, fmt.Errorf("invalid regex pattern: %w", err)
				}
				return regex.ReplaceAllString(str, replacement), nil
			}
		}
		return nil, fmt.Errorf("regex_replace requires 'pattern' and 'replacement' parameters")

	case OpConcat:
		if values, ok := parameters["values"].([]interface{}); ok {
			var parts []string
			parts = append(parts, str)
			for _, v := range values {
				parts = append(parts, fmt.Sprintf("%v", v))
			}
			separator := ""
			if sep, ok := parameters["separator"].(string); ok {
				separator = sep
			}
			return strings.Join(parts, separator), nil
		}
		return nil, fmt.Errorf("concat requires 'values' parameter")

	case OpSplit:
		if separator, ok := parameters["separator"].(string); ok {
			return strings.Split(str, separator), nil
		}
		return nil, fmt.Errorf("split requires 'separator' parameter")

	default:
		return nil, fmt.Errorf("unsupported transformation operation: %s", operation)
	}
}

// applyValidationRule 应用验证规则
func (efm *EnhancedFieldMapper) applyValidationRule(data map[string]interface{}, rule FieldValidationRule) error {
	value, exists := data[rule.Field]

	// 检查必填
	if rule.Required && (!exists || value == nil) {
		if rule.DefaultValue != nil {
			data[rule.Field] = rule.DefaultValue
			return nil
		}
		return fmt.Errorf("required field %s is missing or null", rule.Field)
	}

	if !exists || value == nil {
		return nil // 非必填字段为空，跳过验证
	}

	str := fmt.Sprintf("%v", value)

	// 长度验证
	if rule.MinLength > 0 && len(str) < rule.MinLength {
		return efm.handleValidationError(rule, fmt.Sprintf("field %s length %d is less than minimum %d", rule.Field, len(str), rule.MinLength))
	}
	if rule.MaxLength > 0 && len(str) > rule.MaxLength {
		return efm.handleValidationError(rule, fmt.Sprintf("field %s length %d exceeds maximum %d", rule.Field, len(str), rule.MaxLength))
	}

	// 正则验证
	if rule.Pattern != "" {
		matched, err := regexp.MatchString(rule.Pattern, str)
		if err != nil {
			return fmt.Errorf("invalid regex pattern: %w", err)
		}
		if !matched {
			return efm.handleValidationError(rule, fmt.Sprintf("field %s value does not match pattern %s", rule.Field, rule.Pattern))
		}
	}

	// 值集合验证
	if len(rule.ValueSet) > 0 {
		found := false
		for _, allowedValue := range rule.ValueSet {
			if fmt.Sprintf("%v", value) == fmt.Sprintf("%v", allowedValue) {
				found = true
				break
			}
		}
		if !found {
			return efm.handleValidationError(rule, fmt.Sprintf("field %s value is not in allowed set", rule.Field))
		}
	}

	// 自定义规则验证
	if rule.CustomRule != "" {
		valid, err := efm.evaluateCustomValidation(data, rule.CustomRule)
		if err != nil {
			return fmt.Errorf("custom validation error: %w", err)
		}
		if !valid {
			return efm.handleValidationError(rule, fmt.Sprintf("field %s failed custom validation", rule.Field))
		}
	}

	return nil
}

// handleValidationError 处理验证错误
func (efm *EnhancedFieldMapper) handleValidationError(rule FieldValidationRule, defaultMessage string) error {
	message := defaultMessage
	if rule.ErrorMessage != "" {
		message = rule.ErrorMessage
	}
	return fmt.Errorf("%s", message)
}

// evaluateCondition 评估条件
func (efm *EnhancedFieldMapper) evaluateCondition(data map[string]interface{}, condition string) (bool, error) {
	// 创建临时行用于表达式计算
	tempRow := step.NewSimpleRow(data)
	return efm.EvaluateExpressionToBoolean(condition, tempRow)
}

// evaluateCustomValidation 评估自定义验证
func (efm *EnhancedFieldMapper) evaluateCustomValidation(data map[string]interface{}, rule string) (bool, error) {
	// 创建临时行用于表达式计算
	tempRow := step.NewSimpleRow(data)
	return efm.EvaluateExpressionToBoolean(rule, tempRow)
}

// shouldRemoveField 检查字段是否应该被移除
func (efm *EnhancedFieldMapper) shouldRemoveField(fieldName string) bool {
	for _, removedField := range efm.removedFields {
		if removedField == fieldName {
			return true
		}
	}
	return false
}

// convertSchemaFromMeta 将meta.FieldMeta转换为step.Schema
func (efm *EnhancedFieldMapper) convertSchemaFromMeta(fieldMetas []meta.FieldMeta) *step.Schema {
	if fieldMetas == nil {
		return nil
	}

	fields := make([]step.Field, len(fieldMetas))
	for i, fieldMeta := range fieldMetas {
		fields[i] = step.Field{
			Name:   fieldMeta.Name,
			Type:   efm.convertMetaFieldTypeToStepFieldType(fieldMeta.Type),
			Length: fieldMeta.Length,
			Format: fieldMeta.Format,
		}
	}

	return &step.Schema{Fields: fields}
}

// convertMetaFieldTypeToStepFieldType 转换字段类型
func (efm *EnhancedFieldMapper) convertMetaFieldTypeToStepFieldType(metaType meta.FieldType) step.FieldType {
	switch metaType {
	case meta.FieldTypeString:
		return step.StringType
	case meta.FieldTypeInteger, meta.FieldTypeLong, meta.FieldTypeBigInt:
		return step.IntegerType
	case meta.FieldTypeFloat, meta.FieldTypeDouble, meta.FieldTypeNumber:
		return step.FloatType
	case meta.FieldTypeBoolean:
		return step.BooleanType
	case meta.FieldTypeDate, meta.FieldTypeTimestamp:
		return step.DateTimeType
	case meta.FieldTypeBinary:
		return step.BinaryType
	default:
		return step.StringType
	}
}

// inferOutputSchema 推断输出模式
func (efm *EnhancedFieldMapper) inferOutputSchema(inputSchema *step.Schema) *step.Schema {
	if inputSchema == nil {
		return nil
	}

	outputFields := make([]step.Field, 0)

	// 处理现有字段
	if efm.preserveUnmapped {
		for _, field := range inputSchema.Fields {
			if !efm.shouldRemoveField(field.Name) {
				// 检查是否有重命名
				if newName, exists := efm.renameMappings[field.Name]; exists {
					newField := field
					newField.Name = newName
					outputFields = append(outputFields, newField)
				} else {
					outputFields = append(outputFields, field)
				}
			}
		}
	}

	// 添加映射字段
	for _, mapping := range efm.fieldMappings {
		field := step.Field{
			Name: mapping.TargetField,
			Type: efm.getFieldType(mapping.DataType),
		}
		outputFields = append(outputFields, field)
	}

	// 添加新字段
	for _, addedField := range efm.addedFields {
		field := step.Field{
			Name: addedField.TargetField,
			Type: efm.getFieldType(addedField.DataType),
		}
		outputFields = append(outputFields, field)
	}

	return &step.Schema{Fields: outputFields}
}

// getFieldType 获取字段类型
func (efm *EnhancedFieldMapper) getFieldType(dataType string) step.FieldType {
	switch strings.ToLower(dataType) {
	case "string", "text":
		return step.StringType
	case "int", "integer":
		return step.IntegerType
	case "float", "double", "number":
		return step.FloatType
	case "bool", "boolean":
		return step.BooleanType
	case "date", "datetime", "timestamp":
		return step.DateTimeType
	default:
		return step.StringType // 默认为字符串类型
	}
}

// convertType 类型转换
func (efm *EnhancedFieldMapper) convertType(value interface{}, targetType string) (interface{}, error) {
	converter := NewTypeConverter(targetType)
	return converter.Convert(value)
}

// 字符串处理辅助函数
func (efm *EnhancedFieldMapper) padLeft(str string, length int, padChar string) string {
	if len(str) >= length {
		return str
	}
	padCount := length - len(str)
	return strings.Repeat(padChar, padCount) + str
}

func (efm *EnhancedFieldMapper) padRight(str string, length int, padChar string) string {
	if len(str) >= length {
		return str
	}
	padCount := length - len(str)
	return str + strings.Repeat(padChar, padCount)
}

func (efm *EnhancedFieldMapper) substring(str string, start, end int) string {
	if start < 0 {
		start = 0
	}
	if end > len(str) {
		end = len(str)
	}
	if start >= end {
		return ""
	}
	return str[start:end]
}

// validateConfiguration 验证配置
func (efm *EnhancedFieldMapper) validateConfiguration() error {
	// 验证字段映射
	for i, mapping := range efm.fieldMappings {
		if mapping.SourceField == "" && mapping.Expression == "" {
			return fmt.Errorf("field mapping %d: source_field or expression is required", i)
		}
		if mapping.TargetField == "" {
			return fmt.Errorf("field mapping %d: target_field is required", i)
		}
	}

	// 验证添加字段
	for i, field := range efm.addedFields {
		if field.TargetField == "" {
			return fmt.Errorf("added field %d: target_field is required", i)
		}
		if field.Expression == "" && field.DefaultValue == nil {
			return fmt.Errorf("added field %d: expression or default_value is required", i)
		}
	}

	// 验证验证规则
	for i, rule := range efm.validationRules {
		if rule.Field == "" {
			return fmt.Errorf("validation rule %d: field is required", i)
		}
		// 验证正则表达式
		if rule.Pattern != "" {
			if _, err := regexp.Compile(rule.Pattern); err != nil {
				return fmt.Errorf("validation rule %d: invalid regex pattern: %w", i, err)
			}
		}
	}

	// 验证转换规则
	for i, rule := range efm.transformRules {
		if rule.Field == "" {
			return fmt.Errorf("transform rule %d: field is required", i)
		}
		if rule.Operation == "" {
			return fmt.Errorf("transform rule %d: operation is required", i)
		}
		// 验证操作类型
		if !efm.isValidOperation(rule.Operation) {
			return fmt.Errorf("transform rule %d: unsupported operation: %s", i, rule.Operation)
		}
	}

	return nil
}

// isValidOperation 检查操作是否有效
func (efm *EnhancedFieldMapper) isValidOperation(operation string) bool {
	validOps := []string{
		OpTrim, OpUpperCase, OpLowerCase, OpTitleCase,
		OpPadLeft, OpPadRight, OpSubstring, OpReplace, OpRegexReplace,
		OpConcat, OpSplit, OpJoin, OpFormatNumber, OpFormatDate,
		OpTypeConvert, OpCustomFunction,
	}

	for _, validOp := range validOps {
		if operation == validOp {
			return true
		}
	}
	return false
}

// EvaluateExpressionToBoolean 评估表达式为布尔值（重用增强转换步骤的方法）
func (efm *EnhancedFieldMapper) EvaluateExpressionToBoolean(expression string, row step.Row) (bool, error) {
	variables := efm.rowToVariables(row)
	result, err := efm.engine.EvaluateToBoolean(expression, variables)
	if err != nil {
		return false, fmt.Errorf("expression evaluation error: %w", err)
	}
	efm.metrics.ExpressionCalls++
	return result, nil
}

// GetMappingStatistics 获取映射统计信息
func (efm *EnhancedFieldMapper) GetMappingStatistics() map[string]interface{} {
	efm.mutex.RLock()
	defer efm.mutex.RUnlock()

	return map[string]interface{}{
		"field_mappings_count":   len(efm.fieldMappings),
		"rename_mappings_count":  len(efm.renameMappings),
		"removed_fields_count":   len(efm.removedFields),
		"added_fields_count":     len(efm.addedFields),
		"validation_rules_count": len(efm.validationRules),
		"transform_rules_count":  len(efm.transformRules),
		"preserve_unmapped":      efm.preserveUnmapped,
		"strict_mode":            efm.strictMode,
	}
}

// GetType 返回步骤类型
func (efm *EnhancedFieldMapper) GetType() meta.StepType {
	return meta.StepTypeFieldMapper
}
