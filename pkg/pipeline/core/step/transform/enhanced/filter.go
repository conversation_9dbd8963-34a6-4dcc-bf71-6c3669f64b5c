package enhanced

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// EnhancedFilter 增强过滤器步骤
type EnhancedFilter struct {
	*EnhancedTransformStep
	conditions      []FilterCondition
	logicalOperator string // AND, OR
	conditionGroups []ConditionGroup
	compiledFilters map[string]any // 编译后的过滤表达式缓存
	errorHandling   ErrorHandlingMode
	passFailCounts  map[string]int64 // 通过/失败统计
}

// FilterCondition 过滤条件
type FilterCondition struct {
	Field         string `json:"field"`          // 字段名
	Operator      string `json:"operator"`       // 操作符: eq, ne, gt, ge, lt, le, in, not_in, contains, starts_with, ends_with, regex, is_null, is_not_null
	Value         any    `json:"value"`          // 比较值
	Expression    string `json:"expression"`     // 表达式模式
	CaseSensitive bool   `json:"case_sensitive"` // 大小写敏感
	Negate        bool   `json:"negate"`         // 否定条件
	Description   string `json:"description"`    // 条件描述
}

// ConditionGroup 条件组（支持复杂逻辑组合）
type ConditionGroup struct {
	Conditions []FilterCondition `json:"conditions"`
	Operator   string            `json:"operator"` // AND, OR
	Negate     bool              `json:"negate"`   // 否定整个组
}

// ErrorHandlingMode 错误处理模式
type ErrorHandlingMode string

const (
	ErrorModeSkip    ErrorHandlingMode = "skip"    // 跳过错误行
	ErrorModeDefault ErrorHandlingMode = "default" // 使用默认值
	ErrorModeError   ErrorHandlingMode = "error"   // 抛出错误
	ErrorModePass    ErrorHandlingMode = "pass"    // 让错误行通过
	ErrorModeReject  ErrorHandlingMode = "reject"  // 拒绝错误行
)

// NewEnhancedFilter 创建增强过滤器
func NewEnhancedFilter(stepMeta meta.StepMeta) *EnhancedFilter {
	return &EnhancedFilter{
		EnhancedTransformStep: NewEnhancedTransformStep(stepMeta),
		conditions:            []FilterCondition{},
		logicalOperator:       "AND",
		conditionGroups:       []ConditionGroup{},
		compiledFilters:       make(map[string]any),
		errorHandling:         ErrorModeSkip,
		passFailCounts:        make(map[string]int64),
	}
}

// Initialize 初始化过滤器
func (ef *EnhancedFilter) Initialize() error {
	if err := ef.BaseStep.Initialize(); err != nil {
		return err
	}

	return ef.parseFilterConfiguration()
}

// Execute 执行过滤
func (ef *EnhancedFilter) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	ef.metrics.StartTime = getCurrentNano()

	return ef.ExecuteWithContext(ctx, input, func(row step.Row) (step.Row, error) {
		// 评估过滤条件
		shouldPass, err := ef.evaluateRow(row)
		if err != nil {
			return ef.handleFilterError(row, err)
		}

		if shouldPass {
			ef.passFailCounts["pass"]++
			return row, nil
		}

		ef.passFailCounts["fail"]++
		return nil, nil // 不通过过滤条件，返回 nil 表示过滤掉
	})
}

// parseFilterConfiguration 解析过滤配置
func (ef *EnhancedFilter) parseFilterConfiguration() error {
	// 解析简单条件列表
	if conditionsConfig, exists := ef.GetConfigSlice("conditions"); exists {
		for i, conditionConfig := range conditionsConfig {
			condition, err := ef.parseCondition(conditionConfig, i)
			if err != nil {
				return err
			}
			ef.conditions = append(ef.conditions, condition)
		}
	}

	// 解析条件组
	if groupsConfig, exists := ef.GetConfigSlice("condition_groups"); exists {
		for i, groupConfig := range groupsConfig {
			group, err := ef.parseConditionGroup(groupConfig, i)
			if err != nil {
				return err
			}
			ef.conditionGroups = append(ef.conditionGroups, group)
		}
	}

	// 解析全局逻辑操作符
	if operator, exists := ef.GetConfigString("logical_operator"); exists {
		ef.logicalOperator = strings.ToUpper(operator)
	}

	// 解析错误处理模式
	if errorMode, exists := ef.GetConfigString("error_handling"); exists {
		ef.errorHandling = ErrorHandlingMode(errorMode)
	}

	return ef.validateConfiguration()
}

// parseCondition 解析单个条件
func (ef *EnhancedFilter) parseCondition(conditionConfig interface{}, index int) (FilterCondition, error) {
	conditionMap, ok := conditionConfig.(map[string]interface{})
	if !ok {
		return FilterCondition{}, fmt.Errorf("condition %d: invalid format", index)
	}

	condition := FilterCondition{}

	// 字段名
	if field, ok := conditionMap["field"].(string); ok {
		condition.Field = field
	} else if expression, ok := conditionMap["expression"].(string); ok {
		condition.Expression = expression
	} else {
		return FilterCondition{}, fmt.Errorf("condition %d: field or expression is required", index)
	}

	// 操作符
	if operator, ok := conditionMap["operator"].(string); ok {
		condition.Operator = operator
	} else if condition.Expression == "" {
		return FilterCondition{}, fmt.Errorf("condition %d: operator is required when not using expression", index)
	}

	// 比较值
	condition.Value = conditionMap["value"]

	// 大小写敏感
	if caseSensitive, ok := conditionMap["case_sensitive"].(bool); ok {
		condition.CaseSensitive = caseSensitive
	} else {
		condition.CaseSensitive = true
	}

	// 否定条件
	if negate, ok := conditionMap["negate"].(bool); ok {
		condition.Negate = negate
	}

	// 描述
	if description, ok := conditionMap["description"].(string); ok {
		condition.Description = description
	}

	return condition, nil
}

// parseConditionGroup 解析条件组
func (ef *EnhancedFilter) parseConditionGroup(groupConfig interface{}, index int) (ConditionGroup, error) {
	groupMap, ok := groupConfig.(map[string]interface{})
	if !ok {
		return ConditionGroup{}, fmt.Errorf("condition group %d: invalid format", index)
	}

	group := ConditionGroup{}

	// 组内操作符
	if operator, ok := groupMap["operator"].(string); ok {
		group.Operator = strings.ToUpper(operator)
	} else {
		group.Operator = "AND"
	}

	// 否定组
	if negate, ok := groupMap["negate"].(bool); ok {
		group.Negate = negate
	}

	// 解析组内条件
	if conditionsConfig, ok := groupMap["conditions"].([]interface{}); ok {
		for i, conditionConfig := range conditionsConfig {
			condition, err := ef.parseCondition(conditionConfig, i)
			if err != nil {
				return ConditionGroup{}, fmt.Errorf("condition group %d: %w", index, err)
			}
			group.Conditions = append(group.Conditions, condition)
		}
	}

	return group, nil
}

// evaluateRow 评估行是否满足过滤条件
func (ef *EnhancedFilter) evaluateRow(row step.Row) (bool, error) {
	// 如果有表达式条件，使用表达式引擎评估
	if len(ef.conditions) > 0 || len(ef.conditionGroups) > 0 {
		return ef.evaluateConditions(row)
	}

	// 检查是否有配置的表达式
	if expression, exists := ef.GetConfigString("expression"); exists {
		return ef.EvaluateExpressionToBoolean(expression, row)
	}

	// 默认通过
	return true, nil
}

// evaluateConditions 评估所有条件
func (ef *EnhancedFilter) evaluateConditions(row step.Row) (bool, error) {
	results := []bool{}

	// 评估简单条件
	for _, condition := range ef.conditions {
		result, err := ef.evaluateCondition(row, condition)
		if err != nil {
			return false, err
		}
		results = append(results, result)
	}

	// 评估条件组
	for _, group := range ef.conditionGroups {
		result, err := ef.evaluateConditionGroup(row, group)
		if err != nil {
			return false, err
		}
		results = append(results, result)
	}

	// 根据逻辑操作符组合结果
	if len(results) == 0 {
		return true, nil
	}

	return ef.combineResults(results, ef.logicalOperator), nil
}

// evaluateCondition 评估单个条件
func (ef *EnhancedFilter) evaluateCondition(row step.Row, condition FilterCondition) (bool, error) {
	var result bool
	var err error

	if condition.Expression != "" {
		// 使用表达式引擎
		result, err = ef.EvaluateExpressionToBoolean(condition.Expression, row)
	} else {
		// 使用操作符比较
		result, err = ef.evaluateOperatorCondition(row, condition)
	}

	if err != nil {
		return false, err
	}

	// 应用否定
	if condition.Negate {
		result = !result
	}

	return result, nil
}

// evaluateOperatorCondition 评估操作符条件
func (ef *EnhancedFilter) evaluateOperatorCondition(row step.Row, condition FilterCondition) (bool, error) {
	fieldValue := row.Get(condition.Field)

	switch condition.Operator {
	case "eq", "=", "==":
		return ef.compareEqual(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "ne", "!=", "<>":
		return !ef.compareEqual(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "gt", ">":
		return ef.compareGreater(fieldValue, condition.Value), nil
	case "ge", ">=":
		return ef.compareGreaterEqual(fieldValue, condition.Value), nil
	case "lt", "<":
		return ef.compareLess(fieldValue, condition.Value), nil
	case "le", "<=":
		return ef.compareLessEqual(fieldValue, condition.Value), nil
	case "in":
		return ef.compareIn(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "not_in":
		return !ef.compareIn(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "contains":
		return ef.compareContains(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "starts_with":
		return ef.compareStartsWith(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "ends_with":
		return ef.compareEndsWith(fieldValue, condition.Value, condition.CaseSensitive), nil
	case "regex":
		return ef.compareRegex(fieldValue, condition.Value)
	case "is_null":
		return fieldValue == nil, nil
	case "is_not_null":
		return fieldValue != nil, nil
	default:
		return false, fmt.Errorf("unsupported operator: %s", condition.Operator)
	}
}

// evaluateConditionGroup 评估条件组
func (ef *EnhancedFilter) evaluateConditionGroup(row step.Row, group ConditionGroup) (bool, error) {
	if len(group.Conditions) == 0 {
		return true, nil
	}

	results := []bool{}
	for _, condition := range group.Conditions {
		result, err := ef.evaluateCondition(row, condition)
		if err != nil {
			return false, err
		}
		results = append(results, result)
	}

	// 组合组内结果
	result := ef.combineResults(results, group.Operator)

	// 应用组否定
	if group.Negate {
		result = !result
	}

	return result, nil
}

// combineResults 根据逻辑操作符组合结果
func (ef *EnhancedFilter) combineResults(results []bool, operator string) bool {
	if len(results) == 0 {
		return true
	}

	switch operator {
	case "AND":
		for _, result := range results {
			if !result {
				return false
			}
		}
		return true
	case "OR":
		for _, result := range results {
			if result {
				return true
			}
		}
		return false
	default:
		// 默认为 AND
		for _, result := range results {
			if !result {
				return false
			}
		}
		return true
	}
}

// 比较函数
func (ef *EnhancedFilter) compareEqual(v1, v2 interface{}, caseSensitive bool) bool {
	if v1 == nil && v2 == nil {
		return true
	}
	if v1 == nil || v2 == nil {
		return false
	}

	str1 := fmt.Sprintf("%v", v1)
	str2 := fmt.Sprintf("%v", v2)

	if !caseSensitive {
		str1 = strings.ToLower(str1)
		str2 = strings.ToLower(str2)
	}

	return str1 == str2
}

func (ef *EnhancedFilter) compareGreater(v1, v2 interface{}) bool {
	f1, ok1 := ef.toFloat64(v1)
	f2, ok2 := ef.toFloat64(v2)
	if ok1 && ok2 {
		return f1 > f2
	}

	str1 := fmt.Sprintf("%v", v1)
	str2 := fmt.Sprintf("%v", v2)
	return str1 > str2
}

func (ef *EnhancedFilter) compareGreaterEqual(v1, v2 interface{}) bool {
	f1, ok1 := ef.toFloat64(v1)
	f2, ok2 := ef.toFloat64(v2)
	if ok1 && ok2 {
		return f1 >= f2
	}

	str1 := fmt.Sprintf("%v", v1)
	str2 := fmt.Sprintf("%v", v2)
	return str1 >= str2
}

func (ef *EnhancedFilter) compareLess(v1, v2 interface{}) bool {
	f1, ok1 := ef.toFloat64(v1)
	f2, ok2 := ef.toFloat64(v2)
	if ok1 && ok2 {
		return f1 < f2
	}

	str1 := fmt.Sprintf("%v", v1)
	str2 := fmt.Sprintf("%v", v2)
	return str1 < str2
}

func (ef *EnhancedFilter) compareLessEqual(v1, v2 interface{}) bool {
	f1, ok1 := ef.toFloat64(v1)
	f2, ok2 := ef.toFloat64(v2)
	if ok1 && ok2 {
		return f1 <= f2
	}

	str1 := fmt.Sprintf("%v", v1)
	str2 := fmt.Sprintf("%v", v2)
	return str1 <= str2
}

func (ef *EnhancedFilter) compareIn(value interface{}, listValue interface{}, caseSensitive bool) bool {
	list, ok := listValue.([]interface{})
	if !ok {
		// 尝试将单个值转换为列表
		list = []interface{}{listValue}
	}

	for _, item := range list {
		if ef.compareEqual(value, item, caseSensitive) {
			return true
		}
	}
	return false
}

func (ef *EnhancedFilter) compareContains(value, pattern interface{}, caseSensitive bool) bool {
	str1 := fmt.Sprintf("%v", value)
	str2 := fmt.Sprintf("%v", pattern)

	if !caseSensitive {
		str1 = strings.ToLower(str1)
		str2 = strings.ToLower(str2)
	}

	return strings.Contains(str1, str2)
}

func (ef *EnhancedFilter) compareStartsWith(value, pattern interface{}, caseSensitive bool) bool {
	str1 := fmt.Sprintf("%v", value)
	str2 := fmt.Sprintf("%v", pattern)

	if !caseSensitive {
		str1 = strings.ToLower(str1)
		str2 = strings.ToLower(str2)
	}

	return strings.HasPrefix(str1, str2)
}

func (ef *EnhancedFilter) compareEndsWith(value, pattern interface{}, caseSensitive bool) bool {
	str1 := fmt.Sprintf("%v", value)
	str2 := fmt.Sprintf("%v", pattern)

	if !caseSensitive {
		str1 = strings.ToLower(str1)
		str2 = strings.ToLower(str2)
	}

	return strings.HasSuffix(str1, str2)
}

func (ef *EnhancedFilter) compareRegex(value, pattern interface{}) (bool, error) {
	// 这里可以使用 regexp 包进行正则匹配
	// 为了简化，暂时使用包含比较
	return ef.compareContains(value, pattern, true), nil
}

// toFloat64 转换为 float64
func (ef *EnhancedFilter) toFloat64(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case float64:
		return v, true
	case float32:
		return float64(v), true
	case int:
		return float64(v), true
	case int32:
		return float64(v), true
	case int64:
		return float64(v), true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		}
	}
	return 0, false
}

// handleFilterError 处理过滤错误
func (ef *EnhancedFilter) handleFilterError(row step.Row, err error) (step.Row, error) {
	ef.recordError(fmt.Errorf("filter evaluation error: %w", err))

	switch ef.errorHandling {
	case ErrorModeSkip:
		return nil, nil // 跳过该行
	case ErrorModePass:
		return row, nil // 让行通过
	case ErrorModeReject:
		return nil, nil // 拒绝该行
	case ErrorModeError:
		return nil, err // 抛出错误
	default:
		return nil, nil // 默认跳过
	}
}

// validateConfiguration 验证配置
func (ef *EnhancedFilter) validateConfiguration() error {
	// 验证逻辑操作符
	if ef.logicalOperator != "AND" && ef.logicalOperator != "OR" {
		return fmt.Errorf("invalid logical operator: %s", ef.logicalOperator)
	}

	// 验证条件
	for i, condition := range ef.conditions {
		if condition.Field == "" && condition.Expression == "" {
			return fmt.Errorf("condition %d: field or expression is required", i)
		}
		if condition.Field != "" && condition.Operator == "" {
			return fmt.Errorf("condition %d: operator is required when using field", i)
		}
	}

	// 验证条件组
	for i, group := range ef.conditionGroups {
		if group.Operator != "AND" && group.Operator != "OR" {
			return fmt.Errorf("condition group %d: invalid operator %s", i, group.Operator)
		}
		if len(group.Conditions) == 0 {
			return fmt.Errorf("condition group %d: at least one condition is required", i)
		}
	}

	return nil
}

// EvaluateExpressionToBoolean 评估表达式为布尔值
func (ef *EnhancedFilter) EvaluateExpressionToBoolean(expression string, row step.Row) (bool, error) {
	// 转换行数据为变量映射
	variables := ef.rowToVariables(row)

	// 使用表达式引擎评估
	result, err := ef.engine.EvaluateToBoolean(expression, variables)
	if err != nil {
		return false, fmt.Errorf("expression evaluation error: %w", err)
	}

	ef.metrics.ExpressionCalls++
	return result, nil
}

// GetFilterStatistics 获取过滤统计信息
func (ef *EnhancedFilter) GetFilterStatistics() map[string]int64 {
	ef.mutex.RLock()
	defer ef.mutex.RUnlock()

	stats := make(map[string]int64)
	for k, v := range ef.passFailCounts {
		stats[k] = v
	}
	return stats
}

// GetType 返回步骤类型
func (ef *EnhancedFilter) GetType() meta.StepType {
	return meta.StepTypeEnhancedFilter
}

// getCurrentNano 获取当前纳秒时间戳
func getCurrentNano() int64 {
	return time.Now().UnixNano()
}
