package enhanced

import (
	"fmt"
	"math"
	"testing"
	"time"
)

// TestBloomFilterOptimization 测试布隆过滤器优化功能
func TestBloomFilterOptimization(t *testing.T) {
	// 测试基于期望假阳性率的自动参数计算
	testCases := []struct {
		name              string
		expectedElements  int64
		falsePositiveRate float64
		expectedSizeRange [2]int64 // 期望的位数组大小范围
		expectedHashRange [2]int   // 期望的哈希函数数量范围
	}{
		{
			name:              "小规模数据集",
			expectedElements:  10000,
			falsePositiveRate: 0.01,
			expectedSizeRange: [2]int64{90000, 100000}, // 约95KB
			expectedHashRange: [2]int{6, 8},
		},
		{
			name:              "中等规模数据集",
			expectedElements:  1000000,
			falsePositiveRate: 0.01,
			expectedSizeRange: [2]int64{9500000, 10000000}, // 约9.5MB
			expectedHashRange: [2]int{6, 8},
		},
		{
			name:              "低假阳性率要求",
			expectedElements:  100000,
			falsePositiveRate: 0.001,
			expectedSizeRange: [2]int64{1400000, 1500000}, // 约1.4MB
			expectedHashRange: [2]int{9, 11},
		},
		{
			name:              "高假阳性率容忍",
			expectedElements:  100000,
			falsePositiveRate: 0.1,
			expectedSizeRange: [2]int64{450000, 500000}, // 约480KB
			expectedHashRange: [2]int{3, 4},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := BloomFilterConfig{
				ExpectedElements:  tc.expectedElements,
				FalsePositiveRate: tc.falsePositiveRate,
			}

			bf := NewBloomFilterWithConfig(config)
			stats := bf.GetStats()

			// 验证位数组大小
			if int64(stats.Size) < tc.expectedSizeRange[0] || int64(stats.Size) > tc.expectedSizeRange[1] {
				t.Errorf("位数组大小 %d 不在期望范围 [%d, %d] 内",
					stats.Size, tc.expectedSizeRange[0], tc.expectedSizeRange[1])
			}

			// 验证哈希函数数量
			if stats.HashFunctions < tc.expectedHashRange[0] || stats.HashFunctions > tc.expectedHashRange[1] {
				t.Errorf("哈希函数数量 %d 不在期望范围 [%d, %d] 内",
					stats.HashFunctions, tc.expectedHashRange[0], tc.expectedHashRange[1])
			}

			// 验证目标假阳性率
			if math.Abs(stats.TargetFPRate-tc.falsePositiveRate) > 0.0001 {
				t.Errorf("目标假阳性率 %.6f 与期望值 %.6f 不匹配",
					stats.TargetFPRate, tc.falsePositiveRate)
			}

			t.Logf("配置: %s", tc.name)
			t.Logf("  位数组大小: %d (%.2f KB)", stats.Size, float64(stats.Size)/8/1024)
			t.Logf("  哈希函数数量: %d", stats.HashFunctions)
			t.Logf("  目标假阳性率: %.6f", stats.TargetFPRate)
			t.Logf("  内存使用: %.2f KB", float64(stats.MemoryUsageBytes)/1024)
		})
	}
}

// TestBloomFilterAccuracy 测试布隆过滤器准确性
func TestBloomFilterAccuracy(t *testing.T) {
	config := BloomFilterConfig{
		ExpectedElements:  10000,
		FalsePositiveRate: 0.01,
	}

	bf := NewBloomFilterWithConfig(config)

	// 添加已知元素
	knownElements := make([]string, 1000)
	for i := 0; i < 1000; i++ {
		element := fmt.Sprintf("element_%d", i)
		knownElements[i] = element
		bf.Add(element)
	}

	// 测试真阳性率（应该是100%）
	truePositives := 0
	for _, element := range knownElements {
		if bf.Contains(element) {
			truePositives++
		}
	}

	truePositiveRate := float64(truePositives) / float64(len(knownElements))
	if truePositiveRate < 1.0 {
		t.Errorf("真阳性率 %.4f 应该为 1.0", truePositiveRate)
	}

	// 测试假阳性率
	falsePositives := 0
	testElements := 10000
	for i := 1000; i < 1000+testElements; i++ {
		element := fmt.Sprintf("element_%d", i)
		if bf.Contains(element) {
			falsePositives++
		}
	}

	actualFPRate := float64(falsePositives) / float64(testElements)
	stats := bf.GetStats()

	t.Logf("实际假阳性率: %.6f", actualFPRate)
	t.Logf("理论假阳性率: %.6f", stats.ActualFPRate)
	t.Logf("目标假阳性率: %.6f", stats.TargetFPRate)

	// 假阳性率应该在合理范围内（目标值的3倍以内）
	if actualFPRate > stats.TargetFPRate*3 {
		t.Errorf("实际假阳性率 %.6f 过高，超过目标值 %.6f 的3倍",
			actualFPRate, stats.TargetFPRate)
	}
}

// TestBloomFilterPerformance 测试布隆过滤器性能
func TestBloomFilterPerformance(t *testing.T) {
	config := BloomFilterConfig{
		ExpectedElements:  100000,
		FalsePositiveRate: 0.01,
	}

	bf := NewBloomFilterWithConfig(config)

	// 测试添加性能
	addStart := time.Now()
	for i := 0; i < 50000; i++ {
		bf.Add(fmt.Sprintf("element_%d", i))
	}
	addDuration := time.Since(addStart)

	// 测试查询性能
	queryStart := time.Now()
	for i := 0; i < 100000; i++ {
		bf.Contains(fmt.Sprintf("element_%d", i))
	}
	queryDuration := time.Since(queryStart)

	stats := bf.GetStats()

	t.Logf("性能测试结果:")
	t.Logf("  添加50K元素耗时: %v (%.2f ns/op)", addDuration, float64(addDuration.Nanoseconds())/50000)
	t.Logf("  查询100K次耗时: %v (%.2f ns/op)", queryDuration, float64(queryDuration.Nanoseconds())/100000)
	t.Logf("  负载因子: %.4f", stats.LoadFactor)
	t.Logf("  估计元素数量: %d", bf.EstimateElementCount())
	t.Logf("  实际假阳性率: %.6f", stats.ActualFPRate)

	// 性能要求：每次操作应在1微秒内完成
	avgAddTime := float64(addDuration.Nanoseconds()) / 50000
	avgQueryTime := float64(queryDuration.Nanoseconds()) / 100000

	if avgAddTime > 1000 { // 1微秒 = 1000纳秒
		t.Errorf("添加操作平均耗时 %.2f ns 超过1微秒", avgAddTime)
	}

	if avgQueryTime > 1000 {
		t.Errorf("查询操作平均耗时 %.2f ns 超过1微秒", avgQueryTime)
	}
}

// TestBloomFilterConcurrency 测试布隆过滤器并发安全
func TestBloomFilterConcurrency(t *testing.T) {
	config := BloomFilterConfig{
		ExpectedElements:  10000,
		FalsePositiveRate: 0.01,
	}

	bf := NewBloomFilterWithConfig(config)

	// 并发添加和查询
	done := make(chan bool, 4)

	// 两个 goroutine 添加元素
	go func() {
		for i := 0; i < 1000; i++ {
			bf.Add(fmt.Sprintf("add1_%d", i))
		}
		done <- true
	}()

	go func() {
		for i := 0; i < 1000; i++ {
			bf.Add(fmt.Sprintf("add2_%d", i))
		}
		done <- true
	}()

	// 两个 goroutine 查询元素
	go func() {
		for i := 0; i < 1000; i++ {
			bf.Contains(fmt.Sprintf("query1_%d", i))
		}
		done <- true
	}()

	go func() {
		for i := 0; i < 1000; i++ {
			bf.Contains(fmt.Sprintf("query2_%d", i))
		}
		done <- true
	}()

	// 等待所有 goroutine 完成
	for i := 0; i < 4; i++ {
		<-done
	}

	stats := bf.GetStats()
	t.Logf("并发测试完成:")
	t.Logf("  元素数量: %d", stats.ElementCount)
	t.Logf("  负载因子: %.4f", stats.LoadFactor)
	t.Logf("  实际假阳性率: %.6f", stats.ActualFPRate)

	// 验证元素数量
	if stats.ElementCount != 2000 {
		t.Errorf("元素数量 %d 不等于期望值 2000", stats.ElementCount)
	}
}

// TestBloomFilterMemoryLimit 测试内存限制功能
func TestBloomFilterMemoryLimit(t *testing.T) {
	config := BloomFilterConfig{
		ExpectedElements:  1000000,
		FalsePositiveRate: 0.001,
		MaxMemoryMB:       1, // 限制为1MB
	}

	bf := NewBloomFilterWithConfig(config)
	stats := bf.GetStats()

	maxMemoryBytes := int64(1 * 1024 * 1024) // 1MB
	if stats.MemoryUsageBytes > maxMemoryBytes {
		t.Errorf("内存使用 %d 字节超过限制 %d 字节",
			stats.MemoryUsageBytes, maxMemoryBytes)
	}

	t.Logf("内存限制测试:")
	t.Logf("  位数组大小: %d", stats.Size)
	t.Logf("  内存使用: %.2f KB", float64(stats.MemoryUsageBytes)/1024)
	t.Logf("  哈希函数数量: %d", stats.HashFunctions)
	t.Logf("  目标假阳性率: %.6f", stats.TargetFPRate)
}

// TestBloomFilterOverload 测试过载检测
func TestBloomFilterOverload(t *testing.T) {
	config := BloomFilterConfig{
		ExpectedElements:  1000,
		FalsePositiveRate: 0.01,
	}

	bf := NewBloomFilterWithConfig(config)

	// 添加预期数量的元素
	for i := 0; i < 1000; i++ {
		bf.Add(fmt.Sprintf("element_%d", i))
	}

	if bf.IsOverloaded() {
		t.Errorf("布隆过滤器在预期负载下不应该被标记为过载")
	}

	// 添加超过预期的元素（5倍）
	for i := 1000; i < 5000; i++ {
		bf.Add(fmt.Sprintf("element_%d", i))
	}

	stats := bf.GetStats()
	t.Logf("过载测试:")
	t.Logf("  元素数量: %d", stats.ElementCount)
	t.Logf("  负载因子: %.4f", stats.LoadFactor)
	t.Logf("  实际假阳性率: %.6f", stats.ActualFPRate)
	t.Logf("  目标假阳性率: %.6f", stats.TargetFPRate)
	t.Logf("  是否过载: %v", bf.IsOverloaded())

	// 在大量过载的情况下，应该被检测为过载
	if !bf.IsOverloaded() {
		t.Errorf("布隆过滤器在严重过载下应该被标记为过载")
	}
}

// TestMurmurHashQuality 测试MurmurHash算法质量
func TestMurmurHashQuality(t *testing.T) {
	bf := &BloomFilter{
		size:     1000000,
		hashFunc: 3,
	}

	// 测试哈希分布均匀性 - 增加样本数量，减少桶数量
	hashCounts := make(map[uint32]int)

	// 生成更多测试数据以获得更好的统计意义
	sampleCount := 0
	for i := 0; i < 1000; i++ {
		testStr := fmt.Sprintf("test_string_%d", i)
		for seed := uint32(0); seed < 10; seed++ {
			hash := bf.murmurHash(testStr, seed)
			bucket := hash % 1000 // 分成1000个桶，确保有足够样本
			hashCounts[bucket]++
			sampleCount++
		}
	}

	// 计算分布的标准差
	expectedPerBucket := float64(sampleCount) / 1000
	variance := 0.0

	for i := uint32(0); i < 1000; i++ {
		count := float64(hashCounts[i])
		diff := count - expectedPerBucket
		variance += diff * diff
	}

	variance /= 1000
	stdDev := math.Sqrt(variance)

	t.Logf("哈希分布测试:")
	t.Logf("  总样本数: %d", sampleCount)
	t.Logf("  期望每桶: %.2f", expectedPerBucket)
	t.Logf("  标准差: %.4f", stdDev)
	t.Logf("  变异系数: %.4f", stdDev/expectedPerBucket)

	// 良好的哈希函数标准差应该相对较小，对于泊松分布，0.35是合理的阈值
	if stdDev/expectedPerBucket > 0.35 {
		t.Errorf("哈希分布不够均匀，变异系数 %.4f 过高", stdDev/expectedPerBucket)
	}
}

// BenchmarkBloomFilterOperations 性能基准测试
func BenchmarkBloomFilterOperations(b *testing.B) {
	config := BloomFilterConfig{
		ExpectedElements:  1000000,
		FalsePositiveRate: 0.01,
	}

	bf := NewBloomFilterWithConfig(config)

	// 预填充一些数据
	for i := 0; i < 50000; i++ {
		bf.Add(fmt.Sprintf("element_%d", i))
	}

	b.Run("Add", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			bf.Add(fmt.Sprintf("bench_add_%d", i))
		}
	})

	b.Run("Contains", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			bf.Contains(fmt.Sprintf("bench_query_%d", i%100000))
		}
	})

	b.Run("MurmurHash", func(b *testing.B) {
		testString := "benchmark_test_string"
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			bf.murmurHash(testString, uint32(i%100))
		}
	})
}
