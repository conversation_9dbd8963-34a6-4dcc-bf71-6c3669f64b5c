package enhanced

import (
	"context"
	"fmt"
	"strconv"
	"sync"

	"admin/pkg/expr"
	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// ExpressionEngine 表达式引擎接口的包装器
type ExpressionEngine interface {
	Evaluate(expression string, variables map[string]any) (any, error)
	EvaluateToBoolean(expression string, variables map[string]any) (bool, error)
	Validate(expression string) error
}

// EnhancedTransformStep 增强转换步骤的基类
type EnhancedTransformStep struct {
	*step.BaseStep
	engine  ExpressionEngine
	metrics *TransformMetrics
	mutex   sync.RWMutex
}

// TransformMetrics 转换指标
type TransformMetrics struct {
	TotalRows       int64            // 总处理行数
	ProcessedRows   int64            // 成功处理行数
	ErrorRows       int64            // 错误行数
	ExpressionCalls int64            // 表达式调用次数
	FieldStats      map[string]int64 // 字段操作统计
	StartTime       int64            // 开始时间（纳秒）
	LastUpdate      int64            // 最后更新时间（纳秒）
	Errors          []error          // 错误列表
	mutex           sync.RWMutex     // 读写锁
}

// NewEnhancedTransformStep 创建增强转换步骤
func NewEnhancedTransformStep(stepMeta meta.StepMeta) *EnhancedTransformStep {
	return &EnhancedTransformStep{
		BaseStep: step.NewBaseStep(stepMeta),
		engine:   expr.NewExprEngine(), // 使用默认表达式引擎
		metrics: &TransformMetrics{
			FieldStats: make(map[string]int64),
			Errors:     make([]error, 0),
		},
	}
}

// SetExpressionEngine 设置自定义表达式引擎
func (ets *EnhancedTransformStep) SetExpressionEngine(engine ExpressionEngine) {
	ets.mutex.Lock()
	defer ets.mutex.Unlock()
	ets.engine = engine
}

// EvaluateExpression 使用行数据作为上下文计算表达式
func (ets *EnhancedTransformStep) EvaluateExpression(expression string, row step.Row) (any, error) {
	variables := ets.rowToVariables(row)

	ets.metrics.mutex.Lock()
	ets.metrics.ExpressionCalls++
	ets.metrics.mutex.Unlock()

	result, err := ets.engine.Evaluate(expression, variables)
	if err != nil {
		ets.recordError(fmt.Errorf("expression evaluation failed: %w", err))
	}
	return result, err
}

// EvaluateCondition 计算布尔条件表达式
func (ets *EnhancedTransformStep) EvaluateCondition(condition string, row step.Row) (bool, error) {
	if condition == "" {
		return true, nil
	}

	variables := ets.rowToVariables(row)

	ets.metrics.mutex.Lock()
	ets.metrics.ExpressionCalls++
	ets.metrics.mutex.Unlock()

	result, err := ets.engine.EvaluateToBoolean(condition, variables)
	if err != nil {
		ets.recordError(fmt.Errorf("condition evaluation failed: %w", err))
	}
	return result, err
}

// ValidateExpression 验证表达式语法
func (ets *EnhancedTransformStep) ValidateExpression(expression string) error {
	return ets.engine.Validate(expression)
}

// rowToVariables 将行数据转换为表达式变量
func (ets *EnhancedTransformStep) rowToVariables(row step.Row) map[string]any {
	variables := make(map[string]any)

	// 添加所有字段作为变量
	for _, field := range row.Fields() {
		variables[field] = row.Get(field)
	}

	// 添加内置变量
	variables["__ROW_COUNT__"] = ets.metrics.ProcessedRows
	variables["__FIELD_COUNT__"] = len(row.Fields())

	return variables
}

// recordError 记录错误
func (ets *EnhancedTransformStep) recordError(err error) {
	ets.metrics.mutex.Lock()
	defer ets.metrics.mutex.Unlock()

	ets.metrics.ErrorRows++
	ets.metrics.Errors = append(ets.metrics.Errors, err)

	// 限制错误列表大小
	if len(ets.metrics.Errors) > 100 {
		ets.metrics.Errors = ets.metrics.Errors[1:]
	}
}

// updateMetrics 更新处理指标
func (ets *EnhancedTransformStep) updateMetrics() {
	ets.metrics.mutex.Lock()
	defer ets.metrics.mutex.Unlock()

	ets.metrics.ProcessedRows++
	ets.metrics.TotalRows++
}

// GetMetrics 获取转换指标
func (ets *EnhancedTransformStep) GetMetrics() *TransformMetrics {
	ets.metrics.mutex.RLock()
	defer ets.metrics.mutex.RUnlock()

	// 返回指标副本
	metricsCopy := &TransformMetrics{
		TotalRows:       ets.metrics.TotalRows,
		ProcessedRows:   ets.metrics.ProcessedRows,
		ErrorRows:       ets.metrics.ErrorRows,
		ExpressionCalls: ets.metrics.ExpressionCalls,
		StartTime:       ets.metrics.StartTime,
		LastUpdate:      ets.metrics.LastUpdate,
		FieldStats:      make(map[string]int64),
		Errors:          make([]error, len(ets.metrics.Errors)),
	}

	// 复制字段统计
	for k, v := range ets.metrics.FieldStats {
		metricsCopy.FieldStats[k] = v
	}

	// 复制错误列表
	copy(metricsCopy.Errors, ets.metrics.Errors)

	return metricsCopy
}

// ExecuteWithContext 带上下文的执行框架
func (ets *EnhancedTransformStep) ExecuteWithContext(ctx context.Context, input step.RowSet, processor func(step.Row) (step.Row, error)) (step.RowSet, error) {
	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	var outputRows []step.Row
	var schema []meta.FieldMeta

	// 获取输入schema
	if input.Schema() != nil {
		schema = input.Schema()
	}

	// 处理每一行数据
	for {
		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet(schema), ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			ets.recordError(fmt.Errorf("input error: %w", err))
			return step.NewEmptyRowSet(schema), err
		}
		if row == nil {
			break
		}

		// 使用处理器处理行
		processedRow, err := processor(row)
		if err != nil {
			ets.recordError(fmt.Errorf("row processing error: %w", err))
			continue // 跳过错误行
		}

		if processedRow != nil {
			outputRows = append(outputRows, processedRow)
		}

		ets.updateMetrics()
	}

	return step.NewMemoryRowSetFromRows(outputRows, schema), nil
}

// FieldMapping 字段映射配置
type FieldMapping struct {
	SourceField  string `json:"source_field"`  // 源字段名
	TargetField  string `json:"target_field"`  // 目标字段名
	Expression   string `json:"expression"`    // 转换表达式
	DataType     string `json:"data_type"`     // 目标数据类型
	DefaultValue any    `json:"default_value"` // 默认值
	Required     bool   `json:"required"`      // 是否必填
}

// ConditionalRule 条件规则
type ConditionalRule struct {
	Condition  string `json:"condition"`  // 条件表达式
	Action     string `json:"action"`     // 动作：transform, skip, error
	Expression string `json:"expression"` // 动作表达式
	Value      any    `json:"value"`      // 固定值
	Message    string `json:"message"`    // 错误消息
}

// TypeConverter 类型转换器
type TypeConverter struct {
	targetType string
}

// NewTypeConverter 创建类型转换器
func NewTypeConverter(targetType string) *TypeConverter {
	return &TypeConverter{targetType: targetType}
}

// Convert 转换值到目标类型
func (tc *TypeConverter) Convert(value any) (any, error) {
	if value == nil {
		return nil, nil
	}

	switch tc.targetType {
	case "string":
		return fmt.Sprintf("%v", value), nil
	case "int", "integer":
		switch v := value.(type) {
		case int:
			return int64(v), nil
		case int32:
			return int64(v), nil
		case int64:
			return v, nil
		case float32:
			return int64(v), nil
		case float64:
			return int64(v), nil
		case string:
			if parsed, err := strconv.ParseInt(v, 10, 64); err == nil {
				return parsed, nil
			}
			return nil, fmt.Errorf("cannot convert '%v' to integer", value)
		default:
			return nil, fmt.Errorf("cannot convert %T to integer", value)
		}
	case "float", "decimal", "number":
		switch v := value.(type) {
		case int:
			return float64(v), nil
		case int32:
			return float64(v), nil
		case int64:
			return float64(v), nil
		case float32:
			return float64(v), nil
		case float64:
			return v, nil
		case string:
			if parsed, err := strconv.ParseFloat(v, 64); err == nil {
				return parsed, nil
			}
			return nil, fmt.Errorf("cannot convert '%v' to float", value)
		default:
			return nil, fmt.Errorf("cannot convert %T to float", value)
		}
	case "bool", "boolean":
		switch v := value.(type) {
		case bool:
			return v, nil
		case string:
			switch v {
			case "true", "TRUE", "True", "1", "yes", "YES", "Yes":
				return true, nil
			case "false", "FALSE", "False", "0", "no", "NO", "No":
				return false, nil
			default:
				return false, fmt.Errorf("cannot convert '%v' to boolean", value)
			}
		case int, int32, int64:
			return v != 0, nil
		case float32, float64:
			return v != 0.0, nil
		default:
			return nil, fmt.Errorf("cannot convert %T to boolean", value)
		}
	default:
		return value, nil // 不支持的类型，返回原值
	}
}
