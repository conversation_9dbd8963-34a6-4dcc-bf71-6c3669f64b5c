package enhanced

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// EnhancedAggregator 增强聚合器
type EnhancedAggregator struct {
	*EnhancedTransformStep
	groupByFields    []string
	aggregations     []AggregationRule
	havingCondition  string
	sortRules        []SortRule
	limitCount       int
	offsetCount      int
	memoryOptimized  bool
	streamingMode    bool
	strictMode       bool
	aggregateResults map[string]*AggregateState
}

// AggregationRule 聚合规则
type AggregationRule struct {
	SourceField  string         `json:"source_field"`  // 源字段名
	TargetField  string         `json:"target_field"`  // 目标字段名
	Function     string         `json:"function"`      // 聚合函数
	Expression   string         `json:"expression"`    // 聚合表达式
	Condition    string         `json:"condition"`     // 聚合条件
	Parameters   map[string]any `json:"parameters"`    // 函数参数
	DataType     string         `json:"data_type"`     // 结果数据类型
	DefaultValue any            `json:"default_value"` // 默认值
	Description  string         `json:"description"`   // 规则描述
}

// SortRule 排序规则
type SortRule struct {
	Field     string `json:"field"`      // 排序字段
	Direction string `json:"direction"`  // ASC 或 DESC
	NullsLast bool   `json:"nulls_last"` // NULL 值排序位置
}

// AggregateState 聚合状态
type AggregateState struct {
	GroupKey    string
	GroupValues map[string]any
	Aggregates  map[string]*FunctionState
	RowCount    int64
	FirstSeen   time.Time
	LastSeen    time.Time
}

// FunctionState 函数状态
type FunctionState struct {
	Function   string
	Value      any
	Count      int64
	Sum        float64
	Min        any
	Max        any
	Values     []any           // 用于 COLLECT_LIST, MEDIAN 等
	Distinct   map[string]bool // 用于 COUNT_DISTINCT
	LastValue  any
	FirstValue any
}

// 聚合函数常量
const (
	FuncCount         = "count"
	FuncCountDistinct = "count_distinct"
	FuncSum           = "sum"
	FuncAvg           = "avg"
	FuncMin           = "min"
	FuncMax           = "max"
	FuncFirst         = "first"
	FuncLast          = "last"
	FuncConcat        = "concat"
	FuncCollectList   = "collect_list"
	FuncCollectSet    = "collect_set"
	FuncStdDev        = "stddev"
	FuncVariance      = "variance"
	FuncMedian        = "median"
	FuncPercentile    = "percentile"
	FuncCustom        = "custom"
)

// NewEnhancedAggregator 创建增强聚合器
func NewEnhancedAggregator(stepMeta meta.StepMeta) *EnhancedAggregator {
	return &EnhancedAggregator{
		EnhancedTransformStep: NewEnhancedTransformStep(stepMeta),
		groupByFields:         []string{},
		aggregations:          []AggregationRule{},
		sortRules:             []SortRule{},
		limitCount:            -1,
		offsetCount:           0,
		memoryOptimized:       false,
		streamingMode:         false,
		aggregateResults:      make(map[string]*AggregateState),
	}
}

// Initialize 初始化聚合器
func (ea *EnhancedAggregator) Initialize() error {
	if err := ea.BaseStep.Initialize(); err != nil {
		return err
	}

	return ea.parseAggregationConfiguration()
}

// Execute 执行聚合
func (ea *EnhancedAggregator) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	ea.metrics.StartTime = getCurrentNano()

	// 第一阶段：累积聚合状态
	if err := ea.accumulateAggregates(ctx, input); err != nil {
		return nil, err
	}

	// 第二阶段：生成聚合结果
	return ea.generateAggregateResults(ctx)
}

// parseAggregationConfiguration 解析聚合配置
func (ea *EnhancedAggregator) parseAggregationConfiguration() error {
	// 解析分组字段
	if groupByConfig, exists := ea.GetConfigSlice("group_by"); exists {
		for _, fieldConfig := range groupByConfig {
			if fieldName, ok := fieldConfig.(string); ok {
				ea.groupByFields = append(ea.groupByFields, fieldName)
			}
		}
	}

	// 解析聚合规则
	if aggregationsConfig, exists := ea.GetConfigSlice("aggregations"); exists {
		for i, aggConfig := range aggregationsConfig {
			aggregation, err := ea.parseAggregationRule(aggConfig, i)
			if err != nil {
				return err
			}
			ea.aggregations = append(ea.aggregations, aggregation)
		}
	}

	// 解析 HAVING 条件
	if havingCondition, exists := ea.GetConfigString("having"); exists {
		ea.havingCondition = havingCondition
	}

	// 解析排序规则
	if sortConfig, exists := ea.GetConfigSlice("order_by"); exists {
		for i, sortRuleConfig := range sortConfig {
			sortRule, err := ea.parseSortRule(sortRuleConfig, i)
			if err != nil {
				return err
			}
			ea.sortRules = append(ea.sortRules, sortRule)
		}
	}

	// 解析限制和偏移
	if limit, exists := ea.GetConfigInt("limit"); exists {
		ea.limitCount = int(limit)
	}

	if offset, exists := ea.GetConfigInt("offset"); exists {
		ea.offsetCount = int(offset)
	}

	// 解析优化选项
	if memOptimized, exists := ea.GetConfigBool("memory_optimized"); exists {
		ea.memoryOptimized = memOptimized
	}

	if streaming, exists := ea.GetConfigBool("streaming_mode"); exists {
		ea.streamingMode = streaming
	}

	return ea.validateConfiguration()
}

// parseAggregationRule 解析聚合规则
func (ea *EnhancedAggregator) parseAggregationRule(aggConfig interface{}, index int) (AggregationRule, error) {
	aggMap, ok := aggConfig.(map[string]interface{})
	if !ok {
		return AggregationRule{}, fmt.Errorf("aggregation %d: invalid format", index)
	}

	aggregation := AggregationRule{}

	// 源字段
	if sourceField, ok := aggMap["source_field"].(string); ok {
		aggregation.SourceField = sourceField
	}

	// 目标字段
	if targetField, ok := aggMap["target_field"].(string); ok {
		aggregation.TargetField = targetField
	} else {
		return AggregationRule{}, fmt.Errorf("aggregation %d: target_field is required", index)
	}

	// 聚合函数
	if function, ok := aggMap["function"].(string); ok {
		aggregation.Function = function
	} else if expression, ok := aggMap["expression"].(string); ok {
		aggregation.Expression = expression
		aggregation.Function = FuncCustom
	} else {
		return AggregationRule{}, fmt.Errorf("aggregation %d: function or expression is required", index)
	}

	// 聚合条件
	if condition, ok := aggMap["condition"].(string); ok {
		aggregation.Condition = condition
	}

	// 参数
	if parameters, ok := aggMap["parameters"].(map[string]interface{}); ok {
		aggregation.Parameters = parameters
	} else {
		aggregation.Parameters = make(map[string]any)
	}

	// 数据类型
	if dataType, ok := aggMap["data_type"].(string); ok {
		aggregation.DataType = dataType
	}

	// 默认值
	aggregation.DefaultValue = aggMap["default_value"]

	// 描述
	if description, ok := aggMap["description"].(string); ok {
		aggregation.Description = description
	}

	return aggregation, nil
}

// parseSortRule 解析排序规则
func (ea *EnhancedAggregator) parseSortRule(sortConfig interface{}, index int) (SortRule, error) {
	var sortRule SortRule

	// 如果是字符串，简单解析
	if fieldStr, ok := sortConfig.(string); ok {
		parts := strings.Fields(fieldStr)
		sortRule.Field = parts[0]
		if len(parts) > 1 {
			sortRule.Direction = strings.ToUpper(parts[1])
		} else {
			sortRule.Direction = "ASC"
		}
		return sortRule, nil
	}

	// 如果是映射，详细解析
	sortMap, ok := sortConfig.(map[string]interface{})
	if !ok {
		return SortRule{}, fmt.Errorf("sort rule %d: invalid format", index)
	}

	// 字段名
	if field, ok := sortMap["field"].(string); ok {
		sortRule.Field = field
	} else {
		return SortRule{}, fmt.Errorf("sort rule %d: field is required", index)
	}

	// 排序方向
	if direction, ok := sortMap["direction"].(string); ok {
		sortRule.Direction = strings.ToUpper(direction)
	} else {
		sortRule.Direction = "ASC"
	}

	// NULL 值处理
	if nullsLast, ok := sortMap["nulls_last"].(bool); ok {
		sortRule.NullsLast = nullsLast
	}

	return sortRule, nil
}

// accumulateAggregates 累积聚合状态
func (ea *EnhancedAggregator) accumulateAggregates(ctx context.Context, input step.RowSet) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil || row == nil {
			break
		}

		// 生成分组键
		groupKey := ea.generateGroupKey(row)

		// 获取或创建聚合状态
		state, exists := ea.aggregateResults[groupKey]
		if !exists {
			state = ea.createAggregateState(groupKey, row)
			ea.aggregateResults[groupKey] = state
		}

		// 更新聚合状态
		if err := ea.updateAggregateState(state, row); err != nil {
			ea.recordError(fmt.Errorf("aggregate update error: %w", err))
			if ea.strictMode {
				return err
			}
		}

		ea.updateMetrics()
	}

	return nil
}

// generateGroupKey 生成分组键
func (ea *EnhancedAggregator) generateGroupKey(row step.Row) string {
	if len(ea.groupByFields) == 0 {
		return "__all__" // 全局聚合
	}

	var keyParts []string
	for _, field := range ea.groupByFields {
		value := row.Get(field)
		keyParts = append(keyParts, fmt.Sprintf("%v", value))
	}

	return strings.Join(keyParts, "|")
}

// createAggregateState 创建聚合状态
func (ea *EnhancedAggregator) createAggregateState(groupKey string, row step.Row) *AggregateState {
	state := &AggregateState{
		GroupKey:    groupKey,
		GroupValues: make(map[string]any),
		Aggregates:  make(map[string]*FunctionState),
		RowCount:    0,
		FirstSeen:   time.Now(),
		LastSeen:    time.Now(),
	}

	// 保存分组字段值
	for _, field := range ea.groupByFields {
		state.GroupValues[field] = row.Get(field)
	}

	// 初始化聚合函数状态
	for _, aggregation := range ea.aggregations {
		state.Aggregates[aggregation.TargetField] = ea.createFunctionState(aggregation.Function)
	}

	return state
}

// createFunctionState 创建函数状态
func (ea *EnhancedAggregator) createFunctionState(function string) *FunctionState {
	return &FunctionState{
		Function: function,
		Count:    0,
		Sum:      0,
		Values:   make([]any, 0),
		Distinct: make(map[string]bool),
	}
}

// updateAggregateState 更新聚合状态
func (ea *EnhancedAggregator) updateAggregateState(state *AggregateState, row step.Row) error {
	state.RowCount++
	state.LastSeen = time.Now()

	for _, aggregation := range ea.aggregations {
		// 检查聚合条件
		if aggregation.Condition != "" {
			shouldAggregate, err := ea.evaluateAggregateCondition(row, aggregation.Condition)
			if err != nil {
				return fmt.Errorf("condition evaluation failed: %w", err)
			}
			if !shouldAggregate {
				continue
			}
		}

		funcState := state.Aggregates[aggregation.TargetField]
		if err := ea.updateFunctionState(funcState, row, aggregation); err != nil {
			return fmt.Errorf("function state update failed: %w", err)
		}
	}

	return nil
}

// updateFunctionState 更新函数状态
func (ea *EnhancedAggregator) updateFunctionState(funcState *FunctionState, row step.Row, aggregation AggregationRule) error {
	var value any
	var err error

	// 获取聚合值
	if aggregation.Expression != "" {
		value, err = ea.EvaluateExpression(aggregation.Expression, row)
		if err != nil {
			return fmt.Errorf("expression evaluation failed: %w", err)
		}
	} else if aggregation.SourceField != "" {
		value = row.Get(aggregation.SourceField)
	}

	// 更新函数状态
	return ea.applyAggregateFunction(funcState, aggregation.Function, value, aggregation.Parameters)
}

// applyAggregateFunction 应用聚合函数
func (ea *EnhancedAggregator) applyAggregateFunction(funcState *FunctionState, function string, value any, parameters map[string]any) error {
	funcState.Count++

	switch function {
	case FuncCount:
		if value != nil {
			funcState.Value = funcState.Count
		}

	case FuncCountDistinct:
		if value != nil {
			key := fmt.Sprintf("%v", value)
			if !funcState.Distinct[key] {
				funcState.Distinct[key] = true
				funcState.Value = int64(len(funcState.Distinct))
			}
		}

	case FuncSum:
		if numValue, err := ea.toNumber(value); err == nil {
			funcState.Sum += numValue
			funcState.Value = funcState.Sum
		}

	case FuncAvg:
		if numValue, err := ea.toNumber(value); err == nil {
			funcState.Sum += numValue
			funcState.Value = funcState.Sum / float64(funcState.Count)
		}

	case FuncMin:
		if funcState.Min == nil || ea.compareValues(value, funcState.Min) < 0 {
			funcState.Min = value
			funcState.Value = value
		}

	case FuncMax:
		if funcState.Max == nil || ea.compareValues(value, funcState.Max) > 0 {
			funcState.Max = value
			funcState.Value = value
		}

	case FuncFirst:
		if funcState.FirstValue == nil {
			funcState.FirstValue = value
			funcState.Value = value
		}

	case FuncLast:
		funcState.LastValue = value
		funcState.Value = value

	case FuncConcat:
		separator := ","
		if sep, ok := parameters["separator"].(string); ok {
			separator = sep
		}
		if funcState.Value == nil {
			funcState.Value = fmt.Sprintf("%v", value)
		} else {
			funcState.Value = fmt.Sprintf("%v%s%v", funcState.Value, separator, value)
		}

	case FuncCollectList:
		funcState.Values = append(funcState.Values, value)
		funcState.Value = funcState.Values

	case FuncCollectSet:
		key := fmt.Sprintf("%v", value)
		if !funcState.Distinct[key] {
			funcState.Distinct[key] = true
			funcState.Values = append(funcState.Values, value)
			funcState.Value = funcState.Values
		}

	case FuncMedian:
		if numValue, err := ea.toNumber(value); err == nil {
			funcState.Values = append(funcState.Values, numValue)
			funcState.Value = ea.calculateMedian(funcState.Values)
		}

	case FuncPercentile:
		if numValue, err := ea.toNumber(value); err == nil {
			funcState.Values = append(funcState.Values, numValue)
			percentile := 50.0 // 默认 50%
			if p, ok := parameters["percentile"].(float64); ok {
				percentile = p
			}
			funcState.Value = ea.calculatePercentile(funcState.Values, percentile)
		}

	default:
		return fmt.Errorf("unsupported aggregate function: %s", function)
	}

	return nil
}

// generateAggregateResults 生成聚合结果
func (ea *EnhancedAggregator) generateAggregateResults(ctx context.Context) (step.RowSet, error) {
	var outputRows []step.Row

	// 转换聚合状态为输出行
	for _, state := range ea.aggregateResults {
		row := ea.createAggregateRow(state)

		// 应用 HAVING 条件
		if ea.havingCondition != "" {
			shouldInclude, err := ea.evaluateHavingCondition(row, ea.havingCondition)
			if err != nil {
				ea.recordError(fmt.Errorf("having condition evaluation failed: %w", err))
				continue
			}
			if !shouldInclude {
				continue
			}
		}

		outputRows = append(outputRows, row)
	}

	// 排序
	if len(ea.sortRules) > 0 {
		ea.sortRows(outputRows)
	}

	// 应用 OFFSET 和 LIMIT
	outputRows = ea.applyOffsetLimit(outputRows)

	// 创建输出模式
	schema := ea.createOutputSchema()
	schemaFields := ea.convertSchemaToFieldMeta(schema)

	return step.NewMemoryRowSetFromRows(outputRows, schemaFields), nil
}

// createAggregateRow 创建聚合行
func (ea *EnhancedAggregator) createAggregateRow(state *AggregateState) step.Row {
	data := make(map[string]interface{})

	// 添加分组字段
	for field, value := range state.GroupValues {
		data[field] = value
	}

	// 添加聚合结果
	for targetField, funcState := range state.Aggregates {
		data[targetField] = funcState.Value
	}

	// 添加元数据字段（如果需要）
	if includeMetadata, exists := ea.GetConfigBool("include_metadata"); exists && includeMetadata {
		data["__row_count__"] = state.RowCount
		data["__first_seen__"] = state.FirstSeen
		data["__last_seen__"] = state.LastSeen
	}

	return step.NewSimpleRow(data)
}

// evaluateAggregateCondition 评估聚合条件
func (ea *EnhancedAggregator) evaluateAggregateCondition(row step.Row, condition string) (bool, error) {
	variables := ea.rowToVariables(row)
	return ea.engine.EvaluateToBoolean(condition, variables)
}

// evaluateHavingCondition 评估 HAVING 条件
func (ea *EnhancedAggregator) evaluateHavingCondition(row step.Row, condition string) (bool, error) {
	variables := ea.rowToVariables(row)
	return ea.engine.EvaluateToBoolean(condition, variables)
}

// sortRows 排序行
func (ea *EnhancedAggregator) sortRows(rows []step.Row) {
	sort.Slice(rows, func(i, j int) bool {
		for _, sortRule := range ea.sortRules {
			val1 := rows[i].Get(sortRule.Field)
			val2 := rows[j].Get(sortRule.Field)

			// 处理 NULL 值
			if val1 == nil && val2 == nil {
				continue
			}
			if val1 == nil {
				return !sortRule.NullsLast
			}
			if val2 == nil {
				return sortRule.NullsLast
			}

			cmp := ea.compareValues(val1, val2)
			if cmp == 0 {
				continue
			}

			if sortRule.Direction == "DESC" {
				return cmp > 0
			}
			return cmp < 0
		}
		return false
	})
}

// compareValues 比较两个值
func (ea *EnhancedAggregator) compareValues(v1, v2 any) int {
	// 尝试数值比较
	if n1, err1 := ea.toNumber(v1); err1 == nil {
		if n2, err2 := ea.toNumber(v2); err2 == nil {
			if n1 < n2 {
				return -1
			}
			if n1 > n2 {
				return 1
			}
			return 0
		}
	}

	// 字符串比较
	s1 := fmt.Sprintf("%v", v1)
	s2 := fmt.Sprintf("%v", v2)
	return strings.Compare(s1, s2)
}

// applyOffsetLimit 应用偏移和限制
func (ea *EnhancedAggregator) applyOffsetLimit(rows []step.Row) []step.Row {
	start := ea.offsetCount
	if start < 0 {
		start = 0
	}
	if start >= len(rows) {
		return []step.Row{}
	}

	end := len(rows)
	if ea.limitCount > 0 {
		end = start + ea.limitCount
		if end > len(rows) {
			end = len(rows)
		}
	}

	return rows[start:end]
}

// createOutputSchema 创建输出模式
func (ea *EnhancedAggregator) createOutputSchema() *step.Schema {
	fields := make([]step.Field, 0)

	// 分组字段
	for _, field := range ea.groupByFields {
		fields = append(fields, step.Field{
			Name: field,
			Type: step.StringType, // 简化处理，实际应该根据输入模式推断
		})
	}

	// 聚合字段
	for _, aggregation := range ea.aggregations {
		fieldType := ea.getAggregateFieldType(aggregation.Function, aggregation.DataType)
		fields = append(fields, step.Field{
			Name: aggregation.TargetField,
			Type: fieldType,
		})
	}

	return &step.Schema{Fields: fields}
}

// convertSchemaToFieldMeta 将step.Schema转换为[]meta.FieldMeta
func (ea *EnhancedAggregator) convertSchemaToFieldMeta(schema *step.Schema) []meta.FieldMeta {
	if schema == nil {
		return nil
	}

	fieldMetas := make([]meta.FieldMeta, len(schema.Fields))
	for i, field := range schema.Fields {
		fieldMetas[i] = meta.FieldMeta{
			Name:   field.Name,
			Type:   ea.convertStepFieldTypeToMeta(field.Type),
			Length: field.Length,
			Format: field.Format,
		}
	}

	return fieldMetas
}

// convertStepFieldTypeToMeta 转换字段类型
func (ea *EnhancedAggregator) convertStepFieldTypeToMeta(stepType step.FieldType) meta.FieldType {
	switch stepType {
	case step.StringType:
		return meta.FieldTypeString
	case step.IntegerType:
		return meta.FieldTypeInteger
	case step.FloatType:
		return meta.FieldTypeFloat
	case step.BooleanType:
		return meta.FieldTypeBoolean
	case step.DateTimeType:
		return meta.FieldTypeDate
	case step.BinaryType:
		return meta.FieldTypeBinary
	default:
		return meta.FieldTypeString
	}
}

// getAggregateFieldType 获取聚合字段类型
func (ea *EnhancedAggregator) getAggregateFieldType(function, dataType string) step.FieldType {
	if dataType != "" {
		switch strings.ToLower(dataType) {
		case "int", "integer", "long":
			return step.IntegerType
		case "float", "double", "number":
			return step.FloatType
		case "bool", "boolean":
			return step.BooleanType
		case "date", "datetime", "timestamp":
			return step.DateTimeType
		default:
			return step.StringType
		}
	}

	// 根据函数推断类型
	switch function {
	case FuncCount, FuncCountDistinct:
		return step.IntegerType
	case FuncSum, FuncAvg, FuncMedian, FuncPercentile:
		return step.FloatType
	case FuncCollectList, FuncCollectSet:
		return step.StringType // 简化处理
	default:
		return step.StringType
	}
}

// 数学计算辅助函数
func (ea *EnhancedAggregator) toNumber(value any) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to number", value)
	}
}

func (ea *EnhancedAggregator) calculateMedian(values []any) float64 {
	if len(values) == 0 {
		return 0
	}

	numbers := make([]float64, 0, len(values))
	for _, v := range values {
		if num, err := ea.toNumber(v); err == nil {
			numbers = append(numbers, num)
		}
	}

	if len(numbers) == 0 {
		return 0
	}

	sort.Float64s(numbers)
	n := len(numbers)
	if n%2 == 0 {
		return (numbers[n/2-1] + numbers[n/2]) / 2
	}
	return numbers[n/2]
}

func (ea *EnhancedAggregator) calculatePercentile(values []any, percentile float64) float64 {
	if len(values) == 0 {
		return 0
	}

	numbers := make([]float64, 0, len(values))
	for _, v := range values {
		if num, err := ea.toNumber(v); err == nil {
			numbers = append(numbers, num)
		}
	}

	if len(numbers) == 0 {
		return 0
	}

	sort.Float64s(numbers)
	index := (percentile / 100.0) * float64(len(numbers)-1)
	if index == math.Trunc(index) {
		return numbers[int(index)]
	}

	lower := int(math.Floor(index))
	upper := int(math.Ceil(index))
	weight := index - math.Floor(index)

	return numbers[lower]*(1-weight) + numbers[upper]*weight
}

// validateConfiguration 验证配置
func (ea *EnhancedAggregator) validateConfiguration() error {
	// 验证聚合规则
	for i, aggregation := range ea.aggregations {
		if aggregation.TargetField == "" {
			return fmt.Errorf("aggregation %d: target_field is required", i)
		}
		if aggregation.Function == "" && aggregation.Expression == "" {
			return fmt.Errorf("aggregation %d: function or expression is required", i)
		}
		if aggregation.Function != "" && !ea.isValidAggregateFunction(aggregation.Function) {
			return fmt.Errorf("aggregation %d: unsupported function: %s", i, aggregation.Function)
		}
	}

	// 验证排序规则
	for i, sortRule := range ea.sortRules {
		if sortRule.Field == "" {
			return fmt.Errorf("sort rule %d: field is required", i)
		}
		if sortRule.Direction != "ASC" && sortRule.Direction != "DESC" {
			return fmt.Errorf("sort rule %d: direction must be ASC or DESC", i)
		}
	}

	return nil
}

// isValidAggregateFunction 检查聚合函数是否有效
func (ea *EnhancedAggregator) isValidAggregateFunction(function string) bool {
	validFuncs := []string{
		FuncCount, FuncCountDistinct, FuncSum, FuncAvg,
		FuncMin, FuncMax, FuncFirst, FuncLast,
		FuncConcat, FuncCollectList, FuncCollectSet,
		FuncStdDev, FuncVariance, FuncMedian, FuncPercentile,
		FuncCustom,
	}

	for _, validFunc := range validFuncs {
		if function == validFunc {
			return true
		}
	}
	return false
}

// GetAggregateStatistics 获取聚合统计信息
func (ea *EnhancedAggregator) GetAggregateStatistics() map[string]interface{} {
	ea.mutex.RLock()
	defer ea.mutex.RUnlock()

	return map[string]any{
		"group_count":       len(ea.aggregateResults),
		"group_by_fields":   ea.groupByFields,
		"aggregation_count": len(ea.aggregations),
		"having_condition":  ea.havingCondition,
		"sort_rules":        len(ea.sortRules),
		"limit_count":       ea.limitCount,
		"offset_count":      ea.offsetCount,
		"memory_optimized":  ea.memoryOptimized,
		"streaming_mode":    ea.streamingMode,
	}
}
