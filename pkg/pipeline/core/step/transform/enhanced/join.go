package enhanced

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// EnhancedJoin 增强的连接组件，支持多种连接类型和优化策略
type EnhancedJoin struct {
	*EnhancedTransformStep

	// 连接配置
	joinType       JoinType             // 连接类型
	leftKeys       []string             // 左侧连接键
	rightKeys      []string             // 右侧连接键
	leftRowSet     step.RowSet          // 左侧数据集
	rightRowSet    step.RowSet          // 右侧数据集
	joinConditions []JoinCondition      // 连接条件
	outputFields   []OutputFieldMapping // 输出字段映射

	// 性能优化
	useHashJoin bool                             // 是否使用哈希连接
	buildSide   JoinSide                         // 构建侧选择
	bloomFilter *BloomFilter                     // 布隆过滤器
	indexCache  map[string]map[string][]step.Row // 索引缓存

	// 排序合并连接
	sortedJoin  bool // 是否使用排序合并连接
	leftSorted  bool // 左侧是否已排序
	rightSorted bool // 右侧是否已排序

	// 内存管理
	maxMemoryUsage int64 // 最大内存使用量

	// 连接指标
	joinMetrics *JoinMetrics // 连接指标
}

// JoinType 连接类型
type JoinType int

const (
	JoinTypeInner JoinType = iota
	JoinTypeLeft
	JoinTypeRight
	JoinTypeFull
	JoinTypeCross
	JoinTypeSemi
	JoinTypeAnti
)

// JoinSide 连接侧
type JoinSide int

const (
	JoinSideAuto JoinSide = iota
	JoinSideLeft
	JoinSideRight
)

// JoinCondition 连接条件
type JoinCondition struct {
	LeftField     string `json:"left_field"`     // 左侧字段
	RightField    string `json:"right_field"`    // 右侧字段
	Operator      string `json:"operator"`       // 操作符 (=, !=, <, <=, >, >=)
	CaseSensitive bool   `json:"case_sensitive"` // 是否大小写敏感
}

// OutputFieldMapping 输出字段映射
type OutputFieldMapping struct {
	SourceSide   JoinSide `json:"source_side"`   // 源侧 (left/right)
	SourceField  string   `json:"source_field"`  // 源字段
	TargetField  string   `json:"target_field"`  // 目标字段
	Expression   string   `json:"expression"`    // 表达式（可选）
	DefaultValue any      `json:"default_value"` // 默认值
}

// JoinMetrics 连接指标
type JoinMetrics struct {
	LeftInputRows      int64         // 左侧输入行数
	RightInputRows     int64         // 右侧输入行数
	OutputRows         int64         // 输出行数
	MatchedPairs       int64         // 匹配的行对数
	UnmatchedLeft      int64         // 未匹配的左侧行数
	UnmatchedRight     int64         // 未匹配的右侧行数
	HashBuildTime      time.Duration // 哈希表构建时间
	ProbeTime          time.Duration // 探测时间
	SortTime           time.Duration // 排序时间
	MemoryUsage        int64         // 内存使用量
	DiskSpills         int64         // 磁盘溢出次数
	IndexHitRate       float64       // 索引命中率
	BloomFilterQueries int64         // 布隆过滤器查询次数
	BloomFilterHits    int64         // 布隆过滤器命中次数
	mutex              sync.RWMutex  // 读写锁
}

// BloomFilter 优化的布隆过滤器实现
type BloomFilter struct {
	bitArray     []bool       // 位数组
	size         int          // 位数组大小
	hashFunc     int          // 哈希函数数量
	elemCount    int64        // 已添加元素数量
	maxElements  int64        // 预期最大元素数量
	targetFPRate float64      // 目标假阳性率
	actualFPRate float64      // 实际假阳性率
	mutex        sync.RWMutex // 并发安全锁
}

// BloomFilterConfig 布隆过滤器配置
type BloomFilterConfig struct {
	ExpectedElements  int64   // 预期元素数量
	FalsePositiveRate float64 // 期望假阳性率 (默认 0.01 即 1%)
	MaxMemoryMB       int     // 最大内存使用量（MB）
}

// BloomFilterStats 布隆过滤器统计信息
type BloomFilterStats struct {
	Size             int     // 位数组大小
	HashFunctions    int     // 哈希函数数量
	ElementCount     int64   // 已添加元素数量
	MaxElements      int64   // 预期最大元素数量
	TargetFPRate     float64 // 目标假阳性率
	ActualFPRate     float64 // 实际假阳性率
	MemoryUsageBytes int64   // 内存使用量（字节）
	LoadFactor       float64 // 负载因子
}

// NewEnhancedJoin 创建增强的连接组件
func NewEnhancedJoin(stepMeta meta.StepMeta) *EnhancedJoin {
	ej := &EnhancedJoin{
		EnhancedTransformStep: NewEnhancedTransformStep(stepMeta),
		joinType:              JoinTypeInner,
		useHashJoin:           true,
		buildSide:             JoinSideAuto,
		maxMemoryUsage:        1024 * 1024 * 1024, // 1GB
		indexCache:            make(map[string]map[string][]step.Row),
		joinMetrics:           &JoinMetrics{},
	}

	return ej
}

// Initialize 初始化连接组件
func (ej *EnhancedJoin) Initialize() error {
	if err := ej.EnhancedTransformStep.Initialize(); err != nil {
		return err
	}

	// 解析连接配置
	if err := ej.parseJoinConfig(); err != nil {
		return fmt.Errorf("failed to parse join config: %w", err)
	}

	// 初始化布隆过滤器
	if enableBloom, _ := ej.GetConfigBool("enable_bloom_filter"); enableBloom {
		// 获取布隆过滤器配置参数
		expectedElements, _ := ej.GetConfigInt("bloom_expected_elements")
		if expectedElements == 0 {
			expectedElements = 1000000 // 默认100万元素
		}

		falsePositiveRateStr, _ := ej.GetConfigString("bloom_false_positive_rate")
		falsePositiveRate := 0.01 // 默认1%假阳性率
		if falsePositiveRateStr != "" {
			if rate, err := strconv.ParseFloat(falsePositiveRateStr, 64); err == nil {
				falsePositiveRate = rate
			}
		}

		maxMemoryMB, _ := ej.GetConfigInt("bloom_max_memory_mb")
		if maxMemoryMB == 0 {
			maxMemoryMB = 64 // 默认64MB
		}

		// 使用配置创建优化的布隆过滤器
		config := BloomFilterConfig{
			ExpectedElements:  expectedElements,
			FalsePositiveRate: falsePositiveRate,
			MaxMemoryMB:       int(maxMemoryMB),
		}
		ej.bloomFilter = NewBloomFilterWithConfig(config)

		// 记录布隆过滤器配置信息
		stats := ej.bloomFilter.GetStats()
		fmt.Printf("Bloom filter initialized - size: %d, hash_functions: %d, expected_elements: %d, target_fp_rate: %.4f, memory_mb: %d\n",
			stats.Size, stats.HashFunctions, stats.MaxElements, stats.TargetFPRate, stats.MemoryUsageBytes/1024/1024)
	}

	return nil
}

// Execute 执行连接操作
func (ej *EnhancedJoin) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	ej.metrics.StartTime = time.Now().UnixNano()

	// 检查是否有右侧数据集
	if ej.rightRowSet == nil {
		return nil, fmt.Errorf("right dataset is required for join operation")
	}

	// 设置左侧数据集
	ej.leftRowSet = input

	// 根据连接策略执行连接
	var outputRows []step.Row
	var err error

	if ej.sortedJoin {
		outputRows, err = ej.executeSortMergeJoin(ctx)
	} else if ej.useHashJoin {
		outputRows, err = ej.executeHashJoin(ctx)
	} else {
		outputRows, err = ej.executeNestedLoopJoin(ctx)
	}

	if err != nil {
		return nil, fmt.Errorf("join execution failed: %w", err)
	}

	// 创建输出模式
	outputSchema := ej.createOutputSchema()

	ej.joinMetrics.OutputRows = int64(len(outputRows))
	return step.NewMemoryRowSetFromRows(outputRows, outputSchema), nil
}

// SetRightDataSet 设置右侧数据集
func (ej *EnhancedJoin) SetRightDataSet(rightRowSet step.RowSet) {
	ej.rightRowSet = rightRowSet
}

// executeHashJoin 执行哈希连接
func (ej *EnhancedJoin) executeHashJoin(ctx context.Context) ([]step.Row, error) {
	startTime := time.Now()
	defer func() {
		ej.joinMetrics.HashBuildTime = time.Since(startTime)
	}()

	// 选择构建侧和探测侧
	buildRowSet, probeRowSet, buildKeys, probeKeys := ej.chooseBuildAndProbeSides()

	// 构建哈希表
	hashTable, err := ej.buildHashTable(ctx, buildRowSet, buildKeys)
	if err != nil {
		return nil, fmt.Errorf("failed to build hash table: %w", err)
	}

	// 探测阶段
	probeStartTime := time.Now()
	outputRows, err := ej.probeHashTable(ctx, probeRowSet, probeKeys, hashTable)
	ej.joinMetrics.ProbeTime = time.Since(probeStartTime)

	return outputRows, err
}

// executeSortMergeJoin 执行排序合并连接
func (ej *EnhancedJoin) executeSortMergeJoin(ctx context.Context) ([]step.Row, error) {
	sortStartTime := time.Now()

	// 排序左侧数据
	leftRows, err := ej.sortRowSet(ctx, ej.leftRowSet, ej.leftKeys, ej.leftSorted)
	if err != nil {
		return nil, fmt.Errorf("failed to sort left dataset: %w", err)
	}
	ej.joinMetrics.LeftInputRows = int64(len(leftRows))

	// 排序右侧数据
	rightRows, err := ej.sortRowSet(ctx, ej.rightRowSet, ej.rightKeys, ej.rightSorted)
	if err != nil {
		return nil, fmt.Errorf("failed to sort right dataset: %w", err)
	}
	ej.joinMetrics.RightInputRows = int64(len(rightRows))

	ej.joinMetrics.SortTime = time.Since(sortStartTime)

	// 合并连接
	return ej.mergeSortedDatasets(ctx, leftRows, rightRows)
}

// executeNestedLoopJoin 执行嵌套循环连接
func (ej *EnhancedJoin) executeNestedLoopJoin(ctx context.Context) ([]step.Row, error) {
	// 将右侧数据加载到内存
	rightRows, err := ej.loadRowSetToMemory(ctx, ej.rightRowSet)
	if err != nil {
		return nil, fmt.Errorf("failed to load right dataset: %w", err)
	}
	ej.joinMetrics.RightInputRows = int64(len(rightRows))

	var outputRows []step.Row

	// 遍历左侧数据
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		leftRow, _, err := ej.leftRowSet.Next()
		if err != nil {
			return nil, fmt.Errorf("left dataset error: %w", err)
		}
		if leftRow == nil {
			break
		}

		ej.joinMetrics.LeftInputRows++

		// 与右侧每一行进行比较
		hasMatch := false
		for _, rightRow := range rightRows {
			if ej.evaluateJoinConditions(leftRow, rightRow) {
				hasMatch = true
				ej.joinMetrics.MatchedPairs++

				// 根据连接类型生成输出行
				if outputRow := ej.createJoinedRow(leftRow, rightRow); outputRow != nil {
					outputRows = append(outputRows, outputRow)
				}

				// Semi连接只需要一个匹配
				if ej.joinType == JoinTypeSemi {
					break
				}
			}
		}

		// 处理未匹配的左侧行
		if !hasMatch {
			ej.joinMetrics.UnmatchedLeft++
			if ej.shouldIncludeUnmatchedLeft() {
				if outputRow := ej.createJoinedRow(leftRow, nil); outputRow != nil {
					outputRows = append(outputRows, outputRow)
				}
			}
		}
	}

	// 处理未匹配的右侧行（Right Join和Full Join）
	if ej.shouldIncludeUnmatchedRight() {
		outputRows = append(outputRows, ej.createUnmatchedRightRows(rightRows)...)
	}

	return outputRows, nil
}

// buildHashTable 构建哈希表
func (ej *EnhancedJoin) buildHashTable(ctx context.Context, buildRowSet step.RowSet, keys []string) (map[string][]step.Row, error) {
	hashTable := make(map[string][]step.Row)

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := buildRowSet.Next()
		if err != nil {
			return nil, fmt.Errorf("build dataset error: %w", err)
		}
		if row == nil {
			break
		}

		// 计算连接键的哈希值
		keyValue := ej.calculateJoinKey(row, keys)

		// 添加到布隆过滤器
		if ej.bloomFilter != nil {
			ej.bloomFilter.Add(keyValue)
		}

		hashTable[keyValue] = append(hashTable[keyValue], row.Clone())
	}

	return hashTable, nil
}

// probeHashTable 探测哈希表
func (ej *EnhancedJoin) probeHashTable(ctx context.Context, probeRowSet step.RowSet, keys []string, hashTable map[string][]step.Row) ([]step.Row, error) {
	var outputRows []step.Row

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		probeRow, _, err := probeRowSet.Next()
		if err != nil {
			return nil, fmt.Errorf("probe dataset error: %w", err)
		}
		if probeRow == nil {
			break
		}

		keyValue := ej.calculateJoinKey(probeRow, keys)

		// 布隆过滤器快速检查
		if ej.bloomFilter != nil {
			ej.joinMetrics.BloomFilterQueries++
			if !ej.bloomFilter.Contains(keyValue) {
				continue // 肯定不存在，跳过
			}
			ej.joinMetrics.BloomFilterHits++
		}

		// 在哈希表中查找匹配行
		if matchingRows, exists := hashTable[keyValue]; exists {
			hasMatch := false
			for _, matchingRow := range matchingRows {
				if ej.evaluateJoinConditions(probeRow, matchingRow) {
					hasMatch = true
					ej.joinMetrics.MatchedPairs++

					// 根据探测侧确定左右行
					var leftRow, rightRow step.Row
					if ej.buildSide == JoinSideRight {
						leftRow, rightRow = probeRow, matchingRow
					} else {
						leftRow, rightRow = matchingRow, probeRow
					}

					if outputRow := ej.createJoinedRow(leftRow, rightRow); outputRow != nil {
						outputRows = append(outputRows, outputRow)
					}
				}
			}

			if !hasMatch {
				// 处理未匹配的探测行
				if ej.shouldIncludeUnmatchedProbe() {
					var leftRow, rightRow step.Row
					if ej.buildSide == JoinSideRight {
						leftRow, rightRow = probeRow, nil
					} else {
						leftRow, rightRow = nil, probeRow
					}

					if outputRow := ej.createJoinedRow(leftRow, rightRow); outputRow != nil {
						outputRows = append(outputRows, outputRow)
					}
				}
			}
		}
	}

	return outputRows, nil
}

// sortRowSet 排序行数据集
func (ej *EnhancedJoin) sortRowSet(ctx context.Context, rowSet step.RowSet, keys []string, alreadySorted bool) ([]step.Row, error) {
	if alreadySorted {
		return ej.loadRowSetToMemory(ctx, rowSet)
	}

	rows, err := ej.loadRowSetToMemory(ctx, rowSet)
	if err != nil {
		return nil, err
	}

	// 排序
	sort.Slice(rows, func(i, j int) bool {
		return ej.compareRows(rows[i], rows[j], keys) < 0
	})

	return rows, nil
}

// mergeSortedDatasets 合并已排序的数据集
func (ej *EnhancedJoin) mergeSortedDatasets(ctx context.Context, leftRows, rightRows []step.Row) ([]step.Row, error) {
	var outputRows []step.Row
	leftIndex, rightIndex := 0, 0

	for leftIndex < len(leftRows) && rightIndex < len(rightRows) {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		leftRow := leftRows[leftIndex]
		rightRow := rightRows[rightIndex]

		comparison := ej.compareJoinKeys(leftRow, rightRow)

		if comparison == 0 {
			// 键相等，检查连接条件
			if ej.evaluateJoinConditions(leftRow, rightRow) {
				ej.joinMetrics.MatchedPairs++
				if outputRow := ej.createJoinedRow(leftRow, rightRow); outputRow != nil {
					outputRows = append(outputRows, outputRow)
				}
			}

			// 处理可能的多个匹配
			// 这里简化处理，实际应该处理多对多的情况
			rightIndex++
		} else if comparison < 0 {
			// 左侧键较小
			if ej.shouldIncludeUnmatchedLeft() {
				if outputRow := ej.createJoinedRow(leftRow, nil); outputRow != nil {
					outputRows = append(outputRows, outputRow)
				}
			}
			leftIndex++
		} else {
			// 右侧键较小
			if ej.shouldIncludeUnmatchedRight() {
				if outputRow := ej.createJoinedRow(nil, rightRow); outputRow != nil {
					outputRows = append(outputRows, outputRow)
				}
			}
			rightIndex++
		}
	}

	// 处理剩余的行
	for leftIndex < len(leftRows) {
		if ej.shouldIncludeUnmatchedLeft() {
			if outputRow := ej.createJoinedRow(leftRows[leftIndex], nil); outputRow != nil {
				outputRows = append(outputRows, outputRow)
			}
		}
		leftIndex++
	}

	for rightIndex < len(rightRows) {
		if ej.shouldIncludeUnmatchedRight() {
			if outputRow := ej.createJoinedRow(nil, rightRows[rightIndex]); outputRow != nil {
				outputRows = append(outputRows, outputRow)
			}
		}
		rightIndex++
	}

	return outputRows, nil
}

// evaluateJoinConditions 评估连接条件
func (ej *EnhancedJoin) evaluateJoinConditions(leftRow, rightRow step.Row) bool {
	if len(ej.joinConditions) == 0 {
		// 使用默认的键比较
		return ej.compareJoinKeys(leftRow, rightRow) == 0
	}

	for _, condition := range ej.joinConditions {
		leftValue := leftRow.Get(condition.LeftField)
		rightValue := rightRow.Get(condition.RightField)

		if !ej.compareValues(leftValue, rightValue, condition.Operator, condition.CaseSensitive) {
			return false
		}
	}

	return true
}

// createJoinedRow 创建连接后的行
func (ej *EnhancedJoin) createJoinedRow(leftRow, rightRow step.Row) step.Row {
	outputData := make(map[string]interface{})

	// 处理输出字段映射
	for _, mapping := range ej.outputFields {
		var sourceRow step.Row

		switch mapping.SourceSide {
		case JoinSideLeft:
			sourceRow = leftRow
		case JoinSideRight:
			sourceRow = rightRow
		}

		var value interface{}
		if sourceRow != nil {
			if mapping.Expression != "" {
				// 使用表达式计算值
				evaluatedValue, err := ej.EvaluateExpression(mapping.Expression, sourceRow)
				if err != nil {
					value = mapping.DefaultValue
				} else {
					value = evaluatedValue
				}
			} else {
				value = sourceRow.Get(mapping.SourceField)
			}
		} else {
			value = mapping.DefaultValue
		}

		outputData[mapping.TargetField] = value
	}

	// 如果没有指定输出映射，使用默认行为
	if len(ej.outputFields) == 0 {
		if leftRow != nil {
			for _, field := range leftRow.Fields() {
				outputData["left_"+field] = leftRow.Get(field)
			}
		}
		if rightRow != nil {
			for _, field := range rightRow.Fields() {
				outputData["right_"+field] = rightRow.Get(field)
			}
		}
	}

	return step.NewSimpleRow(outputData)
}

// 辅助方法
func (ej *EnhancedJoin) chooseBuildAndProbeSides() (buildRowSet, probeRowSet step.RowSet, buildKeys, probeKeys []string) {
	// 简化实现：总是选择右侧作为构建侧
	if ej.buildSide == JoinSideAuto || ej.buildSide == JoinSideRight {
		return ej.rightRowSet, ej.leftRowSet, ej.rightKeys, ej.leftKeys
	}
	return ej.leftRowSet, ej.rightRowSet, ej.leftKeys, ej.rightKeys
}

func (ej *EnhancedJoin) calculateJoinKey(row step.Row, keys []string) string {
	var keyParts []string
	for _, key := range keys {
		value := row.Get(key)
		keyParts = append(keyParts, fmt.Sprintf("%v", value))
	}
	return strings.Join(keyParts, "|")
}

func (ej *EnhancedJoin) compareJoinKeys(leftRow, rightRow step.Row) int {
	leftKey := ej.calculateJoinKey(leftRow, ej.leftKeys)
	rightKey := ej.calculateJoinKey(rightRow, ej.rightKeys)
	return strings.Compare(leftKey, rightKey)
}

func (ej *EnhancedJoin) compareRows(row1, row2 step.Row, keys []string) int {
	for _, key := range keys {
		val1 := row1.Get(key)
		val2 := row2.Get(key)

		if result := ej.compareValuesForSort(val1, val2); result != 0 {
			return result
		}
	}
	return 0
}

func (ej *EnhancedJoin) compareValuesForSort(val1, val2 interface{}) int {
	// 实现值比较逻辑用于排序
	str1 := fmt.Sprintf("%v", val1)
	str2 := fmt.Sprintf("%v", val2)

	if str1 < str2 {
		return -1
	} else if str1 > str2 {
		return 1
	}
	return 0
}

func (ej *EnhancedJoin) compareValues(val1, val2 interface{}, operator string, caseSensitive bool) bool {
	// 处理 nil 值
	if val1 == nil && val2 == nil {
		return operator == "=" || operator == "eq" || operator == "<=" || operator == "le" || operator == ">=" || operator == "ge"
	}
	if val1 == nil || val2 == nil {
		return operator == "!=" || operator == "ne"
	}

	// 尝试进行类型感知的比较
	result := ej.compareTypedValues(val1, val2, caseSensitive)

	switch operator {
	case "=", "eq":
		return result == 0
	case "!=", "ne":
		return result != 0
	case "<", "lt":
		return result < 0
	case "<=", "le":
		return result <= 0
	case ">", "gt":
		return result > 0
	case ">=", "ge":
		return result >= 0
	default:
		return result == 0
	}
}

// compareTypedValues 进行类型感知的值比较
func (ej *EnhancedJoin) compareTypedValues(val1, val2 interface{}, caseSensitive bool) int {
	// 如果类型相同，直接比较
	switch v1 := val1.(type) {
	case string:
		if v2, ok := val2.(string); ok {
			return ej.compareStrings(v1, v2, caseSensitive)
		}
	case int:
		if v2, ok := val2.(int); ok {
			return ej.compareInts(int64(v1), int64(v2))
		}
		if v2, ok := val2.(int64); ok {
			return ej.compareInts(int64(v1), v2)
		}
		if v2, ok := val2.(float64); ok {
			return ej.compareFloats(float64(v1), v2)
		}
	case int64:
		if v2, ok := val2.(int64); ok {
			return ej.compareInts(v1, v2)
		}
		if v2, ok := val2.(int); ok {
			return ej.compareInts(v1, int64(v2))
		}
		if v2, ok := val2.(float64); ok {
			return ej.compareFloats(float64(v1), v2)
		}
	case float64:
		if v2, ok := val2.(float64); ok {
			return ej.compareFloats(v1, v2)
		}
		if v2, ok := val2.(int); ok {
			return ej.compareFloats(v1, float64(v2))
		}
		if v2, ok := val2.(int64); ok {
			return ej.compareFloats(v1, float64(v2))
		}
	case bool:
		if v2, ok := val2.(bool); ok {
			return ej.compareBools(v1, v2)
		}
	case time.Time:
		if v2, ok := val2.(time.Time); ok {
			return ej.compareTimes(v1, v2)
		}
	}

	// 尝试数值转换比较
	if num1, ok1 := ej.tryConvertToNumber(val1); ok1 {
		if num2, ok2 := ej.tryConvertToNumber(val2); ok2 {
			return ej.compareFloats(num1, num2)
		}
	}

	// 尝试时间转换比较
	if time1, ok1 := ej.tryConvertToTime(val1); ok1 {
		if time2, ok2 := ej.tryConvertToTime(val2); ok2 {
			return ej.compareTimes(time1, time2)
		}
	}

	// 回退到字符串比较
	str1 := fmt.Sprintf("%v", val1)
	str2 := fmt.Sprintf("%v", val2)
	return ej.compareStrings(str1, str2, caseSensitive)
}

// compareStrings 字符串比较
func (ej *EnhancedJoin) compareStrings(s1, s2 string, caseSensitive bool) int {
	if !caseSensitive {
		s1 = strings.ToLower(s1)
		s2 = strings.ToLower(s2)
	}
	return strings.Compare(s1, s2)
}

// compareInts 整数比较
func (ej *EnhancedJoin) compareInts(i1, i2 int64) int {
	if i1 < i2 {
		return -1
	} else if i1 > i2 {
		return 1
	}
	return 0
}

// compareFloats 浮点数比较
func (ej *EnhancedJoin) compareFloats(f1, f2 float64) int {
	const epsilon = 1e-9
	diff := f1 - f2
	if diff < -epsilon {
		return -1
	} else if diff > epsilon {
		return 1
	}
	return 0
}

// compareBools 布尔值比较
func (ej *EnhancedJoin) compareBools(b1, b2 bool) int {
	if b1 == b2 {
		return 0
	}
	if !b1 && b2 {
		return -1 // false < true
	}
	return 1
}

// compareTimes 时间比较
func (ej *EnhancedJoin) compareTimes(t1, t2 time.Time) int {
	if t1.Before(t2) {
		return -1
	} else if t1.After(t2) {
		return 1
	}
	return 0
}

// tryConvertToNumber 尝试转换为数值
func (ej *EnhancedJoin) tryConvertToNumber(val interface{}) (float64, bool) {
	switch v := val.(type) {
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case float64:
		return v, true
	case float32:
		return float64(v), true
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f, true
		}
		if i, err := strconv.ParseInt(v, 10, 64); err == nil {
			return float64(i), true
		}
	}
	return 0, false
}

// tryConvertToTime 尝试转换为时间
func (ej *EnhancedJoin) tryConvertToTime(val interface{}) (time.Time, bool) {
	switch v := val.(type) {
	case time.Time:
		return v, true
	case string:
		// 尝试多种时间格式
		formats := []string{
			time.RFC3339,
			time.RFC3339Nano,
			"2006-01-02 15:04:05",
			"2006-01-02",
			"15:04:05",
			"2006/01/02 15:04:05",
			"2006/01/02",
		}
		for _, format := range formats {
			if t, err := time.Parse(format, v); err == nil {
				return t, true
			}
		}
	case int64:
		// 尝试作为 Unix 时间戳
		if v > 1000000000 && v < 9999999999 { // 合理的时间戳范围
			return time.Unix(v, 0), true
		}
		if v > 1000000000000 && v < 9999999999999 { // 毫秒时间戳
			return time.Unix(v/1000, (v%1000)*1000000), true
		}
	}
	return time.Time{}, false
}

func (ej *EnhancedJoin) shouldIncludeUnmatchedLeft() bool {
	return ej.joinType == JoinTypeLeft || ej.joinType == JoinTypeFull
}

func (ej *EnhancedJoin) shouldIncludeUnmatchedRight() bool {
	return ej.joinType == JoinTypeRight || ej.joinType == JoinTypeFull
}

func (ej *EnhancedJoin) shouldIncludeUnmatchedProbe() bool {
	if ej.buildSide == JoinSideRight {
		return ej.shouldIncludeUnmatchedLeft()
	}
	return ej.shouldIncludeUnmatchedRight()
}

func (ej *EnhancedJoin) loadRowSetToMemory(ctx context.Context, rowSet step.RowSet) ([]step.Row, error) {
	var rows []step.Row

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := rowSet.Next()
		if err != nil {
			return nil, err
		}
		if row == nil {
			break
		}

		rows = append(rows, row.Clone())
	}

	return rows, nil
}

func (ej *EnhancedJoin) createUnmatchedRightRows(rightRows []step.Row) []step.Row {
	var outputRows []step.Row
	for _, rightRow := range rightRows {
		if outputRow := ej.createJoinedRow(nil, rightRow); outputRow != nil {
			outputRows = append(outputRows, outputRow)
		}
	}
	return outputRows
}

func (ej *EnhancedJoin) createOutputSchema() []meta.FieldMeta {
	var schema []meta.FieldMeta

	for _, mapping := range ej.outputFields {
		schema = append(schema, meta.FieldMeta{
			Name: mapping.TargetField,
			Type: meta.FieldTypeString, // 简化实现
		})
	}

	return schema
}

func (ej *EnhancedJoin) parseJoinConfig() error {
	// 解析连接类型
	if joinTypeStr, _ := ej.GetConfigString("join_type"); joinTypeStr != "" {
		switch strings.ToLower(joinTypeStr) {
		case "inner":
			ej.joinType = JoinTypeInner
		case "left":
			ej.joinType = JoinTypeLeft
		case "right":
			ej.joinType = JoinTypeRight
		case "full":
			ej.joinType = JoinTypeFull
		case "cross":
			ej.joinType = JoinTypeCross
		case "semi":
			ej.joinType = JoinTypeSemi
		case "anti":
			ej.joinType = JoinTypeAnti
		}
	}

	// 解析连接键
	if leftKeysStr, _ := ej.GetConfigString("left_keys"); leftKeysStr != "" {
		ej.leftKeys = strings.Split(leftKeysStr, ",")
		for i := range ej.leftKeys {
			ej.leftKeys[i] = strings.TrimSpace(ej.leftKeys[i])
		}
	}
	if rightKeysStr, _ := ej.GetConfigString("right_keys"); rightKeysStr != "" {
		ej.rightKeys = strings.Split(rightKeysStr, ",")
		for i := range ej.rightKeys {
			ej.rightKeys[i] = strings.TrimSpace(ej.rightKeys[i])
		}
	}

	// 解析其他配置...
	if useHashJoin, _ := ej.GetConfigBool("use_hash_join"); useHashJoin {
		ej.useHashJoin = true
	}
	if sortedJoin, _ := ej.GetConfigBool("sorted_join"); sortedJoin {
		ej.sortedJoin = true
	}

	return nil
}

// NewBloomFilter 创建新的布隆过滤器
func NewBloomFilter(size, hashFunctions int) *BloomFilter {
	return &BloomFilter{
		bitArray: make([]bool, size),
		size:     size,
		hashFunc: hashFunctions,
	}
}

// NewBloomFilterWithConfig 根据配置创建优化的布隆过滤器
func NewBloomFilterWithConfig(config BloomFilterConfig) *BloomFilter {
	// 设置默认值
	if config.FalsePositiveRate <= 0 {
		config.FalsePositiveRate = 0.01 // 默认1%假阳性率
	}
	if config.ExpectedElements <= 0 {
		config.ExpectedElements = 1000000 // 默认100万元素
	}

	// 计算最优参数
	optimalSize := calculateOptimalBitArraySize(config.ExpectedElements, config.FalsePositiveRate)
	optimalHashFuncs := calculateOptimalHashFunctions(optimalSize, config.ExpectedElements)

	// 检查内存限制
	if config.MaxMemoryMB > 0 {
		maxSize := int64(config.MaxMemoryMB * 1024 * 1024 * 8) // 转换为位数
		if optimalSize > maxSize {
			optimalSize = maxSize
			// 重新计算哈希函数数量
			optimalHashFuncs = calculateOptimalHashFunctions(optimalSize, config.ExpectedElements)
		}
	}

	return &BloomFilter{
		bitArray:     make([]bool, optimalSize),
		size:         int(optimalSize),
		hashFunc:     optimalHashFuncs,
		maxElements:  config.ExpectedElements,
		targetFPRate: config.FalsePositiveRate,
	}
}

// calculateOptimalBitArraySize 计算最优位数组大小
func calculateOptimalBitArraySize(expectedElements int64, falsePositiveRate float64) int64 {
	// m = -(n * ln(p)) / (ln(2)^2)
	// 其中 n = 预期元素数量, p = 假阳性率, m = 位数组大小
	m := -float64(expectedElements) * math.Log(falsePositiveRate) / (math.Log(2) * math.Log(2))
	return int64(math.Ceil(m))
}

// calculateOptimalHashFunctions 计算最优哈希函数数量
func calculateOptimalHashFunctions(bitArraySize int64, expectedElements int64) int {
	// k = (m/n) * ln(2)
	// 其中 m = 位数组大小, n = 预期元素数量, k = 哈希函数数量
	k := float64(bitArraySize) / float64(expectedElements) * math.Log(2)
	hashFuncs := int(math.Round(k))

	// 限制哈希函数数量在合理范围内
	if hashFuncs < 1 {
		hashFuncs = 1
	} else if hashFuncs > 20 {
		hashFuncs = 20
	}

	return hashFuncs
}

// Add 添加元素到布隆过滤器
func (bf *BloomFilter) Add(item string) {
	bf.mutex.Lock()
	defer bf.mutex.Unlock()

	for i := 0; i < bf.hashFunc; i++ {
		hash := bf.murmurHash(item, uint32(i)) % uint32(bf.size)
		bf.bitArray[hash] = true
	}

	bf.elemCount++
	bf.updateActualFPRate()
}

// Contains 检查元素是否可能存在
func (bf *BloomFilter) Contains(item string) bool {
	bf.mutex.RLock()
	defer bf.mutex.RUnlock()

	for i := 0; i < bf.hashFunc; i++ {
		hash := bf.murmurHash(item, uint32(i)) % uint32(bf.size)
		if !bf.bitArray[hash] {
			return false // 肯定不存在
		}
	}
	return true // 可能存在
}

// murmurHash 实现MurmurHash3算法的简化版本
func (bf *BloomFilter) murmurHash(data string, seed uint32) uint32 {
	const (
		c1 = 0xcc9e2d51
		c2 = 0x1b873593
		r1 = 15
		r2 = 13
		m  = 5
		n  = 0xe6546b64
	)

	hash := seed
	bytes := []byte(data)
	length := len(bytes)

	// 处理4字节块
	for i := 0; i < length-3; i += 4 {
		k := uint32(bytes[i]) | uint32(bytes[i+1])<<8 | uint32(bytes[i+2])<<16 | uint32(bytes[i+3])<<24
		k *= c1
		k = (k << r1) | (k >> (32 - r1))
		k *= c2
		hash ^= k
		hash = (hash << r2) | (hash >> (32 - r2))
		hash = hash*m + n
	}

	// 处理剩余字节
	tail := length & 3
	if tail > 0 {
		var k uint32
		for i := 0; i < tail; i++ {
			k |= uint32(bytes[length-tail+i]) << (8 * i)
		}
		k *= c1
		k = (k << r1) | (k >> (32 - r1))
		k *= c2
		hash ^= k
	}

	// 最终化
	hash ^= uint32(length)
	hash ^= hash >> 16
	hash *= 0x85ebca6b
	hash ^= hash >> 13
	hash *= 0xc2b2ae35
	hash ^= hash >> 16

	return hash
}

// updateActualFPRate 更新实际假阳性率
func (bf *BloomFilter) updateActualFPRate() {
	if bf.elemCount == 0 {
		bf.actualFPRate = 0
		return
	}

	// 计算实际假阳性率: (1 - e^(-k*n/m))^k
	// 其中 k = 哈希函数数量, n = 元素数量, m = 位数组大小
	k := float64(bf.hashFunc)
	n := float64(bf.elemCount)
	m := float64(bf.size)

	bf.actualFPRate = math.Pow(1-math.Exp(-k*n/m), k)
}

// GetStats 获取布隆过滤器统计信息
func (bf *BloomFilter) GetStats() BloomFilterStats {
	bf.mutex.RLock()
	defer bf.mutex.RUnlock()

	loadFactor := 0.0
	if bf.maxElements > 0 {
		loadFactor = float64(bf.elemCount) / float64(bf.maxElements)
	}

	return BloomFilterStats{
		Size:             bf.size,
		HashFunctions:    bf.hashFunc,
		ElementCount:     bf.elemCount,
		MaxElements:      bf.maxElements,
		TargetFPRate:     bf.targetFPRate,
		ActualFPRate:     bf.actualFPRate,
		MemoryUsageBytes: int64(bf.size / 8), // 位转字节
		LoadFactor:       loadFactor,
	}
}

// IsOverloaded 检查是否过载
func (bf *BloomFilter) IsOverloaded() bool {
	bf.mutex.RLock()
	defer bf.mutex.RUnlock()

	// 如果实际假阳性率超过目标假阳性率的2倍，认为过载
	return bf.actualFPRate > bf.targetFPRate*2
}

// Reset 重置布隆过滤器
func (bf *BloomFilter) Reset() {
	bf.mutex.Lock()
	defer bf.mutex.Unlock()

	for i := range bf.bitArray {
		bf.bitArray[i] = false
	}
	bf.elemCount = 0
	bf.actualFPRate = 0
}

// EstimateElementCount 估算元素数量（基于位数组填充度）
func (bf *BloomFilter) EstimateElementCount() int64 {
	bf.mutex.RLock()
	defer bf.mutex.RUnlock()

	setBits := 0
	for _, bit := range bf.bitArray {
		if bit {
			setBits++
		}
	}

	if setBits == 0 {
		return 0
	}

	// 使用公式估算：n = -(m * ln(1 - X/m)) / k
	// 其中 X = 设置的位数, m = 位数组大小, k = 哈希函数数量
	m := float64(bf.size)
	k := float64(bf.hashFunc)
	x := float64(setBits)

	if x >= m {
		return bf.maxElements // 位数组已满，返回最大元素数
	}

	estimated := -(m * math.Log(1-x/m)) / k
	return int64(math.Round(estimated))
}

// GetMetrics 获取连接指标
func (ej *EnhancedJoin) GetJoinMetrics() *JoinMetrics {
	ej.joinMetrics.mutex.RLock()
	defer ej.joinMetrics.mutex.RUnlock()

	// 计算索引命中率
	if ej.joinMetrics.BloomFilterQueries > 0 {
		ej.joinMetrics.IndexHitRate = float64(ej.joinMetrics.BloomFilterHits) / float64(ej.joinMetrics.BloomFilterQueries)
	}

	return ej.joinMetrics
}

// GetBloomFilterStats 获取布隆过滤器详细统计信息
func (ej *EnhancedJoin) GetBloomFilterStats() *BloomFilterStats {
	if ej.bloomFilter == nil {
		return nil
	}

	stats := ej.bloomFilter.GetStats()
	return &stats
}

// IsBloomFilterOverloaded 检查布隆过滤器是否过载
func (ej *EnhancedJoin) IsBloomFilterOverloaded() bool {
	if ej.bloomFilter == nil {
		return false
	}

	return ej.bloomFilter.IsOverloaded()
}

// ResetBloomFilter 重置布隆过滤器
func (ej *EnhancedJoin) ResetBloomFilter() {
	if ej.bloomFilter != nil {
		ej.bloomFilter.Reset()
	}
}
