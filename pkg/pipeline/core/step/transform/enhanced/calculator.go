package enhanced

import (
	"context"
	"fmt"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// EnhancedCalculator 增强计算器步骤
type EnhancedCalculator struct {
	*EnhancedTransformStep
	calculations     []CalculationRule
	compiledExprs    map[string]any // 编译后的表达式缓存
	conditionalRules []ConditionalRule
}

// CalculationRule 计算规则
type CalculationRule struct {
	TargetField  string `json:"target_field"`  // 目标字段名
	Expression   string `json:"expression"`    // 计算表达式
	DataType     string `json:"data_type"`     // 数据类型
	DefaultValue any    `json:"default_value"` // 默认值
	Condition    string `json:"condition"`     // 执行条件
	Description  string `json:"description"`   // 规则描述
	OnError      string `json:"on_error"`      // 错误处理：skip, default, error
}

// NewEnhancedCalculator 创建增强计算器
func NewEnhancedCalculator(stepMeta meta.StepMeta) *EnhancedCalculator {
	return &EnhancedCalculator{
		EnhancedTransformStep: NewEnhancedTransformStep(stepMeta),
		calculations:          []CalculationRule{},
		compiledExprs:         make(map[string]any),
		conditionalRules:      []ConditionalRule{},
	}
}

// Initialize 初始化步骤
func (ec *EnhancedCalculator) Initialize() error {
	if err := ec.EnhancedTransformStep.BaseStep.Initialize(); err != nil {
		return err
	}

	// 解析计算规则配置
	if err := ec.parseCalculationRules(); err != nil {
		return fmt.Errorf("failed to parse calculation rules: %w", err)
	}

	// 验证所有表达式
	if err := ec.validateExpressions(); err != nil {
		return fmt.Errorf("expression validation failed: %w", err)
	}

	// 预编译常用表达式
	ec.precompileExpressions()

	return nil
}

// Execute 执行增强计算
func (ec *EnhancedCalculator) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	ec.metrics.StartTime = time.Now().UnixNano()
	defer func() {
		ec.metrics.LastUpdate = time.Now().UnixNano()
	}()

	return ec.ExecuteWithContext(ctx, input, ec.processRow)
}

// processRow 处理单行数据
func (ec *EnhancedCalculator) processRow(row step.Row) (step.Row, error) {
	// 创建输出行，复制原始数据
	outputData := make(map[string]interface{})
	for _, field := range row.Fields() {
		outputData[field] = row.Get(field)
	}
	outputRow := step.NewSimpleRow(outputData)

	// 执行所有计算规则
	for _, calc := range ec.calculations {
		if err := ec.executeCalculation(calc, outputRow); err != nil {
			switch calc.OnError {
			case "skip":
				continue // 跳过这个计算
			case "default":
				outputRow.Set(calc.TargetField, calc.DefaultValue)
				continue
			default: // "error"
				return nil, fmt.Errorf("calculation failed for field '%s': %w", calc.TargetField, err)
			}
		}

		// 更新字段统计
		ec.metrics.mutex.Lock()
		ec.metrics.FieldStats[calc.TargetField]++
		ec.metrics.mutex.Unlock()
	}

	// 执行条件规则
	for _, rule := range ec.conditionalRules {
		if err := ec.executeConditionalRule(rule, outputRow); err != nil {
			return nil, fmt.Errorf("conditional rule failed: %w", err)
		}
	}

	return outputRow, nil
}

// executeCalculation 执行单个计算规则
func (ec *EnhancedCalculator) executeCalculation(calc CalculationRule, row step.Row) error {
	// 检查执行条件
	if calc.Condition != "" {
		shouldExecute, err := ec.EvaluateCondition(calc.Condition, row)
		if err != nil {
			return fmt.Errorf("condition evaluation failed: %w", err)
		}
		if !shouldExecute {
			return nil // 条件不满足，跳过计算
		}
	}

	// 计算表达式
	result, err := ec.EvaluateExpression(calc.Expression, row)
	if err != nil {
		return fmt.Errorf("expression evaluation failed: %w", err)
	}

	// 类型转换
	if calc.DataType != "" {
		converter := NewTypeConverter(calc.DataType)
		result, err = converter.Convert(result)
		if err != nil {
			return fmt.Errorf("type conversion failed: %w", err)
		}
	}

	// 设置计算结果
	row.Set(calc.TargetField, result)
	return nil
}

// executeConditionalRule 执行条件规则
func (ec *EnhancedCalculator) executeConditionalRule(rule ConditionalRule, row step.Row) error {
	// 评估条件
	conditionMet, err := ec.EvaluateCondition(rule.Condition, row)
	if err != nil {
		return fmt.Errorf("condition evaluation failed: %w", err)
	}

	if !conditionMet {
		return nil // 条件不满足
	}

	// 执行动作
	switch rule.Action {
	case "transform":
		if rule.Expression != "" {
			result, err := ec.EvaluateExpression(rule.Expression, row)
			if err != nil {
				return fmt.Errorf("transformation expression failed: %w", err)
			}
			// 这里需要知道目标字段，暂时使用固定字段名
			row.Set("__conditional_result__", result)
		}
	case "skip":
		return fmt.Errorf("row should be skipped")
	case "error":
		message := rule.Message
		if message == "" {
			message = "conditional rule triggered error"
		}
		return fmt.Errorf("%s", message)
	}

	return nil
}

// parseCalculationRules 解析计算规则配置
func (ec *EnhancedCalculator) parseCalculationRules() error {
	config := ec.GetMeta().Config

	// 解析calculations配置
	if calcConfig, exists := config["calculations"]; exists {
		calcList, ok := calcConfig.([]interface{})
		if !ok {
			return fmt.Errorf("calculations must be an array")
		}

		for i, calcItem := range calcList {
			calcMap, ok := calcItem.(map[string]interface{})
			if !ok {
				return fmt.Errorf("calculation item %d must be an object", i)
			}

			calc := CalculationRule{
				OnError: "error", // 默认错误处理方式
			}

			// 解析必填字段
			if targetField, exists := calcMap["target_field"]; exists {
				calc.TargetField, _ = targetField.(string)
			}
			if calc.TargetField == "" {
				return fmt.Errorf("calculation %d: target_field is required", i)
			}

			if expression, exists := calcMap["expression"]; exists {
				calc.Expression, _ = expression.(string)
			}
			if calc.Expression == "" {
				return fmt.Errorf("calculation %d: expression is required", i)
			}

			// 解析可选字段
			if dataType, exists := calcMap["data_type"]; exists {
				calc.DataType, _ = dataType.(string)
			}
			if defaultValue, exists := calcMap["default_value"]; exists {
				calc.DefaultValue = defaultValue
			}
			if condition, exists := calcMap["condition"]; exists {
				calc.Condition, _ = condition.(string)
			}
			if description, exists := calcMap["description"]; exists {
				calc.Description, _ = description.(string)
			}
			if onError, exists := calcMap["on_error"]; exists {
				calc.OnError, _ = onError.(string)
			}

			ec.calculations = append(ec.calculations, calc)
		}
	}

	// 解析conditional_rules配置
	if rulesConfig, exists := config["conditional_rules"]; exists {
		rulesList, ok := rulesConfig.([]interface{})
		if !ok {
			return fmt.Errorf("conditional_rules must be an array")
		}

		for i, ruleItem := range rulesList {
			ruleMap, ok := ruleItem.(map[string]interface{})
			if !ok {
				return fmt.Errorf("conditional rule item %d must be an object", i)
			}

			rule := ConditionalRule{}

			if condition, exists := ruleMap["condition"]; exists {
				rule.Condition, _ = condition.(string)
			}
			if action, exists := ruleMap["action"]; exists {
				rule.Action, _ = action.(string)
			}
			if expression, exists := ruleMap["expression"]; exists {
				rule.Expression, _ = expression.(string)
			}
			if value, exists := ruleMap["value"]; exists {
				rule.Value = value
			}
			if message, exists := ruleMap["message"]; exists {
				rule.Message, _ = message.(string)
			}

			ec.conditionalRules = append(ec.conditionalRules, rule)
		}
	}

	return nil
}

// validateExpressions 验证所有表达式
func (ec *EnhancedCalculator) validateExpressions() error {
	// 验证计算表达式
	for i, calc := range ec.calculations {
		if err := ec.ValidateExpression(calc.Expression); err != nil {
			return fmt.Errorf("calculation %d expression invalid: %w", i, err)
		}
		if calc.Condition != "" {
			if err := ec.ValidateExpression(calc.Condition); err != nil {
				return fmt.Errorf("calculation %d condition invalid: %w", i, err)
			}
		}
	}

	// 验证条件规则表达式
	for i, rule := range ec.conditionalRules {
		if rule.Condition != "" {
			if err := ec.ValidateExpression(rule.Condition); err != nil {
				return fmt.Errorf("conditional rule %d condition invalid: %w", i, err)
			}
		}
		if rule.Expression != "" {
			if err := ec.ValidateExpression(rule.Expression); err != nil {
				return fmt.Errorf("conditional rule %d expression invalid: %w", i, err)
			}
		}
	}

	return nil
}

// precompileExpressions 预编译常用表达式
func (ec *EnhancedCalculator) precompileExpressions() {
	// 这里可以预编译表达式以提高性能
	// 当前版本暂时跳过，后续可以实现表达式缓存
}

// Validate 验证步骤配置
func (ec *EnhancedCalculator) Validate() error {
	if len(ec.calculations) == 0 {
		return fmt.Errorf("at least one calculation rule is required")
	}

	// 检查目标字段名冲突
	fieldNames := make(map[string]bool)
	for i, calc := range ec.calculations {
		if fieldNames[calc.TargetField] {
			return fmt.Errorf("duplicate target field '%s' in calculation %d", calc.TargetField, i)
		}
		fieldNames[calc.TargetField] = true
	}

	return nil
}

// GetCalculationRules 获取计算规则
func (ec *EnhancedCalculator) GetCalculationRules() []CalculationRule {
	return ec.calculations
}

// AddCalculationRule 添加计算规则
func (ec *EnhancedCalculator) AddCalculationRule(rule CalculationRule) error {
	// 验证表达式
	if err := ec.ValidateExpression(rule.Expression); err != nil {
		return fmt.Errorf("invalid expression: %w", err)
	}
	if rule.Condition != "" {
		if err := ec.ValidateExpression(rule.Condition); err != nil {
			return fmt.Errorf("invalid condition: %w", err)
		}
	}

	ec.calculations = append(ec.calculations, rule)
	return nil
}

// RemoveCalculationRule 移除计算规则
func (ec *EnhancedCalculator) RemoveCalculationRule(targetField string) bool {
	for i, calc := range ec.calculations {
		if calc.TargetField == targetField {
			ec.calculations = append(ec.calculations[:i], ec.calculations[i+1:]...)
			return true
		}
	}
	return false
}

// GetConditionalRules 获取条件规则
func (ec *EnhancedCalculator) GetConditionalRules() []ConditionalRule {
	return ec.conditionalRules
}

// AddConditionalRule 添加条件规则
func (ec *EnhancedCalculator) AddConditionalRule(rule ConditionalRule) error {
	if rule.Condition != "" {
		if err := ec.ValidateExpression(rule.Condition); err != nil {
			return fmt.Errorf("invalid condition: %w", err)
		}
	}
	if rule.Expression != "" {
		if err := ec.ValidateExpression(rule.Expression); err != nil {
			return fmt.Errorf("invalid expression: %w", err)
		}
	}

	ec.conditionalRules = append(ec.conditionalRules, rule)
	return nil
}

// GetSupportedFunctions 获取支持的函数列表
func (ec *EnhancedCalculator) GetSupportedFunctions() []string {
	return []string{
		// 数学函数
		"abs", "ceil", "floor", "round", "sqrt", "pow", "log", "exp",
		"sin", "cos", "tan", "asin", "acos", "atan",
		"min", "max", "sum", "avg",

		// 字符串函数
		"len", "upper", "lower", "trim", "substr", "concat", "replace",
		"startswith", "endswith", "contains", "split", "join",

		// 日期函数
		"now", "today", "year", "month", "day", "hour", "minute", "second",
		"dateformat", "dateparse", "datediff", "dateadd",

		// 类型转换函数
		"tostring", "toint", "tofloat", "tobool",

		// 条件函数
		"if", "isnull", "isempty", "coalesce", "nullif",

		// 正则表达式函数
		"regex_match", "regex_replace", "regex_extract",
	}
}

// GetExpressionExamples 获取表达式示例
func (ec *EnhancedCalculator) GetExpressionExamples() map[string]string {
	return map[string]string{
		"算术运算":  "price * quantity",
		"条件判断":  "age >= 18 ? '成年' : '未成年'",
		"字符串拼接": "concat(first_name, ' ', last_name)",
		"数学函数":  "round(total * 0.1, 2)",
		"日期计算":  "datediff(now(), created_at, 'days')",
		"类型转换":  "toint(score_str)",
		"空值处理":  "coalesce(phone, mobile, '无')",
		"正则匹配":  "regex_match(email, '^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$')",
		"复合表达式": "if(status == 'active' && score > 80, 'A级', if(score > 60, 'B级', 'C级'))",
		"字段引用":  "upper(trim(name))",
	}
}

// GetType 返回步骤类型
func (ec *EnhancedCalculator) GetType() meta.StepType {
	return meta.StepTypeEnhancedCalculator
}
