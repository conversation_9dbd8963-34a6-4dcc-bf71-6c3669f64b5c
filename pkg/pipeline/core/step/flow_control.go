package step

import (
	"context"
	"fmt"
	"sync"
	"time"

	"admin/pkg/pipeline/meta"
)

// DataFlowSplitter 数据流分叉器
type DataFlowSplitter struct {
	*BaseStep
	outputs    []chan RowSet    // 输出通道
	condition  SplitCondition   // 分叉条件
	strategy   SplitStrategy    // 分叉策略
	bufferSize int              // 缓冲区大小
	metrics    *SplitterMetrics // 分叉指标
}

// SplitCondition 分叉条件函数
type SplitCondition func(row Row) int

// SplitStrategy 分叉策略
type SplitStrategy int

const (
	SplitStrategyRoundRobin  SplitStrategy = iota // 轮询
	SplitStrategyConditional                      // 条件分叉
	SplitStrategyDuplicate                        // 复制到所有输出
	SplitStrategyHash                             // 哈希分片
)

// SplitterMetrics 分叉器指标
type SplitterMetrics struct {
	InputRows    int64         // 输入行数
	OutputCounts map[int]int64 // 各输出的行数
	StartTime    time.Time     // 开始时间
	EndTime      time.Time     // 结束时间
	mutex        sync.RWMutex  // 读写锁
}

// NewDataFlowSplitter 创建数据流分叉器
func NewDataFlowSplitter(stepMeta meta.StepMeta, outputCount int, strategy SplitStrategy) *DataFlowSplitter {
	splitter := &DataFlowSplitter{
		BaseStep:   NewBaseStep(stepMeta),
		outputs:    make([]chan RowSet, outputCount),
		strategy:   strategy,
		bufferSize: 100,
		metrics: &SplitterMetrics{
			OutputCounts: make(map[int]int64),
		},
	}

	// 初始化输出通道
	for i := range splitter.outputs {
		splitter.outputs[i] = make(chan RowSet, splitter.bufferSize)
		splitter.metrics.OutputCounts[i] = 0
	}

	return splitter
}

// SetCondition 设置分叉条件
func (dfs *DataFlowSplitter) SetCondition(condition SplitCondition) {
	dfs.condition = condition
}

// Execute 执行数据分叉
func (dfs *DataFlowSplitter) Execute(ctx context.Context, input RowSet) (RowSet, error) {
	if input == nil {
		return NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	dfs.metrics.StartTime = time.Now()
	defer func() {
		dfs.metrics.EndTime = time.Now()
	}()

	// 创建输出RowSet列表
	outputRowSets := make([][]Row, len(dfs.outputs))
	for i := range outputRowSets {
		outputRowSets[i] = []Row{}
	}

	roundRobinIndex := 0

	// 处理每一行数据
	for {
		row, _, err := input.Next()
		if err != nil {
			return nil, fmt.Errorf("input error: %w", err)
		}
		if row == nil {
			break
		}

		dfs.metrics.InputRows++

		// 根据策略选择输出
		switch dfs.strategy {
		case SplitStrategyRoundRobin:
			outputIndex := roundRobinIndex % len(dfs.outputs)
			outputRowSets[outputIndex] = append(outputRowSets[outputIndex], row.Clone())
			dfs.metrics.OutputCounts[outputIndex]++
			roundRobinIndex++

		case SplitStrategyConditional:
			if dfs.condition != nil {
				outputIndex := dfs.condition(row)
				if outputIndex >= 0 && outputIndex < len(dfs.outputs) {
					outputRowSets[outputIndex] = append(outputRowSets[outputIndex], row.Clone())
					dfs.metrics.OutputCounts[outputIndex]++
				}
			}

		case SplitStrategyDuplicate:
			for i := range dfs.outputs {
				outputRowSets[i] = append(outputRowSets[i], row.Clone())
				dfs.metrics.OutputCounts[i]++
			}

		case SplitStrategyHash:
			hash := dfs.calculateRowHash(row)
			outputIndex := hash % len(dfs.outputs)
			outputRowSets[outputIndex] = append(outputRowSets[outputIndex], row.Clone())
			dfs.metrics.OutputCounts[outputIndex]++
		}
	}

	input.Close()

	// 创建合并的输出（第一个输出作为主输出）
	if len(outputRowSets) > 0 && len(outputRowSets[0]) > 0 {
		return NewMemoryRowSetFromRows(outputRowSets[0], input.Schema()), nil
	}

	return NewEmptyRowSet(input.Schema()), nil
}

// GetOutputs 获取所有输出数据集
func (dfs *DataFlowSplitter) GetOutputs(input RowSet) ([]RowSet, error) {
	// 这是一个特殊方法，用于获取所有分叉的输出
	// 在实际使用中，需要特殊处理
	return nil, fmt.Errorf("use Execute method for standard pipeline integration")
}

// calculateRowHash 计算行数据的哈希值
func (dfs *DataFlowSplitter) calculateRowHash(row Row) int {
	hash := 0
	for _, field := range row.Fields() {
		value := row.Get(field)
		if value != nil {
			hash += len(fmt.Sprintf("%v", value))
		}
	}
	return hash
}

// GetMetrics 获取分叉器指标
func (dfs *DataFlowSplitter) GetMetrics() *SplitterMetrics {
	dfs.metrics.mutex.RLock()
	defer dfs.metrics.mutex.RUnlock()

	// 返回指标副本
	metricsCopy := &SplitterMetrics{
		InputRows:    dfs.metrics.InputRows,
		OutputCounts: make(map[int]int64),
		StartTime:    dfs.metrics.StartTime,
		EndTime:      dfs.metrics.EndTime,
	}

	for k, v := range dfs.metrics.OutputCounts {
		metricsCopy.OutputCounts[k] = v
	}

	return metricsCopy
}

// DataFlowMerger 数据流合并器
type DataFlowMerger struct {
	*BaseStep
	inputs     []RowSet       // 输入数据集列表
	strategy   MergeStrategy  // 合并策略
	sortFields []string       // 排序字段（用于排序合并）
	bufferSize int            // 缓冲区大小
	metrics    *MergerMetrics // 合并器指标
}

// MergeStrategy 合并策略
type MergeStrategy int

const (
	MergeStrategyConcat      MergeStrategy = iota // 简单连接
	MergeStrategySortedMerge                      // 排序合并
	MergeStrategyInterleave                       // 交替合并
	MergeStrategyUnion                            // 联合（去重）
)

// MergerMetrics 合并器指标
type MergerMetrics struct {
	InputCounts   map[int]int64 // 各输入的行数
	OutputRows    int64         // 输出行数
	DuplicateRows int64         // 重复行数（用于联合策略）
	StartTime     time.Time     // 开始时间
	EndTime       time.Time     // 结束时间
	mutex         sync.RWMutex  // 读写锁
}

// NewDataFlowMerger 创建数据流合并器
func NewDataFlowMerger(stepMeta meta.StepMeta, strategy MergeStrategy) *DataFlowMerger {
	return &DataFlowMerger{
		BaseStep:   NewBaseStep(stepMeta),
		inputs:     []RowSet{},
		strategy:   strategy,
		bufferSize: 100,
		metrics: &MergerMetrics{
			InputCounts: make(map[int]int64),
		},
	}
}

// AddInput 添加输入数据集
func (dfm *DataFlowMerger) AddInput(input RowSet) {
	dfm.inputs = append(dfm.inputs, input)
	dfm.metrics.InputCounts[len(dfm.inputs)-1] = 0
}

// SetSortFields 设置排序字段
func (dfm *DataFlowMerger) SetSortFields(fields []string) {
	dfm.sortFields = fields
}

// Execute 执行数据合并
func (dfm *DataFlowMerger) Execute(ctx context.Context, input RowSet) (RowSet, error) {
	// 将输入添加到输入列表
	if input != nil {
		dfm.AddInput(input)
	}

	if len(dfm.inputs) == 0 {
		return NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	dfm.metrics.StartTime = time.Now()
	defer func() {
		dfm.metrics.EndTime = time.Now()
	}()

	var outputRows []Row
	var schema []meta.FieldMeta

	// 获取schema（使用第一个非空输入的schema）
	for _, inputRowSet := range dfm.inputs {
		if inputRowSet != nil && inputRowSet.Schema() != nil {
			schema = inputRowSet.Schema()
			break
		}
	}

	switch dfm.strategy {
	case MergeStrategyConcat:
		outputRows = dfm.concatMerge(ctx)
	case MergeStrategySortedMerge:
		outputRows = dfm.sortedMerge(ctx)
	case MergeStrategyInterleave:
		outputRows = dfm.interleaveMerge(ctx)
	case MergeStrategyUnion:
		outputRows = dfm.unionMerge(ctx)
	default:
		outputRows = dfm.concatMerge(ctx)
	}

	dfm.metrics.OutputRows = int64(len(outputRows))

	// 关闭所有输入
	for _, inputRowSet := range dfm.inputs {
		if inputRowSet != nil {
			inputRowSet.Close()
		}
	}

	return NewMemoryRowSetFromRows(outputRows, schema), nil
}

// concatMerge 简单连接合并
func (dfm *DataFlowMerger) concatMerge(ctx context.Context) []Row {
	var outputRows []Row

	for i, inputRowSet := range dfm.inputs {
		if inputRowSet == nil {
			continue
		}

		for {
			select {
			case <-ctx.Done():
				return outputRows
			default:
			}

			row, _, err := inputRowSet.Next()
			if err != nil || row == nil {
				break
			}

			outputRows = append(outputRows, row.Clone())
			dfm.metrics.InputCounts[i]++
		}
	}

	return outputRows
}

// sortedMerge 排序合并
func (dfm *DataFlowMerger) sortedMerge(ctx context.Context) []Row {
	// 简化实现：先收集所有数据，然后排序
	// 实际实现应该使用优先队列进行流式排序合并
	var allRows []Row

	for i, inputRowSet := range dfm.inputs {
		if inputRowSet == nil {
			continue
		}

		for {
			select {
			case <-ctx.Done():
				return allRows
			default:
			}

			row, _, err := inputRowSet.Next()
			if err != nil || row == nil {
				break
			}

			allRows = append(allRows, row.Clone())
			dfm.metrics.InputCounts[i]++
		}
	}

	// 这里应该实现实际的排序逻辑
	// 目前返回未排序的结果
	return allRows
}

// interleaveMerge 交替合并
func (dfm *DataFlowMerger) interleaveMerge(ctx context.Context) []Row {
	var outputRows []Row
	inputIterators := make([]*RowIterator, len(dfm.inputs))

	// 创建迭代器
	for i, inputRowSet := range dfm.inputs {
		if inputRowSet != nil {
			inputIterators[i] = NewRowIterator(inputRowSet)
		}
	}

	// 交替读取
	currentIndex := 0
	for {
		select {
		case <-ctx.Done():
			return outputRows
		default:
		}

		hasData := false
		for i := 0; i < len(inputIterators); i++ {
			iteratorIndex := (currentIndex + i) % len(inputIterators)
			iterator := inputIterators[iteratorIndex]

			if iterator != nil && iterator.HasNext() {
				row := iterator.Next()
				if row != nil {
					outputRows = append(outputRows, row.Clone())
					dfm.metrics.InputCounts[iteratorIndex]++
					hasData = true
					currentIndex = (iteratorIndex + 1) % len(inputIterators)
					break
				}
			}
		}

		if !hasData {
			break
		}
	}

	return outputRows
}

// unionMerge 联合合并（去重）
func (dfm *DataFlowMerger) unionMerge(ctx context.Context) []Row {
	var outputRows []Row
	seenRows := make(map[string]bool)

	for i, inputRowSet := range dfm.inputs {
		if inputRowSet == nil {
			continue
		}

		for {
			select {
			case <-ctx.Done():
				return outputRows
			default:
			}

			row, _, err := inputRowSet.Next()
			if err != nil || row == nil {
				break
			}

			// 计算行的哈希值用于去重
			rowHash := dfm.calculateRowHash(row)
			if !seenRows[rowHash] {
				outputRows = append(outputRows, row.Clone())
				seenRows[rowHash] = true
				dfm.metrics.InputCounts[i]++
			} else {
				dfm.metrics.DuplicateRows++
			}
		}
	}

	return outputRows
}

// calculateRowHash 计算行数据的哈希值用于去重
func (dfm *DataFlowMerger) calculateRowHash(row Row) string {
	var hashParts []string
	for _, field := range row.Fields() {
		value := row.Get(field)
		hashParts = append(hashParts, fmt.Sprintf("%s:%v", field, value))
	}
	return fmt.Sprintf("%v", hashParts)
}

// GetMetrics 获取合并器指标
func (dfm *DataFlowMerger) GetMetrics() *MergerMetrics {
	dfm.metrics.mutex.RLock()
	defer dfm.metrics.mutex.RUnlock()

	// 返回指标副本
	metricsCopy := &MergerMetrics{
		InputCounts:   make(map[int]int64),
		OutputRows:    dfm.metrics.OutputRows,
		DuplicateRows: dfm.metrics.DuplicateRows,
		StartTime:     dfm.metrics.StartTime,
		EndTime:       dfm.metrics.EndTime,
	}

	for k, v := range dfm.metrics.InputCounts {
		metricsCopy.InputCounts[k] = v
	}

	return metricsCopy
}

// RowIterator 行迭代器
type RowIterator struct {
	rowSet   RowSet
	hasNext  bool
	nextRow  Row
	finished bool
}

// NewRowIterator 创建行迭代器
func NewRowIterator(rowSet RowSet) *RowIterator {
	iterator := &RowIterator{
		rowSet:   rowSet,
		finished: false,
	}
	iterator.advance()
	return iterator
}

// HasNext 检查是否有下一行
func (ri *RowIterator) HasNext() bool {
	return ri.hasNext && !ri.finished
}

// Next 获取下一行
func (ri *RowIterator) Next() Row {
	if !ri.HasNext() {
		return nil
	}

	currentRow := ri.nextRow
	ri.advance()
	return currentRow
}

// advance 前进到下一行
func (ri *RowIterator) advance() {
	if ri.finished {
		ri.hasNext = false
		return
	}

	row, _, err := ri.rowSet.Next()
	if err != nil || row == nil {
		ri.hasNext = false
		ri.finished = true
		return
	}

	ri.nextRow = row
	ri.hasNext = true
}

// LoadBalancer 负载均衡器
type LoadBalancer struct {
	*BaseStep
	outputs      []Step          // 输出步骤列表
	strategy     BalanceStrategy // 负载均衡策略
	weights      []int           // 权重（用于加权轮询）
	currentIndex int             // 当前索引
	counters     []int64         // 计数器
	mutex        sync.RWMutex    // 读写锁
}

// BalanceStrategy 负载均衡策略
type BalanceStrategy int

const (
	BalanceStrategyRoundRobin       BalanceStrategy = iota // 轮询
	BalanceStrategyWeighted                                // 加权轮询
	BalanceStrategyLeastConnections                        // 最少连接
	BalanceStrategyRandom                                  // 随机
)

// NewLoadBalancer 创建负载均衡器
func NewLoadBalancer(stepMeta meta.StepMeta, outputs []Step, strategy BalanceStrategy) *LoadBalancer {
	return &LoadBalancer{
		BaseStep:     NewBaseStep(stepMeta),
		outputs:      outputs,
		strategy:     strategy,
		weights:      make([]int, len(outputs)),
		currentIndex: 0,
		counters:     make([]int64, len(outputs)),
	}
}

// SetWeights 设置权重
func (lb *LoadBalancer) SetWeights(weights []int) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if len(weights) == len(lb.outputs) {
		copy(lb.weights, weights)
	}
}

// Execute 执行负载均衡
func (lb *LoadBalancer) Execute(ctx context.Context, input RowSet) (RowSet, error) {
	if input == nil || len(lb.outputs) == 0 {
		return NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	// 选择目标步骤
	targetStep := lb.selectTarget()
	if targetStep == nil {
		return NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("no available target step")
	}

	// 执行选中的步骤
	return targetStep.Execute(ctx, input)
}

// selectTarget 选择目标步骤
func (lb *LoadBalancer) selectTarget() Step {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if len(lb.outputs) == 0 {
		return nil
	}

	var selectedIndex int

	switch lb.strategy {
	case BalanceStrategyRoundRobin:
		selectedIndex = lb.currentIndex % len(lb.outputs)
		lb.currentIndex++

	case BalanceStrategyWeighted:
		selectedIndex = lb.selectWeightedTarget()

	case BalanceStrategyLeastConnections:
		selectedIndex = lb.selectLeastConnectionsTarget()

	case BalanceStrategyRandom:
		selectedIndex = int(time.Now().UnixNano()) % len(lb.outputs)

	default:
		selectedIndex = 0
	}

	lb.counters[selectedIndex]++
	return lb.outputs[selectedIndex]
}

// selectWeightedTarget 选择加权目标
func (lb *LoadBalancer) selectWeightedTarget() int {
	// 简化的加权轮询实现
	totalWeight := 0
	for _, weight := range lb.weights {
		totalWeight += weight
	}

	if totalWeight == 0 {
		return lb.currentIndex % len(lb.outputs)
	}

	target := int(time.Now().UnixNano()) % totalWeight
	currentWeight := 0

	for i, weight := range lb.weights {
		currentWeight += weight
		if target < currentWeight {
			return i
		}
	}

	return 0
}

// selectLeastConnectionsTarget 选择最少连接目标
func (lb *LoadBalancer) selectLeastConnectionsTarget() int {
	minConnections := lb.counters[0]
	selectedIndex := 0

	for i := 1; i < len(lb.counters); i++ {
		if lb.counters[i] < minConnections {
			minConnections = lb.counters[i]
			selectedIndex = i
		}
	}

	return selectedIndex
}

// GetCounters 获取计数器
func (lb *LoadBalancer) GetCounters() []int64 {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	counters := make([]int64, len(lb.counters))
	copy(counters, lb.counters)
	return counters
}
