package step

import (
	"context"
	"runtime"
	"sync"
	"time"
)

// MemoryManager 内存管理器
type MemoryManager struct {
	maxMemoryBytes int64
	currentUsage   int64
	checkInterval  time.Duration
	gcThreshold    float64
	mutex          sync.RWMutex
	enabled        bool
	alerts         []MemoryAlert
}

// MemoryAlert 内存告警
type MemoryAlert struct {
	Timestamp time.Time
	Usage     int64
	Limit     int64
	Message   string
}

// NewMemoryManager 创建内存管理器
func NewMemoryManager(maxMemoryMB int64) *MemoryManager {
	return &MemoryManager{
		maxMemoryBytes: maxMemoryMB * 1024 * 1024,
		checkInterval:  5 * time.Second,
		gcThreshold:    0.8, // 80%时触发GC
		enabled:        true,
		alerts:         make([]MemoryAlert, 0, 100),
	}
}

// StartMonitoring 开始内存监控
func (mm *MemoryManager) StartMonitoring(ctx context.Context) {
	if !mm.enabled {
		return
	}

	ticker := time.NewTicker(mm.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			mm.checkMemoryUsage()
		}
	}
}

// checkMemoryUsage 检查内存使用
func (mm *MemoryManager) checkMemoryUsage() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	mm.mutex.Lock()
	mm.currentUsage = int64(m.Alloc)
	mm.mutex.Unlock()

	usageRatio := float64(mm.currentUsage) / float64(mm.maxMemoryBytes)

	// 超过阈值触发GC
	if usageRatio > mm.gcThreshold {
		runtime.GC()
		mm.addAlert("Memory usage high, triggered GC", mm.currentUsage)
	}

	// 超过限制发出警告
	if mm.currentUsage > mm.maxMemoryBytes {
		mm.addAlert("Memory usage exceeded limit", mm.currentUsage)
	}
}

// addAlert 添加告警
func (mm *MemoryManager) addAlert(message string, usage int64) {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	alert := MemoryAlert{
		Timestamp: time.Now(),
		Usage:     usage,
		Limit:     mm.maxMemoryBytes,
		Message:   message,
	}

	mm.alerts = append(mm.alerts, alert)

	// 限制告警数量
	if len(mm.alerts) > 100 {
		mm.alerts = mm.alerts[1:]
	}
}

// GetUsage 获取当前内存使用情况
func (mm *MemoryManager) GetUsage() (int64, float64) {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	usage := mm.currentUsage
	ratio := float64(usage) / float64(mm.maxMemoryBytes)
	return usage, ratio
}

// CanAllocate 检查是否可以分配指定大小的内存
func (mm *MemoryManager) CanAllocate(bytes int64) bool {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	return mm.currentUsage+bytes <= mm.maxMemoryBytes
}
