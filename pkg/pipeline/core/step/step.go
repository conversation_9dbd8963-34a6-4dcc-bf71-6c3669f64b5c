package step

import (
	"context"

	"admin/pkg/pipeline/meta"
)

// Step 步骤执行接口，定义了ETL处理步骤的核心行为
type Step interface {
	// Execute 执行步骤，处理输入数据并返回输出数据
	Execute(ctx context.Context, input RowSet) (RowSet, error)

	// GetMeta 获取步骤的元数据信息
	GetMeta() meta.StepMeta

	// Validate 验证步骤配置是否正确
	Validate() error

	// Initialize 初始化步骤，在执行前调用
	Initialize() error

	// Cleanup 清理步骤资源，在执行后调用
	Cleanup() error
}

// BaseStep 基础步骤实现，提供公共功能
type BaseStep struct {
	meta   meta.StepMeta
	config map[string]interface{}
}

// NewBaseStep 创建一个新的基础步骤
func NewBaseStep(stepMeta meta.StepMeta) *BaseStep {
	return &BaseStep{
		meta:   stepMeta,
		config: stepMeta.Config,
	}
}

func (bs *BaseStep) GetMeta() meta.StepMeta {
	return bs.meta
}

func (bs *BaseStep) Validate() error {
	// 基础验证逻辑
	if bs.meta.Name == "" {
		return meta.NewValidationError("step name is required")
	}
	if bs.meta.Type == "" {
		return meta.NewValidationError("step type is required")
	}
	return nil
}

func (bs *BaseStep) Initialize() error {
	// 基础初始化逻辑
	return nil
}

func (bs *BaseStep) Cleanup() error {
	// 基础清理逻辑
	return nil
}

// GetConfigString 获取字符串类型的配置值
func (bs *BaseStep) GetConfigString(key string) (string, bool) {
	if val, exists := bs.config[key]; exists {
		if s, ok := val.(string); ok {
			return s, true
		}
	}
	return "", false
}

// GetConfigInt 获取整数类型的配置值
func (bs *BaseStep) GetConfigInt(key string) (int64, bool) {
	if val, exists := bs.config[key]; exists {
		switch v := val.(type) {
		case int64:
			return v, true
		case int:
			return int64(v), true
		case float64:
			return int64(v), true
		}
	}
	return 0, false
}

// GetConfigBool 获取布尔类型的配置值
func (bs *BaseStep) GetConfigBool(key string) (bool, bool) {
	if val, exists := bs.config[key]; exists {
		if b, ok := val.(bool); ok {
			return b, true
		}
	}
	return false, false
}

// GetConfigMap 获取map类型的配置值
func (bs *BaseStep) GetConfigMap(key string) (map[string]interface{}, bool) {
	if val, exists := bs.config[key]; exists {
		if m, ok := val.(map[string]interface{}); ok {
			return m, true
		}
	}
	return nil, false
}

// GetConfigSlice 获取slice类型的配置值
func (bs *BaseStep) GetConfigSlice(key string) ([]interface{}, bool) {
	if val, exists := bs.config[key]; exists {
		if s, ok := val.([]interface{}); ok {
			return s, true
		}
	}
	return nil, false
}

// FieldType 字段类型
type FieldType string

const (
	StringType   FieldType = "String"
	IntegerType  FieldType = "Integer"
	FloatType    FieldType = "Float"
	BooleanType  FieldType = "Boolean"
	DateTimeType FieldType = "DateTime"
	BinaryType   FieldType = "Binary"
)

// Field 字段定义
type Field struct {
	Name   string    `json:"name"`
	Type   FieldType `json:"type"`
	Length int       `json:"length,omitempty"`
	Format string    `json:"format,omitempty"`
}

// Schema 数据模式定义
type Schema struct {
	Fields []Field `json:"fields"`
}
