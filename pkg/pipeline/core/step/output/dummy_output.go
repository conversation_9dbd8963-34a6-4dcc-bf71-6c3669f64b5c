package output

import (
	"context"
	"fmt"
	"log"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// DummyOutput 虚拟输出步骤，用于测试，打印接收到的数据
type DummyOutput struct {
	*step.BaseStep
	printRows  bool
	printStats bool
	rowCount   int
}

// NewDummyOutput 创建一个新的虚拟输出步骤
func NewDummyOutput(stepMeta meta.StepMeta) *DummyOutput {
	return &DummyOutput{
		BaseStep:   step.NewBaseStep(stepMeta),
		printRows:  true, // 默认打印行数据
		printStats: true, // 默认打印统计信息
		rowCount:   0,
	}
}

// Initialize 初始化步骤
func (d *DummyOutput) Initialize() error {
	if err := d.BaseStep.Initialize(); err != nil {
		return err
	}

	// 从配置中获取是否打印行数据
	if printRows, exists := d.GetConfigBool("print_rows"); exists {
		d.printRows = printRows
	}

	// 从配置中获取是否打印统计信息
	if printStats, exists := d.GetConfigBool("print_stats"); exists {
		d.printStats = printStats
	}

	d.rowCount = 0
	return nil
}

// Execute 执行步骤，处理输入数据
func (d *DummyOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	if input == nil {
		log.Printf("[DummyOutput:%s] Received nil input", d.GetMeta().Name)
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	defer input.Close()

	d.rowCount = 0

	// 遍历输入数据
	for {
		row, hasMore, err := input.Next()
		if err != nil {
			return step.NewEmptyRowSet([]meta.FieldMeta{}), fmt.Errorf("input error: %w", err)
		}
		if !hasMore {
			break
		}

		d.rowCount++

		// 检查是否需要取消
		select {
		case <-ctx.Done():
			return step.NewEmptyRowSet([]meta.FieldMeta{}), ctx.Err()
		default:
		}

		// 打印行数据
		if d.printRows && row != nil {
			d.printRow(row)
		}
	}

	// 打印统计信息
	if d.printStats {
		log.Printf("[DummyOutput:%s] Processed %d rows", d.GetMeta().Name, d.rowCount)
	}

	// DummyOutput不产生输出数据，返回空的RowSet
	return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
}

// Validate 验证步骤配置
func (d *DummyOutput) Validate() error {
	return d.BaseStep.Validate()
}

// printRow 打印单行数据
func (d *DummyOutput) printRow(row step.Row) {
	if row == nil {
		log.Printf("[DummyOutput:%s] Row %d: <nil>", d.GetMeta().Name, d.rowCount)
		return
	}

	// 获取字段列表
	fields := row.Fields()
	if len(fields) == 0 {
		log.Printf("[DummyOutput:%s] Row %d: <empty>", d.GetMeta().Name, d.rowCount)
		return
	}

	// 构建行数据字符串
	rowStr := fmt.Sprintf("[DummyOutput:%s] Row %d: {", d.GetMeta().Name, d.rowCount)
	for i, field := range fields {
		value := row.Get(field)
		if i > 0 {
			rowStr += ", "
		}
		rowStr += fmt.Sprintf("%s: %v", field, value)
	}
	rowStr += "}"

	log.Printf("%s", rowStr)
}

// GetProcessedRowCount 获取已处理的行数（用于测试和统计）
func (d *DummyOutput) GetProcessedRowCount() int {
	return d.rowCount
}
