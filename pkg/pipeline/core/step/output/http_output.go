package output

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// HTTPOutput HTTP输出组件
type HTTPOutput struct {
	*EnhancedOutputBase

	// HTTP配置
	url           string // 目标URL
	method        string // HTTP方法
	contentType   string // 内容类型
	requestFormat string // 请求格式: json, form, xml

	// 认证配置
	authType     string            // 认证类型: none, basic, bearer, api_key
	username     string            // 用户名
	password     string            // 密码
	token        string            // 令牌
	apiKey       string            // API密钥
	apiKeyHeader string            // API密钥头名称
	headers      map[string]string // 自定义头

	// 响应处理
	responseFormat    string // 响应格式: json, xml, text
	successStatusCode int    // 成功状态码
	retryStatusCodes  []int  // 重试状态码
	// HTTP客户端
	client *http.Client // HTTP客户端
}

// NewHTTPOutput 创建HTTP输出组件
func NewHTTPOutput(stepMeta meta.StepMeta) *HTTPOutput {
	return &HTTPOutput{
		EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
		method:             "POST",
		contentType:        "application/json",
		requestFormat:      "json",
		responseFormat:     "json",
		client:             &http.Client{Timeout: 30 * time.Second},
	}
}

// Initialize 初始化HTTP输出组件
func (ho *HTTPOutput) Initialize() error {
	if err := ho.EnhancedOutputBase.Initialize(); err != nil {
		return err
	}

	// 解析配置
	if err := ho.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse HTTP config: %w", err)
	}

	// 配置HTTP客户端
	ho.client.Timeout = ho.timeout

	return nil
}

// Execute 执行HTTP输出
func (ho *HTTPOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	ho.metrics.StartTime = time.Now()
	defer func() {
		ho.metrics.EndTime = time.Now()
		ho.updateThroughputMetrics()
	}()

	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	// 收集所有数据
	var rows []map[string]interface{}
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			ho.recordError(fmt.Errorf("input error: %w", err))
			return nil, err
		}

		if row == nil {
			break
		}

		ho.metrics.InputRows++

		// 转换行数据为map
		rowData := make(map[string]interface{})
		for _, field := range row.Fields() {
			rowData[field] = row.Get(field)
		}
		rows = append(rows, rowData)
	}

	// 发送HTTP请求
	if err := ho.sendRequest(ctx, rows); err != nil {
		ho.recordError(fmt.Errorf("failed to send HTTP request: %w", err))
		return nil, err
	}

	ho.metrics.OutputRows = ho.metrics.InputRows

	return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
}

// sendRequest 发送HTTP请求
func (ho *HTTPOutput) sendRequest(ctx context.Context, data []map[string]interface{}) error {
	// 准备请求体
	var requestBody []byte
	var err error

	switch ho.requestFormat {
	case "json":
		requestBody, err = json.Marshal(data)
		if err != nil {
			return fmt.Errorf("failed to marshal JSON: %w", err)
		}
	default:
		return fmt.Errorf("unsupported request format: %s", ho.requestFormat)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, ho.method, ho.url, bytes.NewReader(requestBody))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置内容类型
	req.Header.Set("Content-Type", ho.contentType)

	// 设置认证
	if err := ho.setAuthentication(req); err != nil {
		return fmt.Errorf("failed to set authentication: %w", err)
	}

	// 设置自定义头
	for key, value := range ho.headers {
		req.Header.Set(key, value)
	}

	// 发送请求
	resp, err := ho.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if ho.successStatusCode != 0 && resp.StatusCode != ho.successStatusCode {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	return nil
}

// setAuthentication 设置认证
func (ho *HTTPOutput) setAuthentication(req *http.Request) error {
	switch ho.authType {
	case "basic":
		if ho.username != "" && ho.password != "" {
			req.SetBasicAuth(ho.username, ho.password)
		}
	case "bearer":
		if ho.token != "" {
			req.Header.Set("Authorization", "Bearer "+ho.token)
		}
	case "api_key":
		if ho.apiKey != "" {
			headerName := ho.apiKeyHeader
			if headerName == "" {
				headerName = "X-API-Key"
			}
			req.Header.Set(headerName, ho.apiKey)
		}
	}
	return nil
}

// parseConfig 解析HTTP配置
func (ho *HTTPOutput) parseConfig() error {
	ho.url, _ = ho.GetConfigString("url")
	ho.method, _ = ho.GetConfigString("method")
	ho.contentType, _ = ho.GetConfigString("content_type")
	ho.requestFormat, _ = ho.GetConfigString("request_format")
	ho.responseFormat, _ = ho.GetConfigString("response_format")

	// 认证配置
	ho.authType, _ = ho.GetConfigString("auth_type")
	ho.username, _ = ho.GetConfigString("username")
	ho.password, _ = ho.GetConfigString("password")
	ho.token, _ = ho.GetConfigString("token")
	ho.apiKey, _ = ho.GetConfigString("api_key")
	ho.apiKeyHeader, _ = ho.GetConfigString("api_key_header")

	// 自定义头
	if headers, exists := ho.GetConfigMap("headers"); exists {
		ho.headers = make(map[string]string)
		for key, value := range headers {
			if valueStr, ok := value.(string); ok {
				ho.headers[key] = valueStr
			}
		}
	}

	// 状态码配置
	if successCode, exists := ho.GetConfigInt("success_status_code"); exists {
		ho.successStatusCode = int(successCode)
	}

	return nil
}
