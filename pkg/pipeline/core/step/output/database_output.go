package output

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// DatabaseOutput 数据库输出组件
type DatabaseOutput struct {
	*EnhancedOutputBase

	// 数据库连接
	db         *sql.DB // 数据库连接
	dsn        string  // 数据源名称
	driverName string  // 驱动名称

	// 表配置
	tableName     string // 目标表名
	schema        string // 模式名
	createTable   bool   // 是否自动创建表
	truncateTable bool   // 是否截断表

	// 插入配置
	insertMode   string   // 插入模式: insert, upsert, update
	conflictKeys []string // 冲突键（用于upsert）
	updateFields []string // 更新字段

	// 性能优化
	useTransaction bool      // 是否使用事务
	useBulkInsert  bool      // 是否使用批量插入
	preparedStmt   *sql.Stmt // 预编译语句
}

// NewDatabaseOutput 创建数据库输出组件
func NewDatabaseOutput(stepMeta meta.StepMeta) *DatabaseOutput {
	return &DatabaseOutput{
		EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
		insertMode:         "insert",
		useTransaction:     true,
		useBulkInsert:      true,
	}
}

// Initialize 初始化数据库输出组件
func (do *DatabaseOutput) Initialize() error {
	if err := do.EnhancedOutputBase.Initialize(); err != nil {
		return err
	}

	// 解析配置
	if err := do.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse database config: %w", err)
	}

	// 建立数据库连接
	if err := do.connect(); err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 准备表
	if err := do.prepareTable(); err != nil {
		return fmt.Errorf("failed to prepare table: %w", err)
	}

	return nil
}

// Execute 执行数据库输出
func (do *DatabaseOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	do.metrics.StartTime = time.Now()
	defer func() {
		do.metrics.EndTime = time.Now()
		do.updateThroughputMetrics()
		if do.preparedStmt != nil {
			do.preparedStmt.Close()
		}
	}()

	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	// 准备插入语句
	if err := do.prepareInsertStatement(input.Schema()); err != nil {
		return nil, fmt.Errorf("failed to prepare insert statement: %w", err)
	}

	var tx *sql.Tx
	var err error

	// 开启事务（如果启用）
	if do.useTransaction {
		tx, err = do.db.BeginTx(ctx, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to begin transaction: %w", err)
		}
		defer func() {
			if err != nil {
				err := tx.Rollback()
				if err != nil {
					do.recordError(fmt.Errorf("failed to rollback transaction: %w", err))
				}
			} else {
				err := tx.Commit()
				if err != nil {
					do.recordError(fmt.Errorf("failed to commit transaction: %w", err))
				}
			}
		}()
	}

	// 处理数据行
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			do.recordError(fmt.Errorf("input error: %w", err))
			return nil, err
		}

		if row == nil {
			break
		}

		do.metrics.InputRows++

		// 执行插入
		if err := do.insertRow(ctx, row, input.Schema()); err != nil {
			do.recordError(fmt.Errorf("failed to insert row: %w", err))
			do.metrics.ErrorRows++

			if do.errorHandling == "abort" {
				return nil, err
			}
			continue
		}

		do.metrics.OutputRows++
	}

	return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
}

// parseConfig 解析数据库配置
func (do *DatabaseOutput) parseConfig() error {
	do.dsn, _ = do.GetConfigString("dsn")
	do.driverName, _ = do.GetConfigString("driver")
	do.tableName, _ = do.GetConfigString("table")
	do.schema, _ = do.GetConfigString("schema")
	do.insertMode, _ = do.GetConfigString("insert_mode")
	do.createTable, _ = do.GetConfigBool("create_table")
	do.truncateTable, _ = do.GetConfigBool("truncate_table")
	do.useTransaction, _ = do.GetConfigBool("use_transaction")
	do.useBulkInsert, _ = do.GetConfigBool("use_bulk_insert")

	// 解析冲突键和更新字段
	if conflictKeys, exists := do.GetConfigSlice("conflict_keys"); exists {
		for _, key := range conflictKeys {
			if keyStr, ok := key.(string); ok {
				do.conflictKeys = append(do.conflictKeys, keyStr)
			}
		}
	}

	if updateFields, exists := do.GetConfigSlice("update_fields"); exists {
		for _, field := range updateFields {
			if fieldStr, ok := field.(string); ok {
				do.updateFields = append(do.updateFields, fieldStr)
			}
		}
	}

	return nil
}

// connect 建立数据库连接
func (do *DatabaseOutput) connect() error {
	var err error
	do.db, err = sql.Open(do.driverName, do.dsn)
	if err != nil {
		return err
	}

	// 测试连接
	if err := do.db.Ping(); err != nil {
		return err
	}

	return nil
}

// prepareTable 准备目标表
func (do *DatabaseOutput) prepareTable() error {
	// 如果需要截断表
	if do.truncateTable {
		query := fmt.Sprintf("TRUNCATE TABLE %s", do.tableName)
		if _, err := do.db.Exec(query); err != nil {
			return fmt.Errorf("failed to truncate table: %w", err)
		}
	}

	return nil
}

// prepareInsertStatement 准备插入语句
func (do *DatabaseOutput) prepareInsertStatement(schema []meta.FieldMeta) error {
	if do.preparedStmt != nil {
		return nil // 已经准备过了
	}

	fields := make([]string, len(schema))
	placeholders := make([]string, len(schema))

	for i, field := range schema {
		fields[i] = "`" + field.Name + "`"
		placeholders[i] = "?"
	}

	fieldStr := ""
	placeholderStr := ""
	for i := range fields {
		if i > 0 {
			fieldStr += ", "
			placeholderStr += ", "
		}
		fieldStr += fields[i]
		placeholderStr += placeholders[i]
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		do.tableName, fieldStr, placeholderStr)

	var err error
	do.preparedStmt, err = do.db.Prepare(query)
	return err
}

// insertRow 插入单行数据
func (do *DatabaseOutput) insertRow(ctx context.Context, row step.Row, schema []meta.FieldMeta) error {
	values := make([]interface{}, len(schema))
	for i, field := range schema {
		values[i] = row.Get(field.Name)
	}

	var err error
	if do.preparedStmt != nil {
		_, err = do.preparedStmt.ExecContext(ctx, values...)
	} else {
		// 直接执行（不推荐，但作为后备方案）
		fields := make([]string, len(schema))
		placeholders := make([]string, len(schema))
		for i, field := range schema {
			fields[i] = "`" + field.Name + "`"
			placeholders[i] = "?"
		}
		query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
			do.tableName,
			fmt.Sprintf("%s", fields[0]),
			placeholders[0])

		// 重新构建查询
		fieldStr := ""
		placeholderStr := ""
		for i, field := range fields {
			if i > 0 {
				fieldStr += ", "
				placeholderStr += ", "
			}
			fieldStr += field
			placeholderStr += "?"
		}
		query = fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
			do.tableName, fieldStr, placeholderStr)

		_, err = do.db.ExecContext(ctx, query, values...)
	}

	return err
}
