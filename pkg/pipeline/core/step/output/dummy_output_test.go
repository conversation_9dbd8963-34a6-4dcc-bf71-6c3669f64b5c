package output

import (
	"context"
	"testing"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDummyOutput_Execute(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name: "test_dummy_output",
		Type: meta.StepTypeDummyOutput,
		Config: map[string]interface{}{
			"print_rows":  true,
			"print_stats": true,
		},
	}
	dummyOutput := NewDummyOutput(stepMeta)
	err := dummyOutput.Initialize()
	assert.NoError(t, err)

	// 创建测试数据
	testData := []map[string]interface{}{
		{"id": 1, "name": "Alice", "age": 25},
		{"id": 2, "name": "Bob", "age": 30},
	}
	schema := []meta.FieldMeta{
		{Name: "id", Type: meta.FieldTypeInteger},
		{Name: "name", Type: meta.FieldTypeString},
		{Name: "age", Type: meta.FieldTypeInteger},
	}
	inputRowSet := step.NewMemoryRowSet(testData, schema)

	// 执行
	ctx := context.Background()
	output, err := dummyOutput.Execute(ctx, inputRowSet)
	assert.NoError(t, err)

	// 验证输出为空（DummyOutput不产生输出）
	row, hasMore, err := output.Next()
	assert.NoError(t, err)
	assert.False(t, hasMore)
	assert.Nil(t, row)

	output.Close()
}

func TestDummyOutput_EmptyInput(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name:   "test_dummy_output_empty",
		Type:   meta.StepTypeDummyOutput,
		Config: map[string]interface{}{},
	}
	dummyOutput := NewDummyOutput(stepMeta)
	err := dummyOutput.Initialize()
	require.NoError(t, err)

	// 测试空输入
	schema := []meta.FieldMeta{
		{Name: "id", Type: meta.FieldTypeInteger},
	}
	emptyRowSet := step.NewEmptyRowSet(schema)

	ctx := context.Background()
	output, err := dummyOutput.Execute(ctx, emptyRowSet)
	require.NoError(t, err)
	require.NotNil(t, output)

	defer output.Close()
	row, hasMore, err := output.Next()
	assert.NoError(t, err)
	assert.False(t, hasMore)
	assert.Nil(t, row)
}

func TestDummyOutput_Configuration(t *testing.T) {
	tests := []struct {
		name          string
		config        map[string]interface{}
		expectedPrint bool
		expectedStats bool
	}{
		{
			name:          "default config",
			config:        map[string]interface{}{},
			expectedPrint: true,
			expectedStats: true,
		},
		{
			name: "disable print rows",
			config: map[string]interface{}{
				"print_rows": false,
			},
			expectedPrint: false,
			expectedStats: true,
		},
		{
			name: "disable print stats",
			config: map[string]interface{}{
				"print_stats": false,
			},
			expectedPrint: true,
			expectedStats: false,
		},
		{
			name: "disable both",
			config: map[string]interface{}{
				"print_rows":  false,
				"print_stats": false,
			},
			expectedPrint: false,
			expectedStats: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stepMeta := meta.StepMeta{
				Name:   "test_dummy_output_config",
				Type:   meta.StepTypeDummyOutput,
				Config: tt.config,
			}
			dummyOutput := NewDummyOutput(stepMeta)
			err := dummyOutput.Initialize()
			require.NoError(t, err)

			assert.Equal(t, tt.expectedPrint, dummyOutput.printRows)
			assert.Equal(t, tt.expectedStats, dummyOutput.printStats)
		})
	}
}

func TestDummyOutput_Validate(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name:   "test_dummy_output_validate",
		Type:   meta.StepTypeDummyOutput,
		Config: map[string]interface{}{},
	}
	dummyOutput := NewDummyOutput(stepMeta)
	err := dummyOutput.Validate()
	assert.NoError(t, err)
}

func TestDummyOutput_CancelContext(t *testing.T) {
	stepMeta := meta.StepMeta{
		Name:   "test_dummy_output_cancel",
		Type:   meta.StepTypeDummyOutput,
		Config: map[string]interface{}{},
	}
	dummyOutput := NewDummyOutput(stepMeta)
	err := dummyOutput.Initialize()
	require.NoError(t, err)

	// 创建测试数据
	testData := []map[string]interface{}{
		{"id": 1},
		{"id": 2},
	}
	schema := []meta.FieldMeta{
		{Name: "id", Type: meta.FieldTypeInteger},
	}
	inputRowSet := step.NewMemoryRowSet(testData, schema)

	// 创建可取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // 立即取消

	output, err := dummyOutput.Execute(ctx, inputRowSet)

	// 应该返回取消错误或者正常输出
	if err != nil {
		assert.Equal(t, context.Canceled, err)
	} else {
		assert.NotNil(t, output)
		output.Close()
	}
}
