package output

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// JSONOutput JSON文件输出组件
type JSONOutput struct {
	*FileOutput

	// JSON配置
	jsonFormat  string // JSON格式: lines, array, object
	prettyPrint bool   // 是否美化输出
	rootKey     string // 根键名（object格式）

	// JSON状态
	firstRecord  bool // 是否第一条记录
	arrayStarted bool // 数组是否已开始
}

// NewJSONOutput 创建JSON输出组件
func NewJSONOutput(stepMeta meta.StepMeta) *JSONOutput {
	return &JSONOutput{
		FileOutput: &FileOutput{
			EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
			encoding:           "utf-8",
		},
		jsonFormat:  "lines",
		prettyPrint: false,
		firstRecord: true,
	}
}

// Initialize 初始化JSON输出组件
func (jo *JSONOutput) Initialize() error {
	if err := jo.FileOutput.Initialize(); err != nil {
		return err
	}

	// 解析配置
	if err := jo.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse JSON config: %w", err)
	}

	// 打开文件
	if err := jo.openFile(); err != nil {
		return fmt.Errorf("failed to open JSON file: %w", err)
	}

	// 根据JSON格式初始化
	if jo.jsonFormat == "array" {
		if _, err := jo.writer.Write([]byte("[")); err != nil {
			return fmt.Errorf("failed to write JSON array start: %w", err)
		}
		jo.arrayStarted = true
	} else if jo.jsonFormat == "object" && jo.rootKey != "" {
		if _, err := fmt.Fprintf(jo.writer, `{"%s": [`, jo.rootKey); err != nil {
			return fmt.Errorf("failed to write JSON object start: %w", err)
		}
		jo.arrayStarted = true
	}

	return nil
}

// Execute 执行JSON输出
func (jo *JSONOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	jo.metrics.StartTime = time.Now()
	defer func() {
		jo.metrics.EndTime = time.Now()
		jo.updateThroughputMetrics()
		jo.finalize()
		if jo.file != nil {
			jo.file.Close()
		}
	}()

	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	// 处理数据行
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			jo.recordError(fmt.Errorf("input error: %w", err))
			return nil, err
		}

		if row == nil {
			break
		}

		jo.metrics.InputRows++

		// 转换行数据为JSON对象
		rowData := make(map[string]interface{})
		for _, field := range row.Fields() {
			rowData[field] = row.Get(field)
		}

		// 序列化为JSON
		var jsonData []byte
		if jo.prettyPrint {
			jsonData, err = json.MarshalIndent(rowData, "", "  ")
		} else {
			jsonData, err = json.Marshal(rowData)
		}

		if err != nil {
			jo.recordError(fmt.Errorf("failed to marshal JSON: %w", err))
			jo.metrics.ErrorRows++

			if jo.errorHandling == "abort" {
				return nil, err
			}
			continue
		}

		// 写入JSON数据
		if err := jo.writeJSONRecord(jsonData); err != nil {
			jo.recordError(fmt.Errorf("failed to write JSON record: %w", err))
			jo.metrics.ErrorRows++

			if jo.errorHandling == "abort" {
				return nil, err
			}
			continue
		}

		jo.metrics.OutputRows++
		jo.firstRecord = false
	}

	return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
}

// writeJSONRecord 写入JSON记录
func (jo *JSONOutput) writeJSONRecord(jsonData []byte) error {
	switch jo.jsonFormat {
	case "lines":
		// JSON Lines格式：每行一个JSON对象
		_, err := jo.writer.Write(jsonData)
		if err != nil {
			return err
		}
		_, err = jo.writer.Write([]byte("\n"))
		return err

	case "array":
		// JSON数组格式
		if !jo.firstRecord {
			if _, err := jo.writer.Write([]byte(",")); err != nil {
				return err
			}
		}
		_, err := jo.writer.Write(jsonData)
		return err

	case "object":
		// JSON对象格式（带根键）
		if !jo.firstRecord {
			if _, err := jo.writer.Write([]byte(",")); err != nil {
				return err
			}
		}
		_, err := jo.writer.Write(jsonData)
		return err

	default:
		return fmt.Errorf("unsupported JSON format: %s", jo.jsonFormat)
	}
}

// finalize 完成JSON输出
func (jo *JSONOutput) finalize() {
	if jo.jsonFormat == "array" && jo.arrayStarted {
		if _, err := jo.writer.Write([]byte("]")); err != nil {
			jo.recordError(fmt.Errorf("failed to write JSON array end: %w", err))
		}
	} else if jo.jsonFormat == "object" && jo.arrayStarted {
		if _, err := jo.writer.Write([]byte("]}")); err != nil {
			jo.recordError(fmt.Errorf("failed to write JSON object end: %w", err))
		}
	}
}

// parseConfig 解析JSON配置
func (jo *JSONOutput) parseConfig() error {
	// 支持两种配置键名：filename 和 file_path
	if filename, exists := jo.GetConfigString("filename"); exists && filename != "" {
		jo.filePath = filename
	} else {
		jo.filePath, _ = jo.GetConfigString("file_path")
	}

	jo.jsonFormat, _ = jo.GetConfigString("format")
	jo.prettyPrint, _ = jo.GetConfigBool("pretty_print")
	jo.rootKey, _ = jo.GetConfigString("root_key")
	jo.append, _ = jo.GetConfigBool("append")
	return nil
}
