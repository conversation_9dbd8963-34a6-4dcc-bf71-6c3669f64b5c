package output

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// ElasticsearchOutput Elasticsearch输出组件
type ElasticsearchOutput struct {
	*EnhancedOutputBase

	// Elasticsearch配置
	hosts     []string // Elasticsearch节点地址
	index     string   // 索引名称
	indexType string   // 文档类型（ES7之前使用）

	// 认证配置
	username string // 用户名
	password string // 密码
	apiKey   string // API密钥

	// 索引配置
	createIndex   bool                   // 是否自动创建索引
	indexSettings map[string]interface{} // 索引设置
	indexMappings map[string]interface{} // 索引映射
	refreshPolicy string                 // 刷新策略: true, false, wait_for

	// 文档配置
	idField      string // 文档ID字段
	versionField string // 版本字段
	routingField string // 路由字段

	// 批量操作
	bulkSize   int                      // 批量操作大小
	enableBulk bool                     // 是否启用批量操作
	bulkBuffer []map[string]interface{} // 批量缓冲区

	// HTTP客户端
	client *http.Client // HTTP客户端
}

// NewElasticsearchOutput 创建Elasticsearch输出组件
func NewElasticsearchOutput(stepMeta meta.StepMeta) *ElasticsearchOutput {
	return &ElasticsearchOutput{
		EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
		hosts:              []string{"http://localhost:9200"},
		refreshPolicy:      "false",
		bulkSize:           1000,
		enableBulk:         true,
		client:             &http.Client{Timeout: 30 * time.Second},
	}
}

// Initialize 初始化Elasticsearch输出组件
func (eo *ElasticsearchOutput) Initialize() error {
	if err := eo.EnhancedOutputBase.Initialize(); err != nil {
		return err
	}

	// 解析配置
	if err := eo.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse Elasticsearch config: %w", err)
	}

	// 配置HTTP客户端
	eo.client.Timeout = eo.timeout

	// 创建索引（如果需要）
	if eo.createIndex {
		if err := eo.ensureIndex(); err != nil {
			return fmt.Errorf("failed to create index: %w", err)
		}
	}

	return nil
}

// Execute 执行Elasticsearch输出
func (eo *ElasticsearchOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	eo.metrics.StartTime = time.Now()
	defer func() {
		eo.metrics.EndTime = time.Now()
		eo.updateThroughputMetrics()

		// 处理剩余的批量数据
		if len(eo.bulkBuffer) > 0 {
			err := eo.flushBulk(ctx)
			if err != nil {
				eo.recordError(fmt.Errorf("failed to flush remaining bulk data: %w", err))
				if eo.errorHandling == "abort" {
					return
				}
			}
		}
	}()

	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	// 处理数据行
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			eo.recordError(fmt.Errorf("input error: %w", err))
			return nil, err
		}

		if row == nil {
			break
		}

		eo.metrics.InputRows++

		// 转换行数据为文档
		doc := make(map[string]interface{})
		for _, field := range row.Fields() {
			doc[field] = row.Get(field)
		}

		// 添加到批量缓冲区或直接索引
		if eo.enableBulk {
			eo.bulkBuffer = append(eo.bulkBuffer, doc)

			// 如果批量缓冲区已满，执行批量操作
			if len(eo.bulkBuffer) >= eo.bulkSize {
				if err := eo.flushBulk(ctx); err != nil {
					eo.recordError(fmt.Errorf("failed to flush bulk: %w", err))
					if eo.errorHandling == "abort" {
						return nil, err
					}
				}
			}
		} else {
			// 单个文档索引
			if err := eo.indexDocument(ctx, doc); err != nil {
				eo.recordError(fmt.Errorf("failed to index document: %w", err))
				eo.metrics.ErrorRows++

				if eo.errorHandling == "abort" {
					return nil, err
				}
				continue
			}
		}

		eo.metrics.OutputRows++
	}

	return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
}

// indexDocument 索引单个文档
func (eo *ElasticsearchOutput) indexDocument(ctx context.Context, doc map[string]interface{}) error {
	// 准备请求URL
	url := fmt.Sprintf("%s/%s/_doc", eo.hosts[0], eo.index)

	// 如果指定了ID字段，使用PUT方法
	if eo.idField != "" {
		if id, exists := doc[eo.idField]; exists {
			url = fmt.Sprintf("%s/%s/_doc/%v", eo.hosts[0], eo.index, id)
		}
	}

	// 序列化文档
	docData, err := json.Marshal(doc)
	if err != nil {
		return fmt.Errorf("failed to marshal document: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(docData))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if eo.refreshPolicy != "" {
		req.URL.RawQuery = fmt.Sprintf("refresh=%s", eo.refreshPolicy)
	}

	// 设置认证
	eo.setAuthentication(req)

	// 发送请求
	resp, err := eo.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("indexing failed with status: %d", resp.StatusCode)
	}

	return nil
}

// flushBulk 执行批量操作
func (eo *ElasticsearchOutput) flushBulk(ctx context.Context) error {
	if len(eo.bulkBuffer) == 0 {
		return nil
	}

	// 构建批量请求体
	var bulkBody strings.Builder
	for _, doc := range eo.bulkBuffer {
		// 操作元数据
		action := map[string]interface{}{
			"index": map[string]interface{}{
				"_index": eo.index,
			},
		}

		// 如果指定了ID字段
		if eo.idField != "" {
			if id, exists := doc[eo.idField]; exists {
				action["index"].(map[string]interface{})["_id"] = id
			}
		}

		actionData, _ := json.Marshal(action)
		bulkBody.Write(actionData)
		bulkBody.WriteString("\n")

		// 文档数据
		docData, _ := json.Marshal(doc)
		bulkBody.Write(docData)
		bulkBody.WriteString("\n")
	}

	// 创建批量请求
	url := fmt.Sprintf("%s/_bulk", eo.hosts[0])
	req, err := http.NewRequestWithContext(ctx, "POST", url, strings.NewReader(bulkBody.String()))
	if err != nil {
		return fmt.Errorf("failed to create bulk request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-ndjson")
	if eo.refreshPolicy != "" {
		req.URL.RawQuery = fmt.Sprintf("refresh=%s", eo.refreshPolicy)
	}

	// 设置认证
	eo.setAuthentication(req)

	// 发送请求
	resp, err := eo.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send bulk request: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("bulk operation failed with status: %d", resp.StatusCode)
	}

	// 清空批量缓冲区
	eo.bulkBuffer = eo.bulkBuffer[:0]

	return nil
}

// ensureIndex 确保索引存在
func (eo *ElasticsearchOutput) ensureIndex() error {
	// 检查索引是否存在
	url := fmt.Sprintf("%s/%s", eo.hosts[0], eo.index)
	req, err := http.NewRequest("HEAD", url, nil)
	if err != nil {
		return err
	}

	eo.setAuthentication(req)

	resp, err := eo.client.Do(req)
	if err != nil {
		return err
	}
	resp.Body.Close()

	// 如果索引存在，直接返回
	if resp.StatusCode == 200 {
		return nil
	}

	// 创建索引
	indexConfig := map[string]interface{}{}
	if eo.indexSettings != nil {
		indexConfig["settings"] = eo.indexSettings
	}
	if eo.indexMappings != nil {
		indexConfig["mappings"] = eo.indexMappings
	}

	configData, err := json.Marshal(indexConfig)
	if err != nil {
		return err
	}

	req, err = http.NewRequest("PUT", url, bytes.NewReader(configData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	eo.setAuthentication(req)

	resp, err = eo.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("failed to create index with status: %d", resp.StatusCode)
	}

	return nil
}

// setAuthentication 设置认证
func (eo *ElasticsearchOutput) setAuthentication(req *http.Request) {
	if eo.username != "" && eo.password != "" {
		req.SetBasicAuth(eo.username, eo.password)
	} else if eo.apiKey != "" {
		req.Header.Set("Authorization", "ApiKey "+eo.apiKey)
	}
}

// parseConfig 解析Elasticsearch配置
func (eo *ElasticsearchOutput) parseConfig() error {
	// 主机配置
	if hosts, exists := eo.GetConfigSlice("hosts"); exists {
		eo.hosts = []string{}
		for _, host := range hosts {
			if hostStr, ok := host.(string); ok {
				eo.hosts = append(eo.hosts, hostStr)
			}
		}
	}

	eo.index, _ = eo.GetConfigString("index")
	eo.indexType, _ = eo.GetConfigString("type")

	// 认证配置
	eo.username, _ = eo.GetConfigString("username")
	eo.password, _ = eo.GetConfigString("password")
	eo.apiKey, _ = eo.GetConfigString("api_key")

	// 索引配置
	eo.createIndex, _ = eo.GetConfigBool("create_index")
	eo.refreshPolicy, _ = eo.GetConfigString("refresh_policy")

	// 文档配置
	eo.idField, _ = eo.GetConfigString("id_field")
	eo.versionField, _ = eo.GetConfigString("version_field")
	eo.routingField, _ = eo.GetConfigString("routing_field")

	// 批量操作配置
	if bulkSize, exists := eo.GetConfigInt("bulk_size"); exists {
		eo.bulkSize = int(bulkSize)
	}
	eo.enableBulk, _ = eo.GetConfigBool("enable_bulk")

	// 索引设置和映射
	if settings, exists := eo.GetConfigMap("index_settings"); exists {
		eo.indexSettings = settings
	}

	if mappings, exists := eo.GetConfigMap("index_mappings"); exists {
		eo.indexMappings = mappings
	}

	return nil
}
