package output

import (
	"context"
	"encoding/csv"
	"fmt"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// CSVOutput CSV文件输出组件
type CSVOutput struct {
	*FileOutput

	// CSV配置
	delimiter     rune // 分隔符
	quote         rune // 引用符
	writeHeader   bool // 是否写入标题行
	headerWritten bool // 标题是否已写入

	// CSV写入器
	csvWriter *csv.Writer // CSV写入器
}

// NewCSVOutput 创建CSV输出组件
func NewCSVOutput(stepMeta meta.StepMeta) *CSVOutput {
	return &CSVOutput{
		FileOutput: &FileOutput{
			EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
			encoding:           "utf-8",
		},
		delimiter:   ',',
		quote:       '"',
		writeHeader: true,
	}
}

// Initialize 初始化CSV输出组件
func (co *CSVOutput) Initialize() error {
	if err := co.FileOutput.Initialize(); err != nil {
		return err
	}

	// 解析配置
	if err := co.parseConfig(); err != nil {
		return fmt.Errorf("failed to parse CSV config: %w", err)
	}

	// 打开文件
	if err := co.openFile(); err != nil {
		return fmt.Errorf("failed to open CSV file: %w", err)
	}

	// 创建CSV写入器
	co.csvWriter = csv.NewWriter(co.writer)
	co.csvWriter.Comma = co.delimiter
	// Note: csv.Writer 不支持自定义 Quote 字符，使用默认的双引号

	return nil
}

// Execute 执行CSV输出
func (co *CSVOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
	co.metrics.StartTime = time.Now()
	defer func() {
		co.metrics.EndTime = time.Now()
		co.updateThroughputMetrics()
		co.csvWriter.Flush()
		if co.file != nil {
			co.file.Close()
		}
	}()

	if input == nil {
		return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
	}

	// 写入标题行
	if co.writeHeader && !co.headerWritten {
		schema := input.Schema()
		headers := make([]string, len(schema))
		for i, field := range schema {
			headers[i] = field.Name
		}

		if err := co.csvWriter.Write(headers); err != nil {
			return nil, fmt.Errorf("failed to write CSV header: %w", err)
		}
		co.headerWritten = true
	}

	// 处理数据行
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		row, _, err := input.Next()
		if err != nil {
			co.recordError(fmt.Errorf("input error: %w", err))
			return nil, err
		}

		if row == nil {
			break
		}

		co.metrics.InputRows++

		// 转换行数据为字符串切片，使用schema定义的字段顺序
		schema := input.Schema()
		record := make([]string, len(schema))
		for i, fieldMeta := range schema {
			value := row.Get(fieldMeta.Name)
			record[i] = fmt.Sprintf("%v", value)
		}

		// 写入CSV记录
		if err := co.csvWriter.Write(record); err != nil {
			co.recordError(fmt.Errorf("failed to write CSV record: %w", err))
			co.metrics.ErrorRows++

			if co.errorHandling == "abort" {
				return nil, err
			}
			continue
		}

		co.metrics.OutputRows++
	}

	return step.NewEmptyRowSet([]meta.FieldMeta{}), nil
}

// parseConfig 解析CSV配置
func (co *CSVOutput) parseConfig() error {
	// 支持两种配置键名：filename 和 file_path
	if filename, exists := co.GetConfigString("filename"); exists && filename != "" {
		co.filePath = filename
	} else {
		co.filePath, _ = co.GetConfigString("file_path")
	}

	if delimiter, exists := co.GetConfigString("delimiter"); exists && delimiter != "" {
		co.delimiter = rune(delimiter[0])
	}
	if quote, exists := co.GetConfigString("quote"); exists && quote != "" {
		co.quote = rune(quote[0])
	}
	// 支持两种配置键名：write_header 和 include_header
	if writeHeader, exists := co.GetConfigBool("write_header"); exists {
		co.writeHeader = writeHeader
	} else if includeHeader, exists := co.GetConfigBool("include_header"); exists {
		co.writeHeader = includeHeader
	}
	co.append, _ = co.GetConfigBool("append")
	return nil
}
