# Output Components

这个包包含了ETL流程中的各种输出组件。为了更好的代码组织和维护性，每个输出类型都被放在独立的文件中。

## 文件结构

### 基础组件
- **`base_output.go`** - 基础输出组件和公共功能
  - `EnhancedOutputBase` - 增强输出基类
  - `OutputMetrics` - 输出指标结构
  - `FileOutput` - 文件输出基类

### 具体输出组件
- **`csv_output.go`** - CSV文件输出组件
  - 支持自定义分隔符、引用符
  - 支持标题行控制
  - 支持追加模式

- **`json_output.go`** - JSON文件输出组件
  - 支持多种JSON格式：lines、array、object
  - 支持美化输出
  - 支持自定义根键

- **`database_output.go`** - 数据库输出组件
  - 支持多种数据库类型
  - 支持批量插入和事务
  - 支持upsert操作

- **`http_output.go`** - HTTP输出组件
  - 支持REST API调用
  - 支持多种认证方式
  - 支持自定义请求头

- **`elasticsearch_output.go`** - Elasticsearch输出组件
  - 支持批量索引操作
  - 支持索引自动创建
  - 支持认证和集群配置

- **`dummy_output.go`** - 虚拟输出组件（测试用）

## 使用方式

每个输出组件都实现了相同的接口：

```go
type Step interface {
    Initialize() error
    Execute(ctx context.Context, input RowSet) (RowSet, error)
    Cleanup() error
}
```

### 配置示例

#### CSV输出
```json
{
  "name": "CSVOutput",
  "type": "CSVFileOutput",
  "config": {
    "filename": "output/data.csv",
    "delimiter": ",",
    "write_header": true,
    "encoding": "utf-8"
  }
}
```

#### JSON输出
```json
{
  "name": "JSONOutput",
  "type": "JSONFileOutput",
  "config": {
    "filename": "output/data.json",
    "format": "lines",
    "pretty_print": false
  }
}
```

#### 数据库输出
```json
{
  "name": "DatabaseOutput",
  "type": "TableOutput",
  "config": {
    "driver": "mysql",
    "dsn": "user:password@tcp(localhost:3306)/database",
    "table": "output_table",
    "insert_mode": "insert"
  }
}
```

#### HTTP输出
```json
{
  "name": "HTTPOutput",
  "type": "RestOutput",
  "config": {
    "url": "https://api.example.com/data",
    "method": "POST",
    "content_type": "application/json",
    "auth_type": "bearer",
    "token": "your-token"
  }
}
```

#### Elasticsearch输出
```json
{
  "name": "ESOutput",
  "type": "ElasticsearchOutput",
  "config": {
    "hosts": ["http://localhost:9200"],
    "index": "my-index",
    "bulk_size": 1000,
    "refresh_policy": "false"
  }
}
```

## 扩展新输出组件

要添加新的输出组件：

1. 创建新的Go文件，如 `my_output.go`
2. 定义输出结构体，继承 `EnhancedOutputBase`
3. 实现 `Initialize`、`Execute` 和其他必要方法
4. 在 `enhanced_steps.go` 中注册新组件

### 示例结构
```go
type MyOutput struct {
    *EnhancedOutputBase
    // 自定义字段
}

func NewMyOutput(stepMeta meta.StepMeta) *MyOutput {
    return &MyOutput{
        EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
    }
}

func (mo *MyOutput) Initialize() error {
    // 初始化逻辑
}

func (mo *MyOutput) Execute(ctx context.Context, input step.RowSet) (step.RowSet, error) {
    // 执行逻辑
}
```

## 性能和监控

所有输出组件都包含内置的性能监控功能：

- 输入/输出行数统计
- 错误率统计
- 吞吐量计算
- 执行时间监控
- 批量操作支持

可以通过 `GetMetrics()` 方法获取详细的性能指标。
