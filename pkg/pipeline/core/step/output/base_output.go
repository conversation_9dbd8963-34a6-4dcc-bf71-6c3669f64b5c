package output

import (
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// EnhancedOutputBase 增强输出组件基类
type EnhancedOutputBase struct {
	*step.BaseStep

	// 通用配置
	batchSize     int           // 批处理大小
	maxRetries    int           // 最大重试次数
	retryDelay    time.Duration // 重试延迟
	timeout       time.Duration // 超时时间
	enableMetrics bool          // 是否启用指标

	// 错误处理
	errorHandling string // 错误处理策略: skip, abort, retry
	maxErrors     int    // 最大错误数

	// 输出指标
	metrics *OutputMetrics // 输出指标
}

// OutputMetrics 输出指标
type OutputMetrics struct {
	StartTime      time.Time     // 开始时间
	EndTime        time.Time     // 结束时间
	InputRows      int64         // 输入行数
	OutputRows     int64         // 输出行数
	ErrorRows      int64         // 错误行数
	BatchCount     int64         // 批次数量
	RetryCount     int64         // 重试次数
	ThroughputRate float64       // 吞吐率（行/秒）
	AverageLatency time.Duration // 平均延迟
	Errors         []error       // 错误列表
	mutex          sync.RWMutex  // 读写锁
}

// FileOutput 文件输出组件基类
type FileOutput struct {
	*EnhancedOutputBase

	// 文件配置
	filePath    string // 文件路径
	encoding    string // 文件编码
	compression string // 压缩格式: none, gzip, zip
	append      bool   // 是否追加模式

	// 文件滚动
	enableRolling  bool   // 是否启用文件滚动
	maxFileSize    int64  // 最大文件大小
	maxFileCount   int    // 最大文件数量
	rollingPattern string // 滚动文件名模式

	// 输出流
	writer io.Writer // 输出写入器
	file   *os.File  // 文件句柄
}

// NewEnhancedOutputBase 创建增强输出基类
func NewEnhancedOutputBase(stepMeta meta.StepMeta) *EnhancedOutputBase {
	return &EnhancedOutputBase{
		BaseStep:      step.NewBaseStep(stepMeta),
		batchSize:     1000,
		maxRetries:    3,
		retryDelay:    1 * time.Second,
		timeout:       30 * time.Second,
		enableMetrics: true,
		errorHandling: "skip",
		maxErrors:     100,
		metrics:       &OutputMetrics{},
	}
}

// Initialize 初始化基础输出组件
func (eob *EnhancedOutputBase) Initialize() error {
	return eob.BaseStep.Initialize()
}

// recordError 记录错误
func (eob *EnhancedOutputBase) recordError(err error) {
	eob.metrics.mutex.Lock()
	defer eob.metrics.mutex.Unlock()

	eob.metrics.Errors = append(eob.metrics.Errors, err)

	// 限制错误列表大小
	if len(eob.metrics.Errors) > eob.maxErrors {
		eob.metrics.Errors = eob.metrics.Errors[1:]
	}
}

// updateThroughputMetrics 更新吞吐量指标
func (eob *EnhancedOutputBase) updateThroughputMetrics() {
	if eob.metrics.EndTime.IsZero() || eob.metrics.StartTime.IsZero() {
		return
	}

	duration := eob.metrics.EndTime.Sub(eob.metrics.StartTime).Seconds()
	if duration > 0 {
		eob.metrics.ThroughputRate = float64(eob.metrics.OutputRows) / duration
	}
}

// GetMetrics 获取输出指标
func (eob *EnhancedOutputBase) GetMetrics() *OutputMetrics {
	eob.metrics.mutex.RLock()
	defer eob.metrics.mutex.RUnlock()
	return eob.metrics
}

// NewFileOutput 创建文件输出组件
func NewFileOutput(stepMeta meta.StepMeta, filePath string) *FileOutput {
	return &FileOutput{
		EnhancedOutputBase: NewEnhancedOutputBase(stepMeta),
		filePath:           filePath,
		encoding:           "utf-8",
		compression:        "none",
		append:             false,
		enableRolling:      false,
		maxFileSize:        100 * 1024 * 1024, // 100MB
		maxFileCount:       10,
		rollingPattern:     "2006-01-02_15-04-05",
	}
}

// Initialize 初始化文件输出组件
func (fo *FileOutput) Initialize() error {
	return fo.EnhancedOutputBase.Initialize()
}

// openFile 打开文件，支持滚动和压缩
func (fo *FileOutput) openFile() error {
	// 检查是否需要文件滚动
	if fo.enableRolling {
		if err := fo.checkAndRotateFile(); err != nil {
			return fmt.Errorf("failed to rotate file: %w", err)
		}
	}

	var err error
	var flag int

	if fo.append {
		flag = os.O_CREATE | os.O_WRONLY | os.O_APPEND
	} else {
		flag = os.O_CREATE | os.O_WRONLY | os.O_TRUNC
	}

	// 确保目录存在
	dir := filepath.Dir(fo.filePath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	fo.file, err = os.OpenFile(fo.filePath, flag, 0o644)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}

	// 设置压缩写入器
	fo.writer, err = fo.createCompressedWriter()
	if err != nil {
		fo.file.Close()
		return fmt.Errorf("failed to create compressed writer: %w", err)
	}

	return nil
}

// checkAndRotateFile 检查并轮转文件
func (fo *FileOutput) checkAndRotateFile() error {
	if !fo.enableRolling {
		return nil
	}

	// 检查文件是否存在和大小
	if info, err := os.Stat(fo.filePath); err == nil {
		if info.Size() >= fo.maxFileSize {
			return fo.rotateFile()
		}
	}

	return nil
}

// rotateFile 轮转文件
func (fo *FileOutput) rotateFile() error {
	// 关闭当前文件
	if fo.file != nil {
		fo.file.Close()
	}

	// 生成轮转文件名
	timestamp := time.Now()
	rotatedName := fo.generateRotatedFileName(timestamp)

	// 重命名当前文件
	if err := os.Rename(fo.filePath, rotatedName); err != nil {
		return fmt.Errorf("failed to rename file: %w", err)
	}

	// 清理旧文件
	return fo.cleanupOldFiles()
}

// generateRotatedFileName 生成轮转文件名
func (fo *FileOutput) generateRotatedFileName(timestamp time.Time) string {
	if fo.rollingPattern == "" {
		fo.rollingPattern = "2006-01-02_15-04-05"
	}

	dir := filepath.Dir(fo.filePath)
	base := filepath.Base(fo.filePath)
	ext := filepath.Ext(base)
	name := strings.TrimSuffix(base, ext)

	rotatedName := fmt.Sprintf("%s_%s%s", name, timestamp.Format(fo.rollingPattern), ext)
	return filepath.Join(dir, rotatedName)
}

// cleanupOldFiles 清理旧文件
func (fo *FileOutput) cleanupOldFiles() error {
	if fo.maxFileCount <= 0 {
		return nil
	}

	dir := filepath.Dir(fo.filePath)
	pattern := fo.getFilePattern()

	files, err := filepath.Glob(filepath.Join(dir, pattern))
	if err != nil {
		return err
	}

	// 如果文件数量超过限制，删除最老的文件
	if len(files) > fo.maxFileCount {
		// 按修改时间排序
		type fileInfo struct {
			path    string
			modTime time.Time
		}

		var fileInfos []fileInfo
		for _, file := range files {
			if info, err := os.Stat(file); err == nil {
				fileInfos = append(fileInfos, fileInfo{
					path:    file,
					modTime: info.ModTime(),
				})
			}
		}

		// 删除最老的文件
		for len(fileInfos) > fo.maxFileCount {
			oldest := 0
			for i, fi := range fileInfos {
				if fi.modTime.Before(fileInfos[oldest].modTime) {
					oldest = i
				}
			}

			os.Remove(fileInfos[oldest].path)
			fileInfos = append(fileInfos[:oldest], fileInfos[oldest+1:]...)
		}
	}

	return nil
}

// getFilePattern 获取文件匹配模式
func (fo *FileOutput) getFilePattern() string {
	base := filepath.Base(fo.filePath)
	ext := filepath.Ext(base)
	name := strings.TrimSuffix(base, ext)
	return fmt.Sprintf("%s_*%s", name, ext)
}

// createCompressedWriter 创建压缩写入器
func (fo *FileOutput) createCompressedWriter() (io.Writer, error) {
	switch strings.ToLower(fo.compression) {
	case "gzip", "gz":
		return gzip.NewWriter(fo.file), nil
	case "zip":
		// 对于zip压缩，我们需要特殊处理
		return fo.createZipWriter()
	case "none", "":
		return fo.file, nil
	default:
		return fo.file, nil
	}
}

// createZipWriter 创建ZIP写入器
func (fo *FileOutput) createZipWriter() (io.Writer, error) {
	zipWriter := zip.NewWriter(fo.file)

	// 创建ZIP文件内的条目
	entryName := filepath.Base(fo.filePath)
	if ext := filepath.Ext(entryName); ext != "" {
		entryName = strings.TrimSuffix(entryName, ext) + ".txt"
	}

	writer, err := zipWriter.Create(entryName)
	if err != nil {
		return nil, err
	}

	// 需要在某个地方保存zipWriter的引用以便后续关闭
	// 这里简化处理，实际使用中可能需要更复杂的管理
	return writer, nil
}

// Close 关闭文件输出
func (fo *FileOutput) Close() error {
	var err error

	// 关闭压缩写入器
	if closer, ok := fo.writer.(io.Closer); ok && closer != fo.file {
		if closeErr := closer.Close(); closeErr != nil {
			err = closeErr
		}
	}

	// 关闭文件
	if fo.file != nil {
		if closeErr := fo.file.Close(); closeErr != nil && err == nil {
			err = closeErr
		}
		fo.file = nil
	}

	return err
}

// SetCompression 设置压缩格式
func (fo *FileOutput) SetCompression(compression string) *FileOutput {
	fo.compression = compression
	return fo
}

// EnableRolling 启用文件滚动
func (fo *FileOutput) EnableRolling(maxSizeMB int64, maxCount int, pattern string) *FileOutput {
	fo.enableRolling = true
	fo.maxFileSize = maxSizeMB * 1024 * 1024
	fo.maxFileCount = maxCount
	if pattern != "" {
		fo.rollingPattern = pattern
	}
	return fo
}

// SetAppendMode 设置追加模式
func (fo *FileOutput) SetAppendMode(append bool) *FileOutput {
	fo.append = append
	return fo
}

// SetEncoding 设置文件编码
func (fo *FileOutput) SetEncoding(encoding string) *FileOutput {
	fo.encoding = encoding
	return fo
}

// GetFilePath 获取文件路径
func (fo *FileOutput) GetFilePath() string {
	return fo.filePath
}

// IsCompressionEnabled 检查是否启用压缩
func (fo *FileOutput) IsCompressionEnabled() bool {
	return fo.compression != "none" && fo.compression != ""
}

// IsRollingEnabled 检查是否启用滚动
func (fo *FileOutput) IsRollingEnabled() bool {
	return fo.enableRolling
}
