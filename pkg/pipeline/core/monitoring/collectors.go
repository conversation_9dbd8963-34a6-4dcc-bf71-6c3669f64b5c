package monitoring

import (
	"runtime"
	"sync"
	"time"

	"admin/pkg/pipeline/core/step"
	"admin/pkg/pipeline/meta"
)

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	Collect() []*Metric
}

// SystemMetricsCollector 系统指标收集器
type SystemMetricsCollector struct {
	lastCollection time.Time
}

// Collect 收集系统指标
func (smc *SystemMetricsCollector) Collect() []*Metric {
	now := time.Now()
	defer func() {
		smc.lastCollection = now
	}()

	var metrics []*Metric
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 内存指标
	metrics = append(metrics, &Metric{
		Name:      "system_memory_alloc_bytes",
		Type:      MetricTypeGauge,
		Value:     float64(m.Alloc),
		Timestamp: now,
		Help:      "Bytes of allocated heap objects",
	})

	metrics = append(metrics, &Metric{
		Name:      "system_memory_sys_bytes",
		Type:      MetricTypeGauge,
		Value:     float64(m.Sys),
		Timestamp: now,
		Help:      "Total bytes of memory obtained from the OS",
	})

	metrics = append(metrics, &Metric{
		Name:      "system_memory_heap_inuse_bytes",
		Type:      MetricTypeGauge,
		Value:     float64(m.HeapInuse),
		Timestamp: now,
		Help:      "Bytes in in-use spans",
	})

	// GC指标
	metrics = append(metrics, &Metric{
		Name:      "system_gc_runs_total",
		Type:      MetricTypeCounter,
		Value:     float64(m.NumGC),
		Timestamp: now,
		Help:      "Total number of GC runs",
	})

	metrics = append(metrics, &Metric{
		Name:      "system_gc_pause_seconds",
		Type:      MetricTypeGauge,
		Value:     float64(m.PauseNs[(m.NumGC+255)%256]) / 1e9,
		Timestamp: now,
		Help:      "Length of the last GC pause in seconds",
	})

	// Goroutine指标
	metrics = append(metrics, &Metric{
		Name:      "system_goroutines",
		Type:      MetricTypeGauge,
		Value:     float64(runtime.NumGoroutine()),
		Timestamp: now,
		Help:      "Number of goroutines",
	})

	// CPU核心数
	metrics = append(metrics, &Metric{
		Name:      "system_cpu_cores",
		Type:      MetricTypeGauge,
		Value:     float64(runtime.NumCPU()),
		Timestamp: now,
		Help:      "Number of CPU cores",
	})

	return metrics
}

// ETLMetricsCollector ETL指标收集器
type ETLMetricsCollector struct {
	stepMonitors map[string]*StepMonitor
	mu           sync.RWMutex
}

// NewETLMetricsCollector 创建ETL指标收集器
func NewETLMetricsCollector() *ETLMetricsCollector {
	return &ETLMetricsCollector{
		stepMonitors: make(map[string]*StepMonitor),
	}
}

// RegisterStepMonitor 注册步骤监控器
func (emc *ETLMetricsCollector) RegisterStepMonitor(monitor *StepMonitor) {
	emc.mu.Lock()
	defer emc.mu.Unlock()
	emc.stepMonitors[monitor.stepName] = monitor
}

// Collect 收集ETL指标
func (emc *ETLMetricsCollector) Collect() []*Metric {
	emc.mu.RLock()
	defer emc.mu.RUnlock()

	var metrics []*Metric
	now := time.Now()

	// 收集各个步骤的指标
	for _, monitor := range emc.stepMonitors {
		stepMetrics := monitor.GetMetrics()

		// 处理行数
		metrics = append(metrics, &Metric{
			Name:      "etl_step_processed_rows_total",
			Type:      MetricTypeCounter,
			Value:     float64(stepMetrics.ProcessedRows),
			Labels:    map[string]string{"step": monitor.stepName, "type": string(monitor.stepType)},
			Timestamp: now,
			Help:      "Total number of rows processed by step",
		})

		// 错误行数
		metrics = append(metrics, &Metric{
			Name:      "etl_step_error_rows_total",
			Type:      MetricTypeCounter,
			Value:     float64(stepMetrics.ErrorRows),
			Labels:    map[string]string{"step": monitor.stepName, "type": string(monitor.stepType)},
			Timestamp: now,
			Help:      "Total number of error rows in step",
		})

		// 处理时间
		if stepMetrics.Duration > 0 {
			metrics = append(metrics, &Metric{
				Name:      "etl_step_duration_seconds",
				Type:      MetricTypeHistogram,
				Value:     stepMetrics.Duration.Seconds(),
				Labels:    map[string]string{"step": monitor.stepName, "type": string(monitor.stepType)},
				Timestamp: now,
				Help:      "Step execution duration in seconds",
			})
		}

		// 吞吐率
		if stepMetrics.ThroughputRate > 0 {
			metrics = append(metrics, &Metric{
				Name:      "etl_step_throughput_rows_per_second",
				Type:      MetricTypeGauge,
				Value:     stepMetrics.ThroughputRate,
				Labels:    map[string]string{"step": monitor.stepName, "type": string(monitor.stepType)},
				Timestamp: now,
				Help:      "Step throughput in rows per second",
			})
		}

		// 内存使用
		if stepMetrics.MemoryUsage > 0 {
			metrics = append(metrics, &Metric{
				Name:      "etl_step_memory_bytes",
				Type:      MetricTypeGauge,
				Value:     float64(stepMetrics.MemoryUsage),
				Labels:    map[string]string{"step": monitor.stepName, "type": string(monitor.stepType)},
				Timestamp: now,
				Help:      "Step memory usage in bytes",
			})
		}
	}

	// 全局ETL指标
	totalRows := int64(0)
	totalErrors := int64(0)
	totalSteps := len(emc.stepMonitors)

	for _, monitor := range emc.stepMonitors {
		stepMetrics := monitor.GetMetrics()
		totalRows += stepMetrics.ProcessedRows
		totalErrors += stepMetrics.ErrorRows
	}

	// 总处理行数
	metrics = append(metrics, &Metric{
		Name:      "etl_total_processed_rows",
		Type:      MetricTypeCounter,
		Value:     float64(totalRows),
		Timestamp: now,
		Help:      "Total number of rows processed across all steps",
	})

	// 总错误数
	metrics = append(metrics, &Metric{
		Name:      "etl_total_errors",
		Type:      MetricTypeCounter,
		Value:     float64(totalErrors),
		Timestamp: now,
		Help:      "Total number of errors across all steps",
	})

	// 错误率
	errorRate := float64(0)
	if totalRows > 0 {
		errorRate = float64(totalErrors) / float64(totalRows) * 100
	}
	metrics = append(metrics, &Metric{
		Name:      "etl_error_rate",
		Type:      MetricTypeGauge,
		Value:     errorRate,
		Timestamp: now,
		Help:      "Error rate as percentage",
	})

	// 活跃步骤数
	metrics = append(metrics, &Metric{
		Name:      "etl_active_steps",
		Type:      MetricTypeGauge,
		Value:     float64(totalSteps),
		Timestamp: now,
		Help:      "Number of active ETL steps",
	})

	return metrics
}

// StepMonitor 步骤监控器
type StepMonitor struct {
	stepName    string
	stepType    meta.StepType
	registry    *MetricsRegistry
	stepMetrics *StepMetrics
	startTime   time.Time
	mu          sync.RWMutex
}

// StepMetrics 步骤指标
type StepMetrics struct {
	ProcessedRows  int64         `json:"processed_rows"`
	ErrorRows      int64         `json:"error_rows"`
	Duration       time.Duration `json:"duration"`
	ThroughputRate float64       `json:"throughput_rate"`
	MemoryUsage    int64         `json:"memory_usage"`
	LastUpdate     time.Time     `json:"last_update"`
}

// NewStepMonitor 创建步骤监控器
func NewStepMonitor(s step.Step, registry *MetricsRegistry) *StepMonitor {
	stepMeta := s.GetMeta()
	return &StepMonitor{
		stepName:    stepMeta.Name,
		stepType:    stepMeta.Type,
		registry:    registry,
		stepMetrics: &StepMetrics{},
		startTime:   time.Now(),
	}
}

// RecordRowProcessed 记录行处理
func (sm *StepMonitor) RecordRowProcessed() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.stepMetrics.ProcessedRows++
	sm.stepMetrics.LastUpdate = time.Now()
	sm.updateThroughput()
}

// RecordRowsProcessed 记录批量行处理
func (sm *StepMonitor) RecordRowsProcessed(count int64) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.stepMetrics.ProcessedRows += count
	sm.stepMetrics.LastUpdate = time.Now()
	sm.updateThroughput()
}

// RecordError 记录错误
func (sm *StepMonitor) RecordError() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.stepMetrics.ErrorRows++
	sm.stepMetrics.LastUpdate = time.Now()
}

// RecordDuration 记录执行时间
func (sm *StepMonitor) RecordDuration(duration time.Duration) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.stepMetrics.Duration = duration
	sm.stepMetrics.LastUpdate = time.Now()
	sm.updateThroughput()
}

// RecordMemoryUsage 记录内存使用
func (sm *StepMonitor) RecordMemoryUsage(bytes int64) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.stepMetrics.MemoryUsage = bytes
	sm.stepMetrics.LastUpdate = time.Now()
}

// GetMetrics 获取指标
func (sm *StepMonitor) GetMetrics() *StepMetrics {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	// 返回副本
	return &StepMetrics{
		ProcessedRows:  sm.stepMetrics.ProcessedRows,
		ErrorRows:      sm.stepMetrics.ErrorRows,
		Duration:       sm.stepMetrics.Duration,
		ThroughputRate: sm.stepMetrics.ThroughputRate,
		MemoryUsage:    sm.stepMetrics.MemoryUsage,
		LastUpdate:     sm.stepMetrics.LastUpdate,
	}
}

// updateThroughput 更新吞吐率
func (sm *StepMonitor) updateThroughput() {
	duration := time.Since(sm.startTime).Seconds()
	if duration > 0 {
		sm.stepMetrics.ThroughputRate = float64(sm.stepMetrics.ProcessedRows) / duration
	}
}

// Reset 重置指标
func (sm *StepMonitor) Reset() {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.stepMetrics = &StepMetrics{}
	sm.startTime = time.Now()
}

// PipelineMetricsCollector 管道指标收集器
type PipelineMetricsCollector struct {
	pipelineMetrics map[string]*PipelineMetrics
	mu              sync.RWMutex
}

// PipelineMetrics 管道指标
type PipelineMetrics struct {
	Name           string        `json:"name"`
	Status         string        `json:"status"`
	StartTime      time.Time     `json:"start_time"`
	EndTime        time.Time     `json:"end_time"`
	Duration       time.Duration `json:"duration"`
	TotalSteps     int           `json:"total_steps"`
	CompletedSteps int           `json:"completed_steps"`
	FailedSteps    int           `json:"failed_steps"`
	TotalRows      int64         `json:"total_rows"`
	ProcessedRows  int64         `json:"processed_rows"`
	ErrorRows      int64         `json:"error_rows"`
	ThroughputRate float64       `json:"throughput_rate"`
	ErrorRate      float64       `json:"error_rate"`
}

// NewPipelineMetricsCollector 创建管道指标收集器
func NewPipelineMetricsCollector() *PipelineMetricsCollector {
	return &PipelineMetricsCollector{
		pipelineMetrics: make(map[string]*PipelineMetrics),
	}
}

// RegisterPipelineMetrics 注册管道指标
func (pmc *PipelineMetricsCollector) RegisterPipelineMetrics(name string, metrics *PipelineMetrics) {
	pmc.mu.Lock()
	defer pmc.mu.Unlock()
	pmc.pipelineMetrics[name] = metrics
}

// Collect 收集管道指标
func (pmc *PipelineMetricsCollector) Collect() []*Metric {
	pmc.mu.RLock()
	defer pmc.mu.RUnlock()

	var metrics []*Metric
	now := time.Now()

	for name, pipelineMetrics := range pmc.pipelineMetrics {
		labels := map[string]string{"pipeline": name}

		// 管道状态
		statusValue := float64(0)
		switch pipelineMetrics.Status {
		case "running":
			statusValue = 1
		case "completed":
			statusValue = 2
		case "failed":
			statusValue = 3
		}

		metrics = append(metrics, &Metric{
			Name:      "etl_pipeline_status",
			Type:      MetricTypeGauge,
			Value:     statusValue,
			Labels:    labels,
			Timestamp: now,
			Help:      "Pipeline status (0=idle, 1=running, 2=completed, 3=failed)",
		})

		// 管道持续时间
		if !pipelineMetrics.EndTime.IsZero() {
			duration := pipelineMetrics.EndTime.Sub(pipelineMetrics.StartTime).Seconds()
			metrics = append(metrics, &Metric{
				Name:      "etl_pipeline_duration_seconds",
				Type:      MetricTypeHistogram,
				Value:     duration,
				Labels:    labels,
				Timestamp: now,
				Help:      "Pipeline execution duration in seconds",
			})
		}

		// 步骤指标
		metrics = append(metrics, &Metric{
			Name:      "etl_pipeline_total_steps",
			Type:      MetricTypeGauge,
			Value:     float64(pipelineMetrics.TotalSteps),
			Labels:    labels,
			Timestamp: now,
			Help:      "Total number of steps in pipeline",
		})

		metrics = append(metrics, &Metric{
			Name:      "etl_pipeline_completed_steps",
			Type:      MetricTypeGauge,
			Value:     float64(pipelineMetrics.CompletedSteps),
			Labels:    labels,
			Timestamp: now,
			Help:      "Number of completed steps in pipeline",
		})

		// 行处理指标
		metrics = append(metrics, &Metric{
			Name:      "etl_pipeline_processed_rows",
			Type:      MetricTypeCounter,
			Value:     float64(pipelineMetrics.ProcessedRows),
			Labels:    labels,
			Timestamp: now,
			Help:      "Total rows processed by pipeline",
		})

		// 吞吐率
		metrics = append(metrics, &Metric{
			Name:      "etl_pipeline_throughput",
			Type:      MetricTypeGauge,
			Value:     pipelineMetrics.ThroughputRate,
			Labels:    labels,
			Timestamp: now,
			Help:      "Pipeline throughput in rows per second",
		})

		// 错误率
		metrics = append(metrics, &Metric{
			Name:      "etl_pipeline_error_rate",
			Type:      MetricTypeGauge,
			Value:     pipelineMetrics.ErrorRate,
			Labels:    labels,
			Timestamp: now,
			Help:      "Pipeline error rate as percentage",
		})
	}

	return metrics
}
