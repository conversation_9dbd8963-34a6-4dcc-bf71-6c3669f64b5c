package monitoring

import (
	"sync"
	"time"
)

// MetricsRegistry 指标注册表
type MetricsRegistry struct {
	metrics map[string]*Metric
	mu      sync.RWMutex
}

// Metric 指标定义
type Metric struct {
	Name      string            `json:"name"`
	Type      MetricType        `json:"type"`
	Value     float64           `json:"value"`
	Labels    map[string]string `json:"labels"`
	Timestamp time.Time         `json:"timestamp"`
	Help      string            `json:"help"`
}

// MetricType 指标类型
type MetricType int

const (
	MetricTypeCounter   MetricType = iota // 计数器
	MetricTypeGauge                       // 仪表盘
	MetricTypeHistogram                   // 直方图
	MetricTypeSummary                     // 摘要
)

// NewMetricsRegistry 创建指标注册表
func NewMetricsRegistry() *MetricsRegistry {
	return &MetricsRegistry{
		metrics: make(map[string]*Metric),
	}
}

// Register 注册指标
func (mr *MetricsRegistry) Register(metric *Metric) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	key := mr.getMetricKey(metric.Name, metric.Labels)
	mr.metrics[key] = metric
}

// GetMetric 获取指标
func (mr *MetricsRegistry) GetMetric(name string) *Metric {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	return mr.metrics[name]
}

// GetMetricWithLabels 根据标签获取指标
func (mr *MetricsRegistry) GetMetricWithLabels(name string, labels map[string]string) *Metric {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	key := mr.getMetricKey(name, labels)
	return mr.metrics[key]
}

// GetAllMetrics 获取所有指标
func (mr *MetricsRegistry) GetAllMetrics() map[string]*Metric {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	result := make(map[string]*Metric)
	for k, v := range mr.metrics {
		result[k] = v
	}
	return result
}

// GetSnapshot 获取指标快照
func (mr *MetricsRegistry) GetSnapshot() *MetricsSnapshot {
	mr.mu.RLock()
	defer mr.mu.RUnlock()

	snapshot := &MetricsSnapshot{
		Timestamp: time.Now(),
		Metrics:   make(map[string]*Metric),
	}

	for k, v := range mr.metrics {
		// 创建指标副本
		metricCopy := &Metric{
			Name:      v.Name,
			Type:      v.Type,
			Value:     v.Value,
			Labels:    make(map[string]string),
			Timestamp: v.Timestamp,
			Help:      v.Help,
		}

		// 复制标签
		for lk, lv := range v.Labels {
			metricCopy.Labels[lk] = lv
		}

		snapshot.Metrics[k] = metricCopy
	}

	return snapshot
}

// Cleanup 清理过期指标
func (mr *MetricsRegistry) Cleanup(retentionPeriod time.Duration) {
	mr.mu.Lock()
	defer mr.mu.Unlock()

	cutoff := time.Now().Add(-retentionPeriod)
	for key, metric := range mr.metrics {
		if metric.Timestamp.Before(cutoff) {
			delete(mr.metrics, key)
		}
	}
}

// getMetricKey 生成指标键
func (mr *MetricsRegistry) getMetricKey(name string, labels map[string]string) string {
	if len(labels) == 0 {
		return name
	}

	// 简化实现，实际应用中应该使用更稳定的键生成方法
	key := name
	for k, v := range labels {
		key += ":" + k + "=" + v
	}
	return key
}

// MetricsSnapshot 指标快照
type MetricsSnapshot struct {
	Timestamp time.Time          `json:"timestamp"`
	Metrics   map[string]*Metric `json:"metrics"`
}

// Counter 计数器指标
type Counter struct {
	value int64
	mu    sync.RWMutex
}

// NewCounter 创建计数器
func NewCounter() *Counter {
	return &Counter{}
}

// Inc 增加计数
func (c *Counter) Inc() {
	c.Add(1)
}

// Add 增加指定值
func (c *Counter) Add(delta int64) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value += delta
}

// Get 获取当前值
func (c *Counter) Get() int64 {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.value
}

// Gauge 仪表盘指标
type Gauge struct {
	value float64
	mu    sync.RWMutex
}

// NewGauge 创建仪表盘
func NewGauge() *Gauge {
	return &Gauge{}
}

// Set 设置值
func (g *Gauge) Set(value float64) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.value = value
}

// Get 获取当前值
func (g *Gauge) Get() float64 {
	g.mu.RLock()
	defer g.mu.RUnlock()
	return g.value
}

// Inc 增加值
func (g *Gauge) Inc() {
	g.Add(1)
}

// Dec 减少值
func (g *Gauge) Dec() {
	g.Add(-1)
}

// Add 增加指定值
func (g *Gauge) Add(delta float64) {
	g.mu.Lock()
	defer g.mu.Unlock()
	g.value += delta
}

// Histogram 直方图指标
type Histogram struct {
	buckets []float64
	counts  []int64
	sum     float64
	count   int64
	mu      sync.RWMutex
}

// NewHistogram 创建直方图
func NewHistogram(buckets []float64) *Histogram {
	return &Histogram{
		buckets: buckets,
		counts:  make([]int64, len(buckets)+1), // +1 for +Inf bucket
	}
}

// Observe 观察值
func (h *Histogram) Observe(value float64) {
	h.mu.Lock()
	defer h.mu.Unlock()

	h.sum += value
	h.count++

	// 找到合适的桶
	for i, bucket := range h.buckets {
		if value <= bucket {
			h.counts[i]++
			return
		}
	}

	// 值超过所有桶，放入+Inf桶
	h.counts[len(h.buckets)]++
}

// GetBuckets 获取桶计数
func (h *Histogram) GetBuckets() ([]float64, []int64) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	buckets := make([]float64, len(h.buckets))
	counts := make([]int64, len(h.counts))

	copy(buckets, h.buckets)
	copy(counts, h.counts)

	return buckets, counts
}

// GetSum 获取总和
func (h *Histogram) GetSum() float64 {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.sum
}

// GetCount 获取总数
func (h *Histogram) GetCount() int64 {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.count
}

// TimerHistogram 时间直方图
type TimerHistogram struct {
	*Histogram
}

// NewTimerHistogram 创建时间直方图
func NewTimerHistogram() *TimerHistogram {
	// 默认时间桶：1ms, 5ms, 10ms, 50ms, 100ms, 500ms, 1s, 5s, 10s, 30s
	buckets := []float64{
		0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0, 30.0,
	}
	return &TimerHistogram{
		Histogram: NewHistogram(buckets),
	}
}

// Time 计时功能
func (th *TimerHistogram) Time(fn func()) {
	start := time.Now()
	fn()
	duration := time.Since(start).Seconds()
	th.Observe(duration)
}

// Summary 摘要指标
type Summary struct {
	observations []float64
	sum          float64
	count        int64
	quantiles    []float64
	maxAge       time.Duration
	mu           sync.RWMutex
}

// NewSummary 创建摘要
func NewSummary(quantiles []float64, maxAge time.Duration) *Summary {
	return &Summary{
		observations: make([]float64, 0),
		quantiles:    quantiles,
		maxAge:       maxAge,
	}
}

// Observe 观察值
func (s *Summary) Observe(value float64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.observations = append(s.observations, value)
	s.sum += value
	s.count++

	// 清理过期观察值（简化实现）
	if len(s.observations) > 1000 {
		s.observations = s.observations[len(s.observations)-1000:]
	}
}

// GetQuantiles 获取分位数
func (s *Summary) GetQuantiles() map[float64]float64 {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if len(s.observations) == 0 {
		return make(map[float64]float64)
	}

	// 简化的分位数计算
	result := make(map[float64]float64)
	for _, q := range s.quantiles {
		index := int(float64(len(s.observations)-1) * q)
		if index >= 0 && index < len(s.observations) {
			result[q] = s.observations[index]
		}
	}

	return result
}

// GetSum 获取总和
func (s *Summary) GetSum() float64 {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.sum
}

// GetCount 获取总数
func (s *Summary) GetCount() int64 {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.count
}
