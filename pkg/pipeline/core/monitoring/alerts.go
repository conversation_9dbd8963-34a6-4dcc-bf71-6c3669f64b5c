package monitoring

import (
	"fmt"
	"sync"
	"time"
)

// AlertManager 告警管理器
type AlertManager struct {
	rules         []*AlertRule
	thresholds    AlertThresholds
	alertHistory  []*Alert
	notifications []AlertNotifier
	mu            sync.RWMutex
	maxHistory    int
}

// AlertRule 告警规则
type AlertRule struct {
	Name        string            `json:"name"`
	MetricName  string            `json:"metric_name"`
	Condition   AlertCondition    `json:"condition"`
	Threshold   float64           `json:"threshold"`
	Duration    time.Duration     `json:"duration"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	Severity    AlertSeverity     `json:"severity"`
	Enabled     bool              `json:"enabled"`
}

// AlertCondition 告警条件
type AlertCondition int

const (
	ConditionGreaterThan AlertCondition = iota
	ConditionLessThan
	ConditionEquals
	ConditionNotEquals
	ConditionGreaterOrEqual
	ConditionLessOrEqual
)

// AlertSeverity 告警严重程度
type AlertSeverity int

const (
	SeverityInfo AlertSeverity = iota
	SeverityWarning
	SeverityCritical
	SeverityEmergency
)

// Alert 告警实例
type Alert struct {
	ID          string            `json:"id"`
	RuleName    string            `json:"rule_name"`
	MetricName  string            `json:"metric_name"`
	Value       float64           `json:"value"`
	Threshold   float64           `json:"threshold"`
	Condition   AlertCondition    `json:"condition"`
	Severity    AlertSeverity     `json:"severity"`
	Labels      map[string]string `json:"labels"`
	Description string            `json:"description"`
	Timestamp   time.Time         `json:"timestamp"`
	Resolved    bool              `json:"resolved"`
	ResolvedAt  *time.Time        `json:"resolved_at,omitempty"`
}

// AlertNotifier 告警通知器接口
type AlertNotifier interface {
	Notify(alert *Alert) error
}

// NewAlertManager 创建告警管理器
func NewAlertManager(thresholds AlertThresholds) *AlertManager {
	am := &AlertManager{
		rules:         []*AlertRule{},
		thresholds:    thresholds,
		alertHistory:  []*Alert{},
		notifications: []AlertNotifier{},
		maxHistory:    1000,
	}

	// 添加默认规则
	am.addDefaultRules()
	return am
}

// addDefaultRules 添加默认告警规则
func (am *AlertManager) addDefaultRules() {
	defaultRules := []*AlertRule{
		{
			Name:        "high_cpu_usage",
			MetricName:  "system_cpu_usage_percent",
			Condition:   ConditionGreaterThan,
			Threshold:   am.thresholds.CPUUsageThreshold,
			Duration:    5 * time.Minute,
			Description: "CPU usage is too high",
			Severity:    SeverityWarning,
			Enabled:     true,
		},
		{
			Name:        "high_memory_usage",
			MetricName:  "system_memory_usage_percent",
			Condition:   ConditionGreaterThan,
			Threshold:   am.thresholds.MemoryUsageThreshold,
			Duration:    5 * time.Minute,
			Description: "Memory usage is too high",
			Severity:    SeverityWarning,
			Enabled:     true,
		},
		{
			Name:        "high_error_rate",
			MetricName:  "etl_error_rate",
			Condition:   ConditionGreaterThan,
			Threshold:   am.thresholds.ErrorRateThreshold,
			Duration:    2 * time.Minute,
			Description: "ETL error rate is too high",
			Severity:    SeverityCritical,
			Enabled:     true,
		},
		{
			Name:        "low_throughput",
			MetricName:  "etl_throughput",
			Condition:   ConditionLessThan,
			Threshold:   am.thresholds.ThroughputThreshold,
			Duration:    10 * time.Minute,
			Description: "ETL throughput is too low",
			Severity:    SeverityWarning,
			Enabled:     true,
		},
		{
			Name:        "high_latency",
			MetricName:  "etl_average_latency_seconds",
			Condition:   ConditionGreaterThan,
			Threshold:   am.thresholds.LatencyThreshold.Seconds(),
			Duration:    3 * time.Minute,
			Description: "ETL latency is too high",
			Severity:    SeverityWarning,
			Enabled:     true,
		},
	}

	for _, rule := range defaultRules {
		am.AddRule(rule)
	}
}

// AddRule 添加告警规则
func (am *AlertManager) AddRule(rule *AlertRule) {
	am.mu.Lock()
	defer am.mu.Unlock()
	am.rules = append(am.rules, rule)
}

// RemoveRule 移除告警规则
func (am *AlertManager) RemoveRule(name string) {
	am.mu.Lock()
	defer am.mu.Unlock()

	for i, rule := range am.rules {
		if rule.Name == name {
			am.rules = append(am.rules[:i], am.rules[i+1:]...)
			break
		}
	}
}

// GetRules 获取所有规则
func (am *AlertManager) GetRules() []*AlertRule {
	am.mu.RLock()
	defer am.mu.RUnlock()

	rules := make([]*AlertRule, len(am.rules))
	copy(rules, am.rules)
	return rules
}

// AddNotifier 添加通知器
func (am *AlertManager) AddNotifier(notifier AlertNotifier) {
	am.mu.Lock()
	defer am.mu.Unlock()
	am.notifications = append(am.notifications, notifier)
}

// EvaluateAlerts 评估告警
func (am *AlertManager) EvaluateAlerts(snapshot *MetricsSnapshot) []*Alert {
	am.mu.RLock()
	rules := make([]*AlertRule, len(am.rules))
	copy(rules, am.rules)
	am.mu.RUnlock()

	var alerts []*Alert

	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		metric := am.findMetricByName(snapshot, rule.MetricName)
		if metric == nil {
			continue
		}

		if am.evaluateCondition(metric.Value, rule.Condition, rule.Threshold) {
			alert := &Alert{
				ID:          am.generateAlertID(rule, metric),
				RuleName:    rule.Name,
				MetricName:  rule.MetricName,
				Value:       metric.Value,
				Threshold:   rule.Threshold,
				Condition:   rule.Condition,
				Severity:    rule.Severity,
				Labels:      am.mergeLabels(rule.Labels, metric.Labels),
				Description: rule.Description,
				Timestamp:   time.Now(),
				Resolved:    false,
			}

			alerts = append(alerts, alert)
		}
	}

	return alerts
}

// TriggerAlert 触发告警
func (am *AlertManager) TriggerAlert(alert *Alert) {
	// 检查是否是重复告警
	if am.isDuplicateAlert(alert) {
		return
	}

	// 添加到历史记录
	am.addToHistory(alert)

	// 发送通知
	am.mu.RLock()
	notifiers := make([]AlertNotifier, len(am.notifications))
	copy(notifiers, am.notifications)
	am.mu.RUnlock()

	for _, notifier := range notifiers {
		go func(n AlertNotifier) {
			if err := n.Notify(alert); err != nil {
				fmt.Printf("Failed to send alert notification: %v\n", err)
			}
		}(notifier)
	}
}

// ResolveAlert 解决告警
func (am *AlertManager) ResolveAlert(alertID string) {
	am.mu.Lock()
	defer am.mu.Unlock()

	for _, alert := range am.alertHistory {
		if alert.ID == alertID && !alert.Resolved {
			alert.Resolved = true
			now := time.Now()
			alert.ResolvedAt = &now
			break
		}
	}
}

// GetActiveAlerts 获取活跃告警
func (am *AlertManager) GetActiveAlerts() []*Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()

	var activeAlerts []*Alert
	for _, alert := range am.alertHistory {
		if !alert.Resolved {
			activeAlerts = append(activeAlerts, alert)
		}
	}

	return activeAlerts
}

// GetAlertHistory 获取告警历史
func (am *AlertManager) GetAlertHistory(limit int) []*Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()

	if limit <= 0 || limit > len(am.alertHistory) {
		limit = len(am.alertHistory)
	}

	history := make([]*Alert, limit)
	copy(history, am.alertHistory[len(am.alertHistory)-limit:])
	return history
}

// findMetricByName 根据名称查找指标
func (am *AlertManager) findMetricByName(snapshot *MetricsSnapshot, name string) *Metric {
	for _, metric := range snapshot.Metrics {
		if metric.Name == name {
			return metric
		}
	}
	return nil
}

// evaluateCondition 评估条件
func (am *AlertManager) evaluateCondition(value float64, condition AlertCondition, threshold float64) bool {
	switch condition {
	case ConditionGreaterThan:
		return value > threshold
	case ConditionLessThan:
		return value < threshold
	case ConditionEquals:
		return value == threshold
	case ConditionNotEquals:
		return value != threshold
	case ConditionGreaterOrEqual:
		return value >= threshold
	case ConditionLessOrEqual:
		return value <= threshold
	default:
		return false
	}
}

// generateAlertID 生成告警ID
func (am *AlertManager) generateAlertID(rule *AlertRule, metric *Metric) string {
	return fmt.Sprintf("%s_%s_%d", rule.Name, rule.MetricName, time.Now().Unix())
}

// mergeLabels 合并标签
func (am *AlertManager) mergeLabels(ruleLabels, metricLabels map[string]string) map[string]string {
	merged := make(map[string]string)

	for k, v := range metricLabels {
		merged[k] = v
	}

	for k, v := range ruleLabels {
		merged[k] = v
	}

	return merged
}

// isDuplicateAlert 检查是否是重复告警
func (am *AlertManager) isDuplicateAlert(alert *Alert) bool {
	am.mu.RLock()
	defer am.mu.RUnlock()

	cutoff := time.Now().Add(-5 * time.Minute) // 5分钟内的重复告警

	for _, existing := range am.alertHistory {
		if existing.RuleName == alert.RuleName &&
			existing.MetricName == alert.MetricName &&
			!existing.Resolved &&
			existing.Timestamp.After(cutoff) {
			return true
		}
	}

	return false
}

// addToHistory 添加到历史记录
func (am *AlertManager) addToHistory(alert *Alert) {
	am.mu.Lock()
	defer am.mu.Unlock()

	am.alertHistory = append(am.alertHistory, alert)

	// 限制历史记录大小
	if len(am.alertHistory) > am.maxHistory {
		am.alertHistory = am.alertHistory[len(am.alertHistory)-am.maxHistory:]
	}
}

// EmailNotifier 邮件通知器
type EmailNotifier struct {
	smtpHost string
	smtpPort int
	username string
	password string
	to       []string
}

// NewEmailNotifier 创建邮件通知器
func NewEmailNotifier(smtpHost string, smtpPort int, username, password string, to []string) *EmailNotifier {
	return &EmailNotifier{
		smtpHost: smtpHost,
		smtpPort: smtpPort,
		username: username,
		password: password,
		to:       to,
	}
}

// Notify 发送邮件通知
func (en *EmailNotifier) Notify(alert *Alert) error {
	// 实际应用中需要实现SMTP邮件发送
	fmt.Printf("Email notification: %s - %s (Value: %.2f, Threshold: %.2f)\n",
		alert.RuleName, alert.Description, alert.Value, alert.Threshold)
	return nil
}

// WebhookNotifier Webhook通知器
type WebhookNotifier struct {
	url     string
	headers map[string]string
}

// NewWebhookNotifier 创建Webhook通知器
func NewWebhookNotifier(url string, headers map[string]string) *WebhookNotifier {
	return &WebhookNotifier{
		url:     url,
		headers: headers,
	}
}

// Notify 发送Webhook通知
func (wn *WebhookNotifier) Notify(alert *Alert) error {
	// 实际应用中需要实现HTTP请求发送
	fmt.Printf("Webhook notification to %s: %s - %s\n",
		wn.url, alert.RuleName, alert.Description)
	return nil
}

// LogNotifier 日志通知器
type LogNotifier struct {
	// 可以配置日志级别等
}

// NewLogNotifier 创建日志通知器
func NewLogNotifier() *LogNotifier {
	return &LogNotifier{}
}

// Notify 记录日志通知
func (ln *LogNotifier) Notify(alert *Alert) error {
	fmt.Printf("[ALERT] %s: %s (Value: %.2f, Threshold: %.2f) at %s\n",
		ln.severityToString(alert.Severity),
		alert.Description,
		alert.Value,
		alert.Threshold,
		alert.Timestamp.Format(time.RFC3339),
	)
	return nil
}

// severityToString 将严重程度转换为字符串
func (ln *LogNotifier) severityToString(severity AlertSeverity) string {
	switch severity {
	case SeverityInfo:
		return "INFO"
	case SeverityWarning:
		return "WARNING"
	case SeverityCritical:
		return "CRITICAL"
	case SeverityEmergency:
		return "EMERGENCY"
	default:
		return "UNKNOWN"
	}
}
