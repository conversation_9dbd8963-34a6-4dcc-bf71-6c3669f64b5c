package monitoring

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"admin/pkg/pipeline/core/step"
)

// MonitoringSystem 监控系统
type MonitoringSystem struct {
	registry     *MetricsRegistry
	collectors   []MetricsCollector
	reporters    []MetricsReporter
	alertManager *AlertManager
	config       MonitoringConfig
	isRunning    int32
	stopChan     chan struct{}
	mu           sync.RWMutex
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	CollectionInterval  time.Duration   // 指标收集间隔
	ReportingInterval   time.Duration   // 报告间隔
	RetentionPeriod     time.Duration   // 数据保留期
	MaxMetricsInMemory  int             // 内存中最大指标数
	EnableSystemMetrics bool            // 是否启用系统指标
	EnableAlerts        bool            // 是否启用告警
	EnableHealthCheck   bool            // 是否启用健康检查
	AlertThresholds     AlertThresholds // 告警阈值
}

// AlertThresholds 告警阈值
type AlertThresholds struct {
	CPUUsageThreshold    float64       // CPU使用率阈值
	MemoryUsageThreshold float64       // 内存使用率阈值
	ErrorRateThreshold   float64       // 错误率阈值
	LatencyThreshold     time.Duration // 延迟阈值
	ThroughputThreshold  float64       // 吞吐量阈值
	DiskUsageThreshold   float64       // 磁盘使用率阈值
}

// DefaultMonitoringConfig 默认监控配置
var DefaultMonitoringConfig = MonitoringConfig{
	CollectionInterval:  10 * time.Second,
	ReportingInterval:   60 * time.Second,
	RetentionPeriod:     24 * time.Hour,
	MaxMetricsInMemory:  10000,
	EnableSystemMetrics: true,
	EnableAlerts:        true,
	EnableHealthCheck:   true,
	AlertThresholds: AlertThresholds{
		CPUUsageThreshold:    80.0,
		MemoryUsageThreshold: 85.0,
		ErrorRateThreshold:   5.0,
		LatencyThreshold:     5 * time.Second,
		ThroughputThreshold:  100.0,
		DiskUsageThreshold:   90.0,
	},
}

// NewMonitoringSystem 创建监控系统
func NewMonitoringSystem(config *MonitoringConfig) *MonitoringSystem {
	if config == nil {
		config = &DefaultMonitoringConfig
	}

	ms := &MonitoringSystem{
		registry:     NewMetricsRegistry(),
		collectors:   []MetricsCollector{},
		reporters:    []MetricsReporter{},
		alertManager: NewAlertManager(config.AlertThresholds),
		config:       *config,
		stopChan:     make(chan struct{}),
	}

	// 添加默认收集器
	if config.EnableSystemMetrics {
		ms.AddCollector(&SystemMetricsCollector{})
	}

	// 添加ETL专用收集器
	ms.AddCollector(&ETLMetricsCollector{})

	return ms
}

// Start 启动监控系统
func (ms *MonitoringSystem) Start(ctx context.Context) error {
	if !atomic.CompareAndSwapInt32(&ms.isRunning, 0, 1) {
		return fmt.Errorf("monitoring system is already running")
	}

	// 启动指标收集
	go ms.startMetricsCollection(ctx)

	// 启动指标报告
	go ms.startMetricsReporting(ctx)

	// 启动告警检查
	if ms.config.EnableAlerts {
		go ms.startAlertChecking(ctx)
	}

	// 启动健康检查
	if ms.config.EnableHealthCheck {
		go ms.startHealthChecking(ctx)
	}

	return nil
}

// Stop 停止监控系统
func (ms *MonitoringSystem) Stop() error {
	if !atomic.CompareAndSwapInt32(&ms.isRunning, 1, 0) {
		return fmt.Errorf("monitoring system is not running")
	}

	close(ms.stopChan)
	return nil
}

// AddCollector 添加指标收集器
func (ms *MonitoringSystem) AddCollector(collector MetricsCollector) {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.collectors = append(ms.collectors, collector)
}

// AddReporter 添加指标报告器
func (ms *MonitoringSystem) AddReporter(reporter MetricsReporter) {
	ms.mu.Lock()
	defer ms.mu.Unlock()
	ms.reporters = append(ms.reporters, reporter)
}

// GetRegistry 获取指标注册表
func (ms *MonitoringSystem) GetRegistry() *MetricsRegistry {
	return ms.registry
}

// GetAlertManager 获取告警管理器
func (ms *MonitoringSystem) GetAlertManager() *AlertManager {
	return ms.alertManager
}

// startMetricsCollection 启动指标收集
func (ms *MonitoringSystem) startMetricsCollection(ctx context.Context) {
	ticker := time.NewTicker(ms.config.CollectionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ms.stopChan:
			return
		case <-ticker.C:
			ms.collectMetrics()
		}
	}
}

// startMetricsReporting 启动指标报告
func (ms *MonitoringSystem) startMetricsReporting(ctx context.Context) {
	ticker := time.NewTicker(ms.config.ReportingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ms.stopChan:
			return
		case <-ticker.C:
			ms.reportMetrics()
		}
	}
}

// startAlertChecking 启动告警检查
func (ms *MonitoringSystem) startAlertChecking(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 告警检查频率更高
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ms.stopChan:
			return
		case <-ticker.C:
			ms.checkAlerts()
		}
	}
}

// startHealthChecking 启动健康检查
func (ms *MonitoringSystem) startHealthChecking(ctx context.Context) {
	ticker := time.NewTicker(60 * time.Second) // 健康检查频率
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ms.stopChan:
			return
		case <-ticker.C:
			ms.performHealthCheck()
		}
	}
}

// collectMetrics 收集指标
func (ms *MonitoringSystem) collectMetrics() {
	ms.mu.RLock()
	collectors := make([]MetricsCollector, len(ms.collectors))
	copy(collectors, ms.collectors)
	ms.mu.RUnlock()

	for _, collector := range collectors {
		metrics := collector.Collect()
		for _, metric := range metrics {
			ms.registry.Register(metric)
		}
	}

	// 清理过期指标
	ms.registry.Cleanup(ms.config.RetentionPeriod)
}

// reportMetrics 报告指标
func (ms *MonitoringSystem) reportMetrics() {
	ms.mu.RLock()
	reporters := make([]MetricsReporter, len(ms.reporters))
	copy(reporters, ms.reporters)
	ms.mu.RUnlock()

	snapshot := ms.registry.GetSnapshot()
	for _, reporter := range reporters {
		if err := reporter.Report(snapshot); err != nil {
			// 记录报告错误，但不中断其他报告器
			fmt.Printf("Error reporting metrics: %v\n", err)
		}
	}
}

// checkAlerts 检查告警
func (ms *MonitoringSystem) checkAlerts() {
	snapshot := ms.registry.GetSnapshot()
	alerts := ms.alertManager.EvaluateAlerts(snapshot)

	for _, alert := range alerts {
		// 触发告警
		ms.alertManager.TriggerAlert(alert)
	}
}

// performHealthCheck 执行健康检查
func (ms *MonitoringSystem) performHealthCheck() {
	healthStatus := &HealthStatus{
		Timestamp:  time.Now(),
		Status:     HealthStatusHealthy,
		Components: make(map[string]ComponentHealth),
	}

	// 检查系统资源
	systemHealth := ms.checkSystemHealth()
	healthStatus.Components["system"] = systemHealth

	// 检查ETL组件
	etlHealth := ms.checkETLHealth()
	healthStatus.Components["admin"] = etlHealth

	// 确定整体健康状态
	healthStatus.Status = ms.determineOverallHealth(healthStatus.Components)

	// 注册健康状态指标
	ms.registry.Register(&Metric{
		Name:      "system_health_status",
		Type:      MetricTypeGauge,
		Value:     float64(healthStatus.Status),
		Labels:    map[string]string{"component": "overall"},
		Timestamp: time.Now(),
	})
}

// checkSystemHealth 检查系统健康状态
func (ms *MonitoringSystem) checkSystemHealth() ComponentHealth {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	health := ComponentHealth{
		Status:  HealthStatusHealthy,
		Message: "System is healthy",
		Metrics: make(map[string]float64),
	}

	// CPU使用率（简化实现）
	cpuUsage := float64(runtime.NumGoroutine()) / float64(runtime.GOMAXPROCS(0)) * 100
	health.Metrics["cpu_usage"] = cpuUsage

	// 内存使用率
	memUsage := float64(m.Alloc) / float64(m.Sys) * 100
	health.Metrics["memory_usage"] = memUsage

	// Goroutine数量
	health.Metrics["goroutine_count"] = float64(runtime.NumGoroutine())

	// 检查阈值
	if cpuUsage > ms.config.AlertThresholds.CPUUsageThreshold {
		health.Status = HealthStatusUnhealthy
		health.Message = "High CPU usage detected"
	}

	if memUsage > ms.config.AlertThresholds.MemoryUsageThreshold {
		health.Status = HealthStatusUnhealthy
		health.Message = "High memory usage detected"
	}

	return health
}

// checkETLHealth 检查ETL健康状态
func (ms *MonitoringSystem) checkETLHealth() ComponentHealth {
	health := ComponentHealth{
		Status:  HealthStatusHealthy,
		Message: "ETL system is healthy",
		Metrics: make(map[string]float64),
	}

	// 获取最近的错误率
	errorRateMetric := ms.registry.GetMetric("etl_error_rate")
	if errorRateMetric != nil {
		errorRate := errorRateMetric.Value
		health.Metrics["error_rate"] = errorRate

		if errorRate > ms.config.AlertThresholds.ErrorRateThreshold {
			health.Status = HealthStatusUnhealthy
			health.Message = "High error rate detected"
		}
	}

	// 获取最近的吞吐量
	throughputMetric := ms.registry.GetMetric("etl_throughput")
	if throughputMetric != nil {
		throughput := throughputMetric.Value
		health.Metrics["throughput"] = throughput

		if throughput < ms.config.AlertThresholds.ThroughputThreshold {
			health.Status = HealthStatusDegraded
			health.Message = "Low throughput detected"
		}
	}

	return health
}

// determineOverallHealth 确定整体健康状态
func (ms *MonitoringSystem) determineOverallHealth(components map[string]ComponentHealth) HealthStatusType {
	hasUnhealthy := false
	hasDegraded := false

	for _, component := range components {
		switch component.Status {
		case HealthStatusUnhealthy:
			hasUnhealthy = true
		case HealthStatusDegraded:
			hasDegraded = true
		}
	}

	if hasUnhealthy {
		return HealthStatusUnhealthy
	}
	if hasDegraded {
		return HealthStatusDegraded
	}

	return HealthStatusHealthy
}

// GetHealthStatus 获取当前健康状态
func (ms *MonitoringSystem) GetHealthStatus() *HealthStatus {
	healthMetric := ms.registry.GetMetric("system_health_status")
	if healthMetric == nil {
		return &HealthStatus{
			Timestamp:  time.Now(),
			Status:     HealthStatusUnknown,
			Components: map[string]ComponentHealth{},
		}
	}

	return &HealthStatus{
		Timestamp:  healthMetric.Timestamp,
		Status:     HealthStatusType(healthMetric.Value),
		Components: map[string]ComponentHealth{}, // 实际应用中需要更详细的实现
	}
}

// RegisterStepMonitor 注册步骤监控
func (ms *MonitoringSystem) RegisterStepMonitor(step step.Step) *StepMonitor {
	return NewStepMonitor(step, ms.registry)
}

// HealthStatus 健康状态
type HealthStatus struct {
	Timestamp  time.Time                  `json:"timestamp"`
	Status     HealthStatusType           `json:"status"`
	Components map[string]ComponentHealth `json:"components"`
}

// HealthStatusType 健康状态类型
type HealthStatusType int

const (
	HealthStatusUnknown HealthStatusType = iota
	HealthStatusHealthy
	HealthStatusDegraded
	HealthStatusUnhealthy
)

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Status  HealthStatusType   `json:"status"`
	Message string             `json:"message"`
	Metrics map[string]float64 `json:"metrics"`
}
