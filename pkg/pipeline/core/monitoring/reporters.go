package monitoring

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"
)

// MetricsReporter 指标报告器接口
type MetricsReporter interface {
	Report(snapshot *MetricsSnapshot) error
}

// PrometheusReporter Prometheus格式报告器
type PrometheusReporter struct {
	endpoint string
	client   *http.Client
}

// NewPrometheusReporter 创建Prometheus报告器
func NewPrometheusReporter(endpoint string) *PrometheusReporter {
	return &PrometheusReporter{
		endpoint: endpoint,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Report 报告指标到Prometheus
func (pr *PrometheusReporter) Report(snapshot *MetricsSnapshot) error {
	prometheusFormat := pr.convertToPrometheusFormat(snapshot)

	req, err := http.NewRequest("POST", pr.endpoint, strings.NewReader(prometheusFormat))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "text/plain")

	resp, err := pr.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send metrics: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("prometheus returned status %d: %s", resp.StatusCode, string(body))
	}

	return nil
}

// convertToPrometheusFormat 转换为Prometheus格式
func (pr *PrometheusReporter) convertToPrometheusFormat(snapshot *MetricsSnapshot) string {
	var lines []string

	for _, metric := range snapshot.Metrics {
		line := pr.formatMetric(metric)
		if line != "" {
			lines = append(lines, line)
		}
	}

	return strings.Join(lines, "\n")
}

// formatMetric 格式化单个指标
func (pr *PrometheusReporter) formatMetric(metric *Metric) string {
	name := strings.ReplaceAll(metric.Name, "-", "_")

	var labelParts []string
	for k, v := range metric.Labels {
		labelParts = append(labelParts, fmt.Sprintf(`%s="%s"`, k, v))
	}

	var labelString string
	if len(labelParts) > 0 {
		labelString = "{" + strings.Join(labelParts, ",") + "}"
	}

	timestamp := metric.Timestamp.UnixMilli()

	return fmt.Sprintf("%s%s %f %d", name, labelString, metric.Value, timestamp)
}

// JSONReporter JSON格式报告器
type JSONReporter struct {
	writer io.Writer
}

// NewJSONReporter 创建JSON报告器
func NewJSONReporter(writer io.Writer) *JSONReporter {
	return &JSONReporter{
		writer: writer,
	}
}

// Report 以JSON格式报告指标
func (jr *JSONReporter) Report(snapshot *MetricsSnapshot) error {
	encoder := json.NewEncoder(jr.writer)
	encoder.SetIndent("", "  ")
	return encoder.Encode(snapshot)
}

// LogReporter 日志报告器
type LogReporter struct {
	logger *log.Logger
}

// NewLogReporter 创建日志报告器
func NewLogReporter(logger *log.Logger) *LogReporter {
	if logger == nil {
		logger = log.Default()
	}
	return &LogReporter{
		logger: logger,
	}
}

// Report 以日志形式报告指标
func (lr *LogReporter) Report(snapshot *MetricsSnapshot) error {
	lr.logger.Printf("=== Metrics Report at %s ===", snapshot.Timestamp.Format(time.RFC3339))

	for name, metric := range snapshot.Metrics {
		var labelStr string
		if len(metric.Labels) > 0 {
			var parts []string
			for k, v := range metric.Labels {
				parts = append(parts, fmt.Sprintf("%s=%s", k, v))
			}
			labelStr = fmt.Sprintf("{%s}", strings.Join(parts, ","))
		}

		lr.logger.Printf("%s%s: %f", name, labelStr, metric.Value)
	}

	lr.logger.Printf("=== End Metrics Report ===")
	return nil
}

// HTTPExposureReporter HTTP暴露报告器（用于Prometheus抓取）
type HTTPExposureReporter struct {
	addr     string
	server   *http.Server
	snapshot *MetricsSnapshot
}

// NewHTTPExposureReporter 创建HTTP暴露报告器
func NewHTTPExposureReporter(addr string) *HTTPExposureReporter {
	reporter := &HTTPExposureReporter{
		addr: addr,
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/metrics", reporter.handleMetrics)
	mux.HandleFunc("/health", reporter.handleHealth)

	reporter.server = &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	return reporter
}

// Start 启动HTTP服务器
func (her *HTTPExposureReporter) Start() error {
	go func() {
		if err := her.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("HTTP exposure server error: %v", err)
		}
	}()
	return nil
}

// Stop 停止HTTP服务器
func (her *HTTPExposureReporter) Stop() error {
	return her.server.Close()
}

// Report 更新快照数据
func (her *HTTPExposureReporter) Report(snapshot *MetricsSnapshot) error {
	her.snapshot = snapshot
	return nil
}

// handleMetrics 处理指标请求
func (her *HTTPExposureReporter) handleMetrics(w http.ResponseWriter, r *http.Request) {
	if her.snapshot == nil {
		w.WriteHeader(http.StatusNoContent)
		return
	}

	w.Header().Set("Content-Type", "text/plain; version=0.0.4; charset=utf-8")

	for _, metric := range her.snapshot.Metrics {
		line := her.formatPrometheusMetric(metric)
		if line != "" {
			fmt.Fprintln(w, line)
		}
	}
}

// handleHealth 处理健康检查请求
func (her *HTTPExposureReporter) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	response := map[string]interface{}{
		"status":    "healthy",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	if her.snapshot != nil {
		response["last_update"] = her.snapshot.Timestamp.Format(time.RFC3339)
		response["metrics_count"] = len(her.snapshot.Metrics)
	}

	json.NewEncoder(w).Encode(response)
}

// formatPrometheusMetric 格式化Prometheus指标
func (her *HTTPExposureReporter) formatPrometheusMetric(metric *Metric) string {
	name := strings.ReplaceAll(metric.Name, "-", "_")

	var labelParts []string
	for k, v := range metric.Labels {
		labelParts = append(labelParts, fmt.Sprintf(`%s="%s"`, k, v))
	}

	var labelString string
	if len(labelParts) > 0 {
		labelString = "{" + strings.Join(labelParts, ",") + "}"
	}

	return fmt.Sprintf("%s%s %f", name, labelString, metric.Value)
}

// CompositeReporter 组合报告器
type CompositeReporter struct {
	reporters []MetricsReporter
}

// NewCompositeReporter 创建组合报告器
func NewCompositeReporter(reporters ...MetricsReporter) *CompositeReporter {
	return &CompositeReporter{
		reporters: reporters,
	}
}

// AddReporter 添加报告器
func (cr *CompositeReporter) AddReporter(reporter MetricsReporter) {
	cr.reporters = append(cr.reporters, reporter)
}

// Report 报告到所有报告器
func (cr *CompositeReporter) Report(snapshot *MetricsSnapshot) error {
	var errors []error

	for _, reporter := range cr.reporters {
		if err := reporter.Report(snapshot); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		var errorMessages []string
		for _, err := range errors {
			errorMessages = append(errorMessages, err.Error())
		}
		return fmt.Errorf("reporter errors: %s", strings.Join(errorMessages, "; "))
	}

	return nil
}

// CSVReporter CSV格式报告器
type CSVReporter struct {
	writer   io.Writer
	headers  []string
	firstRun bool
}

// NewCSVReporter 创建CSV报告器
func NewCSVReporter(writer io.Writer) *CSVReporter {
	return &CSVReporter{
		writer:   writer,
		firstRun: true,
	}
}

// Report 以CSV格式报告指标
func (cr *CSVReporter) Report(snapshot *MetricsSnapshot) error {
	if cr.firstRun {
		// 写入CSV头部
		headers := []string{"timestamp", "metric_name", "value", "type", "labels"}
		fmt.Fprintf(cr.writer, "%s\n", strings.Join(headers, ","))
		cr.firstRun = false
	}

	for _, metric := range snapshot.Metrics {
		var labelsStr string
		if len(metric.Labels) > 0 {
			var labelPairs []string
			for k, v := range metric.Labels {
				labelPairs = append(labelPairs, fmt.Sprintf("%s=%s", k, v))
			}
			labelsStr = strings.Join(labelPairs, ";")
		}

		fmt.Fprintf(cr.writer, "%s,%s,%f,%d,\"%s\"\n",
			snapshot.Timestamp.Format(time.RFC3339),
			metric.Name,
			metric.Value,
			metric.Type,
			labelsStr,
		)
	}

	return nil
}

// DatabaseReporter 数据库报告器（示例实现）
type DatabaseReporter struct {
	// 实际应用中需要数据库连接
	connectionString string
}

// NewDatabaseReporter 创建数据库报告器
func NewDatabaseReporter(connectionString string) *DatabaseReporter {
	return &DatabaseReporter{
		connectionString: connectionString,
	}
}

// Report 报告指标到数据库
func (dr *DatabaseReporter) Report(snapshot *MetricsSnapshot) error {
	// 实际应用中需要实现数据库插入逻辑
	log.Printf("Would insert %d metrics to database", len(snapshot.Metrics))
	return nil
}

// SlackReporter Slack通知报告器（用于关键指标告警）
type SlackReporter struct {
	webhookURL string
	client     *http.Client
	threshold  float64 // 只报告超过阈值的指标
}

// NewSlackReporter 创建Slack报告器
func NewSlackReporter(webhookURL string, threshold float64) *SlackReporter {
	return &SlackReporter{
		webhookURL: webhookURL,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		threshold: threshold,
	}
}

// Report 报告关键指标到Slack
func (sr *SlackReporter) Report(snapshot *MetricsSnapshot) error {
	var alertMetrics []*Metric

	// 筛选需要告警的指标
	for _, metric := range snapshot.Metrics {
		if sr.shouldAlert(metric) {
			alertMetrics = append(alertMetrics, metric)
		}
	}

	if len(alertMetrics) == 0 {
		return nil // 没有需要告警的指标
	}

	message := sr.buildSlackMessage(alertMetrics)
	return sr.sendSlackMessage(message)
}

// shouldAlert 判断是否需要告警
func (sr *SlackReporter) shouldAlert(metric *Metric) bool {
	// 简化的告警逻辑
	switch metric.Name {
	case "etl_error_rate":
		return metric.Value > sr.threshold
	case "system_memory_usage_percent":
		return metric.Value > 85.0
	case "system_cpu_usage_percent":
		return metric.Value > 90.0
	}
	return false
}

// buildSlackMessage 构建Slack消息
func (sr *SlackReporter) buildSlackMessage(metrics []*Metric) map[string]interface{} {
	var text strings.Builder
	text.WriteString("🚨 *ETL系统告警* 🚨\n\n")

	for _, metric := range metrics {
		text.WriteString(fmt.Sprintf("• %s: %.2f\n", metric.Name, metric.Value))
	}

	return map[string]interface{}{
		"text":       text.String(),
		"username":   "ETL Monitor",
		"icon_emoji": ":warning:",
	}
}

// sendSlackMessage 发送Slack消息
func (sr *SlackReporter) sendSlackMessage(message map[string]interface{}) error {
	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal slack message: %w", err)
	}

	resp, err := sr.client.Post(sr.webhookURL, "application/json", strings.NewReader(string(payload)))
	if err != nil {
		return fmt.Errorf("failed to send slack message: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("slack returned status %d", resp.StatusCode)
	}

	return nil
}
