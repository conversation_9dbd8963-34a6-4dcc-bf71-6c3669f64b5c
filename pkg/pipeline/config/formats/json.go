package formats

import (
	"encoding/json"
	"fmt"

	"admin/pkg/pipeline/meta"
)

// JSONParser JSON 格式解析器
type JSONParser struct{}

// ParseJob 解析 JSON 格式的作业配置
func (p *JSONParser) ParseJob(data []byte) (*meta.JobMeta, error) {
	// 首先尝试解析包装格式 { "job": {...} }
	var jobConfig struct {
		Job *meta.JobMeta `json:"job"`
	}

	if err := json.Unmarshal(data, &jobConfig); err == nil && jobConfig.Job != nil {
		return jobConfig.Job, nil
	}

	// 如果包装格式失败，尝试直接解析作为 JobMeta 对象
	var job meta.JobMeta
	if err := json.Unmarshal(data, &job); err != nil {
		return nil, fmt.Errorf("failed to parse JSON job config: %w", err)
	}

	return &job, nil
}

// ParseTransformation 解析 JSON 格式的转换配置
func (p *JSONParser) ParseTransformation(data []byte) (*meta.TransformationMeta, error) {
	// 首先尝试解析包装格式 { "transformation": {...} }
	var transConfig struct {
		Transformation *meta.TransformationMeta `json:"transformation"`
	}

	if err := json.Unmarshal(data, &transConfig); err == nil && transConfig.Transformation != nil {
		return transConfig.Transformation, nil
	}

	// 如果包装格式失败，尝试直接解析作为 TransformationMeta 对象
	var trans meta.TransformationMeta
	if err := json.Unmarshal(data, &trans); err != nil {
		return nil, fmt.Errorf("failed to parse JSON transformation config: %w", err)
	}

	return &trans, nil
}

// ParseConnections 解析 JSON 格式的连接配置
func (p *JSONParser) ParseConnections(data []byte) (*meta.ConnectionsConfig, error) {
	var connConfig meta.ConnectionsConfig

	if err := json.Unmarshal(data, &connConfig); err != nil {
		return nil, fmt.Errorf("failed to parse JSON connections config: %w", err)
	}

	return &connConfig, nil
}

// MarshalJob 序列化作业配置为 JSON 格式
func (p *JSONParser) MarshalJob(job *meta.JobMeta) ([]byte, error) {
	jobWrapper := struct {
		Job *meta.JobMeta `json:"job"`
	}{
		Job: job,
	}

	data, err := json.MarshalIndent(jobWrapper, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal job to JSON: %w", err)
	}

	return data, nil
}

// MarshalTransformation 序列化转换配置为 JSON 格式
func (p *JSONParser) MarshalTransformation(trans *meta.TransformationMeta) ([]byte, error) {
	transWrapper := struct {
		Transformation *meta.TransformationMeta `json:"transformation"`
	}{
		Transformation: trans,
	}

	data, err := json.MarshalIndent(transWrapper, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal transformation to JSON: %w", err)
	}

	return data, nil
}

// MarshalConnections 序列化连接配置为 JSON 格式
func (p *JSONParser) MarshalConnections(connections *meta.ConnectionsConfig) ([]byte, error) {
	data, err := json.MarshalIndent(connections, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal connections to JSON: %w", err)
	}

	return data, nil
}

// SupportedExtensions 返回支持的文件扩展名
func (p *JSONParser) SupportedExtensions() []string {
	return []string{".json"}
}
