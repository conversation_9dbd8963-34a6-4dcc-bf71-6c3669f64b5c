package formats

import (
	"fmt"

	"admin/pkg/pipeline/meta"

	"gopkg.in/yaml.v3"
)

// YAMLParser YAML 格式解析器
type YAMLParser struct{}

// ParseJob 解析 YAML 格式的作业配置
func (p *YAMLParser) ParseJob(data []byte) (*meta.JobMeta, error) {
	// 首先尝试解析包装格式 { "job": {...} }
	var jobConfig struct {
		Job *meta.JobMeta `yaml:"job"`
	}

	if err := yaml.Unmarshal(data, &jobConfig); err == nil && jobConfig.Job != nil {
		return jobConfig.Job, nil
	}

	// 如果包装格式失败，尝试直接解析作为 JobMeta 对象
	var job meta.JobMeta
	if err := yaml.Unmarshal(data, &job); err != nil {
		return nil, fmt.Errorf("failed to parse YAML job config: %w", err)
	}

	return &job, nil
}

// ParseTransformation 解析 YAML 格式的转换配置
func (p *YAMLParser) ParseTransformation(data []byte) (*meta.TransformationMeta, error) {
	// 首先尝试解析包装格式 { "transformation": {...} }
	var transConfig struct {
		Transformation *meta.TransformationMeta `yaml:"transformation"`
	}

	if err := yaml.Unmarshal(data, &transConfig); err == nil && transConfig.Transformation != nil {
		return transConfig.Transformation, nil
	}

	// 如果包装格式失败，尝试直接解析作为 TransformationMeta 对象
	var trans meta.TransformationMeta
	if err := yaml.Unmarshal(data, &trans); err != nil {
		return nil, fmt.Errorf("failed to parse YAML transformation config: %w", err)
	}

	return &trans, nil
}

// ParseConnections 解析 YAML 格式的连接配置
func (p *YAMLParser) ParseConnections(data []byte) (*meta.ConnectionsConfig, error) {
	var connConfig meta.ConnectionsConfig

	if err := yaml.Unmarshal(data, &connConfig); err != nil {
		return nil, fmt.Errorf("failed to parse YAML connections config: %w", err)
	}

	return &connConfig, nil
}

// MarshalJob 序列化作业配置为 YAML 格式
func (p *YAMLParser) MarshalJob(job *meta.JobMeta) ([]byte, error) {
	jobWrapper := struct {
		Job *meta.JobMeta `yaml:"job"`
	}{
		Job: job,
	}

	data, err := yaml.Marshal(jobWrapper)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal job to YAML: %w", err)
	}

	return data, nil
}

// MarshalTransformation 序列化转换配置为 YAML 格式
func (p *YAMLParser) MarshalTransformation(trans *meta.TransformationMeta) ([]byte, error) {
	transWrapper := struct {
		Transformation *meta.TransformationMeta `yaml:"transformation"`
	}{
		Transformation: trans,
	}

	data, err := yaml.Marshal(transWrapper)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal transformation to YAML: %w", err)
	}

	return data, nil
}

// MarshalConnections 序列化连接配置为 YAML 格式
func (p *YAMLParser) MarshalConnections(connections *meta.ConnectionsConfig) ([]byte, error) {
	data, err := yaml.Marshal(connections)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal connections to YAML: %w", err)
	}

	return data, nil
}

// SupportedExtensions 返回支持的文件扩展名
func (p *YAMLParser) SupportedExtensions() []string {
	return []string{".yaml", ".yml"}
}
