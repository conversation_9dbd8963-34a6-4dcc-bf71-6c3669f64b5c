package formats

import (
	"testing"
	"time"

	"admin/pkg/pipeline/meta"
)

func TestJSONParser(t *testing.T) {
	parser := &JSONParser{}

	// 测试作业配置解析
	jobJSON := `{
		"job": {
			"meta": {
				"name": "test-job",
				"description": "Test job configuration",
				"version": "1.0.0",
				"author": "test"
			},
			"parameters": {
				"param1": "value1"
			},
			"variables": {
				"var1": "varvalue1"
			},
			"entries": [
				{
					"name": "start",
					"type": "START",
					"description": "Start entry",
					"config": {},
					"position": {"x": 100, "y": 100}
				}
			],
			"hops": []
		}
	}`

	job, err := parser.ParseJob([]byte(jobJSON))
	if err != nil {
		t.Fatalf("Failed to parse job: %v", err)
	}

	if job.Meta.Name != "test-job" {
		t.<PERSON>("Expected job name 'test-job', got '%s'", job.Meta.Name)
	}

	if job.Parameters["param1"] != "value1" {
		t.<PERSON>("Expected parameter value 'value1', got '%s'", job.Parameters["param1"])
	}

	// 测试序列化
	data, err := parser.MarshalJob(job)
	if err != nil {
		t.Fatalf("Failed to marshal job: %v", err)
	}

	if len(data) == 0 {
		t.Error("Marshaled data is empty")
	}
}

func TestYAMLParser(t *testing.T) {
	parser := &YAMLParser{}

	// 测试转换配置解析
	transYAML := `
transformation:
  meta:
    name: test-transformation
    description: Test transformation configuration
    version: 1.0.0
    author: test
  parameters:
    param1: value1
  variables:
    var1: varvalue1
  steps:
    - name: input
      type: INPUT
      description: Input step
      config: {}
      position:
        x: 100
        y: 100
      fields:
        - name: field1
          type: STRING
          length: 50
  hops: []
`

	trans, err := parser.ParseTransformation([]byte(transYAML))
	if err != nil {
		t.Fatalf("Failed to parse transformation: %v", err)
	}

	if trans.Meta.Name != "test-transformation" {
		t.Errorf("Expected transformation name 'test-transformation', got '%s'", trans.Meta.Name)
	}

	if len(trans.Steps) != 1 {
		t.Errorf("Expected 1 step, got %d", len(trans.Steps))
	}

	// 测试序列化
	data, err := parser.MarshalTransformation(trans)
	if err != nil {
		t.Fatalf("Failed to marshal transformation: %v", err)
	}

	if len(data) == 0 {
		t.Error("Marshaled data is empty")
	}
}

func TestXMLParser(t *testing.T) {
	parser := &XMLParser{}

	// 测试作业配置解析
	jobXML := `<?xml version="1.0" encoding="UTF-8"?>
<job>
  <name>test-job</name>
  <description>Test job configuration</description>
  <parameters>
    <parameter>
      <name>param1</name>
      <value>value1</value>
    </parameter>
  </parameters>
  <variables>
    <variable>
      <name>var1</name>
      <value>varvalue1</value>
    </variable>
  </variables>
  <entries>
    <entry>
      <name>start</name>
      <type>START</type>
      <description>Start entry</description>
      <config></config>
      <position>
        <x>100</x>
        <y>100</y>
      </position>
    </entry>
  </entries>
  <hops></hops>
</job>`

	job, err := parser.ParseJob([]byte(jobXML))
	if err != nil {
		t.Fatalf("Failed to parse XML job: %v", err)
	}

	if job.Meta.Name != "test-job" {
		t.Errorf("Expected job name 'test-job', got '%s'", job.Meta.Name)
	}

	if job.Parameters["param1"] != "value1" {
		t.Errorf("Expected parameter value 'value1', got '%s'", job.Parameters["param1"])
	}

	// 测试序列化
	data, err := parser.MarshalJob(job)
	if err != nil {
		t.Fatalf("Failed to marshal XML job: %v", err)
	}

	if len(data) == 0 {
		t.Error("Marshaled XML data is empty")
	}
}

func TestParserFactory(t *testing.T) {
	factory := NewParserFactory()

	// 测试JSON解析器
	jsonParser := factory.GetParser(".json")
	if jsonParser == nil {
		t.Error("JSON parser should not be nil")
	}

	// 测试YAML解析器
	yamlParser := factory.GetParser(".yaml")
	if yamlParser == nil {
		t.Error("YAML parser should not be nil")
	}

	// 测试XML解析器
	xmlParser := factory.GetParser(".xml")
	if xmlParser == nil {
		t.Error("XML parser should not be nil")
	}

	// 测试kjb格式（应该返回XML解析器）
	kjbParser := factory.GetParser(".kjb")
	if kjbParser == nil {
		t.Error("KJB parser should not be nil")
	}

	// 测试不支持的格式（应该返回默认JSON解析器）
	defaultParser := factory.GetParser(".txt")
	if defaultParser == nil {
		t.Error("Default parser should not be nil")
	}

	// 测试支持的格式列表
	formats := factory.SupportedFormats()
	if len(formats) == 0 {
		t.Error("Should have supported formats")
	}

	t.Logf("Supported formats: %v", formats)
}

func TestConnectionsConfig(t *testing.T) {
	parser := &JSONParser{}

	// 测试连接配置
	connJSON := `{
		"connections": [
			{
				"name": "test-db",
				"type": "MYSQL",
				"description": "Test database connection",
				"config": {
					"host": "localhost",
					"port": "3306",
					"database": "testdb"
				}
			}
		]
	}`

	conn, err := parser.ParseConnections([]byte(connJSON))
	if err != nil {
		t.Fatalf("Failed to parse connections: %v", err)
	}

	if len(conn.Connections) != 1 {
		t.Errorf("Expected 1 connection, got %d", len(conn.Connections))
	}

	if conn.Connections[0].Name != "test-db" {
		t.Errorf("Expected connection name 'test-db', got '%s'", conn.Connections[0].Name)
	}

	// 测试序列化
	data, err := parser.MarshalConnections(conn)
	if err != nil {
		t.Fatalf("Failed to marshal connections: %v", err)
	}

	if len(data) == 0 {
		t.Error("Marshaled connections data is empty")
	}
}

func TestSupportedExtensions(t *testing.T) {
	tests := []struct {
		parser Parser
		name   string
		exts   []string
	}{
		{&JSONParser{}, "JSON", []string{".json"}},
		{&YAMLParser{}, "YAML", []string{".yaml", ".yml"}},
		{&XMLParser{}, "XML", []string{".xml", ".kjb", ".ktr"}},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			exts := test.parser.SupportedExtensions()
			if len(exts) != len(test.exts) {
				t.Errorf("Expected %d extensions, got %d", len(test.exts), len(exts))
			}

			for i, ext := range test.exts {
				if i >= len(exts) || exts[i] != ext {
					t.Errorf("Expected extension '%s', got '%s'", ext, exts[i])
				}
			}
		})
	}
}

// 创建测试用的作业元数据
func createTestJobMeta() *meta.JobMeta {
	return &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "test-job",
			Description: "Test job",
			Version:     "1.0.0",
			Author:      "test-author",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Parameters: map[string]string{
			"param1": "value1",
			"param2": "value2",
		},
		Variables: map[string]string{
			"var1": "varvalue1",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:        "start",
				Type:        meta.JobEntryTypeStart,
				Description: "Start entry",
				Config:      map[string]interface{}{},
				Position: meta.Position{
					X: 100,
					Y: 100,
				},
			},
		},
		Hops: []meta.JobHopMeta{},
	}
}

// 创建测试用的转换元数据
func createTestTransformationMeta() *meta.TransformationMeta {
	return &meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name:        "test-transformation",
			Description: "Test transformation",
			Version:     "1.0.0",
			Author:      "test-author",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Parameters: map[string]string{
			"param1": "value1",
		},
		Variables: map[string]string{
			"var1": "varvalue1",
		},
		Steps: []meta.StepMeta{
			{
				Name:        "input",
				Type:        meta.StepTypeTableInput,
				Description: "Input step",
				Config:      map[string]interface{}{},
				Position: meta.Position{
					X: 100,
					Y: 100,
				},
				Fields: []meta.FieldMeta{
					{
						Name:   "field1",
						Type:   meta.FieldTypeString,
						Length: 50,
					},
				},
			},
		},
		Hops: []meta.HopMeta{},
	}
}
