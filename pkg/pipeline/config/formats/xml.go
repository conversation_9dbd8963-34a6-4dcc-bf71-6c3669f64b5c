package formats

import (
	"encoding/xml"
	"fmt"

	"admin/pkg/pipeline/meta"
)

// XMLParser XML 格式解析器（兼容 Spoon）
type XMLParser struct{}

// SpoonJob 表示 Spoon .kjb 文件结构
type SpoonJob struct {
	XMLName     xml.Name         `xml:"job"`
	Name        string           `xml:"name"`
	Description string           `xml:"description,omitempty"`
	Parameters  []SpoonParameter `xml:"parameters>parameter"`
	Variables   []SpoonVariable  `xml:"variables>variable"`
	Entries     []SpoonJobEntry  `xml:"entries>entry"`
	Hops        []SpoonJobHop    `xml:"hops>hop"`
}

// SpoonJobEntry 表示 Spoon 作业条目
type SpoonJobEntry struct {
	Name        string         `xml:"name"`
	Type        string         `xml:"type"`
	Description string         `xml:"description,omitempty"`
	Config      interface{}    `xml:"config"`
	Position    *SpoonPosition `xml:"position,omitempty"`
}

// SpoonJobHop 表示 Spoon 作业连接
type SpoonJobHop struct {
	From          string `xml:"from"`
	To            string `xml:"to"`
	Evaluation    bool   `xml:"evaluation"`
	Unconditional bool   `xml:"unconditional"`
	Condition     string `xml:"condition,omitempty"`
}

// SpoonTransformation 表示 Spoon .ktr 文件结构
type SpoonTransformation struct {
	XMLName     xml.Name         `xml:"transformation"`
	Name        string           `xml:"name"`
	Description string           `xml:"description,omitempty"`
	Parameters  []SpoonParameter `xml:"parameters>parameter"`
	Variables   []SpoonVariable  `xml:"variables>variable"`
	Steps       []SpoonStep      `xml:"steps>step"`
	Hops        []SpoonTransHop  `xml:"hops>hop"`
}

// SpoonStep 表示 Spoon 转换步骤
type SpoonStep struct {
	Name        string         `xml:"name"`
	Type        string         `xml:"type"`
	Description string         `xml:"description,omitempty"`
	Config      interface{}    `xml:"config"`
	Fields      []SpoonField   `xml:"fields>field"`
	Position    *SpoonPosition `xml:"position,omitempty"`
}

// SpoonTransHop 表示 Spoon 转换连接
type SpoonTransHop struct {
	From      string `xml:"from"`
	To        string `xml:"to"`
	Enabled   bool   `xml:"enabled"`
	Condition string `xml:"condition,omitempty"`
}

// SpoonParameter 表示 Spoon 参数
type SpoonParameter struct {
	Name  string `xml:"name"`
	Value string `xml:"value"`
}

// SpoonVariable 表示 Spoon 变量
type SpoonVariable struct {
	Name  string `xml:"name"`
	Value string `xml:"value"`
}

// SpoonField 表示 Spoon 字段定义
type SpoonField struct {
	Name   string `xml:"name"`
	Type   string `xml:"type"`
	Length int    `xml:"length,omitempty"`
}

// SpoonPosition 表示 Spoon 位置信息
type SpoonPosition struct {
	X int `xml:"x"`
	Y int `xml:"y"`
}

// SpoonConnections 表示 Spoon 连接配置
type SpoonConnections struct {
	XMLName     xml.Name          `xml:"connections"`
	Connections []SpoonConnection `xml:"connection"`
}

// SpoonConnection 表示 Spoon 连接定义
type SpoonConnection struct {
	Name        string                 `xml:"name"`
	Type        string                 `xml:"type"`
	Description string                 `xml:"description,omitempty"`
	Config      map[string]interface{} `xml:"config"`
}

// ParseJob 解析 XML 格式的作业配置（兼容 Spoon .kjb 格式）
func (p *XMLParser) ParseJob(data []byte) (*meta.JobMeta, error) {
	// 首先检查XML是否包含job根元素
	var rootElement struct {
		XMLName xml.Name
	}
	if err := xml.Unmarshal(data, &rootElement); err != nil {
		return nil, fmt.Errorf("unsupported file format: invalid XML")
	}

	if rootElement.XMLName.Local != "job" {
		return nil, fmt.Errorf("unsupported file format: expected element type <job> but have <%s>", rootElement.XMLName.Local)
	}

	var spoonJob SpoonJob
	if err := xml.Unmarshal(data, &spoonJob); err != nil {
		return nil, fmt.Errorf("failed to parse XML job config: %w", err)
	}

	// 转换 Spoon 格式到内部格式
	jobMeta := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        spoonJob.Name,
			Description: spoonJob.Description,
		},
		Parameters: make(map[string]string),
		Variables:  make(map[string]string),
		Entries:    make([]meta.JobEntryMeta, 0, len(spoonJob.Entries)),
		Hops:       make([]meta.JobHopMeta, 0, len(spoonJob.Hops)),
	}

	// 转换参数
	for _, param := range spoonJob.Parameters {
		jobMeta.Parameters[param.Name] = param.Value
	}

	// 转换变量
	for _, variable := range spoonJob.Variables {
		jobMeta.Variables[variable.Name] = variable.Value
	}

	// 转换作业条目
	for _, entry := range spoonJob.Entries {
		// 处理配置字段
		var configMap map[string]interface{}
		if entry.Config != nil {
			if m, ok := entry.Config.(map[string]interface{}); ok {
				configMap = m
			} else {
				configMap = make(map[string]interface{})
			}
		} else {
			configMap = make(map[string]interface{})
		}

		entryMeta := meta.JobEntryMeta{
			Name:        entry.Name,
			Type:        meta.JobEntryType(entry.Type),
			Description: entry.Description,
			Config:      configMap,
		}

		if entry.Position != nil {
			entryMeta.Position = meta.Position{
				X: entry.Position.X,
				Y: entry.Position.Y,
			}
		}

		jobMeta.Entries = append(jobMeta.Entries, entryMeta)
	}

	// 转换作业连接
	for _, hop := range spoonJob.Hops {
		hopMeta := meta.JobHopMeta{
			From:          hop.From,
			To:            hop.To,
			Evaluation:    hop.Evaluation,
			Unconditional: hop.Unconditional,
			Condition:     hop.Condition,
		}

		jobMeta.Hops = append(jobMeta.Hops, hopMeta)
	}

	return jobMeta, nil
}

// ParseTransformation 解析 XML 格式的转换配置（兼容 Spoon .ktr 格式）
func (p *XMLParser) ParseTransformation(data []byte) (*meta.TransformationMeta, error) {
	var spoonTrans SpoonTransformation
	if err := xml.Unmarshal(data, &spoonTrans); err != nil {
		return nil, fmt.Errorf("failed to parse XML transformation config: %w", err)
	}

	// 转换 Spoon 格式到内部格式
	transMeta := &meta.TransformationMeta{
		Meta: meta.TransMetaInfo{
			Name:        spoonTrans.Name,
			Description: spoonTrans.Description,
		},
		Parameters: make(map[string]string),
		Variables:  make(map[string]string),
		Steps:      make([]meta.StepMeta, 0, len(spoonTrans.Steps)),
		Hops:       make([]meta.HopMeta, 0, len(spoonTrans.Hops)),
	}

	// 转换参数
	for _, param := range spoonTrans.Parameters {
		transMeta.Parameters[param.Name] = param.Value
	}

	// 转换变量
	for _, variable := range spoonTrans.Variables {
		transMeta.Variables[variable.Name] = variable.Value
	}

	// 转换步骤
	for _, step := range spoonTrans.Steps {
		// 安全地转换Config
		var config map[string]interface{}
		if step.Config != nil {
			if configMap, ok := step.Config.(map[string]interface{}); ok {
				config = configMap
			} else {
				config = make(map[string]interface{})
			}
		} else {
			config = make(map[string]interface{})
		}

		stepMeta := meta.StepMeta{
			Name:        step.Name,
			Type:        meta.StepType(step.Type),
			Description: step.Description,
			Config:      config,
			Fields:      make([]meta.FieldMeta, 0, len(step.Fields)),
		}

		if step.Position != nil {
			stepMeta.Position = meta.Position{
				X: step.Position.X,
				Y: step.Position.Y,
			}
		}

		// 转换字段定义
		for _, field := range step.Fields {
			fieldMeta := meta.FieldMeta{
				Name:   field.Name,
				Type:   meta.FieldType(field.Type),
				Length: field.Length,
			}
			stepMeta.Fields = append(stepMeta.Fields, fieldMeta)
		}

		transMeta.Steps = append(transMeta.Steps, stepMeta)
	}

	// 转换连接
	for _, hop := range spoonTrans.Hops {
		hopMeta := meta.HopMeta{
			From:      hop.From,
			To:        hop.To,
			Enabled:   hop.Enabled,
			Condition: hop.Condition,
		}

		transMeta.Hops = append(transMeta.Hops, hopMeta)
	}

	return transMeta, nil
}

// ParseConnections 解析 XML 格式的连接配置
func (p *XMLParser) ParseConnections(data []byte) (*meta.ConnectionsConfig, error) {
	var spoonConns SpoonConnections
	if err := xml.Unmarshal(data, &spoonConns); err != nil {
		return nil, fmt.Errorf("failed to parse XML connections config: %w", err)
	}

	// 转换 Spoon 格式到内部格式
	connConfig := &meta.ConnectionsConfig{
		Connections: make([]meta.ConnectionMeta, 0, len(spoonConns.Connections)),
	}

	for _, conn := range spoonConns.Connections {
		connMeta := meta.ConnectionMeta{
			Name:        conn.Name,
			Type:        meta.ConnectionType(conn.Type),
			Description: conn.Description,
			Config:      conn.Config,
		}

		connConfig.Connections = append(connConfig.Connections, connMeta)
	}

	return connConfig, nil
}

// MarshalJob 序列化作业配置为 XML 格式（兼容 Spoon .kjb 格式）
func (p *XMLParser) MarshalJob(job *meta.JobMeta) ([]byte, error) {
	spoonJob := SpoonJob{
		Name:        job.Meta.Name,
		Description: job.Meta.Description,
		Parameters:  make([]SpoonParameter, 0, len(job.Parameters)),
		Variables:   make([]SpoonVariable, 0, len(job.Variables)),
		Entries:     make([]SpoonJobEntry, 0, len(job.Entries)),
		Hops:        make([]SpoonJobHop, 0, len(job.Hops)),
	}

	// 转换参数
	for name, value := range job.Parameters {
		spoonJob.Parameters = append(spoonJob.Parameters, SpoonParameter{
			Name:  name,
			Value: value,
		})
	}

	// 转换变量
	for name, value := range job.Variables {
		spoonJob.Variables = append(spoonJob.Variables, SpoonVariable{
			Name:  name,
			Value: value,
		})
	}

	// 转换作业条目
	for _, entry := range job.Entries {
		spoonEntry := SpoonJobEntry{
			Name:        entry.Name,
			Type:        string(entry.Type),
			Description: entry.Description,
			Config:      struct{}{}, // 暂时使用空结构体，后续可以扩展为具体的配置结构
		}

		// Check if Position is not zero value
		if entry.Position.X != 0 || entry.Position.Y != 0 {
			spoonEntry.Position = &SpoonPosition{
				X: entry.Position.X,
				Y: entry.Position.Y,
			}
		}

		spoonJob.Entries = append(spoonJob.Entries, spoonEntry)
	}

	// 转换作业连接
	for _, hop := range job.Hops {
		spoonHop := SpoonJobHop{
			From:          hop.From,
			To:            hop.To,
			Evaluation:    hop.Evaluation,
			Unconditional: hop.Unconditional,
			Condition:     hop.Condition,
		}

		spoonJob.Hops = append(spoonJob.Hops, spoonHop)
	}

	// 序列化为 XML
	data, err := xml.MarshalIndent(spoonJob, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal job to XML: %w", err)
	}

	// 添加 XML 声明
	xmlHeader := `<?xml version="1.0" encoding="UTF-8"?>` + "\n"
	return append([]byte(xmlHeader), data...), nil
}

// MarshalTransformation 序列化转换配置为 XML 格式（兼容 Spoon .ktr 格式）
func (p *XMLParser) MarshalTransformation(trans *meta.TransformationMeta) ([]byte, error) {
	spoonTrans := SpoonTransformation{
		Name:        trans.Meta.Name,
		Description: trans.Meta.Description,
		Parameters:  make([]SpoonParameter, 0, len(trans.Parameters)),
		Variables:   make([]SpoonVariable, 0, len(trans.Variables)),
		Steps:       make([]SpoonStep, 0, len(trans.Steps)),
		Hops:        make([]SpoonTransHop, 0, len(trans.Hops)),
	}

	// 转换参数
	for name, value := range trans.Parameters {
		spoonTrans.Parameters = append(spoonTrans.Parameters, SpoonParameter{
			Name:  name,
			Value: value,
		})
	}

	// 转换变量
	for name, value := range trans.Variables {
		spoonTrans.Variables = append(spoonTrans.Variables, SpoonVariable{
			Name:  name,
			Value: value,
		})
	}

	// 转换步骤
	for _, step := range trans.Steps {
		spoonStep := SpoonStep{
			Name:        step.Name,
			Type:        string(step.Type),
			Description: step.Description,
			Config:      struct{}{}, // 暂时使用空结构体，后续可以扩展为具体的配置结构
			Fields:      make([]SpoonField, 0, len(step.Fields)),
		}

		// Check if Position is not zero value
		if step.Position.X != 0 || step.Position.Y != 0 {
			spoonStep.Position = &SpoonPosition{
				X: step.Position.X,
				Y: step.Position.Y,
			}
		}

		// 转换字段定义
		for _, field := range step.Fields {
			spoonField := SpoonField{
				Name:   field.Name,
				Type:   string(field.Type),
				Length: field.Length,
			}
			spoonStep.Fields = append(spoonStep.Fields, spoonField)
		}

		spoonTrans.Steps = append(spoonTrans.Steps, spoonStep)
	}

	// 转换连接
	for _, hop := range trans.Hops {
		spoonHop := SpoonTransHop{
			From:      hop.From,
			To:        hop.To,
			Enabled:   hop.Enabled,
			Condition: hop.Condition,
		}

		spoonTrans.Hops = append(spoonTrans.Hops, spoonHop)
	}

	// 序列化为 XML
	data, err := xml.MarshalIndent(spoonTrans, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal transformation to XML: %w", err)
	}

	// 添加 XML 声明
	xmlHeader := `<?xml version="1.0" encoding="UTF-8"?>` + "\n"
	return append([]byte(xmlHeader), data...), nil
}

// MarshalConnections 序列化连接配置为 XML 格式
func (p *XMLParser) MarshalConnections(connections *meta.ConnectionsConfig) ([]byte, error) {
	spoonConns := SpoonConnections{
		Connections: make([]SpoonConnection, 0, len(connections.Connections)),
	}

	for _, conn := range connections.Connections {
		spoonConn := SpoonConnection{
			Name:        conn.Name,
			Type:        string(conn.Type),
			Description: conn.Description,
			Config:      conn.Config,
		}

		spoonConns.Connections = append(spoonConns.Connections, spoonConn)
	}

	// 序列化为 XML
	data, err := xml.MarshalIndent(spoonConns, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("failed to marshal connections to XML: %w", err)
	}

	// 添加 XML 声明
	xmlHeader := `<?xml version="1.0" encoding="UTF-8"?>` + "\n"
	return append([]byte(xmlHeader), data...), nil
}

// SupportedExtensions 返回支持的文件扩展名
func (p *XMLParser) SupportedExtensions() []string {
	return []string{".xml", ".kjb", ".ktr"}
}
