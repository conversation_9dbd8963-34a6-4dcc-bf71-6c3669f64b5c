package config

import (
	"fmt"
	"regexp"

	"admin/pkg/pipeline/meta"
)

// 注意：VariableResolver 接口和 DefaultVariableResolver 实现已移至 parser.go
// 这里只保留业务验证相关的代码

// BusinessValidator 业务规则验证器
type BusinessValidator struct{}

// NewBusinessValidator 创建业务验证器
func NewBusinessValidator() *BusinessValidator {
	return &BusinessValidator{}
}

// ValidateJobFile 验证作业文件
func (v *BusinessValidator) ValidateJobFile(filename string) error {
	loader := NewFileLoader("")
	job, err := loader.LoadJob(filename)
	if err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// 额外的业务规则验证
	if err := v.validateJobBusinessRules(job); err != nil {
		return fmt.Errorf("business rule validation failed: %w", err)
	}

	return nil
}

// ValidateTransformationFile 验证转换文件
func (v *BusinessValidator) ValidateTransformationFile(filename string) error {
	loader := NewFileLoader("")
	trans, err := loader.LoadTransformation(filename)
	if err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// 额外的业务规则验证
	if err := v.validateTransformationBusinessRules(trans); err != nil {
		return fmt.Errorf("business rule validation failed: %w", err)
	}

	return nil
}

// ValidateConnectionsFile 验证连接文件
func (v *BusinessValidator) ValidateConnectionsFile(filename string) error {
	loader := NewFileLoader("")
	connections, err := loader.LoadConnections(filename)
	if err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// 额外的业务规则验证
	if err := v.validateConnectionsBusinessRules(connections); err != nil {
		return fmt.Errorf("business rule validation failed: %w", err)
	}

	return nil
}

// validateJobBusinessRules 验证作业业务规则
func (v *BusinessValidator) validateJobBusinessRules(job *meta.JobMeta) error {
	// 检查是否有孤立的条目（没有任何连接）
	connectedEntries := make(map[string]bool)

	for _, hop := range job.Hops {
		connectedEntries[hop.From] = true
		connectedEntries[hop.To] = true
	}

	for _, entry := range job.Entries {
		if entry.Type != meta.JobEntryTypeStart && entry.Type != meta.JobEntryTypeSuccess &&
			entry.Type != meta.JobEntryTypeError && !connectedEntries[entry.Name] {
			return fmt.Errorf("orphaned entry found: %s", entry.Name)
		}
	}

	// 检查是否有循环依赖
	if err := v.checkJobCycles(job); err != nil {
		return err
	}

	return nil
}

// validateTransformationBusinessRules 验证转换业务规则
func (v *BusinessValidator) validateTransformationBusinessRules(trans *meta.TransformationMeta) error {
	// 检查是否有孤立的步骤
	connectedSteps := make(map[string]bool)

	for _, hop := range trans.Hops {
		connectedSteps[hop.From] = true
		connectedSteps[hop.To] = true
	}

	inputSteps := trans.GetInputSteps()
	outputSteps := trans.GetOutputSteps()

	for _, step := range trans.Steps {
		isInput := false
		isOutput := false

		for _, inputStep := range inputSteps {
			if inputStep.Name == step.Name {
				isInput = true
				break
			}
		}

		for _, outputStep := range outputSteps {
			if outputStep.Name == step.Name {
				isOutput = true
				break
			}
		}

		if !isInput && !isOutput && !connectedSteps[step.Name] {
			return fmt.Errorf("orphaned step found: %s", step.Name)
		}
	}

	// 检查数据流的完整性
	if len(inputSteps) == 0 {
		return fmt.Errorf("transformation must have at least one input step")
	}

	if len(outputSteps) == 0 {
		return fmt.Errorf("transformation must have at least one output step")
	}

	return nil
}

// validateConnectionsBusinessRules 验证连接业务规则
func (v *BusinessValidator) validateConnectionsBusinessRules(connections *meta.ConnectionsConfig) error {
	// 检查连接名称的命名规范
	namePattern := regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_-]*$`)

	for _, conn := range connections.Connections {
		if !namePattern.MatchString(conn.Name) {
			return fmt.Errorf("invalid connection name format: %s", conn.Name)
		}
	}

	return nil
}

// checkJobCycles 检查作业循环依赖
func (v *BusinessValidator) checkJobCycles(job *meta.JobMeta) error {
	// 使用DFS检测循环
	visited := make(map[string]bool)
	recursionStack := make(map[string]bool)

	// 构建邻接表
	adjList := make(map[string][]string)
	for _, hop := range job.Hops {
		adjList[hop.From] = append(adjList[hop.From], hop.To)
	}

	// 从每个未访问的节点开始DFS
	for _, entry := range job.Entries {
		if !visited[entry.Name] {
			if v.hasCycleDFS(entry.Name, adjList, visited, recursionStack) {
				return fmt.Errorf("cycle detected in job flow starting from entry: %s", entry.Name)
			}
		}
	}

	return nil
}

// hasCycleDFS 使用DFS检测循环
func (v *BusinessValidator) hasCycleDFS(node string, adjList map[string][]string, visited, recursionStack map[string]bool) bool {
	visited[node] = true
	recursionStack[node] = true

	for _, neighbor := range adjList[node] {
		if !visited[neighbor] {
			if v.hasCycleDFS(neighbor, adjList, visited, recursionStack) {
				return true
			}
		} else if recursionStack[neighbor] {
			return true
		}
	}

	recursionStack[node] = false
	return false
}
