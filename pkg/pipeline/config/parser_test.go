package config

import (
	"testing"
	"time"

	"admin/pkg/pipeline/meta"
)

func TestConfigParser(t *testing.T) {
	// 创建变量解析器
	resolver := NewDefaultVariableResolver()
	resolver.SetParameter("batch_date", "2024-01-01")
	resolver.SetParameter("source_db", "test_db")
	resolver.SetVariable("log_level", "INFO")
	resolver.SetVariable("timeout", "30m")

	// 创建配置解析器
	parser := NewConfigParser(resolver)

	t.Run("ParseJob", func(t *testing.T) {
		// 创建测试作业
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "test-job",
				Description: "Test job with variables",
			},
			Parameters: map[string]string{
				"source_db":  "${source_db}",
				"batch_date": "${batch_date}",
				"today":      "${TODAY}",
			},
			Variables: map[string]string{
				"log_level": "${log_level}",
				"timeout":   "${timeout}",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name:        "START",
					Type:        meta.JobEntryTypeStart,
					Description: "Job started at ${NOW}",
					Config: map[string]interface{}{
						"message": "Processing batch ${batch_date}",
					},
				},
			},
		}

		// 解析作业
		parsedJob, err := parser.ParseJob(job)
		if err != nil {
			t.Fatalf("Failed to parse job: %v", err)
		}

		// 验证参数解析
		if parsedJob.Parameters["source_db"] != "test_db" {
			t.Errorf("Expected source_db to be 'test_db', got '%s'", parsedJob.Parameters["source_db"])
		}

		if parsedJob.Parameters["batch_date"] != "2024-01-01" {
			t.Errorf("Expected batch_date to be '2024-01-01', got '%s'", parsedJob.Parameters["batch_date"])
		}

		// 验证内置变量
		if parsedJob.Parameters["today"] == "" || parsedJob.Parameters["today"] == "${TODAY}" {
			t.Errorf("TODAY variable was not resolved properly")
		}

		// 验证配置中的变量
		config := parsedJob.Entries[0].Config
		if message, ok := config["message"].(string); !ok || message == "Processing batch ${batch_date}" {
			t.Errorf("Message in config was not resolved properly: %v", config["message"])
		}
	})

	t.Run("ParseTransformation", func(t *testing.T) {
		// 创建测试转换
		trans := &meta.TransformationMeta{
			Meta: meta.TransMetaInfo{
				Name:        "test-trans",
				Description: "Test transformation with variables",
			},
			Parameters: map[string]string{
				"input_file":  "/data/${batch_date}/input.csv",
				"output_file": "/data/${batch_date}/output.csv",
			},
			Variables: map[string]string{
				"separator": ",",
				"encoding":  "UTF-8",
			},
			Steps: []meta.StepMeta{
				{
					Name:        "file_input",
					Type:        meta.StepTypeCSVFileInput,
					Description: "Read CSV file for ${batch_date}",
					Config: map[string]interface{}{
						"filename":  "${input_file}",
						"separator": "${separator}",
						"encoding":  "${encoding}",
					},
					Fields: []meta.FieldMeta{
						{
							Name:   "id",
							Type:   meta.FieldTypeInteger,
							Length: 10,
							Format: "ID-${batch_date}",
						},
					},
				},
			},
		}

		// 解析转换
		parsedTrans, err := parser.ParseTransformation(trans)
		if err != nil {
			t.Fatalf("Failed to parse transformation: %v", err)
		}

		// 验证参数解析
		expectedInputFile := "/data/2024-01-01/input.csv"
		if parsedTrans.Parameters["input_file"] != expectedInputFile {
			t.Errorf("Expected input_file to be '%s', got '%s'", expectedInputFile, parsedTrans.Parameters["input_file"])
		}

		// 验证步骤配置
		step := parsedTrans.Steps[0]
		if filename := step.Config["filename"]; filename != expectedInputFile {
			t.Errorf("Expected filename in config to be '%s', got '%v'", expectedInputFile, filename)
		}

		// 验证字段格式
		expectedFormat := "ID-2024-01-01"
		if step.Fields[0].Format != expectedFormat {
			t.Errorf("Expected field format to be '%s', got '%s'", expectedFormat, step.Fields[0].Format)
		}
	})
}

func TestDefaultVariableResolver(t *testing.T) {
	resolver := NewDefaultVariableResolver()

	t.Run("Parameters and Variables", func(t *testing.T) {
		// 设置参数和变量
		resolver.SetParameter("param1", "value1")
		resolver.SetVariable("var1", "value2")

		// 测试解析
		value, err := resolver.Resolve("param1")
		if err != nil {
			t.Fatalf("Failed to resolve parameter: %v", err)
		}
		if value != "value1" {
			t.Errorf("Expected 'value1', got '%s'", value)
		}

		value, err = resolver.Resolve("var1")
		if err != nil {
			t.Fatalf("Failed to resolve variable: %v", err)
		}
		if value != "value2" {
			t.Errorf("Expected 'value2', got '%s'", value)
		}
	})

	t.Run("Built-in Variables", func(t *testing.T) {
		// 测试内置变量
		builtins := []string{"NOW", "TODAY", "YESTERDAY", "TOMORROW", "CURRENT_YEAR", "CURRENT_MONTH", "CURRENT_DAY", "TIMESTAMP", "UUID"}

		for _, builtin := range builtins {
			value, err := resolver.Resolve(builtin)
			if err != nil {
				t.Errorf("Failed to resolve built-in variable '%s': %v", builtin, err)
			}
			if value == "" {
				t.Errorf("Built-in variable '%s' returned empty value", builtin)
			}
		}

		// 验证TODAY格式
		today, _ := resolver.Resolve("TODAY")
		expectedFormat := time.Now().Format("2006-01-02")
		if today != expectedFormat {
			t.Errorf("Expected TODAY to be '%s', got '%s'", expectedFormat, today)
		}
	})

	t.Run("Function Calls", func(t *testing.T) {
		// 测试日期函数
		value, err := resolver.Resolve("DATE()")
		if err != nil {
			t.Fatalf("Failed to resolve DATE(): %v", err)
		}
		if value == "" {
			t.Errorf("DATE() returned empty value")
		}

		// 测试带偏移的日期函数
		value, err = resolver.Resolve("DATE(+1)")
		if err != nil {
			t.Fatalf("Failed to resolve DATE(+1): %v", err)
		}
		if value == "" {
			t.Errorf("DATE(+1) returned empty value")
		}

		// 测试格式化函数
		resolver.SetVariable("test_var", "world")
		value, err = resolver.Resolve("FORMAT('Hello %s', test_var)")
		if err != nil {
			t.Fatalf("Failed to resolve FORMAT function: %v", err)
		}
		expected := "Hello world"
		if value != expected {
			t.Errorf("Expected '%s', got '%s'", expected, value)
		}
	})

	t.Run("Compound Expressions", func(t *testing.T) {
		// 设置作业相关变量
		resolver.SetVariable("job.name", "test-job")
		resolver.SetVariable("step.input.rows_read", "1000")

		// 测试作业属性
		value, err := resolver.Resolve("job.name")
		if err != nil {
			t.Fatalf("Failed to resolve job.name: %v", err)
		}
		if value != "test-job" {
			t.Errorf("Expected 'test-job', got '%s'", value)
		}

		// 测试步骤属性
		value, err = resolver.Resolve("step.input.rows_read")
		if err != nil {
			t.Fatalf("Failed to resolve step.input.rows_read: %v", err)
		}
		if value != "1000" {
			t.Errorf("Expected '1000', got '%s'", value)
		}
	})

	t.Run("Error Cases", func(t *testing.T) {
		// 测试未定义变量
		_, err := resolver.Resolve("undefined_var")
		if err == nil {
			t.Errorf("Expected error for undefined variable")
		}

		// 测试无效函数调用
		_, err = resolver.Resolve("INVALID_FUNC()")
		if err == nil {
			t.Errorf("Expected error for invalid function call")
		}
	})
}

func TestExpressionEvaluator(t *testing.T) {
	resolver := NewDefaultVariableResolver()
	resolver.SetVariable("count", "100")
	resolver.SetVariable("status", "success")

	adapter := NewExprAdapter(resolver)

	t.Run("Simple Conditions", func(t *testing.T) {
		tests := []struct {
			condition string
			expected  bool
		}{
			{"true", true},
			{"false", false},
			{"${count} > 50", true},
			{"${count} < 50", false},
			{"${status} == 'success'", true},
			{"${status} != 'error'", true},
			{"${count} >= 100", true},
			{"${count} <= 100", true},
		}

		for _, test := range tests {
			result, err := adapter.EvaluateToBoolean(test.condition)
			if err != nil {
				t.Errorf("Failed to evaluate condition '%s': %v", test.condition, err)
				continue
			}
			if result != test.expected {
				t.Errorf("Condition '%s': expected %v, got %v", test.condition, test.expected, result)
			}
		}
	})

	t.Run("Empty Condition", func(t *testing.T) {
		result, err := adapter.EvaluateToBoolean("")
		if err != nil {
			t.Errorf("Empty condition should not return error: %v", err)
		}
		if !result {
			t.Errorf("Empty condition should return true")
		}
	})
}

func TestConfigValidator(t *testing.T) {
	validator := NewConfigValidator()

	t.Run("Valid Job", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "valid-job",
				Description: "A valid job",
			},
			Parameters: map[string]string{
				"param1": "value1",
			},
			Variables: map[string]string{
				"var1": "value1",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name:     "START",
					Type:     meta.JobEntryTypeStart,
					Position: meta.Position{X: 100, Y: 100},
				},
				{
					Name:     "SUCCESS",
					Type:     meta.JobEntryTypeSuccess,
					Position: meta.Position{X: 300, Y: 100},
				},
			},
			Hops: []meta.JobHopMeta{
				{
					From: "START",
					To:   "SUCCESS",
				},
			},
		}

		err := validator.ValidateJob(job)
		if err != nil {
			t.Errorf("Valid job should not return error: %v", err)
		}
	})

	t.Run("Invalid Job - No Name", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name: "", // Invalid: empty name
			},
		}

		err := validator.ValidateJob(job)
		if err == nil {
			t.Errorf("Job with empty name should return error")
		}
	})

	t.Run("Invalid Job - No START Entry", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name: "test-job",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "SUCCESS",
					Type: meta.JobEntryTypeSuccess,
				},
			},
		}

		err := validator.ValidateJob(job)
		if err == nil {
			t.Errorf("Job without START entry should return error")
		}
	})

	t.Run("Invalid Job - Duplicate Entry Names", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name: "test-job",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "START",
					Type: meta.JobEntryTypeStart,
				},
				{
					Name: "START", // Duplicate name
					Type: meta.JobEntryTypeSuccess,
				},
			},
		}

		err := validator.ValidateJob(job)
		if err == nil {
			t.Errorf("Job with duplicate entry names should return error")
		}
	})

	t.Run("Invalid Job - Invalid Hop Reference", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name: "test-job",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "START",
					Type: meta.JobEntryTypeStart,
				},
			},
			Hops: []meta.JobHopMeta{
				{
					From: "START",
					To:   "NONEXISTENT", // Invalid reference
				},
			},
		}

		err := validator.ValidateJob(job)
		if err == nil {
			t.Errorf("Job with invalid hop reference should return error")
		}
	})
}

func TestIntegration(t *testing.T) {
	// 测试完整的配置流程：加载 -> 验证 -> 解析

	// 创建验证器
	validator := NewConfigValidator()

	// 创建解析器
	resolver := NewDefaultVariableResolver()
	resolver.SetParameter("batch_date", "2024-01-01")
	parser := NewConfigParser(resolver)

	t.Run("Complete Workflow", func(t *testing.T) {
		// 创建测试作业
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "integration-test-job",
				Description: "Integration test job",
			},
			Parameters: map[string]string{
				"batch_date": "${batch_date}",
			},
			Variables: map[string]string{
				"log_level": "INFO",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name:     "START",
					Type:     meta.JobEntryTypeStart,
					Position: meta.Position{X: 100, Y: 100},
				},
			},
		}

		// 1. 验证配置
		err := validator.ValidateJob(job)
		if err != nil {
			t.Fatalf("Validation failed: %v", err)
		}

		// 2. 解析配置
		parsedJob, err := parser.ParseJob(job)
		if err != nil {
			t.Fatalf("Parsing failed: %v", err)
		}

		// 3. 验证解析结果
		if parsedJob.Parameters["batch_date"] != "2024-01-01" {
			t.Errorf("Expected batch_date to be resolved to '2024-01-01', got '%s'", parsedJob.Parameters["batch_date"])
		}

		// 4. 再次验证解析后的配置
		err = validator.ValidateJob(parsedJob)
		if err != nil {
			t.Errorf("Parsed job validation failed: %v", err)
		}
	})
}
