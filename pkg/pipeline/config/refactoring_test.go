package config

import (
	"testing"
	"time"
)

func TestExprAdapterIntegration(t *testing.T) {
	// 创建增强的变量解析器
	resolver := NewEnhancedVariableResolver()
	resolver.SetParameter("count", "75")
	resolver.SetParameter("status", "success")
	resolver.SetParameter("threshold", "50")
	resolver.SetParameter("factor", "2.5")

	// 创建表达式适配器
	adapter := NewExprAdapter(resolver)

	tests := []struct {
		name     string
		expr     string
		expected interface{}
	}{
		// 算术运算
		{"简单算术", "2 + 3 * 4", 14},
		{"复杂算术", "(count - threshold) * factor", 62.5},

		// 逻辑运算
		{"逻辑与", "count > threshold && status == 'success'", true},
		{"逻辑或", "count < threshold || status == 'success'", true},

		// 三元运算符
		{"三元表达式", "count > threshold ? '超过阈值' : '低于阈值'", "超过阈值"},

		// 内置函数
		{"日期函数", "DATE()", time.Now().Format("2006-01-02")}, // 使用动态当前日期
		{"格式函数", "FORMAT('数量: %s', count)", "数量: 75"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := adapter.EvaluateExpression(tt.expr)
			if err != nil {
				t.Fatalf("表达式求值失败：%v", err)
			}

			// 根据期望的类型执行不同的断言
			switch expected := tt.expected.(type) {
			case int:
				// 支持浮点数与整数比较
				if floatResult, ok := result.(float64); ok && float64(expected) == floatResult {
					// 断言通过
				} else if intResult, ok := result.(int); ok && expected == intResult {
					// 断言通过
				} else {
					t.Errorf("表达式 %q 的结果 %v 不符合预期 %v", tt.expr, result, expected)
				}
			case float64:
				if floatResult, ok := result.(float64); ok && expected == floatResult {
					// 断言通过
				} else {
					t.Errorf("表达式 %q 的结果 %v 不符合预期 %v", tt.expr, result, expected)
				}
			case string:
				if strResult, ok := result.(string); ok && expected == strResult {
					// 断言通过
				} else {
					t.Errorf("表达式 %q 的结果 %v 不符合预期 %v", tt.expr, result, expected)
				}
			case bool:
				if boolResult, ok := result.(bool); ok && expected == boolResult {
					// 断言通过
				} else {
					t.Errorf("表达式 %q 的结果 %v 不符合预期 %v", tt.expr, result, expected)
				}
			default:
				if result != expected {
					t.Errorf("表达式 %q 的结果 %v 不符合预期 %v", tt.expr, result, expected)
				}
			}
		})
	}
}

func TestEnhancedVariableResolverIntegration(t *testing.T) {
	// 创建增强的变量解析器
	resolver := NewEnhancedVariableResolver()
	resolver.SetParameter("count", "75")
	resolver.SetParameter("status", "success")
	resolver.SetVariable("path", "/data")

	tests := []struct {
		name      string
		varName   string
		expected  string
		shouldErr bool
	}{
		// 基本变量
		{"参数访问", "count", "75", false},
		{"变量访问", "path", "/data", false},
		{"内置变量", "TODAY", time.Now().Format("2006-01-02"), false}, // 使用动态当前日期

		// 复合表达式
		{"算术表达式", "count + 5", "80", false},
		{"字符串拼接", "path + '/logs'", "/data/logs", false},

		// 函数调用
		{"日期函数", "DATE()", time.Now().Format("2006-01-02"), false}, // 使用动态当前日期
		{"格式函数", "FORMAT('状态: %s', status)", "状态: success", false},

		// 错误情况
		{"未知变量", "unknown_var", "", true},
		{"语法错误", "count +* 5", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := resolver.Resolve(tt.varName)

			if tt.shouldErr {
				if err == nil {
					t.Errorf("期望错误但得到结果：%v", result)
				}
				return
			}

			if err != nil {
				t.Fatalf("解析失败：%v", err)
			}

			if result != tt.expected {
				t.Errorf("变量 %q 的结果 %q 不符合预期 %q", tt.varName, result, tt.expected)
			}
		})
	}
}

func TestConfigParserWithExprAdapter(t *testing.T) {
	// 创建增强的变量解析器
	resolver := NewEnhancedVariableResolver()
	resolver.SetParameter("count", "75")
	resolver.SetParameter("threshold", "50")

	// 创建配置解析器
	parser := NewConfigParser(resolver)

	tests := []struct {
		name      string
		template  string
		expected  string
		shouldErr bool
	}{
		// 简单变量替换
		{"简单变量", "当前计数：${count}", "当前计数：75", false},

		// 表达式求值
		{"算术表达式", "计算结果：${count * 2}", "计算结果：150", false},
		{"比较表达式", "状态：${count > threshold ? '超出' : '正常'}", "状态：超出", false},

		// 混合模式
		{"混合表达式", "A=${count} B=${count + 10} C=${count * 2}", "A=75 B=85 C=150", false},

		// 复杂表达式
		{"嵌套表达式", "${count > threshold ? '超出' + '(' + (count - threshold) + ')' : '正常'}", "超出(25)", false},

		// 错误情况
		{"语法错误", "${count +* 5}", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := parser.resolveString(tt.template)

			if tt.shouldErr {
				if err == nil {
					t.Errorf("期望错误但得到结果：%v", result)
				}
				return
			}

			if err != nil {
				t.Fatalf("解析失败：%v", err)
			}

			if result != tt.expected {
				t.Errorf("模板 %q 的结果 %q 不符合预期 %q", tt.template, result, tt.expected)
			}
		})
	}
}
