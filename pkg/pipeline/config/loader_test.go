package config

import (
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"admin/pkg/pipeline/meta"
)

func TestFileLoader_LoadJob(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test job JSON file
	jobJSON := `{
		"meta": {
			"name": "test-job",
			"description": "Test job",
			"version": "1.0.0",
			"author": "test",
			"created": "2024-01-01T00:00:00Z",
			"modified": "2024-01-01T00:00:00Z"
		},
		"parameters": {
			"param1": "value1"
		},
		"variables": {
			"var1": "value1"
		},
		"entries": [
			{
				"name": "START",
				"type": "START",
				"description": "Start entry",
				"config": {},
				"position": {"x": 100, "y": 100}
			},
			{
				"name": "SUCCESS",
				"type": "SUCCESS",
				"description": "Success entry",
				"config": {},
				"position": {"x": 300, "y": 100}
			}
		],
		"hops": [
			{
				"from": "START",
				"to": "SUCCESS",
				"evaluation": true,
				"unconditional": true
			}
		]
	}`

	jobFile := filepath.Join(tempDir, "test_job.json")
	err = ioutil.WriteFile(jobFile, []byte(jobJSON), 0o644)
	require.NoError(t, err)

	// Test loading job
	loader := NewFileLoader(tempDir)
	job, err := loader.LoadJob("test_job.json")
	assert.NoError(t, err)
	require.NotNil(t, job)

	// Verify job metadata
	assert.Equal(t, "test-job", job.Meta.Name)
	assert.Equal(t, "Test job", job.Meta.Description)
	assert.Equal(t, "1.0.0", job.Meta.Version)
	assert.Equal(t, "test", job.Meta.Author)

	// Verify parameters and variables
	assert.Equal(t, "value1", job.Parameters["param1"])
	assert.Equal(t, "value1", job.Variables["var1"])

	// Verify entries
	assert.Len(t, job.Entries, 2)
	assert.Equal(t, "START", job.Entries[0].Name)
	assert.Equal(t, meta.JobEntryTypeStart, job.Entries[0].Type)
	assert.Equal(t, "SUCCESS", job.Entries[1].Name)
	assert.Equal(t, meta.JobEntryTypeSuccess, job.Entries[1].Type)

	// Verify hops
	assert.Len(t, job.Hops, 1)
	assert.Equal(t, "START", job.Hops[0].From)
	assert.Equal(t, "SUCCESS", job.Hops[0].To)
	assert.True(t, job.Hops[0].Evaluation)
	assert.True(t, job.Hops[0].Unconditional)
}

func TestFileLoader_LoadJobYAML(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test job YAML file
	jobYAML := `meta:
  name: test-job-yaml
  description: Test job YAML
  version: 1.0.0
  author: test
  created: 2024-01-01T00:00:00Z
  modified: 2024-01-01T00:00:00Z
parameters:
  param1: value1
variables:
  var1: value1
entries:
  - name: START
    type: START
    description: Start entry
    config: {}
    position:
      x: 100
      y: 100
  - name: SUCCESS
    type: SUCCESS
    description: Success entry
    config: {}
    position:
      x: 300
      y: 100
hops:
  - from: START
    to: SUCCESS
    evaluation: true
    unconditional: true`

	jobFile := filepath.Join(tempDir, "test_job.yaml")
	err = ioutil.WriteFile(jobFile, []byte(jobYAML), 0o644)
	require.NoError(t, err)

	// Test loading job from YAML
	loader := NewFileLoader(tempDir)
	job, err := loader.LoadJob("test_job.yaml")
	assert.NoError(t, err)
	require.NotNil(t, job)

	// Verify job metadata
	assert.Equal(t, "test-job-yaml", job.Meta.Name)
	assert.Equal(t, "Test job YAML", job.Meta.Description)
}

func TestFileLoader_LoadTransformation(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test transformation JSON file
	transJSON := `{
		"meta": {
			"name": "test-transformation",
			"description": "Test transformation",
			"version": "1.0.0",
			"author": "test",
			"created": "2024-01-01T00:00:00Z",
			"modified": "2024-01-01T00:00:00Z"
		},
		"parameters": {
			"source_db": "test_db"
		},
		"variables": {
			"timeout": "300"
		},
		"steps": [
			{
				"name": "input_step",
				"type": "TableInput",
				"description": "Input step",
				"config": {
					"connection": "test_conn",
					"sql": "SELECT * FROM users"
				},
				"position": {"x": 100, "y": 100}
			},
			{
				"name": "output_step",
				"type": "TableOutput",
				"description": "Output step",
				"config": {
					"connection": "test_conn",
					"table": "output_table"
				},
				"position": {"x": 300, "y": 100}
			}
		],
		"hops": [
			{
				"from": "input_step",
				"to": "output_step",
				"enabled": true
			}
		]
	}`

	transFile := filepath.Join(tempDir, "test_transformation.json")
	err = ioutil.WriteFile(transFile, []byte(transJSON), 0o644)
	require.NoError(t, err)

	// Test loading transformation
	loader := NewFileLoader(tempDir)
	trans, err := loader.LoadTransformation("test_transformation.json")
	assert.NoError(t, err)
	require.NotNil(t, trans)

	// Verify transformation metadata
	assert.Equal(t, "test-transformation", trans.Meta.Name)
	assert.Equal(t, "Test transformation", trans.Meta.Description)
	assert.Equal(t, "1.0.0", trans.Meta.Version)

	// Verify parameters and variables
	assert.Equal(t, "test_db", trans.Parameters["source_db"])
	assert.Equal(t, "300", trans.Variables["timeout"])

	// Verify steps
	assert.Len(t, trans.Steps, 2)
	assert.Equal(t, "input_step", trans.Steps[0].Name)
	assert.Equal(t, meta.StepTypeTableInput, trans.Steps[0].Type)
	assert.Equal(t, "output_step", trans.Steps[1].Name)
	assert.Equal(t, meta.StepTypeTableOutput, trans.Steps[1].Type)

	// Verify hops
	assert.Len(t, trans.Hops, 1)
	assert.Equal(t, "input_step", trans.Hops[0].From)
	assert.Equal(t, "output_step", trans.Hops[0].To)
	assert.True(t, trans.Hops[0].Enabled)
}

func TestFileLoader_LoadConnections(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test connections JSON file
	connectionsJSON := `{
		"connections": [
			{
				"name": "mysql_conn",
				"type": "DATABASE",
				"description": "MySQL connection",
				"config": {
					"database_type": "mysql",
					"hostname": "localhost",
					"port": "3306",
					"database": "test_db",
					"username": "user",
					"password": "password"
				}
			},
			{
				"name": "redis_conn",
				"type": "REDIS",
				"description": "Redis connection",
				"config": {
					"hostname": "localhost",
					"port": "6379"
				}
			}
		]
	}`

	connectionsFile := filepath.Join(tempDir, "connections.json")
	err = ioutil.WriteFile(connectionsFile, []byte(connectionsJSON), 0o644)
	require.NoError(t, err)

	// Test loading connections
	loader := NewFileLoader(tempDir)
	connections, err := loader.LoadConnections("connections.json")
	assert.NoError(t, err)
	require.NotNil(t, connections)

	// Verify connections
	assert.Len(t, connections.Connections, 2)

	// Verify MySQL connection
	mysqlConn := connections.Connections[0]
	assert.Equal(t, "mysql_conn", mysqlConn.Name)
	assert.Equal(t, meta.ConnectionTypeDatabase, mysqlConn.Type)
	assert.Equal(t, "MySQL connection", mysqlConn.Description)
	assert.Equal(t, "mysql", mysqlConn.Config["database_type"])
	assert.Equal(t, "localhost", mysqlConn.Config["hostname"])

	// Verify Redis connection
	redisConn := connections.Connections[1]
	assert.Equal(t, "redis_conn", redisConn.Name)
	assert.Equal(t, meta.ConnectionTypeRedis, redisConn.Type)
	assert.Equal(t, "Redis connection", redisConn.Description)
	assert.Equal(t, "localhost", redisConn.Config["hostname"])
	assert.Equal(t, "6379", redisConn.Config["port"])
}

func TestFileLoader_SaveAndLoad(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	loader := NewFileLoader(tempDir)

	// Create test job
	job := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "save-test-job",
			Description: "Save test job",
			Version:     "1.0.0",
			Author:      "test",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Parameters: map[string]string{
			"param1": "value1",
		},
		Variables: map[string]string{
			"var1": "value1",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:        "START",
				Type:        meta.JobEntryTypeStart,
				Description: "Start entry",
				Config:      map[string]interface{}{},
				Position:    meta.Position{X: 100, Y: 100},
			},
		},
	}

	// Save job
	err = loader.SaveJob(job, "save_test.json")
	assert.NoError(t, err)

	// Load job back
	loadedJob, err := loader.LoadJob("save_test.json")
	assert.NoError(t, err)
	require.NotNil(t, loadedJob)

	// Verify saved and loaded job match
	assert.Equal(t, job.Meta.Name, loadedJob.Meta.Name)
	assert.Equal(t, job.Meta.Description, loadedJob.Meta.Description)
	assert.Equal(t, job.Parameters["param1"], loadedJob.Parameters["param1"])
	assert.Equal(t, job.Variables["var1"], loadedJob.Variables["var1"])
	assert.Len(t, loadedJob.Entries, 1)
	assert.Equal(t, "START", loadedJob.Entries[0].Name)
}

func TestFileLoader_InvalidFiles(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	loader := NewFileLoader(tempDir)

	// Test loading non-existent file
	_, err = loader.LoadJob("nonexistent.json")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to read")

	// Create invalid JSON file
	invalidJSON := `{"meta": invalid json`
	invalidFile := filepath.Join(tempDir, "invalid.json")
	err = ioutil.WriteFile(invalidFile, []byte(invalidJSON), 0o644)
	require.NoError(t, err)

	// Test loading invalid JSON
	_, err = loader.LoadJob("invalid.json")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse")

	// Create job with validation errors
	invalidJobJSON := `{
		"meta": {
			"name": "",
			"description": "Invalid job"
		},
		"entries": []
	}`
	invalidJobFile := filepath.Join(tempDir, "invalid_job.json")
	err = ioutil.WriteFile(invalidJobFile, []byte(invalidJobJSON), 0o644)
	require.NoError(t, err)

	// Test loading job with validation errors
	_, err = loader.LoadJob("invalid_job.json")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid job configuration")
}

func TestFileLoader_UnsupportedFormat(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	loader := NewFileLoader(tempDir)

	// Create test file with unsupported extension
	testFile := filepath.Join(tempDir, "test.xml")
	err = ioutil.WriteFile(testFile, []byte("<xml></xml>"), 0o644)
	require.NoError(t, err)

	// Test loading unsupported format
	_, err = loader.LoadJob("test.xml")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported file format")
}

func TestFileLoader_DirectoryHandling(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create subdirectory
	subDir := filepath.Join(tempDir, "subdir")
	err = os.MkdirAll(subDir, 0o755)
	require.NoError(t, err)

	// Create job in subdirectory
	jobJSON := `{
		"meta": {
			"name": "subdir-job",
			"description": "Job in subdirectory"
		},
		"entries": [
			{
				"name": "START",
				"type": "START",
				"config": {},
				"position": {"x": 100, "y": 100}
			}
		]
	}`

	jobFile := filepath.Join(subDir, "job.json")
	err = ioutil.WriteFile(jobFile, []byte(jobJSON), 0o644)
	require.NoError(t, err)

	// Test loading job from subdirectory
	loader := NewFileLoader(tempDir)
	job, err := loader.LoadJob("subdir/job.json")
	assert.NoError(t, err)
	require.NotNil(t, job)
	assert.Equal(t, "subdir-job", job.Meta.Name)
}

func TestFileLoader_RelativeAndAbsolutePaths(t *testing.T) {
	// Create temporary directory for test files
	tempDir, err := ioutil.TempDir("", "etl_test_")
	require.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create simple job file
	jobJSON := `{
		"meta": {
			"name": "path-test-job"
		},
		"entries": [
			{
				"name": "START",
				"type": "START",
				"config": {},
				"position": {"x": 100, "y": 100}
			}
		]
	}`

	jobFile := filepath.Join(tempDir, "job.json")
	err = ioutil.WriteFile(jobFile, []byte(jobJSON), 0o644)
	require.NoError(t, err)

	// Test with relative path
	loader := NewFileLoader(tempDir)
	job, err := loader.LoadJob("job.json")
	assert.NoError(t, err)
	require.NotNil(t, job)
	assert.Equal(t, "path-test-job", job.Meta.Name)

	// Test with absolute path should also work through the loader
	job, err = loader.LoadJob("./job.json")
	assert.NoError(t, err)
	require.NotNil(t, job)
	assert.Equal(t, "path-test-job", job.Meta.Name)
}

func TestFileLoader_EmptyBasePath(t *testing.T) {
	// Test loader with empty base path (should use current directory)
	loader := NewFileLoader("")

	// This should not panic and should handle relative paths
	assert.NotNil(t, loader)

	// We can't test actual file loading without creating files in current directory
	// which could interfere with the actual project, so we just verify the loader
	// is constructed properly
	assert.NotNil(t, loader)
}
