package config

import (
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"admin/pkg/pipeline/meta"
)

func TestDefaultVariableResolver_Resolve(t *testing.T) {
	resolver := NewDefaultVariableResolver()

	t.Run("EmptyString", func(t *testing.T) {
		_, err := resolver.Resolve("")
		assert.Error(t, err) // Empty variable name should return error
	})

	t.Run("BuiltinVariables", func(t *testing.T) {
		testCases := []struct {
			name     string
			input    string
			contains string
		}{
			{"NOW", "NOW", ""},
			{"TODAY", "TODAY", ""},
			{"YESTERDAY", "YESTERDAY", ""},
			{"TOMORROW", "TOMORROW", ""},
			{"CURRENT_YEAR", "CURRENT_YEAR", ""},
			{"CURRENT_MONTH", "CURRENT_MONTH", ""},
			{"CURRENT_DAY", "CURRENT_DAY", ""},
			{"TIMESTAMP", "TIMESTAMP", ""},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				result, err := resolver.Resolve(tc.input)
				assert.NoError(t, err)
				assert.NotEqual(t, tc.input, result, "Variable should be resolved")
				assert.NotEmpty(t, result, "Should not be empty")
			})
		}
	})

	t.Run("TimeBasedVariables", func(t *testing.T) {
		now := time.Now()

		result, err := resolver.Resolve("TODAY")
		assert.NoError(t, err)
		assert.Equal(t, now.Format("2006-01-02"), result)

		result, err = resolver.Resolve("CURRENT_YEAR")
		assert.NoError(t, err)
		assert.Equal(t, now.Format("2006"), result)

		result, err = resolver.Resolve("CURRENT_MONTH")
		assert.NoError(t, err)
		assert.Equal(t, now.Format("01"), result)
	})

	t.Run("SystemVariables", func(t *testing.T) {
		// Test USER variable (if available)
		result, err := resolver.Resolve("USER")
		assert.NoError(t, err)
		if user := os.Getenv("USER"); user != "" {
			assert.Equal(t, user, result)
		}

		// Test HOME variable (if available)
		result, err = resolver.Resolve("HOME")
		assert.NoError(t, err)
		if home := os.Getenv("HOME"); home != "" {
			assert.Equal(t, home, result)
		}
	})

	t.Run("CustomVariables", func(t *testing.T) {
		resolver.SetVariable("CUSTOM_VAR", "custom_value")

		result, err := resolver.Resolve("CUSTOM_VAR")
		assert.NoError(t, err)
		assert.Equal(t, "custom_value", result)

		// Test getting variable
		value, exists := resolver.GetVariable("CUSTOM_VAR")
		assert.True(t, exists)
		assert.Equal(t, "custom_value", value)

		// Test non-existent variable
		_, exists = resolver.GetVariable("NON_EXISTENT")
		assert.False(t, exists)
	})

	t.Run("EnvironmentVariables", func(t *testing.T) {
		// Set a test environment variable
		testKey := "TEST_ETL_VAR"
		testValue := "test_etl_value"
		os.Setenv(testKey, testValue)
		defer os.Unsetenv(testKey)

		result, err := resolver.Resolve(testKey)
		assert.NoError(t, err)
		assert.Equal(t, testValue, result)
	})

	t.Run("MultipleVariables", func(t *testing.T) {
		resolver.SetVariable("VAR1", "value1")
		resolver.SetVariable("VAR2", "value2")

		// Create a parser to test string interpolation
		parser := NewConfigParser(resolver)

		result, err := parser.ResolveVariablesInString("${VAR1}_${VAR2}_${TODAY}")
		assert.NoError(t, err)
		assert.Contains(t, result, "value1_value2_")
		assert.NotContains(t, result, "${")
	})

	t.Run("NestedText", func(t *testing.T) {
		resolver.SetVariable("DB_HOST", "localhost")
		resolver.SetVariable("DB_PORT", "5432")

		// Create a parser to test string interpolation
		parser := NewConfigParser(resolver)

		result, err := parser.ResolveVariablesInString("jdbc:postgresql://${DB_HOST}:${DB_PORT}/testdb")
		assert.NoError(t, err)
		assert.Equal(t, "***************************************", result)
	})

	t.Run("UnknownVariables", func(t *testing.T) {
		_, err := resolver.Resolve("UNKNOWN_VAR")
		assert.Error(t, err, "Unknown variables should return error")
	})

	t.Run("CaseInsensitiveBuiltins", func(t *testing.T) {
		result1, err := resolver.Resolve("NOW")
		assert.NoError(t, err)

		_, err = resolver.Resolve("now")
		assert.Error(t, err, "Built-in variables should be case sensitive")

		// Both should be resolved (case insensitive)
		assert.NotContains(t, result1, "${")
	})

	t.Run("InvalidVariableName", func(t *testing.T) {
		// Test with invalid variable names
		_, err := resolver.Resolve("")
		assert.Error(t, err, "Empty variable name should return error")

		_, err = resolver.Resolve("123VAR")
		assert.Error(t, err, "Invalid variable name should return error")
	})
}

func TestDefaultVariableResolver_SetAndGetVariable(t *testing.T) {
	resolver := NewDefaultVariableResolver()

	t.Run("SetAndGet", func(t *testing.T) {
		resolver.SetVariable("test_key", "test_value")

		value, exists := resolver.GetVariable("test_key")
		assert.True(t, exists)
		assert.Equal(t, "test_value", value)
	})

	t.Run("OverwriteVariable", func(t *testing.T) {
		resolver.SetVariable("key", "value1")
		resolver.SetVariable("key", "value2")

		value, exists := resolver.GetVariable("key")
		assert.True(t, exists)
		assert.Equal(t, "value2", value)
	})

	t.Run("EmptyValue", func(t *testing.T) {
		resolver.SetVariable("empty_key", "")

		value, exists := resolver.GetVariable("empty_key")
		assert.True(t, exists)
		assert.Equal(t, "", value)
	})

	t.Run("NonExistentKey", func(t *testing.T) {
		_, exists := resolver.GetVariable("non_existent")
		assert.False(t, exists)
	})
}

func TestValidator_ValidateJobFile(t *testing.T) {
	validator := NewBusinessValidator()

	// Create test directory
	testDir := filepath.Join(os.TempDir(), "etl_test_validator")
	os.MkdirAll(testDir, 0o755)
	defer os.RemoveAll(testDir)

	t.Run("ValidJob", func(t *testing.T) {
		// Create a valid job file
		jobFile := "valid_job.json"
		validJob := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "test_job",
				Description: "Test job",
				Version:     "1.0",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "START",
					Type: meta.JobEntryTypeStart,
				},
				{
					Name: "TRANSFORM",
					Type: meta.JobEntryTypeTrans,
					Config: map[string]interface{}{
						"transformation_file": "test.ktr",
					},
				},
				{
					Name: "SUCCESS",
					Type: meta.JobEntryTypeSuccess,
				},
			},
			Hops: []meta.JobHopMeta{
				{From: "START", To: "TRANSFORM"},
				{From: "TRANSFORM", To: "SUCCESS"},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveJob(validJob, jobFile)
		require.NoError(t, err)

		err = validator.ValidateJobFile(filepath.Join(testDir, jobFile))
		assert.NoError(t, err)
	})

	t.Run("InvalidJobFile", func(t *testing.T) {
		// Test with non-existent file
		err := validator.ValidateJobFile("non_existent.json")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validation failed")
	})

	t.Run("JobWithOrphanedEntry", func(t *testing.T) {
		jobFile := "orphaned_job.json"
		orphanedJob := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "orphaned_job",
				Description: "Job with orphaned entry",
				Version:     "1.0",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "START",
					Type: meta.JobEntryTypeStart,
				},
				{
					Name: "ORPHANED",
					Type: meta.JobEntryTypeTrans,
					Config: map[string]interface{}{
						"transformation_file": "test.ktr",
					},
				},
				{
					Name: "SUCCESS",
					Type: meta.JobEntryTypeSuccess,
				},
			},
			Hops: []meta.JobHopMeta{
				{From: "START", To: "SUCCESS"},
				// ORPHANED entry is not connected
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveJob(orphanedJob, jobFile)
		require.NoError(t, err)

		err = validator.ValidateJobFile(filepath.Join(testDir, jobFile))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "orphaned entry found")
	})

	t.Run("JobWithCycle", func(t *testing.T) {
		jobFile := "cycle_job.json"
		cyclicJob := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "cyclic_job",
				Description: "Job with cycle",
				Version:     "1.0",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "START",
					Type: meta.JobEntryTypeStart,
				},
				{
					Name: "STEP1",
					Type: meta.JobEntryTypeTrans,
					Config: map[string]interface{}{
						"transformation_file": "test1.ktr",
					},
				},
				{
					Name: "STEP2",
					Type: meta.JobEntryTypeTrans,
					Config: map[string]interface{}{
						"transformation_file": "test2.ktr",
					},
				},
				{
					Name: "SUCCESS",
					Type: meta.JobEntryTypeSuccess,
				},
			},
			Hops: []meta.JobHopMeta{
				{From: "START", To: "STEP1"},
				{From: "STEP1", To: "STEP2"},
				{From: "STEP2", To: "STEP1"}, // Creates a cycle
				{From: "STEP2", To: "SUCCESS"},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveJob(cyclicJob, jobFile)
		require.NoError(t, err)

		err = validator.ValidateJobFile(filepath.Join(testDir, jobFile))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cycle detected")
	})
}

func TestValidator_ValidateTransformationFile(t *testing.T) {
	validator := NewBusinessValidator()

	// Create test directory
	testDir := filepath.Join(os.TempDir(), "etl_test_validator_trans")
	os.MkdirAll(testDir, 0o755)
	defer os.RemoveAll(testDir)

	t.Run("ValidTransformation", func(t *testing.T) {
		transFile := "valid_trans.json"
		validTrans := &meta.TransformationMeta{
			Meta: meta.TransMetaInfo{
				Name:        "test_transformation",
				Description: "Test transformation",
				Version:     "1.0",
			},
			Steps: []meta.StepMeta{
				{
					Name: "input",
					Type: meta.StepTypeTableInput,
					Config: map[string]interface{}{
						"connection": "test_db",
						"sql":        "SELECT * FROM source_table",
					},
				},
				{
					Name: "output",
					Type: meta.StepTypeTableOutput,
					Config: map[string]interface{}{
						"connection": "test_db",
						"table":      "target_table",
					},
				},
			},
			Hops: []meta.HopMeta{
				{From: "input", To: "output"},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveTransformation(validTrans, transFile)
		require.NoError(t, err)

		err = validator.ValidateTransformationFile(filepath.Join(testDir, transFile))
		assert.NoError(t, err)
	})

	t.Run("TransformationWithoutInput", func(t *testing.T) {
		transFile := "no_input_trans.json"
		noInputTrans := &meta.TransformationMeta{
			Meta: meta.TransMetaInfo{
				Name:        "no_input_transformation",
				Description: "Transformation without input",
				Version:     "1.0",
			},
			Steps: []meta.StepMeta{
				{
					Name: "output",
					Type: meta.StepTypeTableOutput,
					Config: map[string]interface{}{
						"connection": "test_db",
						"table":      "target_table",
					},
				},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveTransformation(noInputTrans, transFile)
		require.NoError(t, err)

		err = validator.ValidateTransformationFile(filepath.Join(testDir, transFile))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "must have at least one input step")
	})

	t.Run("TransformationWithoutOutput", func(t *testing.T) {
		transFile := "no_output_trans.json"
		noOutputTrans := &meta.TransformationMeta{
			Meta: meta.TransMetaInfo{
				Name:        "no_output_transformation",
				Description: "Transformation without output",
				Version:     "1.0",
			},
			Steps: []meta.StepMeta{
				{
					Name: "input",
					Type: meta.StepTypeTableInput,
					Config: map[string]interface{}{
						"connection": "test_db",
						"sql":        "SELECT * FROM source_table",
					},
				},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveTransformation(noOutputTrans, transFile)
		require.NoError(t, err)

		err = validator.ValidateTransformationFile(filepath.Join(testDir, transFile))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "must have at least one output step")
	})

	t.Run("TransformationWithOrphanedStep", func(t *testing.T) {
		transFile := "orphaned_step_trans.json"
		orphanedStepTrans := &meta.TransformationMeta{
			Meta: meta.TransMetaInfo{
				Name:        "orphaned_step_transformation",
				Description: "Transformation with orphaned step",
				Version:     "1.0",
			},
			Steps: []meta.StepMeta{
				{
					Name: "input",
					Type: meta.StepTypeTableInput,
					Config: map[string]interface{}{
						"connection": "test_db",
						"sql":        "SELECT * FROM source_table",
					},
				},
				{
					Name: "orphaned",
					Type: meta.StepTypeCalculator,
					Config: map[string]interface{}{
						"calculation": "A + B",
					},
				},
				{
					Name: "output",
					Type: meta.StepTypeTableOutput,
					Config: map[string]interface{}{
						"connection": "test_db",
						"table":      "target_table",
					},
				},
			},
			Hops: []meta.HopMeta{
				{From: "input", To: "output"},
				// orphaned step is not connected
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveTransformation(orphanedStepTrans, transFile)
		require.NoError(t, err)

		err = validator.ValidateTransformationFile(filepath.Join(testDir, transFile))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "orphaned step found")
	})
}

func TestValidator_ValidateConnectionsFile(t *testing.T) {
	validator := NewBusinessValidator()

	// Create test directory
	testDir := filepath.Join(os.TempDir(), "etl_test_validator_conn")
	os.MkdirAll(testDir, 0o755)
	defer os.RemoveAll(testDir)

	t.Run("ValidConnections", func(t *testing.T) {
		connFile := "valid_connections.json"
		validConnections := &meta.ConnectionsConfig{
			Connections: []meta.ConnectionMeta{
				{
					Name: "test_db",
					Type: meta.ConnectionTypeDatabase,
					Config: map[string]interface{}{
						"database_type": "postgresql",
						"hostname":      "localhost",
						"port":          5432,
						"database":      "testdb",
						"username":      "user",
						"password":      "pass",
					},
				},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveConnections(validConnections, connFile)
		require.NoError(t, err)

		err = validator.ValidateConnectionsFile(filepath.Join(testDir, connFile))
		assert.NoError(t, err)
	})

	t.Run("InvalidConnectionNames", func(t *testing.T) {
		connFile := "invalid_name_connections.json"
		invalidConnections := &meta.ConnectionsConfig{
			Connections: []meta.ConnectionMeta{
				{
					Name: "123invalid", // Starts with number
					Type: meta.ConnectionTypeDatabase,
					Config: map[string]interface{}{
						"database_type": "postgresql",
						"hostname":      "localhost",
						"port":          5432,
						"database":      "testdb",
						"username":      "user",
					},
				},
				{
					Name: "valid-name",
					Type: meta.ConnectionTypeDatabase,
					Config: map[string]interface{}{
						"database_type": "mysql",
						"hostname":      "localhost",
						"port":          3306,
						"database":      "testdb",
						"username":      "user",
					},
				},
				{
					Name: "invalid name", // Contains space
					Type: meta.ConnectionTypeRedis,
					Config: map[string]interface{}{
						"hostname": "localhost",
						"port":     6379,
					},
				},
			},
		}

		loader := NewFileLoader(testDir)
		err := loader.SaveConnections(invalidConnections, connFile)
		require.NoError(t, err)

		err = validator.ValidateConnectionsFile(filepath.Join(testDir, connFile))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid connection name format")
	})

	t.Run("NonExistentFile", func(t *testing.T) {
		err := validator.ValidateConnectionsFile("non_existent_connections.json")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "validation failed")
	})
}

func TestValidator_BusinessRules(t *testing.T) {
	validator := NewBusinessValidator()

	t.Run("JobCycleDetection", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{Name: "test_job"},
			Entries: []meta.JobEntryMeta{
				{Name: "A", Type: meta.JobEntryTypeStart},
				{Name: "B", Type: meta.JobEntryTypeTrans, Config: map[string]interface{}{"transformation_file": "test.ktr"}},
				{Name: "C", Type: meta.JobEntryTypeTrans, Config: map[string]interface{}{"transformation_file": "test.ktr"}},
				{Name: "D", Type: meta.JobEntryTypeSuccess},
			},
			Hops: []meta.JobHopMeta{
				{From: "A", To: "B"},
				{From: "B", To: "C"},
				{From: "C", To: "B"}, // Creates cycle B -> C -> B
				{From: "C", To: "D"},
			},
		}

		err := validator.validateJobBusinessRules(job)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cycle detected")
	})

	t.Run("JobNoCycle", func(t *testing.T) {
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{Name: "test_job"},
			Entries: []meta.JobEntryMeta{
				{Name: "A", Type: meta.JobEntryTypeStart},
				{Name: "B", Type: meta.JobEntryTypeTrans, Config: map[string]interface{}{"transformation_file": "test.ktr"}},
				{Name: "C", Type: meta.JobEntryTypeTrans, Config: map[string]interface{}{"transformation_file": "test.ktr"}},
				{Name: "D", Type: meta.JobEntryTypeSuccess},
			},
			Hops: []meta.JobHopMeta{
				{From: "A", To: "B"},
				{From: "A", To: "C"},
				{From: "B", To: "D"},
				{From: "C", To: "D"},
			},
		}

		err := validator.validateJobBusinessRules(job)
		assert.NoError(t, err)
	})

	t.Run("ConnectionNameValidation", func(t *testing.T) {
		validNames := []string{
			"valid_name",
			"valid-name",
			"validName123",
			"a",
			"A_B_C",
		}

		invalidNames := []string{
			"123invalid",   // Starts with number
			"invalid name", // Contains space
			"invalid@name", // Contains special character
			"",             // Empty
		}

		connections := &meta.ConnectionsConfig{}

		for _, name := range validNames {
			connections.Connections = []meta.ConnectionMeta{
				{Name: name, Type: meta.ConnectionTypeDatabase},
			}
			err := validator.validateConnectionsBusinessRules(connections)
			assert.NoError(t, err, "Name %s should be valid", name)
		}

		for _, name := range invalidNames {
			if name == "" {
				continue // Empty name will be caught by earlier validation
			}
			connections.Connections = []meta.ConnectionMeta{
				{Name: name, Type: meta.ConnectionTypeDatabase},
			}
			err := validator.validateConnectionsBusinessRules(connections)
			assert.Error(t, err, "Name %s should be invalid", name)
		}
	})
}

func TestVariableResolver_Integration(t *testing.T) {
	resolver := NewDefaultVariableResolver()

	t.Run("DatabaseConnectionString", func(t *testing.T) {
		resolver.SetVariable("DB_HOST", "localhost")
		resolver.SetVariable("DB_PORT", "5432")
		resolver.SetVariable("DB_NAME", "testdb")

		// Use ConfigParser for string interpolation
		parser := NewConfigParser(resolver)
		connectionString := "postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}"
		result, err := parser.ResolveVariablesInString(connectionString)

		assert.NoError(t, err)
		assert.Equal(t, "postgresql://localhost:5432/testdb", result)
	})

	t.Run("LogFilePath", func(t *testing.T) {
		// Use ConfigParser for string interpolation
		parser := NewConfigParser(resolver)
		logPath := "/var/log/admin/${TODAY}/job_${TIMESTAMP}.log"
		result, err := parser.ResolveVariablesInString(logPath)

		assert.NoError(t, err)
		assert.Contains(t, result, "/var/log/admin/")
		assert.Contains(t, result, "/job_")
		assert.Contains(t, result, ".log")
		assert.NotContains(t, result, "${")
	})

	t.Run("ConditionalExpression", func(t *testing.T) {
		resolver.SetVariable("ENVIRONMENT", "PROD")

		// Use ConfigParser for string interpolation
		parser := NewConfigParser(resolver)
		expression := "IF ${ENVIRONMENT} = 'PROD' THEN '/prod/data' ELSE '/dev/data'"
		result, err := parser.ResolveVariablesInString(expression)

		assert.NoError(t, err)
		assert.Equal(t, "IF PROD = 'PROD' THEN '/prod/data' ELSE '/dev/data'", result)
	})
}
