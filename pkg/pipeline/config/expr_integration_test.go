package config

import (
	"testing"

	"admin/pkg/pipeline/meta"
)

func TestExpressionEngineIntegration(t *testing.T) {
	// 创建变量解析器
	resolver := NewDefaultVariableResolver()
	resolver.SetParameter("count", "75")
	resolver.SetParameter("status", "success")
	resolver.SetParameter("threshold", "50")
	resolver.SetParameter("factor", "2.5")
	resolver.SetVariable("base_path", "/data")
	resolver.SetVariable("date_format", "2006-01-02")

	// 创建配置解析器
	parser := NewConfigParser(resolver)

	t.Run("Advanced Expression Syntax", func(t *testing.T) {
		// 创建测试作业，包含高级表达式
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "expr-test-job",
				Description: "Job testing advanced expressions",
			},
			Parameters: map[string]string{
				"computed_value": "${count * factor}",
				"status_check":   "${status == 'success' ? 'OK' : 'FAILED'}",
				"threshold_met":  "${count > threshold}",
			},
			Variables: map[string]string{
				"dynamic_path": "${base_path + '/' + TODAY}",
				"complex_calc": "${(count + threshold) / 2}",
			},
			Entries: []meta.JobEntryMeta{
				{
					Name: "conditional_step",
					Type: meta.JobEntryTypeScript,
					Config: map[string]interface{}{
						"condition": "${count > threshold && status == 'success'}",
						"message":   "${'Processing ' + count + ' records'}",
					},
				},
			},
			Hops: []meta.JobHopMeta{
				{
					From:      "conditional_step",
					To:        "END",
					Condition: "${threshold_met}",
				},
			},
		}

		// 解析作业
		parsedJob, err := parser.ParseJob(job)
		if err != nil {
			t.Fatalf("Failed to parse job with expressions: %v", err)
		}

		// 验证数学运算
		if parsedJob.Parameters["computed_value"] != "187.5" { // 75 * 2.5
			t.Errorf("Expected computed_value to be '187.5', got '%s'", parsedJob.Parameters["computed_value"])
		}

		// 验证三元运算符
		if parsedJob.Parameters["status_check"] != "OK" {
			t.Errorf("Expected status_check to be 'OK', got '%s'", parsedJob.Parameters["status_check"])
		}

		// 验证布尔表达式
		if parsedJob.Parameters["threshold_met"] != "true" {
			t.Errorf("Expected threshold_met to be 'true', got '%s'", parsedJob.Parameters["threshold_met"])
		}

		// 验证复杂计算
		if parsedJob.Variables["complex_calc"] != "62.5" { // (75 + 50) / 2
			t.Errorf("Expected complex_calc to be '62.5', got '%s'", parsedJob.Variables["complex_calc"])
		}
	})

	t.Run("Function Calls", func(t *testing.T) {
		// 测试内置函数调用
		trans := &meta.TransformationMeta{
			Meta: meta.TransMetaInfo{
				Name: "function-test",
			},
			Parameters: map[string]string{
				"math_result": "${max(count, threshold)}",
				"string_len":  "${length(status)}",
				"upper_case":  "${upper(status)}",
			},
			Variables: map[string]string{
				"array_access": "${[1, 2, 3][1]}", // Should return 2
				"conditional":  "${count > 100 ? 'high' : 'normal'}",
			},
		}

		// 解析转换
		parsedTrans, err := parser.ParseTransformation(trans)
		if err != nil {
			t.Fatalf("Failed to parse transformation with functions: %v", err)
		}

		// 验证max函数
		if parsedTrans.Parameters["math_result"] != "75" { // max(75, 50)
			t.Errorf("Expected math_result to be '75', got '%s'", parsedTrans.Parameters["math_result"])
		}

		// 验证len函数
		if parsedTrans.Parameters["string_len"] != "7" { // len("success")
			t.Errorf("Expected string_len to be '7', got '%s'", parsedTrans.Parameters["string_len"])
		}

		// 验证upper函数
		if parsedTrans.Parameters["upper_case"] != "SUCCESS" {
			t.Errorf("Expected upper_case to be 'SUCCESS', got '%s'", parsedTrans.Parameters["upper_case"])
		}

		// 验证数组访问
		if parsedTrans.Variables["array_access"] != "2" {
			t.Errorf("Expected array_access to be '2', got '%s'", parsedTrans.Variables["array_access"])
		}

		// 验证条件表达式
		if parsedTrans.Variables["conditional"] != "normal" {
			t.Errorf("Expected conditional to be 'normal', got '%s'", parsedTrans.Variables["conditional"])
		}
	})

	t.Run("Condition Evaluation", func(t *testing.T) {
		adapter := NewExprAdapter(resolver)

		tests := []struct {
			condition string
			expected  bool
			desc      string
		}{
			{"${count > threshold}", true, "simple comparison"},
			{"${count > threshold && status == 'success'}", true, "logical AND"},
			{"${count < threshold || status == 'success'}", true, "logical OR"},
			{"${count >= 75}", true, "greater than or equal"},
			{"${status != 'error'}", true, "not equal"},
			{"${count * factor > 100}", true, "arithmetic in condition"},
		}

		for _, test := range tests {
			result, err := adapter.EvaluateToBoolean(test.condition)
			if err != nil {
				t.Errorf("Failed to evaluate condition '%s': %v", test.condition, err)
				continue
			}
			if result != test.expected {
				t.Errorf("Condition '%s' (%s): expected %v, got %v",
					test.condition, test.desc, test.expected, result)
			}
		}
	})

	t.Run("Backward Compatibility", func(t *testing.T) {
		// 确保原有的简单变量替换仍然工作
		job := &meta.JobMeta{
			Meta: meta.JobMetaInfo{
				Name:        "compat-test",
				Description: "Backward compatibility test",
			},
			Parameters: map[string]string{
				"simple_var":   "${count}",
				"builtin_var":  "${TODAY}",
				"nested_path":  "/data/${CURRENT_YEAR}/${CURRENT_MONTH}",
				"plain_string": "no variables here",
			},
		}

		parsedJob, err := parser.ParseJob(job)
		if err != nil {
			t.Fatalf("Failed to parse backward compatibility job: %v", err)
		}

		// 验证简单变量替换
		if parsedJob.Parameters["simple_var"] != "75" {
			t.Errorf("Expected simple_var to be '75', got '%s'", parsedJob.Parameters["simple_var"])
		}

		// 验证内置变量
		if parsedJob.Parameters["builtin_var"] == "${TODAY}" || parsedJob.Parameters["builtin_var"] == "" {
			t.Errorf("TODAY variable was not resolved: %s", parsedJob.Parameters["builtin_var"])
		}

		// 验证普通字符串保持不变
		if parsedJob.Parameters["plain_string"] != "no variables here" {
			t.Errorf("Plain string was modified: %s", parsedJob.Parameters["plain_string"])
		}
	})
}
