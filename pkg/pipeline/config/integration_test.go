package config

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"admin/pkg/pipeline/config/formats"
	"admin/pkg/pipeline/meta"
)

// TestConfigParserIntegration 配置解析器集成测试
func TestConfigParserIntegration(t *testing.T) {
	// 创建变量解析器
	resolver := NewDefaultVariableResolver()
	resolver.SetParameter("batch_date", "2024-01-01")
	resolver.SetParameter("source_db", "user_source")
	resolver.SetVariable("log_level", "INFO")

	// 创建配置解析器
	parser := NewConfigParser(resolver)

	// 创建测试作业
	job := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "test-job-${batch_date}",
			Description: "Test job for ${source_db}",
			Version:     "1.0.0",
			Author:      "ETL Team",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Parameters: map[string]string{
			"target_db":   "warehouse",
			"batch_size":  "1000",
			"retry_count": "3",
		},
		Variables: map[string]string{
			"timeout":  "30m",
			"log_file": "/logs/admin-${batch_date}.log",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:        "START",
				Type:        meta.JobEntryTypeStart,
				Description: "Job start for ${batch_date}",
				Config:      map[string]interface{}{},
				Position:    meta.Position{X: 100, Y: 100},
			},
			{
				Name:        "extract_data",
				Type:        meta.JobEntryTypeTrans,
				Description: "Extract data from ${source_db}",
				Config: map[string]interface{}{
					"transformation_file": "extract_users.json",
					"parameters": map[string]interface{}{
						"source_db":  "${source_db}",
						"batch_date": "${batch_date}",
						"log_level":  "${log_level}",
					},
				},
				Position: meta.Position{X: 300, Y: 100},
			},
		},
		Hops: []meta.JobHopMeta{
			{
				From:          "START",
				To:            "extract_data",
				Evaluation:    true,
				Unconditional: true,
			},
		},
	}

	// 解析作业配置
	parsedJob, err := parser.ParseJob(job)
	require.NoError(t, err)
	require.NotNil(t, parsedJob)

	// 验证解析结果
	assert.Equal(t, "test-job-2024-01-01", parsedJob.Meta.Name)
	assert.Equal(t, "Test job for user_source", parsedJob.Meta.Description)
	assert.Equal(t, "Job start for 2024-01-01", parsedJob.Entries[0].Description)
	assert.Equal(t, "Extract data from user_source", parsedJob.Entries[1].Description)

	// 验证参数解析
	extractConfig := parsedJob.Entries[1].Config
	params := extractConfig["parameters"].(map[string]interface{})
	assert.Equal(t, "user_source", params["source_db"])
	assert.Equal(t, "2024-01-01", params["batch_date"])
	assert.Equal(t, "INFO", params["log_level"])

	// 验证变量解析
	assert.Equal(t, "/logs/admin-2024-01-01.log", parsedJob.Variables["log_file"])
}

// TestConfigValidatorIntegration 配置验证器集成测试
func TestConfigValidatorIntegration(t *testing.T) {
	validator := NewConfigValidator()

	// 创建有效的作业配置
	validJob := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "valid-job",
			Description: "Valid job for testing",
			Version:     "1.0.0",
		},
		Parameters: map[string]string{
			"param1": "value1",
		},
		Variables: map[string]string{
			"var1": "value1",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:     "START",
				Type:     meta.JobEntryTypeStart,
				Config:   map[string]interface{}{},
				Position: meta.Position{X: 100, Y: 100},
			},
			{
				Name:     "SUCCESS",
				Type:     meta.JobEntryTypeSuccess,
				Config:   map[string]interface{}{},
				Position: meta.Position{X: 300, Y: 100},
			},
		},
		Hops: []meta.JobHopMeta{
			{
				From:          "START",
				To:            "SUCCESS",
				Evaluation:    true,
				Unconditional: true,
			},
		},
	}

	// 验证有效配置
	err := validator.ValidateJob(validJob)
	assert.NoError(t, err)

	// 创建无效的作业配置（缺少START条目）
	invalidJob := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name: "invalid-job",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:     "SUCCESS",
				Type:     meta.JobEntryTypeSuccess,
				Config:   map[string]interface{}{},
				Position: meta.Position{X: 300, Y: 100},
			},
		},
	}

	// 验证无效配置
	err = validator.ValidateJob(invalidJob)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "START")
}

// TestVariableResolverIntegration 变量解析器集成测试
func TestVariableResolverIntegration(t *testing.T) {
	resolver := NewDefaultVariableResolver()

	// 测试内置变量
	today, err := resolver.Resolve("TODAY")
	require.NoError(t, err)
	assert.NotEmpty(t, today)

	now, err := resolver.Resolve("NOW")
	require.NoError(t, err)
	assert.NotEmpty(t, now)

	// 测试自定义参数和变量
	resolver.SetParameter("test_param", "param_value")
	resolver.SetVariable("test_var", "var_value")

	paramValue, err := resolver.Resolve("test_param")
	require.NoError(t, err)
	assert.Equal(t, "param_value", paramValue)

	varValue, err := resolver.Resolve("test_var")
	require.NoError(t, err)
	assert.Equal(t, "var_value", varValue)

	// 测试不存在的变量
	_, err = resolver.Resolve("nonexistent")
	assert.Error(t, err)
}

// TestExpressionEvaluatorIntegration 表达式求值器集成测试
func TestExpressionEvaluatorIntegration(t *testing.T) {
	resolver := NewDefaultVariableResolver()
	resolver.SetVariable("status", "success")
	resolver.SetVariable("count", "100")

	adapter := NewExprAdapter(resolver)

	// 测试简单条件
	result, err := adapter.EvaluateToBoolean("true")
	require.NoError(t, err)
	assert.True(t, result)

	result, err = adapter.EvaluateToBoolean("false")
	require.NoError(t, err)
	assert.False(t, result)

	// 测试比较操作
	result, err = adapter.EvaluateToBoolean("${status} == 'success'")
	require.NoError(t, err)
	assert.True(t, result)

	result, err = adapter.EvaluateToBoolean("${count} > 50")
	require.NoError(t, err)
	assert.True(t, result)
}

// TestFormatsIntegration 格式集成测试
func TestFormatsIntegration(t *testing.T) {
	// 测试格式工厂
	factory := formats.NewParserFactory()

	jsonParser := factory.GetParser("json")
	assert.NotNil(t, jsonParser)

	yamlParser := factory.GetParser("yaml")
	assert.NotNil(t, yamlParser)

	xmlParser := factory.GetParser("xml")
	assert.NotNil(t, xmlParser)

	// 测试不支持的格式（会返回默认的JSON解析器）
	defaultParser := factory.GetParser("unsupported")
	assert.NotNil(t, defaultParser)

	// 创建测试作业
	job := &meta.JobMeta{
		Meta: meta.JobMetaInfo{
			Name:        "test-job",
			Description: "Test job",
			Version:     "1.0.0",
			Author:      "Test Author",
			Created:     time.Now(),
			Modified:    time.Now(),
		},
		Parameters: map[string]string{
			"param1": "value1",
		},
		Variables: map[string]string{
			"var1": "value1",
		},
		Entries: []meta.JobEntryMeta{
			{
				Name:     "START",
				Type:     meta.JobEntryTypeStart,
				Config:   map[string]interface{}{},
				Position: meta.Position{X: 100, Y: 100},
			},
		},
		Hops: []meta.JobHopMeta{},
	}

	// 测试JSON序列化和反序列化
	jsonData, err := jsonParser.MarshalJob(job)
	require.NoError(t, err)
	assert.NotEmpty(t, jsonData)

	parsedJob, err := jsonParser.ParseJob(jsonData)
	require.NoError(t, err)
	assert.Equal(t, job.Meta.Name, parsedJob.Meta.Name)

	// 测试YAML序列化和反序列化
	yamlData, err := yamlParser.MarshalJob(job)
	require.NoError(t, err)
	assert.NotEmpty(t, yamlData)

	parsedJobYaml, err := yamlParser.ParseJob(yamlData)
	require.NoError(t, err)
	assert.Equal(t, job.Meta.Name, parsedJobYaml.Meta.Name)

	// 测试XML序列化和反序列化
	xmlData, err := xmlParser.MarshalJob(job)
	require.NoError(t, err)
	assert.NotEmpty(t, xmlData)

	parsedJobXml, err := xmlParser.ParseJob(xmlData)
	require.NoError(t, err)
	assert.Equal(t, job.Meta.Name, parsedJobXml.Meta.Name)
}
