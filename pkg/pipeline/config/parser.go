package config

import (
	"encoding/json"
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"admin/pkg/pipeline/meta"
)

// VariableResolver 变量解析器接口
type VariableResolver interface {
	Resolve(name string) (string, error)
	SetVariable(name, value string)
	GetVariable(name string) (string, bool)
	SetParameter(name, value string)
	GetParameter(name string) (string, bool)
	// 新增方法：获取所有变量和参数
	GetAllVariables() map[string]string
	GetAllParameters() map[string]string
}

// Parser 配置解析器接口
type Parser interface {
	ParseJob(job *meta.JobMeta) (*meta.JobMeta, error)
	ParseTransformation(trans *meta.TransformationMeta) (*meta.TransformationMeta, error)
	SetVariableResolver(resolver VariableResolver)
}

// ConfigParser 默认配置解析器实现
type ConfigParser struct {
	variableResolver VariableResolver
	variablePattern  *regexp.Regexp
	exprAdapter      *ExprAdapter
}

// NewConfigParser 创建配置解析器
func NewConfigParser(resolver VariableResolver) *ConfigParser {
	parser := &ConfigParser{
		variableResolver: resolver,
		variablePattern:  regexp.MustCompile(`\$\{([^}]+)\}`),
		exprAdapter:      NewExprAdapter(resolver),
	}
	return parser
}

// SetVariableResolver 设置变量解析器
func (p *ConfigParser) SetVariableResolver(resolver VariableResolver) {
	p.variableResolver = resolver
	p.exprAdapter = NewExprAdapter(resolver)
}

// ParseJob 解析作业配置（包含变量替换）
func (p *ConfigParser) ParseJob(job *meta.JobMeta) (*meta.JobMeta, error) {
	if job == nil {
		return nil, fmt.Errorf("job configuration is nil")
	}

	// 深拷贝作业配置
	data, err := json.Marshal(job)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal job for parsing: %w", err)
	}

	var parsedJob meta.JobMeta
	if err := json.Unmarshal(data, &parsedJob); err != nil {
		return nil, fmt.Errorf("failed to unmarshal job for parsing: %w", err)
	}

	// 解析参数和变量
	if err := p.resolveJobVariables(&parsedJob); err != nil {
		return nil, fmt.Errorf("failed to resolve job variables: %w", err)
	}

	return &parsedJob, nil
}

// ParseTransformation 解析转换配置（包含变量替换）
func (p *ConfigParser) ParseTransformation(trans *meta.TransformationMeta) (*meta.TransformationMeta, error) {
	if trans == nil {
		return nil, fmt.Errorf("transformation configuration is nil")
	}

	// 深拷贝转换配置
	data, err := json.Marshal(trans)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal transformation for parsing: %w", err)
	}

	var parsedTrans meta.TransformationMeta
	if err := json.Unmarshal(data, &parsedTrans); err != nil {
		return nil, fmt.Errorf("failed to unmarshal transformation for parsing: %w", err)
	}

	// 解析参数和变量
	if err := p.resolveTransformationVariables(&parsedTrans); err != nil {
		return nil, fmt.Errorf("failed to resolve transformation variables: %w", err)
	}

	return &parsedTrans, nil
}

// resolveJobVariables 解析作业变量
func (p *ConfigParser) resolveJobVariables(job *meta.JobMeta) error {
	// 不需要先添加作业参数和变量，这些值本身可能包含变量引用
	// 我们会在解析时直接对它们进行处理

	// 解析 job meta 信息
	if job.Meta.Name != "" {
		resolved, err := p.resolveString(job.Meta.Name)
		if err != nil {
			return fmt.Errorf("failed to resolve job name: %w", err)
		}
		job.Meta.Name = resolved
	}

	if job.Meta.Description != "" {
		resolved, err := p.resolveString(job.Meta.Description)
		if err != nil {
			return fmt.Errorf("failed to resolve job description: %w", err)
		}
		job.Meta.Description = resolved
	}

	// 解析参数
	for key, value := range job.Parameters {
		resolved, err := p.resolveString(value)
		if err != nil {
			return fmt.Errorf("failed to resolve parameter %s: %w", key, err)
		}
		job.Parameters[key] = resolved
		p.variableResolver.SetParameter(key, resolved)
	}

	// 解析变量
	for key, value := range job.Variables {
		resolved, err := p.resolveString(value)
		if err != nil {
			return fmt.Errorf("failed to resolve variable %s: %w", key, err)
		}
		job.Variables[key] = resolved
		p.variableResolver.SetVariable(key, resolved)
	}

	// 解析条目配置
	for i := range job.Entries {
		if err := p.resolveMapVariables(job.Entries[i].Config); err != nil {
			return fmt.Errorf("failed to resolve entry %s config: %w", job.Entries[i].Name, err)
		}

		// 解析条目描述
		if job.Entries[i].Description != "" {
			resolved, err := p.resolveString(job.Entries[i].Description)
			if err != nil {
				return fmt.Errorf("failed to resolve entry %s description: %w", job.Entries[i].Name, err)
			}
			job.Entries[i].Description = resolved
		}
	}

	// 解析连接条件
	for i := range job.Hops {
		if job.Hops[i].Condition != "" {
			resolved, err := p.resolveString(job.Hops[i].Condition)
			if err != nil {
				return fmt.Errorf("failed to resolve hop condition: %w", err)
			}
			job.Hops[i].Condition = resolved
		}
	}

	return nil
}

// resolveTransformationVariables 解析转换变量
func (p *ConfigParser) resolveTransformationVariables(trans *meta.TransformationMeta) error {
	// 不需要先添加转换参数和变量，这些值本身可能包含变量引用
	// 我们会在解析时直接对它们进行处理

	// 解析参数
	for key, value := range trans.Parameters {
		resolved, err := p.resolveString(value)
		if err != nil {
			return fmt.Errorf("failed to resolve parameter %s: %w", key, err)
		}
		trans.Parameters[key] = resolved
		p.variableResolver.SetParameter(key, resolved)
	}

	// 解析变量
	for key, value := range trans.Variables {
		resolved, err := p.resolveString(value)
		if err != nil {
			return fmt.Errorf("failed to resolve variable %s: %w", key, err)
		}
		trans.Variables[key] = resolved
		p.variableResolver.SetVariable(key, resolved)
	}

	// 解析步骤配置
	for i := range trans.Steps {
		if err := p.resolveMapVariables(trans.Steps[i].Config); err != nil {
			return fmt.Errorf("failed to resolve step %s config: %w", trans.Steps[i].Name, err)
		}

		// 解析步骤描述
		if trans.Steps[i].Description != "" {
			resolved, err := p.resolveString(trans.Steps[i].Description)
			if err != nil {
				return fmt.Errorf("failed to resolve step %s description: %w", trans.Steps[i].Name, err)
			}
			trans.Steps[i].Description = resolved
		}

		// 解析字段格式
		for j := range trans.Steps[i].Fields {
			if trans.Steps[i].Fields[j].Format != "" {
				resolved, err := p.resolveString(trans.Steps[i].Fields[j].Format)
				if err != nil {
					return fmt.Errorf("failed to resolve step %s field %s format: %w",
						trans.Steps[i].Name, trans.Steps[i].Fields[j].Name, err)
				}
				trans.Steps[i].Fields[j].Format = resolved
			}
		}
	}

	// 解析连接条件
	for i := range trans.Hops {
		if trans.Hops[i].Condition != "" {
			resolved, err := p.resolveString(trans.Hops[i].Condition)
			if err != nil {
				return fmt.Errorf("failed to resolve hop condition: %w", err)
			}
			trans.Hops[i].Condition = resolved
		}
	}

	return nil
}

// resolveMapVariables 解析映射中的变量
func (p *ConfigParser) resolveMapVariables(m map[string]interface{}) error {
	for key, value := range m {
		switch v := value.(type) {
		case string:
			resolved, err := p.resolveString(v)
			if err != nil {
				return fmt.Errorf("failed to resolve config %s: %w", key, err)
			}
			m[key] = resolved
		case map[string]interface{}:
			if err := p.resolveMapVariables(v); err != nil {
				return err
			}
		case []interface{}:
			for i, item := range v {
				if strItem, ok := item.(string); ok {
					resolved, err := p.resolveString(strItem)
					if err != nil {
						return fmt.Errorf("failed to resolve array item at index %d: %w", i, err)
					}
					v[i] = resolved
				} else if mapItem, ok := item.(map[string]interface{}); ok {
					if err := p.resolveMapVariables(mapItem); err != nil {
						return err
					}
				}
			}
		}
	}
	return nil
}

// resolveString 解析字符串中的变量和表达式
func (p *ConfigParser) resolveString(value string) (string, error) {
	if p.variableResolver == nil {
		return value, nil
	}

	// 首先检查是否包含变量引用
	if !strings.Contains(value, "${") {
		return value, nil
	}

	// 检查是否包含表达式语法（在变量引用的基础上）
	if p.containsExpressionSyntax(value) {
		// 使用新的表达式引擎
		return p.resolveWithExpressionEngine(value)
	}

	// 查找传统的变量引用 ${variable}
	matches := p.variablePattern.FindAllStringSubmatch(value, -1)
	if len(matches) == 0 {
		return value, nil
	}

	result := value
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		varName := strings.TrimSpace(match[1])
		placeholder := match[0]

		// 解析变量
		resolved, err := p.variableResolver.Resolve(varName)
		if err != nil {
			return "", fmt.Errorf("failed to resolve variable '%s': %w", varName, err)
		}

		result = strings.ReplaceAll(result, placeholder, resolved)
	}

	return result, nil
}

// containsExpressionSyntax 检查字符串是否包含表达式语法
func (p *ConfigParser) containsExpressionSyntax(value string) bool {
	// 只有包含变量引用的字符串才可能是表达式
	if !strings.Contains(value, "${") {
		return false
	}

	// 提取变量引用内容进行检查
	matches := p.variablePattern.FindAllStringSubmatch(value, -1)
	if len(matches) == 0 {
		return false
	}

	for _, match := range matches {
		if len(match) >= 2 {
			varContent := strings.TrimSpace(match[1])

			// 检查变量引用内容是否包含表达式操作符
			expressionOperators := []string{
				"&&", "||", "==", "!=", ">=", "<=", ">", "<",
				"+", "-", "*", "/", "%", "!", "?", ":",
			}

			for _, op := range expressionOperators {
				if strings.Contains(varContent, op) {
					return true
				}
			}

			// 检查是否包含函数调用语法
			if strings.Contains(varContent, "(") && strings.Contains(varContent, ")") {
				return true
			}

			// 检查是否包含数组/对象访问语法
			if strings.Contains(varContent, "[") && strings.Contains(varContent, "]") {
				return true
			}
		}
	}

	return false
}

// resolveWithExpressionEngine 使用表达式引擎解析字符串
func (p *ConfigParser) resolveWithExpressionEngine(value string) (string, error) {
	// 检查是否整个字符串就是一个表达式（没有字面文本）
	if strings.HasPrefix(value, "${") && strings.HasSuffix(value, "}") && strings.Count(value, "${") == 1 {
		// 纯表达式，直接求值
		expression := value[2 : len(value)-1]
		result, err := p.exprAdapter.EvaluateExpression(expression)
		if err != nil {
			return "", fmt.Errorf("failed to evaluate expression '%s': %w", expression, err)
		}
		return p.convertToString(result), nil
	}

	// 模板字符串，需要分别处理文本和表达式
	return p.resolveTemplateString(value)
}

// resolveTemplateString 解析模板字符串
func (p *ConfigParser) resolveTemplateString(template string) (string, error) {
	pattern := regexp.MustCompile(`\$\{([^}]+)\}`)
	result := template

	// 查找所有的表达式
	matches := pattern.FindAllStringSubmatch(template, -1)
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		placeholder := match[0] // ${expression}
		expression := match[1]  // expression

		// 使用表达式引擎求值
		evalResult, err := p.exprAdapter.EvaluateExpression(expression)
		if err != nil {
			return "", fmt.Errorf("failed to evaluate expression '%s': %w", expression, err)
		}

		// 替换占位符
		result = strings.ReplaceAll(result, placeholder, p.convertToString(evalResult))
	}

	return result, nil
}

// convertToString 将任意类型转换为字符串
func (p *ConfigParser) convertToString(value any) string {
	switch v := value.(type) {
	case string:
		return v
	case nil:
		return ""
	case bool:
		if v {
			return "true"
		}
		return "false"
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// ResolveVariablesInString 解析字符串中的变量 (public method for testing)
func (p *ConfigParser) ResolveVariablesInString(value string) (string, error) {
	return p.resolveString(value)
}

// DefaultVariableResolver 默认变量解析器实现
type DefaultVariableResolver struct {
	parameters map[string]string
	variables  map[string]string
	builtins   map[string]func() string
}

// NewDefaultVariableResolver 创建默认变量解析器
func NewDefaultVariableResolver() *DefaultVariableResolver {
	resolver := &DefaultVariableResolver{
		parameters: make(map[string]string),
		variables:  make(map[string]string),
		builtins:   make(map[string]func() string),
	}

	// 注册内置变量
	resolver.registerBuiltins()

	return resolver
}

// registerBuiltins 注册内置变量
func (r *DefaultVariableResolver) registerBuiltins() {
	// 时间相关变量
	r.builtins["NOW"] = func() string {
		return time.Now().Format("2006-01-02 15:04:05")
	}
	r.builtins["TODAY"] = func() string {
		return time.Now().Format("2006-01-02")
	}
	r.builtins["YESTERDAY"] = func() string {
		return time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	}
	r.builtins["TOMORROW"] = func() string {
		return time.Now().AddDate(0, 0, 1).Format("2006-01-02")
	}
	r.builtins["CURRENT_YEAR"] = func() string {
		return time.Now().Format("2006")
	}
	r.builtins["CURRENT_MONTH"] = func() string {
		return time.Now().Format("01")
	}
	r.builtins["CURRENT_DAY"] = func() string {
		return time.Now().Format("02")
	}
	r.builtins["TIMESTAMP"] = func() string {
		return fmt.Sprintf("%d", time.Now().Unix())
	}

	// 系统相关变量
	r.builtins["UUID"] = func() string {
		return generateUUID()
	}
}

// SetVariable 设置变量
func (r *DefaultVariableResolver) SetVariable(name, value string) {
	r.variables[name] = value
}

// GetVariable 获取变量
func (r *DefaultVariableResolver) GetVariable(name string) (string, bool) {
	value, exists := r.variables[name]
	return value, exists
}

// SetParameter 设置参数
func (r *DefaultVariableResolver) SetParameter(name, value string) {
	r.parameters[name] = value
}

// GetParameter 获取参数
func (r *DefaultVariableResolver) GetParameter(name string) (string, bool) {
	value, exists := r.parameters[name]
	return value, exists
}

// GetAllVariables 获取所有变量
func (r *DefaultVariableResolver) GetAllVariables() map[string]string {
	result := make(map[string]string)
	for k, v := range r.variables {
		result[k] = v
	}
	return result
}

// GetAllParameters 获取所有参数
func (r *DefaultVariableResolver) GetAllParameters() map[string]string {
	result := make(map[string]string)
	for k, v := range r.parameters {
		result[k] = v
	}
	return result
}

// Resolve 解析变量
func (r *DefaultVariableResolver) Resolve(name string) (string, error) {
	// 首先检查参数
	if value, exists := r.parameters[name]; exists {
		return value, nil
	}

	// 然后检查变量
	if value, exists := r.variables[name]; exists {
		return value, nil
	}

	// 最后检查内置变量
	if builtin, exists := r.builtins[name]; exists {
		return builtin(), nil
	}

	// 检查环境变量
	if envValue := os.Getenv(name); envValue != "" {
		return envValue, nil
	}

	// 检查是否包含函数调用或复合表达式
	if r.isComplexExpression(name) {
		// 创建临时的表达式适配器来处理复杂表达式
		exprAdapter := NewExprAdapter(r)
		result, err := exprAdapter.EvaluateExpression(name)
		if err != nil {
			return "", fmt.Errorf("failed to evaluate expression '%s': %w", name, err)
		}
		return r.convertToString(result), nil
	}

	return "", fmt.Errorf("variable '%s' not found", name)
}

// isComplexExpression 检查是否是复杂表达式
func (r *DefaultVariableResolver) isComplexExpression(name string) bool {
	// 检查函数调用
	if strings.Contains(name, "(") && strings.Contains(name, ")") {
		return true
	}

	// 检查复合表达式
	if strings.Contains(name, ".") {
		return true
	}

	return false
}

// convertToString 将任意类型转换为字符串
func (r *DefaultVariableResolver) convertToString(value any) string {
	switch v := value.(type) {
	case string:
		return v
	case nil:
		return ""
	case bool:
		if v {
			return "true"
		}
		return "false"
	case int:
		return fmt.Sprintf("%d", v)
	case int64:
		return fmt.Sprintf("%d", v)
	case float64:
		return fmt.Sprintf("%g", v)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// generateUUID 生成简单的UUID
func generateUUID() string {
	// 简单的UUID生成（生产环境应使用更好的实现）
	return fmt.Sprintf("%d-%d", time.Now().UnixNano(), time.Now().Unix())
}
