package config

import (
	"fmt"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	"admin/pkg/expr"
	"admin/pkg/expr/builtin"
)

// ExprAdapter 表达式适配器，统一处理表达式引擎与配置系统的集成
type ExprAdapter struct {
	resolver VariableResolver
	engine   expr.ExprEngine
}

// NewExprAdapter 创建表达式适配器
func NewExprAdapter(resolver VariableResolver) *ExprAdapter {
	adapter := &ExprAdapter{
		resolver: resolver,
		engine:   expr.NewExprEngine(),
	}

	// 注册自定义函数
	adapter.registerBuiltinFunctions()

	return adapter
}

// EvaluateExpression 求值表达式
func (e *ExprAdapter) EvaluateExpression(expression string) (any, error) {
	if expression == "" {
		return "", nil
	}

	// 预处理表达式，替换 ${} 语法
	processedExpr, err := e.preprocessExpression(expression)
	if err != nil {
		return nil, err
	}

	// 准备变量上下文
	variables := e.prepareVariables()

	// 使用表达式引擎计算表达式
	result, err := e.engine.Evaluate(processedExpr, variables)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate expression '%s': %w", expression, err)
	}

	return result, nil
}

// EvaluateToBoolean 求值为布尔值
func (e *ExprAdapter) EvaluateToBoolean(expression string) (bool, error) {
	if expression == "" {
		return true, nil
	}

	// 处理简单布尔值
	if e.isSimpleBoolean(expression) {
		return e.evaluateSimpleBoolean(expression)
	}

	// 预处理表达式，替换 ${} 语法
	processedExpr, err := e.preprocessExpression(expression)
	if err != nil {
		return false, err
	}

	// 准备变量上下文
	variables := e.prepareVariables()

	// 使用表达式引擎计算条件
	result, err := e.engine.EvaluateToBoolean(processedExpr, variables)
	if err != nil {
		return false, fmt.Errorf("failed to evaluate condition '%s': %w", expression, err)
	}

	return result, nil
}

// ValidateExpression 验证表达式语法
func (e *ExprAdapter) ValidateExpression(expression string) error {
	if expression == "" {
		return nil
	}

	return e.engine.Validate(expression)
}

// evaluateSimpleBoolean 求值简单布尔值（向后兼容）
func (e *ExprAdapter) evaluateSimpleBoolean(value string) (bool, error) {
	value = strings.TrimSpace(value)

	if value == "true" {
		return true, nil
	}
	if value == "false" {
		return false, nil
	}

	// 保持向后兼容：非明确布尔值默认返回true
	return true, nil
}

// isSimpleBoolean 检查是否是简单布尔值
func (e *ExprAdapter) isSimpleBoolean(value string) bool {
	value = strings.TrimSpace(value)
	return value == "true" || value == "false"
}

// prepareVariables 准备变量上下文
func (e *ExprAdapter) prepareVariables() map[string]any {
	variables := make(map[string]any)

	// 添加内置变量
	e.addBuiltinVariables(variables)

	// 如果有变量解析器，添加解析器中的变量
	if e.resolver != nil {
		e.addResolverVariables(variables)
	}

	return variables
}

// registerBuiltinFunctions 注册内置函数
func (e *ExprAdapter) registerBuiltinFunctions() {
	// 日期函数
	e.registerDateFunction()
	e.registerFormatFunction()
}

// registerDateFunction 注册日期函数
func (e *ExprAdapter) registerDateFunction() {
	dateFunc := builtin.NewFunction(
		"DATE",
		func(args []any) (any, error) {
			if len(args) == 0 {
				return time.Now().Format("2006-01-02"), nil
			}

			if len(args) == 1 {
				switch arg := args[0].(type) {
				case string:
					// 支持相对日期
					if strings.HasPrefix(arg, "+") || strings.HasPrefix(arg, "-") {
						days := 0
						if _, err := fmt.Sscanf(arg, "%d", &days); err == nil {
							return time.Now().AddDate(0, 0, days).Format("2006-01-02"), nil
						}
					}
					return arg, nil
				case float64:
					days := int(arg)
					return time.Now().AddDate(0, 0, days).Format("2006-01-02"), nil
				}
			}

			return time.Now().Format("2006-01-02"), nil
		},
		0, 1,
	)
	e.engine.RegisterFunction(dateFunc)
}

// registerFormatFunction 注册格式化函数
func (e *ExprAdapter) registerFormatFunction() {
	formatFunc := builtin.NewFunction(
		"FORMAT",
		func(args []any) (any, error) {
			if len(args) < 2 {
				return nil, fmt.Errorf("FORMAT function requires at least 2 arguments")
			}

			template, ok := args[0].(string)
			if !ok {
				return nil, fmt.Errorf("FORMAT first argument must be a string")
			}

			// 转换参数，特别处理数值类型
			formatArgs := make([]any, len(args)-1)
			for i := 1; i < len(args); i++ {
				arg := args[i]
				// 将所有数值类型转换为最适合格式化的类型
				switch v := arg.(type) {
				case float64:
					if v == float64(int64(v)) {
						// 是整数，转换为字符串形式的整数
						formatArgs[i-1] = fmt.Sprintf("%.0f", v)
					} else {
						// 是浮点数，保持为float64
						formatArgs[i-1] = v
					}
				case int64:
					// 转换为字符串
					formatArgs[i-1] = fmt.Sprintf("%d", v)
				case int:
					// 转换为字符串
					formatArgs[i-1] = fmt.Sprintf("%d", v)
				default:
					formatArgs[i-1] = arg
				}
			}

			return fmt.Sprintf(template, formatArgs...), nil
		},
		2, -1,
	)
	e.engine.RegisterFunction(formatFunc)
}

// addBuiltinVariables 添加内置变量
func (e *ExprAdapter) addBuiltinVariables(variables map[string]any) {
	now := time.Now()

	// 时间相关变量
	variables["NOW"] = now.Format("2006-01-02 15:04:05")
	variables["TODAY"] = now.Format("2006-01-02")
	variables["YESTERDAY"] = now.AddDate(0, 0, -1).Format("2006-01-02")
	variables["TOMORROW"] = now.AddDate(0, 0, 1).Format("2006-01-02")
	variables["CURRENT_YEAR"] = now.Format("2006")
	variables["CURRENT_MONTH"] = now.Format("01")
	variables["CURRENT_DAY"] = now.Format("02")
	variables["TIMESTAMP"] = now.Unix()

	// 系统相关变量
	variables["UUID"] = generateUUID()

	// 环境变量
	for _, env := range os.Environ() {
		pair := strings.SplitN(env, "=", 2)
		if len(pair) == 2 {
			variables["ENV_"+pair[0]] = pair[1]
		}
	}
}

// addResolverVariables 从变量解析器添加变量
func (e *ExprAdapter) addResolverVariables(variables map[string]any) {
	// 添加所有参数
	for name, value := range e.resolver.GetAllParameters() {
		variables[name] = e.convertStringToAppropriateType(value)
	}

	// 添加所有变量
	for name, value := range e.resolver.GetAllVariables() {
		variables[name] = e.convertStringToAppropriateType(value)
	}

	// 添加作业、步骤相关的变量映射
	e.addJobStepVariables(variables)
}

// addJobStepVariables 添加作业、步骤相关的变量映射
func (e *ExprAdapter) addJobStepVariables(variables map[string]any) {
	jobVars := make(map[string]any)
	stepVars := make(map[string]any)

	// 从所有变量中提取job和step前缀的变量
	for name, value := range e.resolver.GetAllVariables() {
		if strings.HasPrefix(name, "job.") {
			parts := strings.SplitN(name, ".", 2)
			if len(parts) == 2 {
				jobVars[parts[1]] = e.convertStringToAppropriateType(value)
			}
		} else if strings.HasPrefix(name, "step.") {
			parts := strings.SplitN(name, ".", 3)
			if len(parts) == 3 {
				stepName := parts[1]
				propName := parts[2]

				if stepMap, ok := stepVars[stepName].(map[string]any); ok {
					stepMap[propName] = e.convertStringToAppropriateType(value)
				} else {
					stepMap = make(map[string]any)
					stepMap[propName] = e.convertStringToAppropriateType(value)
					stepVars[stepName] = stepMap
				}
			}
		}
	}

	// 添加到变量集合中
	if len(jobVars) > 0 {
		variables["job"] = jobVars
	}
	if len(stepVars) > 0 {
		variables["step"] = stepVars
	}
}

// convertStringToAppropriateType 将字符串转换为适当的类型
func (e *ExprAdapter) convertStringToAppropriateType(value string) any {
	// 尝试转换为数字
	if floatVal, err := strconv.ParseFloat(value, 64); err == nil {
		// 将所有数字都转换为 float64，避免类型不匹配问题
		return floatVal
	}

	// 尝试转换为布尔值
	if boolVal, err := strconv.ParseBool(value); err == nil {
		return boolVal
	}

	// 检查是否是空值
	if value == "null" || value == "nil" || value == "" {
		return nil
	}

	// 默认保持为字符串
	return value
}

// preprocessExpression 预处理表达式，将 ${variable} 语法转换为直接的变量引用
func (e *ExprAdapter) preprocessExpression(expression string) (string, error) {
	// 如果不包含 ${}，直接返回
	if !strings.Contains(expression, "${") {
		return expression, nil
	}

	// 使用正则表达式匹配 ${variable} 模式
	re := regexp.MustCompile(`\$\{([^}]+)\}`)

	// 替换所有匹配的变量引用
	result := re.ReplaceAllString(expression, "$1")

	return result, nil
}
