package config

import (
	"fmt"
	"time"
)

// Example 展示重构后的配置模块使用方法
func Example() {
	// 创建一个增强的变量解析器
	resolver := NewEnhancedVariableResolver()

	// 设置一些变量和参数
	resolver.SetParameter("user", "张三")
	resolver.SetParameter("age", "30")
	resolver.SetParameter("score", "85.5")
	resolver.SetVariable("baseDir", "/home/<USER>")
	resolver.SetVariable("timestamp", fmt.Sprintf("%d", time.Now().Unix()))

	// 创建配置解析器
	parser := NewConfigParser(resolver)

	// 示例1：简单变量替换
	result1, _ := parser.ResolveVariablesInString("你好，${user}！")
	fmt.Println(result1)

	// 示例2：算术表达式
	result2, _ := parser.ResolveVariablesInString("明年你将会${age + 1}岁")
	fmt.Println(result2)

	// 示例3：条件表达式
	result3, _ := parser.ResolveVariablesInString("成绩评级：${score >= 80 ? '优秀' : score >= 60 ? '良好' : '需要努力'}")
	fmt.Println(result3)

	// 示例4：字符串连接和函数调用
	result4, _ := parser.ResolveVariablesInString("输出目录：${baseDir + '/' + FORMAT('%s_logs', user) + '_' + TODAY}")
	fmt.Println(result4)

	// 示例5：混合表达式
	result5, _ := parser.ResolveVariablesInString("用户${user}（${age}岁）的得分率为${score / 100 * 100}%")
	fmt.Println(result5)

	// Output:
	// 你好，张三！
	// 明年你将会31岁
	// 成绩评级：优秀
	// 输出目录：/home/<USER>/张三_logs_2025-06-03
	// 用户张三（30岁）的得分率为85.5%
}
