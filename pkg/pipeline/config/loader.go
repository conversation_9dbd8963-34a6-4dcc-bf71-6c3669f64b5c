package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"admin/pkg/pipeline/config/formats"
	"admin/pkg/pipeline/meta"
)

// Loader 配置加载器接口
type Loader interface {
	LoadJob(filename string) (*meta.JobMeta, error)
	LoadTransformation(filename string) (*meta.TransformationMeta, error)
	LoadConnections(filename string) (*meta.ConnectionsConfig, error)
	SaveJob(job *meta.JobMeta, filename string) error
	SaveTransformation(trans *meta.TransformationMeta, filename string) error
	SaveConnections(connections *meta.ConnectionsConfig, filename string) error
}

// FileLoader 文件配置加载器
type FileLoader struct {
	basePath      string
	parserFactory *formats.ParserFactory
}

// NewFileLoader 创建文件配置加载器
func NewFileLoader(basePath string) *FileLoader {
	return &FileLoader{
		basePath:      basePath,
		parserFactory: formats.NewParserFactory(),
	}
}

// LoadJob 加载作业配置
func (f *FileLoader) LoadJob(filename string) (*meta.JobMeta, error) {
	fullPath := filepath.Join(f.basePath, filename)
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read job file %s: %w", fullPath, err)
	}

	var job meta.JobMeta
	if err := f.unmarshal(data, fullPath, &job); err != nil {
		return nil, fmt.Errorf("failed to parse job file %s: %w", fullPath, err)
	}

	if err := job.Validate(); err != nil {
		return nil, fmt.Errorf("invalid job configuration in %s: %w", fullPath, err)
	}

	return &job, nil
}

// LoadTransformation 加载转换配置
func (f *FileLoader) LoadTransformation(filename string) (*meta.TransformationMeta, error) {
	fullPath := filepath.Join(f.basePath, filename)
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read transformation file %s: %w", fullPath, err)
	}

	var trans meta.TransformationMeta
	if err := f.unmarshal(data, fullPath, &trans); err != nil {
		return nil, fmt.Errorf("failed to parse transformation file %s: %w", fullPath, err)
	}

	if err := trans.Validate(); err != nil {
		return nil, fmt.Errorf("invalid transformation configuration in %s: %w", fullPath, err)
	}

	return &trans, nil
}

// LoadConnections 加载连接配置
func (f *FileLoader) LoadConnections(filename string) (*meta.ConnectionsConfig, error) {
	fullPath := filepath.Join(f.basePath, filename)
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read connections file %s: %w", fullPath, err)
	}

	var connections meta.ConnectionsConfig
	if err := f.unmarshal(data, fullPath, &connections); err != nil {
		return nil, fmt.Errorf("failed to parse connections file %s: %w", fullPath, err)
	}

	if err := connections.Validate(); err != nil {
		return nil, fmt.Errorf("invalid connections configuration in %s: %w", fullPath, err)
	}

	return &connections, nil
}

// SaveJob 保存作业配置
func (f *FileLoader) SaveJob(job *meta.JobMeta, filename string) error {
	if err := job.Validate(); err != nil {
		return fmt.Errorf("invalid job configuration: %w", err)
	}

	fullPath := filepath.Join(f.basePath, filename)
	data, err := f.marshal(job, fullPath)
	if err != nil {
		return fmt.Errorf("failed to marshal job: %w", err)
	}

	if err := os.WriteFile(fullPath, data, 0o644); err != nil {
		return fmt.Errorf("failed to write job file %s: %w", fullPath, err)
	}

	return nil
}

// SaveTransformation 保存转换配置
func (f *FileLoader) SaveTransformation(trans *meta.TransformationMeta, filename string) error {
	if err := trans.Validate(); err != nil {
		return fmt.Errorf("invalid transformation configuration: %w", err)
	}

	fullPath := filepath.Join(f.basePath, filename)
	data, err := f.marshal(trans, fullPath)
	if err != nil {
		return fmt.Errorf("failed to marshal transformation: %w", err)
	}

	if err := os.WriteFile(fullPath, data, 0o644); err != nil {
		return fmt.Errorf("failed to write transformation file %s: %w", fullPath, err)
	}

	return nil
}

// SaveConnections 保存连接配置
func (f *FileLoader) SaveConnections(connections *meta.ConnectionsConfig, filename string) error {
	if err := connections.Validate(); err != nil {
		return fmt.Errorf("invalid connections configuration: %w", err)
	}

	fullPath := filepath.Join(f.basePath, filename)
	data, err := f.marshal(connections, fullPath)
	if err != nil {
		return fmt.Errorf("failed to marshal connections: %w", err)
	}

	if err := os.WriteFile(fullPath, data, 0o644); err != nil {
		return fmt.Errorf("failed to write connections file %s: %w", fullPath, err)
	}

	return nil
}

// unmarshal 根据文件扩展名解析数据
func (f *FileLoader) unmarshal(data []byte, filename string, v interface{}) error {
	ext := strings.ToLower(filepath.Ext(filename))
	parser := f.parserFactory.GetParser(ext)

	// 根据数据类型选择对应的解析方法
	switch target := v.(type) {
	case *meta.JobMeta:
		job, err := parser.ParseJob(data)
		if err != nil {
			return err
		}
		*target = *job
	case *meta.TransformationMeta:
		trans, err := parser.ParseTransformation(data)
		if err != nil {
			return err
		}
		*target = *trans
	case *meta.ConnectionsConfig:
		connections, err := parser.ParseConnections(data)
		if err != nil {
			return err
		}
		*target = *connections
	default:
		return fmt.Errorf("unsupported data type for unmarshal")
	}

	return nil
}

// marshal 根据文件扩展名序列化数据
func (f *FileLoader) marshal(v interface{}, filename string) ([]byte, error) {
	ext := strings.ToLower(filepath.Ext(filename))
	parser := f.parserFactory.GetParser(ext)

	// 根据数据类型选择对应的序列化方法
	switch data := v.(type) {
	case *meta.JobMeta:
		return parser.MarshalJob(data)
	case *meta.TransformationMeta:
		return parser.MarshalTransformation(data)
	case *meta.ConnectionsConfig:
		return parser.MarshalConnections(data)
	default:
		return nil, fmt.Errorf("unsupported data type for marshal")
	}
}
