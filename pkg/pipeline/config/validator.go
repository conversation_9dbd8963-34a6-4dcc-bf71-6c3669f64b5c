package config

import (
	"fmt"
	"regexp"
	"strings"

	"admin/pkg/pipeline/meta"
)

// Validator 配置验证器接口
type Validator interface {
	ValidateJob(job *meta.JobMeta) error
	ValidateTransformation(trans *meta.TransformationMeta) error
	ValidateConnections(connections *meta.ConnectionsConfig) error
}

// ConfigValidator 默认配置验证器实现
type ConfigValidator struct {
	namePattern *regexp.Regexp
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() *ConfigValidator {
	return &ConfigValidator{
		namePattern: regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_-]*$`),
	}
}

// ValidateJob 验证作业配置
func (v *ConfigValidator) ValidateJob(job *meta.JobMeta) error {
	if job == nil {
		return fmt.Errorf("job configuration is nil")
	}

	// 验证基本信息
	if err := v.validateJobMeta(&job.Meta); err != nil {
		return fmt.Errorf("invalid job meta: %w", err)
	}

	// 验证参数
	if err := v.validateParameters(job.Parameters); err != nil {
		return fmt.Errorf("invalid job parameters: %w", err)
	}

	// 验证变量
	if err := v.validateVariables(job.Variables); err != nil {
		return fmt.Errorf("invalid job variables: %w", err)
	}

	// 验证作业条目
	if err := v.validateJobEntries(job.Entries); err != nil {
		return fmt.Errorf("invalid job entries: %w", err)
	}

	// 验证作业连接
	if err := v.validateJobHops(job.Hops, job.Entries); err != nil {
		return fmt.Errorf("invalid job hops: %w", err)
	}

	return nil
}

// ValidateTransformation 验证转换配置
func (v *ConfigValidator) ValidateTransformation(trans *meta.TransformationMeta) error {
	if trans == nil {
		return fmt.Errorf("transformation configuration is nil")
	}

	// 验证基本信息
	if err := v.validateTransMeta(&trans.Meta); err != nil {
		return fmt.Errorf("invalid transformation meta: %w", err)
	}

	// 验证参数
	if err := v.validateParameters(trans.Parameters); err != nil {
		return fmt.Errorf("invalid transformation parameters: %w", err)
	}

	// 验证变量
	if err := v.validateVariables(trans.Variables); err != nil {
		return fmt.Errorf("invalid transformation variables: %w", err)
	}

	// 验证步骤
	if err := v.validateSteps(trans.Steps); err != nil {
		return fmt.Errorf("invalid transformation steps: %w", err)
	}

	// 验证连接
	if err := v.validateHops(trans.Hops, trans.Steps); err != nil {
		return fmt.Errorf("invalid transformation hops: %w", err)
	}

	return nil
}

// ValidateConnections 验证连接配置
func (v *ConfigValidator) ValidateConnections(connections *meta.ConnectionsConfig) error {
	if connections == nil {
		return fmt.Errorf("connections configuration is nil")
	}

	// 验证连接
	if err := v.validateConnectionMetas(connections.Connections); err != nil {
		return fmt.Errorf("invalid connections: %w", err)
	}

	return nil
}

// validateJobMeta 验证作业基本信息
func (v *ConfigValidator) validateJobMeta(meta *meta.JobMetaInfo) error {
	if meta.Name == "" {
		return fmt.Errorf("job name is required")
	}

	if !v.namePattern.MatchString(meta.Name) {
		return fmt.Errorf("job name '%s' contains invalid characters", meta.Name)
	}

	return nil
}

// validateTransMeta 验证转换基本信息
func (v *ConfigValidator) validateTransMeta(meta *meta.TransMetaInfo) error {
	if meta.Name == "" {
		return fmt.Errorf("transformation name is required")
	}

	if !v.namePattern.MatchString(meta.Name) {
		return fmt.Errorf("transformation name '%s' contains invalid characters", meta.Name)
	}

	return nil
}

// validateParameters 验证参数
func (v *ConfigValidator) validateParameters(params map[string]string) error {
	for name, value := range params {
		if name == "" {
			return fmt.Errorf("parameter name cannot be empty")
		}

		if !v.namePattern.MatchString(name) {
			return fmt.Errorf("parameter name '%s' contains invalid characters", name)
		}

		if value == "" {
			return fmt.Errorf("parameter '%s' value cannot be empty", name)
		}
	}

	return nil
}

// validateVariables 验证变量
func (v *ConfigValidator) validateVariables(vars map[string]string) error {
	for name, value := range vars {
		if name == "" {
			return fmt.Errorf("variable name cannot be empty")
		}

		if !v.namePattern.MatchString(name) {
			return fmt.Errorf("variable name '%s' contains invalid characters", name)
		}

		if value == "" {
			return fmt.Errorf("variable '%s' value cannot be empty", name)
		}
	}

	return nil
}

// validateJobEntries 验证作业条目
func (v *ConfigValidator) validateJobEntries(entries []meta.JobEntryMeta) error {
	if len(entries) == 0 {
		return fmt.Errorf("job must have at least one entry")
	}

	entryNames := make(map[string]bool)
	hasStart := false

	for i, entry := range entries {
		// 验证条目名称
		if entry.Name == "" {
			return fmt.Errorf("entry at index %d: name is required", i)
		}

		if !v.namePattern.MatchString(entry.Name) {
			return fmt.Errorf("entry at index %d: name '%s' contains invalid characters", i, entry.Name)
		}

		// 检查名称唯一性
		if entryNames[entry.Name] {
			return fmt.Errorf("entry at index %d: duplicate entry name '%s'", i, entry.Name)
		}
		entryNames[entry.Name] = true

		// 验证条目类型
		if err := v.validateJobEntryType(entry.Type); err != nil {
			return fmt.Errorf("entry at index %d: %w", i, err)
		}

		// 检查是否有START条目
		if entry.Type == meta.JobEntryTypeStart {
			hasStart = true
		}

		// 验证位置信息
		if err := v.validatePosition(entry.Position); err != nil {
			return fmt.Errorf("entry at index %d: %w", i, err)
		}
	}

	if !hasStart {
		return fmt.Errorf("job must have a START entry")
	}

	return nil
}

// validateJobHops 验证作业连接
func (v *ConfigValidator) validateJobHops(hops []meta.JobHopMeta, entries []meta.JobEntryMeta) error {
	entryNames := make(map[string]bool)
	for _, entry := range entries {
		entryNames[entry.Name] = true
	}

	for i, hop := range hops {
		// 验证源条目
		if hop.From == "" {
			return fmt.Errorf("hop at index %d: 'from' is required", i)
		}

		if !entryNames[hop.From] {
			return fmt.Errorf("hop at index %d: source entry '%s' does not exist", i, hop.From)
		}

		// 验证目标条目
		if hop.To == "" {
			return fmt.Errorf("hop at index %d: 'to' is required", i)
		}

		if !entryNames[hop.To] {
			return fmt.Errorf("hop at index %d: target entry '%s' does not exist", i, hop.To)
		}

		// 验证条件
		if hop.Condition != "" && strings.TrimSpace(hop.Condition) == "" {
			return fmt.Errorf("hop at index %d: condition cannot be empty when specified", i)
		}
	}

	return nil
}

// validateSteps 验证转换步骤
func (v *ConfigValidator) validateSteps(steps []meta.StepMeta) error {
	if len(steps) == 0 {
		return fmt.Errorf("transformation must have at least one step")
	}

	stepNames := make(map[string]bool)

	for i, step := range steps {
		// 验证步骤名称
		if step.Name == "" {
			return fmt.Errorf("step at index %d: name is required", i)
		}

		if !v.namePattern.MatchString(step.Name) {
			return fmt.Errorf("step at index %d: name '%s' contains invalid characters", i, step.Name)
		}

		// 检查名称唯一性
		if stepNames[step.Name] {
			return fmt.Errorf("step at index %d: duplicate step name '%s'", i, step.Name)
		}
		stepNames[step.Name] = true

		// 验证步骤类型
		if err := v.validateStepType(step.Type); err != nil {
			return fmt.Errorf("step at index %d: %w", i, err)
		}

		// 验证字段
		if err := v.validateFields(step.Fields); err != nil {
			return fmt.Errorf("step at index %d: %w", i, err)
		}

		// 验证位置信息
		if err := v.validatePosition(step.Position); err != nil {
			return fmt.Errorf("step at index %d: %w", i, err)
		}
	}

	return nil
}

// validateHops 验证转换连接
func (v *ConfigValidator) validateHops(hops []meta.HopMeta, steps []meta.StepMeta) error {
	stepNames := make(map[string]bool)
	for _, step := range steps {
		stepNames[step.Name] = true
	}

	for i, hop := range hops {
		// 验证源步骤
		if hop.From == "" {
			return fmt.Errorf("hop at index %d: 'from' is required", i)
		}

		if !stepNames[hop.From] {
			return fmt.Errorf("hop at index %d: source step '%s' does not exist", i, hop.From)
		}

		// 验证目标步骤
		if hop.To == "" {
			return fmt.Errorf("hop at index %d: 'to' is required", i)
		}

		if !stepNames[hop.To] {
			return fmt.Errorf("hop at index %d: target step '%s' does not exist", i, hop.To)
		}

		// 验证条件
		if hop.Condition != "" && strings.TrimSpace(hop.Condition) == "" {
			return fmt.Errorf("hop at index %d: condition cannot be empty when specified", i)
		}
	}

	return nil
}

// validateConnectionMetas 验证连接元数据
func (v *ConfigValidator) validateConnectionMetas(connections []meta.ConnectionMeta) error {
	if len(connections) == 0 {
		return fmt.Errorf("at least one connection is required")
	}

	connectionNames := make(map[string]bool)

	for i, conn := range connections {
		// 验证连接名称
		if conn.Name == "" {
			return fmt.Errorf("connection at index %d: name is required", i)
		}

		if !v.namePattern.MatchString(conn.Name) {
			return fmt.Errorf("connection at index %d: name '%s' contains invalid characters", i, conn.Name)
		}

		// 检查名称唯一性
		if connectionNames[conn.Name] {
			return fmt.Errorf("connection at index %d: duplicate connection name '%s'", i, conn.Name)
		}
		connectionNames[conn.Name] = true

		// 验证连接类型
		if err := v.validateConnectionType(conn.Type); err != nil {
			return fmt.Errorf("connection at index %d: %w", i, err)
		}

		// 验证配置
		if err := v.validateConnectionConfig(conn.Type, conn.Config); err != nil {
			return fmt.Errorf("connection at index %d: %w", i, err)
		}
	}

	return nil
}

// validateJobEntryType 验证作业条目类型
func (v *ConfigValidator) validateJobEntryType(entryType meta.JobEntryType) error {
	switch entryType {
	case meta.JobEntryTypeStart, meta.JobEntryTypeSuccess, meta.JobEntryTypeError,
		meta.JobEntryTypeTrans, meta.JobEntryTypeJob, meta.JobEntryTypeSQL,
		meta.JobEntryTypeScript, meta.JobEntryTypeFile, meta.JobEntryTypeFTP,
		meta.JobEntryTypeHTTP, meta.JobEntryTypeEmail, meta.JobEntryTypeAbort,
		meta.JobEntryTypeDummy:
		return nil
	default:
		return fmt.Errorf("invalid job entry type: %s", entryType)
	}
}

// validateStepType 验证步骤类型
func (v *ConfigValidator) validateStepType(stepType meta.StepType) error {
	switch stepType {
	case meta.StepTypeTableInput, meta.StepTypeTableOutput, meta.StepTypeTextFileInput,
		meta.StepTypeTextFileOutput, meta.StepTypeCSVFileInput, meta.StepTypeCSVFileOutput,
		meta.StepTypeExcelInput, meta.StepTypeExcelOutput, meta.StepTypeJSONInput,
		meta.StepTypeJSONOutput, meta.StepTypeXMLInput, meta.StepTypeXMLOutput,
		meta.StepTypeHTTPClient, meta.StepTypeHTTPPost, meta.StepTypeFTPGet,
		meta.StepTypeFTPPut, meta.StepTypeSelectValues, meta.StepTypeFilterRows,
		meta.StepTypeSortRows, meta.StepTypeGroupBy, meta.StepTypeJoinRows,
		meta.StepTypeValueMapper, meta.StepTypeCalculator, meta.StepTypeStringOperations,
		meta.StepTypeScript, meta.StepTypeValidator, meta.StepTypeDummy,
		meta.StepTypeDummyInput, meta.StepTypeDummyOutput:
		return nil
	default:
		return fmt.Errorf("invalid step type: %s", stepType)
	}
}

// validateConnectionType 验证连接类型
func (v *ConfigValidator) validateConnectionType(connType meta.ConnectionType) error {
	switch connType {
	case meta.ConnectionTypeMySQL, meta.ConnectionTypePostgreSQL, meta.ConnectionTypeOracle,
		meta.ConnectionTypeSQLServer, meta.ConnectionTypeMongoDB, meta.ConnectionTypeRedis,
		meta.ConnectionTypeElasticsearch, meta.ConnectionTypeKafka, meta.ConnectionTypeRabbitMQ,
		meta.ConnectionTypeFTP, meta.ConnectionTypeSFTP, meta.ConnectionTypeHTTP,
		meta.ConnectionTypeHTTPS, meta.ConnectionTypeFile:
		return nil
	default:
		return fmt.Errorf("invalid connection type: %s", connType)
	}
}

// validateFields 验证字段
func (v *ConfigValidator) validateFields(fields []meta.FieldMeta) error {
	fieldNames := make(map[string]bool)

	for i, field := range fields {
		// 验证字段名称
		if field.Name == "" {
			return fmt.Errorf("field at index %d: name is required", i)
		}

		if !v.namePattern.MatchString(field.Name) {
			return fmt.Errorf("field at index %d: name '%s' contains invalid characters", i, field.Name)
		}

		// 检查名称唯一性
		if fieldNames[field.Name] {
			return fmt.Errorf("field at index %d: duplicate field name '%s'", i, field.Name)
		}
		fieldNames[field.Name] = true

		// 验证字段类型
		if err := v.validateFieldType(field.Type); err != nil {
			return fmt.Errorf("field at index %d: %w", i, err)
		}

		// 验证长度
		if field.Length < 0 {
			return fmt.Errorf("field at index %d: length cannot be negative", i)
		}
	}

	return nil
}

// validateFieldType 验证字段类型
func (v *ConfigValidator) validateFieldType(fieldType meta.FieldType) error {
	switch fieldType {
	case meta.FieldTypeString, meta.FieldTypeInteger, meta.FieldTypeBigInt,
		meta.FieldTypeNumber, meta.FieldTypeDate, meta.FieldTypeBoolean,
		meta.FieldTypeBinary, meta.FieldTypeTimestamp, meta.FieldTypeInternetAddress,
		meta.FieldTypeNone:
		return nil
	default:
		return fmt.Errorf("invalid field type: %s", fieldType)
	}
}

// validatePosition 验证位置信息
func (v *ConfigValidator) validatePosition(position meta.Position) error {
	if position.X < 0 || position.Y < 0 {
		return fmt.Errorf("position coordinates cannot be negative")
	}

	return nil
}

// validateConnectionConfig 验证连接配置
func (v *ConfigValidator) validateConnectionConfig(connType meta.ConnectionType, config map[string]interface{}) error {
	switch connType {
	case meta.ConnectionTypeMySQL, meta.ConnectionTypePostgreSQL,
		meta.ConnectionTypeOracle, meta.ConnectionTypeSQLServer:
		return v.validateDatabaseConfig(config)
	case meta.ConnectionTypeFTP, meta.ConnectionTypeSFTP:
		return v.validateFTPConfig(config)
	case meta.ConnectionTypeHTTP, meta.ConnectionTypeHTTPS:
		return v.validateHTTPConfig(config)
	default:
		// 对于其他类型，进行基本验证
		return v.validateBasicConfig(config)
	}
}

// validateDatabaseConfig 验证数据库连接配置
func (v *ConfigValidator) validateDatabaseConfig(config map[string]interface{}) error {
	requiredFields := []string{"host", "port", "database", "username"}

	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("database connection missing required field: %s", field)
		}
	}

	return nil
}

// validateFTPConfig 验证FTP连接配置
func (v *ConfigValidator) validateFTPConfig(config map[string]interface{}) error {
	requiredFields := []string{"host", "port", "username"}

	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("FTP connection missing required field: %s", field)
		}
	}

	return nil
}

// validateHTTPConfig 验证HTTP连接配置
func (v *ConfigValidator) validateHTTPConfig(config map[string]interface{}) error {
	if _, exists := config["url"]; !exists {
		return fmt.Errorf("HTTP connection missing required field: url")
	}

	return nil
}

// validateBasicConfig 验证基本配置
func (v *ConfigValidator) validateBasicConfig(config map[string]interface{}) error {
	// 基本验证：确保配置不为空
	if len(config) == 0 {
		return fmt.Errorf("connection configuration cannot be empty")
	}

	return nil
}
