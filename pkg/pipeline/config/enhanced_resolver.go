package config

import (
	"fmt"
	"os"
	"strings"
)

// EnhancedVariableResolver 增强的变量解析器，使用表达式引擎处理复杂表达式
type EnhancedVariableResolver struct {
	*DefaultVariableResolver
	exprAdapter *ExprAdapter
}

// NewEnhancedVariableResolver 创建增强的变量解析器
func NewEnhancedVariableResolver() *EnhancedVariableResolver {
	resolver := &EnhancedVariableResolver{
		DefaultVariableResolver: NewDefaultVariableResolver(),
	}
	resolver.exprAdapter = NewExprAdapter(resolver)
	return resolver
}

// Resolve 解析变量，优先使用表达式引擎
func (r *EnhancedVariableResolver) Resolve(name string) (string, error) {
	// 首先检查简单变量
	if value, exists := r.parameters[name]; exists {
		return value, nil
	}

	if value, exists := r.variables[name]; exists {
		return value, nil
	}

	if builtin, exists := r.builtins[name]; exists {
		return builtin(), nil
	}

	if envValue := os.Getenv(name); envValue != "" {
		return envValue, nil
	}

	// 处理复杂表达式
	if r.isCompoundExpression(name) || r.isFunctionCall(name) {
		return r.resolveWithExprEngine(name)
	}

	return "", fmt.Errorf("variable '%s' not found", name)
}

// resolveWithExprEngine 使用表达式引擎解析表达式
func (r *EnhancedVariableResolver) resolveWithExprEngine(expression string) (string, error) {
	result, err := r.exprAdapter.EvaluateExpression(expression)
	if err != nil {
		return "", err
	}

	// 将结果转换为字符串
	switch v := result.(type) {
	case string:
		return v, nil
	case nil:
		return "", nil
	case bool:
		if v {
			return "true", nil
		}
		return "false", nil
	case int:
		return fmt.Sprintf("%d", v), nil
	case int64:
		return fmt.Sprintf("%d", v), nil
	case float64:
		if v == float64(int(v)) {
			return fmt.Sprintf("%d", int(v)), nil
		}
		return fmt.Sprintf("%g", v), nil
	default:
		return fmt.Sprintf("%v", v), nil
	}
}

// isCompoundExpression 检查是否是复合表达式
func (r *EnhancedVariableResolver) isCompoundExpression(expr string) bool {
	return strings.Contains(expr, ".") ||
		strings.Contains(expr, "[") ||
		strings.Contains(expr, "]") ||
		strings.Contains(expr, "+") ||
		strings.Contains(expr, "-") ||
		strings.Contains(expr, "*") ||
		strings.Contains(expr, "/") ||
		strings.Contains(expr, "%") ||
		strings.Contains(expr, "==") ||
		strings.Contains(expr, "!=") ||
		strings.Contains(expr, ">") ||
		strings.Contains(expr, "<") ||
		strings.Contains(expr, ">=") ||
		strings.Contains(expr, "<=") ||
		strings.Contains(expr, "&&") ||
		strings.Contains(expr, "||") ||
		strings.Contains(expr, "?") ||
		strings.Contains(expr, ":")
}

// isFunctionCall 检查是否是函数调用
func (r *EnhancedVariableResolver) isFunctionCall(expr string) bool {
	return strings.Contains(expr, "(") && strings.Contains(expr, ")")
}
