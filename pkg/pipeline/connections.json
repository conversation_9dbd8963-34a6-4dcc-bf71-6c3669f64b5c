{"connections": [{"name": "user_source", "type": "DATABASE", "description": "用户源数据库 - MySQL主库", "config": {"database_type": "mysql", "hostname": "${DB_HOST:localhost}", "port": "${DB_PORT:3306}", "database": "user_system", "username": "${DB_USER:etl_user}", "password": "${DB_PASSWORD}", "connection_pool": {"max_connections": 10, "min_connections": 2, "connection_timeout": "30s", "idle_timeout": "300s", "max_lifetime": "3600s"}, "properties": {"useSSL": "true", "serverTimezone": "Asia/Shanghai", "charset": "utf8mb4", "allowPublicKeyRetrieval": "true", "useUnicode": "true", "characterEncoding": "utf8", "autoReconnect": "true", "failOverReadOnly": "false", "maxReconnects": "3"}, "read_only": true, "validation_query": "SELECT 1", "test_on_borrow": true, "test_while_idle": true}}, {"name": "user_warehouse", "type": "DATABASE", "description": "用户数据仓库 - PostgreSQL", "config": {"database_type": "postgresql", "hostname": "${DW_HOST:localhost}", "port": "${DW_PORT:5432}", "database": "data_warehouse", "username": "${DW_USER:dw_user}", "password": "${DW_PASSWORD}", "schema": "public", "connection_pool": {"max_connections": 20, "min_connections": 5, "connection_timeout": "30s", "idle_timeout": "600s", "max_lifetime": "7200s"}, "properties": {"sslmode": "require", "connect_timeout": "30", "statement_timeout": "300000", "lock_timeout": "30000", "application_name": "etl_pipeline"}, "read_only": false, "validation_query": "SELECT version()", "test_on_borrow": true, "test_while_idle": true, "batch_size": 1000, "enable_transactions": true}}, {"name": "redis_cache", "type": "REDIS", "description": "Redis缓存服务器", "config": {"hostname": "${REDIS_HOST:localhost}", "port": "${REDIS_PORT:6379}", "password": "${REDIS_PASSWORD}", "database": 0, "timeout": "5s", "pool_size": 10, "min_idle_conns": 2, "max_retries": 3, "retry_delay": "1s", "dial_timeout": "5s", "read_timeout": "3s", "write_timeout": "3s", "pool_timeout": "4s", "idle_timeout": "300s", "idle_check_frequency": "60s"}}, {"name": "elasticsearch_logs", "type": "ELASTICSEARCH", "description": "Elasticsearch日志存储", "config": {"hosts": ["${ES_HOST:localhost}:${ES_PORT:9200}"], "username": "${ES_USER}", "password": "${ES_PASSWORD}", "index_prefix": "etl_logs", "timeout": "30s", "max_retries": 3, "retry_delay": "2s", "bulk_size": 100, "flush_interval": "10s", "compression": true, "sniff": false, "health_check": true, "health_check_interval": "60s"}}, {"name": "kafka_events", "type": "KAFKA", "description": "Kafka事件流", "config": {"brokers": ["${KAFKA_BROKER1:localhost:9092}", "${KAFKA_BROKER2:localhost:9093}"], "security_protocol": "SASL_SSL", "sasl_mechanism": "PLAIN", "sasl_username": "${KAFKA_USER}", "sasl_password": "${KAFKA_PASSWORD}", "ssl_ca_location": "/etc/ssl/certs/ca-certificates.crt", "producer": {"acks": "all", "retries": 3, "batch_size": 16384, "linger_ms": 5, "buffer_memory": 33554432, "compression_type": "gzip", "max_request_size": 1048576, "request_timeout_ms": 30000, "delivery_timeout_ms": 120000}, "consumer": {"group_id": "etl_pipeline_group", "auto_offset_reset": "earliest", "enable_auto_commit": false, "max_poll_records": 500, "max_poll_interval_ms": 300000, "session_timeout_ms": 30000, "heartbeat_interval_ms": 3000, "fetch_min_bytes": 1, "fetch_max_wait_ms": 500}}}, {"name": "s3_storage", "type": "S3", "description": "AWS S3存储", "config": {"region": "${AWS_REGION:us-east-1}", "bucket": "${S3_BUCKET:etl-data-bucket}", "access_key": "${AWS_ACCESS_KEY_ID}", "secret_key": "${AWS_SECRET_ACCESS_KEY}", "session_token": "${AWS_SESSION_TOKEN}", "endpoint": "${S3_ENDPOINT}", "force_path_style": false, "use_ssl": true, "timeout": "60s", "max_retries": 3, "retry_delay": "2s", "multipart_threshold": "64MB", "multipart_chunk_size": "5MB", "max_upload_parts": 10000, "server_side_encryption": "AES256", "storage_class": "STANDARD"}}, {"name": "ftp_server", "type": "FTP", "description": "FTP文件服务器", "config": {"hostname": "${FTP_HOST:localhost}", "port": "${FTP_PORT:21}", "username": "${FTP_USER}", "password": "${FTP_PASSWORD}", "passive_mode": true, "timeout": "30s", "keep_alive": "60s", "max_connections": 5, "use_tls": false, "tls_config": {"insecure_skip_verify": false, "server_name": "${FTP_HOST}"}}}, {"name": "smtp_server", "type": "SMTP", "description": "SMTP邮件服务器", "config": {"hostname": "${SMTP_HOST:smtp.company.com}", "port": "${SMTP_PORT:587}", "username": "${SMTP_USER:<EMAIL>}", "password": "${SMTP_PASSWORD}", "use_tls": true, "use_ssl": false, "timeout": "30s", "keep_alive": true, "max_connections": 3, "auth_method": "PLAIN", "from_address": "${SMTP_FROM:<EMAIL>}", "from_name": "ETL System"}}, {"name": "rest_api_service", "type": "HTTP", "description": "REST API服务", "config": {"base_url": "${API_BASE_URL:https://api.company.com}", "timeout": "30s", "max_retries": 3, "retry_delay": "2s", "keep_alive": true, "max_idle_conns": 10, "max_conns_per_host": 5, "idle_conn_timeout": "90s", "tls_handshake_timeout": "10s", "expect_continue_timeout": "1s", "headers": {"User-Agent": "ETL-Pipeline/1.0", "Accept": "application/json", "Content-Type": "application/json"}, "auth": {"type": "bearer", "token": "${API_TOKEN}"}, "rate_limit": {"requests_per_second": 10, "burst": 20}}}, {"name": "mongodb_logs", "type": "MONGODB", "description": "MongoDB日志数据库", "config": {"uri": "${MONGO_URI:mongodb://localhost:27017}", "database": "etl_logs", "username": "${MONGO_USER}", "password": "${MONGO_PASSWORD}", "auth_source": "admin", "timeout": "30s", "max_pool_size": 10, "min_pool_size": 2, "max_idle_time": "300s", "server_selection_timeout": "30s", "socket_timeout": "30s", "connect_timeout": "30s", "heartbeat_frequency": "10s", "local_threshold": "15ms", "max_staleness": "90s", "read_preference": "primary", "write_concern": {"w": "majority", "j": true, "wtimeout": "30s"}, "read_concern": {"level": "majority"}}}]}