{"transformation": {"meta": {"name": "transform_users", "description": "转换用户数据，包含数据增强、标准化和业务逻辑处理", "version": "1.0.0", "author": "ETL Team", "created": "2025-07-07T02:00:00Z", "modified": "2025-07-07T02:00:00Z"}, "parameters": {"target_db": "", "batch_size": "1000", "parallel_workers": "4"}, "variables": {"lookup_timeout": "60", "cache_size": "10000", "error_threshold": "0.05"}, "steps": [{"name": "user_input", "type": "DummyInput", "description": "接收来自提取步骤的用户数据", "config": {}, "fields": [{"name": "user_id", "type": "Integer"}, {"name": "username", "type": "String"}, {"name": "email", "type": "String"}, {"name": "phone", "type": "String"}, {"name": "first_name", "type": "String"}, {"name": "last_name", "type": "String"}, {"name": "birth_date", "type": "Date"}, {"name": "age", "type": "Integer"}, {"name": "age_group", "type": "String"}, {"name": "gender", "type": "String"}, {"name": "status", "type": "String"}, {"name": "created_at", "type": "DateTime"}, {"name": "updated_at", "type": "DateTime"}, {"name": "last_login_at", "type": "DateTime"}, {"name": "batch_date", "type": "String"}, {"name": "extract_time", "type": "DateTime"}, {"name": "source_system", "type": "String"}, {"name": "data_quality_score", "type": "Integer"}, {"name": "validation_status", "type": "String"}, {"name": "has_phone", "type": "Boolean"}, {"name": "profile_completeness", "type": "Float"}], "position": {"x": 100, "y": 100}}, {"name": "lookup_user_preferences", "type": "TableInput", "description": "查找用户偏好设置", "config": {"connection": "${target_db}", "sql": "SELECT user_id, language_preference, timezone, notification_enabled, marketing_consent FROM user_preferences WHERE user_id IN (${user_input.user_id})", "timeout": "${lookup_timeout}s", "cache_enabled": true, "cache_size": "${cache_size}"}, "fields": [{"name": "user_id", "type": "Integer"}, {"name": "language_preference", "type": "String"}, {"name": "timezone", "type": "String"}, {"name": "notification_enabled", "type": "Boolean"}, {"name": "marketing_consent", "type": "Boolean"}], "position": {"x": 100, "y": 300}}, {"name": "lookup_user_segments", "type": "TableInput", "description": "查找用户分群信息", "config": {"connection": "${target_db}", "sql": "SELECT user_id, segment_name, segment_score, last_activity_date FROM user_segments WHERE user_id IN (${user_input.user_id}) AND is_active = 1", "timeout": "${lookup_timeout}s"}, "fields": [{"name": "user_id", "type": "Integer"}, {"name": "segment_name", "type": "String"}, {"name": "segment_score", "type": "Float"}, {"name": "last_activity_date", "type": "Date"}], "position": {"x": 100, "y": 500}}, {"name": "join_user_preferences", "type": "Join", "description": "关联用户偏好数据", "config": {"join_type": "LEFT", "left_keys": ["user_id"], "right_keys": ["user_id"], "left_stream": "user_input", "right_stream": "lookup_user_preferences"}, "position": {"x": 400, "y": 200}}, {"name": "join_user_segments", "type": "Join", "description": "关联用户分群数据", "config": {"join_type": "LEFT", "left_keys": ["user_id"], "right_keys": ["user_id"], "left_stream": "join_user_preferences", "right_stream": "lookup_user_segments"}, "position": {"x": 600, "y": 300}}, {"name": "calculate_derived_fields", "type": "Calculator", "description": "计算衍生字段", "config": {"calculations": [{"field": "full_name", "type": "String", "formula": "CONCAT(${first_name}, ' ', ${last_name})"}, {"field": "days_since_created", "type": "Integer", "formula": "DATEDIFF(NOW(), ${created_at})"}, {"field": "days_since_last_login", "type": "Integer", "formula": "CASE WHEN ${last_login_at} IS NULL THEN -1 ELSE DATEDIFF(NOW(), ${last_login_at}) END"}, {"field": "user_lifecycle_stage", "type": "String", "formula": "${days_since_created} < 30 ? 'New' : ${days_since_last_login} < 7 ? 'Active' : ${days_since_last_login} < 30 ? 'At Risk' : 'Inactive'"}, {"field": "engagement_score", "type": "Float", "formula": "(CASE WHEN ${last_login_at} IS NOT NULL THEN 0.4 ELSE 0 END) + (CASE WHEN ${notification_enabled} = true THEN 0.2 ELSE 0 END) + (CASE WHEN ${marketing_consent} = true THEN 0.1 ELSE 0 END) + (${profile_completeness} * 0.3)"}]}, "position": {"x": 800, "y": 300}}, {"name": "standardize_timezone", "type": "ValueMapper", "description": "标准化时区信息", "config": {"field": "timezone", "mappings": [{"source": "EST", "target": "America/New_York"}, {"source": "PST", "target": "America/Los_Angeles"}, {"source": "CST", "target": "America/Chicago"}, {"source": "MST", "target": "America/Denver"}, {"source": "GMT", "target": "Europe/London"}, {"source": "CET", "target": "Europe/Paris"}, {"source": "JST", "target": "Asia/Tokyo"}, {"source": "CST", "target": "Asia/Shanghai"}], "default_value": "UTC", "target_field": "timezone_standard"}, "position": {"x": 1000, "y": 300}}, {"name": "categorize_user_value", "type": "Calculator", "description": "用户价值分类", "config": {"calculations": [{"field": "user_value_tier", "type": "String", "formula": "${engagement_score} >= 0.8 ? 'High Value' : ${engagement_score} >= 0.5 ? 'Medium Value' : 'Low Value'"}, {"field": "retention_risk", "type": "String", "formula": "${days_since_last_login} > 90 ? 'High Risk' : ${days_since_last_login} > 30 ? 'Medium Risk' : 'Low Risk'"}, {"field": "marketing_priority", "type": "Integer", "formula": "CASE WHEN ${user_value_tier} = 'High Value' AND ${retention_risk} = 'High Risk' THEN 1 WHEN ${user_value_tier} = 'High Value' THEN 2 WHEN ${retention_risk} = 'High Risk' THEN 3 ELSE 4 END"}]}, "position": {"x": 1200, "y": 300}}, {"name": "add_processing_metadata", "type": "AddConstants", "description": "添加处理元数据", "config": {"constants": [{"field": "transform_time", "type": "DateTime", "value": "${NOW}"}, {"field": "transform_version", "type": "String", "value": "1.0.0"}, {"field": "processing_batch", "type": "String", "value": "${batch_date}"}, {"field": "data_lineage", "type": "String", "value": "users -> extract_users -> transform_users"}, {"field": "quality_check_passed", "type": "Boolean", "value": "true"}, {"field": "record_hash", "type": "String", "value": "MD5(CONCAT(${user_id}, ${email}, ${updated_at}))"}]}, "position": {"x": 1400, "y": 300}}, {"name": "apply_business_rules", "type": "FilterRows", "description": "应用业务规则过滤", "config": {"condition": "${status} IN ('active', 'pending') AND ${validation_status} = 'PASSED' AND ${age} >= 13", "send_true_to": "final_field_selection", "send_false_to": "business_rule_violations"}, "position": {"x": 1600, "y": 300}}, {"name": "final_field_selection", "type": "SelectValues", "description": "选择最终输出字段", "config": {"fields": [{"name": "user_id"}, {"name": "username"}, {"name": "email"}, {"name": "phone"}, {"name": "full_name"}, {"name": "first_name"}, {"name": "last_name"}, {"name": "birth_date"}, {"name": "age"}, {"name": "age_group"}, {"name": "gender"}, {"name": "status"}, {"name": "created_at"}, {"name": "updated_at"}, {"name": "last_login_at"}, {"name": "days_since_created"}, {"name": "days_since_last_login"}, {"name": "user_lifecycle_stage"}, {"name": "engagement_score"}, {"name": "user_value_tier"}, {"name": "retention_risk"}, {"name": "marketing_priority"}, {"name": "language_preference"}, {"name": "timezone_standard", "rename": "timezone"}, {"name": "notification_enabled"}, {"name": "marketing_consent"}, {"name": "segment_name"}, {"name": "segment_score"}, {"name": "last_activity_date"}, {"name": "profile_completeness"}, {"name": "data_quality_score"}, {"name": "batch_date"}, {"name": "extract_time"}, {"name": "transform_time"}, {"name": "source_system"}, {"name": "transform_version"}, {"name": "processing_batch"}, {"name": "data_lineage"}, {"name": "record_hash"}]}, "position": {"x": 1800, "y": 300}}, {"name": "sort_by_priority", "type": "Sort", "description": "按营销优先级排序", "config": {"sort_fields": [{"field": "marketing_priority", "order": "ASC"}, {"field": "engagement_score", "order": "DESC"}, {"field": "user_id", "order": "ASC"}]}, "position": {"x": 2000, "y": 300}}, {"name": "transformed_users_output", "type": "DummyOutput", "description": "转换后的用户数据输出", "config": {}, "position": {"x": 2200, "y": 300}}, {"name": "business_rule_violations", "type": "AddConstants", "description": "标记业务规则违规记录", "config": {"constants": [{"field": "violation_type", "type": "String", "value": "BUSINESS_RULE_VIOLATION"}, {"field": "violation_reason", "type": "String", "value": "Status not active/pending, validation failed, or age under 13"}, {"field": "violation_time", "type": "DateTime", "value": "${NOW}"}, {"field": "requires_review", "type": "Boolean", "value": "true"}]}, "position": {"x": 1600, "y": 500}}, {"name": "violation_log_output", "type": "TextFileOutput", "description": "输出业务规则违规记录", "config": {"filename": "/tmp/etl/user_transform_violations_${batch_date}.csv", "separator": ",", "enclosure": "\"", "header": true, "append": false, "fields": ["user_id", "username", "email", "status", "age", "validation_status", "violation_type", "violation_reason", "violation_time", "batch_date"]}, "position": {"x": 1800, "y": 500}}, {"name": "generate_transform_stats", "type": "Aggregator", "description": "生成转换统计信息", "config": {"group_fields": ["user_value_tier", "user_lifecycle_stage"], "aggregations": [{"field": "user_id", "type": "count", "result_field": "user_count"}, {"field": "engagement_score", "type": "avg", "result_field": "avg_engagement"}, {"field": "profile_completeness", "type": "avg", "result_field": "avg_completeness"}, {"field": "days_since_last_login", "type": "avg", "result_field": "avg_days_since_login"}]}, "position": {"x": 2000, "y": 100}}, {"name": "stats_output", "type": "TextFileOutput", "description": "输出转换统计信息", "config": {"filename": "/tmp/etl/user_transform_stats_${batch_date}.csv", "separator": ",", "enclosure": "\"", "header": true, "append": false}, "position": {"x": 2200, "y": 100}}], "hops": [{"from": "user_input", "to": "join_user_preferences", "enabled": true}, {"from": "lookup_user_preferences", "to": "join_user_preferences", "enabled": true}, {"from": "join_user_preferences", "to": "join_user_segments", "enabled": true}, {"from": "lookup_user_segments", "to": "join_user_segments", "enabled": true}, {"from": "join_user_segments", "to": "calculate_derived_fields", "enabled": true}, {"from": "calculate_derived_fields", "to": "standardize_timezone", "enabled": true}, {"from": "standardize_timezone", "to": "categorize_user_value", "enabled": true}, {"from": "categorize_user_value", "to": "add_processing_metadata", "enabled": true}, {"from": "add_processing_metadata", "to": "apply_business_rules", "enabled": true}, {"from": "apply_business_rules", "to": "final_field_selection", "enabled": true, "condition": "true"}, {"from": "apply_business_rules", "to": "business_rule_violations", "enabled": true, "condition": "false"}, {"from": "final_field_selection", "to": "sort_by_priority", "enabled": true}, {"from": "sort_by_priority", "to": "transformed_users_output", "enabled": true}, {"from": "sort_by_priority", "to": "generate_transform_stats", "enabled": true}, {"from": "business_rule_violations", "to": "violation_log_output", "enabled": true}, {"from": "generate_transform_stats", "to": "stats_output", "enabled": true}]}}