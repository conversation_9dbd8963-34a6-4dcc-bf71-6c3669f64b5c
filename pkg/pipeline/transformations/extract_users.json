{"transformation": {"meta": {"name": "extract_users", "description": "从源数据库提取用户数据，包含数据验证和清洗", "version": "1.0.0", "author": "ETL Team", "created": "2025-07-07T02:00:00Z", "modified": "2025-07-07T02:00:00Z"}, "parameters": {"source_db": "", "batch_date": "", "batch_size": "1000"}, "variables": {"sql_timeout": "300", "fetch_size": "500", "max_errors": "100"}, "steps": [{"name": "user_source", "type": "TableInput", "description": "从源数据库读取用户数据", "config": {"connection": "${source_db}", "sql": "SELECT id, username, email, phone, first_name, last_name, birth_date, gender, status, created_at, updated_at, last_login_at FROM users WHERE updated_at >= ? AND updated_at < DATE_ADD(?, INTERVAL 1 DAY) ORDER BY id", "parameters": ["${batch_date}", "${batch_date}"], "limit": "${batch_size}", "fetch_size": "${fetch_size}", "timeout": "${sql_timeout}s"}, "fields": [{"name": "id", "type": "Integer", "length": 10}, {"name": "username", "type": "String", "length": 50}, {"name": "email", "type": "String", "length": 255}, {"name": "phone", "type": "String", "length": 20}, {"name": "first_name", "type": "String", "length": 50}, {"name": "last_name", "type": "String", "length": 50}, {"name": "birth_date", "type": "Date"}, {"name": "gender", "type": "String", "length": 10}, {"name": "status", "type": "String", "length": 20}, {"name": "created_at", "type": "DateTime"}, {"name": "updated_at", "type": "DateTime"}, {"name": "last_login_at", "type": "DateTime"}], "position": {"x": 100, "y": 100}}, {"name": "add_row_number", "type": "AddConstants", "description": "添加行号和批次信息", "config": {"constants": [{"field": "row_number", "type": "Integer", "value": "${ROW_NUMBER}"}, {"field": "batch_date", "type": "String", "value": "${batch_date}"}, {"field": "extract_time", "type": "DateTime", "value": "${NOW}"}, {"field": "source_system", "type": "String", "value": "user_db"}]}, "position": {"x": 300, "y": 100}}, {"name": "validate_required_fields", "type": "FilterRows", "description": "验证必填字段", "config": {"condition": "LENGTH(TRIM(COALESCE(${username}, ''))) > 0 AND LENGTH(TRIM(COALESCE(${email}, ''))) > 0 AND ${id} IS NOT NULL", "send_true_to": "email_format_validator", "send_false_to": "invalid_required_fields"}, "position": {"x": 500, "y": 100}}, {"name": "email_format_validator", "type": "RegexEval", "description": "验证邮箱格式", "config": {"field": "email", "regex": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "result_field": "email_valid", "replace_value": "false", "match_value": "true"}, "position": {"x": 700, "y": 100}}, {"name": "phone_format_validator", "type": "RegexEval", "description": "验证电话号码格式", "config": {"field": "phone", "regex": "^[\\d\\-\\+\\(\\)\\s]{10,20}$", "result_field": "phone_valid", "replace_value": "false", "match_value": "true", "allow_null": true}, "position": {"x": 900, "y": 100}}, {"name": "clean_phone_number", "type": "StringOperations", "description": "清理电话号码格式", "config": {"field": "phone", "operations": [{"type": "replace", "search": "[^\\d]", "replace": "", "use_regex": true}, {"type": "trim"}], "target_field": "phone_clean"}, "position": {"x": 1100, "y": 100}}, {"name": "standardize_names", "type": "StringOperations", "description": "标准化姓名格式", "config": {"operations": [{"field": "first_name", "type": "proper_case", "target_field": "first_name_std"}, {"field": "last_name", "type": "proper_case", "target_field": "last_name_std"}]}, "position": {"x": 1300, "y": 100}}, {"name": "calculate_age", "type": "Calculator", "description": "计算用户年龄", "config": {"calculations": [{"field": "age", "type": "Integer", "formula": "YEAR(NOW()) - YEAR(${birth_date})"}, {"field": "age_group", "type": "String", "formula": "${age} < 18 ? 'Minor' : ${age} < 30 ? 'Young Adult' : ${age} < 50 ? 'Adult' : 'Senior'"}]}, "position": {"x": 1500, "y": 100}}, {"name": "validate_business_rules", "type": "FilterRows", "description": "验证业务规则", "config": {"condition": "${email_valid} == 'true' AND (${phone_valid} == 'true' OR ${phone} IS NULL) AND ${age} >= 0 AND ${age} <= 120", "send_true_to": "add_data_quality_flags", "send_false_to": "invalid_business_rules"}, "position": {"x": 1700, "y": 100}}, {"name": "add_data_quality_flags", "type": "AddConstants", "description": "添加数据质量标识", "config": {"constants": [{"field": "data_quality_score", "type": "Integer", "value": "100"}, {"field": "validation_status", "type": "String", "value": "PASSED"}, {"field": "validation_time", "type": "DateTime", "value": "${NOW}"}, {"field": "has_phone", "type": "Boolean", "value": "${phone_clean} IS NOT NULL AND LENGTH(${phone_clean}) > 0"}, {"field": "profile_completeness", "type": "Float", "value": "(CASE WHEN ${first_name} IS NOT NULL THEN 0.2 ELSE 0 END) + (CASE WHEN ${last_name} IS NOT NULL THEN 0.2 ELSE 0 END) + (CASE WHEN ${phone} IS NOT NULL THEN 0.2 ELSE 0 END) + (CASE WHEN ${birth_date} IS NOT NULL THEN 0.2 ELSE 0 END) + 0.2"}]}, "position": {"x": 1900, "y": 100}}, {"name": "select_final_fields", "type": "SelectValues", "description": "选择最终输出字段", "config": {"fields": [{"name": "id", "rename": "user_id"}, {"name": "username"}, {"name": "email"}, {"name": "phone_clean", "rename": "phone"}, {"name": "first_name_std", "rename": "first_name"}, {"name": "last_name_std", "rename": "last_name"}, {"name": "birth_date"}, {"name": "age"}, {"name": "age_group"}, {"name": "gender"}, {"name": "status"}, {"name": "created_at"}, {"name": "updated_at"}, {"name": "last_login_at"}, {"name": "batch_date"}, {"name": "extract_time"}, {"name": "source_system"}, {"name": "data_quality_score"}, {"name": "validation_status"}, {"name": "has_phone"}, {"name": "profile_completeness"}]}, "position": {"x": 2100, "y": 100}}, {"name": "valid_users_output", "type": "DummyOutput", "description": "有效用户数据输出", "config": {}, "position": {"x": 2300, "y": 100}}, {"name": "invalid_required_fields", "type": "AddConstants", "description": "标记必填字段缺失记录", "config": {"constants": [{"field": "error_type", "type": "String", "value": "MISSING_REQUIRED_FIELDS"}, {"field": "error_message", "type": "String", "value": "Missing required fields: username, email, or id"}, {"field": "validation_status", "type": "String", "value": "FAILED"}, {"field": "validation_time", "type": "DateTime", "value": "${NOW}"}]}, "position": {"x": 500, "y": 300}}, {"name": "invalid_business_rules", "type": "AddConstants", "description": "标记业务规则验证失败记录", "config": {"constants": [{"field": "error_type", "type": "String", "value": "BUSINESS_RULE_VIOLATION"}, {"field": "error_message", "type": "String", "value": "Failed business rule validation: invalid email format, phone format, or age range"}, {"field": "validation_status", "type": "String", "value": "FAILED"}, {"field": "validation_time", "type": "DateTime", "value": "${NOW}"}]}, "position": {"x": 1700, "y": 300}}, {"name": "combine_error_records", "type": "DummyOutput", "description": "合并所有错误记录", "config": {}, "position": {"x": 1100, "y": 400}}, {"name": "error_log_output", "type": "TextFileOutput", "description": "输出错误记录到日志文件", "config": {"filename": "/tmp/etl/user_extraction_errors_${batch_date}.csv", "separator": ",", "enclosure": "\"", "header": true, "append": false, "fields": ["id", "username", "email", "phone", "first_name", "last_name", "error_type", "error_message", "validation_time", "batch_date"]}, "position": {"x": 1300, "y": 400}}, {"name": "data_quality_summary", "type": "Aggregator", "description": "生成数据质量摘要", "config": {"group_fields": ["validation_status"], "aggregations": [{"field": "id", "type": "count", "result_field": "record_count"}, {"field": "profile_completeness", "type": "avg", "result_field": "avg_completeness"}, {"field": "data_quality_score", "type": "avg", "result_field": "avg_quality_score"}]}, "position": {"x": 2100, "y": 300}}, {"name": "quality_summary_output", "type": "TextFileOutput", "description": "输出数据质量摘要", "config": {"filename": "/tmp/etl/user_quality_summary_${batch_date}.csv", "separator": ",", "enclosure": "\"", "header": true, "append": false}, "position": {"x": 2300, "y": 300}}], "hops": [{"from": "user_source", "to": "add_row_number", "enabled": true}, {"from": "add_row_number", "to": "validate_required_fields", "enabled": true}, {"from": "validate_required_fields", "to": "email_format_validator", "enabled": true, "condition": "true"}, {"from": "validate_required_fields", "to": "invalid_required_fields", "enabled": true, "condition": "false"}, {"from": "email_format_validator", "to": "phone_format_validator", "enabled": true}, {"from": "phone_format_validator", "to": "clean_phone_number", "enabled": true}, {"from": "clean_phone_number", "to": "standardize_names", "enabled": true}, {"from": "standardize_names", "to": "calculate_age", "enabled": true}, {"from": "calculate_age", "to": "validate_business_rules", "enabled": true}, {"from": "validate_business_rules", "to": "add_data_quality_flags", "enabled": true, "condition": "true"}, {"from": "validate_business_rules", "to": "invalid_business_rules", "enabled": true, "condition": "false"}, {"from": "add_data_quality_flags", "to": "select_final_fields", "enabled": true}, {"from": "select_final_fields", "to": "valid_users_output", "enabled": true}, {"from": "select_final_fields", "to": "data_quality_summary", "enabled": true}, {"from": "invalid_required_fields", "to": "combine_error_records", "enabled": true}, {"from": "invalid_business_rules", "to": "combine_error_records", "enabled": true}, {"from": "combine_error_records", "to": "error_log_output", "enabled": true}, {"from": "data_quality_summary", "to": "quality_summary_output", "enabled": true}]}}