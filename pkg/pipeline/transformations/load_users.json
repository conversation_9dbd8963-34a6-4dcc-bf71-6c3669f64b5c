{"transformation": {"meta": {"name": "load_users", "description": "将转换后的用户数据加载到数据仓库，支持增量更新和历史记录", "version": "1.0.0", "author": "ETL Team", "created": "2025-07-07T02:00:00Z", "modified": "2025-07-07T02:00:00Z"}, "parameters": {"target_db": "", "batch_date": "", "upsert_mode": "true"}, "variables": {"batch_size": "500", "timeout": "300", "enable_history": "true"}, "steps": [{"name": "transformed_user_input", "type": "DummyInput", "description": "接收转换后的用户数据", "config": {}, "fields": [{"name": "user_id", "type": "Integer"}, {"name": "username", "type": "String"}, {"name": "email", "type": "String"}, {"name": "phone", "type": "String"}, {"name": "full_name", "type": "String"}, {"name": "first_name", "type": "String"}, {"name": "last_name", "type": "String"}, {"name": "birth_date", "type": "Date"}, {"name": "age", "type": "Integer"}, {"name": "age_group", "type": "String"}, {"name": "gender", "type": "String"}, {"name": "status", "type": "String"}, {"name": "created_at", "type": "DateTime"}, {"name": "updated_at", "type": "DateTime"}, {"name": "last_login_at", "type": "DateTime"}, {"name": "days_since_created", "type": "Integer"}, {"name": "days_since_last_login", "type": "Integer"}, {"name": "user_lifecycle_stage", "type": "String"}, {"name": "engagement_score", "type": "Float"}, {"name": "user_value_tier", "type": "String"}, {"name": "retention_risk", "type": "String"}, {"name": "marketing_priority", "type": "Integer"}, {"name": "language_preference", "type": "String"}, {"name": "timezone", "type": "String"}, {"name": "notification_enabled", "type": "Boolean"}, {"name": "marketing_consent", "type": "Boolean"}, {"name": "segment_name", "type": "String"}, {"name": "segment_score", "type": "Float"}, {"name": "last_activity_date", "type": "Date"}, {"name": "profile_completeness", "type": "Float"}, {"name": "data_quality_score", "type": "Integer"}, {"name": "batch_date", "type": "String"}, {"name": "extract_time", "type": "DateTime"}, {"name": "transform_time", "type": "DateTime"}, {"name": "source_system", "type": "String"}, {"name": "transform_version", "type": "String"}, {"name": "processing_batch", "type": "String"}, {"name": "data_lineage", "type": "String"}, {"name": "record_hash", "type": "String"}], "position": {"x": 100, "y": 100}}, {"name": "lookup_existing_records", "type": "TableInput", "description": "查找现有记录以支持增量更新", "config": {"connection": "${target_db}", "sql": "SELECT user_id, record_hash, last_updated FROM dim_users WHERE user_id IN (${transformed_user_input.user_id}) AND is_current = 1", "timeout": "${timeout}s"}, "fields": [{"name": "existing_user_id", "type": "Integer"}, {"name": "existing_record_hash", "type": "String"}, {"name": "existing_last_updated", "type": "DateTime"}], "position": {"x": 100, "y": 300}}, {"name": "join_existing_records", "type": "Join", "description": "关联现有记录信息", "config": {"join_type": "LEFT", "left_keys": ["user_id"], "right_keys": ["existing_user_id"], "left_stream": "transformed_user_input", "right_stream": "lookup_existing_records"}, "position": {"x": 400, "y": 200}}, {"name": "detect_changes", "type": "Calculator", "description": "检测数据变更", "config": {"calculations": [{"field": "is_new_record", "type": "Boolean", "formula": "${existing_user_id} IS NULL"}, {"field": "has_changes", "type": "Boolean", "formula": "${existing_record_hash} IS NULL OR ${existing_record_hash} != ${record_hash}"}, {"field": "change_type", "type": "String", "formula": "${is_new_record} ? 'INSERT' : ${has_changes} ? 'UPDATE' : 'NO_CHANGE'"}, {"field": "load_timestamp", "type": "DateTime", "formula": "${NOW}"}]}, "position": {"x": 600, "y": 200}}, {"name": "filter_changed_records", "type": "FilterRows", "description": "过滤需要更新的记录", "config": {"condition": "${change_type} IN ('INSERT', 'UPDATE')", "send_true_to": "prepare_dimension_record", "send_false_to": "unchanged_records_log"}, "position": {"x": 800, "y": 200}}, {"name": "prepare_dimension_record", "type": "AddConstants", "description": "准备维度表记录", "config": {"constants": [{"field": "dim_user_key", "type": "String", "value": "CONCAT('USER_', ${user_id}, '_', UNIX_TIMESTAMP(${load_timestamp}))"}, {"field": "effective_date", "type": "Date", "value": "${batch_date}"}, {"field": "expiry_date", "type": "Date", "value": "9999-12-31"}, {"field": "is_current", "type": "Boolean", "value": "true"}, {"field": "version_number", "type": "Integer", "value": "CASE WHEN ${is_new_record} THEN 1 ELSE (SELECT COALESCE(MAX(version_number), 0) + 1 FROM dim_users WHERE user_id = ${user_id}) END"}, {"field": "created_by", "type": "String", "value": "ETL_SYSTEM"}, {"field": "updated_by", "type": "String", "value": "ETL_SYSTEM"}]}, "position": {"x": 1000, "y": 200}}, {"name": "split_insert_update", "type": "FilterRows", "description": "分离新增和更新记录", "config": {"condition": "${change_type} = 'INSERT'", "send_true_to": "prepare_insert_records", "send_false_to": "prepare_update_records"}, "position": {"x": 1200, "y": 200}}, {"name": "prepare_insert_records", "type": "SelectValues", "description": "准备新增记录字段", "config": {"fields": [{"name": "dim_user_key"}, {"name": "user_id"}, {"name": "username"}, {"name": "email"}, {"name": "phone"}, {"name": "full_name"}, {"name": "first_name"}, {"name": "last_name"}, {"name": "birth_date"}, {"name": "age"}, {"name": "age_group"}, {"name": "gender"}, {"name": "status"}, {"name": "created_at"}, {"name": "updated_at"}, {"name": "last_login_at"}, {"name": "days_since_created"}, {"name": "days_since_last_login"}, {"name": "user_lifecycle_stage"}, {"name": "engagement_score"}, {"name": "user_value_tier"}, {"name": "retention_risk"}, {"name": "marketing_priority"}, {"name": "language_preference"}, {"name": "timezone"}, {"name": "notification_enabled"}, {"name": "marketing_consent"}, {"name": "segment_name"}, {"name": "segment_score"}, {"name": "last_activity_date"}, {"name": "profile_completeness"}, {"name": "data_quality_score"}, {"name": "effective_date"}, {"name": "expiry_date"}, {"name": "is_current"}, {"name": "version_number"}, {"name": "batch_date"}, {"name": "extract_time"}, {"name": "transform_time"}, {"name": "load_timestamp"}, {"name": "source_system"}, {"name": "transform_version"}, {"name": "processing_batch"}, {"name": "data_lineage"}, {"name": "record_hash"}, {"name": "created_by"}, {"name": "updated_by"}]}, "position": {"x": 1400, "y": 100}}, {"name": "prepare_update_records", "type": "SelectValues", "description": "准备更新记录字段", "config": {"fields": [{"name": "dim_user_key"}, {"name": "user_id"}, {"name": "username"}, {"name": "email"}, {"name": "phone"}, {"name": "full_name"}, {"name": "first_name"}, {"name": "last_name"}, {"name": "birth_date"}, {"name": "age"}, {"name": "age_group"}, {"name": "gender"}, {"name": "status"}, {"name": "created_at"}, {"name": "updated_at"}, {"name": "last_login_at"}, {"name": "days_since_created"}, {"name": "days_since_last_login"}, {"name": "user_lifecycle_stage"}, {"name": "engagement_score"}, {"name": "user_value_tier"}, {"name": "retention_risk"}, {"name": "marketing_priority"}, {"name": "language_preference"}, {"name": "timezone"}, {"name": "notification_enabled"}, {"name": "marketing_consent"}, {"name": "segment_name"}, {"name": "segment_score"}, {"name": "last_activity_date"}, {"name": "profile_completeness"}, {"name": "data_quality_score"}, {"name": "effective_date"}, {"name": "expiry_date"}, {"name": "is_current"}, {"name": "version_number"}, {"name": "batch_date"}, {"name": "extract_time"}, {"name": "transform_time"}, {"name": "load_timestamp"}, {"name": "source_system"}, {"name": "transform_version"}, {"name": "processing_batch"}, {"name": "data_lineage"}, {"name": "record_hash"}, {"name": "updated_by"}]}, "position": {"x": 1400, "y": 300}}, {"name": "expire_old_records", "type": "<PERSON><PERSON><PERSON>", "description": "将旧记录标记为过期（SCD Type 2）", "config": {"script_type": "sql", "connection": "${target_db}", "script": "UPDATE dim_users SET is_current = false, expiry_date = CURDATE() WHERE user_id IN (${prepare_update_records.user_id}) AND is_current = true", "timeout": "${timeout}s"}, "position": {"x": 1600, "y": 300}}, {"name": "insert_new_records", "type": "TableOutput", "description": "插入新记录到维度表", "config": {"connection": "${target_db}", "table": "dim_users", "batch_size": "${batch_size}", "timeout": "${timeout}s", "on_conflict": "ignore", "commit_size": "${batch_size}"}, "position": {"x": 1600, "y": 100}}, {"name": "insert_updated_records", "type": "TableOutput", "description": "插入更新记录到维度表", "config": {"connection": "${target_db}", "table": "dim_users", "batch_size": "${batch_size}", "timeout": "${timeout}s", "on_conflict": "ignore", "commit_size": "${batch_size}"}, "position": {"x": 1800, "y": 300}}, {"name": "update_fact_tables", "type": "<PERSON><PERSON><PERSON>", "description": "更新相关事实表的用户维度键", "config": {"script_type": "sql", "connection": "${target_db}", "script": "UPDATE fact_user_activities fa JOIN dim_users du ON fa.user_id = du.user_id SET fa.dim_user_key = du.dim_user_key WHERE du.is_current = true AND fa.dim_user_key IS NULL AND fa.activity_date >= '${batch_date}'", "timeout": "${timeout}s", "ignore_errors": false}, "position": {"x": 2000, "y": 200}}, {"name": "log_load_statistics", "type": "<PERSON><PERSON><PERSON>", "description": "记录加载统计信息", "config": {"script_type": "sql", "connection": "${target_db}", "script": "INSERT INTO etl_load_log (table_name, batch_date, records_inserted, records_updated, records_unchanged, load_start_time, load_end_time, status) SELECT 'dim_users', '${batch_date}', (SELECT COUNT(*) FROM dim_users WHERE batch_date = '${batch_date}' AND version_number = 1), (SELECT COUNT(*) FROM dim_users WHERE batch_date = '${batch_date}' AND version_number > 1), ${unchanged_records_log.record_count}, '${load_timestamp}', NOW(), 'SUCCESS'", "timeout": "60s"}, "position": {"x": 2200, "y": 200}}, {"name": "create_data_quality_report", "type": "Aggregator", "description": "生成数据质量报告", "config": {"group_fields": ["user_value_tier", "data_quality_score"], "aggregations": [{"field": "user_id", "type": "count", "result_field": "user_count"}, {"field": "profile_completeness", "type": "avg", "result_field": "avg_completeness"}, {"field": "engagement_score", "type": "avg", "result_field": "avg_engagement"}]}, "position": {"x": 1800, "y": 100}}, {"name": "quality_report_output", "type": "TextFileOutput", "description": "输出数据质量报告", "config": {"filename": "/tmp/etl/user_load_quality_report_${batch_date}.csv", "separator": ",", "enclosure": "\"", "header": true, "append": false}, "position": {"x": 2000, "y": 100}}, {"name": "unchanged_records_log", "type": "Aggregator", "description": "统计未变更记录", "config": {"group_fields": [], "aggregations": [{"field": "user_id", "type": "count", "result_field": "record_count"}]}, "position": {"x": 800, "y": 400}}, {"name": "final_output", "type": "DummyOutput", "description": "最终输出", "config": {}, "position": {"x": 2400, "y": 200}}, {"name": "error_handler", "type": "TextFileOutput", "description": "错误记录处理", "config": {"filename": "/tmp/etl/user_load_errors_${batch_date}.csv", "separator": ",", "enclosure": "\"", "header": true, "append": false, "fields": ["user_id", "username", "email", "error_message", "error_time", "batch_date"]}, "position": {"x": 1200, "y": 500}}], "hops": [{"from": "transformed_user_input", "to": "join_existing_records", "enabled": true}, {"from": "lookup_existing_records", "to": "join_existing_records", "enabled": true}, {"from": "join_existing_records", "to": "detect_changes", "enabled": true}, {"from": "detect_changes", "to": "filter_changed_records", "enabled": true}, {"from": "filter_changed_records", "to": "prepare_dimension_record", "enabled": true, "condition": "true"}, {"from": "filter_changed_records", "to": "unchanged_records_log", "enabled": true, "condition": "false"}, {"from": "prepare_dimension_record", "to": "split_insert_update", "enabled": true}, {"from": "split_insert_update", "to": "prepare_insert_records", "enabled": true, "condition": "true"}, {"from": "split_insert_update", "to": "prepare_update_records", "enabled": true, "condition": "false"}, {"from": "prepare_insert_records", "to": "insert_new_records", "enabled": true}, {"from": "prepare_update_records", "to": "expire_old_records", "enabled": true}, {"from": "expire_old_records", "to": "insert_updated_records", "enabled": true}, {"from": "insert_new_records", "to": "create_data_quality_report", "enabled": true}, {"from": "insert_updated_records", "to": "update_fact_tables", "enabled": true}, {"from": "update_fact_tables", "to": "log_load_statistics", "enabled": true}, {"from": "log_load_statistics", "to": "final_output", "enabled": true}, {"from": "create_data_quality_report", "to": "quality_report_output", "enabled": true}, {"from": "quality_report_output", "to": "final_output", "enabled": true}, {"from": "unchanged_records_log", "to": "final_output", "enabled": true}]}}