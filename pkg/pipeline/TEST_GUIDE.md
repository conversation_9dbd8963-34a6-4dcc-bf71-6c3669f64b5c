# ETL Pipeline 测试指南

## 概述

本文档提供了完整的ETL Pipeline组件测试配置和使用指南。这些配置文件展示了一个企业级ETL流程的完整实现，包括数据提取、转换、加载和质量检查。

## 测试文件结构

```
pkg/pipeline/
├── test_comprehensive_etl.json      # 主Job配置文件
├── transformations/                 # 转换配置目录
│   ├── extract_users.json          # 数据提取转换
│   ├── transform_users.json        # 数据转换处理
│   └── load_users.json             # 数据加载转换
├── connections.json                 # 连接配置文件
├── test_pipeline.go                # 测试脚本
└── TEST_GUIDE.md                   # 本文档
```

## 配置文件说明

### 1. 主Job配置 (test_comprehensive_etl.json)

这是一个完整的ETL作业配置，包含以下特性：

**核心功能：**
- ✅ 环境初始化和连接验证
- ✅ 数据源状态检查和数据量评估
- ✅ 条件分支处理（有数据/无数据）
- ✅ 三阶段ETL流程（提取→转换→加载）
- ✅ 数据质量检查和验证
- ✅ 错误处理和回滚机制
- ✅ 邮件通知和日志记录
- ✅ 资源清理和状态更新

**流程控制：**
- 支持条件分支和错误处理
- 包含完整的成功/失败路径
- 实现了数据备份和回滚机制
- 提供详细的执行日志和通知

### 2. 转换配置文件

#### extract_users.json - 数据提取
- 从源数据库读取用户数据
- 数据验证和格式检查
- 错误记录分离和日志记录
- 数据质量评分和统计

#### transform_users.json - 数据转换
- 数据增强和标准化
- 业务规则应用
- 用户价值分析和分群
- 衍生字段计算

#### load_users.json - 数据加载
- SCD Type 2 历史记录管理
- 增量更新和变更检测
- 数据仓库维度表更新
- 加载统计和质量报告

### 3. 连接配置 (connections.json)

支持多种数据源和目标：
- **数据库**: MySQL, PostgreSQL, MongoDB
- **缓存**: Redis
- **消息队列**: Kafka
- **存储**: AWS S3, FTP
- **搜索引擎**: Elasticsearch
- **通信**: SMTP, HTTP/REST API

## 运行测试

### 前置条件

1. **Go环境**: Go 1.21+
2. **依赖包**: 确保所有必要的Go模块已安装
3. **配置文件**: 所有JSON配置文件在正确位置

### 执行测试

```bash
# 进入pipeline目录
cd /Volumes/data/Code/Go/src/admin/pkg/pipeline

# 运行测试脚本
go run test_pipeline.go
```

### 预期输出

```
=== ETL Pipeline 综合测试 ===
测试时间: 2025-07-07 11:00:00

1. 测试配置文件加载...
   - Job名称: comprehensive_etl_test
   - Job描述: 全面的ETL流程测试，涵盖数据提取、转换、加载和质量检查
   - 参数数量: 8
   - 变量数量: 6
   - 条目数量: 18
   - 连接数量: 25
✅ 配置文件加载测试通过

2. 测试连接配置...
   - 连接总数: 10
   - user_source (DATABASE): 用户源数据库 - MySQL主库
   - user_warehouse (DATABASE): 用户数据仓库 - PostgreSQL
   - redis_cache (REDIS): Redis缓存服务器
   ...
✅ 连接配置测试通过

3. 测试Transformation配置...
   - extract_users: 15个步骤, 16个连接
   - transform_users: 18个步骤, 15个连接
   - load_users: 20个步骤, 18个连接
✅ Transformation配置测试通过

...

=== 测试总结 ===
✅ 所有核心功能测试通过
📊 配置文件结构完整
🔧 组件注册机制正常
🔄 变量解析功能正常
⚡ 系统基本就绪
```

## 生产环境部署建议

### 1. 环境配置

```bash
# 设置环境变量
export DB_HOST=prod-mysql.company.com
export DB_USER=etl_user
export DB_PASSWORD=secure_password
export DW_HOST=prod-postgres.company.com
export DW_USER=dw_user
export DW_PASSWORD=secure_password
export REDIS_HOST=prod-redis.company.com
export SMTP_HOST=smtp.company.com
export SMTP_USER=<EMAIL>
export SMTP_PASSWORD=email_password
```

### 2. 数据库准备

#### MySQL源数据库表结构
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    birth_date DATE,
    gender ENUM('M', 'F', 'Other'),
    status ENUM('active', 'inactive', 'pending', 'suspended'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    INDEX idx_updated_at (updated_at),
    INDEX idx_status (status)
);
```

#### PostgreSQL数据仓库表结构
```sql
-- 用户维度表 (SCD Type 2)
CREATE TABLE dim_users (
    dim_user_key VARCHAR(100) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    username VARCHAR(50),
    email VARCHAR(255),
    phone VARCHAR(20),
    full_name VARCHAR(100),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    birth_date DATE,
    age INTEGER,
    age_group VARCHAR(20),
    gender VARCHAR(10),
    status VARCHAR(20),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    last_login_at TIMESTAMP,
    days_since_created INTEGER,
    days_since_last_login INTEGER,
    user_lifecycle_stage VARCHAR(20),
    engagement_score DECIMAL(5,4),
    user_value_tier VARCHAR(20),
    retention_risk VARCHAR(20),
    marketing_priority INTEGER,
    language_preference VARCHAR(10),
    timezone VARCHAR(50),
    notification_enabled BOOLEAN,
    marketing_consent BOOLEAN,
    segment_name VARCHAR(50),
    segment_score DECIMAL(5,4),
    last_activity_date DATE,
    profile_completeness DECIMAL(5,4),
    data_quality_score INTEGER,
    effective_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    version_number INTEGER NOT NULL DEFAULT 1,
    batch_date VARCHAR(10),
    extract_time TIMESTAMP,
    transform_time TIMESTAMP,
    load_timestamp TIMESTAMP,
    source_system VARCHAR(50),
    transform_version VARCHAR(10),
    processing_batch VARCHAR(50),
    data_lineage TEXT,
    record_hash VARCHAR(64),
    created_by VARCHAR(50),
    updated_by VARCHAR(50)
);

-- 索引
CREATE INDEX idx_dim_users_user_id ON dim_users(user_id);
CREATE INDEX idx_dim_users_is_current ON dim_users(is_current);
CREATE INDEX idx_dim_users_effective_date ON dim_users(effective_date);
CREATE INDEX idx_dim_users_batch_date ON dim_users(batch_date);

-- ETL作业日志表
CREATE TABLE etl_job_log (
    id SERIAL PRIMARY KEY,
    job_name VARCHAR(100) NOT NULL,
    batch_date VARCHAR(10) NOT NULL,
    status VARCHAR(20) NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    records_processed INTEGER DEFAULT 0,
    error_message TEXT,
    error_step VARCHAR(100),
    message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ETL加载日志表
CREATE TABLE etl_load_log (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    batch_date VARCHAR(10) NOT NULL,
    records_inserted INTEGER DEFAULT 0,
    records_updated INTEGER DEFAULT 0,
    records_unchanged INTEGER DEFAULT 0,
    load_start_time TIMESTAMP,
    load_end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户偏好表
CREATE TABLE user_preferences (
    user_id INTEGER PRIMARY KEY,
    language_preference VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50) DEFAULT 'UTC',
    notification_enabled BOOLEAN DEFAULT TRUE,
    marketing_consent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户分群表
CREATE TABLE user_segments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    segment_name VARCHAR(50) NOT NULL,
    segment_score DECIMAL(5,4),
    last_activity_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 监控和告警

#### Prometheus指标配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'etl-pipeline'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s
```

#### Grafana Dashboard配置
```json
{
  "dashboard": {
    "title": "ETL Pipeline Monitoring",
    "panels": [
      {
        "title": "Job Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(etl_job_success_total[1h]) / rate(etl_job_total[1h]) * 100"
          }
        ]
      },
      {
        "title": "Processing Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, etl_job_duration_seconds_bucket)"
          }
        ]
      },
      {
        "title": "Records Processed",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(etl_records_processed_total[5m])"
          }
        ]
      }
    ]
  }
}
```

### 4. 调度配置

#### Cron调度示例
```bash
# 每日凌晨2点执行ETL作业
0 2 * * * /usr/local/bin/etl-runner --config /etc/etl/prod.yml --job comprehensive_etl_test --batch-date $(date -d "yesterday" +%Y-%m-%d)

# 每小时执行增量同步
0 * * * * /usr/local/bin/etl-runner --config /etc/etl/prod.yml --job incremental_sync --batch-date $(date +%Y-%m-%d)
```

## 性能优化建议

### 1. 数据库优化
- 为经常查询的字段创建索引
- 使用分区表处理大数据量
- 配置合适的连接池大小
- 启用查询缓存

### 2. 内存管理
- 设置合适的批处理大小
- 使用流式处理避免内存溢出
- 监控内存使用情况
- 配置垃圾回收参数

### 3. 并发处理
- 合理设置并行工作线程数
- 使用异步处理提高吞吐量
- 避免资源竞争和死锁
- 实现背压控制机制

## 故障排查

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加超时时间
   - 验证连接配置

2. **内存不足**
   - 减少批处理大小
   - 增加系统内存
   - 优化数据处理逻辑

3. **数据质量问题**
   - 检查源数据格式
   - 验证业务规则
   - 查看错误日志文件

4. **性能问题**
   - 分析执行计划
   - 优化SQL查询
   - 调整并发参数

### 日志分析

```bash
# 查看ETL执行日志
tail -f /tmp/etl/*.log

# 检查错误记录
cat /tmp/etl/*_errors_*.csv

# 查看数据质量报告
cat /tmp/etl/*_quality_*.csv
```

## 扩展开发

### 添加新的Step组件

1. 实现Step接口
2. 注册到Step注册表
3. 添加配置验证
4. 编写单元测试
5. 更新文档

### 集成新的数据源

1. 实现连接接口
2. 添加连接配置
3. 创建相应的Input/Output步骤
4. 测试连接稳定性
5. 添加监控指标

## 总结

这套ETL Pipeline配置提供了一个完整的企业级数据处理解决方案。通过合理的配置和优化，可以满足大多数数据集成和处理需求。建议在生产环境部署前进行充分的测试和性能调优。
