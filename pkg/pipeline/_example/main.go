package main

import (
	"context"
	"fmt"

	"admin/pkg/pipeline/config"
	"admin/pkg/pipeline/core"
	"admin/pkg/pipeline/core/job"
	"admin/pkg/pipeline/core/step"

	"go.uber.org/zap"
)

func main() {
	// 1. 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()
	logger.Info("ETL 示例程序启动")

	// 2. 定义配置文件路径
	exampleDir := "/Volumes/data/Code/Go/src/admin/pkg/pipeline/_example"
	// jobFilePath := filepath.Join(exampleDir, "sample_job.json")
	// transformationFilePath := filepath.Join(exampleDir, "sample_transformation.json")

	// 3. 创建文件加载器
	fileLoader := config.NewFileLoader(exampleDir)

	// 4. 加载 Job 配置
	jobMeta, err := fileLoader.LoadJob("complete_pipeline_test.json")
	if err != nil {
		logger.Error("加载 Job 配置失败", zap.Error(err))
		return
	}
	logger.Info("Job 配置加载成功", zap.String("job_name", jobMeta.Meta.Name))

	// 5. 注册所有步骤类型
	stepRegistry := step.NewRegistry()
	core.RegisterBasicSteps(stepRegistry)
	logger.Info("ETL 步骤注册完成")

	// 6. 创建 Job 执行器
	executor := job.NewExecutor(jobMeta, stepRegistry, logger)
	if executor == nil {
		logger.Error("创建 Job 执行器失败")
		return
	}
	defer executor.Close() // 确保执行器关闭

	logger.Info("Job 执行器创建成功")

	// 7. 执行 Job
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel() // 确保上下文被取消

	err = executor.Execute(ctx)
	if err != nil {
		logger.Error("Job 执行失败", zap.Error(err))
	} else {
		logger.Info("Job 执行完成")
	}

	// 8. 打印执行结果概要
	results := executor.GetResults()
	fmt.Println("\n--- Job 执行结果概要 ---")
	for entryName, result := range results {
		fmt.Printf("条目: %s, 状态: %s, 耗时: %v\n",
			entryName, result.Status.String(), result.EndTime.Sub(result.StartTime))
		if result.Error != nil {
			fmt.Printf("  错误: %v\n", result.Error)
		}
	}
	fmt.Println("----------------------")

	// 9. 清理创建的 JSON 文件 (可选)
	// os.Remove(jobFilePath)
	// os.Remove(transformationFilePath)
	// os.Remove(exampleDir) // 如果目录是空的，可以尝试删除
}
