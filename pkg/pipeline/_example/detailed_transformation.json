{"meta": {"name": "DetailedDataTransformation", "description": "A comprehensive data processing transformation with multiple steps", "version": "1.0.0", "author": "<PERSON><PERSON>", "created": "2025-06-20T14:57:54+08:00", "modified": "2025-06-20T14:57:54+08:00"}, "parameters": {"input_file": "input/data.csv", "output_file": "output/processed_data.csv", "filter_threshold": "100", "sample_percentage": "0.1"}, "variables": {"transformation.start_time": "${NOW()}", "transformation.run_id": "${UUID()}"}, "steps": [{"name": "CSVInput", "type": "CSVFileInput", "description": "Read input data from CSV file", "config": {"filename": "${input_file}", "delimiter": ",", "has_header": true, "encoding": "utf-8"}, "fields": [{"name": "id", "type": "Integer"}, {"name": "name", "type": "String"}, {"name": "value", "type": "Number"}, {"name": "timestamp", "type": "Timestamp"}], "position": {"x": 100, "y": 100}}, {"name": "FilterRows", "type": "FilterRows", "description": "Filter rows based on value threshold", "config": {"conditions": [{"field": "value", "operator": "ge", "value": "${filter_threshold}"}]}, "position": {"x": 300, "y": 100}}, {"name": "SampleData", "type": "SampleRows", "description": "Randomly sample data rows", "config": {"percentage": "${sample_percentage}", "seed": "12345"}, "position": {"x": 300, "y": 200}}, {"name": "AggregateValues", "type": "Aggregate", "description": "Calculate summary statistics", "config": {"group_by": ["name"], "aggregations": [{"field": "value", "operation": "avg", "output_field": "avg_value"}, {"field": "value", "operation": "max", "output_field": "max_value"}, {"field": "value", "operation": "min", "output_field": "min_value"}]}, "fields": [{"name": "name", "type": "String"}, {"name": "avg_value", "type": "Number"}, {"name": "max_value", "type": "Number"}, {"name": "min_value", "type": "Number"}], "position": {"x": 500, "y": 100}}, {"name": "JSONOutput", "type": "JSONFileOutput", "description": "Write output to JSON file", "config": {"filename": "${output_file}", "pretty_print": true, "encoding": "utf-8"}, "position": {"x": 700, "y": 100}}], "hops": [{"from": "CSVInput", "to": "FilterRows", "enabled": true}, {"from": "FilterRows", "to": "SampleData", "enabled": true}, {"from": "SampleData", "to": "AggregateValues", "enabled": true}, {"from": "AggregateValues", "to": "JSONOutput", "enabled": true}]}