{"job": {"meta": {"name": "simple-user-processing-job", "description": "一个简单的用户数据处理作业", "version": "1.0.0", "author": "Gemini", "created": "2025-06-25T10:00:00Z"}, "parameters": {"input_file": "users.csv", "output_file": "processed_users.json", "min_age": "18"}, "entries": [{"name": "START", "type": "START", "description": "作业开始"}, {"name": "read_user_data", "type": "DUMMY", "description": "模拟从 CSV 文件读取用户数据", "config": {"row_count": 100, "fields": [{"name": "id", "type": "Integer"}, {"name": "name", "type": "String"}, {"name": "age", "type": "Integer"}]}}, {"name": "filter_underage_users", "type": "TRANS", "description": "过滤掉未满18岁的用户", "config": {"transformation_name": "filter_rows", "steps": [{"type": "FilterRows", "condition": {"expression": "age >= {min_age}"}}]}}, {"name": "capitalize_username", "type": "TRANS", "description": "将用户名转换为大写", "config": {"transformation_name": "capitalize_name", "steps": [{"type": "StringOperations", "operations": [{"field_in": "name", "field_out": "name_upper", "operation": "upper"}]}]}}, {"name": "write_processed_data", "type": "DUMMY", "description": "模拟将处理后的数据写入 JSON 文件"}, {"name": "SUCCESS", "type": "SUCCESS", "description": "作业成功结束"}], "hops": [{"from": "START", "to": "read_user_data", "unconditional": true}, {"from": "read_user_data", "to": "filter_underage_users", "unconditional": true}, {"from": "filter_underage_users", "to": "capitalize_username", "unconditional": true}, {"from": "capitalize_username", "to": "write_processed_data", "unconditional": true}, {"from": "write_processed_data", "to": "SUCCESS", "unconditional": true}]}}