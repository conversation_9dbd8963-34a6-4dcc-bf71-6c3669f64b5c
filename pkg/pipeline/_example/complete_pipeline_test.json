{"job": {"meta": {"name": "complete-pipeline-test", "description": "测试所有步骤类型的完整管道", "version": "1.0.0", "author": "Pipeline Test Team", "created": "2025-04-05T00:00:00Z", "modified": "2025-04-05T00:00:00Z"}, "parameters": {"source_db": "test_source", "target_db": "test_warehouse", "batch_date": "2025-04-05", "batch_size": "1000"}, "variables": {"log_level": "INFO", "retry_count": "3", "timeout": "30m"}, "entries": [{"name": "START", "type": "START", "description": "作业开始", "position": {"x": 100, "y": 100}}, {"name": "dummy_input", "type": "DUMMY", "description": "测试DummyInput", "config": {"row_count": 10, "fields": [{"name": "id", "type": "Integer"}, {"name": "name", "type": "String"}, {"name": "email", "type": "String"}]}, "position": {"x": 300, "y": 100}}, {"name": "select_values", "type": "TRANS", "description": "测试SelectValues", "config": {"transformation_file": "select_values.json"}, "position": {"x": 500, "y": 80}}, {"name": "filter_rows", "type": "TRANS", "description": "测试FilterRows", "config": {"transformation_file": "filter_rows.json"}, "position": {"x": 700, "y": 80}}, {"name": "calculator", "type": "TRANS", "description": "测试Calculator", "config": {"transformation_file": "calculator.json"}, "position": {"x": 900, "y": 80}}, {"name": "value_mapper", "type": "TRANS", "description": "测试ValueMapper", "config": {"transformation_file": "value_mapper.json"}, "position": {"x": 1100, "y": 80}}, {"name": "string_operations", "type": "TRANS", "description": "测试StringOperations", "config": {"transformation_file": "string_operations.json"}, "position": {"x": 1300, "y": 80}}, {"name": "date_operations", "type": "TRANS", "description": "测试DateOperations", "config": {"transformation_file": "date_operations.json"}, "position": {"x": 1500, "y": 80}}, {"name": "add_constants", "type": "TRANS", "description": "测试AddConstants", "config": {"transformation_file": "add_constants.json"}, "position": {"x": 1700, "y": 80}}, {"name": "regex_eval", "type": "EVAL", "description": "测试RegexEval", "config": {"condition": "upper_name != null and upper_name != ''", "success_message": "正则匹配成功", "failure_message": "正则匹配失败"}, "position": {"x": 1900, "y": 80}}, {"name": "sort", "type": "TRANS", "description": "测试Sort", "config": {"transformation_file": "sort.json"}, "position": {"x": 2100, "y": 80}}, {"name": "unique_rows", "type": "TRANS", "description": "测试UniqueRows", "config": {"transformation_file": "unique_rows.json"}, "position": {"x": 2300, "y": 80}}, {"name": "enhanced_filter", "type": "TRANS", "description": "测试EnhancedFilter", "config": {"transformation_file": "enhanced_filter.json"}, "position": {"x": 2500, "y": 80}}, {"name": "enhanced_calculator", "type": "TRANS", "description": "测试EnhancedCalculator", "config": {"transformation_file": "enhanced_calculator.json"}, "position": {"x": 2700, "y": 80}}, {"name": "dummy_output", "type": "DUMMY", "description": "测试DummyOutput", "position": {"x": 2900, "y": 80}}, {"name": "json_output", "type": "TRANS", "description": "测试JSONFileOutput", "config": {"transformation_file": "json_output.json"}, "position": {"x": 3100, "y": 80}}, {"name": "http_output", "type": "TRANS", "description": "测试RestOutput", "config": {"transformation_file": "http_output.json"}, "position": {"x": 3300, "y": 80}}, {"name": "SUCCESS", "type": "SUCCESS", "description": "作业成功结束", "position": {"x": 3500, "y": 80}}], "hops": [{"from": "START", "to": "dummy_input", "evaluation": true, "unconditional": true}, {"from": "dummy_input", "to": "select_values", "evaluation": true, "unconditional": true}, {"from": "select_values", "to": "filter_rows", "evaluation": true, "unconditional": true}, {"from": "filter_rows", "to": "calculator", "evaluation": true, "unconditional": true}, {"from": "calculator", "to": "value_mapper", "evaluation": true, "unconditional": true}, {"from": "value_mapper", "to": "string_operations", "evaluation": true, "unconditional": true}, {"from": "string_operations", "to": "date_operations", "evaluation": true, "unconditional": true}, {"from": "date_operations", "to": "add_constants", "evaluation": true, "unconditional": true}, {"from": "add_constants", "to": "regex_eval", "evaluation": true, "unconditional": true}, {"from": "regex_eval", "to": "sort", "evaluation": true, "unconditional": true}, {"from": "sort", "to": "unique_rows", "evaluation": true, "unconditional": true}, {"from": "unique_rows", "to": "enhanced_filter", "evaluation": true, "unconditional": true}, {"from": "enhanced_filter", "to": "enhanced_calculator", "evaluation": true, "unconditional": true}, {"from": "enhanced_calculator", "to": "dummy_output", "evaluation": true, "unconditional": true}, {"from": "dummy_output", "to": "json_output", "evaluation": true, "unconditional": true}, {"from": "json_output", "to": "http_output", "evaluation": true, "unconditional": true}, {"from": "http_output", "to": "SUCCESS", "evaluation": true, "unconditional": true}]}}