{"meta": {"name": "SampleTransformation", "description": "A sample transformation for generating, filtering, and selecting data.", "version": "1.0.0", "author": "<PERSON><PERSON>", "created": "2025-06-15T00:00:00Z", "modified": "2025-06-15T00:00:00Z"}, "parameters": {"row_count": "100", "filter_expression": "value >= 80", "output_file_name": "filtered_output.csv"}, "variables": {"trans.start_time": "${NOW()}"}, "steps": [{"name": "CSVInput", "type": "CSVFileInput", "description": "Reads input data from CSV", "config": {"filename": "input/data.csv", "delimiter": ",", "has_header": true, "fields": [{"name": "id", "type": "Integer"}, {"name": "value", "type": "Number"}, {"name": "timestamp", "type": "Timestamp", "format": "yyyy-MM-dd HH:mm:ss"}]}, "fields": [{"name": "id", "type": "Integer"}, {"name": "value", "type": "Number"}, {"name": "timestamp", "type": "Timestamp"}], "position": {"x": 100, "y": 100}}, {"name": "FilterRows", "type": "FilterRows", "description": "Filters rows based on a condition", "config": {"conditions": [{"field": "value", "operator": "ge", "value": 50}]}, "position": {"x": 300, "y": 100}}, {"name": "SelectValues", "type": "SelectValues", "description": "Selects and renames fields", "config": {"fields": [{"source_field": "id", "target_field": "record_id"}, {"source_field": "value", "target_field": "data_value"}]}, "fields": [{"name": "record_id", "type": "Integer"}, {"name": "data_value", "type": "Number"}], "position": {"x": 500, "y": 100}}, {"name": "CSVOutput", "type": "CSVFileOutput", "description": "Outputs data to CSV file", "config": {"filename": "output/filtered_data.csv", "delimiter": ",", "write_header": true, "encoding": "utf-8"}, "position": {"x": 700, "y": 100}}], "hops": [{"from": "CSVInput", "to": "FilterRows", "enabled": true}, {"from": "FilterRows", "to": "SelectValues", "enabled": true}, {"from": "SelectValues", "to": "CSVOutput", "enabled": true}]}