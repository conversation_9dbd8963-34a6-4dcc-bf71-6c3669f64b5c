package lexer

import (
	"strings"
	"unicode"
	"unicode/utf8"
)

// Lexer 表示词法分析器
type Lexer struct {
	input        string // 输入的表达式字符串
	readPosition int    // 下一个位置
	ch           rune   // 当前字符

	line   int // 当前行号
	column int // 当前列号
}

// New 创建一个新的词法分析器
func New(input string) *Lexer {
	l := &Lexer{
		input:  input,
		line:   1,
		column: 0,
	}
	l.readChar()
	return l
}

// NextToken 获取下一个token
func (l *Lexer) NextToken() Token {
	l.skipWhitespace()

	// 记录当前token的起始位置
	line := l.line
	column := l.column

	var tok Token

	switch l.ch {
	case '=':
		if l.peek<PERSON>har() == '=' {
			ch := l.ch
			l.readChar()
			tok = Token{Type: TOKEN_EQ, Literal: string(ch) + string(l.ch), Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_ILLEGAL, Literal: string(l.ch), Line: line, Column: column}
		}
	case '!':
		if l.peek<PERSON>har() == '=' {
			ch := l.ch
			l.readChar()
			tok = Token{Type: TOKEN_NEQ, Literal: string(ch) + string(l.ch), Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_NOT, Literal: string(l.ch), Line: line, Column: column}
		}
	case '+':
		tok = Token{Type: TOKEN_PLUS, Literal: string(l.ch), Line: line, Column: column}
	case '-':
		tok = Token{Type: TOKEN_MINUS, Literal: string(l.ch), Line: line, Column: column}
	case '*':
		tok = Token{Type: TOKEN_MULTIPLY, Literal: string(l.ch), Line: line, Column: column}
	case '/':
		tok = Token{Type: TOKEN_DIVIDE, Literal: string(l.ch), Line: line, Column: column}
	case '%':
		tok = Token{Type: TOKEN_MOD, Literal: string(l.ch), Line: line, Column: column}
	case '<':
		if l.peekChar() == '=' {
			ch := l.ch
			l.readChar()
			tok = Token{Type: TOKEN_LTE, Literal: string(ch) + string(l.ch), Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_LT, Literal: string(l.ch), Line: line, Column: column}
		}
	case '>':
		if l.peekChar() == '=' {
			ch := l.ch
			l.readChar()
			tok = Token{Type: TOKEN_GTE, Literal: string(ch) + string(l.ch), Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_GT, Literal: string(l.ch), Line: line, Column: column}
		}
	case '&':
		if l.peekChar() == '&' {
			ch := l.ch
			l.readChar()
			tok = Token{Type: TOKEN_AND, Literal: string(ch) + string(l.ch), Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_ILLEGAL, Literal: string(l.ch), Line: line, Column: column}
		}
	case '|':
		if l.peekChar() == '|' {
			ch := l.ch
			l.readChar()
			tok = Token{Type: TOKEN_OR, Literal: string(ch) + string(l.ch), Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_ILLEGAL, Literal: string(l.ch), Line: line, Column: column}
		}
	case '(':
		tok = Token{Type: TOKEN_LPAREN, Literal: string(l.ch), Line: line, Column: column}
	case ')':
		tok = Token{Type: TOKEN_RPAREN, Literal: string(l.ch), Line: line, Column: column}
	case '{':
		tok = Token{Type: TOKEN_LBRACE, Literal: string(l.ch), Line: line, Column: column}
	case '}':
		tok = Token{Type: TOKEN_RBRACE, Literal: string(l.ch), Line: line, Column: column}
	case '[':
		tok = Token{Type: TOKEN_LBRACKET, Literal: string(l.ch), Line: line, Column: column}
	case ']':
		tok = Token{Type: TOKEN_RBRACKET, Literal: string(l.ch), Line: line, Column: column}
	case ',':
		tok = Token{Type: TOKEN_COMMA, Literal: string(l.ch), Line: line, Column: column}
	case '.':
		tok = Token{Type: TOKEN_DOT, Literal: string(l.ch), Line: line, Column: column}
	case ':':
		tok = Token{Type: TOKEN_COLON, Literal: string(l.ch), Line: line, Column: column}
	case '?':
		tok = Token{Type: TOKEN_QUESTION, Literal: string(l.ch), Line: line, Column: column}
	case '"', '\'':
		// 处理字符串字面量
		literal := l.readString(l.ch)
		if literal == "UNTERMINATED_STRING" {
			// 未闭合的字符串，返回ILLEGAL token
			tok = Token{Type: TOKEN_ILLEGAL, Literal: "未闭合的字符串", Line: line, Column: column}
		} else {
			tok = Token{Type: TOKEN_STRING, Literal: literal, Line: line, Column: column}
		}
		return tok
	case 0:
		tok.Type = TOKEN_EOF
		tok.Literal = ""
		tok.Line = line
		tok.Column = column
	default:
		if isLetter(l.ch) {
			// 处理标识符
			tok.Literal = l.readIdentifier()
			tok.Type = LookupIdent(tok.Literal)
			tok.Line = line
			tok.Column = column
			return tok
		} else if isDigit(l.ch) {
			// 处理数字
			tok.Type = TOKEN_NUMBER
			tok.Literal = l.readNumber()
			tok.Line = line
			tok.Column = column
			return tok
		} else {
			tok = Token{Type: TOKEN_ILLEGAL, Literal: string(l.ch), Line: line, Column: column}
		}
	}

	l.readChar() // 移动到下一个字符
	return tok
}

// readChar 读取下一个字符
func (l *Lexer) readChar() {
	if l.readPosition >= len(l.input) {
		l.ch = 0
	} else {
		// 使用utf8支持Unicode
		r, width := utf8.DecodeRuneInString(l.input[l.readPosition:])
		l.ch = r

		// 如果是换行符，更新行列号
		if l.ch == '\n' {
			l.line++
			l.column = 0
		} else {
			l.column++
		}

		l.readPosition += width
	}
}

// peekChar 预读下一个字符，但不移动指针
func (l *Lexer) peekChar() rune {
	if l.readPosition >= len(l.input) {
		return 0
	}
	r, _ := utf8.DecodeRuneInString(l.input[l.readPosition:])
	return r
}

// skipWhitespace 跳过空白字符
func (l *Lexer) skipWhitespace() {
	for unicode.IsSpace(l.ch) {
		l.readChar()
	}
}

// readIdentifier 读取标识符
func (l *Lexer) readIdentifier() string {
	// 使用显式的字符构建，确保所有字符都被包含
	var sb strings.Builder
	for isLetter(l.ch) || isDigit(l.ch) {
		sb.WriteRune(l.ch)
		l.readChar()
	}

	return sb.String()
}

// readString 读取字符串
func (l *Lexer) readString(quote rune) string {
	// 跳过起始引号
	startLine := l.line
	startColumn := l.column
	l.readChar()

	// 记录字符串的实际内容
	var result strings.Builder

	for {
		// 当遇到结束引号或EOF时，结束字符串读取
		if l.ch == quote {
			// 找到结束引号，正常结束
			break
		}

		if l.ch == 0 {
			// 达到EOF但没有找到结束引号，这是一个未闭合的字符串
			// 设置当前token为ILLEGAL，返回到目前为止的内容
			l.column = startColumn
			l.line = startLine
			return "UNTERMINATED_STRING"
		}

		// 处理转义字符
		if l.ch == '\\' {
			if l.peekChar() == '\\' || l.peekChar() == quote || l.peekChar() == 'n' ||
				l.peekChar() == 't' || l.peekChar() == 'r' {
				l.readChar() // 跳过转义符

				// 根据不同的转义字符添加相应的字符
				switch l.ch {
				case '\\':
					result.WriteRune('\\')
				case quote:
					result.WriteRune(quote)
				case 'n':
					result.WriteRune('\n')
				case 't':
					result.WriteRune('\t')
				case 'r':
					result.WriteRune('\r')
				}
			} else {
				// 无效的转义序列，保留原字符
				result.WriteRune('\\')
			}
		} else {
			// 普通字符直接添加
			result.WriteRune(l.ch)
		}

		l.readChar()
	}

	// 消费结束的引号（如果不是因为EOF而结束的）
	if l.ch == quote {
		l.readChar()
	}

	return result.String()
}

// readNumber 读取数字
func (l *Lexer) readNumber() string {
	position := l.readPosition - 1 // 当前字符已经是数字，需要减1才是起始位置
	var endPos int = position

	// 读取整数部分
	for isDigit(l.ch) {
		endPos = l.readPosition
		l.readChar()
	}

	// 检查小数点
	if l.ch == '.' && isDigit(l.peekChar()) {
		endPos = l.readPosition
		l.readChar() // 消耗掉小数点

		// 读取小数部分
		for isDigit(l.ch) {
			endPos = l.readPosition
			l.readChar()
		}
	}

	// 检查科学计数法表示
	if l.ch == 'e' || l.ch == 'E' {
		nextChar := l.peekChar()
		if isDigit(nextChar) || nextChar == '+' || nextChar == '-' {
			endPos = l.readPosition
			l.readChar() // 消耗掉 'e' 或 'E'

			// 处理可能的符号
			if l.ch == '+' || l.ch == '-' {
				endPos = l.readPosition
				l.readChar()
			}

			// 读取指数部分
			hasDigit := false
			for isDigit(l.ch) {
				endPos = l.readPosition
				l.readChar()
				hasDigit = true
			}

			// 确保指数部分有至少一个数字
			if !hasDigit {
				// 如果没有数字，这是一个无效的科学计数法
				// 由于已经消耗了字符，无法简单返回错误，我们返回到此为止的有效数字部分
			}
		}
	}

	// 返回解析到的数字字符串
	return l.input[position:endPos]
}

// newToken 创建一个新的token
func newToken(tokenType TokenType, ch rune) Token {
	return Token{Type: tokenType, Literal: string(ch)}
}

// isLetter 判断是否为字母
func isLetter(ch rune) bool {
	result := unicode.IsLetter(ch) || ch == '_'
	return result
}

// isDigit 判断是否为数字
func isDigit(ch rune) bool {
	result := unicode.IsDigit(ch)
	return result
}
