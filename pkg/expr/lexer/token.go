package lexer

import "fmt"

// TokenType 表示词法单元的类型
type TokenType int

const (
	// 特殊token
	TOKEN_ILLEGAL TokenType = iota
	TOKEN_EOF

	// 标识符和字面量
	TOKEN_IDENT  // 标识符
	TOKEN_NUMBER // 数字
	TOKEN_STRING // 字符串
	TOKEN_BOOL   // 布尔值

	// 运算符
	TOKEN_PLUS     // +
	TOKEN_MINUS    // -
	TOKEN_MULTIPLY // *
	TOKEN_DIVIDE   // /
	TOKEN_MOD      // %

	// 比较运算符
	TOKEN_EQ  // ==
	TOKEN_NEQ // !=
	TOKEN_LT  // <
	TOKEN_GT  // >
	TOKEN_LTE // <=
	TOKEN_GTE // >=

	// 逻辑运算符
	TOKEN_AND // &&
	TOKEN_OR  // ||
	TOKEN_NOT // !

	// 分隔符
	TOKEN_LPAREN   // (
	TOKEN_RPAREN   // )
	TOKEN_LBRACE   // {
	TOKEN_RBRACE   // }
	TOKEN_LBRACKET // [
	TOKEN_RBRACKET // ]
	TOKEN_COMMA    // ,
	TOKEN_COLON    // :
	TOKEN_DOT      // .
	TOKEN_QUESTION // ?

	// 空值
	TOKEN_NULL // null
)

// Token 表示一个词法单元
type Token struct {
	Type    TokenType // token类型
	Literal string    // token字面值
	Line    int       // 行号
	Column  int       // 列号
}

// 用于调试输出的token类型字符串表示
var tokenNames = map[TokenType]string{
	TOKEN_ILLEGAL:  "ILLEGAL",
	TOKEN_EOF:      "EOF",
	TOKEN_IDENT:    "IDENT",
	TOKEN_NUMBER:   "NUMBER",
	TOKEN_STRING:   "STRING",
	TOKEN_BOOL:     "BOOL",
	TOKEN_PLUS:     "+",
	TOKEN_MINUS:    "-",
	TOKEN_MULTIPLY: "*",
	TOKEN_DIVIDE:   "/",
	TOKEN_MOD:      "%",
	TOKEN_EQ:       "==",
	TOKEN_NEQ:      "!=",
	TOKEN_LT:       "<",
	TOKEN_GT:       ">",
	TOKEN_LTE:      "<=",
	TOKEN_GTE:      ">=",
	TOKEN_AND:      "&&",
	TOKEN_OR:       "||",
	TOKEN_NOT:      "!",
	TOKEN_LPAREN:   "(",
	TOKEN_RPAREN:   ")",
	TOKEN_LBRACE:   "{",
	TOKEN_RBRACE:   "}",
	TOKEN_LBRACKET: "[",
	TOKEN_RBRACKET: "]",
	TOKEN_COMMA:    ",",
	TOKEN_COLON:    ":",
	TOKEN_DOT:      ".",
	TOKEN_QUESTION: "?",
	TOKEN_NULL:     "null",
}

// String 返回token的字符串表示
func (t Token) String() string {
	return fmt.Sprintf("Token{Type: %v, Literal: %q, Line: %d, Column: %d}",
		t.Type, t.Literal, t.Line, t.Column)
}

// String 返回TokenType的字符串表示
func (t TokenType) String() string {
	if name, ok := tokenNames[t]; ok {
		return name
	}
	return fmt.Sprintf("TokenType(%d)", int(t))
}

// 关键字映射
var keywords = map[string]TokenType{
	"true":  TOKEN_BOOL,
	"false": TOKEN_BOOL,
	"null":  TOKEN_NULL,
}

// LookupIdent 检查标识符是否是关键字
func LookupIdent(ident string) TokenType {
	if tok, ok := keywords[ident]; ok {
		return tok
	}
	return TOKEN_IDENT
}
