package lexer

import (
	"testing"
)

func TestNextToken(t *testing.T) {
	input := `!false`

	tests := []struct {
		expectedType    TokenType
		expectedLiteral string
	}{
		// {TOKEN_NUMBER, "1"},
		// {TOKEN_PLUS, "+"},
		// {TOKEN_NUMBER, "2"},
		// {TOKEN_MULTIPLY, "*"},
		// {TOKEN_LPAREN, "("},
		// {TOKEN_NUMBER, "3"},
		// {TOKEN_MINUS, "-"},
		// {TOKEN_NUMBER, "4"},
		// {TOKEN_RPAREN, ")"},
		// {TOKEN_DIVIDE, "/"},
		// {TOKEN_NUMBER, "5"},
		// {TOKEN_IDENT, "x"},
		// {TOKEN_EQ, "=="},
		// {TOKEN_IDENT, "y"},
		// {TOKEN_AND, "&&"},
		// {TOKEN_IDENT, "z"},
		// {TOKEN_NEQ, "!="},
		// {TOKEN_STRING, "hello"},
		// {TOKEN_IDENT, "age"},
		// {TOKEN_GTE, ">="},
		// {TOKEN_NUMBER, "18"},
		// {TOKEN_OR, "||"},
		// {TOKEN_IDENT, "score"},
		// {TOKEN_LTE, "<="},
		// {TOKEN_NUMBER, "60"},
		// {TOKEN_BOOL, "true"},
		// {TOKEN_AND, "&&"},
		{TOKEN_NOT, "!"},
		{TOKEN_BOOL, "false"},
		{TOKEN_EOF, ""},
	}

	l := New(input)

	for i, tt := range tests {
		tok := l.NextToken()

		if tok.Type != tt.expectedType {
			t.Fatalf("tests[%d] - tokentype wrong. expected=%q, got=%q",
				i, tokenNames[tt.expectedType], tokenNames[tok.Type])
		}

		if tok.Literal != tt.expectedLiteral {
			t.Fatalf("tests[%d] - literal wrong. expected=%q, got=%q",
				i, tt.expectedLiteral, tok.Literal)
		}
	}
}

func TestIllegalToken(t *testing.T) {
	input := "@#$"
	l := New(input)

	for _, ch := range input {
		tok := l.NextToken()
		if tok.Type != TOKEN_ILLEGAL {
			t.Errorf("expected illegal token for character %q, got %s", ch, tok.Type)
		}
	}
}

func TestLineAndColumn(t *testing.T) {
	input := "a + b\nc * d"
	l := New(input)

	tests := []struct {
		expectedLine   int
		expectedColumn int
	}{
		{1, 1}, // 'a'
		{1, 3}, // '+'
		{1, 5}, // 'b'
		{2, 1}, // 'c'
		{2, 3}, // '*'
		{2, 5}, // 'd'
	}

	for i, tt := range tests {
		tok := l.NextToken()
		if tok.Line != tt.expectedLine || tok.Column != tt.expectedColumn {
			t.Errorf("tests[%d] - position wrong. expected=(%d,%d), got=(%d,%d)",
				i, tt.expectedLine, tt.expectedColumn, tok.Line, tok.Column)
		}
	}
}

// TestTernaryOperator 测试三元运算符的词法分析
func TestTernaryOperator(t *testing.T) {
	input := `10 > 5 ? 'Greater' : 'Less'`

	tests := []struct {
		expectedType    TokenType
		expectedLiteral string
	}{
		{TOKEN_NUMBER, "10"},
		{TOKEN_GT, ">"},
		{TOKEN_NUMBER, "5"},
		{TOKEN_QUESTION, "?"},
		{TOKEN_STRING, "Greater"},
		{TOKEN_COLON, ":"},
		{TOKEN_STRING, "Less"},
		{TOKEN_EOF, ""},
	}

	l := New(input)

	for i, tt := range tests {
		tok := l.NextToken()

		if tok.Type != tt.expectedType {
			t.Fatalf("tests[%d] - tokentype wrong. expected=%q, got=%q",
				i, tokenNames[tt.expectedType], tokenNames[tok.Type])
		}

		if tok.Literal != tt.expectedLiteral {
			t.Fatalf("tests[%d] - literal wrong. expected=%q, got=%q",
				i, tt.expectedLiteral, tok.Literal)
		}
	}
}

// TestNestedTernaryOperator 测试嵌套的三元运算符的词法分析
func TestNestedTernaryOperator(t *testing.T) {
	input := `score >= 90 ? "A" : score >= 80 ? "B" : "C"`

	tests := []struct {
		expectedType    TokenType
		expectedLiteral string
	}{
		{TOKEN_IDENT, "score"},
		{TOKEN_GTE, ">="},
		{TOKEN_NUMBER, "90"},
		{TOKEN_QUESTION, "?"},
		{TOKEN_STRING, "A"},
		{TOKEN_COLON, ":"},
		{TOKEN_IDENT, "score"},
		{TOKEN_GTE, ">="},
		{TOKEN_NUMBER, "80"},
		{TOKEN_QUESTION, "?"},
		{TOKEN_STRING, "B"},
		{TOKEN_COLON, ":"},
		{TOKEN_STRING, "C"},
		{TOKEN_EOF, ""},
	}

	l := New(input)

	for i, tt := range tests {
		tok := l.NextToken()

		if tok.Type != tt.expectedType {
			t.Fatalf("tests[%d] - tokentype wrong. expected=%q, got=%q",
				i, tokenNames[tt.expectedType], tokenNames[tok.Type])
		}

		if tok.Literal != tt.expectedLiteral {
			t.Fatalf("tests[%d] - literal wrong. expected=%q, got=%q",
				i, tt.expectedLiteral, tok.Literal)
		}
	}
}

// TestComplexTernary 测试复杂表达式中的三元运算符
func TestComplexTernary(t *testing.T) {
	input := `user.age >= 18 && user.vip ? price * 0.8 : price`

	tests := []struct {
		expectedType    TokenType
		expectedLiteral string
	}{
		{TOKEN_IDENT, "user"},
		{TOKEN_DOT, "."},
		{TOKEN_IDENT, "age"},
		{TOKEN_GTE, ">="},
		{TOKEN_NUMBER, "18"},
		{TOKEN_AND, "&&"},
		{TOKEN_IDENT, "user"},
		{TOKEN_DOT, "."},
		{TOKEN_IDENT, "vip"},
		{TOKEN_QUESTION, "?"},
		{TOKEN_IDENT, "price"},
		{TOKEN_MULTIPLY, "*"},
		{TOKEN_NUMBER, "0.8"},
		{TOKEN_COLON, ":"},
		{TOKEN_IDENT, "price"},
		{TOKEN_EOF, ""},
	}

	l := New(input)

	for i, tt := range tests {
		tok := l.NextToken()

		if tok.Type != tt.expectedType {
			t.Fatalf("tests[%d] - tokentype wrong. expected=%q, got=%q",
				i, tokenNames[tt.expectedType], tokenNames[tok.Type])
		}

		if tok.Literal != tt.expectedLiteral {
			t.Fatalf("tests[%d] - literal wrong. expected=%q, got=%q",
				i, tt.expectedLiteral, tok.Literal)
		}
	}
}
