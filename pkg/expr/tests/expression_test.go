package tests

import (
	"slices"
	"testing"

	"admin/pkg/expr/ast"
	"admin/pkg/expr/builtin"
	"admin/pkg/expr/eval"
	"admin/pkg/expr/ext"
	"admin/pkg/expr/lexer"
	"admin/pkg/expr/optimizer"
	"admin/pkg/expr/parser"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 测试基本的算术表达式
func TestArithmeticExpressions(t *testing.T) {
	tests := []struct {
		name     string
		expr     string
		expected float64
	}{
		{"加法表达式", "2 + 3", 5},
		{"减法表达式", "5 - 3", 2},
		{"乘法表达式", "4 * 3", 12},
		{"除法表达式", "12 / 4", 3},
		{"取模表达式", "7 % 4", 3},
		{"含括号表达式", "(2 + 3) * 4", 20},
		{"含括号表达式2", "2 + (3 * 4)", 14},
		{"多重嵌套括号", "((2 + 3) * 2) + (10 / (5 - 3))", 15},
		{"含负数表达式", "-5 + 10", 5},
		{"复杂算术表达式", "2 * 3 + 4 * 5", 26},
		{"复杂算术表达式2", "2 * (3 + 4) * 5", 70},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, nil)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试逻辑表达式
func TestLogicalExpressions(t *testing.T) {
	tests := []struct {
		name     string
		expr     string
		expected bool
	}{
		{"与操作-true", "true && true", true},
		{"与操作-false", "true && false", false},
		{"或操作-true", "true || false", true},
		{"或操作-false", "false || false", false},
		{"非操作-true", "!false", true},
		{"非操作-false", "!true", false},
		{"复杂逻辑表达式", "true && (false || !false)", true},
		{"带括号逻辑表达式", "(true && false) || true", true},
		{"带括号逻辑表达式2", "!(true && false) && true", true},
		{"多层嵌套", "!(false || (true && false))", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, nil)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试比较表达式
func TestComparisonExpressions(t *testing.T) {
	tests := []struct {
		name     string
		expr     string
		expected bool
	}{
		{"相等性测试-true", "5 == 5", true},
		{"相等性测试-false", "5 == 6", false},
		{"不等性测试-true", "5 != 6", true},
		{"不等性测试-false", "5 != 5", false},
		{"大于测试-true", "6 > 5", true},
		{"大于测试-false", "5 > 6", false},
		{"小于测试-true", "5 < 6", true},
		{"小于测试-false", "6 < 5", false},
		{"大于等于测试-true", "6 >= 6", true},
		{"大于等于测试-false", "5 >= 6", false},
		{"小于等于测试-true", "5 <= 5", true},
		{"小于等于测试-false", "6 <= 5", false},
		{"复杂比较表达式", "(5 > 3) && (10 <= 10)", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, nil)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试字符串操作
func TestStringOperations(t *testing.T) {
	tests := []struct {
		name     string
		expr     string
		expected interface{} // 将类型从string改为interface{}
	}{
		{"字符串连接", "'Hello' + ' ' + 'World'", "Hello World"},
		{"字符串与数字连接", "'Count: ' + 5", "Count: 5"},
		{"两个字符串比较", "'abc' == 'abc'", true},
		{"两个字符串比较2", "'abc' != 'def'", true},
		{"字符串排序比较", "'abc' < 'def'", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, nil)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试变量使用
func TestVariables(t *testing.T) {
	variables := map[string]interface{}{
		"x":    float64(10),
		"y":    float64(20),
		"z":    "测试字符串",
		"flag": true,
	}

	tests := []struct {
		name     string
		expr     string
		expected interface{}
	}{
		{"数字变量", "x + y", float64(30)},
		{"字符串变量", "z + '!'", "测试字符串!"},
		{"布尔变量", "flag && true", true},
		{"变量与常量混合", "x * 2 + y", float64(40)},
		{"字符串与数字变量", "'结果: ' + (x + y)", "结果: 30"},
		{"条件表达式", "x > 5 && y < 30", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, variables)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试函数调用
func TestFunctionCalls(t *testing.T) {
	// 确保内置函数已注册
	variables := map[string]interface{}{
		"nums": []any{1.0, 2.0, 3.0, 4.0, 5.0},
		"name": "张三",
		"age":  float64(30),
	}

	tests := []struct {
		name     string
		expr     string
		expected any
	}{
		{"min函数", "min(10, 5, 20)", float64(5)},
		{"max函数", "max(10, 5, 20)", float64(20)},
		{"abs函数", "abs(-15)", float64(15)},
		{"round函数", "round(3.14159, 2)", float64(3.14)},
		{"length函数", "length('Hello')", float64(5)},
		{"upper函数", "upper('hello')", "HELLO"},
		{"lower函数", "lower('HELLO')", "hello"},
		{"concat函数", "concat('Hello', ' ', 'World')", "Hello World"},
		{"if函数", "if(10 > 5, '大于', '小于')", "大于"},
		{"if函数2", "if(false, 'true分支', 'false分支')", "false分支"},
		{"嵌套函数调用", "upper(concat('hello', ' ', 'world'))", "HELLO WORLD"},
		{"函数与表达式", "min(10, 5) * max(2, 3)", float64(15)},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, variables)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试数组和对象操作
func TestArrayAndObjectOperations(t *testing.T) {
	variables := map[string]any{
		"scores": []any{60.0, 70.0, 80.0, 90.0, 100.0},
		"user": map[string]any{
			"name": "张三",
			"age":  float64(30),
			"profile": map[string]any{
				"city": "北京",
				"job":  "工程师",
			},
		},
	}

	tests := []struct {
		name     string
		expr     string
		expected interface{}
	}{
		{"数组字面量", "[1, 2, 3, 4, 5]", []interface{}{float64(1), float64(2), float64(3), float64(4), float64(5)}},
		{"数组索引访问", "scores[2]", float64(80)},
		{"数组求和", "scores[0] + scores[4]", float64(160)},
		{"对象字面量", "{name: '李四', age: 25}", map[string]interface{}{"name": "李四", "age": float64(25)}},
		{name: "对象属性访问", expr: "user.name", expected: "张三"},
		{"对象嵌套属性", "user.profile.city", "北京"},
		{
			"数组与对象混合", "{users: ['张三', '李四', '王五']}",
			map[string]any{"users": []any{"张三", "李四", "王五"}},
		},
		{"对象方括号访问", "user['name']", "张三"},
		{"数组对象混合", "scores[user.age / 10 - 1]", float64(80)},

		// 新增数组处理函数测试
		{"数组includes函数", "includes(scores, 80)", true},
		{"数组includes函数(不存在)", "includes(scores, 85)", false},
		{"数组indexOf函数", "indexOf(scores, 90)", float64(3)},
		{"数组indexOf函数(不存在)", "indexOf(scores, 85)", float64(-1)},
		{"数组join函数", "join(scores, '-')", "60-70-80-90-100"},
		{"数组slice函数", "slice(scores, 1, 3)", []interface{}{float64(70), float64(80)}},
		{"数组slice负索引", "slice(scores, -2)", []interface{}{float64(90), float64(100)}},
		{"数组length函数", "length(scores)", float64(5)},
		{"数组sort函数", "sort([3, 1, 4, 2])", []interface{}{float64(1), float64(2), float64(3), float64(4)}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := eval.EvaluateWithCache(tt.expr, variables)
			require.NoError(t, err, "解析表达式应该成功")
			assert.Equal(t, tt.expected, result, "表达式计算结果应该一致")
		})
	}
}

// 测试数组高级函数
func TestArrayHigherOrderFunctions(t *testing.T) {
	// variables := map[string]interface{}{
	// 	"nums": []interface{}{1.0, 2.0, 3.0, 4.0, 5.0},
	// 	"persons": []interface{}{
	// 		map[string]interface{}{"name": "张三", "age": float64(20)},
	// 		map[string]interface{}{"name": "李四", "age": float64(30)},
	// 		map[string]interface{}{"name": "王五", "age": float64(40)},
	// 	},
	// 	"mixed": []interface{}{0.0, "", false, nil, "test", 123.0, true},
	// }

	// 这些测试用例可能依赖于高级函数的具体实现
	// 对于filter, map, some, every等需要表达式参数的函数
	// 可能需要调整测试方式或实现更复杂的表达式求值机制

	// TODO: 为 filter, map, some, every 等高级函数编写更完整测试
	// 当特定实现完成后添加测试用例
}

// 测试错误处理
func TestErrorHandling(t *testing.T) {
	tests := []struct {
		name        string
		expr        string
		errContains string
	}{
		{"除以零错误", "10 / 0", "除数不能为零"},
		{"语法错误", "10 +* 5", "无法解析表达式"},
		{"未定义变量", "x + 10", "未定义变量"},
		{"类型错误", "'abc' - 5", "类型错误"},
		{"括号不匹配", "(10 + 5", "括号不匹配"},
		{"函数不存在", "notExistFunc(10)", "不是一个函数"},
		{"函数参数不匹配", "min()", "至少需要1个参数"},
		{"下标越界", "[1, 2, 3][5]", "索引越界"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := eval.EvaluateWithCache(tt.expr, nil)
			assert.Error(t, err, "应该返回错误")
			assert.Contains(t, err.Error(), tt.errContains, "错误信息应包含预期内容")
		})
	}
}

// 测试优化功能
func TestOptimization(t *testing.T) {
	// 常量折叠测试
	l := lexer.New("2 + 3 * 4")
	p := parser.New(l)
	expr, err := p.ParseExpression()
	require.NoError(t, err, "解析表达式应该成功")

	// 优化前
	result1, err := eval.EvaluateExpression(expr, nil)
	require.NoError(t, err)
	assert.Equal(t, float64(14), result1)

	// 进行优化
	optimizedExpr := optimizer.OptimizeExpression(expr)

	// 优化后，查看是否被折叠为单个常量
	numLit, ok := optimizedExpr.(*ast.NumberLiteral)
	if assert.True(t, ok, "优化后应转换为数字字面量") {
		assert.Equal(t, float64(14), numLit.Value)
	}
}

// TestConstantFolding 测试不同类型的常量折叠
func TestConstantFolding(t *testing.T) {
	testCases := []struct {
		expr     string
		expected interface{}
	}{
		// 算术表达式折叠
		{"1 + 2 + 3", float64(6)},
		{"(5 - 2) * 3", float64(9)},
		{"10 / 2", float64(5)},
		{"2 * (3 + 4)", float64(14)},
		{"10 % 3", float64(1)},

		// 字符串折叠
		{`"hello" + " " + "world"`, "hello world"},

		// 布尔表达式折叠
		{"true && false", false},
		{"true || false", true},
		{"!false", true},
		{"!(true && false)", true},

		// 比较表达式折叠
		{"5 > 3", true},
		{"10 == 10", true},
		{"2 != 2", false},
		{"5 >= 5", true},
	}

	for _, tc := range testCases {
		t.Run(tc.expr, func(t *testing.T) {
			// 解析并优化表达式
			expr, err := optimizer.CompileAndOptimize(tc.expr)
			require.NoError(t, err)

			// 检查是否折叠为单个常量
			switch expected := tc.expected.(type) {
			case float64:
				numLit, ok := expr.(*ast.NumberLiteral)
				if assert.True(t, ok, "应该优化为数字字面量") {
					assert.Equal(t, expected, numLit.Value)
				}
			case string:
				strLit, ok := expr.(*ast.StringLiteral)
				if assert.True(t, ok, "应该优化为字符串字面量") {
					assert.Equal(t, expected, strLit.Value)
				}
			case bool:
				boolLit, ok := expr.(*ast.BooleanLiteral)
				if assert.True(t, ok, "应该优化为布尔字面量") {
					assert.Equal(t, expected, boolLit.Value)
				}
			}
		})
	}
}

// TestAlgebraicSimplification 测试代数简化
func TestAlgebraicSimplification(t *testing.T) {
	vars := map[string]interface{}{"x": float64(5)}

	testCases := []struct {
		name           string
		expr           string
		expectedExpr   string
		needsEval      bool
		expectedResult interface{}
	}{
		{"x + 0 = x", "x + 0", "x", true, float64(5)},
		{"0 + x = x", "0 + x", "x", true, float64(5)},
		{"x - 0 = x", "x - 0", "x", true, float64(5)},
		{"x * 1 = x", "x * 1", "x", true, float64(5)},
		{"1 * x = x", "1 * x", "x", true, float64(5)},
		{"x * 0 = 0", "x * 0", "0", false, float64(0)},
		{"0 * x = 0", "0 * x", "0", false, float64(0)},
		{"x / 1 = x", "x / 1", "x", true, float64(5)},
		{"x - x = 0", "x - x", "0", false, float64(0)},
		{"x / x = 1", "x / x", "1", false, float64(1)},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建优化器配置
			config := optimizer.DefaultConfig()

			// 解析表达式
			l := lexer.New(tc.expr)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			require.NoError(t, err)

			// 创建评估适配器和优化器实例
			evaluator := optimizer.NewEvaluatorAdapter().WithVariables(vars)
			opt := optimizer.New(evaluator)

			// 优化表达式
			optimized := opt.Optimize(expr, vars, config)

			if !tc.needsEval {
				// 检查是否简化为预期的字面量
				switch val := tc.expectedResult.(type) {
				case float64:
					if val == 0 {
						numLit, ok := optimized.(*ast.NumberLiteral)
						if assert.True(t, ok, "应简化为数字字面量") {
							assert.Equal(t, val, numLit.Value, "应简化为0")
						}
					} else if val == 1 {
						numLit, ok := optimized.(*ast.NumberLiteral)
						if assert.True(t, ok, "应简化为数字字面量") {
							assert.Equal(t, val, numLit.Value, "应简化为1")
						}
					}
				case bool:
					boolLit, ok := optimized.(*ast.BooleanLiteral)
					if assert.True(t, ok, "应简化为布尔字面量") {
						assert.Equal(t, val, boolLit.Value)
					}
				}
			} else {
				// 执行并验证结果
				result, err := eval.EvaluateExpression(optimized, vars)
				require.NoError(t, err)
				assert.Equal(t, tc.expectedResult, result)
			}
		})
	}
}

// TestShortCircuitOptimization 测试短路优化
func TestShortCircuitOptimization(t *testing.T) {
	vars := map[string]interface{}{"x": true, "y": false}

	testCases := []struct {
		name         string
		expr         string
		expectedExpr string
		expected     bool
	}{
		{"true && x = x", "true && x", "x", true},
		{"x && true = x", "x && true", "x", true},
		{"false && x = false", "false && x", "false", false},
		{"x && false = false", "x && false", "false", false},
		{"true || x = true", "true || x", "true", true},
		{"x || true = true", "x || true", "true", true},
		{"false || x = x", "false || x", "x", true},
		{"x || false = x", "x || false", "x", true},
		{"x && !x = false", "x && !x", "false", false},
		{"x || !x = true", "x || !x", "true", true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建优化器配置和规则
			evaluator := optimizer.NewEvaluatorAdapter().WithVariables(vars)
			config := optimizer.DefaultConfig()
			opt := optimizer.New(evaluator)

			// 解析表达式
			l := lexer.New(tc.expr)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			require.NoError(t, err)

			// 优化表达式
			optimized := opt.Optimize(expr, vars, config)

			// 评估结果
			result, err := eval.EvaluateExpression(optimized, vars)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, result)

			// 检查特定的字面量优化情况
			if tc.expectedExpr == "true" || tc.expectedExpr == "false" {
				boolLit, ok := optimized.(*ast.BooleanLiteral)
				if assert.True(t, ok, "应优化为布尔字面量") {
					assert.Equal(t, tc.expected, boolLit.Value)
				}
			}
		})
	}
}

// TestTernaryOperator 测试三元运算符
func TestTernaryOperator(t *testing.T) {
	testCases := []struct {
		expr           string
		variables      map[string]any
		expected       any
		shouldOptimize bool
	}{
		// // 常量条件优化
		{"true ? 1 : 2", nil, float64(1), true},
		{"false ? 1 : 2", nil, float64(2), true},
		// 常量分支优化
		{"x ? 5 : 5", map[string]any{"x": true}, float64(5), true},
		// 简化为逻辑表达式
		{"x ? true : false", map[string]any{"x": true}, true, true},
		{"x ? false : true", map[string]any{"x": true}, false, true},
		// 一般的三元表达式
		{"x > 10 ? 'big' : 'small'", map[string]any{"x": float64(5)}, "small", false},
	}

	for i, tc := range testCases {
		t.Run(tc.expr, func(t *testing.T) {
			// 创建优化器和配置
			config := optimizer.DefaultConfig()
			evaluator := optimizer.NewEvaluatorAdapter().WithVariables(tc.variables)
			opt := optimizer.New(evaluator)

			// 解析表达式
			l := lexer.New(tc.expr)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			require.NoError(t, err, "用例 %d: 解析表达式应该成功", i+1)

			// 优化表达式
			optimized := opt.Optimize(expr, tc.variables, config)

			// 验证优化结果
			if tc.shouldOptimize {
				switch tc.expected.(type) {
				case float64:
					_, ok := optimized.(*ast.NumberLiteral)
					assert.True(t, ok, "用例 %d: 应优化为数字字面量", i+1)
				case string:
					_, ok := optimized.(*ast.StringLiteral)
					assert.True(t, ok, "用例 %d: 应优化为字符串字面量", i+1)
				case bool:
					_, ok := optimized.(*ast.BooleanLiteral)
					assert.True(t, ok, "用例 %d: 应优化为布尔字面量", i+1)
				}
			}

			// 检查执行结果
			result, err := eval.EvaluateExpression(optimized, tc.variables)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestCustomFunctions 测试自定义函数及其优化
func TestCustomFunctions(t *testing.T) {
	// 创建一个自定义函数集
	vars := map[string]interface{}{
		"double": func(x float64) float64 { return x * 2 },
		"sum": func(args ...float64) float64 {
			var total float64
			for _, v := range args {
				total += v
			}
			return total
		},
		"x": 5,
	}

	testCases := []struct {
		expr     string
		expected float64
	}{
		{"min(1, 2, 3, 4)", 1},
		{"max(6,7)", 7},
	}

	for _, tc := range testCases {
		t.Run(tc.expr, func(t *testing.T) {
			// 解析表达式
			l := lexer.New(tc.expr)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			require.NoError(t, err)

			// 作为对比，先评估未优化的表达式
			unoptimizedResult, err := eval.EvaluateExpression(expr, vars)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, unoptimizedResult)

			// 创建优化器和配置
			evaluator := optimizer.NewEvaluatorAdapter().WithVariables(vars)
			opt := optimizer.New(evaluator)
			optimized := opt.Optimize(expr, vars, optimizer.DefaultConfig())

			// 评估优化后的表达式
			optimizedResult, err := eval.EvaluateExpression(optimized, vars)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, optimizedResult)
		})
	}
}

// TestMixedComplexExpressions 测试混合复杂表达式的优化
func TestMixedComplexExpressions(t *testing.T) {
	vars := map[string]interface{}{
		"x": float64(10),
		"y": float64(5),
		"z": true,
		"w": false,
	}

	testCases := []struct {
		expr     string
		expected interface{}
	}{
		// 复杂的数学和逻辑混合表达式
		{"x > 5 && (y * 2 == 10 || !z) ? x + y : x - y", float64(15)},
		{"(x > y ? x : y) + (z ? 1 : 0)", float64(11)},
		{"(x + y) * (z ? 2 : 3)", float64(30)},
		{"w ? x : (z ? y : x / 2)", float64(5)},
		{"x > y && y > 0 && z", true},
	}

	for _, tc := range testCases {
		t.Run(tc.expr, func(t *testing.T) {
			// 解析表达式
			l := lexer.New(tc.expr)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			require.NoError(t, err)

			// 创建优化器
			evaluator := optimizer.NewEvaluatorAdapter().WithVariables(vars)
			config := optimizer.DefaultConfig()
			opt := optimizer.New(evaluator)

			// 优化表达式
			optimized := opt.Optimize(expr, vars, config)

			// 验证优化后的结果
			result, err := eval.EvaluateExpression(optimized, vars)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestErrorRecoveryAndDiagnostics 测试错误恢复与诊断功能
func TestErrorRecoveryAndDiagnostics(t *testing.T) {
	// 这个测试验证当表达式有错误时，能够提供有用的诊断信息
	errorCases := []struct {
		expr    string
		errType string
	}{
		{"x + ", "语法错误"},
		{"(x + y", "括号不匹配"},
		{"foo()", "未定义函数"},
		{"1 / (2 - 2)", "除以零"},
		{"'unterminated", "字符串未闭合"},
	}

	for _, tc := range errorCases {
		t.Run(tc.expr, func(t *testing.T) {
			l := lexer.New(tc.expr)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			if err != nil {
				// 如果有解析错误，检查错误类型
				t.Logf("解析错误: %v", err)
				return
			}

			// 如果解析成功但预期运行时错误
			_, err = eval.EvaluateExpression(expr, nil)
			assert.Error(t, err, "应该出现错误")
			t.Logf("运行时错误: %v", err)
		})
	}
}

// TestExpressionSystemIntegration 测试表达式与已有系统的集成
func TestExpressionSystemIntegration(t *testing.T) {
	// 模拟与系统集成的例子
	type User struct {
		ID       int
		Name     string
		Age      int
		IsActive bool
		Roles    []string
		Metadata map[string]interface{}
	}

	// 转换为表达式引擎可以使用的变量映射
	user := User{
		ID:       1001,
		Name:     "Zhang San",
		Age:      30,
		IsActive: true,
		Roles:    []string{"admin", "manager"},
		Metadata: map[string]interface{}{
			"department": "IT",
			"location":   "Beijing",
			"score":      95.5,
		},
	}

	// 创建hasPermission函数并注册到表达式引擎中
	hasPermissionFunc := func(args []interface{}) (interface{}, bool) {
		if len(args) != 1 {
			return false, true
		}
		roleToCheck, ok := args[0].(string)
		if !ok {
			return false, true
		}
		return slices.Contains(user.Roles, roleToCheck), true
	}

	// 注册hasPermission函数到表达式引擎
	hasPermissionFunction := builtin.NewFunction("hasPermission", func(args []interface{}) (interface{}, error) {
		result, _ := hasPermissionFunc(args)
		return result, nil
	}, 1, 1)
	err := ext.RegisterFunction(hasPermissionFunction)
	require.NoError(t, err, "注册hasPermission函数应该成功")
	// 在测试结束时记得清理
	defer ext.UnregisterFunction("hasPermission")

	vars := map[string]interface{}{
		"user": map[string]interface{}{
			"id":       float64(user.ID),
			"name":     user.Name,
			"age":      float64(user.Age),
			"isActive": user.IsActive,
			"roles":    user.Roles,
			"metadata": user.Metadata,
		},
	}

	testCases := []struct {
		desc     string
		expr     string
		expected interface{}
	}{
		{
			"检查用户权限",
			"user.isActive && hasPermission('admin')",
			true,
		},
		{
			"计算得分阈值",
			"user.metadata.score > 90 ? '优秀' : (user.metadata.score > 60 ? '合格' : '不合格')",
			"优秀",
		},
		{
			"根据年龄和部门决定优先级",
			"user.age > 25 && user.metadata.department == 'IT' ? 'high' : 'normal'",
			"high",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			// 解析表达式
			expr, err := optimizer.CompileAndOptimizeWithVariables(tc.expr, vars)
			require.NoError(t, err)

			// 执行表达式
			result, err := eval.EvaluateExpression(expr, vars)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, result)
		})
	}
}
