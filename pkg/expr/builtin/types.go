// 内置类型定义和类型转换逻辑
package builtin

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

// ValueType 表示值的类型
type ValueType int

const (
	// TypeUnknown 未知类型
	TypeUnknown ValueType = iota
	// TypeNull 空值类型
	TypeNull
	// TypeNumber 数字类型
	TypeNumber
	// TypeString 字符串类型
	TypeString
	// TypeBoolean 布尔类型
	TypeBoolean
	// TypeObject 对象类型
	TypeObject
	// TypeArray 数组类型
	TypeArray
	// TypeFunction 函数类型
	TypeFunction
)

// String 返回类型的字符串表示
func (t ValueType) String() string {
	switch t {
	case TypeNull:
		return "null"
	case TypeNumber:
		return "number"
	case TypeString:
		return "string"
	case TypeBoolean:
		return "boolean"
	case TypeObject:
		return "object"
	case TypeArray:
		return "array"
	case TypeFunction:
		return "function"
	default:
		return "unknown"
	}
}

// GetValueType 获取值的类型
func GetValueType(value interface{}) ValueType {
	if value == nil {
		return TypeNull
	}

	switch value.(type) {
	case float64, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		return TypeNumber
	case string:
		return TypeString
	case bool:
		return TypeBoolean
	case []interface{}, []string, []float64, []int:
		return TypeArray
	case func([]interface{}) (interface{}, error):
		return TypeFunction
	default:
		if reflect.TypeOf(value).Kind() == reflect.Map {
			return TypeObject
		}
		if reflect.TypeOf(value).Kind() == reflect.Slice {
			return TypeArray
		}
		if reflect.TypeOf(value).Kind() == reflect.Func {
			return TypeFunction
		}
		return TypeObject
	}
}

// ConvertToType 尝试将值转换为指定类型
func ConvertToType(value interface{}, targetType ValueType) (interface{}, error) {
	// 如果已经是目标类型，直接返回
	currentType := GetValueType(value)
	if currentType == targetType {
		return value, nil
	}

	// null值处理
	if value == nil {
		switch targetType {
		case TypeNull:
			return nil, nil
		case TypeString:
			return "", nil
		case TypeNumber:
			return 0.0, nil
		case TypeBoolean:
			return false, nil
		default:
			return nil, fmt.Errorf("无法将null转换为%s", targetType)
		}
	}

	// 根据目标类型进行转换
	switch targetType {
	case TypeNumber:
		return convertToNumber(value)
	case TypeString:
		return convertToString(value)
	case TypeBoolean:
		return convertToBoolean(value)
	default:
		return nil, fmt.Errorf("不支持的类型转换: %s -> %s", currentType, targetType)
	}
}

// convertToNumber 将值转换为数字
func convertToNumber(value interface{}) (interface{}, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case int:
		return float64(v), nil
	case int8:
		return float64(v), nil
	case int16:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint8:
		return float64(v), nil
	case uint16:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		if num, err := strconv.ParseFloat(v, 64); err == nil {
			return num, nil
		}
		return nil, fmt.Errorf("无法将字符串'%s'转换为数字", v)
	case bool:
		if v {
			return 1.0, nil
		}
		return 0.0, nil
	default:
		return nil, fmt.Errorf("无法将%T转换为数字", value)
	}
}

// convertToString 将值转换为字符串
func convertToString(value interface{}) (interface{}, error) {
	switch v := value.(type) {
	case string:
		return v, nil
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64), nil
	case int:
		return strconv.Itoa(v), nil
	case int64:
		return strconv.FormatInt(v, 10), nil
	case uint:
		return strconv.FormatUint(uint64(v), 10), nil
	case bool:
		return strconv.FormatBool(v), nil
	default:
		return fmt.Sprintf("%v", value), nil
	}
}

// convertToBoolean 将值转换为布尔值
func convertToBoolean(value interface{}) (interface{}, error) {
	switch v := value.(type) {
	case bool:
		return v, nil
	case int:
		return v != 0, nil
	case float64:
		return v != 0, nil
	case string:
		if v == "" {
			return false, nil
		}
		if strings.ToLower(v) == "false" || v == "0" {
			return false, nil
		}
		if strings.ToLower(v) == "true" || v == "1" {
			return true, nil
		}
		return true, nil
	default:
		return value != nil, nil
	}
}
