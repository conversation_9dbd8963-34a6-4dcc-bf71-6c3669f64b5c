
package builtin

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSimpleFunction 测试简单函数的基本功能
func TestSimpleFunction(t *testing.T) {
	fn := NewFunction("test", func(args []any) (any, error) {
		return "ok", nil
	}, 0, 0)

	assert.Equal(t, "test", fn.Name())
	min, max := fn.ArgCount()
	assert.Equal(t, 0, min)
	assert.Equal(t, 0, max)

	result, err := fn.Call(nil)
	require.NoError(t, err)
	assert.Equal(t, "ok", result)
}

// TestFunctionArgumentValidation 测试函数参数数量验证
func TestFunctionArgumentValidation(t *testing.T) {
	// 测试参数数量不足
	fnMin := NewFunction("minArgs", func(args []any) (any, error) { return nil, nil }, 1, -1)
	_, err := fnMin.Call(nil)
	assert.Error(t, err)

	// 测试参数数量过多
	fnMax := NewFunction("maxArgs", func(args []any) (any, error) { return nil, nil }, 0, 1)
	_, err = fnMax.Call([]any{1, 2})
	assert.Error(t, err)
}

// TestMathFunctions 测试数学函数
func TestMathFunctions(t *testing.T) {
	// Min
	result, err := Min.Call([]any{5.0, 2.0, 8.0})
	require.NoError(t, err)
	assert.Equal(t, 2.0, result)

	// Max
	result, err = Max.Call([]any{5.0, 2.0, 8.0})
	require.NoError(t, err)
	assert.Equal(t, 8.0, result)

	// Abs
	result, err = Abs.Call([]any{-10.0})
	require.NoError(t, err)
	assert.Equal(t, 10.0, result)

	// Round
	result, err = Round.Call([]any{3.14159, 2.0})
	require.NoError(t, err)
	assert.Equal(t, 3.14, result)
}

// TestStringFunctions 测试字符串函数
func TestStringFunctions(t *testing.T) {
	// Length
	result, err := Length.Call([]any{"hello"})
	require.NoError(t, err)
	assert.Equal(t, float64(5), result)

	// Upper
	result, err = Upper.Call([]any{"hello"})
	require.NoError(t, err)
	assert.Equal(t, "HELLO", result)

	// Lower
	result, err = Lower.Call([]any{"WORLD"})
	require.NoError(t, err)
	assert.Equal(t, "world", result)

	// Concat
	result, err = Concat.Call([]any{"a", "b", "c"})
	require.NoError(t, err)
	assert.Equal(t, "abc", result)
}

// TestTypeConversionFunctions 测试类型转换函数
func TestTypeConversionFunctions(t *testing.T) {
	// ToString
	result, err := ToString.Call([]any{123.0})
	require.NoError(t, err)
	assert.Equal(t, "123", result)

	// ToNumber
	result, err = ToNumber.Call([]any{"456"})
	require.NoError(t, err)
	assert.Equal(t, 456.0, result)

	// ToBoolean
	result, err = ToBoolean.Call([]any{"true"})
	require.NoError(t, err)
	assert.Equal(t, true, result)
}

// TestLogicalFunctions 测试逻辑函数
func TestLogicalFunctions(t *testing.T) {
	// If
	result, err := If.Call([]any{true, "yes", "no"})
	require.NoError(t, err)
	assert.Equal(t, "yes", result)

	result, err = If.Call([]any{false, "yes", "no"})
	require.NoError(t, err)
	assert.Equal(t, "no", result)
}

// TestArrayFunctions 测试数组函数
func TestArrayFunctions(t *testing.T) {
	arr := []any{1.0, 2.0, 3.0, 2.0}

	// Includes
	result, err := Includes.Call([]any{arr, 2.0})
	require.NoError(t, err)
	assert.Equal(t, true, result)

	// IndexOf
	result, err = IndexOf.Call([]any{arr, 2.0})
	require.NoError(t, err)
	assert.Equal(t, float64(1), result)

	// Join
	result, err = Join.Call([]any{arr, "-"})
	require.NoError(t, err)
	assert.Equal(t, "1-2-3-2", result)

	// Slice
	result, err = Slice.Call([]any{arr, 1.0, 3.0})
	require.NoError(t, err)
	assert.Equal(t, []any{2.0, 3.0}, result)

	// Sort
	unsortedArr := []any{3.0, 1.0, 2.0}
	result, err = Sort.Call([]any{unsortedArr})
	require.NoError(t, err)
	assert.Equal(t, []any{1.0, 2.0, 3.0}, result)
}

// TestRegistry 测试注册表功能
func TestRegistry(t *testing.T) {
	registry := NewRegistry()
	fn := NewFunction("myFunc", func(args []any) (any, error) { return "ok", nil }, 0, 0)

	// Register
	err := registry.Register(fn)
	require.NoError(t, err)

	// Get
	retrievedFn, ok := registry.Get("myFunc")
	assert.True(t, ok)
	assert.Equal(t, "myFunc", retrievedFn.Name())

	// Register duplicate
	err = registry.Register(fn)
	assert.Error(t, err)

	// Unregister
	unregistered := registry.Unregister("myFunc")
	assert.True(t, unregistered)
	_, ok = registry.Get("myFunc")
	assert.False(t, ok)

	// Unregister non-existent
	unregistered = registry.Unregister("nonExistent")
	assert.False(t, unregistered)
}

// TestDefaultRegistry 测试默认注册表
func TestDefaultRegistry(t *testing.T) {
	// Ensure default registry is initialized
	require.NotNil(t, DefaultRegistry)
	initialCount := len(DefaultRegistry.GetAll())
	assert.Greater(t, initialCount, 0)

	// Use convenience functions
	fn := NewFunction("globalFunc", func(args []any) (any, error) { return "ok", nil }, 0, 0)
	err := RegisterFunction(fn)
	require.NoError(t, err)

	_, ok := GetFunction("globalFunc")
	assert.True(t, ok)

	unregistered := UnregisterFunction("globalFunc")
	assert.True(t, unregistered)
	_, ok = GetFunction("globalFunc")
	assert.False(t, ok)
}
