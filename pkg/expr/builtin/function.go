// 内置函数定义
package builtin

import (
	"fmt"
	"math"
	"reflect"
	"strings"
)

// Function 表示可在表达式中调用的函数
type Function interface {
	// Name 返回函数名称
	Name() string

	// Call 执行函数调用
	Call(args []any) (interface{}, error)

	// ArgCount 返回参数个数
	ArgCount() (min int, max int)
}

// SimpleFunction 简单函数实现
type SimpleFunction struct {
	name    string
	handler func(args []any) (any, error)
	minArgs int
	maxArgs int
}

// NewFunction 创建一个新的函数
func NewFunction(
	name string,
	handler func(args []interface{}) (interface{}, error),
	minArgs int,
	maxArgs int,
) Function {
	return &SimpleFunction{
		name:    name,
		handler: handler,
		minArgs: minArgs,
		maxArgs: maxArgs,
	}
}

// Name 返回函数名称
func (f *SimpleFunction) Name() string {
	return f.name
}

// Call 执行函数调用
func (f *SimpleFunction) Call(args []interface{}) (interface{}, error) {
	// 检查参数数量
	argCount := len(args)
	if argCount < f.minArgs {
		return nil, fmt.Errorf("函数 %s 需要至少 %d 个参数，但只提供了 %d 个", f.name, f.minArgs, argCount)
	}

	if f.maxArgs >= 0 && argCount > f.maxArgs {
		return nil, fmt.Errorf("函数 %s 最多接受 %d 个参数，但提供了 %d 个", f.name, f.maxArgs, argCount)
	}

	// 调用处理函数
	return f.handler(args)
}

// ArgCount 返回参数个数
func (f *SimpleFunction) ArgCount() (min int, max int) {
	return f.minArgs, f.maxArgs
}

// 常用的数学函数
var (
	// Min 求多个数值的最小值
	Min = NewFunction(
		"min",
		func(args []interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, fmt.Errorf("min函数至少需要1个参数")
			}

			var min float64
			isFirst := true

			for _, arg := range args {
				num, err := convertToNumber(arg)
				if err != nil {
					return nil, err
				}

				val := num.(float64)
				if isFirst || val < min {
					min = val
					isFirst = false
				}
			}

			return min, nil
		},
		1, -1, // 至少1个参数，无上限
	)

	// Max 求多个数值的最大值
	Max = NewFunction(
		"max",
		func(args []interface{}) (interface{}, error) {
			if len(args) == 0 {
				return nil, fmt.Errorf("max函数至少需要1个参数")
			}

			var max float64
			isFirst := true

			for _, arg := range args {
				num, err := convertToNumber(arg)
				if err != nil {
					return nil, err
				}

				val := num.(float64)
				if isFirst || val > max {
					max = val
					isFirst = false
				}
			}

			return max, nil
		},
		1, -1, // 至少1个参数，无上限
	)

	// Abs 求绝对值
	Abs = NewFunction(
		"abs",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("abs函数需要1个参数")
			}

			num, err := convertToNumber(args[0])
			if err != nil {
				return nil, err
			}

			val := num.(float64)
			if val < 0 {
				return -val, nil
			}
			return val, nil
		},
		1, 1, // 精确1个参数
	)

	// Round 四舍五入
	Round = NewFunction(
		"round",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 && len(args) != 2 {
				return nil, fmt.Errorf("round函数需要1-2个参数")
			}

			num, err := convertToNumber(args[0])
			if err != nil {
				return nil, err
			}
			val := num.(float64)

			decimal := 0
			if len(args) > 1 {
				d, err := convertToNumber(args[1])
				if err != nil {
					return nil, err
				}
				decimal = int(d.(float64))
			}

			p := math.Pow10(decimal)
			return math.Round(val*p) / p, nil
		},
		1, 2, // 1-2个参数
	)
)

// 字符串处理函数
var (
	// Length 计算字符串或数组长度
	Length = NewFunction(
		"length",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("length函数需要1个参数")
			}

			arg := args[0]

			// 检查是否是数组
			if arr, ok := arg.([]interface{}); ok {
				return float64(len(arr)), nil
			}

			// 尝试转换为字符串
			str, err := convertToString(arg)
			if err != nil {
				return nil, err
			}

			return float64(len(str.(string))), nil
		},
		1, 1, // 精确1个参数
	)

	// Upper 将字符串转为大写
	Upper = NewFunction(
		"upper",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("upper函数需要1个参数")
			}

			str, err := convertToString(args[0])
			if err != nil {
				return nil, err
			}

			return strings.ToUpper(str.(string)), nil
		},
		1, 1, // 精确1个参数
	)

	// Lower 将字符串转为小写
	Lower = NewFunction(
		"lower",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("lower函数需要1个参数")
			}

			str, err := convertToString(args[0])
			if err != nil {
				return nil, err
			}

			return strings.ToLower(str.(string)), nil
		},
		1, 1, // 精确1个参数
	)

	// Concat 连接字符串
	Concat = NewFunction(
		"concat",
		func(args []interface{}) (interface{}, error) {
			if len(args) < 2 {
				return nil, fmt.Errorf("concat函数至少需要2个参数")
			}

			var result strings.Builder

			for _, arg := range args {
				str, err := convertToString(arg)
				if err != nil {
					return nil, err
				}

				result.WriteString(str.(string))
			}

			return result.String(), nil
		},
		2, -1, // 至少2个参数，无上限
	)
)

// 类型转换函数
var (
	// ToString 将值转为字符串
	ToString = NewFunction(
		"toString",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("toString函数需要1个参数")
			}

			return convertToString(args[0])
		},
		1, 1, // 精确1个参数
	)

	// ToNumber 将值转为数字
	ToNumber = NewFunction(
		"toNumber",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("toNumber函数需要1个参数")
			}

			return convertToNumber(args[0])
		},
		1, 1, // 精确1个参数
	)

	// ToBoolean 将值转为布尔值
	ToBoolean = NewFunction(
		"toBoolean",
		func(args []interface{}) (interface{}, error) {
			if len(args) != 1 {
				return nil, fmt.Errorf("toBoolean函数需要1个参数")
			}

			return convertToBoolean(args[0])
		},
		1, 1, // 精确1个参数
	)
)

// 逻辑函数
var (
	// If 条件选择
	If = NewFunction(
		"if",
		func(args []any) (any, error) {
			if len(args) != 3 {
				return nil, fmt.Errorf("if函数需要3个参数")
			}

			condition, err := convertToBoolean(args[0])
			if err != nil {
				return nil, err
			}

			if condition.(bool) {
				return args[1], nil
			} else {
				return args[2], nil
			}
		},
		3, 3, // 精确3个参数
	)
)

// 数组处理函数
var (
	// Includes 检查数组是否包含某个元素
	Includes = NewFunction(
		"includes",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("includes函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("includes函数的第一个参数必须是数组")
				}
			}

			// 要查找的元素
			item := args[1]

			// 遍历数组查找元素
			for _, elem := range arr {
				// 使用equals函数比较元素
				if equals(elem, item) {
					return true, nil
				}
			}

			return false, nil
		},
		2, 2, // 精确2个参数
	)

	// IndexOf 查找元素在数组中的位置
	IndexOf = NewFunction(
		"indexOf",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("indexOf函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("indexOf函数的第一个参数必须是数组")
				}
			}

			// 要查找的元素
			item := args[1]

			// 遍历数组查找元素
			for i, elem := range arr {
				if equals(elem, item) {
					return float64(i), nil
				}
			}

			// 未找到返回-1
			return float64(-1), nil
		},
		2, 2, // 精确2个参数
	)

	// Filter 过滤数组元素
	Filter = NewFunction(
		"filter",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("filter函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("filter函数的第一个参数必须是数组")
				}
			}

			// 获取条件表达式
			_, ok = args[1].(string)
			if !ok {
				return nil, fmt.Errorf("filter函数的第二个参数必须是表达式字符串")
			}
			// 注意：这里不再使用 condExpr 变量，避免未使用变量警告

			// 返回结果数组
			result := make([]any, 0)

			// TODO: 这里需要实现表达式的计算，这可能需要依赖外部的表达式解析器
			// 实现方法1: 使用单独的表达式求值器
			// 实现方法2: 使用回调函数
			// 由于这部分依赖外部组件，这里仅提供代码框架
			// 实际实现可能需要将此函数移到非builtin包中

			// 示例：仅保留非零/非空/非false的元素
			for _, elem := range arr {
				// 简化版本，只做简单的值判断
				switch v := elem.(type) {
				case float64:
					if v != 0 {
						result = append(result, elem)
					}
				case string:
					if v != "" {
						result = append(result, elem)
					}
				case bool:
					if v {
						result = append(result, elem)
					}
				case nil:
					// 跳过nil
				default:
					// 其他类型都添加
					result = append(result, elem)
				}
			}

			return result, nil
		},
		2, 2, // 精确2个参数
	)

	// Map 转换数组元素
	Map = NewFunction(
		"map",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("map函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("map函数的第一个参数必须是数组")
				}
			}

			// 获取转换表达式
			_, ok = args[1].(string)
			if !ok {
				return nil, fmt.Errorf("map函数的第二个参数必须是表达式字符串")
			}
			// 注意：这里不再使用 mapExpr 变量，避免未使用变量警告

			// 返回结果数组
			result := make([]any, len(arr))

			// TODO: 这里需要实现表达式的计算，这可能需要依赖外部的表达式解析器
			// 简化版本：仅演示，实际应由外部表达式引擎完成
			for i, elem := range arr {
				// 这里简单地将元素转为字符串作为示例
				result[i] = fmt.Sprintf("%v", elem)
			}

			return result, nil
		},
		2, 2, // 精确2个参数
	)

	// Join 将数组元素连接成字符串
	Join = NewFunction(
		"join",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("join函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("join函数的第一个参数必须是数组")
				}
			}

			// 获取分隔符
			sep, err := convertToString(args[1])
			if err != nil {
				return nil, fmt.Errorf("join函数的第二个参数必须是字符串: %v", err)
			}

			// 转换数组元素为字符串
			strArr := make([]string, len(arr))
			for i, elem := range arr {
				strValue, err := convertToString(elem)
				if err != nil {
					// 如果无法转换，使用默认字符串表示
					strArr[i] = fmt.Sprintf("%v", elem)
				} else {
					strValue, ok := strValue.(string)
					if ok {
						strArr[i] = strValue
					} else {
						strArr[i] = fmt.Sprintf("%v", elem)
					}
				}
			}

			// 连接字符串
			sepStr, ok := sep.(string)
			if !ok {
				sepStr = fmt.Sprintf("%v", sep)
			}
			return strings.Join(strArr, sepStr), nil
		},
		2, 2, // 精确2个参数
	)

	// Some 检查数组是否有至少一个元素满足条件
	Some = NewFunction(
		"some",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("some函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("some函数的第一个参数必须是数组")
				}
			}

			// 获取条件表达式
			_, ok = args[1].(string)
			if !ok {
				return nil, fmt.Errorf("some函数的第二个参数必须是表达式字符串")
			}
			// 注意：这里不再使用 condExpr 变量，避免未使用变量警告

			// TODO: 这里需要实现表达式的计算，这可能需要依赖外部的表达式解析器
			// 简化版本：数组中是否有至少一个true值
			for _, elem := range arr {
				if boolVal, ok := elem.(bool); ok && boolVal {
					return true, nil
				}
				// 非零数值也算true
				if numVal, ok := elem.(float64); ok && numVal != 0 {
					return true, nil
				}
				// 非空字符串也算true
				if strVal, ok := elem.(string); ok && strVal != "" {
					return true, nil
				}
			}

			return false, nil
		},
		2, 2, // 精确2个参数
	)

	// Every 检查数组是否所有元素都满足条件
	Every = NewFunction(
		"every",
		func(args []any) (any, error) {
			if len(args) != 2 {
				return nil, fmt.Errorf("every函数需要2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("every函数的第一个参数必须是数组")
				}
			}

			// 如果数组为空，按照惯例返回true
			if len(arr) == 0 {
				return true, nil
			}

			// 获取条件表达式
			_, ok = args[1].(string)
			if !ok {
				return nil, fmt.Errorf("every函数的第二个参数必须是表达式字符串")
			}
			// 注意：这里不再使用 condExpr 变量，避免未使用变量警告

			// TODO: 这里需要实现表达式的计算，这可能需要依赖外部的表达式解析器
			// 简化版本：数组中是否所有元素都为true值
			for _, elem := range arr {
				// 布尔类型检查
				if boolVal, ok := elem.(bool); ok && !boolVal {
					return false, nil
				}
				// 数值类型检查
				if numVal, ok := elem.(float64); ok && numVal == 0 {
					return false, nil
				}
				// 字符串类型检查
				if strVal, ok := elem.(string); ok && strVal == "" {
					return false, nil
				}
				// nil值检查
				if elem == nil {
					return false, nil
				}
			}

			return true, nil
		},
		2, 2, // 精确2个参数
	)

	// Slice 获取数组的子集
	Slice = NewFunction(
		"slice",
		func(args []any) (any, error) {
			if len(args) < 2 || len(args) > 3 {
				return nil, fmt.Errorf("slice函数需要2-3个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("slice函数的第一个参数必须是数组")
				}
			}

			// 获取起始索引
			beginNum, err := convertToNumber(args[1])
			if err != nil {
				return nil, fmt.Errorf("slice函数的第二个参数必须是数值: %v", err)
			}
			beginFloat, ok := beginNum.(float64)
			if !ok {
				return nil, fmt.Errorf("slice函数的第二个参数必须是数值")
			}
			begin := int(beginFloat)

			// 获取结束索引，默认为数组长度
			end := len(arr)
			if len(args) > 2 {
				endNum, err := convertToNumber(args[2])
				if err != nil {
					return nil, fmt.Errorf("slice函数的第三个参数必须是数值: %v", err)
				}
				endFloat, ok := endNum.(float64)
				if !ok {
					return nil, fmt.Errorf("slice函数的第三个参数必须是数值")
				}
				end = int(endFloat)
			}

			// 处理负索引
			if begin < 0 {
				begin = len(arr) + begin
				if begin < 0 {
					begin = 0
				}
			}
			if end < 0 {
				end = len(arr) + end
				if end < 0 {
					end = 0
				}
			}

			// 处理索引越界
			if begin > len(arr) {
				begin = len(arr)
			}
			if end > len(arr) {
				end = len(arr)
			}

			// 返回子数组
			if begin >= end {
				return []any{}, nil
			}
			return arr[begin:end], nil
		},
		2, 3, // 2-3个参数
	)

	// Length 获取数组长度（复用之前的Length函数）

	// Sort 对数组进行排序
	Sort = NewFunction(
		"sort",
		func(args []any) (any, error) {
			if len(args) < 1 || len(args) > 2 {
				return nil, fmt.Errorf("sort函数需要1-2个参数")
			}

			// 获取数组参数
			arr, ok := args[0].([]any)
			if !ok {
				// 尝试其他类型的数组转换
				if reflect.TypeOf(args[0]).Kind() == reflect.Slice {
					// 将其他类型的slice转换为[]any
					s := reflect.ValueOf(args[0])
					arr = make([]any, s.Len())
					for i := 0; i < s.Len(); i++ {
						arr[i] = s.Index(i).Interface()
					}
				} else {
					return nil, fmt.Errorf("sort函数的第一个参数必须是数组")
				}
			}

			// 如果数组为空或只有一个元素，直接返回
			if len(arr) <= 1 {
				// 返回新数组，避免修改原数组
				newArr := make([]any, len(arr))
				copy(newArr, arr)
				return newArr, nil
			}

			// 默认升序排序
			// 这里我们使用一个简单的冒泡排序作为示例
			// 实际应该使用更高效的排序算法
			newArr := make([]any, len(arr))
			copy(newArr, arr)

			for i := 0; i < len(newArr); i++ {
				for j := 0; j < len(newArr)-i-1; j++ {
					// 尝试比较相邻元素
					result, err := compareForSort(newArr[j], newArr[j+1])
					if err != nil {
						return nil, err
					}

					// 如果前面的元素更大，交换它们
					if result > 0 {
						newArr[j], newArr[j+1] = newArr[j+1], newArr[j]
					}
				}
			}

			return newArr, nil
		},
		1, 2, // 1-2个参数
	)
)

// 辅助函数：比较两个值用于排序
// 返回值：-1表示a < b，0表示a == b，1表示a > b
func compareForSort(a, b any) (int, error) {
	// 两个nil值相等
	if a == nil && b == nil {
		return 0, nil
	}

	// nil值小于任何非nil值
	if a == nil {
		return -1, nil
	}
	if b == nil {
		return 1, nil
	}

	// 数值比较
	aNum, aIsNum := a.(float64)
	bNum, bIsNum := b.(float64)
	if aIsNum && bIsNum {
		if aNum < bNum {
			return -1, nil
		} else if aNum > bNum {
			return 1, nil
		} else {
			return 0, nil
		}
	}

	// 字符串比较
	aStr, aIsStr := a.(string)
	bStr, bIsStr := b.(string)
	if aIsStr && bIsStr {
		if aStr < bStr {
			return -1, nil
		} else if aStr > bStr {
			return 1, nil
		} else {
			return 0, nil
		}
	}

	// 布尔值比较：false < true
	aBool, aIsBool := a.(bool)
	bBool, bIsBool := b.(bool)
	if aIsBool && bIsBool {
		if !aBool && bBool {
			return -1, nil
		} else if aBool && !bBool {
			return 1, nil
		} else {
			return 0, nil
		}
	}

	// 不同类型间的比较规则：nil < 数值 < 字符串 < 布尔值
	if aIsNum && !bIsNum {
		if bIsStr || bIsBool {
			return -1, nil
		}
	}
	if aIsStr {
		if bIsNum {
			return 1, nil // 这里已修复
		}
		if bIsBool {
			return -1, nil
		}
	}
	if aIsBool {
		if bIsNum || bIsStr {
			return 1, nil
		}
	}

	// 尝试转换类型后比较
	aStrVal, err1 := convertToString(a)
	bStrVal, err2 := convertToString(b)
	if err1 == nil && err2 == nil {
		// 正确进行类型断言
		aStrString, aOk := aStrVal.(string)
		bStrString, bOk := bStrVal.(string)
		if aOk && bOk {
			if aStrString < bStrString {
				return -1, nil
			} else if aStrString > bStrString {
				return 1, nil
			} else {
				return 0, nil
			}
		}
	}

	// 无法比较的类型
	return 0, fmt.Errorf("无法比较类型 %T 和 %T", a, b)
}

// 辅助函数：判断两个值是否相等（与eval包中的equals函数类似）
func equals(left, right any) bool {
	// 处理nil值
	if left == nil && right == nil {
		return true
	}
	if left == nil || right == nil {
		return false
	}

	// 比较数值
	leftNum, leftOk := left.(float64)
	rightNum, rightOk := right.(float64)
	if leftOk && rightOk {
		return leftNum == rightNum
	}

	// 比较字符串
	leftStr, leftOk := left.(string)
	rightStr, rightOk := right.(string)
	if leftOk && rightOk {
		return leftStr == rightStr
	}

	// 比较布尔值
	leftBool, leftOk := left.(bool)
	rightBool, rightOk := right.(bool)
	if leftOk && rightOk {
		return leftBool == rightBool
	}

	// 尝试转换类型后比较
	leftStrVal, err1 := convertToString(left)
	rightStrVal, err2 := convertToString(right)
	if err1 == nil && err2 == nil {
		// 确保安全地转换为字符串类型后再比较
		leftStrActual, leftOk := leftStrVal.(string)
		rightStrActual, rightOk := rightStrVal.(string)
		if leftOk && rightOk {
			return leftStrActual == rightStrActual
		}
	}

	// 其他情况，简单比较
	return left == right
}
