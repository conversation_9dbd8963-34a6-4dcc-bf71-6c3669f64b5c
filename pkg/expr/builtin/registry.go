// 内置函数注册管理器
package builtin

import (
	"fmt"
	"sync"
)

// Registry 函数注册管理器，管理所有注册的内置函数
type Registry struct {
	// 函数映射表
	functions map[string]Function
	// 用于并发安全
	mu sync.RWMutex
}

// NewRegistry 创建一个新的注册管理器
func NewRegistry() *Registry {
	return &Registry{
		functions: make(map[string]Function),
	}
}

// DefaultRegistry 默认注册管理器实例
var DefaultRegistry = initDefaultRegistry()

// initDefaultRegistry 初始化默认注册管理器
func initDefaultRegistry() *Registry {
	registry := NewRegistry()

	// 注册数学函数
	registry.Register(Min)
	registry.Register(Max)
	registry.Register(Abs)
	registry.Register(Round)

	// 注册字符串函数
	registry.Register(Length)
	registry.Register(Upper)
	registry.Register(Lower)
	registry.Register(Concat)

	// 注册类型转换函数
	registry.Register(ToString)
	registry.Register(ToNumber)
	registry.Register(ToBoolean)

	// 注册逻辑函数
	registry.Register(If)

	// 注册数组处理函数
	registry.Register(Includes)
	registry.Register(IndexOf)
	registry.Register(Filter)
	registry.Register(Map)
	registry.Register(Join)
	registry.Register(Some)
	registry.Register(Every)
	registry.Register(Slice)
	registry.Register(Sort)

	return registry
}

// Register 注册一个函数
func (r *Registry) Register(fn Function) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	name := fn.Name()
	if _, exists := r.functions[name]; exists {
		return fmt.Errorf("函数 '%s' 已经注册", name)
	}

	r.functions[name] = fn
	return nil
}

// Unregister 注销一个函数
func (r *Registry) Unregister(name string) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.functions[name]; exists {
		delete(r.functions, name)
		return true
	}

	return false
}

// Get 获取一个函数
func (r *Registry) Get(name string) (Function, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	fn, exists := r.functions[name]
	return fn, exists
}

// GetAll 获取所有注册的函数
func (r *Registry) GetAll() map[string]Function {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]Function, len(r.functions))
	for name, fn := range r.functions {
		result[name] = fn
	}

	return result
}

// Clear 清空所有注册的函数
func (r *Registry) Clear() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.functions = make(map[string]Function)
}

// RegisterFunction 便捷函数，向默认注册管理器注册函数
func RegisterFunction(fn Function) error {
	return DefaultRegistry.Register(fn)
}

// UnregisterFunction 便捷函数，从默认注册管理器注销函数
func UnregisterFunction(name string) bool {
	return DefaultRegistry.Unregister(name)
}

// GetFunction 便捷函数，从默认注册管理器获取函数
func GetFunction(name string) (Function, bool) {
	return DefaultRegistry.Get(name)
}
