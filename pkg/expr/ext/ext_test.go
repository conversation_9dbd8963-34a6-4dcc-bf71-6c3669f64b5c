
package ext

import (
	"testing"

	"admin/pkg/expr/builtin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestExtensionRegistry 测试扩展注册表功能
func TestExtensionRegistry(t *testing.T) {
	registry := NewExtensionRegistry()
	fn := builtin.NewFunction("customFunc", func(args []any) (any, error) {
		return "custom", nil
	}, 0, 0)

	// Register
	err := registry.Register(fn)
	require.NoError(t, err)

	// Get
	retrievedFn, ok := registry.Get("customFunc")
	assert.True(t, ok)
	assert.Equal(t, "customFunc", retrievedFn.Name())

	// Register duplicate
	err = registry.Register(fn)
	assert.Error(t, err)

	// Unregister
	unregistered := registry.Unregister("customFunc")
	assert.True(t, unregistered)
	_, ok = registry.Get("customFunc")
	assert.False(t, ok)

	// Unregister non-existent
	unregistered = registry.Unregister("nonExistent")
	assert.False(t, unregistered)

	// Clear
	registry.Register(fn) // re-register for clear test
	registry.Clear()
	assert.Empty(t, registry.GetAll())
}

// TestDefaultExtensionRegistry 测试默认扩展注册表
func TestDefaultExtensionRegistry(t *testing.T) {
	// Ensure default registry is initialized
	require.NotNil(t, DefaultExtensionRegistry)
	initialCount := len(DefaultExtensionRegistry.GetAll())

	// Use convenience functions
	fn := NewFunction("globalExtFunc", func(args []any) (any, error) { return "ok", nil }, 0, 0)
	err := RegisterFunction(fn)
	require.NoError(t, err)

	_, ok := GetFunction("globalExtFunc")
	assert.True(t, ok)

	unregistered := UnregisterFunction("globalExtFunc")
	assert.True(t, unregistered)
	_, ok = GetFunction("globalExtFunc")
	assert.False(t, ok)

	// Ensure count is back to initial
	assert.Equal(t, initialCount, len(DefaultExtensionRegistry.GetAll()))
}

// TestMergeWithBuiltins 测试合并内置和扩展函数
func TestMergeWithBuiltins(t *testing.T) {
	// Save original registries
	originalBuiltinRegistry := builtin.DefaultRegistry
	originalExtRegistry := DefaultExtensionRegistry

	// Set up clean registries for this test
	builtin.DefaultRegistry = builtin.NewRegistry()
	DefaultExtensionRegistry = NewExtensionRegistry()

	// Defer restoration
	defer func() {
		builtin.DefaultRegistry = originalBuiltinRegistry
		DefaultExtensionRegistry = originalExtRegistry
	}()

	// Register a builtin function
	builtinFn := builtin.NewFunction("myFunc", func(args []any) (any, error) { return "builtin", nil }, 0, 0)
	builtin.RegisterFunction(builtinFn)

	// Register an extension function with the same name
	extFn := NewFunction("myFunc", func(args []any) (any, error) { return "extension", nil }, 0, 0)
	RegisterFunction(extFn)

	// Register another unique extension function
	anotherExtFn := NewFunction("anotherFunc", func(args []any) (any, error) { return "another", nil }, 0, 0)
	RegisterFunction(anotherExtFn)

	// Merge
	merged := MergeWithBuiltins()

	// Check that the extension function overrides the builtin one
	fn, ok := merged["myFunc"]
	require.True(t, ok)
	result, _ := fn.Call(nil)
	assert.Equal(t, "extension", result)

	// Check that the other extension function is present
	_, ok = merged["anotherFunc"]
	assert.True(t, ok)

	// The deferred function will handle cleanup
}
