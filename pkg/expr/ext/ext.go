// 表达式引擎的扩展机制
package ext

import (
	"fmt"
	"sync"

	"admin/pkg/expr/builtin"
)

// ExtensionRegistry 扩展注册管理器，管理所有注册的扩展函数
type ExtensionRegistry struct {
	// 函数映射表
	functions map[string]builtin.Function
	// 用于并发安全
	mu sync.RWMutex
}

// NewExtensionRegistry 创建一个新的扩展注册管理器
func NewExtensionRegistry() *ExtensionRegistry {
	return &ExtensionRegistry{
		functions: make(map[string]builtin.Function),
	}
}

// DefaultExtensionRegistry 默认扩展注册管理器实例
var DefaultExtensionRegistry = NewExtensionRegistry()

// Register 注册一个扩展函数
func (r *ExtensionRegistry) Register(fn builtin.Function) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	name := fn.Name()
	if _, exists := r.functions[name]; exists {
		return fmt.Errorf("扩展函数 '%s' 已经注册", name)
	}

	r.functions[name] = fn
	return nil
}

// Unregister 注销一个扩展函数
func (r *ExtensionRegistry) Unregister(name string) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.functions[name]; exists {
		delete(r.functions, name)
		return true
	}

	return false
}

// Get 获取一个扩展函数
func (r *ExtensionRegistry) Get(name string) (builtin.Function, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	fn, exists := r.functions[name]
	return fn, exists
}

// GetAll 获取所有注册的扩展函数
func (r *ExtensionRegistry) GetAll() map[string]builtin.Function {
	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]builtin.Function, len(r.functions))
	for name, fn := range r.functions {
		result[name] = fn
	}

	return result
}

// Clear 清空所有注册的扩展函数
func (r *ExtensionRegistry) Clear() {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.functions = make(map[string]builtin.Function)
}

// RegisterFunction 便捷函数，向默认扩展注册管理器注册函数
func RegisterFunction(fn builtin.Function) error {
	return DefaultExtensionRegistry.Register(fn)
}

// UnregisterFunction 便捷函数，从默认扩展注册管理器注销函数
func UnregisterFunction(name string) bool {
	return DefaultExtensionRegistry.Unregister(name)
}

// GetFunction 便捷函数，从默认扩展注册管理器获取函数
func GetFunction(name string) (builtin.Function, bool) {
	return DefaultExtensionRegistry.Get(name)
}

// MergeWithBuiltins 合并内置函数和扩展函数
// 返回一个新的映射表，包含内置函数和扩展函数
// 如果有同名函数，扩展函数会覆盖内置函数
func MergeWithBuiltins() map[string]builtin.Function {
	builtins := builtin.DefaultRegistry.GetAll()
	extensions := DefaultExtensionRegistry.GetAll()

	result := make(map[string]builtin.Function, len(builtins)+len(extensions))

	// 先添加所有内置函数
	for name, fn := range builtins {
		result[name] = fn
	}

	// 添加扩展函数，会覆盖同名的内置函数
	for name, fn := range extensions {
		result[name] = fn
	}

	return result
}

// NewFunction 创建一个新的函数 (委托给builtin包)
func NewFunction(
	name string,
	handler func(args []interface{}) (interface{}, error),
	minArgs int,
	maxArgs int,
) builtin.Function {
	return builtin.NewFunction(name, handler, minArgs, maxArgs)
}
