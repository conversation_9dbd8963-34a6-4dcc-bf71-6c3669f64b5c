package ast

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLiteralExpressions(t *testing.T) {
	tests := []struct {
		node     Expression
		expected string
	}{
		{
			&NumberLiteral{Value: 42.5, Literal: "42.5"},
			"42.5",
		},
		{
			&StringLiteral{Value: "hello"},
			`"hello"`,
		},
		{
			&BooleanLiteral{Value: true},
			"true",
		},
		{
			&Identifier{Name: "x"},
			"x",
		},
	}

	for _, tt := range tests {
		assert.Equal(t, tt.expected, tt.node.String())
	}
}

func TestBinaryExpression(t *testing.T) {
	// 构造表达式：1 + 2 * 3
	expr := &BinaryExpression{
		Left:     &NumberLiteral{Value: 1, Literal: "1"},
		Operator: "+",
		Right: &BinaryExpression{
			Left:     &NumberLiteral{Value: 2, Literal: "2"},
			Operator: "*",
			Right:    &NumberLiteral{Value: 3, Literal: "3"},
		},
	}

	expected := "(1 + (2 * 3))"
	assert.Equal(t, expected, expr.String())
}

func TestUnaryExpression(t *testing.T) {
	// 构造表达式：!true
	expr := &UnaryExpression{
		Operator: "!",
		Right:    &BooleanLiteral{Value: true},
	}

	expected := "(!true)"
	assert.Equal(t, expected, expr.String())
}

func TestComplexExpression(t *testing.T) {
	// 构造表达式：(x + 1) * !false
	expr := &BinaryExpression{
		Left: &BinaryExpression{
			Left:     &Identifier{Name: "x"},
			Operator: "+",
			Right:    &NumberLiteral{Value: 1, Literal: "1"},
		},
		Operator: "*",
		Right: &UnaryExpression{
			Operator: "!",
			Right:    &BooleanLiteral{Value: false},
		},
	}

	expected := "((x + 1) * (!false))"
	assert.Equal(t, expected, expr.String())
}

func TestPosition(t *testing.T) {
	pos := Position{Line: 1, Column: 5}

	nodes := []NodeWithPosition{
		&NumberLiteral{Position: pos},
		&StringLiteral{Position: pos},
		&BooleanLiteral{Position: pos},
		&Identifier{Position: pos},
		&BinaryExpression{Position: pos},
		&UnaryExpression{Position: pos},
	}

	for _, node := range nodes {
		assert.Equal(t, pos, node.Pos())
	}
}
