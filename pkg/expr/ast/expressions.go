package ast

import (
	"fmt"
	"strconv"
	"strings"
)

// 所有表达式节点都必须实现 Expression 接口
var (
	_ Expression = (*NumberLiteral)(nil)
	_ Expression = (*StringLiteral)(nil)
	_ Expression = (*BooleanLiteral)(nil)
	_ Expression = (*Identifier)(nil)
	_ Expression = (*BinaryExpression)(nil)
	_ Expression = (*UnaryExpression)(nil)
	_ Expression = (*CallExpression)(nil)
	_ Expression = (*ArrayLiteral)(nil)
	_ Expression = (*ObjectLiteral)(nil)
	_ Expression = (*IndexExpression)(nil)
	_ Expression = (*TernaryExpression)(nil)
)

// NumberLiteral 表示数字字面量
type NumberLiteral struct {
	Value    float64
	Literal  string
	Position Position
}

func (nl *NumberLiteral) expressionNode()      {}
func (nl *NumberLiteral) TokenLiteral() string { return nl.Literal }
func (nl *NumberLiteral) String() string       { return nl.Literal }
func (nl *NumberLiteral) Pos() Position        { return nl.Position }

// StringLiteral 表示字符串字面量
type StringLiteral struct {
	Value    string
	Position Position
}

func (sl *StringLiteral) expressionNode()      {}
func (sl *StringLiteral) TokenLiteral() string { return strconv.Quote(sl.Value) }
func (sl *StringLiteral) String() string       { return strconv.Quote(sl.Value) }
func (sl *StringLiteral) Pos() Position        { return sl.Position }

// BooleanLiteral 表示布尔字面量
type BooleanLiteral struct {
	Value    bool
	Position Position
}

func (bl *BooleanLiteral) expressionNode()      {}
func (bl *BooleanLiteral) TokenLiteral() string { return strconv.FormatBool(bl.Value) }
func (bl *BooleanLiteral) String() string       { return strconv.FormatBool(bl.Value) }
func (bl *BooleanLiteral) Pos() Position        { return bl.Position }

// Identifier 表示标识符
type Identifier struct {
	Name     string
	Position Position
}

func (i *Identifier) expressionNode()      {}
func (i *Identifier) TokenLiteral() string { return i.Name }
func (i *Identifier) String() string       { return i.Name }
func (i *Identifier) Pos() Position        { return i.Position }

// BinaryExpression 表示二元运算表达式
type BinaryExpression struct {
	Left     Expression
	Operator string
	Right    Expression
	Position Position
}

func (be *BinaryExpression) expressionNode()      {}
func (be *BinaryExpression) TokenLiteral() string { return be.Operator }
func (be *BinaryExpression) String() string {
	return fmt.Sprintf("(%s %s %s)", be.Left.String(), be.Operator, be.Right.String())
}
func (be *BinaryExpression) Pos() Position { return be.Position }

// UnaryExpression 表示一元运算表达式
type UnaryExpression struct {
	Operator string
	Right    Expression
	Position Position
}

func (ue *UnaryExpression) expressionNode()      {}
func (ue *UnaryExpression) TokenLiteral() string { return ue.Operator }
func (ue *UnaryExpression) String() string {
	return fmt.Sprintf("(%s%s)", ue.Operator, ue.Right.String())
}
func (ue *UnaryExpression) Pos() Position { return ue.Position }

// CallExpression 表示函数调用表达式
type CallExpression struct {
	Function  Expression   // 函数表达式，通常是标识符
	Arguments []Expression // 参数列表
	Position  Position
}

func (ce *CallExpression) expressionNode()      {}
func (ce *CallExpression) TokenLiteral() string { return "call" }
func (ce *CallExpression) String() string {
	var args []string
	for _, arg := range ce.Arguments {
		args = append(args, arg.String())
	}
	return fmt.Sprintf("%s(%s)", ce.Function.String(), strings.Join(args, ", "))
}
func (ce *CallExpression) Pos() Position { return ce.Position }

// ArrayLiteral 表示数组字面量
type ArrayLiteral struct {
	Elements []Expression
	Position Position
}

func (al *ArrayLiteral) expressionNode()      {}
func (al *ArrayLiteral) TokenLiteral() string { return "array" }
func (al *ArrayLiteral) String() string {
	var elements []string
	for _, element := range al.Elements {
		elements = append(elements, element.String())
	}
	return fmt.Sprintf("[%s]", strings.Join(elements, ", "))
}
func (al *ArrayLiteral) Pos() Position { return al.Position }

// KeyValuePair 表示对象字面量中的键值对
type KeyValuePair struct {
	Key   Expression
	Value Expression
}

// ObjectLiteral 表示对象字面量
type ObjectLiteral struct {
	Pairs    []KeyValuePair
	Position Position
}

func (ol *ObjectLiteral) expressionNode()      {}
func (ol *ObjectLiteral) TokenLiteral() string { return "object" }
func (ol *ObjectLiteral) String() string {
	var pairs []string
	for _, pair := range ol.Pairs {
		pairs = append(pairs, fmt.Sprintf("%s: %s", pair.Key.String(), pair.Value.String()))
	}
	return fmt.Sprintf("{%s}", strings.Join(pairs, ", "))
}
func (ol *ObjectLiteral) Pos() Position { return ol.Position }

// IndexExpression 表示索引表达式 arr[idx] 或 obj[key]
type IndexExpression struct {
	Left     Expression // 被索引的对象，如数组或对象
	Index    Expression // 索引表达式
	Position Position
}

func (ie *IndexExpression) expressionNode()      {}
func (ie *IndexExpression) TokenLiteral() string { return "index" }
func (ie *IndexExpression) String() string {
	return fmt.Sprintf("(%s[%s])", ie.Left.String(), ie.Index.String())
}
func (ie *IndexExpression) Pos() Position { return ie.Position }

// TernaryExpression 表示三元条件表达式 (condition ? trueExpr : falseExpr)
type TernaryExpression struct {
	Condition Expression // 条件表达式
	TrueExpr  Expression // 条件为真时的表达式
	FalseExpr Expression // 条件为假时的表达式
	Position  Position
}

func (te *TernaryExpression) expressionNode()      {}
func (te *TernaryExpression) TokenLiteral() string { return "ternary" }
func (te *TernaryExpression) String() string {
	return fmt.Sprintf("(%s ? %s : %s)", te.Condition.String(), te.TrueExpr.String(), te.FalseExpr.String())
}
func (te *TernaryExpression) Pos() Position { return te.Position }
