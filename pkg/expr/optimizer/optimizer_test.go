
package optimizer

import (
	"testing"

	"admin/pkg/expr/ast"
	"admin/pkg/expr/eval"
	"admin/pkg/expr/lexer"
	"admin/pkg/expr/parser"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// parseExpression 是一个辅助函数，用于解析表达式字符串
func parseExpression(t *testing.T, exprStr string) ast.Expression {
	l := lexer.New(exprStr)
	p := parser.New(l)
	expr, err := p.ParseExpression()
	require.NoError(t, err)
	return expr
}

// TestConstantFolding 测试常量折叠优化
func TestConstantFolding(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{"Simple Arithmetic", "2 + 3 * 4", "14"},
		{"Parentheses", "(2 + 3) * 4", "20"},
		{"Unary Operator", "-10 + -5", "-15"},
		{"Boolean Logic", "true && !false", "true"},
		{"Ternary Operator", "true ? 10 : 20", "10"},
		{"Function Call", "abs(-5)", "5"},
		{"No Change", "x + 1", "(x + 1)"},
	}

	optimizer := New(eval.NewEvaluator())

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			inputExpr := parseExpression(t, tc.input)
			optimizedExpr := optimizer.Optimize(inputExpr, nil, nil)
			assert.Equal(t, tc.expected, optimizedExpr.String())
		})
	}
}

// TestAlgebraicSimplification 测试代数化简
func TestAlgebraicSimplification(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{"Add Zero", "x + 0", "x"},
		{"Subtract Zero", "x - 0", "x"},
		{"Multiply One", "x * 1", "x"},
		{"Multiply Zero", "x * 0", "0"},
		{"Divide One", "x / 1", "x"},
		{"Subtract Self", "x - x", "0"},
		{"Logical And True", "x && true", "x"},
		{"Logical Or False", "x || false", "x"},
		{"Double Negation", "!(!x)", "x"},
		{"No Change", "x + y", "(x + y)"},
	}

	optimizer := New(eval.NewEvaluator())

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			inputExpr := parseExpression(t, tc.input)
			optimizedExpr := optimizer.Optimize(inputExpr, nil, nil)
			assert.Equal(t, tc.expected, optimizedExpr.String())
		})
	}
}

// TestShortCircuitOptimization 测试短路优化
func TestShortCircuitOptimization(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{"And with False", "false && someFunction()", "false"},
		{"Or with True", "true || someFunction()", "true"},
		{"No Change", "x && y", "(x && y)"},
	}

	optimizer := New(eval.NewEvaluator())

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			inputExpr := parseExpression(t, tc.input)
			optimizedExpr := optimizer.Optimize(inputExpr, nil, nil)
			assert.Equal(t, tc.expected, optimizedExpr.String())
		})
	}
}

// TestOptimizerStats 测试优化器统计信息
func TestOptimizerStats(t *testing.T) {
	optimizer := New(eval.NewEvaluator())
	expr := parseExpression(t, "(2 + 3) * x + 0")

	_ = optimizer.Optimize(expr, nil, nil)

	stats := optimizer.GetStats()
	require.NotNil(t, stats)
	assert.Greater(t, stats.Passes, 0)
	totalApplications := 0
	for _, count := range stats.RuleApplications {
		totalApplications += count
	}
	assert.GreaterOrEqual(t, totalApplications, 1, "Expected at least one rule application")
	assert.Greater(t, stats.NodesSaved, 0)
}
