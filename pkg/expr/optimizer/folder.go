package optimizer

import (
	"admin/pkg/expr/ast"
)

// ConstantFolding 实现常量折叠优化
type ConstantFolding struct{}

// NewConstantFolding 创建一个新的常量折叠优化实例
func NewConstantFolding() *ConstantFolding {
	return &ConstantFolding{}
}

// Name 返回规则名称
func (c *ConstantFolding) Name() string {
	return "ConstantFolding"
}

// Apply 应用常量折叠优化
func (c *ConstantFolding) Apply(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	result := c.fold(expr, ctx)

	// 记录统计信息
	if result != expr {
		ctx.Stats.RuleApplications[c.Name()]++
	}

	return result
}

// fold 递归折叠常量表达式
func (c *ConstantFolding) fold(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	switch node := expr.(type) {
	case *ast.BinaryExpression:
		return c.foldBinary(node, ctx)

	case *ast.UnaryExpression:
		return c.foldUnary(node, ctx)

	case *ast.CallExpression:
		return c.foldCall(node, ctx)

	case *ast.IndexExpression:
		return c.foldIndex(node, ctx)

	case *ast.ArrayLiteral:
		return c.foldArray(node, ctx)

	case *ast.ObjectLiteral:
		return c.foldObject(node, ctx)

	case *ast.TernaryExpression:
		return c.foldTernary(node, ctx)

	default:
		// 基本类型不需要折叠
		return expr
	}
}

// foldBinary 折叠二元表达式
func (c *ConstantFolding) foldBinary(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 先递归折叠左右子表达式
	node.Left = c.fold(node.Left, ctx)
	node.Right = c.fold(node.Right, ctx)

	// 如果左右两边都是字面量，尝试计算结果
	if isLiteral(node.Left) && isLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	// 对于逻辑运算符，实现短路求值
	if node.Operator == "&&" {
		if isFalse(node.Left) || isFalse(node.Right) {
			return &ast.BooleanLiteral{
				Value:    false,
				Position: node.Position,
			}
		}
		if isTrue(node.Left) {
			return node.Right
		}
		if isTrue(node.Right) {
			return node.Left
		}
	} else if node.Operator == "||" {
		if isTrue(node.Left) || isTrue(node.Right) {
			return &ast.BooleanLiteral{
				Value:    true,
				Position: node.Position,
			}
		}
		if isFalse(node.Left) {
			return node.Right
		}
		if isFalse(node.Right) {
			return node.Left
		}
	}

	return node
}

// foldUnary 折叠一元表达式
func (c *ConstantFolding) foldUnary(node *ast.UnaryExpression, ctx *Context) ast.Expression {
	// 递归折叠子表达式
	node.Right = c.fold(node.Right, ctx)

	// 如果操作数是字面量，尝试计算结果
	if isLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// foldCall 折叠函数调用表达式
func (c *ConstantFolding) foldCall(node *ast.CallExpression, ctx *Context) ast.Expression {
	// 优化函数参数
	allConstArgs := true
	for i, arg := range node.Arguments {
		node.Arguments[i] = c.fold(arg, ctx)
		if !isLiteral(node.Arguments[i]) {
			allConstArgs = false
		}
	}

	// 如果是纯函数且所有参数都是常量，尝试计算
	if allConstArgs && isPureFunctionCall(node) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil && result != nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// foldIndex 折叠索引表达式
func (c *ConstantFolding) foldIndex(node *ast.IndexExpression, ctx *Context) ast.Expression {
	// 递归优化左侧和索引表达式
	node.Left = c.fold(node.Left, ctx)
	node.Index = c.fold(node.Index, ctx)

	// 如果左侧是数组或对象字面量，且索引是常量，尝试计算
	if (isArrayLiteral(node.Left) || isObjectLiteral(node.Left)) && isLiteral(node.Index) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil && result != nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// foldArray 折叠数组字面量
func (c *ConstantFolding) foldArray(node *ast.ArrayLiteral, ctx *Context) ast.Expression {
	// 递归折叠所有元素
	for i, element := range node.Elements {
		node.Elements[i] = c.fold(element, ctx)
	}

	// 注意：即使所有元素都是常量，我们通常不会进一步简化数组字面量
	// 除非有特殊的需求

	return node
}

// foldObject 折叠对象字面量
func (c *ConstantFolding) foldObject(node *ast.ObjectLiteral, ctx *Context) ast.Expression {
	// 递归折叠所有键值对
	for i := range node.Pairs {
		node.Pairs[i].Key = c.fold(node.Pairs[i].Key, ctx)
		node.Pairs[i].Value = c.fold(node.Pairs[i].Value, ctx)
	}

	// 同样，即使所有键值对都是常量，我们通常不会进一步简化对象字面量

	return node
}

// foldTernary 折叠三元表达式
func (c *ConstantFolding) foldTernary(node *ast.TernaryExpression, ctx *Context) ast.Expression {
	// 递归折叠条件表达式
	node.Condition = c.fold(node.Condition, ctx)

	// 条件是常量时，可以直接选择分支
	if isTrue(node.Condition) {
		return c.fold(node.TrueExpr, ctx)
	}

	if isFalse(node.Condition) {
		return c.fold(node.FalseExpr, ctx)
	}

	// 条件不是常量，继续折叠两个分支
	node.TrueExpr = c.fold(node.TrueExpr, ctx)
	node.FalseExpr = c.fold(node.FalseExpr, ctx)

	// 如果两个分支结果相同，可以直接返回其中一个
	if isSameExpression(node.TrueExpr, node.FalseExpr) {
		return node.TrueExpr
	}

	return node
}

// isPureFunctionCall 检查是否是纯函数调用
func isPureFunctionCall(node *ast.CallExpression) bool {
	// 仅简单处理内置纯函数
	if ident, ok := node.Function.(*ast.Identifier); ok {
		switch ident.Name {
		case "min", "max", "abs", "round", "length", "upper", "lower", "concat":
			return true
		default:
			return false
		}
	}
	return false
}

// isArrayLiteral 检查是否是数组字面量
func isArrayLiteral(expr ast.Expression) bool {
	_, ok := expr.(*ast.ArrayLiteral)
	return ok
}

// isObjectLiteral 检查是否是对象字面量
func isObjectLiteral(expr ast.Expression) bool {
	_, ok := expr.(*ast.ObjectLiteral)
	return ok
}
