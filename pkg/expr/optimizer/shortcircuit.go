package optimizer

import (
	"admin/pkg/expr/ast"
)

// ShortCircuitOptimization 实现短路优化
type ShortCircuitOptimization struct{}

// NewShortCircuitOptimization 创建一个新的短路优化实例
func NewShortCircuitOptimization() *ShortCircuitOptimization {
	return &ShortCircuitOptimization{}
}

// Name 返回规则名称
func (s *ShortCircuitOptimization) Name() string {
	return "ShortCircuitOptimization"
}

// Apply 应用短路优化
func (s *ShortCircuitOptimization) Apply(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	result := s.optimize(expr, ctx)

	// 记录统计信息
	if result != expr {
		ctx.Stats.RuleApplications[s.Name()]++
	}

	return result
}

// optimize 递归应用短路优化
func (s *ShortCircuitOptimization) optimize(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	switch node := expr.(type) {
	case *ast.BinaryExpression:
		return s.optimizeBinary(node, ctx)

	case *ast.UnaryExpression:
		node.Right = s.optimize(node.Right, ctx)
		return node

	case *ast.CallExpression:
		// 优化函数参数
		for i, arg := range node.Arguments {
			node.Arguments[i] = s.optimize(arg, ctx)
		}
		return node

	case *ast.IndexExpression:
		node.Left = s.optimize(node.Left, ctx)
		node.Index = s.optimize(node.Index, ctx)
		return node

	case *ast.ArrayLiteral:
		for i, elem := range node.Elements {
			node.Elements[i] = s.optimize(elem, ctx)
		}
		return node

	case *ast.ObjectLiteral:
		for i := range node.Pairs {
			node.Pairs[i].Key = s.optimize(node.Pairs[i].Key, ctx)
			node.Pairs[i].Value = s.optimize(node.Pairs[i].Value, ctx)
		}
		return node

	case *ast.TernaryExpression:
		return s.optimizeTernary(node, ctx)

	default:
		return expr
	}
}

// optimizeBinary 优化二元表达式
func (s *ShortCircuitOptimization) optimizeBinary(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 首先处理短路逻辑
	if node.Operator == "&&" || node.Operator == "||" {
		return s.optimizeLogical(node, ctx)
	}

	// 对于非逻辑运算符，递归优化两侧
	node.Left = s.optimize(node.Left, ctx)
	node.Right = s.optimize(node.Right, ctx)
	return node
}

// optimizeLogical 优化逻辑表达式
func (s *ShortCircuitOptimization) optimizeLogical(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 我们总是先优化左侧，因为它总是会被计算
	node.Left = s.optimize(node.Left, ctx)

	// 根据左侧的值，可能不需要优化右侧
	if node.Operator == "&&" {
		if isFalse(node.Left) {
			// 如果左侧是false，结果必然是false，无需计算右侧
			return &ast.BooleanLiteral{Value: false, Position: node.Position}
		}
		if isTrue(node.Left) {
			// 如果左侧是true，结果取决于右侧
			node.Right = s.optimize(node.Right, ctx)
			return node.Right
		}
	} else if node.Operator == "||" {
		if isTrue(node.Left) {
			// 如果左侧是true，结果必然是true，无需计算右侧
			return &ast.BooleanLiteral{Value: true, Position: node.Position}
		}
		if isFalse(node.Left) {
			// 如果左侧是false，结果取决于右侧
			node.Right = s.optimize(node.Right, ctx)
			return node.Right
		}
	}

	// 左侧不是常量，优化右侧
	node.Right = s.optimize(node.Right, ctx)

	// 现在检查右侧
	if node.Operator == "&&" {
		if isFalse(node.Right) {
			return &ast.BooleanLiteral{Value: false, Position: node.Position}
		}
		if isTrue(node.Right) {
			return node.Left
		}
	} else if node.Operator == "||" {
		if isTrue(node.Right) {
			return &ast.BooleanLiteral{Value: true, Position: node.Position}
		}
		if isFalse(node.Right) {
			return node.Left
		}
	}

	// 合并相同操作符的嵌套表达式
	if leftBinary, ok := node.Left.(*ast.BinaryExpression); ok && leftBinary.Operator == node.Operator {
		// 将 (a && b) && c 转为 a && (b && c)
		// 这可以使后续的短路优化更有效
		return &ast.BinaryExpression{
			Left:     leftBinary.Left,
			Operator: node.Operator,
			Right: &ast.BinaryExpression{
				Left:     leftBinary.Right,
				Operator: node.Operator,
				Right:    node.Right,
				Position: node.Position,
			},
			Position: node.Position,
		}
	}

	return node
}

// optimizeTernary 优化三元表达式
func (s *ShortCircuitOptimization) optimizeTernary(node *ast.TernaryExpression, ctx *Context) ast.Expression {
	// 先优化条件
	node.Condition = s.optimize(node.Condition, ctx)

	// 根据条件值，可能只需要优化其中一个分支
	if isTrue(node.Condition) {
		return s.optimize(node.TrueExpr, ctx)
	}

	if isFalse(node.Condition) {
		return s.optimize(node.FalseExpr, ctx)
	}

	// 条件不是常量，需要优化两个分支
	node.TrueExpr = s.optimize(node.TrueExpr, ctx)
	node.FalseExpr = s.optimize(node.FalseExpr, ctx)

	// 检查特殊情况
	if isSameExpression(node.TrueExpr, node.FalseExpr) {
		// condition ? x : x => x
		return node.TrueExpr
	}

	// 将 condition ? true : false => condition 和 condition ? false : true => !condition
	// 两种模式的优化始终启用，不受 AggressiveOptimization 影响
	if isTrue(node.TrueExpr) && isFalse(node.FalseExpr) {
		// condition ? true : false => condition
		cond := node.Condition

		// 进一步检查：如果condition是标识符，且其值在上下文中已知，则直接返回布尔字面量
		if ident, ok := cond.(*ast.Identifier); ok && ctx.Variables != nil {
			if val, exists := ctx.Variables[ident.Name]; exists {
				if boolVal, ok := val.(bool); ok {
					return &ast.BooleanLiteral{
						Value:    boolVal,
						Position: node.Position,
					}
				}
			}
		}

		return cond
	}

	if isFalse(node.TrueExpr) && isTrue(node.FalseExpr) {
		// condition ? false : true => !condition
		notExpr := &ast.UnaryExpression{
			Operator: "!",
			Right:    node.Condition,
			Position: node.Position,
		}

		// 进一步检查：如果condition是标识符，且其值在上下文中已知，则直接返回布尔字面量
		if ident, ok := node.Condition.(*ast.Identifier); ok && ctx.Variables != nil {
			if val, exists := ctx.Variables[ident.Name]; exists {
				if boolVal, ok := val.(bool); ok {
					return &ast.BooleanLiteral{
						Value:    !boolVal,
						Position: node.Position,
					}
				}
			}
		}

		return notExpr
	}

	// 检查是否可以转换为其他逻辑表达式（仍保留在激进优化模式）
	if ctx.Config.AggressiveOptimization {
		if isTrue(node.TrueExpr) {
			// condition ? true : x => condition || x
			return &ast.BinaryExpression{
				Left:     node.Condition,
				Operator: "||",
				Right:    node.FalseExpr,
				Position: node.Position,
			}
		}

		if isFalse(node.FalseExpr) {
			// condition ? x : false => condition && x
			return &ast.BinaryExpression{
				Left:     node.Condition,
				Operator: "&&",
				Right:    node.TrueExpr,
				Position: node.Position,
			}
		}
	}

	return node
}
