package optimizer

import (
	"admin/pkg/expr/ast"
	"admin/pkg/expr/eval"
)

// EvaluatorAdapter 将eval.Evaluator适配为Evaluable接口
type EvaluatorAdapter struct {
	evaluator *eval.Evaluator
}

// NewEvaluatorAdapter 创建一个新的评估器适配器
func NewEvaluatorAdapter() *EvaluatorAdapter {
	return &EvaluatorAdapter{
		evaluator: eval.NewEvaluator(),
	}
}

// Evaluate 实现Evaluable接口，对表达式求值
func (e *EvaluatorAdapter) Evaluate(expr ast.Expression) (interface{}, error) {
	// 简单委托给现有的evaluator
	return e.evaluator.Evaluate(expr)
}

// WithVariables 返回一个带变量的Evaluator适配器
func (e *EvaluatorAdapter) WithVariables(vars map[string]interface{}) *EvaluatorAdapter {
	// 创建新的上下文
	ctx := eval.NewContext()

	// 设置变量
	if vars != nil {
		ctx.SetMulti(vars)
	}

	// 使用带有变量的上下文创建新的评估器
	return &EvaluatorAdapter{
		evaluator: eval.WithContext(ctx),
	}
}
