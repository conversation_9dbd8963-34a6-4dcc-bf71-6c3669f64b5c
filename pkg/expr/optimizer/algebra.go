package optimizer

import (
	"admin/pkg/expr/ast"
)

// AlgebraicSimplification 实现代数简化规则
type AlgebraicSimplification struct{}

// NewAlgebraicSimplification 创建一个新的代数简化规则
func NewAlgebraicSimplification() *AlgebraicSimplification {
	return &AlgebraicSimplification{}
}

// Name 返回规则名称
func (a *AlgebraicSimplification) Name() string {
	return "AlgebraicSimplification"
}

// Apply 应用代数简化规则
func (a *AlgebraicSimplification) Apply(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	result := a.simplify(expr, ctx)

	// 记录统计信息
	if result != expr {
		ctx.Stats.RuleApplications[a.Name()]++
	}

	return result
}

// simplify 递归简化表达式
func (a *AlgebraicSimplification) simplify(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	switch node := expr.(type) {
	case *ast.BinaryExpression:
		return a.simplifyBinary(node, ctx)
	case *ast.UnaryExpression:
		return a.simplifyUnary(node, ctx)
	case *ast.CallExpression:
		return a.simplifyCall(node, ctx)
	case *ast.IndexExpression:
		return a.simplifyIndex(node, ctx)
	case *ast.ArrayLiteral:
		return a.simplifyArray(node, ctx)
	case *ast.ObjectLiteral:
		return a.simplifyObject(node, ctx)
	case *ast.TernaryExpression:
		return a.simplifyTernary(node, ctx)
	default:
		return expr
	}
}

// simplifyBinary 简化二元表达式
func (a *AlgebraicSimplification) simplifyBinary(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 先递归简化左右子表达式
	node.Left = a.simplify(node.Left, ctx)
	node.Right = a.simplify(node.Right, ctx)

	// 应用代数简化规则
	switch node.Operator {
	case "+": // 加法简化
		return a.simplifyAddition(node, ctx)
	case "-": // 减法简化
		return a.simplifySubtraction(node, ctx)
	case "*": // 乘法简化
		return a.simplifyMultiplication(node, ctx)
	case "/": // 除法简化
		return a.simplifyDivision(node, ctx)
	case "&&", "||": // 逻辑运算简化
		return a.simplifyLogical(node, ctx)
	case "==", "!=", ">", "<", ">=", "<=": // 比较运算简化
		return a.simplifyComparison(node, ctx)
	default:
		return node
	}
}

// simplifyAddition 简化加法表达式
func (a *AlgebraicSimplification) simplifyAddition(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 规则1: x + 0 = x
	if IsZero(node.Right) {
		return node.Left
	}

	// 规则2: 0 + x = x
	if IsZero(node.Left) {
		return node.Right
	}

	// 规则3: x + (-y) = x - y
	if un, ok := node.Right.(*ast.UnaryExpression); ok && un.Operator == "-" {
		return &ast.BinaryExpression{
			Left:     node.Left,
			Operator: "-",
			Right:    un.Right,
			Position: node.Position,
		}
	}

	// 规则4: (-x) + y = y - x
	if un, ok := node.Left.(*ast.UnaryExpression); ok && un.Operator == "-" {
		return &ast.BinaryExpression{
			Left:     node.Right,
			Operator: "-",
			Right:    un.Right,
			Position: node.Position,
		}
	}

	// 规则5: (x + y) + z = x + (y + z) 当y和z都是常量时合并它们
	if leftBin, ok := node.Left.(*ast.BinaryExpression); ok && leftBin.Operator == "+" {
		if isLiteral(leftBin.Right) && isLiteral(node.Right) {
			// 计算y+z
			sum, err := ctx.Evaluator.Evaluate(&ast.BinaryExpression{
				Left:     leftBin.Right,
				Operator: "+",
				Right:    node.Right,
				Position: node.Position,
			})

			if err == nil {
				return &ast.BinaryExpression{
					Left:     leftBin.Left,
					Operator: "+",
					Right:    convertToLiteral(sum, node.Position),
					Position: node.Position,
				}
			}
		}
	}

	// 规则6: 合并数值常量
	if IsNumberLiteral(node.Left) && IsNumberLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	// 规则7: "str1" + "str2" = "str1str2" (字符串连接)
	if IsStringLiteral(node.Left) && IsStringLiteral(node.Right) {
		left, _ := node.Left.(*ast.StringLiteral)
		right, _ := node.Right.(*ast.StringLiteral)
		return &ast.StringLiteral{
			Value:    left.Value + right.Value,
			Position: node.Position,
		}
	}

	// 规则8: x + x = 2 * x
	if isSameExpression(node.Left, node.Right) {
		return &ast.BinaryExpression{
			Left: &ast.NumberLiteral{
				Value:    2,
				Literal:  "2",
				Position: node.Position,
			},
			Operator: "*",
			Right:    node.Left,
			Position: node.Position,
		}
	}

	return node
}

// simplifySubtraction 简化减法表达式
func (a *AlgebraicSimplification) simplifySubtraction(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 规则1: x - 0 = x
	if IsZero(node.Right) {
		return node.Left
	}

	// 规则2: x - x = 0
	if isSameExpression(node.Left, node.Right) {
		return &ast.NumberLiteral{
			Value:    0,
			Literal:  "0",
			Position: node.Position,
		}
	}

	// 规则3: 0 - x = -x
	if IsZero(node.Left) {
		return &ast.UnaryExpression{
			Operator: "-",
			Right:    node.Right,
			Position: node.Position,
		}
	}

	// 规则4: x - (-y) = x + y
	if un, ok := node.Right.(*ast.UnaryExpression); ok && un.Operator == "-" {
		return &ast.BinaryExpression{
			Left:     node.Left,
			Operator: "+",
			Right:    un.Right,
			Position: node.Position,
		}
	}

	// 规则5: 合并数值常量
	if IsNumberLiteral(node.Left) && IsNumberLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// simplifyMultiplication 简化乘法表达式
func (a *AlgebraicSimplification) simplifyMultiplication(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 规则1: x * 0 = 0
	if IsZero(node.Left) || IsZero(node.Right) {
		return &ast.NumberLiteral{
			Value:    0,
			Literal:  "0",
			Position: node.Position,
		}
	}

	// 规则2: x * 1 = x
	if IsOne(node.Right) {
		return node.Left
	}

	// 规则3: 1 * x = x
	if IsOne(node.Left) {
		return node.Right
	}

	// 规则4: x * (-1) = -x
	if IsMinusOne(node.Right) {
		return &ast.UnaryExpression{
			Operator: "-",
			Right:    node.Left,
			Position: node.Position,
		}
	}

	// 规则5: (-1) * x = -x
	if IsMinusOne(node.Left) {
		return &ast.UnaryExpression{
			Operator: "-",
			Right:    node.Right,
			Position: node.Position,
		}
	}

	// 规则6: (x * y) * z = x * (y * z) 当y和z都是常量时合并它们
	if leftBin, ok := node.Left.(*ast.BinaryExpression); ok && leftBin.Operator == "*" {
		if isLiteral(leftBin.Right) && isLiteral(node.Right) {
			// 计算y*z
			product, err := ctx.Evaluator.Evaluate(&ast.BinaryExpression{
				Left:     leftBin.Right,
				Operator: "*",
				Right:    node.Right,
				Position: node.Position,
			})

			if err == nil {
				return &ast.BinaryExpression{
					Left:     leftBin.Left,
					Operator: "*",
					Right:    convertToLiteral(product, node.Position),
					Position: node.Position,
				}
			}
		}
	}

	// 规则7: 合并数值常量
	if IsNumberLiteral(node.Left) && IsNumberLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	// 规则8: x * x = x^2
	if isSameExpression(node.Left, node.Right) && ctx.Config.AggressiveOptimization {
		// 如果启用了激进优化，可以使用幂运算（如果支持）
		// 这里假设没有内置幂运算，仍然返回 x*x
		return node
	}

	return node
}

// simplifyDivision 简化除法表达式
func (a *AlgebraicSimplification) simplifyDivision(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 规则1: 0 / x = 0 (x != 0)
	if IsZero(node.Left) && !IsZero(node.Right) {
		return &ast.NumberLiteral{
			Value:    0,
			Literal:  "0",
			Position: node.Position,
		}
	}

	// 规则2: x / 1 = x
	if IsOne(node.Right) {
		return node.Left
	}

	// 规则3: x / x = 1 (x != 0)
	if isSameExpression(node.Left, node.Right) && !IsZero(node.Left) {
		return &ast.NumberLiteral{
			Value:    1,
			Literal:  "1",
			Position: node.Position,
		}
	}

	// 规则4: x / (-1) = -x
	if IsMinusOne(node.Right) {
		return &ast.UnaryExpression{
			Operator: "-",
			Right:    node.Left,
			Position: node.Position,
		}
	}

	// 规则5: (-x) / (-y) = x / y
	if unLeft, okLeft := node.Left.(*ast.UnaryExpression); okLeft && unLeft.Operator == "-" {
		if unRight, okRight := node.Right.(*ast.UnaryExpression); okRight && unRight.Operator == "-" {
			return &ast.BinaryExpression{
				Left:     unLeft.Right,
				Operator: "/",
				Right:    unRight.Right,
				Position: node.Position,
			}
		}
	}

	// 规则6: 合并数值常量
	if IsNumberLiteral(node.Left) && IsNumberLiteral(node.Right) {
		// 除以零检查
		if numRight, ok := node.Right.(*ast.NumberLiteral); ok && numRight.Value == 0 {
			return node // 不优化除以零的情况
		}

		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// simplifyLogical 简化逻辑表达式
func (a *AlgebraicSimplification) simplifyLogical(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	switch node.Operator {
	case "&&":
		// 规则1: true && x = x
		if isTrue(node.Left) {
			return node.Right
		}

		// 规则2: x && true = x
		if isTrue(node.Right) {
			return node.Left
		}

		// 规则3: false && x = false
		if isFalse(node.Left) {
			return &ast.BooleanLiteral{
				Value:    false,
				Position: node.Position,
			}
		}

		// 规则4: x && false = false
		if isFalse(node.Right) {
			return &ast.BooleanLiteral{
				Value:    false,
				Position: node.Position,
			}
		}

		// 规则5: x && x = x
		if isSameExpression(node.Left, node.Right) {
			return node.Left
		}

		// 规则6: x && !x = false
		if un, ok := node.Right.(*ast.UnaryExpression); ok && un.Operator == "!" && isSameExpression(node.Left, un.Right) {
			return &ast.BooleanLiteral{
				Value:    false,
				Position: node.Position,
			}
		}
		if un, ok := node.Left.(*ast.UnaryExpression); ok && un.Operator == "!" && isSameExpression(un.Right, node.Right) {
			return &ast.BooleanLiteral{
				Value:    false,
				Position: node.Position,
			}
		}

	case "||":
		// 规则1: false || x = x
		if isFalse(node.Left) {
			return node.Right
		}

		// 规则2: x || false = x
		if isFalse(node.Right) {
			return node.Left
		}

		// 规则3: true || x = true
		if isTrue(node.Left) {
			return &ast.BooleanLiteral{
				Value:    true,
				Position: node.Position,
			}
		}

		// 规则4: x || true = true
		if isTrue(node.Right) {
			return &ast.BooleanLiteral{
				Value:    true,
				Position: node.Position,
			}
		}

		// 规则5: x || x = x
		if isSameExpression(node.Left, node.Right) {
			return node.Left
		}

		// 规则6: x || !x = true
		if un, ok := node.Right.(*ast.UnaryExpression); ok && un.Operator == "!" && isSameExpression(node.Left, un.Right) {
			return &ast.BooleanLiteral{
				Value:    true,
				Position: node.Position,
			}
		}
		if un, ok := node.Left.(*ast.UnaryExpression); ok && un.Operator == "!" && isSameExpression(un.Right, node.Right) {
			return &ast.BooleanLiteral{
				Value:    true,
				Position: node.Position,
			}
		}
	}

	// 合并布尔常量
	if IsBooleanLiteral(node.Left) && IsBooleanLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// simplifyComparison 简化比较表达式
func (a *AlgebraicSimplification) simplifyComparison(node *ast.BinaryExpression, ctx *Context) ast.Expression {
	// 规则1: x == x = true (除非x是NaN)
	if node.Operator == "==" && isSameExpression(node.Left, node.Right) && !ctx.Config.AggressiveOptimization {
		return &ast.BooleanLiteral{
			Value:    true,
			Position: node.Position,
		}
	}

	// 规则2: x != x = false (除非x是NaN)
	if node.Operator == "!=" && isSameExpression(node.Left, node.Right) && !ctx.Config.AggressiveOptimization {
		return &ast.BooleanLiteral{
			Value:    false,
			Position: node.Position,
		}
	}

	// 规则3: 合并常量比较
	if isLiteral(node.Left) && isLiteral(node.Right) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	return node
}

// simplifyUnary 简化一元表达式
func (a *AlgebraicSimplification) simplifyUnary(node *ast.UnaryExpression, ctx *Context) ast.Expression {
	// 先简化子表达式
	node.Right = a.simplify(node.Right, ctx)

	switch node.Operator {
	case "-":
		// 规则1: -(-x) = x
		if un, ok := node.Right.(*ast.UnaryExpression); ok && un.Operator == "-" {
			return un.Right
		}

		// 规则2: -(0) = 0
		if IsZero(node.Right) {
			return node.Right // 返回0
		}

		// 规则3: 合并常量
		if IsNumberLiteral(node.Right) {
			result, err := ctx.Evaluator.Evaluate(node)
			if err == nil {
				return convertToLiteral(result, node.Position)
			}
		}

	case "!":
		// 规则1: !(true) = false
		if isTrue(node.Right) {
			return &ast.BooleanLiteral{
				Value:    false,
				Position: node.Position,
			}
		}

		// 规则2: !(false) = true
		if isFalse(node.Right) {
			return &ast.BooleanLiteral{
				Value:    true,
				Position: node.Position,
			}
		}

		// 规则3: !(!x) = x
		if un, ok := node.Right.(*ast.UnaryExpression); ok && un.Operator == "!" {
			return un.Right
		}

		// 规则4: 布尔操作符的德摩根律
		if bin, ok := node.Right.(*ast.BinaryExpression); ok {
			if bin.Operator == "&&" {
				// !(a && b) = !a || !b
				return &ast.BinaryExpression{
					Left: &ast.UnaryExpression{
						Operator: "!",
						Right:    bin.Left,
						Position: bin.Position,
					},
					Operator: "||",
					Right: &ast.UnaryExpression{
						Operator: "!",
						Right:    bin.Right,
						Position: bin.Position,
					},
					Position: node.Position,
				}
			} else if bin.Operator == "||" {
				// !(a || b) = !a && !b
				return &ast.BinaryExpression{
					Left: &ast.UnaryExpression{
						Operator: "!",
						Right:    bin.Left,
						Position: bin.Position,
					},
					Operator: "&&",
					Right: &ast.UnaryExpression{
						Operator: "!",
						Right:    bin.Right,
						Position: bin.Position,
					},
					Position: node.Position,
				}
			} else if bin.Operator == "==" {
				// !(a == b) = a != b
				bin.Operator = "!="
				return bin
			} else if bin.Operator == "!=" {
				// !(a != b) = a == b
				bin.Operator = "=="
				return bin
			} else if bin.Operator == "<" {
				// !(a < b) = a >= b
				bin.Operator = ">="
				return bin
			} else if bin.Operator == ">" {
				// !(a > b) = a <= b
				bin.Operator = "<="
				return bin
			} else if bin.Operator == "<=" {
				// !(a <= b) = a > b
				bin.Operator = ">"
				return bin
			} else if bin.Operator == ">=" {
				// !(a >= b) = a < b
				bin.Operator = "<"
				return bin
			}
		}
	}

	return node
}

// simplifyCall 简化函数调用表达式
func (a *AlgebraicSimplification) simplifyCall(node *ast.CallExpression, ctx *Context) ast.Expression {
	// 简化函数表达式
	node.Function = a.simplify(node.Function, ctx)

	// 简化参数
	for i, arg := range node.Arguments {
		node.Arguments[i] = a.simplify(arg, ctx)
	}

	return node
}

// simplifyIndex 简化索引表达式
func (a *AlgebraicSimplification) simplifyIndex(node *ast.IndexExpression, ctx *Context) ast.Expression {
	// 简化左侧和索引表达式
	node.Left = a.simplify(node.Left, ctx)
	node.Index = a.simplify(node.Index, ctx)

	return node
}

// simplifyArray 简化数组表达式
func (a *AlgebraicSimplification) simplifyArray(node *ast.ArrayLiteral, ctx *Context) ast.Expression {
	// 简化所有元素
	for i, elem := range node.Elements {
		node.Elements[i] = a.simplify(elem, ctx)
	}

	return node
}

// simplifyObject 简化对象表达式
func (a *AlgebraicSimplification) simplifyObject(node *ast.ObjectLiteral, ctx *Context) ast.Expression {
	// 简化所有键值对
	for i, pair := range node.Pairs {
		node.Pairs[i].Key = a.simplify(pair.Key, ctx)
		node.Pairs[i].Value = a.simplify(pair.Value, ctx)
	}

	return node
}

// simplifyTernary 简化三元表达式
func (a *AlgebraicSimplification) simplifyTernary(node *ast.TernaryExpression, ctx *Context) ast.Expression {
	// 简化条件和分支
	node.Condition = a.simplify(node.Condition, ctx)
	node.TrueExpr = a.simplify(node.TrueExpr, ctx)
	node.FalseExpr = a.simplify(node.FalseExpr, ctx)

	// 规则1: 条件为常量的情况
	if isTrue(node.Condition) {
		return node.TrueExpr
	}

	if isFalse(node.Condition) {
		return node.FalseExpr
	}

	// 规则2: 两个分支相同
	if isSameExpression(node.TrueExpr, node.FalseExpr) {
		return node.TrueExpr
	}

	// 规则3: 分支为条件表达式
	if isSameExpression(node.Condition, node.TrueExpr) && ctx.Config.AggressiveOptimization {
		// condition ? condition : falseExpr => condition || falseExpr
		return &ast.BinaryExpression{
			Left:     node.Condition,
			Operator: "||",
			Right:    node.FalseExpr,
			Position: node.Position,
		}
	}

	// 规则4: 条件取反，交换分支
	if un, ok := node.Condition.(*ast.UnaryExpression); ok && un.Operator == "!" {
		return &ast.TernaryExpression{
			Condition: un.Right,
			FalseExpr: node.TrueExpr,
			TrueExpr:  node.FalseExpr,
			Position:  node.Position,
		}
	}

	return node
}
