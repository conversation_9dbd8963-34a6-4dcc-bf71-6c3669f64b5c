package optimizer

import (
	"admin/pkg/expr/ast"
	"admin/pkg/expr/lexer"
	"admin/pkg/expr/parser"
)

// OptimizeExpression 便捷函数，使用默认配置优化表达式
func OptimizeExpression(expr ast.Expression) ast.Expression {
	evaluator := NewEvaluatorAdapter()
	opt := New(evaluator)
	return opt.Optimize(expr, nil, DefaultConfig())
}

// OptimizeWithVariables 便捷函数，使用变量信息优化表达式
func OptimizeWithVariables(expr ast.Expression, variables map[string]interface{}) ast.Expression {
	evaluator := NewEvaluatorAdapter().WithVariables(variables)
	opt := New(evaluator)
	return opt.Optimize(expr, variables, DefaultConfig())
}

// OptimizeWithConfig 便捷函数，使用指定配置优化表达式
func OptimizeWithConfig(expr ast.Expression, config *Config) ast.Expression {
	evaluator := NewEvaluatorAdapter()
	opt := New(evaluator)
	return opt.Optimize(expr, nil, config)
}

// OptimizeComplete 便捷函数，提供完整的优化选项
func OptimizeComplete(expr ast.Expression, variables map[string]interface{}, config *Config) ast.Expression {
	evaluator := NewEvaluatorAdapter().WithVariables(variables)
	opt := New(evaluator)
	return opt.Optimize(expr, variables, config)
}

// CompileAndOptimize 便捷函数，解析字符串表达式并优化
func CompileAndOptimize(expression string) (ast.Expression, error) {
	// 解析表达式
	l := lexer.New(expression)
	p := parser.New(l)

	expr, err := p.ParseExpression()
	if err != nil {
		return nil, err
	}

	// 创建优化配置，启用激进优化
	config := DefaultConfig()
	config.AggressiveOptimization = true

	// 优化
	evaluator := NewEvaluatorAdapter()
	opt := New(evaluator)
	return opt.Optimize(expr, nil, config), nil
}

// CompileAndOptimizeWithVariables 便捷函数，解析字符串表达式并使用变量信息优化
func CompileAndOptimizeWithVariables(expression string, variables map[string]any) (ast.Expression, error) {
	// 解析表达式
	l := lexer.New(expression)
	p := parser.New(l)

	expr, err := p.ParseExpression()
	if err != nil {
		return nil, err
	}

	// 创建优化配置，启用激进优化
	config := DefaultConfig()
	config.AggressiveOptimization = true

	// 使用变量优化
	evaluator := NewEvaluatorAdapter().WithVariables(variables)
	opt := New(evaluator)
	return opt.Optimize(expr, variables, config), nil
}

// 验证表达式
func ValidateExpression(expression string) error {
	// 解析表达式
	l := lexer.New(expression)
	p := parser.New(l)

	_, err := p.ParseExpression()
	return err
}
