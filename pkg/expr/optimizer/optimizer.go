// Package optimizer 为表达式引擎提供高级优化功能
package optimizer

import (
	"admin/pkg/expr/ast"
	"admin/pkg/expr/eval"
	"admin/pkg/expr/lexer"
	"admin/pkg/expr/parser"
)

// Evaluable 表达式求值接口
type Evaluable interface {
	// Evaluate 对表达式进行求值
	Evaluate(expr ast.Expression) (interface{}, error)
}

// Rule 表示一个优化规则
type Rule interface {
	// Name 返回规则名称
	Name() string
	// Apply 应用优化规则到表达式
	Apply(expr ast.Expression, ctx *Context) ast.Expression
}

// Context 表示优化上下文
type Context struct {
	// Variables 表示变量值映射
	Variables map[string]interface{}
	// Config 优化器配置
	Config *Config
	// Evaluator 表达式求值器
	Evaluator Evaluable
	// ExprMap 临时表达式映射，用于优化过程
	ExprMap map[string]ast.Expression
	// Stats 优化统计信息
	Stats *Stats
}

// Stats 优化统计信息
type Stats struct {
	// RuleApplications 记录规则应用次数
	RuleApplications map[string]int
	// Passes 优化通道次数
	Passes int
	// NodesSaved 节省的节点数量
	NodesSaved int
}

// Config 优化器配置
type Config struct {
	// MaxPasses 最大优化通道次数
	MaxPasses int
	// AggressiveOptimization 是否进行激进优化（可能会改变NaN行为）
	AggressiveOptimization bool
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		MaxPasses:              3,
		AggressiveOptimization: false,
	}
}

// Optimizer 表达式优化器
type Optimizer struct {
	rules     []Rule
	evaluator Evaluable
	stats     *Stats // 添加统计信息字段
}

// New 创建一个新的优化器，包含所有标准优化规则
func New(evaluator Evaluable) *Optimizer {
	opt := &Optimizer{
		evaluator: evaluator,
		stats: &Stats{
			RuleApplications: make(map[string]int),
		},
	}

	// 添加所有优化规则，按优先级排序
	opt.rules = append(opt.rules, NewConstantFolding())

	// 添加代数简化规则
	opt.rules = append(opt.rules, NewAlgebraicSimplification())
	// 添加短路优化规则
	opt.rules = append(opt.rules, NewShortCircuitOptimization())

	return opt
}

// NewWithRules 创建一个自定义规则的优化器
func NewWithRules(evaluator Evaluable, rules []Rule) *Optimizer {
	return &Optimizer{
		rules:     rules,
		evaluator: evaluator,
		stats: &Stats{
			RuleApplications: make(map[string]int),
		},
	}
}

// Optimize 优化表达式
func (o *Optimizer) Optimize(expr ast.Expression, variables map[string]interface{}, config *Config) ast.Expression {
	if expr == nil {
		return nil
	}

	// 使用默认配置
	if config == nil {
		config = DefaultConfig()
	}

	// 重置统计信息
	o.stats = &Stats{
		RuleApplications: make(map[string]int),
		Passes:           0,
		NodesSaved:       0,
	}

	// 创建优化上下文
	ctx := &Context{
		Variables: variables,
		Config:    config,
		Evaluator: o.evaluator,
		ExprMap:   make(map[string]ast.Expression),
		Stats:     o.stats,
	}

	// 应用多轮优化，直到表达式稳定或达到最大通道次数
	result := expr
	originalNodeCount := countNodes(expr)

	for range config.MaxPasses {
		ctx.Stats.Passes++
		prevExpr := result

		// 应用所有优化规则
		for _, rule := range o.rules {
			result = rule.Apply(result, ctx)
		}

		// 如果表达式不再改变，提前结束
		if prevExpr.String() == result.String() {
			break
		}
	}

	// 计算节省的节点数
	finalNodeCount := countNodes(result)
	ctx.Stats.NodesSaved = originalNodeCount - finalNodeCount

	return result
}

// GetStats 获取最近一次优化的统计信息
func (o *Optimizer) GetStats() *Stats {
	return o.stats
}

// countNodes 计算表达式树中的节点数量
func countNodes(expr ast.Expression) int {
	if expr == nil {
		return 0
	}

	count := 1 // 当前节点

	switch node := expr.(type) {
	case *ast.BinaryExpression:
		count += countNodes(node.Left)
		count += countNodes(node.Right)
	case *ast.UnaryExpression:
		count += countNodes(node.Right)
	case *ast.CallExpression:
		for _, arg := range node.Arguments {
			count += countNodes(arg)
		}
		count += countNodes(node.Function)
	case *ast.IndexExpression:
		count += countNodes(node.Left)
		count += countNodes(node.Index)
	case *ast.ArrayLiteral:
		for _, elem := range node.Elements {
			count += countNodes(elem)
		}
	case *ast.ObjectLiteral:
		for _, pair := range node.Pairs {
			count += countNodes(pair.Key)
			count += countNodes(pair.Value)
		}
	case *ast.TernaryExpression:
		count += countNodes(node.Condition)
		count += countNodes(node.TrueExpr)
		count += countNodes(node.FalseExpr)
	}

	return count
}

// CompileAndOptimizeExpression 编译、优化并缓存表达式
func CompileAndOptimizeExpression(expression string) (ast.Expression, error) {
	// 首先从缓存获取
	if expr := eval.GlobalExpressionCache.Get(expression); expr != nil {
		return expr, nil
	}

	// 解析表达式
	l := lexer.New(expression)
	p := parser.New(l)

	expr, err := p.ParseExpression()
	if err != nil {
		return nil, err
	}

	// 使用新的优化器优化表达式
	optimizedExpr := OptimizeExpression(expr)

	// 存入缓存
	eval.GlobalExpressionCache.Set(expression, optimizedExpr)

	return optimizedExpr, nil
}

// EvaluateOptimized 使用优化后的表达式计算结果
func EvaluateOptimized(expression string, variables map[string]interface{}) (interface{}, error) {
	// 编译并优化表达式
	expr, err := CompileAndOptimizeExpression(expression)
	if err != nil {
		return nil, err
	}

	// 计算优化后的表达式
	return eval.EvaluateExpression(expr, variables)
}
