package optimizer

import (
	"fmt"

	"admin/pkg/expr/ast"
)

// convertToLiteral 将值转换为AST字面量节点
func convertToLiteral(val interface{}, pos ast.Position) ast.Expression {
	switch v := val.(type) {
	case float64:
		return &ast.NumberLiteral{
			Value:    v,
			Literal:  formatNumber(v),
			Position: pos,
		}
	case int:
		return &ast.NumberLiteral{
			Value:    float64(v),
			Literal:  formatNumber(float64(v)),
			Position: pos,
		}
	case string:
		return &ast.StringLiteral{
			Value:    v,
			Position: pos,
		}
	case bool:
		return &ast.BooleanLiteral{
			Value:    v,
			Position: pos,
		}
	case nil:
		return &ast.StringLiteral{
			Value:    "null",
			Position: pos,
		}
	default:
		// 无法处理的类型
		return nil
	}
}

// formatNumber 格式化数字为字符串
func formatNumber(num float64) string {
	if num == float64(int(num)) {
		return fmt.Sprintf("%d", int(num))
	}
	return fmt.Sprintf("%g", num)
}

// isLiteral 判断一个表达式节点是否是字面量
func isLiteral(expr ast.Expression) bool {
	switch expr.(type) {
	case *ast.NumberLiteral, *ast.StringLiteral, *ast.BooleanLiteral:
		return true
	default:
		return false
	}
}

// isTrue 检查表达式是否为布尔真值
func isTrue(expr ast.Expression) bool {
	if bl, ok := expr.(*ast.BooleanLiteral); ok {
		return bl.Value
	}
	return false
}

// isFalse 检查表达式是否为布尔假值
func isFalse(expr ast.Expression) bool {
	if bl, ok := expr.(*ast.BooleanLiteral); ok {
		return !bl.Value
	}
	return false
}

// isSameExpression 判断两个表达式是否相同(简化版)
func isSameExpression(expr1, expr2 ast.Expression) bool {
	// 如果都是字面量，比较它们的值
	if literal1, ok1 := expr1.(*ast.StringLiteral); ok1 {
		if literal2, ok2 := expr2.(*ast.StringLiteral); ok2 {
			return literal1.Value == literal2.Value
		}
	}
	if literal1, ok1 := expr1.(*ast.NumberLiteral); ok1 {
		if literal2, ok2 := expr2.(*ast.NumberLiteral); ok2 {
			return literal1.Value == literal2.Value
		}
	}
	if literal1, ok1 := expr1.(*ast.BooleanLiteral); ok1 {
		if literal2, ok2 := expr2.(*ast.BooleanLiteral); ok2 {
			return literal1.Value == literal2.Value
		}
	}

	// 简单比较字符串表示
	return expr1.String() == expr2.String()
}

// IsZero 检查表达式是否为数字0
func IsZero(expr ast.Expression) bool {
	if num, ok := expr.(*ast.NumberLiteral); ok {
		return num.Value == 0
	}
	return false
}

// IsOne 检查表达式是否为数字1
func IsOne(expr ast.Expression) bool {
	if num, ok := expr.(*ast.NumberLiteral); ok {
		return num.Value == 1
	}
	return false
}

// IsMinusOne 检查表达式是否为数字-1
func IsMinusOne(expr ast.Expression) bool {
	if num, ok := expr.(*ast.NumberLiteral); ok {
		return num.Value == -1
	}
	return false
}

// IsNumberLiteral 检查是否为数字字面量
func IsNumberLiteral(expr ast.Expression) bool {
	_, ok := expr.(*ast.NumberLiteral)
	return ok
}

// IsStringLiteral 检查是否为字符串字面量
func IsStringLiteral(expr ast.Expression) bool {
	_, ok := expr.(*ast.StringLiteral)
	return ok
}

// IsBooleanLiteral 检查是否为布尔字面量
func IsBooleanLiteral(expr ast.Expression) bool {
	_, ok := expr.(*ast.BooleanLiteral)
	return ok
}
