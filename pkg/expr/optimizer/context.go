package optimizer

import (
	"admin/pkg/expr/ast"
)

// ContextualOptimization 实现基于上下文的优化策略
type ContextualOptimization struct{}

// NewContextualOptimization 创建一个新的上下文相关优化实例
func NewContextualOptimization() *ContextualOptimization {
	return &ContextualOptimization{}
}

// Name 返回规则名称
func (c *ContextualOptimization) Name() string {
	return "ContextualOptimization"
}

// Apply 应用上下文相关优化
func (c *ContextualOptimization) Apply(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil || ctx.Variables == nil || len(ctx.Variables) == 0 {
		return expr
	}

	// 分析变量信息
	typeInfo := c.analyzeVariableTypes(ctx.Variables)

	// 应用优化
	optimized := c.optimize(expr, typeInfo, ctx)

	// 记录统计信息
	if optimized != expr {
		ctx.Stats.RuleApplications[c.Name()]++
	}

	return optimized
}

// 变量类型信息，用于上下文相关优化
type TypeInfo struct {
	// 变量类型映射
	Types map[string]string

	// 数值变量的范围信息
	NumericRanges map[string]*NumericRange

	// 字符串长度信息
	StringLengths map[string]*LengthRange

	// 布尔变量的常量值（如果已知）
	BooleanValues map[string]*BoolValue
}

// NumericRange 表示数值的范围信息
type NumericRange struct {
	Min       float64
	Max       float64
	IsInteger bool
	IsExact   bool // 表示是否是精确值
}

// LengthRange 表示字符串长度的范围信息
type LengthRange struct {
	MinLength int
	MaxLength int
	IsExact   bool // 表示是否是精确长度
}

// BoolValue 表示布尔值信息
type BoolValue struct {
	Value   bool
	IsExact bool // 表示是否是精确值
}

// analyzeVariableTypes 分析变量类型信息
func (c *ContextualOptimization) analyzeVariableTypes(variables map[string]interface{}) *TypeInfo {
	typeInfo := &TypeInfo{
		Types:         make(map[string]string),
		NumericRanges: make(map[string]*NumericRange),
		StringLengths: make(map[string]*LengthRange),
		BooleanValues: make(map[string]*BoolValue),
	}

	for name, value := range variables {
		switch v := value.(type) {
		case float64:
			typeInfo.Types[name] = "number"
			typeInfo.NumericRanges[name] = &NumericRange{
				Min:       v,
				Max:       v,
				IsInteger: v == float64(int64(v)),
				IsExact:   true,
			}

		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
			// 将整数值转换为float64
			var floatVal float64
			switch iv := v.(type) {
			case int:
				floatVal = float64(iv)
			case int8:
				floatVal = float64(iv)
			case int16:
				floatVal = float64(iv)
			case int32:
				floatVal = float64(iv)
			case int64:
				floatVal = float64(iv)
			case uint:
				floatVal = float64(iv)
			case uint8:
				floatVal = float64(iv)
			case uint16:
				floatVal = float64(iv)
			case uint32:
				floatVal = float64(iv)
			case uint64:
				floatVal = float64(iv)
			}

			typeInfo.Types[name] = "number"
			typeInfo.NumericRanges[name] = &NumericRange{
				Min:       floatVal,
				Max:       floatVal,
				IsInteger: true,
				IsExact:   true,
			}

		case string:
			typeInfo.Types[name] = "string"
			typeInfo.StringLengths[name] = &LengthRange{
				MinLength: len(v),
				MaxLength: len(v),
				IsExact:   true,
			}

		case bool:
			typeInfo.Types[name] = "boolean"
			typeInfo.BooleanValues[name] = &BoolValue{
				Value:   v,
				IsExact: true,
			}

		case []interface{}:
			typeInfo.Types[name] = "array"

		case map[string]interface{}:
			typeInfo.Types[name] = "object"

		case nil:
			typeInfo.Types[name] = "null"

		default:
			// 对于其他类型，保守处理
			typeInfo.Types[name] = "unknown"
		}
	}

	return typeInfo
}

// optimize 对表达式应用上下文相关优化
func (c *ContextualOptimization) optimize(expr ast.Expression, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	switch node := expr.(type) {
	case *ast.BinaryExpression:
		return c.optimizeBinary(node, typeInfo, ctx)

	case *ast.UnaryExpression:
		return c.optimizeUnary(node, typeInfo, ctx)

	case *ast.CallExpression:
		return c.optimizeCall(node, typeInfo, ctx)

	case *ast.IndexExpression:
		return c.optimizeIndex(node, typeInfo, ctx)

	case *ast.TernaryExpression:
		return c.optimizeTernary(node, typeInfo, ctx)

	case *ast.Identifier:
		return c.optimizeIdentifier(node, typeInfo, ctx)

	case *ast.ArrayLiteral:
		// 优化数组的每个元素
		for i, elem := range node.Elements {
			node.Elements[i] = c.optimize(elem, typeInfo, ctx)
		}
		return node

	case *ast.ObjectLiteral:
		// 优化对象的每个键值对
		for i, pair := range node.Pairs {
			node.Pairs[i].Key = c.optimize(pair.Key, typeInfo, ctx)
			node.Pairs[i].Value = c.optimize(pair.Value, typeInfo, ctx)
		}
		return node

	default:
		return expr
	}
}

// optimizeBinary 优化二元表达式
func (c *ContextualOptimization) optimizeBinary(node *ast.BinaryExpression, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	// 首先递归优化左右子表达式
	node.Left = c.optimize(node.Left, typeInfo, ctx)
	node.Right = c.optimize(node.Right, typeInfo, ctx)

	// 1. 基于类型的优化：处理不同类型操作数间的运算
	if c.canEvaluateToConstant(node.Left, typeInfo) && c.canEvaluateToConstant(node.Right, typeInfo) {
		// 如果两个操作数都可以计算为常量，尝试直接计算
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	// 2. 针对数值范围的优化
	if node.Operator == "<" || node.Operator == "<=" ||
		node.Operator == ">" || node.Operator == ">=" ||
		node.Operator == "==" || node.Operator == "!=" {
		// 检查比较表达式是否可以简化
		if simplified := c.simplifyComparison(node, typeInfo); simplified != nil {
			return simplified
		}
	}

	// 3. 基于变量常量值的优化
	if ident, ok := node.Left.(*ast.Identifier); ok {
		if boolValue, exists := typeInfo.BooleanValues[ident.Name]; exists && boolValue.IsExact {
			// 处理确定布尔值的变量
			if node.Operator == "&&" {
				if boolValue.Value {
					return node.Right // true && x => x
				} else {
					return &ast.BooleanLiteral{Value: false, Position: node.Position} // false && x => false
				}
			} else if node.Operator == "||" {
				if boolValue.Value {
					return &ast.BooleanLiteral{Value: true, Position: node.Position} // true || x => true
				} else {
					return node.Right // false || x => x
				}
			}
		}
	}

	if ident, ok := node.Right.(*ast.Identifier); ok {
		if boolValue, exists := typeInfo.BooleanValues[ident.Name]; exists && boolValue.IsExact {
			// 处理确定布尔值的变量
			if node.Operator == "&&" {
				if boolValue.Value {
					return node.Left // x && true => x
				} else {
					return &ast.BooleanLiteral{Value: false, Position: node.Position} // x && false => false
				}
			} else if node.Operator == "||" {
				if boolValue.Value {
					return &ast.BooleanLiteral{Value: true, Position: node.Position} // x || true => true
				} else {
					return node.Left // x || false => x
				}
			}
		}
	}

	// 4. 字符串连接优化
	if node.Operator == "+" {
		if leftStr, leftOk := node.Left.(*ast.StringLiteral); leftOk {
			if rightIdent, rightOk := node.Right.(*ast.Identifier); rightOk {
				if lengthInfo, exists := typeInfo.StringLengths[rightIdent.Name]; exists && lengthInfo.IsExact && lengthInfo.MinLength == 0 {
					// "abc" + "" => "abc"
					return leftStr
				}
			}
		}
		if rightStr, rightOk := node.Right.(*ast.StringLiteral); rightOk {
			if leftIdent, leftOk := node.Left.(*ast.Identifier); leftOk {
				if lengthInfo, exists := typeInfo.StringLengths[leftIdent.Name]; exists && lengthInfo.IsExact && lengthInfo.MinLength == 0 {
					// "" + "abc" => "abc"
					return rightStr
				}
			}
		}
	}

	return node
}

// optimizeUnary 优化一元表达式
func (c *ContextualOptimization) optimizeUnary(node *ast.UnaryExpression, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	// 递归优化子表达式
	node.Right = c.optimize(node.Right, typeInfo, ctx)

	// 如果操作数可以计算为常量，尝试直接计算
	if c.canEvaluateToConstant(node.Right, typeInfo) {
		result, err := ctx.Evaluator.Evaluate(node)
		if err == nil {
			return convertToLiteral(result, node.Position)
		}
	}

	// 处理确定布尔值的变量
	if node.Operator == "!" {
		if ident, ok := node.Right.(*ast.Identifier); ok {
			if boolValue, exists := typeInfo.BooleanValues[ident.Name]; exists && boolValue.IsExact {
				return &ast.BooleanLiteral{
					Value:    !boolValue.Value,
					Position: node.Position,
				}
			}
		}
	}

	return node
}

// optimizeCall 优化函数调用表达式
func (c *ContextualOptimization) optimizeCall(node *ast.CallExpression, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	// 优化函数参数
	for i, arg := range node.Arguments {
		node.Arguments[i] = c.optimize(arg, typeInfo, ctx)
	}

	// 如果是已知的纯函数，并且所有参数都是常量，尝试计算
	if ident, ok := node.Function.(*ast.Identifier); ok {
		switch ident.Name {
		case "length":
			// 优化length函数调用，如果参数是已知长度的字符串
			if len(node.Arguments) == 1 {
				if argIdent, ok := node.Arguments[0].(*ast.Identifier); ok {
					if lengthInfo, exists := typeInfo.StringLengths[argIdent.Name]; exists && lengthInfo.IsExact {
						return &ast.NumberLiteral{
							Value:    float64(lengthInfo.MinLength),
							Literal:  formatNumber(float64(lengthInfo.MinLength)),
							Position: node.Position,
						}
					}
				}
			}
		}
	}

	return node
}

// optimizeIndex 优化索引表达式
func (c *ContextualOptimization) optimizeIndex(node *ast.IndexExpression, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	// 优化左侧和索引表达式
	node.Left = c.optimize(node.Left, typeInfo, ctx)
	node.Index = c.optimize(node.Index, typeInfo, ctx)

	return node
}

// optimizeTernary 优化三元表达式
func (c *ContextualOptimization) optimizeTernary(node *ast.TernaryExpression, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	// 优化条件表达式
	node.Condition = c.optimize(node.Condition, typeInfo, ctx)

	// 如果条件是常量或已知布尔值的变量，可以简化
	if isTrue(node.Condition) {
		return c.optimize(node.TrueExpr, typeInfo, ctx)
	}
	if isFalse(node.Condition) {
		return c.optimize(node.FalseExpr, typeInfo, ctx)
	}

	// 如果条件是标识符，检查其是否为已知布尔值
	if ident, ok := node.Condition.(*ast.Identifier); ok {
		if boolValue, exists := typeInfo.BooleanValues[ident.Name]; exists && boolValue.IsExact {
			if boolValue.Value {
				return c.optimize(node.TrueExpr, typeInfo, ctx)
			} else {
				return c.optimize(node.FalseExpr, typeInfo, ctx)
			}
		}
	}

	// 优化两个分支
	node.TrueExpr = c.optimize(node.TrueExpr, typeInfo, ctx)
	node.FalseExpr = c.optimize(node.FalseExpr, typeInfo, ctx)

	// 如果两个分支相同，直接返回其中一个
	if isSameExpression(node.TrueExpr, node.FalseExpr) {
		return node.TrueExpr
	}

	return node
}

// optimizeIdentifier 优化标识符表达式
func (c *ContextualOptimization) optimizeIdentifier(node *ast.Identifier, typeInfo *TypeInfo, ctx *Context) ast.Expression {
	// 如果变量有确切值，可以直接替换为字面量
	if numRange, exists := typeInfo.NumericRanges[node.Name]; exists && numRange.IsExact {
		return &ast.NumberLiteral{
			Value:    numRange.Min, // Min和Max相等
			Literal:  formatNumber(numRange.Min),
			Position: node.Position,
		}
	}

	if boolValue, exists := typeInfo.BooleanValues[node.Name]; exists && boolValue.IsExact {
		return &ast.BooleanLiteral{
			Value:    boolValue.Value,
			Position: node.Position,
		}
	}

	// 注意：字符串变量通常不直接替换为字面量，因为可能很大

	return node
}

// simplifyComparison 简化比较表达式
func (c *ContextualOptimization) simplifyComparison(node *ast.BinaryExpression, typeInfo *TypeInfo) ast.Expression {
	// 处理x < y的情况，其中x和y的范围信息已知
	leftIdent, leftOk := node.Left.(*ast.Identifier)
	rightIdent, rightOk := node.Right.(*ast.Identifier)

	if leftOk && rightOk {
		leftRange, leftExists := typeInfo.NumericRanges[leftIdent.Name]
		rightRange, rightExists := typeInfo.NumericRanges[rightIdent.Name]

		if leftExists && rightExists && leftRange.IsExact && rightRange.IsExact {
			// 如果两个变量都是确切的数值，可以直接计算比较结果
			switch node.Operator {
			case "<":
				return &ast.BooleanLiteral{
					Value:    leftRange.Min < rightRange.Min,
					Position: node.Position,
				}
			case "<=":
				return &ast.BooleanLiteral{
					Value:    leftRange.Min <= rightRange.Min,
					Position: node.Position,
				}
			case ">":
				return &ast.BooleanLiteral{
					Value:    leftRange.Min > rightRange.Min,
					Position: node.Position,
				}
			case ">=":
				return &ast.BooleanLiteral{
					Value:    leftRange.Min >= rightRange.Min,
					Position: node.Position,
				}
			case "==":
				return &ast.BooleanLiteral{
					Value:    leftRange.Min == rightRange.Min,
					Position: node.Position,
				}
			case "!=":
				return &ast.BooleanLiteral{
					Value:    leftRange.Min != rightRange.Min,
					Position: node.Position,
				}
			}
		}

		// 检查范围不重叠的情况
		if leftExists && rightExists {
			if node.Operator == "<" && leftRange.Max < rightRange.Min {
				return &ast.BooleanLiteral{Value: true, Position: node.Position}
			}
			if node.Operator == ">" && leftRange.Min > rightRange.Max {
				return &ast.BooleanLiteral{Value: true, Position: node.Position}
			}
			if node.Operator == "<=" && leftRange.Max <= rightRange.Min {
				return &ast.BooleanLiteral{Value: true, Position: node.Position}
			}
			if node.Operator == ">=" && leftRange.Min >= rightRange.Max {
				return &ast.BooleanLiteral{Value: true, Position: node.Position}
			}

			// 检查范围不可能满足条件的情况
			if node.Operator == "<" && leftRange.Min >= rightRange.Max {
				return &ast.BooleanLiteral{Value: false, Position: node.Position}
			}
			if node.Operator == ">" && leftRange.Max <= rightRange.Min {
				return &ast.BooleanLiteral{Value: false, Position: node.Position}
			}
		}
	}

	// 处理标识符与数字字面量的比较
	if leftOk {
		if rightNum, ok := node.Right.(*ast.NumberLiteral); ok {
			if leftRange, exists := typeInfo.NumericRanges[leftIdent.Name]; exists {
				// 检查范围不可能满足条件的情况
				if node.Operator == "<" && leftRange.Min >= rightNum.Value {
					return &ast.BooleanLiteral{Value: false, Position: node.Position}
				}
				if node.Operator == "<=" && leftRange.Min > rightNum.Value {
					return &ast.BooleanLiteral{Value: false, Position: node.Position}
				}
				if node.Operator == ">" && leftRange.Max <= rightNum.Value {
					return &ast.BooleanLiteral{Value: false, Position: node.Position}
				}
				if node.Operator == ">=" && leftRange.Max < rightNum.Value {
					return &ast.BooleanLiteral{Value: false, Position: node.Position}
				}
				// 检查范围一定满足条件的情况
				if node.Operator == "<" && leftRange.Max < rightNum.Value {
					return &ast.BooleanLiteral{Value: true, Position: node.Position}
				}
				if node.Operator == "<=" && leftRange.Max <= rightNum.Value {
					return &ast.BooleanLiteral{Value: true, Position: node.Position}
				}
				if node.Operator == ">" && leftRange.Min > rightNum.Value {
					return &ast.BooleanLiteral{Value: true, Position: node.Position}
				}
				if node.Operator == ">=" && leftRange.Min >= rightNum.Value {
					return &ast.BooleanLiteral{Value: true, Position: node.Position}
				}
			}
		}
	}

	return nil
}

// canEvaluateToConstant 检查表达式是否可以计算为常量
func (c *ContextualOptimization) canEvaluateToConstant(expr ast.Expression, typeInfo *TypeInfo) bool {
	switch node := expr.(type) {
	case *ast.NumberLiteral, *ast.StringLiteral, *ast.BooleanLiteral:
		return true

	case *ast.Identifier:
		// 检查变量是否有确切值
		if numRange, exists := typeInfo.NumericRanges[node.Name]; exists && numRange.IsExact {
			return true
		}
		if boolValue, exists := typeInfo.BooleanValues[node.Name]; exists && boolValue.IsExact {
			return true
		}
		// 不直接替换字符串常量，因为可能很大
		return false

	default:
		return false
	}
}
