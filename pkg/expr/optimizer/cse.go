package optimizer

import (
	"admin/pkg/expr/ast"
)

// CommonSubexprElimination 实现公共子表达式消除
type CommonSubexprElimination struct{}

// NewCommonSubexprElimination 创建一个新的公共子表达式消除实例
func NewCommonSubexprElimination() *CommonSubexprElimination {
	return &CommonSubexprElimination{}
}

// Name 返回规则名称
func (c *CommonSubexprElimination) Name() string {
	return "CommonSubexprElimination"
}

// Apply 应用公共子表达式消除
func (c *CommonSubexprElimination) Apply(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	// 重置上下文中的表达式映射
	ctx.ExprMap = make(map[string]ast.Expression)

	// 第一遍扫描：收集子表达式信息
	c.collectSubexpressions(expr, ctx)

	// 第二遍：应用优化，消除公共子表达式
	result := c.eliminateCommonSubexprs(expr, ctx)

	return result
}

// collectSubexpressions 收集表达式树中的子表达式
func (c *CommonSubexprElimination) collectSubexpressions(expr ast.Expression, ctx *Context) {
	if expr == nil {
		return
	}

	// 首先对子表达式进行收集
	switch node := expr.(type) {
	case *ast.BinaryExpression:
		c.collectSubexpressions(node.Left, ctx)
		c.collectSubexpressions(node.Right, ctx)

		// 如果子表达式是纯净的（不包含副作用），可以考虑复用
		if isPureExpression(node) {
			key := node.String()
			if _, exists := ctx.ExprMap[key]; !exists {
				ctx.ExprMap[key] = node
			}
		}

	case *ast.UnaryExpression:
		c.collectSubexpressions(node.Right, ctx)

		if isPureExpression(node) {
			key := node.String()
			if _, exists := ctx.ExprMap[key]; !exists {
				ctx.ExprMap[key] = node
			}
		}

	case *ast.CallExpression:
		// 处理函数参数
		for _, arg := range node.Arguments {
			c.collectSubexpressions(arg, ctx)
		}

		// 仅对纯函数调用考虑复用
		if isPureFunction(node, ctx) {
			key := node.String()
			if _, exists := ctx.ExprMap[key]; !exists {
				ctx.ExprMap[key] = node
			}
		}

	case *ast.IndexExpression:
		c.collectSubexpressions(node.Left, ctx)
		c.collectSubexpressions(node.Index, ctx)

		// 索引表达式通常是纯的，但访问可变对象的索引可能有副作用
		// 简化起见，我们假设所有索引表达式都是纯的
		key := node.String()
		if _, exists := ctx.ExprMap[key]; !exists {
			ctx.ExprMap[key] = node
		}

	case *ast.ArrayLiteral:
		for _, elem := range node.Elements {
			c.collectSubexpressions(elem, ctx)
		}

	case *ast.ObjectLiteral:
		for _, pair := range node.Pairs {
			c.collectSubexpressions(pair.Key, ctx)
			c.collectSubexpressions(pair.Value, ctx)
		}

	case *ast.TernaryExpression:
		c.collectSubexpressions(node.Condition, ctx)
		c.collectSubexpressions(node.TrueExpr, ctx)
		c.collectSubexpressions(node.FalseExpr, ctx)

		if isPureExpression(node) {
			key := node.String()
			if _, exists := ctx.ExprMap[key]; !exists {
				ctx.ExprMap[key] = node
			}
		}
	}
}

// eliminateCommonSubexprs 消除公共子表达式
func (c *CommonSubexprElimination) eliminateCommonSubexprs(expr ast.Expression, ctx *Context) ast.Expression {
	if expr == nil {
		return nil
	}

	// 检查整个表达式是否可以被替换
	if isPureExpression(expr) {
		key := expr.String()
		if existingExpr, exists := ctx.ExprMap[key]; exists && existingExpr != expr {
			ctx.Stats.RuleApplications[c.Name()]++
			ctx.Stats.NodesSaved++
			return existingExpr
		}
	}

	// 递归处理子表达式
	switch node := expr.(type) {
	case *ast.BinaryExpression:
		node.Left = c.eliminateCommonSubexprs(node.Left, ctx)
		node.Right = c.eliminateCommonSubexprs(node.Right, ctx)
		return node

	case *ast.UnaryExpression:
		node.Right = c.eliminateCommonSubexprs(node.Right, ctx)
		return node

	case *ast.CallExpression:
		// 处理函数参数
		for i, arg := range node.Arguments {
			node.Arguments[i] = c.eliminateCommonSubexprs(arg, ctx)
		}
		return node

	case *ast.IndexExpression:
		node.Left = c.eliminateCommonSubexprs(node.Left, ctx)
		node.Index = c.eliminateCommonSubexprs(node.Index, ctx)
		return node

	case *ast.ArrayLiteral:
		for i, elem := range node.Elements {
			node.Elements[i] = c.eliminateCommonSubexprs(elem, ctx)
		}
		return node

	case *ast.ObjectLiteral:
		for i, pair := range node.Pairs {
			node.Pairs[i].Key = c.eliminateCommonSubexprs(pair.Key, ctx)
			node.Pairs[i].Value = c.eliminateCommonSubexprs(pair.Value, ctx)
		}
		return node

	case *ast.TernaryExpression:
		node.Condition = c.eliminateCommonSubexprs(node.Condition, ctx)
		node.TrueExpr = c.eliminateCommonSubexprs(node.TrueExpr, ctx)
		node.FalseExpr = c.eliminateCommonSubexprs(node.FalseExpr, ctx)
		return node

	default:
		return expr
	}
}

// isPureExpression 检查表达式是否是纯表达式（无副作用）
func isPureExpression(expr ast.Expression) bool {
	if expr == nil {
		return true
	}

	switch node := expr.(type) {
	case *ast.NumberLiteral, *ast.StringLiteral, *ast.BooleanLiteral, *ast.Identifier:
		// 基础字面量和标识符是纯净的
		return true

	case *ast.BinaryExpression:
		// 二元表达式的纯度取决于其操作数
		return isPureExpression(node.Left) && isPureExpression(node.Right)

	case *ast.UnaryExpression:
		// 一元表达式的纯度取决于其操作数
		return isPureExpression(node.Right)

	case *ast.CallExpression:
		// 函数调用通常不是纯的，除非显式标记为纯函数
		// 此处简化处理，在实际项目中需要维护一个纯函数列表
		if ident, ok := node.Function.(*ast.Identifier); ok {
			pureFunctions := map[string]bool{
				"min":    true,
				"max":    true,
				"abs":    true,
				"round":  true,
				"length": true,
				"upper":  true,
				"lower":  true,
				"concat": true,
				"if":     false, // 条件函数不是纯的，因为它有短路行为
			}

			if pure, exist := pureFunctions[ident.Name]; exist && pure {
				// 递归检查所有参数
				for _, arg := range node.Arguments {
					if !isPureExpression(arg) {
						return false
					}
				}
				return true
			}
		}
		return false

	case *ast.IndexExpression:
		// 索引表达式的纯度取决于被索引的对象和索引值
		return isPureExpression(node.Left) && isPureExpression(node.Index)

	case *ast.ArrayLiteral:
		// 数组字面量的纯度取决于其元素
		for _, elem := range node.Elements {
			if !isPureExpression(elem) {
				return false
			}
		}
		return true

	case *ast.ObjectLiteral:
		// 对象字面量的纯度取决于其所有键值对
		for _, pair := range node.Pairs {
			if !isPureExpression(pair.Key) || !isPureExpression(pair.Value) {
				return false
			}
		}
		return true

	case *ast.TernaryExpression:
		// 三元表达式的纯度取决于条件和两个分支
		return isPureExpression(node.Condition) &&
			isPureExpression(node.TrueExpr) &&
			isPureExpression(node.FalseExpr)

	default:
		// 对于未知表达式类型，保守起见，假设它们不是纯的
		return false
	}
}

// isPureFunction 检查函数调用是否是纯函数调用
func isPureFunction(call *ast.CallExpression, ctx *Context) bool {
	// 仅简单处理常见的纯函数
	// 在实际项目中，可能需要维护一个更完整的纯函数列表
	if ident, ok := call.Function.(*ast.Identifier); ok {
		switch ident.Name {
		case "min", "max", "abs", "round", "length", "upper", "lower", "concat":
			// 检查所有参数
			for _, arg := range call.Arguments {
				if !isPureExpression(arg) {
					return false
				}
			}
			return true
		default:
			return false
		}
	}
	return false
}
