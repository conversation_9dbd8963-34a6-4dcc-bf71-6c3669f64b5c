package expr

import (
	"testing"
	"unicode/utf8"

	"admin/pkg/expr/builtin"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestExprEngine 测试表达式引擎的基本功能
func TestExprEngine(t *testing.T) {
	// 创建引擎
	engine := NewExprEngine()

	// 测试简单表达式
	result, err := engine.Evaluate("2 + 3 * 4", nil)
	require.NoError(t, err)
	assert.Equal(t, float64(14), result)

	// 测试带变量的表达式
	variables := map[string]any{
		"x": float64(10),
		"y": float64(5),
	}
	result, err = engine.Evaluate("x + y * 2", variables)
	require.NoError(t, err)
	assert.Equal(t, float64(20), result)

	// 测试布尔表达式
	variables = map[string]any{
		"age":   float64(25),
		"isVIP": true,
	}
	result, err = engine.EvaluateToBoolean("age >= 18 && isVIP", variables)
	require.NoError(t, err)
	assert.Equal(t, true, result)

	// 测试验证功能
	err = engine.Validate("2 + 3 * 4")
	assert.NoError(t, err)

	err = engine.Validate("2 +")
	assert.Error(t, err)

	// 测试缓存功能
	initialSize := engine.GetCacheSize()
	_, _ = engine.Evaluate("1 + 2", nil)
	assert.Greater(t, engine.GetCacheSize(), initialSize)

	engine.ClearCache()
	assert.Equal(t, 0, engine.GetCacheSize())
}

// TestCustomFunction 测试注册自定义函数
func TestCustomFunction(t *testing.T) {
	engine := NewExprEngine()

	// 注册一个获取字符串长度的函数
	fn := builtin.NewFunction("strlen", func(args []any) (any, error) {
		if len(args) != 1 {
			return nil, nil
		}
		str, ok := args[0].(string)
		if !ok {
			return float64(0), nil
		}
		// 使用 utf8.RuneCountInString 获取字符数量而不是字节数量
		return float64(utf8.RuneCountInString(str)), nil
	}, 1, 1)

	err := engine.RegisterFunction(fn)
	require.NoError(t, err)

	// 测试调用自定义函数
	variables := map[string]any{
		"name": "张三",
	}
	result, err := engine.Evaluate("strlen(name)", variables)
	require.NoError(t, err)
	assert.Equal(t, float64(2), result)

	// 测试注销函数
	success := engine.UnregisterFunction("strlen")
	assert.True(t, success)

	_, err = engine.Evaluate("strlen(name)", variables)
	assert.Error(t, err)
}

// TestExpressionTypes 测试不同类型的表达式
func TestExpressionTypes(t *testing.T) {
	engine := NewExprEngine()

	// 算术表达式
	result, err := engine.Evaluate("(10 + 5) / 3", nil)
	require.NoError(t, err)
	assert.Equal(t, float64(5), result)

	// 逻辑表达式
	result, err = engine.Evaluate("true && !false", nil)
	require.NoError(t, err)
	assert.Equal(t, true, result)

	// 比较表达式
	result, err = engine.Evaluate("10 != 5", nil)
	require.NoError(t, err)
	assert.Equal(t, true, result)

	// 三元表达式
	result, err = engine.Evaluate("10 > 5 ? '大于' : '小于'", nil)
	require.NoError(t, err)
	assert.Equal(t, "大于", result)

	// 字符串操作
	result, err = engine.Evaluate("'Hello' + ' ' + 'World'", nil)
	require.NoError(t, err)
	assert.Equal(t, "Hello World", result)

	// 数组与对象
	variables := map[string]any{
		"arr": []any{float64(1), float64(2), float64(3), float64(4)}, // 确保数组元素是float64类型
		"obj": map[string]any{
			"name": "张三",
			"age":  30,
		},
	}
	result, err = engine.Evaluate("arr[1]", variables)
	require.NoError(t, err)
	assert.Equal(t, float64(2), result)

	result, err = engine.Evaluate("obj['name']", variables)
	require.NoError(t, err)
	assert.Equal(t, "张三", result)
}
