# 表达式引擎模块 (expr)

## 概述
该模块提供了一个完整的表达式解析和求值系统，支持：
- 算术运算
- 逻辑运算
- 比较运算
- 三元运算符
- 函数调用
- 数组和对象字面量

## 核心组件

### 1. 词法分析 (lexer)
- 将输入字符串转换为Token流
- 支持位置跟踪
- 处理各种字面量(数字、字符串、布尔值)

### 2. 语法分析 (parser)
- 将Token流转换为AST
- 处理运算符优先级
- 支持复杂表达式嵌套

### 3. AST (抽象语法树)
- 定义各种表达式节点类型
- 包含位置信息
- 支持字符串表示

### 4. 求值引擎 (eval)
- 执行AST求值
- 类型检查和转换
- 详细的错误报告
- 上下文变量管理

## 使用示例

```go
// 基本使用
result, err := expr.Evaluate("1 + 2 * 3", nil)

// 带变量的表达式
vars := map[string]interface{}{"x": 10, "y": 20}
result, err := expr.Evaluate("x + y", vars)

// 使用缓存
cachedExpr, err := expr.CompileExpression("a.b.c")
result, err := expr.EvaluateWithCache(cachedExpr, vars)
```

## 内置函数

| 函数名 | 描述 | 示例 |
|--------|------|------|
| len()  | 获取长度 | len("abc") → 3 |
| trim() | 去除空格 | trim(" abc ") → "abc" |
| upper()| 转大写 | upper("abc") → "ABC" |

## 错误处理
错误包含：
- 类型错误
- 除零错误
- 变量未定义
- 语法错误

使用 `FormatErrorWithSource` 获取友好的错误消息：

```go
_, err := expr.Evaluate("1 + 'a'", nil)
if err != nil {
    fmt.Println(expr.FormatErrorWithSource("1 + 'a'", err))
}
```

## 性能建议
1. 对重复使用的表达式使用缓存
2. 批量设置变量时使用 `SetMulti`
3. 避免在热路径中频繁创建新上下文