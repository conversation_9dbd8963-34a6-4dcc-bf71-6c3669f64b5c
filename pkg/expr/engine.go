// Package expr 提供表达式引擎的统一接口与实现
package expr

import (
	"admin/pkg/expr/ast"
	"admin/pkg/expr/builtin"
	"admin/pkg/expr/eval"
	"admin/pkg/expr/ext"
	"admin/pkg/expr/optimizer"
)

// ExprEngine 表达式引擎接口，提供表达式计算、验证、优化等功能
type ExprEngine interface {
	// Evaluate 计算表达式的值
	// expression: 表达式字符串
	// variables: 可选的变量映射表
	// 返回计算结果和错误信息
	Evaluate(expression string, variables map[string]any) (any, error)

	// EvaluateToBoolean 计算布尔表达式的值
	// expression: 表达式字符串
	// variables: 可选的变量映射表
	// 返回布尔结果和错误信息
	EvaluateToBoolean(expression string, variables map[string]any) (bool, error)

	// EvaluateWithDiagnostics 计算表达式并提供诊断信息
	// expression: 表达式字符串
	// variables: 可选的变量映射表
	// 返回计算结果、诊断信息和错误信息
	EvaluateWithDiagnostics(expression string, variables map[string]any) (any, *eval.ErrorDiagnostic, error)

	// Validate 验证表达式是否合法
	// expression: 表达式字符串
	// 返回错误信息，如果表达式合法则返回nil
	Validate(expression string) error

	// Compile 编译表达式，返回AST
	// expression: 表达式字符串
	// 返回表达式AST和错误信息
	Compile(expression string) (ast.Expression, error)

	// CompileAndOptimize 编译并优化表达式
	// expression: 表达式字符串
	// variables: 可选的变量映射表，用于在优化阶段进行常量折叠
	// 返回优化后的表达式AST和错误信息
	CompileAndOptimize(expression string, variables map[string]any) (ast.Expression, error)

	// RegisterFunction 注册自定义函数
	// function: 函数定义
	// 返回错误信息
	RegisterFunction(function builtin.Function) error

	// UnregisterFunction 注销自定义函数
	// name: 函数名
	// 返回是否成功注销
	UnregisterFunction(name string) bool

	// ClearCache 清除表达式缓存
	ClearCache()

	// GetCacheSize 获取当前缓存大小
	// 返回缓存中的表达式数量
	GetCacheSize() int
}

// defaultExprEngine 默认表达式引擎实现
type defaultExprEngine struct {
	// 缓存
	cache *eval.ExpressionCache
	// 优化配置
	optimizeConfig *optimizer.Config
}

// NewExprEngine 创建一个新的表达式引擎
// 该函数可用于wire依赖注入
func NewExprEngine() ExprEngine {
	return &defaultExprEngine{
		cache:          eval.NewExpressionCache(),
		optimizeConfig: optimizer.DefaultConfig(),
	}
}

// NewExprEngineWithConfig 创建一个新的表达式引擎，使用指定的优化配置
func NewExprEngineWithConfig(config *optimizer.Config) ExprEngine {
	return &defaultExprEngine{
		cache:          eval.NewExpressionCache(),
		optimizeConfig: config,
	}
}

// Evaluate 实现ExprEngine接口
func (e *defaultExprEngine) Evaluate(expression string, variables map[string]any) (any, error) {
	// 优先使用缓存
	expr, err := e.Compile(expression)
	if err != nil {
		return nil, err
	}

	// 如果有变量，先进行优化
	if len(variables) > 0 {
		expr = optimizer.OptimizeWithVariables(expr, variables)
	}

	// 创建上下文并执行
	ctx := eval.NewContext()
	if variables != nil {
		ctx.SetMulti(variables)
	}

	evaluator := eval.WithContext(ctx)
	return evaluator.Evaluate(expr)
}

// EvaluateToBoolean 实现ExprEngine接口
func (e *defaultExprEngine) EvaluateToBoolean(expression string, variables map[string]any) (bool, error) {
	result, err := e.Evaluate(expression, variables)
	if err != nil {
		return false, err
	}

	// 将结果转换为布尔值
	if boolResult, ok := result.(bool); ok {
		return boolResult, nil
	}

	// 根据类型转换为布尔值
	switch v := result.(type) {
	case nil:
		return false, nil
	case string:
		return len(v) > 0, nil
	case float64:
		return v != 0, nil
	case int:
		return v != 0, nil
	case []any:
		return len(v) > 0, nil
	case map[string]any:
		return len(v) > 0, nil
	default:
		// 其他类型按其存在性判断
		return true, nil
	}
}

// EvaluateWithDiagnostics 实现ExprEngine接口
func (e *defaultExprEngine) EvaluateWithDiagnostics(expression string, variables map[string]any) (any, *eval.ErrorDiagnostic, error) {
	return eval.EvaluateWithDiagnostics(expression, variables)
}

// Validate 实现ExprEngine接口
func (e *defaultExprEngine) Validate(expression string) error {
	return optimizer.ValidateExpression(expression)
}

// Compile 实现ExprEngine接口
func (e *defaultExprEngine) Compile(expression string) (ast.Expression, error) {
	return eval.CompileExpression(expression)
}

// CompileAndOptimize 实现ExprEngine接口
func (e *defaultExprEngine) CompileAndOptimize(expression string, variables map[string]any) (ast.Expression, error) {
	return optimizer.CompileAndOptimizeWithVariables(expression, variables)
}

// RegisterFunction 实现ExprEngine接口
func (e *defaultExprEngine) RegisterFunction(function builtin.Function) error {
	return ext.RegisterFunction(function)
}

// UnregisterFunction 实现ExprEngine接口
func (e *defaultExprEngine) UnregisterFunction(name string) bool {
	return ext.UnregisterFunction(name)
}

// ClearCache 实现ExprEngine接口
func (e *defaultExprEngine) ClearCache() {
	eval.GlobalExpressionCache.Clear()
}

// GetCacheSize 实现ExprEngine接口
func (e *defaultExprEngine) GetCacheSize() int {
	return eval.GlobalExpressionCache.Size()
}
