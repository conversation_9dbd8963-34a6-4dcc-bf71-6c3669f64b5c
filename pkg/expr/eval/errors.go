package eval

import (
	"fmt"
	"strings"

	"admin/pkg/expr/ast"
)

// ErrorType 表达式错误类型
type ErrorType string

const (
	ErrorTypeSyntax       ErrorType = "语法错误"
	ErrorTypeType         ErrorType = "类型错误"
	ErrorTypeRuntime      ErrorType = "运行时错误"
	ErrorTypeReference    ErrorType = "引用错误"
	ErrorTypeRange        ErrorType = "范围错误"
	ErrorTypeSystem       ErrorType = "系统错误"
	ErrorTypeZeroDivide   ErrorType = "除零错误"
	ErrorTypeUndefined    ErrorType = "未定义错误" // 为了与diagnostics.go兼容
	ErrorTypeDivideByZero ErrorType = "除零错误"  // 为了与diagnostics.go兼容
)

// Error 表达式错误，包含详细信息
type Error struct {
	Type     ErrorType    // 错误类型
	Position ast.Position // 错误位置
	Message  string       // 错误消息
	Cause    error        // 原因
}

// Error 实现error接口
func (e *Error) Error() string {
	var builder strings.Builder

	// 将错误类型显式添加到错误消息中，确保包含预期的关键词
	switch e.Type {
	case ErrorTypeSyntax:
		builder.WriteString("无法解析表达式: ")
	case ErrorTypeType:
		builder.WriteString("类型错误: ")
	case ErrorTypeRuntime, ErrorTypeReference, ErrorTypeRange, ErrorTypeSystem:
		builder.WriteString("表达式错误: ")
	case ErrorTypeDivideByZero: // 只使用一个除零错误类型
		// 除零错误不需要前缀，因为Message已经包含了"除数不能为零"
		return e.Message
	default:
		builder.WriteString("表达式错误: ")
	}

	builder.WriteString(e.Message)

	if e.Position.Line > 0 {
		builder.WriteString(fmt.Sprintf(" (位置: %d行,%d列)", e.Position.Line, e.Position.Column))
	}

	if e.Cause != nil {
		builder.WriteString(fmt.Sprintf(" - 原因: %s", e.Cause.Error()))
	}

	return builder.String()
}

// Pos 返回错误位置，兼容diagnostics.go
func (e *Error) Pos() ast.Position {
	return e.Position
}

// NewError 创建一个新的表达式错误
func NewError(errorType ErrorType, pos ast.Position, msg string, cause error) *Error {
	return &Error{
		Type:     errorType,
		Position: pos,
		Message:  msg,
		Cause:    cause,
	}
}

// NewTypeError 创建一个类型错误
func NewTypeError(pos ast.Position, expected, got interface{}) *Error {
	return &Error{
		Type:     ErrorTypeType,
		Position: pos,
		Message:  fmt.Sprintf("期望 %T, 得到 %T", expected, got),
	}
}

// NewRangeError 创建一个范围错误
func NewRangeError(pos ast.Position, msg string) *Error {
	return &Error{
		Type:     ErrorTypeRange,
		Position: pos,
		Message:  msg,
	}
}

// NewDivideByZeroError 创建一个除零错误
func NewDivideByZeroError(pos ast.Position) *Error {
	return &Error{
		Type:     ErrorTypeDivideByZero,
		Position: pos,
		Message:  "除数不能为零",
	}
}
