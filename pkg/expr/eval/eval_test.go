
package eval

import (
	"testing"

	"admin/pkg/expr/parser"
	"admin/pkg/expr/lexer"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestContext 测试上下文功能
func TestContext(t *testing.T) {
	ctx := NewContext()

	// Set and Get
	ctx.Set("x", 10.0)
	val, ok := ctx.Get("x")
	assert.True(t, ok)
	assert.Equal(t, 10.0, val)

	// Get non-existent
	_, ok = ctx.Get("y")
	assert.False(t, ok)

	// SetMulti
	ctx.SetMulti(map[string]any{"a": 1, "b": "hello"})
	val, _ = ctx.Get("a")
	assert.Equal(t, 1, val)
	val, _ = ctx.Get("b")
	assert.Equal(t, "hello", val)

	// Delete
	ctx.Delete("x")
	_, ok = ctx.Get("x")
	assert.False(t, ok)

	// Clear
	ctx.Clear()
	assert.Empty(t, ctx.GetAll())
}

// TestExpressionCache 测试表达式缓存
func TestExpressionCache(t *testing.T) {
	cache := NewExpressionCache()
	exprStr := "2 + 2"
	l := lexer.New(exprStr)
	p := parser.New(l)
	expr, err := p.ParseExpression()
	require.NoError(t, err)

	// Set and Get
	cache.Set(exprStr, expr)
	cachedExpr := cache.Get(exprStr)
	assert.Equal(t, expr, cachedExpr)

	// Get non-existent
	assert.Nil(t, cache.Get("non-existent"))

	// Size and Clear
	assert.Equal(t, 1, cache.Size())
	cache.Clear()
	assert.Equal(t, 0, cache.Size())
}

// TestEvaluator 测试评估器
func TestEvaluator(t *testing.T) {
	testCases := []struct {
		name       string
		expression string
		variables  map[string]any
		expected   any
		expectError bool
	}{
		{"Number Literal", "123", nil, 123.0, false},
		{"String Literal", "'hello'", nil, "hello", false},
		{"Boolean Literal", "true", nil, true, false},
		{"Identifier", "x", map[string]any{"x": 5}, 5, false},
		{"Unary Not", "!true", nil, false, false},
		{"Unary Negate", "-10", nil, -10.0, false},
		{"Binary Add", "2 + 3", nil, 5.0, false},
		{"Binary Subtract", "5 - 2", nil, 3.0, false},
		{"Binary Multiply", "3 * 4", nil, 12.0, false},
		{"Binary Divide", "10 / 2", nil, 5.0, false},
		{"Binary Modulo", "10 % 3", nil, 1.0, false},
		{"Binary Equal", "5 == 5", nil, true, false},
		{"Binary Not Equal", "5 != 3", nil, true, false},
		{"Binary Less Than", "3 < 5", nil, true, false},
		{"Binary Greater Than", "5 > 3", nil, true, false},
		{"Logical And", "true && false", nil, false, false},
		{"Logical Or", "true || false", nil, true, false},
		{"Ternary Expression", "true ? 1 : 2", nil, 1.0, false},
		{"Array Literal", "[1, 'a', true]", nil, []any{1.0, "a", true}, false},
		{"Index Expression (Array)", "[1, 2, 3][1]", nil, 2.0, false},
		{"Index Expression (Object)", "{a: 1, b: 2}['b']", nil, 2.0, false},
		{"Call Expression", "min(5, 2, 8)", nil, 2.0, false},
		{"Undefined Variable", "y", nil, nil, true},
		{"Divide By Zero", "1 / 0", nil, nil, true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			l := lexer.New(tc.expression)
			p := parser.New(l)
			expr, err := p.ParseExpression()
			require.NoError(t, err)

			ctx := NewContext()
			if tc.variables != nil {
				ctx.SetMulti(tc.variables)
			}

			evaluator := WithContext(ctx)
			result, err := evaluator.Evaluate(expr)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expected, result)
			}
		})
	}
}
