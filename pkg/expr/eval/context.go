package eval

import "sync"

// Context 表达式执行上下文，用于存储变量
type Context struct {
	variables map[string]interface{}
	mu        sync.RWMutex
}

// NewContext 创建一个新的执行上下文
func NewContext() *Context {
	return &Context{
		variables: make(map[string]interface{}),
	}
}

// Get 获取变量的值，第二个返回值表示是否存在
func (c *Context) Get(name string) (interface{}, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	value, exists := c.variables[name]
	return value, exists
}

// Set 设置变量的值
func (c *Context) Set(name string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.variables[name] = value
}

// SetMulti 批量设置多个变量
func (c *Context) SetMulti(vars map[string]interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()

	for name, value := range vars {
		c.variables[name] = value
	}
}

// Delete 删除一个变量
func (c *Context) Delete(name string) {
	c.mu.Lock()
	defer c.mu.Unlock()

	delete(c.variables, name)
}

// Clear 清空所有变量
func (c *Context) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.variables = make(map[string]interface{})
}

// GetAll 获取所有变量
func (c *Context) GetAll() map[string]interface{} {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make(map[string]interface{}, len(c.variables))
	for k, v := range c.variables {
		result[k] = v
	}
	return result
}

// Clone 创建上下文的一个副本
func (c *Context) Clone() *Context {
	c.mu.RLock()
	defer c.mu.RUnlock()

	clone := NewContext()
	for k, v := range c.variables {
		clone.variables[k] = v
	}
	return clone
}
