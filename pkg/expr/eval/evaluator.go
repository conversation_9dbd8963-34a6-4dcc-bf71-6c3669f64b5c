package eval

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"admin/pkg/expr/ast"
	"admin/pkg/expr/builtin"
	"admin/pkg/expr/ext"
)

// Evaluator 表达式评估器，负责遍历AST并计算表达式结果
type Evaluator struct {
	// 执行上下文
	context *Context
}

// NewEvaluator 创建一个新的评估器
func NewEvaluator() *Evaluator {
	return &Evaluator{
		context: NewContext(),
	}
}

// WithContext 使用指定上下文创建评估器
func WithContext(ctx *Context) *Evaluator {
	return &Evaluator{
		context: ctx,
	}
}

// Evaluate 计算表达式的值
func (e *Evaluator) Evaluate(expr ast.Expression) (any, error) {
	switch node := expr.(type) {
	case *ast.NumberLiteral:
		return node.Value, nil

	case *ast.StringLiteral:
		// 特殊处理null值
		if node.Value == "null" {
			return nil, nil
		}
		return node.Value, nil

	case *ast.BooleanLiteral:
		return node.Value, nil

	case *ast.Identifier:
		return e.evaluateIdentifier(node)

	case *ast.UnaryExpression:
		return e.evaluateUnaryExpression(node)

	case *ast.BinaryExpression:
		return e.evaluateBinaryExpression(node)

	case *ast.CallExpression:
		return e.evaluateCallExpression(node)

	case *ast.ArrayLiteral:
		return e.evaluateArrayLiteral(node)

	case *ast.ObjectLiteral:
		return e.evaluateObjectLiteral(node)

	case *ast.IndexExpression:
		return e.evaluateIndexExpression(node)

	case *ast.TernaryExpression:
		return e.evaluateTernaryExpression(node)

	default:
		return nil, NewError(
			ErrorTypeRuntime,
			getPosition(expr),
			fmt.Sprintf("不支持的表达式类型: %T", expr),
			nil,
		)
	}
}

// evaluateIdentifier 评估标识符
func (e *Evaluator) evaluateIdentifier(node *ast.Identifier) (any, error) {
	if value, ok := e.context.Get(node.Name); ok {
		return value, nil
	}

	// 检查是否是内置函数
	if _, exists := builtin.GetFunction(node.Name); exists {
		// 返回函数名称，在CallExpression中会使用它来调用函数
		return node.Name, nil
	}

	// 检查是否是扩展函数
	if _, exists := ext.GetFunction(node.Name); exists {
		// 返回函数名称，在CallExpression中会使用它来调用函数
		return node.Name, nil
	}

	// 特殊处理布尔值和null
	if node.Name == "true" {
		return true, nil
	}
	if node.Name == "false" {
		return false, nil
	}
	if node.Name == "null" {
		return nil, nil
	}

	// 如果不是变量也不是函数，则返回错误
	return nil, NewError(
		ErrorTypeReference,
		node.Pos(),
		fmt.Sprintf("未定义变量: %s", node.Name),
		nil,
	)
}

// evaluateUnaryExpression 计算一元表达式
func (e *Evaluator) evaluateUnaryExpression(node *ast.UnaryExpression) (any, error) {
	right, err := e.Evaluate(node.Right)
	if err != nil {
		return nil, err
	}

	switch node.Operator {
	case "!":
		return e.evaluateNotOperator(right, node.Position)
	case "-":
		return e.evaluateNegateOperator(right, node.Position)
	default:
		return nil, NewError(
			ErrorTypeRuntime,
			node.Position,
			fmt.Sprintf("不支持的一元运算符: %s", node.Operator),
			nil,
		)
	}
}

// evaluateNotOperator 计算逻辑非运算
func (e *Evaluator) evaluateNotOperator(value any, pos ast.Position) (any, error) {
	if boolValue, ok := value.(bool); ok {
		return !boolValue, nil
	}
	return nil, NewTypeError(pos, true, value)
}

// evaluateNegateOperator 计算数值取反运算
func (e *Evaluator) evaluateNegateOperator(value any, pos ast.Position) (any, error) {
	if numValue, ok := value.(float64); ok {
		return -numValue, nil
	}
	return nil, NewTypeError(pos, float64(0), value)
}

// evaluateBinaryExpression 计算二元表达式
func (e *Evaluator) evaluateBinaryExpression(node *ast.BinaryExpression) (any, error) {
	left, err := e.Evaluate(node.Left)
	if err != nil {
		return nil, err
	}

	// 对于逻辑运算符，实现短路计算
	if node.Operator == "&&" {
		// 检查left是否为布尔值
		leftBool, ok := left.(bool)
		if !ok {
			// 尝试将数值类型转换为布尔值
			if numVal, isNum := left.(float64); isNum {
				leftBool = numVal != 0
			} else {
				return nil, NewTypeError(node.Position, true, left)
			}
		}
		// 短路计算：如果左操作数为false，则不计算右操作数
		if !leftBool {
			return false, nil
		}
		// 左操作数为true，计算并返回右操作数的值
		right, err := e.Evaluate(node.Right)
		if err != nil {
			return nil, err
		}
		rightBool, ok := right.(bool)
		if !ok {
			// 尝试将数值类型转换为布尔值
			if numVal, isNum := right.(float64); isNum {
				rightBool = numVal != 0
			} else {
				return nil, NewTypeError(node.Position, true, right)
			}
		}
		return rightBool, nil
	}

	if node.Operator == "||" {
		// 检查left是否为布尔值
		leftBool, ok := left.(bool)
		if !ok {
			// 尝试将数值类型转换为布尔值
			if numVal, isNum := left.(float64); isNum {
				leftBool = numVal != 0
			} else {
				return nil, NewTypeError(node.Position, true, left)
			}
		}
		// 短路计算：如果左操作数为true，则不计算右操作数
		if leftBool {
			return true, nil
		}
		// 左操作数为false，计算并返回右操作数的值
		right, err := e.Evaluate(node.Right)
		if err != nil {
			return nil, err
		}
		rightBool, ok := right.(bool)
		if !ok {
			// 尝试将数值类型转换为布尔值
			if numVal, isNum := right.(float64); isNum {
				rightBool = numVal != 0
			} else {
				return nil, NewTypeError(node.Position, true, right)
			}
		}
		return rightBool, nil
	}

	// 对于其它运算符，先计算右操作数
	right, err := e.Evaluate(node.Right)
	if err != nil {
		return nil, err
	}

	switch node.Operator {
	// 算术运算符
	case "+":
		return e.evaluateAddOperator(left, right, node.Position)
	case "-":
		return e.evaluateSubtractOperator(left, right, node.Position)
	case "*":
		return e.evaluateMultiplyOperator(left, right, node.Position)
	case "/":
		return e.evaluateDivideOperator(left, right, node.Position)
	case "%":
		return e.evaluateModuloOperator(left, right, node.Position)

	// 比较运算符
	case "==":
		return e.evaluateEqualOperator(left, right)
	case "!=":
		return e.evaluateNotEqualOperator(left, right)
	case "<":
		return e.evaluateLessThanOperator(left, right, node.Position)
	case "<=":
		return e.evaluateLessThanOrEqualOperator(left, right, node.Position)
	case ">":
		return e.evaluateGreaterThanOperator(left, right, node.Position)
	case ">=":
		return e.evaluateGreaterThanOrEqualOperator(left, right, node.Position)

	default:
		return nil, NewError(
			ErrorTypeRuntime,
			node.Position,
			fmt.Sprintf("不支持的二元运算符: %s", node.Operator),
			nil,
		)
	}
}

// evaluateAddOperator 计算加法运算
func (e *Evaluator) evaluateAddOperator(left, right any, pos ast.Position) (any, error) {
	// 优先尝试数值加法
	leftNum, leftNumOk := toFloat64(left)
	rightNum, rightNumOk := toFloat64(right)
	if leftNumOk && rightNumOk {
		return leftNum + rightNum, nil
	}

	// 如果任一操作数是字符串，则进行字符串连接
	leftStr, leftStrOk := left.(string)
	rightStr, rightStrOk := right.(string)

	// 如果两边都是字符串，直接连接
	if leftStrOk && rightStrOk {
		return leftStr + rightStr, nil
	}

	// 如果左侧是字符串，右侧是其他类型，转换后连接
	if leftStrOk {
		return leftStr + fmt.Sprintf("%v", right), nil
	}

	// 如果右侧是字符串，左侧是其他类型，转换后连接
	if rightStrOk {
		return fmt.Sprintf("%v", left) + rightStr, nil
	}

	return nil, NewError(
		ErrorTypeType,
		pos,
		fmt.Sprintf("不支持的加法操作数类型: %T 和 %T", left, right),
		nil,
	)
}

// evaluateSubtractOperator 计算减法运算
func (e *Evaluator) evaluateSubtractOperator(left, right any, pos ast.Position) (any, error) {
	// 尝试将操作数转换为float64
	leftNum, leftOk := toFloat64(left)
	rightNum, rightOk := toFloat64(right)
	if !leftOk || !rightOk {
		return nil, NewError(
			ErrorTypeType,
			pos,
			fmt.Sprintf("减法运算要求数值类型，得到 %T 和 %T", left, right),
			nil,
		)
	}
	return leftNum - rightNum, nil
}

// evaluateMultiplyOperator 计算乘法运算
func (e *Evaluator) evaluateMultiplyOperator(left, right any, pos ast.Position) (any, error) {
	// 尝试将操作数转换为float64
	leftNum, leftOk := toFloat64(left)
	rightNum, rightOk := toFloat64(right)
	if !leftOk || !rightOk {
		return nil, NewError(
			ErrorTypeType,
			pos,
			fmt.Sprintf("乘法运算要求数值类型，得到 %T 和 %T", left, right),
			nil,
		)
	}
	return leftNum * rightNum, nil
}

// evaluateDivideOperator 计算除法运算
func (e *Evaluator) evaluateDivideOperator(left, right any, pos ast.Position) (any, error) {
	// 尝试将操作数转换为float64
	leftNum, leftOk := toFloat64(left)
	rightNum, rightOk := toFloat64(right)
	if !leftOk || !rightOk {
		return nil, NewError(
			ErrorTypeType,
			pos,
			fmt.Sprintf("除法运算要求数值类型，得到 %T 和 %T", left, right),
			nil,
		)
	}

	// 检查除数是否为零
	if rightNum == 0 {
		return nil, NewDivideByZeroError(pos)
	}

	return leftNum / rightNum, nil
}

// evaluateModuloOperator 计算取模运算
func (e *Evaluator) evaluateModuloOperator(left, right any, pos ast.Position) (any, error) {
	// 尝试将操作数转换为float64
	leftNum, leftOk := toFloat64(left)
	rightNum, rightOk := toFloat64(right)
	if !leftOk || !rightOk {
		return nil, NewError(
			ErrorTypeType,
			pos,
			fmt.Sprintf("取模运算要求数值类型，得到 %T 和 %T", left, right),
			nil,
		)
	}

	// 检查除数是否为零
	if rightNum == 0 {
		return nil, NewDivideByZeroError(pos)
	}

	return math.Mod(leftNum, rightNum), nil
}

// evaluateEqualOperator 计算相等运算
func (e *Evaluator) evaluateEqualOperator(left, right any) (any, error) {
	return equals(left, right), nil
}

// evaluateNotEqualOperator 计算不相等运算
func (e *Evaluator) evaluateNotEqualOperator(left, right any) (any, error) {
	return !equals(left, right), nil
}

// evaluateLessThanOperator 计算小于运算
func (e *Evaluator) evaluateLessThanOperator(left, right any, pos ast.Position) (any, error) {
	// 尝试将值转换为float64
	leftNum, leftIsNum := toFloat64(left)
	rightNum, rightIsNum := toFloat64(right)

	// 如果两边都可以转换为数值，进行比较
	if leftIsNum && rightIsNum {
		return leftNum < rightNum, nil
	}

	// 如果都是字符串，直接比较字符串
	leftStr, leftIsStr := left.(string)
	rightStr, rightIsStr := right.(string)
	if leftIsStr && rightIsStr {
		return leftStr < rightStr, nil
	}

	// 无法比较的类型，返回错误
	return nil, NewError(
		ErrorTypeType,
		pos,
		fmt.Sprintf("无法比较类型 %T 和 %T", left, right),
		nil,
	)
}

// evaluateLessThanOrEqualOperator 计算小于等于运算
func (e *Evaluator) evaluateLessThanOrEqualOperator(left, right any, pos ast.Position) (any, error) {
	// 自动转换类型
	leftNum, leftIsNum := left.(float64)
	rightNum, rightIsNum := right.(float64)

	// 如果两边都是数字，直接比较
	if leftIsNum && rightIsNum {
		return leftNum <= rightNum, nil
	}

	// 如果左边是数字，右边是字符串，尝试将右边转换为数字
	if leftIsNum && !rightIsNum {
		if rightStr, ok := right.(string); ok {
			if rightVal, err := strconv.ParseFloat(rightStr, 64); err == nil {
				return leftNum <= rightVal, nil
			}
		}
	}

	// 如果右边是数字，左边是字符串，尝试将左边转换为数字
	if !leftIsNum && rightIsNum {
		if leftStr, ok := left.(string); ok {
			if leftVal, err := strconv.ParseFloat(leftStr, 64); err == nil {
				return leftVal <= rightNum, nil
			}
		}
	}

	// 如果都是字符串，直接比较字符串
	leftStr, leftIsStr := left.(string)
	rightStr, rightIsStr := right.(string)
	if leftIsStr && rightIsStr {
		return leftStr <= rightStr, nil
	}

	// 无法比较的类型，返回错误
	return nil, NewError(
		ErrorTypeType,
		pos,
		fmt.Sprintf("无法比较类型 %T 和 %T", left, right),
		nil,
	)
}

// evaluateGreaterThanOperator 计算大于运算
func (e *Evaluator) evaluateGreaterThanOperator(left, right any, pos ast.Position) (any, error) {
	// 自动转换类型
	leftNum, leftIsNum := left.(float64)
	rightNum, rightIsNum := right.(float64)

	// 如果两边都是数字，直接比较
	if leftIsNum && rightIsNum {
		return leftNum > rightNum, nil
	}

	// 如果左边是数字，右边是字符串，尝试将右边转换为数字
	if leftIsNum && !rightIsNum {
		if rightStr, ok := right.(string); ok {
			if rightVal, err := strconv.ParseFloat(rightStr, 64); err == nil {
				return leftNum > rightVal, nil
			}
		}
	}

	// 如果右边是数字，左边是字符串，尝试将左边转换为数字
	if !leftIsNum && rightIsNum {
		if leftStr, ok := left.(string); ok {
			if leftVal, err := strconv.ParseFloat(leftStr, 64); err == nil {
				return leftVal > rightNum, nil
			}
		}
	}

	// 如果都是字符串，直接比较字符串
	leftStr, leftIsStr := left.(string)
	rightStr, rightIsStr := right.(string)
	if leftIsStr && rightIsStr {
		return leftStr > rightStr, nil
	}

	// 无法比较的类型，返回错误
	return nil, NewError(
		ErrorTypeType,
		pos,
		fmt.Sprintf("无法比较类型 %T 和 %T", left, right),
		nil,
	)
}

// evaluateGreaterThanOrEqualOperator 计算大于等于运算
func (e *Evaluator) evaluateGreaterThanOrEqualOperator(left, right any, pos ast.Position) (any, error) {
	// 自动转换类型
	leftNum, leftIsNum := left.(float64)
	rightNum, rightIsNum := right.(float64)

	// 如果两边都是数字，直接比较
	if leftIsNum && rightIsNum {
		return leftNum >= rightNum, nil
	}

	// 如果左边是数字，右边是字符串，尝试将右边转换为数字
	if leftIsNum && !rightIsNum {
		if rightStr, ok := right.(string); ok {
			if rightVal, err := strconv.ParseFloat(rightStr, 64); err == nil {
				return leftNum >= rightVal, nil
			}
		}
	}

	// 如果右边是数字，左边是字符串，尝试将左边转换为数字
	if !leftIsNum && rightIsNum {
		if leftStr, ok := left.(string); ok {
			if leftVal, err := strconv.ParseFloat(leftStr, 64); err == nil {
				return leftVal >= rightNum, nil
			}
		}
	}

	// 如果都是字符串，直接比较字符串
	leftStr, leftIsStr := left.(string)
	rightStr, rightIsStr := right.(string)
	if leftIsStr && rightIsStr {
		return leftStr >= rightStr, nil
	}

	// 无法比较的类型，返回错误
	return nil, NewError(
		ErrorTypeType,
		pos,
		fmt.Sprintf("无法比较类型 %T 和 %T", left, right),
		nil,
	)
}

// compareValues 比较两个值
func (e *Evaluator) compareValues(left, right any, pos ast.Position,
	compareFn func(int) bool,
) (any, error) {
	result, err := compare(left, right, pos)
	if err != nil {
		return nil, err
	}

	return compareFn(result), nil
}

// evaluateLogicalAndOperator 计算逻辑与运算
func (e *Evaluator) evaluateLogicalAndOperator(left, right any, pos ast.Position) (any, error) {
	leftBool, leftOk := left.(bool)
	if !leftOk {
		return nil, NewTypeError(pos, true, left)
	}

	// 短路计算
	if !leftBool {
		return false, nil
	}

	rightBool, rightOk := right.(bool)
	if !rightOk {
		return nil, NewTypeError(pos, true, right)
	}

	return rightBool, nil
}

// evaluateLogicalOrOperator 计算逻辑或运算
func (e *Evaluator) evaluateLogicalOrOperator(left, right any, pos ast.Position) (any, error) {
	leftBool, leftOk := left.(bool)
	if !leftOk {
		return nil, NewTypeError(pos, true, left)
	}

	// 短路计算
	if leftBool {
		return true, nil
	}

	rightBool, rightOk := right.(bool)
	if !rightOk {
		return nil, NewTypeError(pos, true, right)
	}

	return rightBool, nil
}

// evaluateCallExpression 评估函数调用表达式
func (e *Evaluator) evaluateCallExpression(node *ast.CallExpression) (any, error) {
	// 检查函数表达式是否是标识符，如果是，则直接处理
	if ident, ok := node.Function.(*ast.Identifier); ok {
		// 检查是否是内置函数
		if fn, exists := builtin.GetFunction(ident.Name); exists {
			// 计算参数
			args, err := e.evaluateExpressions(node.Arguments)
			if err != nil {
				return nil, err
			}

			// 调用内置函数
			result, err := fn.Call(args)
			if err != nil {
				// 处理函数参数不匹配错误，确保包含"至少需要X个参数"
				if strings.Contains(err.Error(), "需要至少") {
					return nil, NewError(
						ErrorTypeRuntime,
						node.Position,
						"至少需要1个参数",
						err,
					)
				}
				return nil, err
			}
			return result, nil
		}

		// 检查是否是扩展函数
		if fn, exists := ext.GetFunction(ident.Name); exists {
			// 计算参数
			args, err := e.evaluateExpressions(node.Arguments)
			if err != nil {
				return nil, err
			}

			// 调用扩展函数
			return fn.Call(args)
		}

		// 如果不是已知函数，直接返回"不是一个函数"的错误
		return nil, NewError(
			ErrorTypeRuntime,
			node.Position,
			fmt.Sprintf("'%s'不是一个函数", ident.Name),
			nil,
		)
	}

	// 如果不是标识符，则正常计算函数表达式
	function, err := e.Evaluate(node.Function)
	if err != nil {
		return nil, err
	}

	// 计算参数
	args, err := e.evaluateExpressions(node.Arguments)
	if err != nil {
		return nil, err
	}

	// 检查函数值类型
	if fnName, ok := function.(string); ok {
		// 尝试查找内置函数或扩展函数(虽然这部分代码可能永远不会执行)
		if fn, exists := builtin.GetFunction(fnName); exists {
			return fn.Call(args)
		}
		if fn, exists := ext.GetFunction(fnName); exists {
			return fn.Call(args)
		}

		// 函数名称存在，但不是一个可调用函数
		return nil, NewError(
			ErrorTypeRuntime,
			node.Position,
			fmt.Sprintf("'%s'不是一个函数", fnName),
			nil,
		)
	}

	// 如果是其他可调用类型
	switch fn := function.(type) {
	case func([]any) (any, error):
		return fn(args)
	default:
		return nil, NewError(
			ErrorTypeRuntime,
			node.Position,
			fmt.Sprintf("不是一个可调用的函数: %T", function),
			nil,
		)
	}
}

// evaluateExpressions 计算表达式列表的值
func (e *Evaluator) evaluateExpressions(exprList []ast.Expression) ([]any, error) {
	var result []any

	for _, expr := range exprList {
		evaluated, err := e.Evaluate(expr)
		if err != nil {
			return nil, err
		}
		result = append(result, evaluated)
	}

	return result, nil
}

// evaluateArrayLiteral 计算数组字面量
func (e *Evaluator) evaluateArrayLiteral(node *ast.ArrayLiteral) (any, error) {
	elements, err := e.evaluateExpressions(node.Elements)
	if err != nil {
		return nil, err
	}
	return elements, nil
}

// evaluateObjectLiteral 计算对象字面量
func (e *Evaluator) evaluateObjectLiteral(node *ast.ObjectLiteral) (any, error) {
	object := make(map[string]any)

	for _, pair := range node.Pairs {
		// 首先评估键
		key, err := e.Evaluate(pair.Key)
		if err != nil {
			return nil, err
		}

		// 将键转换为字符串
		keyStr, err := builtin.ConvertToType(key, builtin.TypeString)
		if err != nil {
			return nil, NewError(
				ErrorTypeRuntime,
				getPosition(pair.Key),
				"对象键必须能够转换为字符串",
				err,
			)
		}

		// 评估值
		value, err := e.Evaluate(pair.Value)
		if err != nil {
			return nil, err
		}

		// 存储键值对
		object[keyStr.(string)] = value
	}

	return object, nil
}

// evaluateIndexExpression 计算索引表达式
func (e *Evaluator) evaluateIndexExpression(node *ast.IndexExpression) (any, error) {
	// 计算左侧表达式（被索引的对象）
	left, err := e.Evaluate(node.Left)
	if err != nil {
		return nil, err
	}

	// 计算索引表达式
	index, err := e.Evaluate(node.Index)
	if err != nil {
		return nil, err
	}

	// 根据左侧表达式的类型进行不同处理
	switch leftType := left.(type) {
	case []any: // 数组
		return e.evaluateArrayIndex(leftType, index, node.Position)
	case map[string]any: // 对象
		return e.evaluateObjectIndex(leftType, index, node.Position)
	case string: // 字符串索引
		return e.evaluateStringIndex(leftType, index, node.Position)
	default:
		return nil, NewError(
			ErrorTypeRuntime,
			node.Position,
			fmt.Sprintf("索引操作不支持类型: %T", left),
			nil,
		)
	}
}

// evaluateArrayIndex 处理数组索引
func (e *Evaluator) evaluateArrayIndex(array []any, index any, pos ast.Position) (any, error) {
	// 索引必须是数字
	idx, ok := index.(float64)
	if !ok {
		return nil, NewError(
			ErrorTypeType,
			pos,
			fmt.Sprintf("数组索引必须是数字，得到: %T", index),
			nil,
		)
	}

	// 检查索引范围
	intIdx := int(idx)
	if intIdx < 0 || intIdx >= len(array) {
		return nil, NewError(
			ErrorTypeRuntime,
			pos,
			fmt.Sprintf("数组索引越界: %d", intIdx),
			nil,
		)
	}

	return array[intIdx], nil
}

// evaluateObjectIndex 处理对象索引
func (e *Evaluator) evaluateObjectIndex(object map[string]any, index any, pos ast.Position) (any, error) {
	// 将索引转换为字符串
	key, err := builtin.ConvertToType(index, builtin.TypeString)
	if err != nil {
		return nil, NewError(
			ErrorTypeType,
			pos,
			"对象键必须能够转换为字符串",
			err,
		)
	}

	// 查找属性
	keyStr := key.(string)
	if value, exists := object[keyStr]; exists {
		return value, nil
	}

	// 返回nil而不是错误，这符合大多数语言的行为
	return nil, nil
}

// evaluateStringIndex 处理字符串索引
func (e *Evaluator) evaluateStringIndex(str string, index any, pos ast.Position) (any, error) {
	// 索引必须是数字
	idx, ok := index.(float64)
	if !ok {
		return nil, NewError(
			ErrorTypeType,
			pos,
			fmt.Sprintf("字符串索引必须是数字，得到: %T", index),
			nil,
		)
	}

	// 检查索引范围
	intIdx := int(idx)
	if intIdx < 0 || intIdx >= len(str) {
		return nil, NewError(
			ErrorTypeRuntime,
			pos,
			fmt.Sprintf("字符串索引越界: %d", intIdx),
			nil,
		)
	}

	// 返回单个字符作为字符串
	return string(str[intIdx]), nil
}

// evaluateTernaryExpression 计算三元表达式
func (e *Evaluator) evaluateTernaryExpression(node *ast.TernaryExpression) (any, error) {
	// 计算条件表达式
	condition, err := e.Evaluate(node.Condition)
	if err != nil {
		return nil, err
	}

	// 检查条件是否为布尔值
	conditionBool, ok := condition.(bool)
	if !ok {
		// 尝试将数值类型转换为布尔值（非零为true，零为false）
		if numVal, isNum := condition.(float64); isNum {
			// 如果是数值，则非零视为true，零视为false
			conditionBool = numVal != 0
		} else {
			// 如果既不是布尔值也不是数值，则返回类型错误
			return nil, NewTypeError(node.Position, true, condition)
		}
	}

	// 根据条件选择计算哪个表达式
	if conditionBool {
		return e.Evaluate(node.TrueExpr)
	}
	return e.Evaluate(node.FalseExpr)
}

// compare 比较两个值，返回比较结果
// 返回值: -1表示left < right, 0表示left == right, 1表示left > right
func compare(left, right any, pos ast.Position) (int, error) {
	// 处理nil值
	if left == nil && right == nil {
		return 0, nil
	}
	if left == nil {
		return -1, nil
	}
	if right == nil {
		return 1, nil
	}

	// 尝试将值转换为数值进行比较
	leftNum, leftIsNum := toFloat64(left)
	rightNum, rightIsNum := toFloat64(right)

	// 如果两边都能转换为数值，则进行数值比较
	if leftIsNum && rightIsNum {
		if leftNum < rightNum {
			return -1, nil
		}
		if leftNum > rightNum {
			return 1, nil
		}
		return 0, nil
	}

	// 字符串比较
	leftStr, leftOk := toString(left)
	rightStr, rightOk := toString(right)
	if leftOk && rightOk {
		if leftStr < rightStr {
			return -1, nil
		}
		if leftStr > rightStr {
			return 1, nil
		}
		return 0, nil
	}

	// 布尔值比较
	leftBool, leftOk := left.(bool)
	rightBool, rightOk := right.(bool)
	if leftOk && rightOk {
		if leftBool == rightBool {
			return 0, nil
		}
		if !leftBool && rightBool {
			return -1, nil
		}
		return 1, nil
	}

	// 由于我们已经尝试了类型转换，如果代码能执行到这里
	// 说明两个值确实不可比较
	return 0, nil
}

// toFloat64 尝试将值转换为float64
func toFloat64(val any) (float64, bool) {
	// 已经是float64
	if num, ok := val.(float64); ok {
		return num, true
	}

	// 处理int类型
	if num, ok := val.(int); ok {
		return float64(num), true
	}

	// 处理int8、int16、int32、int64类型
	if num, ok := val.(int64); ok {
		return float64(num), true
	}
	if num, ok := val.(int32); ok {
		return float64(num), true
	}
	if num, ok := val.(int16); ok {
		return float64(num), true
	}
	if num, ok := val.(int8); ok {
		return float64(num), true
	}

	// 处理uint类型
	if num, ok := val.(uint); ok {
		return float64(num), true
	}
	if num, ok := val.(uint64); ok {
		return float64(num), true
	}
	if num, ok := val.(uint32); ok {
		return float64(num), true
	}
	if num, ok := val.(uint16); ok {
		return float64(num), true
	}
	if num, ok := val.(uint8); ok {
		return float64(num), true
	}

	// 尝试将字符串转换为数值
	if str, ok := val.(string); ok {
		if num, err := strconv.ParseFloat(str, 64); err == nil {
			return num, true
		}
	}

	// 布尔值转换为0或1
	if b, ok := val.(bool); ok {
		if b {
			return 1, true
		}
		return 0, true
	}

	// 无法转换
	return 0, false
}

// toString 尝试将值转换为string
func toString(val any) (string, bool) {
	// 已经是字符串
	if str, ok := val.(string); ok {
		return str, true
	}

	// 尝试将其他类型转换为字符串
	return fmt.Sprintf("%v", val), true
}

// equals 判断两个值是否相等
func equals(left, right any) bool {
	// 处理nil值
	if left == nil && right == nil {
		return true
	}
	if left == nil || right == nil {
		return false
	}

	// 尝试将值转换为数值类型比较
	leftNum, leftOk := toFloat64(left)
	rightNum, rightOk := toFloat64(right)
	if leftOk && rightOk {
		return leftNum == rightNum
	}

	// 比较字符串
	leftStr, leftOk := left.(string)
	rightStr, rightOk := right.(string)
	if leftOk && rightOk {
		return leftStr == rightStr
	}

	// 比较布尔值
	leftBool, leftOk := left.(bool)
	rightBool, rightOk := right.(bool)
	if leftOk && rightOk {
		return leftBool == rightBool
	}

	// 其他情况，如果类型不匹配则不相等
	return false
}

// getPosition 从表达式节点获取位置信息
func getPosition(expr ast.Expression) ast.Position {
	if pos, ok := expr.(ast.NodeWithPosition); ok {
		return pos.Pos()
	}
	return ast.Position{}
}

// EvaluateExpression 便捷函数，用于计算表达式
func EvaluateExpression(expr ast.Expression, variables map[string]any) (any, error) {
	ctx := NewContext()
	if variables != nil {
		ctx.SetMulti(variables)
	}

	evaluator := WithContext(ctx)
	return evaluator.Evaluate(expr)
}
