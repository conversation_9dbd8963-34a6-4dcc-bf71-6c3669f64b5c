package eval

import (
	"fmt"
	"strings"
)

// ErrorDiagnostic 表达式错误诊断信息
type ErrorDiagnostic struct {
	// 原始表达式
	Source string
	// 错误消息
	Message string
	// 错误位置
	Line   int
	Column int
	// 错误范围长度，用于精确标记出错位置
	Length int
	// 错误的严重级别
	Severity DiagnosticSeverity
	// 建议修复
	Fixes []SuggestedFix
}

// DiagnosticSeverity 诊断严重级别
type DiagnosticSeverity int

const (
	// SeverityError 错误级别
	SeverityError DiagnosticSeverity = iota
	// SeverityWarning 警告级别
	SeverityWarning
	// SeverityInfo 信息级别
	SeverityInfo
	// SeverityHint 提示级别
	SeverityHint
)

// SuggestedFix 建议修复
type SuggestedFix struct {
	// 修复描述
	Description string
	// 修复后的文本
	NewText string
	// 修复范围（开始位置）
	StartLine   int
	StartColumn int
	// 修复范围（结束位置）
	EndLine   int
	EndColumn int
}

// FormatErrorWithSource 格式化错误并带有源代码标记
func FormatErrorWithSource(source string, err error) string {
	if evalErr, ok := err.(*Error); ok {
		return formatEvalError(source, evalErr)
	}
	// 对于非表达式错误，也提供格式化的错误信息
	var result strings.Builder

	// 添加基本错误信息
	result.WriteString(fmt.Sprintf("错误: %s\n", err.Error()))

	// 添加可能的原因和建议
	result.WriteString(fmt.Sprintf("\n可能原因: 表达式解析或执行时遇到了未知错误。\n"))
	result.WriteString(fmt.Sprintf("建议修复: 请检查表达式语法是否正确，确保所有括号匹配、操作符使用正确。\n"))

	return result.String()
}

// formatEvalError 格式化表达式计算错误
func formatEvalError(source string, err *Error) string {
	var result strings.Builder

	// 添加基本错误信息
	result.WriteString(fmt.Sprintf("错误: %s\n", err.Message))

	// 如果有位置信息，格式化显示源代码和错误位置
	if err.Position.Line > 0 {
		// 分割源代码为行
		lines := strings.Split(source, "\n")
		if err.Position.Line <= len(lines) {
			// 获取错误所在行
			line := lines[err.Position.Line-1]

			// 添加行号
			result.WriteString(fmt.Sprintf("%d | %s\n", err.Position.Line, line))

			// 添加错误位置的指示箭头
			padding := strings.Repeat(" ", err.Position.Column+len(fmt.Sprintf("%d | ", err.Position.Line))-1)
			result.WriteString(fmt.Sprintf("%s^\n", padding))
		}
	}

	// 添加可能的原因和建议
	result.WriteString(fmt.Sprintf("\n可能原因: %s\n", getPossibleCause(err)))
	result.WriteString(fmt.Sprintf("建议修复: %s\n", getSuggestion(err)))

	return result.String()
}

// getPossibleCause 根据错误类型获取可能的原因
func getPossibleCause(err *Error) string {
	switch err.Type {
	case ErrorTypeType:
		return "表达式中的类型不匹配，例如尝试对非数字类型执行算术运算或对非布尔类型执行逻辑运算。"
	case ErrorTypeUndefined:
		return fmt.Sprintf("表达式中使用了未定义的变量或函数。")
	case ErrorTypeDivideByZero:
		return "表达式中包含除以零的操作。"
	case ErrorTypeRuntime:
		return "表达式在执行过程中遇到了运行时错误。"
	case ErrorTypeSyntax:
		return "表达式的语法不正确。"
	default:
		return "未知错误类型。"
	}
}

// getSuggestion 根据错误类型获取建议修复
func getSuggestion(err *Error) string {
	switch err.Type {
	case ErrorTypeType:
		return "请检查表达式中的类型，确保操作符两边的类型匹配。例如，对于算术运算，操作数应为数字；对于逻辑运算，操作数应为布尔值。"
	case ErrorTypeUndefined:
		return fmt.Sprintf("请确认变量名拼写正确，并且已在上下文中定义。尝试检查表达式中使用的变量是否传递给了计算函数。")
	case ErrorTypeDivideByZero:
		return "请修改表达式，确保除法操作的除数不为零，可以添加条件判断避免除零错误。"
	case ErrorTypeRuntime:
		return "请仔细检查表达式的逻辑，确保所有的操作都在合理范围内。"
	case ErrorTypeSyntax:
		return "请检查表达式的语法，确保括号匹配、操作符使用正确、字符串引号配对等。"
	default:
		return "请尝试简化表达式或拆分为多个小的表达式以定位问题。"
	}
}

// RecoverError 尝试从错误中恢复并继续执行
func RecoverError(source string, err error) (interface{}, bool) {
	// 对于某些错误，我们可以尝试恢复并返回一个默认值
	if evalErr, ok := err.(*Error); ok {
		switch evalErr.Type {
		case ErrorTypeDivideByZero:
			// 除零错误可以返回Infinity或者一个特定值
			return float64(0), true
		case ErrorTypeUndefined:
			// 未定义变量可以当作null处理
			return nil, true
		default:
			// 其他错误暂时无法恢复
			return nil, false
		}
	}
	return nil, false
}

// CreateDiagnostic 创建表达式诊断信息
func CreateDiagnostic(source string, err error) *ErrorDiagnostic {
	if evalErr, ok := err.(*Error); ok {
		return &ErrorDiagnostic{
			Source:   source,
			Message:  evalErr.Message,
			Line:     evalErr.Position.Line,
			Column:   evalErr.Position.Column,
			Length:   estimateErrorLength(source, evalErr),
			Severity: SeverityError,
			Fixes:    generateSuggestedFixes(source, evalErr),
		}
	}

	// 非表达式错误
	return &ErrorDiagnostic{
		Source:   source,
		Message:  err.Error(),
		Severity: SeverityError,
	}
}

// estimateErrorLength 估计错误的范围长度
func estimateErrorLength(source string, err *Error) int {
	// 如果没有位置信息，返回默认长度
	if err.Position.Line <= 0 {
		return 1
	}

	// 分割源代码为行
	lines := strings.Split(source, "\n")
	if err.Position.Line > len(lines) {
		return 1
	}

	// 获取错误所在行
	line := lines[err.Position.Line-1]

	// 根据错误类型估计长度
	switch err.Type {
	case ErrorTypeUndefined:
		// 尝试提取未定义的变量名
		if err.Position.Column <= len(line) {
			// 从错误位置开始，向右扫描直到非标识符字符
			i := err.Position.Column - 1
			for ; i < len(line); i++ {
				if !isIdentifierChar(rune(line[i])) {
					break
				}
			}
			return i - (err.Position.Column - 1)
		}
	case ErrorTypeType, ErrorTypeDivideByZero:
		// 对于类型错误和除零错误，通常与操作符相关
		// 尝试找到操作符的长度
		if err.Position.Column <= len(line) {
			// 假设操作符的长度为1-2个字符
			if err.Position.Column+1 < len(line) && isOperator(line[err.Position.Column:err.Position.Column+2]) {
				return 2
			}
			return 1
		}
	}

	// 默认返回1
	return 1
}

// generateSuggestedFixes 生成修复建议
func generateSuggestedFixes(source string, err *Error) []SuggestedFix {
	var fixes []SuggestedFix

	// 根据错误类型生成不同的修复建议
	switch err.Type {
	case ErrorTypeDivideByZero:
		// 对于除零错误，建议添加条件检查
		fixes = append(fixes, SuggestedFix{
			Description: "添加条件检查避免除零",
			NewText:     "除数 != 0 ? 表达式 : 默认值",
			StartLine:   err.Position.Line,
			StartColumn: 1,
			EndLine:     err.Position.Line,
			EndColumn:   len(strings.Split(source, "\n")[err.Position.Line-1]),
		})
	case ErrorTypeUndefined:
		// 对于未定义变量，建议检查拼写或定义变量
		fixes = append(fixes, SuggestedFix{
			Description: "检查变量名拼写或添加定义",
			NewText:     "请确保变量已定义",
			StartLine:   err.Position.Line,
			StartColumn: err.Position.Column,
			EndLine:     err.Position.Line,
			EndColumn:   err.Position.Column + estimateErrorLength(source, err),
		})
	}

	return fixes
}

// isIdentifierChar 判断是否为标识符字符
func isIdentifierChar(ch rune) bool {
	return (ch >= 'a' && ch <= 'z') ||
		(ch >= 'A' && ch <= 'Z') ||
		(ch >= '0' && ch <= '9') ||
		ch == '_'
}

// isOperator 判断字符串是否为操作符
func isOperator(s string) bool {
	operators := []string{
		"+", "-", "*", "/", "%",
		"==", "!=", "<", ">", "<=", ">=",
		"&&", "||", "!",
	}

	for _, op := range operators {
		if s == op {
			return true
		}
	}

	return false
}

// EvaluateWithDiagnostics 执行表达式并提供详细的诊断信息
func EvaluateWithDiagnostics(expression string, variables map[string]interface{}) (interface{}, *ErrorDiagnostic, error) {
	// 尝试解析和执行表达式
	result, err := EvaluateWithCache(expression, variables)
	if err != nil {
		// 创建诊断信息
		diagnostic := CreateDiagnostic(expression, err)

		// 尝试恢复错误
		if recoveredValue, ok := RecoverError(expression, err); ok {
			return recoveredValue, diagnostic, nil
		}

		return nil, diagnostic, err
	}

	return result, nil, nil
}
