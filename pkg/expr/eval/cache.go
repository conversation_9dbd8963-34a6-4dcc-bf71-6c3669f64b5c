package eval

import (
	"sync"

	"admin/pkg/expr/ast"
	"admin/pkg/expr/lexer"
	"admin/pkg/expr/parser"
)

// ExpressionCache 表达式缓存，用于存储已解析的表达式AST
type ExpressionCache struct {
	expressions map[string]ast.Expression
	mutex       sync.RWMutex
}

// NewExpressionCache 创建一个新的表达式缓存
func NewExpressionCache() *ExpressionCache {
	return &ExpressionCache{
		expressions: make(map[string]ast.Expression),
	}
}

// GlobalExpressionCache 全局表达式缓存实例
var GlobalExpressionCache = NewExpressionCache()

// Get 从缓存中获取表达式AST，如果未找到则返回nil
func (c *ExpressionCache) Get(expression string) ast.Expression {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if expr, ok := c.expressions[expression]; ok {
		return expr
	}

	return nil
}

// Set 将表达式AST添加到缓存中
func (c *ExpressionCache) Set(expression string, expr ast.Expression) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.expressions[expression] = expr
}

// Clear 清空缓存
func (c *ExpressionCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.expressions = make(map[string]ast.Expression)
}

// Size 返回当前缓存中的表达式数量
func (c *ExpressionCache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return len(c.expressions)
}

// CompileExpression 编译并缓存表达式，返回AST
func CompileExpression(expression string) (ast.Expression, error) {
	// 首先尝试从缓存获取
	if expr := GlobalExpressionCache.Get(expression); expr != nil {
		return expr, nil
	}

	// 缓存未命中，进行词法和语法分析
	l := lexer.New(expression)
	p := parser.New(l)

	expr, err := p.ParseExpression()
	if err != nil {
		return nil, err
	}

	// 解析成功，将结果存入缓存
	GlobalExpressionCache.Set(expression, expr)

	return expr, nil
}

// EvaluateWithCache 使用缓存机制计算表达式的值
func EvaluateWithCache(expression string, variables map[string]any) (any, error) {
	// 编译表达式（利用缓存）
	expr, err := CompileExpression(expression)
	if err != nil {
		return nil, err
	}

	// 创建上下文并设置变量
	ctx := NewContext()
	if variables != nil {
		ctx.SetMulti(variables)
	}

	// 预设特殊变量：true, false, null
	ctx.Set("true", true)
	ctx.Set("false", false)
	ctx.Set("null", nil)

	// 使用编译好的AST进行求值
	evaluator := WithContext(ctx)
	return evaluator.Evaluate(expr)
}
