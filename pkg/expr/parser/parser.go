package parser

import (
	"fmt"
	"strconv"

	"admin/pkg/expr/ast"
	"admin/pkg/expr/lexer"
)

// 定义运算符优先级
const (
	_ int = iota
	LOWEST
	TERNARY     // condition ? expr1 : expr2
	OR          // ||
	AND         // &&
	EQUALS      // == or !=
	LESSGREATER // > or < or >= or <=
	SUM         // + or -
	PRODUCT     // * or / or %
	PREFIX      // -X or !X
	CALL        // myFunction(X)
	INDEX       // array[index], object[key]
)

// 运算符优先级映射表
var precedences = map[lexer.TokenType]int{
	lexer.TOKEN_OR:       OR,
	lexer.TOKEN_AND:      AND,
	lexer.TOKEN_EQ:       EQUALS,
	lexer.TOKEN_NEQ:      EQUALS,
	lexer.TOKEN_LT:       LESSGREATER,
	lexer.TOKEN_GT:       LESSGREATER,
	lexer.TOKEN_LTE:      LESSGREATER,
	lexer.TOKEN_GTE:      LESSGREATER,
	lexer.TOKEN_PLUS:     SUM,
	lexer.TOKEN_MINUS:    SUM,
	lexer.TOKEN_MULTIPLY: PRODUCT,
	lexer.TOKEN_DIVIDE:   PRODUCT,
	lexer.TOKEN_MOD:      PRODUCT,
	lexer.TOKEN_LPAREN:   CALL,
	lexer.TOKEN_LBRACKET: INDEX,
	lexer.TOKEN_DOT:      INDEX,
	lexer.TOKEN_QUESTION: TERNARY,
}

// Parser 表示语法分析器
type Parser struct {
	l *lexer.Lexer

	curToken  lexer.Token // 当前token
	peekToken lexer.Token // 下一个token

	prefixParseFns map[lexer.TokenType]prefixParseFn
	infixParseFns  map[lexer.TokenType]infixParseFn

	errors []string // 解析错误
}

type (
	prefixParseFn func() ast.Expression
	infixParseFn  func(ast.Expression) ast.Expression
)

// New 创建一个新的语法分析器
func New(l *lexer.Lexer) *Parser {
	p := &Parser{
		l:              l,
		prefixParseFns: make(map[lexer.TokenType]prefixParseFn),
		infixParseFns:  make(map[lexer.TokenType]infixParseFn),
		errors:         []string{},
	}

	// 注册前缀解析函数
	p.registerPrefix(lexer.TOKEN_IDENT, p.parseIdentifier)
	p.registerPrefix(lexer.TOKEN_NUMBER, p.parseNumberLiteral)
	p.registerPrefix(lexer.TOKEN_STRING, p.parseStringLiteral)
	p.registerPrefix(lexer.TOKEN_BOOL, p.parseBooleanLiteral)
	p.registerPrefix(lexer.TOKEN_NOT, p.parsePrefixExpression)
	p.registerPrefix(lexer.TOKEN_MINUS, p.parsePrefixExpression)
	p.registerPrefix(lexer.TOKEN_LPAREN, p.parseGroupedExpression)
	p.registerPrefix(lexer.TOKEN_LBRACKET, p.parseArrayLiteral)
	p.registerPrefix(lexer.TOKEN_LBRACE, p.parseObjectLiteral)
	p.registerPrefix(lexer.TOKEN_NULL, p.parseNullLiteral)

	// 注册中缀解析函数
	p.registerInfix(lexer.TOKEN_PLUS, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_MINUS, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_MULTIPLY, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_DIVIDE, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_MOD, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_EQ, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_NEQ, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_LT, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_GT, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_LTE, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_GTE, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_AND, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_OR, p.parseInfixExpression)
	p.registerInfix(lexer.TOKEN_LPAREN, p.parseCallExpression)
	p.registerInfix(lexer.TOKEN_LBRACKET, p.parseIndexExpression)
	p.registerInfix(lexer.TOKEN_DOT, p.parseDotExpression)
	p.registerInfix(lexer.TOKEN_QUESTION, p.parseTernaryExpression)

	// 读取第一个token作为当前token
	p.curToken = l.NextToken()
	// 读取第二个token作为预读token
	p.peekToken = l.NextToken()

	return p
}

// Errors 返回解析过程中的错误
func (p *Parser) Errors() []string {
	return p.errors
}

// registerPrefix 注册前缀解析函数
func (p *Parser) registerPrefix(tokenType lexer.TokenType, fn prefixParseFn) {
	p.prefixParseFns[tokenType] = fn
}

// registerInfix 注册中缀解析函数
func (p *Parser) registerInfix(tokenType lexer.TokenType, fn infixParseFn) {
	p.infixParseFns[tokenType] = fn
}

// nextToken 移动到下一个token
func (p *Parser) nextToken() {
	p.curToken = p.peekToken
	p.peekToken = p.l.NextToken()
}

// curTokenIs 判断当前token的类型
func (p *Parser) curTokenIs(t lexer.TokenType) bool {
	return p.curToken.Type == t
}

// peekTokenIs 判断下一个token的类型
func (p *Parser) peekTokenIs(t lexer.TokenType) bool {
	return p.peekToken.Type == t
}

// expectPeek 期望下一个token是指定类型，如果是则前进，否则添加错误
func (p *Parser) expectPeek(t lexer.TokenType) bool {
	if p.peekTokenIs(t) {
		p.nextToken()
		return true
	}
	p.peekError(t)
	return false
}

// peekError 添加期望token类型不匹配的错误
func (p *Parser) peekError(t lexer.TokenType) {
	msg := fmt.Sprintf("括号不匹配: 期望 %v，但找到 %v",
		t, p.peekToken.Type)
	p.errors = append(p.errors, msg)
}

// peekPrecedence 返回下一个token的优先级
func (p *Parser) peekPrecedence() int {
	if p, ok := precedences[p.peekToken.Type]; ok {
		return p
	}
	return LOWEST
}

// curPrecedence 返回当前token的优先级
func (p *Parser) curPrecedence() int {
	if p, ok := precedences[p.curToken.Type]; ok {
		return p
	}
	return LOWEST
}

// ParseExpression 解析表达式
func (p *Parser) ParseExpression() (ast.Expression, error) {
	if p.curToken.Type == lexer.TOKEN_EOF {
		return nil, fmt.Errorf("unexpected end of file")
	}

	expr, err := p.parseExpression(LOWEST)
	if err != nil {
		return nil, err
	}
	return expr, nil
}

// parseExpression 使用运算符优先级解析表达式
func (p *Parser) parseExpression(precedence int) (ast.Expression, error) {
	prefix := p.prefixParseFns[p.curToken.Type]
	if prefix == nil {
		return nil, fmt.Errorf("无法解析表达式: 不支持的符号 %v", p.curToken.Type)
	}

	leftExp := prefix()
	if leftExp == nil {
		if len(p.errors) > 0 {
			return nil, fmt.Errorf("parse error: %s", p.errors[len(p.errors)-1])
		}
		return nil, fmt.Errorf("parse error: failed to parse expression starting with %v", p.curToken.Type)
	}

	for !p.peekTokenIs(lexer.TOKEN_EOF) && precedence < p.peekPrecedence() {
		infix := p.infixParseFns[p.peekToken.Type]
		if infix == nil {
			break
		}

		p.nextToken()
		leftExp = infix(leftExp)
		if leftExp == nil {
			if len(p.errors) > 0 {
				return nil, fmt.Errorf("parse error: %s", p.errors[len(p.errors)-1])
			}
			return nil, fmt.Errorf("parse error: failed to parse infix expression with operator %v", p.curToken.Type)
		}
	}

	return leftExp, nil
}

// parseIdentifier 解析标识符
func (p *Parser) parseIdentifier() ast.Expression {
	return &ast.Identifier{
		Name: p.curToken.Literal,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}
}

// parseNumberLiteral 解析数字字面量
func (p *Parser) parseNumberLiteral() ast.Expression {
	value, err := strconv.ParseFloat(p.curToken.Literal, 64)
	if err != nil {
		p.errors = append(p.errors, fmt.Sprintf("could not parse %q as float", p.curToken.Literal))
		return nil
	}

	return &ast.NumberLiteral{
		Value:   value,
		Literal: p.curToken.Literal,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}
}

// parseStringLiteral 解析字符串字面量
func (p *Parser) parseStringLiteral() ast.Expression {
	return &ast.StringLiteral{
		Value: p.curToken.Literal,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}
}

// 修正parseBooleanLiteral函数，正确处理布尔值字面量
func (p *Parser) parseBooleanLiteral() ast.Expression {
	if p.curToken.Type != lexer.TOKEN_BOOL {
		p.errors = append(p.errors, fmt.Sprintf("expected boolean token, got %s", p.curToken.Type))
		return nil
	}

	var value bool
	// 根据字面值确定布尔值
	if p.curToken.Literal == "true" {
		value = true
	} else if p.curToken.Literal == "false" {
		value = false
	} else {
		p.errors = append(p.errors, fmt.Sprintf("invalid boolean literal %q", p.curToken.Literal))
		return nil
	}

	return &ast.BooleanLiteral{
		Value: value,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}
}

// parsePrefixExpression 解析前缀表达式
func (p *Parser) parsePrefixExpression() ast.Expression {
	expression := &ast.UnaryExpression{
		Operator: p.curToken.Literal,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	p.nextToken()
	right, err := p.parseExpression(PREFIX)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}
	expression.Right = right

	return expression
}

// parseInfixExpression 解析中缀表达式
func (p *Parser) parseInfixExpression(left ast.Expression) ast.Expression {
	expression := &ast.BinaryExpression{
		Left:     left,
		Operator: p.curToken.Literal,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	precedence := p.curPrecedence()
	p.nextToken()

	right, err := p.parseExpression(precedence)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}
	expression.Right = right

	return expression
}

// parseGroupedExpression 解析括号表达式
func (p *Parser) parseGroupedExpression() ast.Expression {
	p.nextToken()

	exp, err := p.parseExpression(LOWEST)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}

	if !p.expectPeek(lexer.TOKEN_RPAREN) {
		return nil
	}

	return exp
}

// parseCallExpression 解析函数调用表达式
func (p *Parser) parseCallExpression(function ast.Expression) ast.Expression {
	callExpr := &ast.CallExpression{
		Function: function,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	callExpr.Arguments = p.parseExpressionList(lexer.TOKEN_RPAREN)

	return callExpr
}

// parseExpressionList 解析表达式列表，用于函数调用参数和数组元素
func (p *Parser) parseExpressionList(end lexer.TokenType) []ast.Expression {
	var list []ast.Expression

	// 如果下一个 token 就是结束符，则返回空列表
	if p.peekTokenIs(end) {
		p.nextToken()
		return list
	}

	p.nextToken() // 移动到第一个元素

	// 解析第一个元素
	expr, err := p.parseExpression(LOWEST)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}
	list = append(list, expr)

	// 解析逗号分隔的其余元素
	for p.peekTokenIs(lexer.TOKEN_COMMA) {
		p.nextToken() // 移动到逗号
		p.nextToken() // 移动到下一个元素

		expr, err := p.parseExpression(LOWEST)
		if err != nil {
			p.errors = append(p.errors, err.Error())
			return nil
		}
		list = append(list, expr)
	}

	// 期望下一个token是结束符
	if !p.expectPeek(end) {
		return nil
	}

	return list
}

// parseArrayLiteral 解析数组字面量
func (p *Parser) parseArrayLiteral() ast.Expression {
	array := &ast.ArrayLiteral{
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	array.Elements = p.parseExpressionList(lexer.TOKEN_RBRACKET)

	return array
}

// parseObjectLiteral 解析对象字面量
func (p *Parser) parseObjectLiteral() ast.Expression {
	object := &ast.ObjectLiteral{
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
		Pairs: []ast.KeyValuePair{},
	}

	// 如果下一个token就是结束的大括号，返回空对象
	if p.peekTokenIs(lexer.TOKEN_RBRACE) {
		p.nextToken()
		return object
	}

	// 解析第一个键值对
	p.nextToken()

	if err := p.parseObjectPair(object); err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}

	// 解析后续键值对
	for p.peekTokenIs(lexer.TOKEN_COMMA) {
		p.nextToken() // 移动到逗号
		p.nextToken() // 移动到下一个键

		if err := p.parseObjectPair(object); err != nil {
			p.errors = append(p.errors, err.Error())
			return nil
		}
	}

	// 期望最后一个token是结束的大括号
	if !p.expectPeek(lexer.TOKEN_RBRACE) {
		return nil
	}

	return object
}

// parseObjectPair 解析对象的单个键值对
func (p *Parser) parseObjectPair(obj *ast.ObjectLiteral) error {
	// 解析键
	var key ast.Expression

	// 键可以是标识符、字符串或表达式
	if p.curTokenIs(lexer.TOKEN_IDENT) {
		// 如果是标识符，转换为字符串字面量，方便后续使用
		key = &ast.StringLiteral{
			Value: p.curToken.Literal,
			Position: ast.Position{
				Line:   p.curToken.Line,
				Column: p.curToken.Column,
			},
		}
	} else if p.curTokenIs(lexer.TOKEN_STRING) {
		key = p.parseStringLiteral()
	} else {
		// 允许动态计算的键：[expr]
		if p.curTokenIs(lexer.TOKEN_LBRACKET) {
			p.nextToken() // 跳过左方括号

			var err error
			key, err = p.parseExpression(LOWEST)
			if err != nil {
				return err
			}

			if !p.expectPeek(lexer.TOKEN_RBRACKET) {
				return fmt.Errorf("expected ']' after computed property")
			}
		} else {
			return fmt.Errorf("object key must be an identifier or string, got %v", p.curToken.Type)
		}
	}

	// 期望下一个token是冒号
	if !p.expectPeek(lexer.TOKEN_COLON) {
		return fmt.Errorf("expected ':' after object key")
	}

	// 移动到值
	p.nextToken()

	// 解析值
	value, err := p.parseExpression(LOWEST)
	if err != nil {
		return err
	}

	// 添加键值对
	obj.Pairs = append(obj.Pairs, ast.KeyValuePair{
		Key:   key,
		Value: value,
	})

	return nil
}

// parseIndexExpression 解析索引表达式 (arr[index] 或 obj["key"])
func (p *Parser) parseIndexExpression(left ast.Expression) ast.Expression {
	indexExpr := &ast.IndexExpression{
		Left: left,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	// 移动到索引内容
	p.nextToken()

	// 解析索引表达式
	var err error
	indexExpr.Index, err = p.parseExpression(LOWEST)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}

	// 期望下一个token是右方括号
	if !p.expectPeek(lexer.TOKEN_RBRACKET) {
		return nil
	}

	return indexExpr
}

// parseDotExpression 解析点表达式 (obj.prop)
func (p *Parser) parseDotExpression(left ast.Expression) ast.Expression {
	// 点表达式会被转换为索引表达式，其中索引是一个字符串字面量
	indexExpr := &ast.IndexExpression{
		Left: left,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	// 期望下一个token是标识符
	if !p.expectPeek(lexer.TOKEN_IDENT) {
		return nil
	}

	// 创建属性名的字符串字面量作为索引
	indexExpr.Index = &ast.StringLiteral{
		Value: p.curToken.Literal,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	return indexExpr
}

// parseNullLiteral 解析null字面量
func (p *Parser) parseNullLiteral() ast.Expression {
	// 在AST中，我们可以将null表示为一个特殊的字面量
	// 这里我们用StringLiteral类型来代表null，在求值时特殊处理
	return &ast.StringLiteral{
		Value: "null",
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}
}

// parseTernaryExpression 解析三元运算符表达式
func (p *Parser) parseTernaryExpression(condition ast.Expression) ast.Expression {
	expression := &ast.TernaryExpression{
		Condition: condition,
		Position: ast.Position{
			Line:   p.curToken.Line,
			Column: p.curToken.Column,
		},
	}

	// 解析第一个表达式（真值部分）
	p.nextToken()
	trueExpr, err := p.parseExpression(LOWEST)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}
	expression.TrueExpr = trueExpr

	// 期望下一个token是冒号
	if !p.expectPeek(lexer.TOKEN_COLON) {
		return nil
	}

	// 解析第二个表达式（假值部分）
	p.nextToken()
	falseExpr, err := p.parseExpression(LOWEST)
	if err != nil {
		p.errors = append(p.errors, err.Error())
		return nil
	}
	expression.FalseExpr = falseExpr

	return expression
}
