package parser

import (
	"testing"

	"admin/pkg/expr/ast"
	"admin/pkg/expr/lexer"

	"github.com/stretchr/testify/assert"
)

func TestNumberLiteral(t *testing.T) {
	input := "42.5566"
	l := lexer.New(input)
	p := New(l)

	expr, err := p.ParseExpression()
	assert.NoError(t, err)

	literal, ok := expr.(*ast.NumberLiteral)
	assert.True(t, ok)
	assert.Equal(t, 42.5566, literal.Value)
	assert.Equal(t, "42.5566", literal.Literal)
}

func TestStringLiteral(t *testing.T) {
	input := `"hello"`
	l := lexer.New(input)
	p := New(l)

	expr, err := p.ParseExpression()
	assert.NoError(t, err)

	literal, ok := expr.(*ast.StringLiteral)
	assert.True(t, ok)
	assert.Equal(t, "hello", literal.Value)
}

func TestBooleanLiteral(t *testing.T) {
	tests := []struct {
		input string
		value bool
	}{
		{"true", true},
		{"false", false},
	}

	for _, tt := range tests {
		l := lexer.New(tt.input)
		p := New(l)

		expr, err := p.ParseExpression()
		assert.NoError(t, err)

		literal, ok := expr.(*ast.BooleanLiteral)
		assert.True(t, ok)
		assert.Equal(t, tt.value, literal.Value)
	}
}

func TestIdentifier(t *testing.T) {
	input := "foobar"
	l := lexer.New(input)
	p := New(l)

	expr, err := p.ParseExpression()
	assert.NoError(t, err)

	ident, ok := expr.(*ast.Identifier)
	assert.True(t, ok)
	assert.Equal(t, "foobar", ident.Name)
}

func TestUnaryExpression(t *testing.T) {
	tests := []struct {
		input    string
		operator string
		value    interface{}
	}{
		{"!true", "!", true},
		{"-42.5", "-", 42.5},
	}

	for _, tt := range tests {
		l := lexer.New(tt.input)
		p := New(l)

		expr, err := p.ParseExpression()
		assert.NoError(t, err)

		unary, ok := expr.(*ast.UnaryExpression)
		assert.True(t, ok)
		assert.Equal(t, tt.operator, unary.Operator)

		switch v := tt.value.(type) {
		case bool:
			right, ok := unary.Right.(*ast.BooleanLiteral)
			assert.True(t, ok)
			assert.Equal(t, v, right.Value)
		case float64:
			right, ok := unary.Right.(*ast.NumberLiteral)
			assert.True(t, ok)
			assert.Equal(t, v, right.Value)
		}
	}
}

func TestBinaryExpression(t *testing.T) {
	tests := []struct {
		input      string
		leftVal    float64
		operator   string
		rightVal   float64
		precedence int
	}{
		{"1 + 2", 1, "+", 2, SUM},
		{"1 - 2", 1, "-", 2, SUM},
		{"1 * 2", 1, "*", 2, PRODUCT},
		{"1 / 2", 1, "/", 2, PRODUCT},
		{"1 % 2", 1, "%", 2, PRODUCT},
	}

	for _, tt := range tests {
		l := lexer.New(tt.input)
		p := New(l)

		expr, err := p.ParseExpression()
		assert.NoError(t, err)

		binary, ok := expr.(*ast.BinaryExpression)
		assert.True(t, ok)
		assert.Equal(t, tt.operator, binary.Operator)

		left, ok := binary.Left.(*ast.NumberLiteral)
		assert.True(t, ok)
		assert.Equal(t, tt.leftVal, left.Value)

		right, ok := binary.Right.(*ast.NumberLiteral)
		assert.True(t, ok)
		assert.Equal(t, tt.rightVal, right.Value)
	}
}

func TestOperatorPrecedence(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{
			"1 + 2 * 3",
			"(1 + (2 * 3))",
		},
		{
			"-1 * 2 + 3",
			"(((-1) * 2) + 3)",
		},
		{
			"1 + 2 * 3 + 4",
			"((1 + (2 * 3)) + 4)",
		},
		{
			"(1 + 2) * 3",
			"((1 + 2) * 3)",
		},
		{
			"x > 5 && y < 10",
			"((x > 5) && (y < 10))",
		},
		{
			"a == b || c != d",
			"((a == b) || (c != d))",
		},
	}

	for _, tt := range tests {
		l := lexer.New(tt.input)
		p := New(l)

		expr, err := p.ParseExpression()
		assert.NoError(t, err)
		assert.Equal(t, tt.expected, expr.String())
	}
}

func TestErrors(t *testing.T) {
	tests := []struct {
		input       string
		errorPrefix string
	}{
		{
			"@",
			"无法解析表达式: 不支持的符号 ILLEGAL",
		},
		{
			"(1 + 2",
			"括号不匹配: 期望 )，但找到 EOF",
		},
	}

	for _, tt := range tests {
		l := lexer.New(tt.input)
		p := New(l)

		_, err := p.ParseExpression()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), tt.errorPrefix)
	}
}

// TestTernaryExpression 测试三元运算符
func TestTernaryExpression(t *testing.T) {
	tests := []struct {
		name         string
		input        string
		condition    string
		trueExpr     string
		falseExpr    string
		expectedTree string
	}{
		{
			name:         "简单三元表达式",
			input:        "x > 5 ? 'greater' : 'less'",
			condition:    "(x > 5)",
			trueExpr:     "\"greater\"",
			falseExpr:    "\"less\"",
			expectedTree: "((x > 5) ? \"greater\" : \"less\")",
		},
		{
			name:         "嵌套三元表达式",
			input:        "x > 10 ? 'big' : x > 5 ? 'medium' : 'small'",
			condition:    "(x > 10)",
			trueExpr:     "\"big\"",
			falseExpr:    "((x > 5) ? \"medium\" : \"small\")",
			expectedTree: "((x > 10) ? \"big\" : ((x > 5) ? \"medium\" : \"small\"))",
		},
		{
			name:         "三元表达式与二元运算",
			input:        "x + y > 10 ? x * 2 : y * 3",
			condition:    "((x + y) > 10)",
			trueExpr:     "(x * 2)",
			falseExpr:    "(y * 3)",
			expectedTree: "(((x + y) > 10) ? (x * 2) : (y * 3))",
		},
		{
			name:         "三元表达式与函数调用",
			input:        "isValid(user) ? getName(user) : 'Unknown'",
			condition:    "isValid(user)",
			trueExpr:     "getName(user)",
			falseExpr:    "\"Unknown\"",
			expectedTree: "(isValid(user) ? getName(user) : \"Unknown\")",
		},
		{
			name:         "三元表达式与逻辑运算",
			input:        "x > 0 && y > 0 ? 'positive' : 'negative'",
			condition:    "((x > 0) && (y > 0))",
			trueExpr:     "\"positive\"",
			falseExpr:    "\"negative\"",
			expectedTree: "(((x > 0) && (y > 0)) ? \"positive\" : \"negative\")",
		},
		{
			name:         "复杂嵌套三元表达式",
			input:        "score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F'",
			condition:    "(score >= 90)",
			trueExpr:     "\"A\"",
			falseExpr:    "((score >= 80) ? \"B\" : ((score >= 70) ? \"C\" : ((score >= 60) ? \"D\" : \"F\")))",
			expectedTree: "((score >= 90) ? \"A\" : ((score >= 80) ? \"B\" : ((score >= 70) ? \"C\" : ((score >= 60) ? \"D\" : \"F\"))))",
		},
		{
			name:         "带括号的三元表达式",
			input:        "(x > 5) ? ('yes' + '!') : ('no' + '?')",
			condition:    "(x > 5)",
			trueExpr:     "(\"yes\" + \"!\")",
			falseExpr:    "(\"no\" + \"?\")",
			expectedTree: "((x > 5) ? (\"yes\" + \"!\") : (\"no\" + \"?\"))",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := lexer.New(tt.input)
			p := New(l)

			expr, err := p.ParseExpression()
			assert.NoError(t, err, "解析应该成功")

			ternary, ok := expr.(*ast.TernaryExpression)
			assert.True(t, ok, "应该解析为三元表达式")

			assert.Equal(t, tt.expectedTree, expr.String(), "AST树表示应匹配预期")

			// 检查条件表达式、真值表达式和假值表达式是否正确解析
			if ok && ternary != nil {
				assert.NotNil(t, ternary.Condition, "条件不应为空")
				assert.NotNil(t, ternary.TrueExpr, "真值表达式不应为空")
				assert.NotNil(t, ternary.FalseExpr, "假值表达式不应为空")
			}
		})
	}
}
