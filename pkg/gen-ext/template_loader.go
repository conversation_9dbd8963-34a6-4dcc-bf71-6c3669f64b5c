package genext

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v3"
)

// TemplateConfig 模板配置
type TemplateConfig struct {
	Templates       []TemplateItem `yaml:"templates"`
	CustomTemplates []TemplateItem `yaml:"custom_templates"`
}

// TemplateItem 模板项
type TemplateItem struct {
	Name             string   `yaml:"name"`
	Description      string   `yaml:"description"`
	Condition        string   `yaml:"condition"`
	ExcludeCondition string   `yaml:"exclude_condition"`
	Imports          []string `yaml:"imports"`
	Template         string   `yaml:"template"`
}

// TemplateLoader 模板加载器
type TemplateLoader struct {
	configPath string
}

// NewTemplateLoader 创建模板加载器
func NewTemplateLoader(configPath string) *TemplateLoader {
	return &TemplateLoader{
		configPath: configPath,
	}
}

// LoadTemplates 加载模板
func (l *TemplateLoader) LoadTemplates() ([]MethodTemplate, error) {
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read template config: %w", err)
	}

	var config TemplateConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse template config: %w", err)
	}

	var templates []MethodTemplate

	// 加载标准模板
	for _, item := range config.Templates {
		template := l.convertToMethodTemplate(item)
		templates = append(templates, template)
	}

	// 加载自定义模板
	for _, item := range config.CustomTemplates {
		template := l.convertToMethodTemplate(item)
		templates = append(templates, template)
	}

	return templates, nil
}

// convertToMethodTemplate 转换为方法模板
func (l *TemplateLoader) convertToMethodTemplate(item TemplateItem) MethodTemplate {
	return MethodTemplate{
		Name:        item.Name,
		Description: item.Description,
		Template:    item.Template,
		Condition:   l.parseCondition(item.Condition, item.ExcludeCondition),
		Imports:     item.Imports,
	}
}

// parseCondition 解析条件
func (l *TemplateLoader) parseCondition(condition string, excludeCondition string) func(string, map[string]bool) bool {
	return func(tableName string, hasFields map[string]bool) bool {
		// 首先检查基本条件是否满足
		conditionMet := false
		
		if strings.Contains(condition, ",") {
			// 多个字段都必须存在 (AND条件)
			fields := strings.Split(condition, ",")
			conditionMet = true
			for _, field := range fields {
				field = strings.TrimSpace(field)
				if !hasFields[field] {
					conditionMet = false
					break
				}
			}
		} else if strings.Contains(condition, "|") {
			// 任意一个字段存在即可 (OR条件)
			fields := strings.Split(condition, "|")
			for _, field := range fields {
				field = strings.TrimSpace(field)
				if hasFields[field] {
					conditionMet = true
					break
				}
			}
		} else {
			// 单个字段条件
			conditionMet = hasFields[strings.TrimSpace(condition)]
		}
		
		// 如果基本条件不满足，直接返回false
		if !conditionMet {
			return false
		}
		
		// 检查排除条件
		if excludeCondition != "" {
			if strings.Contains(excludeCondition, ",") {
				// 如果任意一个排除字段存在，则不生成
				fields := strings.Split(excludeCondition, ",")
				for _, field := range fields {
					field = strings.TrimSpace(field)
					if hasFields[field] {
						return false
					}
				}
			} else if strings.Contains(excludeCondition, "|") {
				// 如果所有排除字段都存在，则不生成
				fields := strings.Split(excludeCondition, "|")
				allExist := true
				for _, field := range fields {
					field = strings.TrimSpace(field)
					if !hasFields[field] {
						allExist = false
						break
					}
				}
				if allExist {
					return false
				}
			} else {
				// 单个排除字段条件
				if hasFields[strings.TrimSpace(excludeCondition)] {
					return false
				}
			}
		}
		
		return true
	}
}

// Enhanced MethodTemplate with imports
type EnhancedMethodTemplate struct {
	MethodTemplate
	Imports []string
}
