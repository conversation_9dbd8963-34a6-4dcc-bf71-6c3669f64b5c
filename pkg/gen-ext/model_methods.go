package genext

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
)

// MethodTemplate 方法模板
type MethodTemplate struct {
	Name        string                                              // 方法名称
	Template    string                                              // 模板内容
	Condition   func(tableName string, hasFields map[string]bool) bool // 生成条件
	Description string                                              // 描述
	Imports     []string                                            // 需要的导入
}

// ModelMethodsGenerator 模型方法生成器
type ModelMethodsGenerator struct {
	ModelDir       string
	Templates      []MethodTemplate
	TemplateLoader *TemplateLoader
}

// NewModelMethodsGenerator 创建模型方法生成器
func NewModelMethodsGenerator(modelDir string) *ModelMethodsGenerator {
	return &ModelMethodsGenerator{
		ModelDir:  modelDir,
		Templates: []MethodTemplate{}, // 空的，将从配置文件加载
	}
}

// LoadTemplatesFromConfig 从配置文件加载模板
func (g *ModelMethodsGenerator) LoadTemplatesFromConfig(configPath string) error {
	loader := NewTemplateLoader(configPath)
	templates, err := loader.LoadTemplates()
	if err != nil {
		return err
	}
	g.Templates = templates
	return nil
}

// AddTemplate 添加自定义模板
func (g *ModelMethodsGenerator) AddTemplate(template MethodTemplate) {
	g.Templates = append(g.Templates, template)
}

// GenerateForTable 为指定表生成方法
func (g *ModelMethodsGenerator) GenerateForTable(tableName, modelName string, hasFields map[string]bool, extraTemplates ...[]MethodTemplate) error {
	modelFile := filepath.Join(g.ModelDir, tableName+".gen.go")
	
	// 读取现有文件内容
	content, err := os.ReadFile(modelFile)
	if err != nil {
		return fmt.Errorf("failed to read model file %s: %w", modelFile, err)
	}

	originalContent := string(content)
	newContent := originalContent
	var allImports []string

	// 合并所有模板
	allTemplates := g.Templates
	for _, templates := range extraTemplates {
		allTemplates = append(allTemplates, templates...)
	}

	// 为每个模板生成方法
	for _, tmpl := range allTemplates {
		if tmpl.Condition(tableName, hasFields) {
			method, err := g.generateMethod(tmpl, tableName, modelName, hasFields)
			if err != nil {
				return fmt.Errorf("failed to generate method %s: %w", tmpl.Name, err)
			}

			// 检查方法是否已存在
			if !g.methodExists(originalContent, modelName, tmpl.Name) {
				newContent += "\n" + method
				allImports = append(allImports, tmpl.Imports...)
			}
		}
	}

	// 添加必要的导入
	if len(allImports) > 0 {
		if err := g.AddImportsToContent(&newContent, allImports); err != nil {
			return fmt.Errorf("failed to add imports: %w", err)
		}
	}

	// 如果内容有变化，写回文件
	if newContent != originalContent {
		return os.WriteFile(modelFile, []byte(newContent), 0644)
	}

	return nil
}

// methodExists 检查方法是否已存在
func (g *ModelMethodsGenerator) methodExists(content, modelName, methodName string) bool {
	patterns := []string{
		fmt.Sprintf("func (*%s) %s", modelName, methodName),
		fmt.Sprintf("func (%s) %s", strings.ToLower(modelName[:1]), methodName),
		fmt.Sprintf("func (m *%s) %s", modelName, methodName),
	}
	
	for _, pattern := range patterns {
		if strings.Contains(content, pattern) {
			return true
		}
	}
	return false
}

// generateMethod 生成单个方法
func (g *ModelMethodsGenerator) generateMethod(tmpl MethodTemplate, tableName, modelName string, hasFields map[string]bool) (string, error) {
	t, err := template.New(tmpl.Name).Parse(tmpl.Template)
	if err != nil {
		return "", err
	}

	data := struct {
		TableName string
		ModelName string
		HasFields map[string]bool
	}{
		TableName: tableName,
		ModelName: modelName,
		HasFields: hasFields,
	}

	var buf strings.Builder
	if err := t.Execute(&buf, data); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// AddImportsToContent 向内容添加必要的import
func (g *ModelMethodsGenerator) AddImportsToContent(content *string, imports []string) error {
	if len(imports) == 0 {
		return nil
	}

	// 去重导入，只添加不存在的导入
	var needImports []string
	for _, imp := range imports {
		// 更精确的检查：在import块中查找
		importExists := false
		lines := strings.Split(*content, "\n")
		inImportBlock := false
		
		for _, line := range lines {
			trimmedLine := strings.TrimSpace(line)
			if strings.Contains(line, "import (") {
				inImportBlock = true
				continue
			}
			if inImportBlock && trimmedLine == ")" {
				inImportBlock = false
				break
			}
			if inImportBlock && strings.Contains(trimmedLine, imp) {
				importExists = true
				break
			}
		}
		
		if !importExists {
			// 额外检查是否已经在needImports中
			found := false
			for _, existing := range needImports {
				if existing == imp {
					found = true
					break
				}
			}
			if !found {
				needImports = append(needImports, imp)
			}
		}
	}

	if len(needImports) == 0 {
		return nil
	}

	lines := strings.Split(*content, "\n")
	var result []string
	importBlockFound := false
	importBlockStart := -1
	importBlockEnd := -1

	// 找到import块的位置
	for i, line := range lines {
		if strings.Contains(line, "import (") {
			importBlockFound = true
			importBlockStart = i
			// 找到import块的结束位置
			for j := i + 1; j < len(lines); j++ {
				if strings.TrimSpace(lines[j]) == ")" {
					importBlockEnd = j
					break
				}
			}
			break
		}
	}

	if !importBlockFound {
		// 如果没有import块，在package后面创建一个
		for i, line := range lines {
			result = append(result, line)
			if strings.HasPrefix(line, "package ") {
				result = append(result, "")
				result = append(result, "import (")
				for _, imp := range needImports {
					result = append(result, "\t"+imp)
				}
				result = append(result, ")")
				// 添加剩余的行
				if i+1 < len(lines) {
					result = append(result, lines[i+1:]...)
				}
				break
			}
		}
	} else {
		// 如果有import块，在现有import块中添加新的导入
		for i, line := range lines {
			if i < importBlockStart || i > importBlockEnd {
				result = append(result, line)
			} else if i == importBlockStart {
				result = append(result, line) // import (
				// 添加现有的导入
				for j := importBlockStart + 1; j < importBlockEnd; j++ {
					result = append(result, lines[j])
				}
				// 添加新的导入
				for _, imp := range needImports {
					result = append(result, "\t"+imp)
				}
			} else if i == importBlockEnd {
				result = append(result, line) // )
			}
			// 跳过import块中间的行，因为已经在上面处理了
		}
	}

	*content = strings.Join(result, "\n")
	return nil
}
