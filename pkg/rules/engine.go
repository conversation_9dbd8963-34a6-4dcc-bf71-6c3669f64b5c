// Package rules 提供规则引擎的初始化和扩展功能
package rules

import (
	"admin/pkg/rules/condition"
	"admin/pkg/rules/core"
)

// 优化级别定义
const (
	// OptimizationLevelNone 不进行优化
	OptimizationLevelNone = iota

	// OptimizationLevelBasic 基本优化（仅延迟计算）
	OptimizationLevelBasic

	// OptimizationLevelIntermediate 中等优化（延迟计算 + 条件合并）
	OptimizationLevelIntermediate

	// OptimizationLevelAdvanced 高级优化（延迟计算 + 条件合并 + 自适应重排序）
	OptimizationLevelAdvanced
)

// CreateEngine 创建并初始化规则引擎
// 配置了高级条件优化器，启用所有优化功能
func CreateEngine(concurrent bool, maxWorkers int) *core.DefaultRuleEngine {
	return CreateEngineWithOptimizationLevel(concurrent, maxWorkers, OptimizationLevelAdvanced)
}

// CreateEngineWithOptimizationLevel 创建规则引擎，并指定优化级别
func CreateEngineWithOptimizationLevel(concurrent bool, maxWorkers int, level int) *core.DefaultRuleEngine {
	// 创建规则引擎
	engine := core.NewDefaultRuleEngine(concurrent, maxWorkers)

	// 根据优化级别创建和配置优化器
	if level > OptimizationLevelNone {
		optimizer := condition.NewAdvancedConditionOptimizer()

		// 根据优化级别配置优化器
		switch level {
		case OptimizationLevelBasic:
			// 仅启用延迟计算
			optimizer.SetLazyEvaluationEnabled(true)
			optimizer.SetConditionMergingEnabled(false)
			optimizer.SetAdaptiveReorderingEnabled(false)

		case OptimizationLevelIntermediate:
			// 启用延迟计算和条件合并
			optimizer.SetLazyEvaluationEnabled(true)
			optimizer.SetConditionMergingEnabled(true)
			optimizer.SetAdaptiveReorderingEnabled(false)

		case OptimizationLevelAdvanced:
			// 启用所有优化
			optimizer.SetLazyEvaluationEnabled(true)
			optimizer.SetConditionMergingEnabled(true)
			optimizer.SetAdaptiveReorderingEnabled(true)
		}

		// 设置优化器到引擎
		engine.SetOptimizer(optimizer)
		engine.SetOptimizationEnabled(true)
	} else {
		// 禁用优化
		engine.SetOptimizationEnabled(false)
	}

	return engine
}

// EnableStatisticsCollection 启用规则统计收集
// 统计数据用于自适应条件重排序
func EnableStatisticsCollection() {
	condition.GetGlobalStatsCollector().Enable()
}

// DisableStatisticsCollection 禁用规则统计收集
func DisableStatisticsCollection() {
	condition.GetGlobalStatsCollector().Disable()
}

// ResetStatistics 重置统计数据
func ResetStatistics() {
	condition.GetGlobalStatsCollector().Reset()
}

// GetStatistics 获取所有条件的统计数据
func GetStatistics() []condition.ConditionStats {
	return condition.GetGlobalStatsCollector().GetAllStats()
}

// OptimizeExistingEngine 对已有的规则引擎应用优化
// 对于已经创建但没有启用优化的引擎，可以使用此函数添加优化能力
func OptimizeExistingEngine(engine *core.DefaultRuleEngine, level int) {
	if engine == nil {
		return
	}

	// 创建并配置优化器
	optimizer := condition.NewAdvancedConditionOptimizer()

	// 根据优化级别配置优化器
	switch level {
	case OptimizationLevelBasic:
		// 仅启用延迟计算
		optimizer.SetLazyEvaluationEnabled(true)
		optimizer.SetConditionMergingEnabled(false)
		optimizer.SetAdaptiveReorderingEnabled(false)

	case OptimizationLevelIntermediate:
		// 启用延迟计算和条件合并
		optimizer.SetLazyEvaluationEnabled(true)
		optimizer.SetConditionMergingEnabled(true)
		optimizer.SetAdaptiveReorderingEnabled(false)

	case OptimizationLevelAdvanced:
		// 启用所有优化
		optimizer.SetLazyEvaluationEnabled(true)
		optimizer.SetConditionMergingEnabled(true)
		optimizer.SetAdaptiveReorderingEnabled(true)

	default:
		// 禁用所有优化
		engine.SetOptimizationEnabled(false)
		return
	}

	// 设置优化器到引擎
	engine.SetOptimizer(optimizer)
	engine.SetOptimizationEnabled(true)

	// 优化已有规则
	engine.OptimizeExistingRules()
}
