package path

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"
)

// 测试基本路径解析功能
func TestBasicPathParsing(t *testing.T) {
	// 创建一个测试数据
	data := map[string]interface{}{
		"user": map[string]interface{}{
			"name": "张三",
			"age":  30,
			"address": map[string]interface{}{
				"city":    "上海",
				"zipcode": "200000",
			},
		},
		"orders": []interface{}{
			map[string]interface{}{
				"id":    "order-001",
				"total": 100.50,
				"items": []interface{}{
					map[string]interface{}{"name": "商品1", "price": 50.25},
					map[string]interface{}{"name": "商品2", "price": 50.25},
				},
			},
			map[string]interface{}{
				"id":    "order-002",
				"total": 200.75,
				"items": []interface{}{
					map[string]interface{}{"name": "商品3", "price": 100.25},
					map[string]interface{}{"name": "商品4", "price": 100.50},
				},
			},
		},
		"tags":    []string{"标签1", "标签2", "标签3"},
		"enabled": true,
		"score":   85.5,
	}

	// 创建解析器
	parser := NewPathParser()

	// 测试用例
	tests := []struct {
		name     string
		path     string
		expected interface{}
		wantErr  bool
	}{
		{
			name:     "获取简单属性",
			path:     "user.name",
			expected: "张三",
			wantErr:  false,
		},
		{
			name:     "获取嵌套属性",
			path:     "user.address.city",
			expected: "上海",
			wantErr:  false,
		},
		{
			name:     "获取数组指定索引",
			path:     "orders[0].id",
			expected: "order-001",
			wantErr:  false,
		},
		{
			name:     "获取深层嵌套属性",
			path:     "orders[1].items[0].name",
			expected: "商品3",
			wantErr:  false,
		},
		{
			name:     "获取整个数组",
			path:     "tags",
			expected: []string{"标签1", "标签2", "标签3"},
			wantErr:  false,
		},
		{
			name:     "获取简单数组索引",
			path:     "tags[1]",
			expected: "标签2",
			wantErr:  false,
		},
		{
			name:     "获取布尔值",
			path:     "enabled",
			expected: true,
			wantErr:  false,
		},
		{
			name:     "获取数值",
			path:     "score",
			expected: 85.5,
			wantErr:  false,
		},
		{
			name:     "路径不存在",
			path:     "user.nonexistent",
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "数组索引越界",
			path:     "tags[10]",
			expected: nil,
			wantErr:  true,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parser.GetValue(data, tt.path)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望错误，不需要检查结果
			if tt.wantErr {
				return
			}

			// 检查结果是否匹配
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("GetValue() got = %v, want %v", got, tt.expected)
			}
		})
	}
}

// 测试数组通配符功能
func TestArrayWildcard(t *testing.T) {
	// 创建一个包含数组的测试数据
	data := map[string]interface{}{
		"users": []interface{}{
			map[string]interface{}{"id": 1, "name": "张三", "active": true},
			map[string]interface{}{"id": 2, "name": "李四", "active": false},
			map[string]interface{}{"id": 3, "name": "王五", "active": true},
		},
		"departments": []interface{}{
			map[string]interface{}{
				"name": "技术部",
				"employees": []interface{}{
					map[string]interface{}{"id": 101, "name": "张工"},
					map[string]interface{}{"id": 102, "name": "李工"},
				},
			},
			map[string]interface{}{
				"name": "市场部",
				"employees": []interface{}{
					map[string]interface{}{"id": 201, "name": "王经理"},
					map[string]interface{}{"id": 202, "name": "赵主管"},
				},
			},
		},
	}

	parser := NewPathParser()

	// 测试用例
	tests := []struct {
		name     string
		path     string
		expected interface{}
		wantErr  bool
	}{
		{
			name:     "获取所有用户名",
			path:     "users[*].name",
			expected: []interface{}{"张三", "李四", "王五"},
			wantErr:  false,
		},
		{
			name:     "获取所有活跃用户ID",
			path:     "users[*].id",
			expected: []interface{}{1, 2, 3},
			wantErr:  false,
		},
		{
			name:     "获取所有部门名称",
			path:     "departments[*].name",
			expected: []interface{}{"技术部", "市场部"},
			wantErr:  false,
		},
		{
			name:     "获取所有部门的所有员工名称（嵌套数组）",
			path:     "departments[*].employees[*].name",
			expected: []interface{}{"张工", "李工", "王经理", "赵主管"},
			wantErr:  false,
		},
		{
			name:     "获取不存在路径",
			path:     "users[*].address",
			expected: []interface{}{},
			wantErr:  false,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parser.GetValue(data, tt.path)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望错误，不需要检查结果
			if tt.wantErr {
				return
			}

			// 检查结果是否匹配
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("GetValue() got = %v, want %v", got, tt.expected)
			}
		})
	}
}

// 测试设置值功能
func TestSetValue(t *testing.T) {
	// 创建测试数据
	data := map[string]interface{}{
		"user": map[string]interface{}{
			"name": "张三",
			"age":  30,
		},
		"items": []interface{}{
			map[string]interface{}{"id": 1, "name": "项目1"},
			map[string]interface{}{"id": 2, "name": "项目2"},
		},
	}

	parser := NewPathParser()

	// 测试用例
	tests := []struct {
		name      string
		path      string
		value     interface{}
		checkPath string
		expected  interface{}
		wantErr   bool
	}{
		{
			name:      "设置简单属性",
			path:      "user.name",
			value:     "李四",
			checkPath: "user.name",
			expected:  "李四",
			wantErr:   false,
		},
		{
			name:      "设置数组元素",
			path:      "items[0].name",
			value:     "新项目1",
			checkPath: "items[0].name",
			expected:  "新项目1",
			wantErr:   false,
		},
		{
			name:      "设置新属性",
			path:      "user.email",
			value:     "<EMAIL>",
			checkPath: "user.email",
			expected:  "<EMAIL>",
			wantErr:   false,
		},
		{
			name:      "设置嵌套新属性",
			path:      "user.address.city",
			value:     "北京",
			checkPath: "user.address.city",
			expected:  "北京",
			wantErr:   false,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 深拷贝测试数据，避免测试间相互影响
			testData := deepCopyMap(data)

			err := parser.SetValue(testData, tt.path, tt.value)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("SetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望错误，不需要检查结果
			if tt.wantErr {
				return
			}

			// 检查设置后的值
			got, err := parser.GetValue(testData, tt.checkPath)
			if err != nil {
				t.Errorf("GetValue() after SetValue() failed: %v", err)
				return
			}

			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("SetValue() -> GetValue() got = %v, want %v", got, tt.expected)
			}
		})
	}
}

// 测试复杂数据结构
func TestComplexDataStructure(t *testing.T) {
	// 从JSON字符串创建复杂数据结构
	jsonStr := `{
		"company": {
			"name": "示例科技有限公司",
			"founded": "2010-05-15T00:00:00Z",
			"headquarters": {
				"country": "中国",
				"city": "上海",
				"address": "浦东新区张江高科技园区"
			},
			"departments": [
				{
					"id": "dept-001",
					"name": "研发部",
					"employees": [
						{
							"id": "emp-101",
							"name": "张工程师",
							"title": "高级开发",
							"skills": ["Go", "Java", "Python"],
							"projects": [
								{"name": "项目A", "status": "进行中"},
								{"name": "项目B", "status": "已完成"}
							]
						},
						{
							"id": "emp-102",
							"name": "李设计师",
							"title": "UI设计",
							"skills": ["Figma", "Photoshop", "Sketch"],
							"projects": [
								{"name": "项目C", "status": "进行中"}
							]
						}
					]
				},
				{
					"id": "dept-002",
					"name": "市场部",
					"employees": [
						{
							"id": "emp-201",
							"name": "王经理",
							"title": "市场经理",
							"region": "华东",
							"clients": [
								{"name": "客户A", "type": "大型企业"},
								{"name": "客户B", "type": "政府机构"},
								{"name": "客户C", "type": "中小企业"}
							]
						}
					]
				}
			],
			"products": [
				{
					"id": "prod-001",
					"name": "企业管理系统",
					"version": "3.5.2",
					"features": ["人事管理", "财务管理", "库存管理", "客户关系管理"],
					"pricing": {
						"basic": 5000,
						"standard": 10000,
						"premium": 20000
					}
				},
				{
					"id": "prod-002",
					"name": "数据分析平台",
					"version": "2.1.0",
					"features": ["实时分析", "预测建模", "可视化报表", "数据集成"],
					"pricing": {
						"basic": 8000,
						"standard": 15000,
						"premium": 30000
					}
				}
			],
			"financial": {
				"revenue": {
					"2020": 10000000,
					"2021": 15000000,
					"2022": 20000000
				},
				"expenses": {
					"2020": 8000000,
					"2021": 12000000,
					"2022": 16000000
				}
			}
		}
	}`

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		t.Fatalf("无法解析JSON: %v", err)
	}

	parser := NewPathParser()

	// 测试用例
	tests := []struct {
		name     string
		path     string
		expected interface{}
		wantErr  bool
	}{
		{
			name:     "获取公司名称",
			path:     "company.name",
			expected: "示例科技有限公司",
			wantErr:  false,
		},
		{
			name:     "获取总部城市",
			path:     "company.headquarters.city",
			expected: "上海",
			wantErr:  false,
		},
		{
			name:     "获取所有部门名称",
			path:     "company.departments[*].name",
			expected: []interface{}{"研发部", "市场部"},
			wantErr:  false,
		},
		{
			name:     "获取研发部第一个员工的所有技能",
			path:     "company.departments[0].employees[0].skills",
			expected: []interface{}{"Go", "Java", "Python"},
			wantErr:  false,
		},
		{
			name:     "获取所有部门的所有员工ID",
			path:     "company.departments[*].employees[*].id",
			expected: []interface{}{"emp-101", "emp-102", "emp-201"},
			wantErr:  false,
		},
		{
			name:     "获取所有产品特性（扁平化处理）",
			path:     "company.products[*].features[*]",
			expected: []interface{}{"人事管理", "财务管理", "库存管理", "客户关系管理", "实时分析", "预测建模", "可视化报表", "数据集成"},
			wantErr:  false,
		},
		{
			name:     "获取第二个产品的高级价格",
			path:     "company.products[1].pricing.premium",
			expected: float64(30000),
			wantErr:  false,
		},
		{
			name:     "获取2021年收入",
			path:     "company.financial.revenue.2021",
			expected: float64(15000000),
			wantErr:  false,
		},
		{
			name:     "获取市场经理的所有客户类型",
			path:     "company.departments[1].employees[0].clients[*].type",
			expected: []interface{}{"大型企业", "政府机构", "中小企业"},
			wantErr:  false,
		},
		{
			name:     "获取所有员工参与的所有项目名称",
			path:     "company.departments[*].employees[*].projects[*].name",
			expected: []interface{}{"项目A", "项目B", "项目C"},
			wantErr:  false,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parser.GetValue(data, tt.path)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望错误，不需要检查结果
			if tt.wantErr {
				return
			}

			// 检查结果是否匹配
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("GetValue() got = %v, want %v", got, tt.expected)
			}
		})
	}
}

// 性能测试
func BenchmarkPathParser(b *testing.B) {
	// 创建测试数据
	data := generateBenchmarkData()

	// 创建解析器
	parser := NewPathParser()

	// 测试路径
	paths := []string{
		"simpleString",      // 简单字符串
		"simpleObject.name", // 简单对象属性
		"nestedObject.level1.level2.level3.value", // 深层嵌套
		"simpleArray[5]",                 // 数组索引
		"objectArray[10].id",             // 对象数组属性
		"objectArray[*].name",            // 数组通配符
		"nestedArrays[*].items[*].value", // 嵌套数组通配符
		"complexPath.nested.arrays[2].objects[3].deep.property", // 复杂路径
	}

	for _, path := range paths {
		b.Run(fmt.Sprintf("Path=%s", path), func(b *testing.B) {
			b.ResetTimer()
			for i := 0; i < b.N; i++ {
				_, _ = parser.GetValue(data, path)
			}
		})
	}
}

// 测试缓存性能影响
func BenchmarkCachePerformance(b *testing.B) {
	// 创建测试数据
	data := generateBenchmarkData()

	// 复杂路径
	path := "nestedArrays[*].items[*].subitems[*].properties.value"

	b.Run("WithCache", func(b *testing.B) {
		// 创建带缓存的解析器
		parser := NewPathParser()

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _ = parser.GetValue(data, path)
		}
	})

	b.Run("NoCache", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// 每次创建新解析器，避免缓存
			parser := NewPathParser()
			_, _ = parser.GetValue(data, path)
		}
	})
}

// 生成性能测试数据
func generateBenchmarkData() map[string]interface{} {
	// 创建一个大型复杂数据结构用于基准测试
	data := map[string]interface{}{
		"simpleString":  "这是一个简单字符串",
		"simpleNumber":  12345,
		"simpleBoolean": true,
		"simpleObject": map[string]interface{}{
			"name": "测试对象",
			"type": "simple",
			"tags": []string{"tag1", "tag2", "tag3"},
		},
		"simpleArray": []interface{}{1, 2, 3, 4, 5, 6, 7, 8, 9, 10},
	}

	// 添加嵌套对象
	nestedObj := map[string]interface{}{}
	current := nestedObj
	for i := 1; i <= 10; i++ {
		levelName := fmt.Sprintf("level%d", i)
		if i < 10 {
			current[levelName] = map[string]interface{}{}
			current = current[levelName].(map[string]interface{})
		} else {
			current[levelName] = map[string]interface{}{
				"value": "深度嵌套值",
				"depth": i,
			}
		}
	}
	data["nestedObject"] = nestedObj

	// 添加对象数组
	objArray := make([]interface{}, 100)
	for i := 0; i < 100; i++ {
		objArray[i] = map[string]interface{}{
			"id":    i + 1,
			"name":  fmt.Sprintf("Item %d", i+1),
			"value": i * 10,
		}
	}
	data["objectArray"] = objArray

	// 添加嵌套数组
	nestedArrays := make([]interface{}, 20)
	for i := 0; i < 20; i++ {
		items := make([]interface{}, 15)
		for j := 0; j < 15; j++ {
			subitems := make([]interface{}, 10)
			for k := 0; k < 10; k++ {
				subitems[k] = map[string]interface{}{
					"id": k,
					"properties": map[string]interface{}{
						"name":  fmt.Sprintf("Subitem %d-%d-%d", i, j, k),
						"value": i*100 + j*10 + k,
					},
				}
			}
			items[j] = map[string]interface{}{
				"id":       j,
				"name":     fmt.Sprintf("Item %d-%d", i, j),
				"value":    i*10 + j,
				"subitems": subitems,
			}
		}
		nestedArrays[i] = map[string]interface{}{
			"id":    i,
			"name":  fmt.Sprintf("Array %d", i),
			"items": items,
		}
	}
	data["nestedArrays"] = nestedArrays

	// 添加复杂路径
	complexPath := map[string]interface{}{
		"nested": map[string]interface{}{
			"arrays": []interface{}{
				map[string]interface{}{"name": "first"},
				map[string]interface{}{"name": "second"},
				map[string]interface{}{"name": "third"},
				map[string]interface{}{
					"objects": []interface{}{
						map[string]interface{}{"name": "obj1"},
						map[string]interface{}{"name": "obj2"},
						map[string]interface{}{"name": "obj3"},
						map[string]interface{}{
							"deep": map[string]interface{}{
								"property": "深层嵌套值",
							},
						},
					},
				},
			},
		},
	}
	data["complexPath"] = complexPath

	return data
}

// 辅助函数：深拷贝map
func deepCopyMap(m map[string]interface{}) map[string]interface{} {
	// 使用JSON序列化和反序列化进行深拷贝
	jsonData, _ := json.Marshal(m)
	result := make(map[string]interface{})
	_ = json.Unmarshal(jsonData, &result)
	return result
}
