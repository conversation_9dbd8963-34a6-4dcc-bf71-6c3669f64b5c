// Package path 提供JSON路径解析功能
package path

import (
	"errors"
)

// 为了兼容性定义的错误
var (
	ErrPathNotFound = errors.New("路径不存在")
	ErrInvalidPath  = errors.New("无效的路径")
)

// PathParser 转发类型，使用 path_parser.go 中的实现
type PathParser struct {
	parser *PathParserImpl
}

// NewPathParser 创建新的路径解析器
func NewPathParser() *PathParser {
	return &PathParser{
		parser: NewPathParserImpl(),
	}
}

// NewPathParserWithCacheSize 创建一个新的指定缓存大小的路径解析器
func NewPathParserWithCacheSize(cacheSize int) *PathParser {
	return &PathParser{
		parser: NewPathParserWithCacheSizeImpl(cacheSize),
	}
}

// GetValue 通过路径获取值
func (p *PathParser) GetValue(data any, path string) (any, error) {
	return p.parser.GetValue(data, path)
}

// SetValue 通过路径设置值
func (p *PathParser) SetValue(data any, path string, value any) error {
	return p.parser.SetValue(data, path, value)
}

// MustGetValue 获取路径值，错误时返回默认值
func (p *PathParser) MustGetValue(data any, path string, defaultValue any) any {
	return p.parser.MustGetValue(data, path, defaultValue)
}

// GetPathValue 从数据中获取指定路径的值（不使用实例）
func GetPathValue(data any, path string) (any, error) {
	parser := NewPathParser()
	return parser.GetValue(data, path)
}

// SetPathValue 设置指定路径的值（不使用实例）
func SetPathValue(data any, path string, value any) error {
	parser := NewPathParser()
	return parser.SetValue(data, path, value)
}

// MustGetPathValue 获取路径值，错误时返回默认值
func MustGetPathValue(data any, path string, defaultValue any) any {
	parser := NewPathParser()
	return parser.MustGetValue(data, path, defaultValue)
}
