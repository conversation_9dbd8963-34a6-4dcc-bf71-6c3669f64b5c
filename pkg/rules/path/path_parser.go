// Package path 提供JSON路径解析功能
package path

import (
	"container/list"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
)

var (
	// 预编译正则表达式，用于匹配路径中的数组访问模式
	// 如 users[0].name 或 users[*].name
	arrayPattern = regexp.MustCompile(`\[([0-9]+|\*)\]`)
	// 常见错误定义
	ErrNotIndexable      = errors.New("对象不可索引")
	ErrIndexOutOfRange   = errors.New("索引超出范围")
	ErrInvalidArrayIndex = errors.New("无效的数组索引")
	ErrInvalidTargetType = errors.New("无效的目标类型")
	ErrNotModifiable     = errors.New("目标不可修改")
)

// LRUCache LRU缓存实现
type LRUCache struct {
	capacity int
	items    map[string]*list.Element
	list     *list.List
	mu       sync.Mutex
}

// cacheItem LRU缓存项
type cacheItem struct {
	key   string
	value interface{}
}

// NewLRUCache 创建一个新的LRU缓存
func NewLRUCache(capacity int) *LRUCache {
	return &LRUCache{
		capacity: capacity,
		items:    make(map[string]*list.Element),
		list:     list.New(),
	}
}

// Put 放入缓存
func (c *LRUCache) Put(key string, value interface{}) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 如果已存在，更新值并移到最前
	if element, ok := c.items[key]; ok {
		c.list.MoveToFront(element)
		element.Value.(*cacheItem).value = value
		return
	}

	// 添加新元素到最前
	element := c.list.PushFront(&cacheItem{key, value})
	c.items[key] = element

	// 如果超出容量，移除最久未使用的元素
	if c.list.Len() > c.capacity {
		c.removeOldest()
	}
}

// Get 获取缓存
func (c *LRUCache) Get(key string) (interface{}, bool) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if element, ok := c.items[key]; ok {
		c.list.MoveToFront(element)
		return element.Value.(*cacheItem).value, true
	}

	return nil, false
}

// removeOldest 移除最久未使用的元素
func (c *LRUCache) removeOldest() {
	if element := c.list.Back(); element != nil {
		c.list.Remove(element)
		delete(c.items, element.Value.(*cacheItem).key)
	}
}

// Clear 清空缓存
func (c *LRUCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.items = make(map[string]*list.Element)
	c.list = list.New()
}

// PathParserImpl 提供从复杂对象中根据路径提取值的功能
type PathParserImpl struct {
	// 全局路径解析缓存，无过期策略，适用于高并发
	globalCache sync.Map

	// LRU路径解析缓存，有容量限制，适用于频繁访问的路径
	lruCache *LRUCache
}

// PathSegment 路径段，表示路径中的一部分
type PathSegment struct {
	// 属性名
	Name string

	// 数组索引，如果为-1表示不是数组访问，如果为-2表示访问所有元素[*]
	Index int

	// 是否是数组元素访问
	IsArrayAccess bool

	// 原始路径字符串
	Original string
}

// NewPathParserImpl 创建一个新的路径解析器
func NewPathParserImpl() *PathParserImpl {
	return &PathParserImpl{
		globalCache: sync.Map{},
		lruCache:    NewLRUCache(100), // 默认缓存100条路径
	}
}

// NewPathParserWithCacheSizeImpl 创建一个新的路径解析器，指定LRU缓存大小
func NewPathParserWithCacheSizeImpl(cacheSize int) *PathParserImpl {
	if cacheSize <= 0 {
		cacheSize = 100
	}
	return &PathParserImpl{
		globalCache: sync.Map{},
		lruCache:    NewLRUCache(cacheSize),
	}
}

// GetValue 根据路径从数据中获取值
// 支持以下路径格式:
// - 简单属性: user.name
// - 数组索引: users[0].name
// - 所有数组元素: users[*].name (返回所有匹配值的切片)
func (p *PathParserImpl) GetValue(data any, path string) (any, error) {
	if data == nil {
		return nil, ErrPathNotFound
	}

	if path == "" {
		return data, nil
	}

	// 解析路径
	segments, err := p.parseAndCachePath(path)
	if err != nil {
		return nil, err
	}

	return p.navigateWithSegments(data, segments)
}

// navigateWithSegments 根据解析后的路径段在数据中导航
func (p *PathParserImpl) navigateWithSegments(data any, segments []PathSegment) (any, error) {
	current := data

	for i, segment := range segments {
		if current == nil {
			return nil, ErrPathNotFound
		}

		var err error

		// 处理数组访问
		if segment.IsArrayAccess {
			current, err = p.handleArrayAccess(current, segment.Name, segment.Index, segments[i+1:])
			if err != nil {
				return nil, fmt.Errorf("处理数组访问失败 %s: %w", segment.Original, err)
			}
			// 如果是所有数组元素访问([*])，结果已经处理完，直接返回
			if segment.Index == -2 {
				return current, nil
			}
		} else {
			// 处理对象属性访问
			current, err = p.getProperty(current, segment.Name)
			if err != nil {
				return nil, fmt.Errorf("获取属性 %s 失败: %w", segment.Name, err)
			}
		}
	}

	return current, nil
}

// handleArrayAccess 处理数组访问逻辑
func (p *PathParserImpl) handleArrayAccess(data any, arrayName string, index int, remainingSegments []PathSegment) (any, error) {
	// 先获取数组对象
	arrayObj, err := p.getProperty(data, arrayName)
	if err != nil {
		return nil, err
	}

	// 处理特殊情况，如果确实是数组，但不是slice或array类型（可能是string或map等）
	val := reflect.ValueOf(arrayObj)

	switch {
	case !val.IsValid():
		return nil, ErrPathNotFound
	case val.Kind() == reflect.Slice || val.Kind() == reflect.Array:
		// 正常数组/切片处理
		if index == -2 { // [*] 访问所有元素
			return p.processAllArrayElements(val, remainingSegments)
		}

		// 单个元素访问
		if index >= val.Len() || index < 0 {
			return nil, ErrIndexOutOfRange
		}

		return val.Index(index).Interface(), nil
	default:
		return nil, ErrNotIndexable
	}
}

// processAllArrayElements 处理[*]操作，访问数组中的所有元素
func (p *PathParserImpl) processAllArrayElements(arrayVal reflect.Value, remainingSegments []PathSegment) (any, error) {
	if len(remainingSegments) == 0 {
		// 如果没有更多的路径段，直接返回数组所有元素
		result := make([]any, arrayVal.Len())
		for i := 0; i < arrayVal.Len(); i++ {
			result[i] = arrayVal.Index(i).Interface()
		}
		return result, nil
	}

	// 如果还有更多路径段，则对每个元素应用剩余路径
	result := make([]any, 0, arrayVal.Len())

	for i := 0; i < arrayVal.Len(); i++ {
		element := arrayVal.Index(i).Interface()
		value, err := p.navigateWithSegments(element, remainingSegments)
		if err != nil {
			// 忽略单个元素的错误，继续处理其他元素
			continue
		}

		// 处理返回多个值的情况（嵌套的[*]）
		if nestedArray, ok := value.([]any); ok {
			result = append(result, nestedArray...)
		} else {
			result = append(result, value)
		}
	}

	return result, nil
}

// getProperty 从对象中获取属性
func (p *PathParserImpl) getProperty(data any, propName string) (any, error) {
	if data == nil {
		return nil, ErrPathNotFound
	}

	val := reflect.ValueOf(data)

	// 处理指针
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return nil, ErrPathNotFound
		}
		val = val.Elem()
	}

	switch val.Kind() {
	case reflect.Map:
		// 处理map类型
		keyValue := reflect.ValueOf(propName)

		// 尝试字符串键
		if val.Type().Key().Kind() == reflect.String {
			mapValue := val.MapIndex(keyValue)
			if !mapValue.IsValid() {
				return nil, ErrPathNotFound
			}
			return mapValue.Interface(), nil
		}

		// 可能是其他类型的键
		for _, key := range val.MapKeys() {
			if fmt.Sprint(key.Interface()) == propName {
				return val.MapIndex(key).Interface(), nil
			}
		}

		return nil, ErrPathNotFound

	case reflect.Struct:
		// 处理结构体类型
		field := val.FieldByName(propName)
		if !field.IsValid() {
			// 尝试查找首字母小写转换为大写的字段
			field = val.FieldByName(strings.Title(propName))
			if !field.IsValid() {
				return nil, ErrPathNotFound
			}
		}

		// 检查字段是否可导出
		if !field.CanInterface() {
			return nil, fmt.Errorf("字段 %s 不可访问", propName)
		}

		return field.Interface(), nil

	case reflect.Slice, reflect.Array:
		// 增加对数组/切片类型的支持
		// 尝试将属性名解析为索引
		index, err := strconv.Atoi(propName)
		if err == nil && index >= 0 && index < val.Len() {
			// 有效的数字索引
			return val.Index(index).Interface(), nil
		}

		// 如果是通用的数组/切片，但请求的是一个属性而非索引
		// 尝试处理每个元素都有相同属性的情况，返回一个新的包含该属性值的数组
		if val.Len() > 0 {
			result := make([]any, 0, val.Len())
			for i := 0; i < val.Len(); i++ {
				elem := val.Index(i).Interface()
				if elemVal, err := p.getProperty(elem, propName); err == nil {
					result = append(result, elemVal)
				}
			}
			if len(result) > 0 {
				return result, nil
			}
		}

		return nil, fmt.Errorf("无法从数组/切片中获取属性 %s", propName)

	default:
		return nil, fmt.Errorf("不支持从 %v 类型获取属性", val.Kind())
	}
}

// parseAndCachePath 解析并缓存路径
func (p *PathParserImpl) parseAndCachePath(path string) ([]PathSegment, error) {
	// 先尝试从LRU缓存获取，这是最快的
	if cached, ok := p.lruCache.Get(path); ok {
		return cached.([]PathSegment), nil
	}

	// 再尝试从全局缓存获取
	if cached, ok := p.globalCache.Load(path); ok {
		segments := cached.([]PathSegment)
		// 同时更新到LRU缓存
		p.lruCache.Put(path, segments)
		return segments, nil
	}

	// 解析路径
	segments, err := p.ParsePath(path)
	if err != nil {
		return nil, err
	}

	// 缓存解析结果
	p.globalCache.Store(path, segments)
	p.lruCache.Put(path, segments)

	return segments, nil
}

// ParsePath 解析路径字符串为路径段数组
func (p *PathParserImpl) ParsePath(path string) ([]PathSegment, error) {
	if path == "" {
		return nil, ErrInvalidPath
	}

	// 分割路径，处理点号分隔的部分
	parts := strings.Split(path, ".")
	segments := make([]PathSegment, 0, len(parts))

	for _, part := range parts {
		if part == "" {
			continue
		}

		// 检查是否包含数组访问
		arrayMatch := arrayPattern.FindStringSubmatch(part)
		if len(arrayMatch) > 0 {
			// 提取数组名和索引
			bracketPos := strings.Index(part, "[")
			arrayName := part[:bracketPos]

			indexStr := arrayMatch[1]
			var index int

			if indexStr == "*" {
				// [*] 表示访问所有元素
				index = -2
			} else {
				// 解析数字索引
				var err error
				index, err = strconv.Atoi(indexStr)
				if err != nil {
					return nil, ErrInvalidArrayIndex
				}
			}

			segments = append(segments, PathSegment{
				Name:          arrayName,
				Index:         index,
				IsArrayAccess: true,
				Original:      part,
			})
		} else {
			// 普通属性访问
			segments = append(segments, PathSegment{
				Name:          part,
				Index:         -1,
				IsArrayAccess: false,
				Original:      part,
			})
		}
	}

	return segments, nil
}

// Resolve 是 GetValue 的别名，提供更加语义化的函数名
func (p *PathParserImpl) Resolve(data any, path string) (any, error) {
	return p.GetValue(data, path)
}

// MustGetValue 尝试获取值，如果失败则返回默认值
func (p *PathParserImpl) MustGetValue(data any, path string, defaultValue any) any {
	value, err := p.GetValue(data, path)
	if err != nil {
		return defaultValue
	}
	return value
}

// SetValue 根据路径设置值
func (p *PathParserImpl) SetValue(data any, path string, value any) error {
	if data == nil {
		return ErrInvalidTargetType
	}

	// 解析路径
	segments, err := p.ParsePath(path)
	if err != nil {
		return err
	}

	// 执行设置
	return p.setValueWithSegments(data, segments, value)
}

// setValueWithSegments 根据路径段设置值
func (p *PathParserImpl) setValueWithSegments(data any, segments []PathSegment, value any) error {
	if len(segments) == 0 {
		return errors.New("无效的路径段")
	}

	// 获取反射值
	dataValue := reflect.ValueOf(data)

	// 处理不同类型
	switch dataValue.Kind() {
	case reflect.Map:
		// 直接处理 map 类型
		return p.setMapValue(dataValue, segments, 0, value)
	case reflect.Ptr:
		// 处理指针类型
		if dataValue.IsNil() {
			return ErrNotModifiable
		}
		elemValue := dataValue.Elem()
		return p.setValueForElement(elemValue, segments, 0, value)
	case reflect.Interface:
		// 处理接口类型
		if dataValue.IsNil() {
			return ErrNotModifiable
		}
		elemValue := dataValue.Elem()
		return p.setValueForElement(elemValue, segments, 0, value)
	default:
		return fmt.Errorf("不支持的类型: %v, 需要map或指针类型", dataValue.Kind())
	}
}

// setValueForElement 为元素设置值
func (p *PathParserImpl) setValueForElement(elemValue reflect.Value, segments []PathSegment, index int, value any) error {
	switch elemValue.Kind() {
	case reflect.Map:
		return p.setMapValue(elemValue, segments, index, value)
	case reflect.Struct:
		return p.setStructValue(elemValue, segments, index, value)
	case reflect.Interface:
		// 递归处理接口
		if elemValue.IsNil() {
			return ErrNotModifiable
		}
		return p.setValueForElement(elemValue.Elem(), segments, index, value)
	default:
		return fmt.Errorf("不支持的类型: %v", elemValue.Kind())
	}
}

// setMapValue 设置Map的值
func (p *PathParserImpl) setMapValue(mapValue reflect.Value, segments []PathSegment, index int, value any) error {
	if index >= len(segments) {
		return errors.New("路径段索引超出范围")
	}

	segment := segments[index]

	// 确保map不是nil
	if mapValue.IsNil() {
		return errors.New("map是nil")
	}

	// 获取map的key类型
	keyType := mapValue.Type().Key()

	// 创建key
	var keyValue reflect.Value
	if keyType.Kind() == reflect.String {
		keyValue = reflect.ValueOf(segment.Name)
	} else {
		// 尝试将字符串转换为其他类型的key
		return fmt.Errorf("不支持的map key类型: %v", keyType.Kind())
	}

	// 最后一个路径段，直接设置值
	if index == len(segments)-1 && !segment.IsArrayAccess {
		valueToSet := reflect.ValueOf(value)
		mapValue.SetMapIndex(keyValue, valueToSet)
		return nil
	}

	// 处理数组访问或进一步的路径
	var nextValue reflect.Value
	existingValue := mapValue.MapIndex(keyValue)

	if existingValue.IsValid() {
		// 使用现有值
		nextValue = existingValue
	} else {
		// 需要创建新值
		if segment.IsArrayAccess || (index < len(segments)-1 && segments[index+1].IsArrayAccess) {
			// 创建slice
			nextValue = reflect.ValueOf(make([]interface{}, 0))
		} else {
			// 创建map
			nextValue = reflect.ValueOf(make(map[string]interface{}))
		}
		// 将新创建的值设置到map
		mapValue.SetMapIndex(keyValue, nextValue)
	}

	// 处理数组访问
	if segment.IsArrayAccess {
		return p.setSliceValue(nextValue, segment.Index, segments, index+1, value)
	}

	// 递归处理下一个路径段
	if index < len(segments)-1 {
		return p.setValueForElement(nextValue, segments, index+1, value)
	}

	return nil
}

// setSliceValue 设置数组/切片的值
func (p *PathParserImpl) setSliceValue(sliceValue reflect.Value, index int, segments []PathSegment, nextIndex int, value any) error {
	// 处理指针类型（指向切片的指针）
	if sliceValue.Kind() == reflect.Ptr {
		if sliceValue.IsNil() {
			return ErrNotModifiable
		}
		return p.setSliceValue(sliceValue.Elem(), index, segments, nextIndex, value)
	}

	// 处理interface类型（可能包含slice）
	if sliceValue.Kind() == reflect.Interface {
		if sliceValue.IsNil() {
			return ErrNotModifiable
		}
		return p.setSliceValue(sliceValue.Elem(), index, segments, nextIndex, value)
	}

	// 处理slice类型
	if sliceValue.Kind() != reflect.Slice && sliceValue.Kind() != reflect.Array {
		return fmt.Errorf("不是slice或array: %v", sliceValue.Kind())
	}

	// 处理通配符 [*]
	if index == -2 {
		return errors.New("不能对[*]设置值")
	}

	// 确保slice有足够长度
	if index >= sliceValue.Len() {
		// 需要扩展slice
		newSlice := reflect.MakeSlice(sliceValue.Type(), index+1, index+1)
		if sliceValue.Len() > 0 {
			reflect.Copy(newSlice, sliceValue)
		}

		// 确保原始切片可寻址
		if !sliceValue.CanSet() {
			return fmt.Errorf("切片不可设置，索引 %d 超出范围且无法扩展", index)
		}

		sliceValue.Set(newSlice)
	}

	// 获取元素
	elemValue := sliceValue.Index(index)

	// 最后一个路径段，直接设置值
	if nextIndex >= len(segments) {
		valueToSet := reflect.ValueOf(value)

		// 尝试类型转换
		if !elemValue.CanSet() {
			return fmt.Errorf("切片元素不可设置，可能是不可寻址的切片")
		}

		if valueToSet.Type().AssignableTo(elemValue.Type()) {
			elemValue.Set(valueToSet)
		} else if valueToSet.Type().ConvertibleTo(elemValue.Type()) {
			elemValue.Set(valueToSet.Convert(elemValue.Type()))
		} else {
			// 如果类型不匹配但目标是接口类型，可以尝试直接设置
			if elemValue.Kind() == reflect.Interface {
				elemValue.Set(valueToSet)
			} else {
				return fmt.Errorf("类型不匹配: 无法将 %v 类型赋值给 %v 类型", valueToSet.Type(), elemValue.Type())
			}
		}

		return nil
	}

	// 递归处理下一个路径段
	return p.setValueForElement(elemValue, segments, nextIndex, value)
}

// setStructValue 设置结构体的值
func (p *PathParserImpl) setStructValue(structValue reflect.Value, segments []PathSegment, index int, value any) error {
	if index >= len(segments) {
		return errors.New("路径段索引超出范围")
	}

	segment := segments[index]

	// 查找字段
	var field reflect.Value

	// 先尝试直接字段名
	field = structValue.FieldByName(segment.Name)

	// 如果找不到，尝试首字母大写
	if !field.IsValid() {
		field = structValue.FieldByName(strings.Title(segment.Name))
	}

	// 如果仍找不到，返回错误
	if !field.IsValid() {
		return fmt.Errorf("结构体中找不到字段: %s", segment.Name)
	}

	// 检查字段是否可设置
	if !field.CanSet() {
		return fmt.Errorf("字段不可设置: %s", segment.Name)
	}

	// 最后一个路径段，直接设置值
	if index == len(segments)-1 && !segment.IsArrayAccess {
		valueToSet := reflect.ValueOf(value)

		// 尝试类型转换
		if valueToSet.Type().AssignableTo(field.Type()) {
			field.Set(valueToSet)
		} else if valueToSet.Type().ConvertibleTo(field.Type()) {
			field.Set(valueToSet.Convert(field.Type()))
		} else {
			return fmt.Errorf("类型不匹配: 无法将 %v 类型赋值给 %v 类型", valueToSet.Type(), field.Type())
		}

		return nil
	}

	// 处理数组访问
	if segment.IsArrayAccess {
		return p.setSliceValue(field, segment.Index, segments, index+1, value)
	}

	// 递归处理下一个路径段
	if index < len(segments)-1 {
		return p.setValueForElement(field, segments, index+1, value)
	}

	return nil
}

// ClearCache 清除所有缓存
func (p *PathParserImpl) ClearCache() {
	p.globalCache = sync.Map{}
	p.lruCache.Clear()
}
