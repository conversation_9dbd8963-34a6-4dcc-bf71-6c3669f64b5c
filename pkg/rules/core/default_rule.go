// Package core 提供规则引擎的核心实现
package core

import "admin/internal/utils"

// DefaultRule 规则的默认实现
type DefaultRule struct {
	// 规则ID
	ID string `json:"id"`

	// 规则名称
	Name string `json:"name"`

	// 规则描述
	Description string `json:"description"`

	// 规则优先级，数字越小优先级越高
	Priority int `json:"priority"`

	// 规则条件
	Condition Condition `json:"condition"`

	// 规则动作
	Actions []Action `json:"actions"`

	// 规则是否启用
	Enabled bool `json:"enabled"`
}

// NewRule 创建一个新的规则
func NewRule(name, description string, priority int, condition Condition, actions []Action, enabled bool) *DefaultRule {
	return &DefaultRule{
		ID:          utils.GetUUID(),
		Name:        name,
		Description: description,
		Priority:    priority,
		Condition:   condition,
		Actions:     actions,
		Enabled:     enabled,
	}
}

// GetID 获取规则ID
func (r *DefaultRule) GetID() string {
	return r.ID
}

// GetName 获取规则名称
func (r *DefaultRule) GetName() string {
	return r.Name
}

// GetDescription 获取规则描述
func (r *DefaultRule) GetDescription() string {
	return r.Description
}

// GetPriority 获取规则优先级
func (r *DefaultRule) GetPriority() int {
	return r.Priority
}

// GetCondition 获取规则条件
func (r *DefaultRule) GetCondition() Condition {
	return r.Condition
}

// GetActions 获取规则动作
func (r *DefaultRule) GetActions() []Action {
	return r.Actions
}

// IsEnabled 规则是否启用
func (r *DefaultRule) IsEnabled() bool {
	return r.Enabled
}

// Enable 启用规则
func (r *DefaultRule) Enable() {
	r.Enabled = true
}

// Disable 禁用规则
func (r *DefaultRule) Disable() {
	r.Enabled = false
}

// SetPriority 设置优先级
func (r *DefaultRule) SetPriority(priority int) {
	r.Priority = priority
}

// SetCondition 设置条件
func (r *DefaultRule) SetCondition(condition Condition) {
	r.Condition = condition
}

// SetActions 设置动作
func (r *DefaultRule) SetActions(actions []Action) {
	r.Actions = actions
}
