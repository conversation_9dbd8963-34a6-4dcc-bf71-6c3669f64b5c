// Package core 定义规则引擎的核心接口和基础结构
package core

import (
	"hash/fnv"
	"strings"
	"sync"
)

// PathNode 定义前缀树节点
// 用于优化路径匹配
type PathNode struct {
	Children map[string]*PathNode
	Rules    map[string]Rule
}

// NewPathNode 创建一个新的前缀树节点
func NewPathNode() *PathNode {
	return &PathNode{
		Children: make(map[string]*PathNode),
		Rules:    make(map[string]Rule),
	}
}

// AddPath 添加路径到前缀树
func (node *PathNode) AddPath(pathParts []string, rule Rule) {
	if len(pathParts) == 0 {
		node.Rules[rule.GetID()] = rule
		return
	}

	nextPart := pathParts[0]
	if _, exists := node.Children[nextPart]; !exists {
		node.Children[nextPart] = NewPathNode()
	}
	node.Children[nextPart].AddPath(pathParts[1:], rule)
}

// FindRules 查找匹配路径的规则
func (node *PathNode) FindRules(pathParts []string) map[string]Rule {
	if len(pathParts) == 0 {
		return node.Rules
	}

	nextPart := pathParts[0]
	if child, exists := node.Children[nextPart]; exists {
		return child.FindRules(pathParts[1:])
	}

	return nil
}

// RuleIndex 规则索引，用于高效查找匹配规则
type RuleIndex struct {
	pathTrie *PathNode

	// 按类型索引规则
	typeIndex map[string]map[string]Rule

	// 按ID索引规则
	idIndex map[string]Rule

	// 启用的规则
	enabledRules map[string]Rule

	// 互斥锁，保证并发安全
	mu sync.RWMutex
}

// NewRuleIndex 创建一个新的规则索引
func NewRuleIndex() *RuleIndex {
	return &RuleIndex{
		pathTrie:     NewPathNode(),
		typeIndex:    make(map[string]map[string]Rule),
		idIndex:      make(map[string]Rule),
		enabledRules: make(map[string]Rule),
	}
}

// AddRule 添加规则到索引
func (ri *RuleIndex) AddRule(rule Rule) {
	if rule == nil {
		return
	}

	id := rule.GetID()
	if id == "" {
		return
	}

	ri.mu.Lock()
	defer ri.mu.Unlock()

	// 索引规则ID
	ri.idIndex[id] = rule

	// 如果规则启用，添加到启用规则索引
	if rule.IsEnabled() {
		ri.enabledRules[id] = rule
	}

	// 添加到前缀树
	paths := ExtractConditionPaths(rule.GetCondition())
	for _, path := range paths {
		pathParts := strings.Split(path, ".")
		ri.pathTrie.AddPath(pathParts, rule)
	}

	// 索引规则的类型（可以基于规则名称、描述等进行分类）
	ruleTypes := ExtractRuleTypes(rule)
	for _, ruleType := range ruleTypes {
		if ruleType == "" {
			continue // 跳过空类型
		}

		if _, exists := ri.typeIndex[ruleType]; !exists {
			ri.typeIndex[ruleType] = make(map[string]Rule)
		}
		ri.typeIndex[ruleType][id] = rule
	}
}

// RemoveRule 从索引中移除规则
func (ri *RuleIndex) RemoveRule(ruleID string) {
	if ruleID == "" {
		return
	}

	ri.mu.Lock()
	defer ri.mu.Unlock()

	rule, exists := ri.idIndex[ruleID]
	if !exists {
		return
	}

	// 从ID索引中移除
	delete(ri.idIndex, ruleID)

	// 从启用规则索引中移除
	delete(ri.enabledRules, ruleID)

	// 从路径索引中移除
	paths := ExtractConditionPaths(rule.GetCondition())
	for _, path := range paths {
		pathParts := strings.Split(path, ".")
		node := ri.pathTrie
		for _, part := range pathParts {
			if child, exists := node.Children[part]; exists {
				node = child
			} else {
				node = nil
				break
			}
		}
		if node != nil {
			delete(node.Rules, ruleID)
		}
	}

	// 从类型索引中移除
	ruleTypes := ExtractRuleTypes(rule)
	for _, ruleType := range ruleTypes {
		if ruleType == "" {
			continue // 跳过空类型
		}

		if ruleMap, exists := ri.typeIndex[ruleType]; exists {
			delete(ruleMap, ruleID)
			// 如果这个类型下没有规则了，删除这个类型索引
			if len(ruleMap) == 0 {
				delete(ri.typeIndex, ruleType)
			}
		}
	}
}

// UpdateRule 更新规则索引
func (ri *RuleIndex) UpdateRule(rule Rule) {
	if rule == nil {
		return
	}

	id := rule.GetID()
	if id == "" {
		return
	}

	// 加写锁保证原子操作
	ri.mu.Lock()
	defer ri.mu.Unlock()

	// 先从ID索引中获取旧规则
	oldRule, exists := ri.idIndex[id]
	if exists {
		// 从旧索引中移除

		// 从启用规则索引中移除
		delete(ri.enabledRules, id)

		// 从路径索引中移除
		paths := ExtractConditionPaths(oldRule.GetCondition())
		for _, path := range paths {
			pathParts := strings.Split(path, ".")
			node := ri.pathTrie
			for _, part := range pathParts {
				if child, exists := node.Children[part]; exists {
					node = child
				} else {
					node = nil
					break
				}
			}
			if node != nil {
				delete(node.Rules, id)
			}
		}

		// 从类型索引中移除
		ruleTypes := ExtractRuleTypes(oldRule)
		for _, ruleType := range ruleTypes {
			if ruleType == "" {
				continue
			}
			if ruleMap, exists := ri.typeIndex[ruleType]; exists {
				delete(ruleMap, id)
				if len(ruleMap) == 0 {
					delete(ri.typeIndex, ruleType)
				}
			}
		}
	}

	// 添加新规则到索引

	// 索引规则ID
	ri.idIndex[id] = rule

	// 如果规则启用，添加到启用规则索引
	if rule.IsEnabled() {
		ri.enabledRules[id] = rule
	}

	// 添加到前缀树
	paths := ExtractConditionPaths(rule.GetCondition())
	for _, path := range paths {
		pathParts := strings.Split(path, ".")
		ri.pathTrie.AddPath(pathParts, rule)
	}

	// 索引规则的类型
	ruleTypes := ExtractRuleTypes(rule)
	for _, ruleType := range ruleTypes {
		if ruleType == "" {
			continue
		}
		if _, exists := ri.typeIndex[ruleType]; !exists {
			ri.typeIndex[ruleType] = make(map[string]Rule)
		}
		ri.typeIndex[ruleType][id] = rule
	}
}

// GetRulesByPath 根据路径获取规则
func (ri *RuleIndex) GetRulesByPath(path string) []Rule {
	if path == "" {
		return nil
	}

	ri.mu.RLock()
	defer ri.mu.RUnlock()

	pathParts := strings.Split(path, ".")
	ruleMap := ri.pathTrie.FindRules(pathParts)

	rules := make([]Rule, 0, len(ruleMap))
	for _, rule := range ruleMap {
		rules = append(rules, rule)
	}

	return rules
}

// GetRulesByType 根据类型获取规则
func (ri *RuleIndex) GetRulesByType(ruleType string) []Rule {
	if ruleType == "" {
		return nil
	}

	ri.mu.RLock()
	defer ri.mu.RUnlock()

	rules := make([]Rule, 0)

	if ruleMap, exists := ri.typeIndex[ruleType]; exists {
		for _, rule := range ruleMap {
			rules = append(rules, rule)
		}
	}

	return rules
}

// GetRuleByID 根据ID获取规则
func (ri *RuleIndex) GetRuleByID(ruleID string) (Rule, bool) {
	if ruleID == "" {
		return nil, false
	}

	ri.mu.RLock()
	defer ri.mu.RUnlock()

	rule, exists := ri.idIndex[ruleID]
	return rule, exists
}

// GetAllRules 获取所有规则
func (ri *RuleIndex) GetAllRules() []Rule {
	ri.mu.RLock()
	defer ri.mu.RUnlock()

	rules := make([]Rule, 0, len(ri.idIndex))

	for _, rule := range ri.idIndex {
		rules = append(rules, rule)
	}

	return rules
}

// GetEnabledRules 获取所有启用的规则
func (ri *RuleIndex) GetEnabledRules() []Rule {
	ri.mu.RLock()
	defer ri.mu.RUnlock()

	rules := make([]Rule, 0, len(ri.enabledRules))

	for _, rule := range ri.enabledRules {
		rules = append(rules, rule)
	}

	return rules
}

// ExtractConditionPaths 从条件中提取路径
func ExtractConditionPaths(cond Condition) []string {
	if cond == nil {
		return nil
	}

	// 使用map去重
	pathMap := make(map[string]struct{})
	extractPaths(cond, pathMap)

	// 转换map为切片
	paths := make([]string, 0, len(pathMap))
	for path := range pathMap {
		if path != "" {
			paths = append(paths, path)
		}
	}

	return paths
}

// extractPaths 递归提取路径到map中
func extractPaths(cond Condition, pathMap map[string]struct{}) {
	if cond == nil {
		return
	}

	// 处理SimpleCondition
	if simpleCondition, ok := cond.(interface {
		GetPath() string
	}); ok {
		path := simpleCondition.GetPath()
		if path != "" {
			pathMap[path] = struct{}{}
		}
		return
	}

	// 处理CompositeCondition
	if compositeCondition, ok := cond.(interface {
		GetConditions() []Condition
	}); ok {
		for _, subCond := range compositeCondition.GetConditions() {
			extractPaths(subCond, pathMap)
		}
		return
	}

	// 处理函数条件、时间条件等其他类型可能包含的路径
	if pathProvider, ok := cond.(interface {
		GetPaths() []string
	}); ok {
		for _, path := range pathProvider.GetPaths() {
			if path != "" {
				pathMap[path] = struct{}{}
			}
		}
	}
}

// ExtractRuleTypes 从规则中提取类型标识
func ExtractRuleTypes(rule Rule) []string {
	if rule == nil {
		return nil
	}

	// 使用map去重
	typeMap := make(map[string]struct{})

	// 1. 如果规则实现了GetType接口，优先使用
	if typer, ok := rule.(interface{ GetType() string }); ok {
		ruleType := typer.GetType()
		if ruleType != "" {
			typeMap[strings.ToLower(ruleType)] = struct{}{}
			return mapToStringSlice(typeMap)
		}
	}

	name := rule.GetName()
	if name == "" {
		// 尝试使用ID作为备选
		id := rule.GetID()
		if id == "" {
			return nil
		}
		name = id
	}

	// 2. 提取首个单词作为类型
	firstSpace := strings.IndexAny(name, " ._-")
	if firstSpace > 0 {
		firstWord := strings.ToLower(name[:firstSpace])
		typeMap[firstWord] = struct{}{}
	} else {
		// 如果没有分隔符，使用整个名称作为类型
		typeMap[strings.ToLower(name)] = struct{}{}
	}

	// 3. 如果规则有描述，也可以从描述中提取类型关键词
	desc := rule.GetDescription()
	if desc != "" {
		// 简单处理：把描述按空格分割，提取首个单词
		parts := strings.Fields(desc)
		if len(parts) > 0 {
			firstWord := strings.ToLower(parts[0])
			// 过滤掉常见的无意义词
			if len(firstWord) > 3 && !isCommonWord(firstWord) {
				typeMap[firstWord] = struct{}{}
			}
		}
	}

	return mapToStringSlice(typeMap)
}

// isCommonWord 检查是否是常见无意义词
func isCommonWord(word string) bool {
	commonWords := map[string]bool{
		"this": true, "rule": true, "for": true, "and": true,
		"the": true, "that": true, "with": true, "when": true,
	}
	return commonWords[word]
}

// mapToStringSlice 将map转换为字符串切片
func mapToStringSlice(m map[string]struct{}) []string {
	slice := make([]string, 0, len(m))
	for key := range m {
		slice = append(slice, key)
	}
	return slice
}

// ShardedRuleIndex 分段锁实现
// 用于替代全局锁，提升并发性能
type ShardedRuleIndex struct {
	shards    []*RuleIndexShard
	shardMask uint32
}

type RuleIndexShard struct {
	mu        sync.RWMutex
	pathTrie  *PathNode
	typeIndex map[string]map[string]Rule
	idIndex   map[string]Rule
}

// NewShardedRuleIndex 创建分段锁索引
func NewShardedRuleIndex(shardCount int) *ShardedRuleIndex {
	shards := make([]*RuleIndexShard, shardCount)
	for i := 0; i < shardCount; i++ {
		shards[i] = &RuleIndexShard{
			pathTrie:  NewPathNode(),
			typeIndex: make(map[string]map[string]Rule),
			idIndex:   make(map[string]Rule),
		}
	}
	return &ShardedRuleIndex{
		shards:    shards,
		shardMask: uint32(shardCount - 1),
	}
}

// getShard 根据规则ID获取分片
func (sri *ShardedRuleIndex) getShard(key string) *RuleIndexShard {
	hash := fnv.New32()
	hash.Write([]byte(key))
	shardIndex := hash.Sum32() & sri.shardMask
	return sri.shards[shardIndex]
}

// AddRule 添加规则到分片
func (sri *ShardedRuleIndex) AddRule(rule Rule) {
	if rule == nil {
		return
	}

	id := rule.GetID()
	if id == "" {
		return
	}

	shard := sri.getShard(id)
	shard.mu.Lock()
	defer shard.mu.Unlock()

	// 添加到分片的前缀树
	paths := ExtractConditionPaths(rule.GetCondition())
	for _, path := range paths {
		pathParts := strings.Split(path, ".")
		shard.pathTrie.AddPath(pathParts, rule)
	}

	// 添加到分片的ID索引
	shard.idIndex[id] = rule
}

// GetRulesByPath 根据路径获取规则
func (sri *ShardedRuleIndex) GetRulesByPath(path string) []Rule {
	if path == "" {
		return nil
	}

	pathParts := strings.Split(path, ".")
	ruleMap := make(map[string]Rule)

	for _, shard := range sri.shards {
		shard.mu.RLock()
		shardRules := shard.pathTrie.FindRules(pathParts)
		for id, rule := range shardRules {
			ruleMap[id] = rule
		}
		shard.mu.RUnlock()
	}

	rules := make([]Rule, 0, len(ruleMap))
	for _, rule := range ruleMap {
		rules = append(rules, rule)
	}

	return rules
}
