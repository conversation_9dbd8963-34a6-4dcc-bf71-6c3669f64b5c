// Package core 规则引擎的核心实现
package core

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"sync"
)

var (
	// 错误定义
	ErrRuleAlreadyExists = errors.New("规则已存在")
	ErrRuleNotFound      = errors.New("规则不存在")
	ErrInvalidRuleID     = errors.New("无效的规则ID")
)

// DefaultRuleEngine 规则引擎的默认实现
type DefaultRuleEngine struct {
	// 规则存储，使用map实现快速查找
	rules map[string]Rule

	// 规则索引系统
	ruleIndex *RuleIndex

	// 互斥锁，保证并发安全
	mu sync.RWMutex

	// 是否使用并发执行规则
	concurrent bool

	// 最大并发数，如果concurrent为true时生效
	maxWorkers int

	// 是否启用条件优化
	enableOptimization bool

	// 条件优化器 (使用interface避免循环导入)
	optimizer RuleOptimizer
}

// RuleOptimizer 规则优化器接口
// 将此接口放在core包中，避免循环依赖
type RuleOptimizer interface {
	// OptimizeRule 优化单个规则
	OptimizeRule(rule Rule) Rule

	// OptimizeRules 批量优化规则
	OptimizeRules(rules []Rule) []Rule
}

// NewDefaultRuleEngine 创建默认规则引擎
func NewDefaultRuleEngine(concurrent bool, maxWorkers int) *DefaultRuleEngine {
	if maxWorkers <= 0 {
		maxWorkers = 10 // 默认10个工作协程
	}

	engine := &DefaultRuleEngine{
		rules:              make(map[string]Rule),
		ruleIndex:          NewRuleIndex(),
		concurrent:         concurrent,
		maxWorkers:         maxWorkers,
		enableOptimization: true, // 默认启用优化
	}

	// 注意: optimizer将在外部通过SetOptimizer方法设置
	return engine
}

// SetOptimizer 设置规则优化器
func (e *DefaultRuleEngine) SetOptimizer(optimizer RuleOptimizer) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.optimizer = optimizer
}

// AddRule 添加规则到引擎
func (e *DefaultRuleEngine) AddRule(rule Rule) error {
	if rule == nil {
		return errors.New("规则不能为nil")
	}

	id := rule.GetID()
	if id == "" {
		return ErrInvalidRuleID
	}

	// 如果启用了优化，且优化器存在，优化规则条件
	if e.enableOptimization && e.optimizer != nil {
		rule = e.optimizer.OptimizeRule(rule)
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	if _, exists := e.rules[id]; exists {
		return fmt.Errorf("%w: %s", ErrRuleAlreadyExists, id)
	}

	e.rules[id] = rule
	// 更新规则索引
	e.ruleIndex.AddRule(rule)
	return nil
}

// AddRules 批量添加规则
func (e *DefaultRuleEngine) AddRules(rules []Rule) error {
	if len(rules) == 0 {
		return nil
	}

	// 如果启用了优化，且优化器存在，批量优化规则
	if e.enableOptimization && e.optimizer != nil {
		rules = e.optimizer.OptimizeRules(rules)
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	for _, rule := range rules {
		if rule == nil {
			continue
		}

		id := rule.GetID()
		if id == "" {
			continue // 跳过无效ID的规则
		}

		if _, exists := e.rules[id]; exists {
			continue // 跳过已存在的规则
		}

		e.rules[id] = rule
		// 更新规则索引
		e.ruleIndex.AddRule(rule)
	}

	return nil
}

// RemoveRule 从引擎移除规则
func (e *DefaultRuleEngine) RemoveRule(ruleID string) error {
	if ruleID == "" {
		return ErrInvalidRuleID
	}

	e.mu.Lock()
	defer e.mu.Unlock()

	if _, exists := e.rules[ruleID]; !exists {
		return fmt.Errorf("%w: %s", ErrRuleNotFound, ruleID)
	}

	// 从规则索引中移除
	e.ruleIndex.RemoveRule(ruleID)
	delete(e.rules, ruleID)
	return nil
}

// GetRule 获取规则
func (e *DefaultRuleEngine) GetRule(ruleID string) (Rule, bool) {
	if ruleID == "" {
		return nil, false
	}

	e.mu.RLock()
	defer e.mu.RUnlock()

	// 使用规则索引获取规则
	return e.ruleIndex.GetRuleByID(ruleID)
}

// Execute 执行所有规则
func (e *DefaultRuleEngine) Execute(ctx context.Context, data any) (*ExecutionResult, error) {
	return e.ExecuteWithFilter(ctx, data, nil)
}

// ExecuteWithFilter 根据过滤条件执行规则
func (e *DefaultRuleEngine) ExecuteWithFilter(ctx context.Context, data any, filter RuleFilter) (*ExecutionResult, error) {
	// 获取规则列表
	e.mu.RLock()
	// 预分配规则切片，避免动态扩容
	rules := make([]Rule, 0, len(e.rules))
	for _, rule := range e.rules {
		if filter == nil || filter(rule) {
			if rule.IsEnabled() {
				rules = append(rules, rule)
			}
		}
	}
	e.mu.RUnlock()

	// 按优先级排序
	sort.Slice(rules, func(i, j int) bool {
		return rules[i].GetPriority() < rules[j].GetPriority()
	})

	// 从对象池获取执行结果
	result := GetResultFromPool()

	// 如果没有匹配的规则，直接返回空结果
	if len(rules) == 0 {
		return result, nil
	}

	// 判断是否使用并发执行
	if e.concurrent && len(rules) > 1 {
		// 临时禁用最短路径优化，确保并发模式下评估所有规则
		originalOptimization := e.enableOptimization
		e.enableOptimization = false
		defer func() {
			// 恢复原始优化设置
			e.enableOptimization = originalOptimization
		}()

		return e.executeConcurrently(ctx, data, rules)
	}

	// 顺序执行规则
	return e.executeSequentially(ctx, data, rules)
}

// executeSequentially 顺序执行规则
func (e *DefaultRuleEngine) executeSequentially(ctx context.Context, data any, rules []Rule) (*ExecutionResult, error) {
	// 从对象池获取执行结果
	result := GetResultFromPool()

	for _, rule := range rules {
		// 检查上下文是否取消
		select {
		case <-ctx.Done():
			result.AddError(ctx.Err())
			return result, ctx.Err()
		default:
			// 继续执行
		}

		// 评估规则条件
		condition := rule.GetCondition()
		if condition == nil {
			continue
		}

		match, err := condition.Evaluate(ctx, data)
		if err != nil {
			result.AddError(fmt.Errorf("评估规则 %s 条件失败: %w", rule.GetID(), err))
			continue
		}

		// 如果条件匹配，执行动作
		if match {
			// 从对象池获取匹配规则对象
			matched := GetMatchedRuleFromPool()
			matched.Rule = rule

			for _, action := range rule.GetActions() {
				// 从对象池获取动作结果对象
				actionResult := GetActionResultFromPool()
				actionResult.Action = action

				err := action.Execute(ctx, data)
				if err != nil {
					actionResult.Error = err
					result.AddError(fmt.Errorf("执行规则 %s 动作失败: %w", rule.GetID(), err))
				}

				matched.ActionResults = append(matched.ActionResults, *actionResult)
				// 归还动作结果对象到池
				PutActionResultToPool(actionResult)
			}

			result.Matches = append(result.Matches, *matched)
			// 归还匹配规则对象到池，但不归还指向的Rule
			matched.Rule = nil
			PutMatchedRuleToPool(matched)
		}
	}

	return result, nil
}

// executeConcurrently 并发执行规则
func (e *DefaultRuleEngine) executeConcurrently(ctx context.Context, data any, rules []Rule) (*ExecutionResult, error) {
	// 从对象池获取执行结果
	result := GetResultFromPool()

	// 如果规则数量为0，直接返回
	rulesCount := len(rules)
	if rulesCount == 0 {
		return result, nil
	}

	// 动态调整工作协程数量，避免过多的协程创建
	// 取规则数量和最大工作协程数的较小值
	workerCount := min(rulesCount, e.maxWorkers)

	// 使用WaitGroup来等待所有工作协程完成
	var wg sync.WaitGroup

	// 创建通道用于收集结果和错误
	resultChan := make(chan *MatchedRule, rulesCount)
	errorChan := make(chan error, rulesCount)

	// 创建工作队列
	jobs := make(chan Rule, rulesCount)

	// 启动工作协程池
	wg.Add(workerCount)
	for range workerCount {
		go func() {
			defer wg.Done()
			for rule := range jobs {
				// 检查上下文是否取消
				select {
				case <-ctx.Done():
					errorChan <- ctx.Err()
					continue
				default:
					// 继续执行
				}

				// 评估规则条件
				condition := rule.GetCondition()
				if condition == nil {
					continue
				}

				match, err := condition.Evaluate(ctx, data)
				if err != nil {
					errorChan <- fmt.Errorf("评估规则 %s 条件失败: %w", rule.GetID(), err)
					continue
				}

				// 如果条件匹配，执行动作
				if match {
					// 从对象池获取匹配规则对象
					matched := GetMatchedRuleFromPool()
					matched.Rule = rule

					for _, action := range rule.GetActions() {
						// 从对象池获取动作结果对象
						actionResult := GetActionResultFromPool()
						actionResult.Action = action

						err := action.Execute(ctx, data)
						if err != nil {
							actionResult.Error = err
							errorChan <- fmt.Errorf("执行规则 %s 动作失败: %w", rule.GetID(), err)
						}

						matched.ActionResults = append(matched.ActionResults, *actionResult)
						// 归还动作结果对象到池
						PutActionResultToPool(actionResult)
					}

					// 将匹配结果发送到结果通道
					resultChan <- matched
				}
			}
		}()
	}

	// 发送任务到工作队列
	for _, r := range rules {
		jobs <- r
	}
	close(jobs) // 关闭任务队列，表示没有更多任务

	// 启动一个协程等待所有工作线程完成，然后关闭结果通道
	go func() {
		wg.Wait()
		close(resultChan)
		close(errorChan)
	}()

	// 收集所有匹配的规则
	var matches []*MatchedRule
	for matched := range resultChan {
		if matched != nil {
			matches = append(matches, matched)
		}
	}

	// 收集所有错误
	for err := range errorChan {
		if err != nil {
			result.AddError(err)
		}
	}

	// 确保按优先级排序匹配结果
	if len(matches) > 1 {
		sort.Slice(matches, func(i, j int) bool {
			return matches[i].Rule.GetPriority() < matches[j].Rule.GetPriority()
		})
	}

	// 将排序后的匹配结果添加到结果中
	for _, matched := range matches {
		result.Matches = append(result.Matches, *matched)
		// 归还匹配规则对象到池，但保留Rule引用给结果使用
		matched.Rule = nil
		PutMatchedRuleToPool(matched)
	}

	return result, nil
}

// ruleExecResult 规则执行结果，用于并发执行
type ruleExecResult struct {
	matched *MatchedRule
	err     error
}

// SetConcurrent 设置是否使用并发执行
func (e *DefaultRuleEngine) SetConcurrent(concurrent bool) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.concurrent = concurrent
}

// SetMaxWorkers 设置最大工作协程数
func (e *DefaultRuleEngine) SetMaxWorkers(maxWorkers int) {
	if maxWorkers <= 0 {
		return
	}

	e.mu.Lock()
	defer e.mu.Unlock()
	e.maxWorkers = maxWorkers
}

// GetRuleCount 获取规则数量
func (e *DefaultRuleEngine) GetRuleCount() int {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return len(e.rules)
}

// GetAllRules 获取所有规则
func (e *DefaultRuleEngine) GetAllRules() []Rule {
	e.mu.RLock()
	defer e.mu.RUnlock()

	rules := make([]Rule, 0, len(e.rules))
	for _, rule := range e.rules {
		rules = append(rules, rule)
	}

	return rules
}

// ClearRules 清除所有规则
func (e *DefaultRuleEngine) ClearRules() {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.rules = make(map[string]Rule)
	e.ruleIndex = NewRuleIndex()
}

// SetOptimizationEnabled 设置是否启用条件优化
func (e *DefaultRuleEngine) SetOptimizationEnabled(enabled bool) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.enableOptimization = enabled
}

// IsOptimizationEnabled 查询是否启用了条件优化
func (e *DefaultRuleEngine) IsOptimizationEnabled() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()
	return e.enableOptimization
}

// OptimizeExistingRules 优化已存在的所有规则
// 此方法可在需要时手动调用，对所有已加载的规则进行优化
func (e *DefaultRuleEngine) OptimizeExistingRules() {
	e.mu.Lock()
	defer e.mu.Unlock()

	// 如果没有启用优化或优化器不存在，直接返回
	if !e.enableOptimization || e.optimizer == nil {
		return
	}

	// 遍历所有规则，逐个优化
	for id, rule := range e.rules {
		optimizedRule := e.optimizer.OptimizeRule(rule)
		e.rules[id] = optimizedRule

		// 更新规则索引
		e.ruleIndex.RemoveRule(id)
		e.ruleIndex.AddRule(optimizedRule)
	}
}
