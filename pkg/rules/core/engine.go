// Package core 定义规则引擎的核心接口和基础结构
package core

import (
	"context"
	"fmt"
	"sync"
)

// RuleEngine 规则引擎接口
type RuleEngine interface {
	// AddRule 添加规则到引擎
	AddRule(rule Rule) error

	// AddRules 批量添加规则
	AddRules(rules []Rule) error

	// RemoveRule 从引擎移除规则
	RemoveRule(ruleID string) error

	// GetRule 获取规则
	GetRule(ruleID string) (Rule, bool)

	// Execute 执行所有规则
	Execute(ctx context.Context, data any) (*ExecutionResult, error)

	// ExecuteWithFilter 根据过滤条件执行规则
	ExecuteWithFilter(ctx context.Context, data any, filter RuleFilter) (*ExecutionResult, error)
}

// Rule 规则接口
type Rule interface {
	// GetID 获取规则ID
	GetID() string

	// GetName 获取规则名称
	GetName() string

	// GetDescription 获取规则描述
	GetDescription() string

	// GetPriority 获取规则优先级，数字越小优先级越高
	GetPriority() int

	// GetCondition 获取规则的条件
	GetCondition() Condition

	// GetActions 获取规则匹配后要执行的动作
	GetActions() []Action

	// IsEnabled 规则是否启用
	IsEnabled() bool
}

// Condition 条件接口
type Condition interface {
	// Evaluate 评估条件是否满足
	Evaluate(ctx context.Context, data any) (bool, error)
}

// CachedCondition 条件评估缓存装饰器
// 用于减少重复计算，提高性能
type CachedCondition struct {
	condition Condition
	cache     *sync.Map
}

// NewCachedCondition 创建一个带缓存的条件
func NewCachedCondition(condition Condition) *CachedCondition {
	return &CachedCondition{
		condition: condition,
		cache:     &sync.Map{},
	}
}

// Evaluate 评估条件，使用缓存
func (cc *CachedCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	cacheKey := fmt.Sprintf("%v", data)
	if result, found := cc.cache.Load(cacheKey); found {
		return result.(bool), nil
	}

	result, err := cc.condition.Evaluate(ctx, data)
	if err == nil {
		cc.cache.Store(cacheKey, result)
	}
	return result, err
}

// Action 动作接口
type Action interface {
	// Execute 执行动作
	Execute(ctx context.Context, data any) error
}

// RuleFilter 规则过滤器，用于选择性执行规则
type RuleFilter func(rule Rule) bool

// ErrorCollector 统一的错误收集接口
// 用于在规则引擎执行过程中收集和管理错误
type ErrorCollector interface {
	AddError(err error)
	GetErrors() []error
	HasErrors() bool
	ClearErrors()
}

// ExecutionResult 规则执行结果
type ExecutionResult struct {
	// Matches 匹配的规则列表
	Matches []MatchedRule

	// Errors 执行过程中的错误
	ErrorCollector
}

// MatchedRule 匹配的规则信息
type MatchedRule struct {
	// Rule 匹配的规则
	Rule Rule

	// ActionResults 动作执行结果
	ActionResults []ActionResult
}

// ActionResult 动作执行结果
type ActionResult struct {
	// Action 执行的动作
	Action Action

	// Error 执行错误，如果为nil表示执行成功
	Error error
}

// 对象池扩展
var (
	ConditionPool = sync.Pool{
		New: func() interface{} {
			return &CachedCondition{}
		},
	}

	ActionPool = sync.Pool{
		New: func() interface{} {
			return &ActionResult{}
		},
	}
)

// GetCondition 从对象池获取条件
func GetCondition() *CachedCondition {
	return ConditionPool.Get().(*CachedCondition)
}

// PutCondition 将条件放回对象池
func PutCondition(cond *CachedCondition) {
	cond.cache = &sync.Map{} // 重置缓存
	ConditionPool.Put(cond)
}

// GetActionResult 从对象池获取动作结果
func GetActionResult() *ActionResult {
	return ActionPool.Get().(*ActionResult)
}

// PutActionResult 将动作结果放回对象池
func PutActionResult(result *ActionResult) {
	result.Error = nil // 重置错误
	ActionPool.Put(result)
}
