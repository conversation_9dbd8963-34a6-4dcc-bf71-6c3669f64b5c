// Package core 定义规则引擎的核心接口和基础结构
package core

import (
	"sync"
)

// ObjectPool 提供结构体对象池的通用封装
type ObjectPool struct {
	pool sync.Pool
}

// NewObjectPool 创建一个新的对象池
func NewObjectPool(newFunc func() interface{}) *ObjectPool {
	return &ObjectPool{
		pool: sync.Pool{
			New: newFunc,
		},
	}
}

// Get 从对象池获取对象
func (p *ObjectPool) Get() interface{} {
	return p.pool.Get()
}

// Put 将对象放回对象池
func (p *ObjectPool) Put(obj interface{}) {
	p.pool.Put(obj)
}

// 执行结果对象池
var (
	// ResultPool 执行结果对象池
	ResultPool = NewObjectPool(func() interface{} {
		return &ExecutionResult{
			Matches: make([]MatchedRule, 0, 8), // 预分配8个元素的容量
		}
	})

	// MatchedRulePool 匹配规则结果对象池
	MatchedRulePool = NewObjectPool(func() interface{} {
		return &MatchedRule{
			ActionResults: make([]ActionResult, 0, 4), // 预分配4个动作结果的容量
		}
	})

	// ActionResultPool 动作执行结果对象池
	ActionResultPool = NewObjectPool(func() interface{} {
		return &ActionResult{}
	})
)

// GetResultFromPool 从池中获取ExecutionResult
func GetResultFromPool() *ExecutionResult {
	return ResultPool.Get().(*ExecutionResult)
}

// PutResultToPool 将ExecutionResult归还到池中
func PutResultToPool(result *ExecutionResult) {
	if result == nil {
		return
	}
	// 清空但保留底层数组
	result.Matches = result.Matches[:0]
	result.ClearErrors()
	ResultPool.Put(result)
}

// GetMatchedRuleFromPool 从池中获取MatchedRule
func GetMatchedRuleFromPool() *MatchedRule {
	return MatchedRulePool.Get().(*MatchedRule)
}

// PutMatchedRuleToPool 将MatchedRule归还到池中
func PutMatchedRuleToPool(matched *MatchedRule) {
	if matched == nil {
		return
	}
	matched.Rule = nil
	matched.ActionResults = matched.ActionResults[:0]
	MatchedRulePool.Put(matched)
}

// GetActionResultFromPool 从池中获取ActionResult
func GetActionResultFromPool() *ActionResult {
	return ActionResultPool.Get().(*ActionResult)
}

// PutActionResultToPool 将ActionResult归还到池中
func PutActionResultToPool(actionResult *ActionResult) {
	if actionResult == nil {
		return
	}
	actionResult.Action = nil
	actionResult.Error = nil
	ActionResultPool.Put(actionResult)
}
