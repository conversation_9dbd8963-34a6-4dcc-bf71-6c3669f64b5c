// Package action 提供规则引擎的动作执行系统
package action

import (
	"context"
	"errors"
	"fmt"
	"reflect"

	"admin/pkg/rules/core"
	p "admin/pkg/rules/path"
)

var (
	// 错误定义
	ErrActionExecutionFailed = errors.New("动作执行失败")
	ErrInvalidActionConfig   = errors.New("无效的动作配置")
	ErrInvalidTargetType     = errors.New("无效的目标类型")
)

// ActionFunc 动作执行函数类型
type ActionFunc func(ctx context.Context, data any, params map[string]any) error

// FunctionAction 函数类型动作，执行自定义函数
type FunctionAction struct {
	// 动作名称
	Name string `json:"name"`

	// 动作描述
	Description string `json:"description"`

	// 动作参数
	Params map[string]any `json:"params"`

	// 动作执行函数
	execFunc ActionFunc
}

// NewFunctionAction 创建一个新的函数动作
func NewFunctionAction(name string, description string, execFunc ActionFunc, params map[string]any) *FunctionAction {
	if params == nil {
		params = make(map[string]any)
	}

	return &FunctionAction{
		Name:        name,
		Description: description,
		Params:      params,
		execFunc:    execFunc,
	}
}

// Execute 执行函数动作
func (a *FunctionAction) Execute(ctx context.Context, data any) error {
	if a.execFunc == nil {
		return fmt.Errorf("%w: 动作函数未定义", ErrActionExecutionFailed)
	}

	return a.execFunc(ctx, data, a.Params)
}

// SetPropertyAction 设置属性值动作，将指定路径的值设置为新值
type SetPropertyAction struct {
	// 要设置的路径
	Path string `json:"path"`

	// 新值
	Value any `json:"value"`

	// 路径解析器
	parser *p.PathParser
}

// NewSetPropertyAction 创建一个新的设置属性动作
func NewSetPropertyAction(path string, value any) *SetPropertyAction {
	return &SetPropertyAction{
		Path:   path,
		Value:  value,
		parser: p.NewPathParser(),
	}
}

// Execute 执行设置属性动作
func (a *SetPropertyAction) Execute(ctx context.Context, data any) error {
	// 检查数据是否可修改
	if !isModifiable(data) {
		return fmt.Errorf("%w: 数据不可修改", ErrInvalidTargetType)
	}

	// 设置属性值
	err := a.parser.SetValue(data, a.Path, a.Value)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrActionExecutionFailed, err)
	}

	return nil
}

// ModifyPropertyAction 修改属性值动作，根据现有值计算新值
type ModifyPropertyAction struct {
	// 要修改的路径
	Path string `json:"path"`

	// 运算操作 (add, subtract, multiply, divide, concat)
	Operation string `json:"operation"`

	// 运算值
	Operand any `json:"operand"`

	// 路径解析器
	parser *p.PathParser
}

// 支持的运算操作
const (
	OpAdd      = "add"      // 加法
	OpSubtract = "subtract" // 减法
	OpMultiply = "multiply" // 乘法
	OpDivide   = "divide"   // 除法
	OpConcat   = "concat"   // 字符串拼接
)

// NewModifyPropertyAction 创建一个新的修改属性动作
func NewModifyPropertyAction(path string, operation string, operand any) *ModifyPropertyAction {
	return &ModifyPropertyAction{
		Path:      path,
		Operation: operation,
		Operand:   operand,
		parser:    p.NewPathParser(),
	}
}

// Execute 执行修改属性动作
func (a *ModifyPropertyAction) Execute(ctx context.Context, data any) error {
	// 检查数据是否可修改
	if !isModifiable(data) {
		return fmt.Errorf("%w: 数据不可修改", ErrInvalidTargetType)
	}

	// 获取当前值
	currentValue, err := a.parser.GetValue(data, a.Path)
	if err != nil {
		return fmt.Errorf("获取属性值失败: %w", err)
	}

	// 计算新值
	newValue, err := computeNewValue(currentValue, a.Operation, a.Operand)
	if err != nil {
		return fmt.Errorf("计算新值失败: %w", err)
	}

	// 设置新值
	err = a.parser.SetValue(data, a.Path, newValue)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrActionExecutionFailed, err)
	}

	return nil
}

// ConditionalAction 条件动作，根据条件执行不同的动作
type ConditionalAction struct {
	// 条件
	Condition core.Condition `json:"condition"`

	// 条件为真时执行的动作
	TrueAction core.Action `json:"trueAction"`

	// 条件为假时执行的动作，可选
	FalseAction core.Action `json:"falseAction,omitempty"`
}

// NewConditionalAction 创建一个新的条件动作
func NewConditionalAction(condition core.Condition, trueAction core.Action, falseAction core.Action) *ConditionalAction {
	return &ConditionalAction{
		Condition:   condition,
		TrueAction:  trueAction,
		FalseAction: falseAction,
	}
}

// Execute 执行条件动作
func (a *ConditionalAction) Execute(ctx context.Context, data any) error {
	// 评估条件
	result, err := a.Condition.Evaluate(ctx, data)
	if err != nil {
		return fmt.Errorf("条件评估失败: %w", err)
	}

	// 根据条件结果执行相应动作
	if result {
		if a.TrueAction != nil {
			return a.TrueAction.Execute(ctx, data)
		}
	} else if a.FalseAction != nil {
		return a.FalseAction.Execute(ctx, data)
	}

	return nil
}

// CompositeAction 组合动作，按顺序执行多个动作
type CompositeAction struct {
	// 动作列表
	Actions []core.Action `json:"actions"`
}

// NewCompositeAction 创建一个新的组合动作
func NewCompositeAction(actions ...core.Action) *CompositeAction {
	return &CompositeAction{
		Actions: actions,
	}
}

// Execute 执行组合动作
func (a *CompositeAction) Execute(ctx context.Context, data any) error {
	for i, action := range a.Actions {
		if err := action.Execute(ctx, data); err != nil {
			return fmt.Errorf("执行第 %d 个动作失败: %w", i+1, err)
		}
	}

	return nil
}

// LogAction 日志动作，将指定消息记录到日志
type LogAction struct {
	// 日志消息
	Message string `json:"message"`

	// 日志级别
	Level string `json:"level"`

	// 要记录的数据路径，可选
	DataPath string `json:"dataPath,omitempty"`

	// 路径解析器
	parser *p.PathParser
}

// 日志级别
const (
	LogLevelDebug = "debug"
	LogLevelInfo  = "info"
	LogLevelWarn  = "warn"
	LogLevelError = "error"
)

// NewLogAction 创建一个新的日志动作
func NewLogAction(message string, level string, dataPath string) *LogAction {
	return &LogAction{
		Message:  message,
		Level:    level,
		DataPath: dataPath,
		parser:   p.NewPathParser(),
	}
}

// Execute 执行日志动作
func (a *LogAction) Execute(ctx context.Context, data any) error {
	logMessage := a.Message

	// 如果指定了数据路径，获取相应的值
	if a.DataPath != "" {
		pathValue, err := a.parser.GetValue(data, a.DataPath)
		if err == nil {
			logMessage = fmt.Sprintf("%s: %v", logMessage, pathValue)
		} else {
			logMessage = fmt.Sprintf("%s: [error getting value: %v]", logMessage, err)
		}
	}

	// 根据日志级别记录日志
	// 实际项目中可以集成特定的日志系统
	switch a.Level {
	case LogLevelDebug:
		fmt.Printf("[DEBUG] %s\n", logMessage)
	case LogLevelInfo:
		fmt.Printf("[INFO] %s\n", logMessage)
	case LogLevelWarn:
		fmt.Printf("[WARN] %s\n", logMessage)
	case LogLevelError:
		fmt.Printf("[ERROR] %s\n", logMessage)
	default:
		fmt.Printf("[%s] %s\n", a.Level, logMessage)
	}

	return nil
}

// AppendToArrayAction 向数组追加元素的动作
type AppendToArrayAction struct {
	// 数组路径
	path string
	// 要追加的值
	value any
}

// NewAppendToArrayAction 创建一个新的数组追加动作
func NewAppendToArrayAction(path string, value any) *AppendToArrayAction {
	return &AppendToArrayAction{
		path:  path,
		value: value,
	}
}

// Execute 执行数组追加操作
func (a *AppendToArrayAction) Execute(ctx context.Context, data any) error {
	// 获取路径解析器
	parser := p.NewPathParser()

	// 获取当前数组
	currentArray, err := parser.GetValue(data, a.path)
	if err != nil {
		// 如果路径不存在，创建新数组
		err = parser.SetValue(data, a.path, []any{a.value})
		return err
	}

	// 检查是否为数组类型
	currentArrayValue := reflect.ValueOf(currentArray)
	if currentArrayValue.Kind() != reflect.Slice && currentArrayValue.Kind() != reflect.Array {
		// 如果不是数组，则创建新数组
		err = parser.SetValue(data, a.path, []any{a.value})
		return err
	}

	// 是数组，执行追加操作
	var newArray reflect.Value

	// 处理指针类型
	if currentArrayValue.Kind() == reflect.Ptr {
		if currentArrayValue.IsNil() {
			// 指针为nil，创建新数组
			elemType := currentArrayValue.Type().Elem().Elem()
			newArray = reflect.MakeSlice(reflect.SliceOf(elemType), 0, 1)
			newPtr := reflect.New(newArray.Type())
			newPtr.Elem().Set(newArray)
			newArray = newPtr
		} else {
			currentArrayValue = currentArrayValue.Elem()
			elemType := currentArrayValue.Type().Elem()
			length := currentArrayValue.Len()
			newArray = reflect.MakeSlice(currentArrayValue.Type(), length+1, length+1)
			for i := 0; i < length; i++ {
				newArray.Index(i).Set(currentArrayValue.Index(i))
			}
			newArray.Index(length).Set(reflect.ValueOf(a.value).Convert(elemType))

			// 将新数组包装为指针
			newPtr := reflect.New(newArray.Type())
			newPtr.Elem().Set(newArray)
			newArray = newPtr
		}
	} else {
		// 普通数组，直接追加
		elemType := currentArrayValue.Type().Elem()
		length := currentArrayValue.Len()
		newArray = reflect.MakeSlice(currentArrayValue.Type(), length+1, length+1)

		// 复制原有元素
		for i := 0; i < length; i++ {
			newArray.Index(i).Set(currentArrayValue.Index(i))
		}

		// 添加新元素，尝试类型转换
		valueToAppend := reflect.ValueOf(a.value)
		if valueToAppend.Type().AssignableTo(elemType) {
			newArray.Index(length).Set(valueToAppend)
		} else if valueToAppend.Type().ConvertibleTo(elemType) {
			newArray.Index(length).Set(valueToAppend.Convert(elemType))
		} else {
			return fmt.Errorf("类型不匹配: 无法将 %v 类型添加到 %v 类型的数组中", valueToAppend.Type(), elemType)
		}
	}

	// 设置回原始数据
	return parser.SetValue(data, a.path, newArray.Interface())
}

// isModifiable 检查数据是否可修改
func isModifiable(data any) bool {
	if data == nil {
		return false
	}

	v := reflect.ValueOf(data)

	// 检查数据类型是否支持修改
	switch v.Kind() {
	case reflect.Ptr:
		// 指针类型可以修改，前提是指向的不是常量
		return !v.IsNil()
	case reflect.Map, reflect.Slice:
		// Map和Slice可以修改
		return true
	default:
		// 其他类型不可修改
		return false
	}
}

// computeNewValue 根据操作和操作数计算新值
func computeNewValue(currentValue any, operation string, operand any) (any, error) {
	if currentValue == nil {
		return nil, fmt.Errorf("当前值为空")
	}

	switch operation {
	case OpAdd, OpSubtract, OpMultiply, OpDivide:
		// 数值运算
		currentFloat, currentOk := toFloat64(currentValue)
		operandFloat, operandOk := toFloat64(operand)

		if !currentOk || !operandOk {
			return nil, fmt.Errorf("非数值类型无法进行数值运算")
		}

		switch operation {
		case OpAdd:
			return currentFloat + operandFloat, nil
		case OpSubtract:
			return currentFloat - operandFloat, nil
		case OpMultiply:
			return currentFloat * operandFloat, nil
		case OpDivide:
			if operandFloat == 0 {
				return nil, fmt.Errorf("除数不能为零")
			}
			return currentFloat / operandFloat, nil
		}

	case OpConcat:
		// 字符串拼接
		currentStr := fmt.Sprint(currentValue)
		operandStr := fmt.Sprint(operand)
		return currentStr + operandStr, nil

	default:
		return nil, fmt.Errorf("不支持的操作: %s", operation)
	}

	return nil, fmt.Errorf("未知错误")
}

// toFloat64 尝试将值转换为float64
func toFloat64(v any) (float64, bool) {
	switch val := v.(type) {
	case int:
		return float64(val), true
	case int8:
		return float64(val), true
	case int16:
		return float64(val), true
	case int32:
		return float64(val), true
	case int64:
		return float64(val), true
	case uint:
		return float64(val), true
	case uint8:
		return float64(val), true
	case uint16:
		return float64(val), true
	case uint32:
		return float64(val), true
	case uint64:
		return float64(val), true
	case float32:
		return float64(val), true
	case float64:
		return val, true
	default:
		// 尝试使用反射转换
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return float64(rv.Int()), true
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return float64(rv.Uint()), true
		case reflect.Float32, reflect.Float64:
			return rv.Float(), true
		default:
			return 0, false
		}
	}
}
