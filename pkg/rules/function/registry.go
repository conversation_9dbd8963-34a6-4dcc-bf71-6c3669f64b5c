// Package function 提供规则引擎的函数支持
package function

import (
	"errors"
	"fmt"
	"reflect"
	"sync"
)

// 常见错误定义
var (
	ErrFunctionNotFound      = errors.New("函数未找到")
	ErrInvalidArgumentCount  = errors.New("参数数量不匹配")
	ErrInvalidArgumentType   = errors.New("参数类型不匹配")
	ErrInvalidReturnValue    = errors.New("返回值类型不匹配")
	ErrFunctionAlreadyExists = errors.New("函数已存在")
)

// FunctionInfo 函数信息
type FunctionInfo struct {
	// 函数名
	Name string

	// 实际函数值
	Fn interface{}

	// 函数签名描述
	Description string

	// 参数类型
	ParamTypes []reflect.Type

	// 返回值类型，至少有一个返回值
	ReturnTypes []reflect.Type
}

// FunctionRegistry 函数注册表
type FunctionRegistry struct {
	// 函数映射表
	functions map[string]FunctionInfo

	// 互斥锁保证并发安全
	mu sync.RWMutex
}

// NewFunctionRegistry 创建一个新的函数注册表
func NewFunctionRegistry() *FunctionRegistry {
	return &FunctionRegistry{
		functions: make(map[string]FunctionInfo),
	}
}

// RegisterFunction 注册自定义函数
func (r *FunctionRegistry) RegisterFunction(name string, fn interface{}, description string) error {
	if name == "" {
		return errors.New("函数名不能为空")
	}

	if fn == nil {
		return errors.New("函数不能为nil")
	}

	// 获取函数类型
	fnValue := reflect.ValueOf(fn)
	fnType := fnValue.Type()

	// 检查是否是函数
	if fnType.Kind() != reflect.Func {
		return errors.New("注册的必须是函数")
	}

	// 提取参数类型
	paramCount := fnType.NumIn()
	paramTypes := make([]reflect.Type, paramCount)
	for i := range paramCount {
		paramTypes[i] = fnType.In(i)
	}

	// 提取返回值类型
	returnCount := fnType.NumOut()
	if returnCount == 0 {
		return errors.New("函数必须至少有一个返回值")
	}

	returnTypes := make([]reflect.Type, returnCount)
	for i := 0; i < returnCount; i++ {
		returnTypes[i] = fnType.Out(i)
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	// 检查函数是否已经存在
	if _, exists := r.functions[name]; exists {
		return fmt.Errorf("%w: %s", ErrFunctionAlreadyExists, name)
	}

	// 注册函数信息
	r.functions[name] = FunctionInfo{
		Name:        name,
		Fn:          fn,
		Description: description,
		ParamTypes:  paramTypes,
		ReturnTypes: returnTypes,
	}

	return nil
}

// RegisterFunctionMap 批量注册函数
func (r *FunctionRegistry) RegisterFunctionMap(functions map[string]interface{}, descriptions map[string]string) error {
	for name, fn := range functions {
		desc := ""
		if d, exists := descriptions[name]; exists {
			desc = d
		}

		if err := r.RegisterFunction(name, fn, desc); err != nil {
			return err
		}
	}

	return nil
}

// UnregisterFunction 注销函数
func (r *FunctionRegistry) UnregisterFunction(name string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, exists := r.functions[name]; !exists {
		return fmt.Errorf("%w: %s", ErrFunctionNotFound, name)
	}

	delete(r.functions, name)
	return nil
}

// GetFunction 获取函数信息
func (r *FunctionRegistry) GetFunction(name string) (FunctionInfo, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	info, exists := r.functions[name]
	if !exists {
		return FunctionInfo{}, fmt.Errorf("%w: %s", ErrFunctionNotFound, name)
	}

	return info, nil
}

// HasFunction 检查函数是否存在
func (r *FunctionRegistry) HasFunction(name string) bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	_, exists := r.functions[name]
	return exists
}

// InvokeFunction 调用函数
func (r *FunctionRegistry) InvokeFunction(name string, args ...interface{}) ([]interface{}, error) {
	// 获取函数信息
	info, err := r.GetFunction(name)
	if err != nil {
		return nil, err
	}

	// 检查参数数量
	if len(args) != len(info.ParamTypes) {
		return nil, fmt.Errorf("%w: 期望%d个参数，实际提供%d个",
			ErrInvalidArgumentCount, len(info.ParamTypes), len(args))
	}

	// 准备参数值
	fnValue := reflect.ValueOf(info.Fn)
	fnArgs := make([]reflect.Value, len(args))

	for i, arg := range args {
		// 参数值
		var argValue reflect.Value
		if arg == nil {
			// 处理nil参数
			argValue = reflect.Zero(info.ParamTypes[i])
		} else {
			argValue = reflect.ValueOf(arg)

			// 类型检查与转换
			if !argValue.Type().AssignableTo(info.ParamTypes[i]) {
				// 尝试类型转换
				if argValue.Type().ConvertibleTo(info.ParamTypes[i]) {
					argValue = argValue.Convert(info.ParamTypes[i])
				} else {
					return nil, fmt.Errorf("%w: 参数 %d 预期 %s 类型, 实际是 %s",
						ErrInvalidArgumentType, i, info.ParamTypes[i], argValue.Type())
				}
			}
		}

		fnArgs[i] = argValue
	}

	// 调用函数
	returnValues := fnValue.Call(fnArgs)

	// 处理返回值
	results := make([]interface{}, len(returnValues))
	for i, val := range returnValues {
		results[i] = val.Interface()
	}

	return results, nil
}

// InvokeFunctionSingle 调用函数并返回单个结果
func (r *FunctionRegistry) InvokeFunctionSingle(name string, args ...interface{}) (interface{}, error) {
	results, err := r.InvokeFunction(name, args...)
	if err != nil {
		return nil, err
	}

	if len(results) == 0 {
		return nil, errors.New("函数没有返回值")
	}

	// 如果最后一个返回值是error类型，则作为错误返回
	lastIndex := len(results) - 1
	if err, ok := results[lastIndex].(error); ok && err != nil {
		return nil, err
	}

	return results[0], nil
}

// GetAllFunctions 获取所有注册的函数信息
func (r *FunctionRegistry) GetAllFunctions() []FunctionInfo {
	r.mu.RLock()
	defer r.mu.RUnlock()

	functions := make([]FunctionInfo, 0, len(r.functions))
	for _, info := range r.functions {
		functions = append(functions, info)
	}

	return functions
}

// RegisterCommonFunctions 注册常用函数
func (r *FunctionRegistry) RegisterCommonFunctions() error {
	// 字符串操作
	if err := r.RegisterFunction("len",
		func(s string) int { return len(s) },
		"返回字符串长度"); err != nil {
		return err
	}

	if err := r.RegisterFunction("concat",
		func(a, b string) string { return a + b },
		"连接两个字符串"); err != nil {
		return err
	}

	if err := r.RegisterFunction("substring",
		func(s string, start, end int) string {
			if start < 0 {
				start = 0
			}
			if end > len(s) {
				end = len(s)
			}
			if start >= end {
				return ""
			}
			return s[start:end]
		},
		"截取字符串子串"); err != nil {
		return err
	}

	// 数值操作
	if err := r.RegisterFunction("add",
		func(a, b float64) float64 { return a + b },
		"两数相加"); err != nil {
		return err
	}

	if err := r.RegisterFunction("subtract",
		func(a, b float64) float64 { return a - b },
		"两数相减"); err != nil {
		return err
	}

	if err := r.RegisterFunction("multiply",
		func(a, b float64) float64 { return a * b },
		"两数相乘"); err != nil {
		return err
	}

	if err := r.RegisterFunction("divide",
		func(a, b float64) (float64, error) {
			if b == 0 {
				return 0, errors.New("除数不能为零")
			}
			return a / b, nil
		},
		"两数相除"); err != nil {
		return err
	}

	return nil
}
