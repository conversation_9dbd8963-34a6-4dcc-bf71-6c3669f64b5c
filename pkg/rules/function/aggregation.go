// Package function 提供规则引擎的函数支持
package function

import (
	"errors"
	"fmt"
	"math"
	"reflect"
	"sort"
)

// AggregationFunction 聚合函数接口
type AggregationFunction struct {
	// 函数名称
	Name string
	// 函数实现
	Fn func(values []any) (any, error)
	// 函数描述
	Description string
}

// 常见聚合函数错误
var (
	ErrEmptyArray      = errors.New("空数组")
	ErrNonNumericData  = errors.New("非数值数据")
	ErrInvalidDataType = errors.New("无效的数据类型")
)

// RegisterAggregationFunctions 在函数注册表中注册聚合函数
func (r *FunctionRegistry) RegisterAggregationFunctions() error {
	// Count 计数函数
	if err := r.RegisterFunction("count",
		func(arr []any) int {
			return len(arr)
		},
		"计算数组元素数量"); err != nil {
		return err
	}

	// Sum 求和函数
	if err := r.RegisterFunction("sum",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, nil
			}

			var sum float64
			for _, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				sum += num
			}
			return sum, nil
		},
		"计算数组元素之和"); err != nil {
		return err
	}

	// Avg 平均值函数
	if err := r.RegisterFunction("avg",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, ErrEmptyArray
			}

			var sum float64
			for _, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				sum += num
			}
			return sum / float64(len(arr)), nil
		},
		"计算数组元素平均值"); err != nil {
		return err
	}

	// Min 最小值函数
	if err := r.RegisterFunction("min",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, ErrEmptyArray
			}

			minVal := math.MaxFloat64
			for _, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				if num < minVal {
					minVal = num
				}
			}
			return minVal, nil
		},
		"计算数组元素最小值"); err != nil {
		return err
	}

	// Max 最大值函数
	if err := r.RegisterFunction("max",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, ErrEmptyArray
			}

			maxVal := -math.MaxFloat64
			for _, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				if num > maxVal {
					maxVal = num
				}
			}
			return maxVal, nil
		},
		"计算数组元素最大值"); err != nil {
		return err
	}

	// Median 中位数函数
	if err := r.RegisterFunction("median",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, ErrEmptyArray
			}

			// 将元素转换为float64数组
			nums := make([]float64, len(arr))
			for i, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				nums[i] = num
			}

			// 排序数组
			sort.Float64s(nums)

			// 计算中位数
			mid := len(nums) / 2
			if len(nums)%2 == 0 {
				// 偶数个元素，取中间两个数的平均值
				return (nums[mid-1] + nums[mid]) / 2, nil
			}
			// 奇数个元素，直接返回中间值
			return nums[mid], nil
		},
		"计算数组元素中位数"); err != nil {
		return err
	}

	// Variance 方差函数
	if err := r.RegisterFunction("variance",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, ErrEmptyArray
			}

			// 计算平均值
			var sum float64
			nums := make([]float64, len(arr))
			for i, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				nums[i] = num
				sum += num
			}
			avg := sum / float64(len(nums))

			// 计算方差
			var variance float64
			for _, num := range nums {
				diff := num - avg
				variance += diff * diff
			}
			return variance / float64(len(nums)), nil
		},
		"计算数组元素方差"); err != nil {
		return err
	}

	// StdDev 标准差函数
	if err := r.RegisterFunction("stdDev",
		func(arr []any) (float64, error) {
			if len(arr) == 0 {
				return 0, ErrEmptyArray
			}

			// 先计算方差
			var sum float64
			nums := make([]float64, len(arr))
			for i, v := range arr {
				num, err := toFloat64(v)
				if err != nil {
					return 0, err
				}
				nums[i] = num
				sum += num
			}
			avg := sum / float64(len(nums))

			variance := 0.0
			for _, num := range nums {
				diff := num - avg
				variance += diff * diff
			}
			variance = variance / float64(len(nums))

			// 标准差是方差的平方根
			return math.Sqrt(variance), nil
		},
		"计算数组元素标准差"); err != nil {
		return err
	}

	// Distinct 唯一值函数
	if err := r.RegisterFunction("distinct",
		func(arr []any) []any {
			if len(arr) == 0 {
				return []any{}
			}

			// 使用map去重
			distinct := make(map[any]bool)
			for _, v := range arr {
				distinct[v] = true
			}

			// 提取唯一值
			result := make([]any, 0, len(distinct))
			for k := range distinct {
				result = append(result, k)
			}

			return result
		},
		"获取数组中的唯一值"); err != nil {
		return err
	}

	// DistinctCount 唯一值计数函数
	if err := r.RegisterFunction("distinctCount",
		func(arr []any) int {
			if len(arr) == 0 {
				return 0
			}

			// 使用map去重并计数
			distinct := make(map[any]bool)
			for _, v := range arr {
				distinct[v] = true
			}

			return len(distinct)
		},
		"计算数组中唯一值的数量"); err != nil {
		return err
	}

	return nil
}

// toFloat64 将值转换为float64
func toFloat64(value any) (float64, error) {
	if value == nil {
		return 0, nil
	}

	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int8:
		return float64(v), nil
	case int16:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint8:
		return float64(v), nil
	case uint16:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		// 尝试将字符串解析为浮点数
		f, err := parseFloat(v)
		if err != nil {
			return 0, err
		}
		return f, nil
	default:
		// 如果是自定义类型，尝试使用反射获取数值
		val := reflect.ValueOf(value)
		switch val.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return float64(val.Int()), nil
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return float64(val.Uint()), nil
		case reflect.Float32, reflect.Float64:
			return val.Float(), nil
		default:
			return 0, ErrNonNumericData
		}
	}
}

// parseFloat 解析字符串为浮点数
func parseFloat(s string) (float64, error) {
	var result float64
	_, err := fmt.Sscanf(s, "%f", &result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

// ToFloat64 将值转换为float64（导出版本）
func (r *FunctionRegistry) ToFloat64(value any) (float64, error) {
	return toFloat64(value)
}
