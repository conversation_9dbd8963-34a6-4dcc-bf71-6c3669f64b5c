# Rules 规则引擎

一个高性能、灵活可扩展的 Go 语言规则引擎框架，支持复杂条件评估和动作执行，适用于业务规则处理、工作流引擎、决策系统等场景。

## 特性

- **强大的条件系统**：支持简单条件 ([`condition.SimpleCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition.go))、复合条件 ([`condition.CompositeCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition.go))、正则表达式条件 ([`condition.RegexCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/regex_condition.go))、时间条件 ([`condition.TimeCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/time_condition.go))、函数条件 ([`condition.FunctionCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/function_condition.go)) 等多种条件类型。
- **灵活的动作系统**：包括属性设置 ([`action.SetPropertyAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go))、修改 ([`action.ModifyPropertyAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go))、日志记录 ([`action.LogAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go))、条件动作 ([`action.ConditionalAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go))、组合动作 ([`action.CompositeAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go))、函数动作 ([`action.FunctionAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)) 等多种动作类型。
- **高性能设计**：支持规则并发执行、对象池复用 ([`core.ObjectPool`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/object_pool.go))、规则索引优化 ([`core.RuleIndex`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/rule_index.go))、路径解析缓存 ([`path.LRUCache`](/Volumes/data/Code/Go/src/admin/pkg/rules/path/path_parser.go)) 和条件优化 ([`condition.ConditionOptimizer`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition_optimizer.go))。
- **路径表达式**：使用灵活的路径表达式 ([`path.PathParser`](/Volumes/data/Code/Go/src/admin/pkg/rules/path/path.go)) 访问复杂数据结构中的属性，支持点表示法、数组索引和通配符。
- **DSL 支持**：提供领域特定语言 (DSL) 解析 ([`dsl.ParseDSL`](/Volumes/data/Code/Go/src/admin/pkg/rules/dsl/dsl.go)) 和表达式评估。
- **函数扩展**：支持自定义函数注册 ([`function.FunctionRegistry`](/Volumes/data/Code/Go/src/admin/pkg/rules/function/registry.go)) 和调用。
- **优先级控制**：规则执行顺序基于优先级 ([`core.Rule.GetPriority`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go))。
- **完全可扩展**：基于接口设计 ([`core.RuleEngine`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go), [`core.Rule`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go), [`core.Condition`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go), [`core.Action`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go))，各组件可根据需求扩展和自定义。

## 安装

```bash
# 注意：请将 yourusername/admin 替换为实际的项目路径
go get github.com/yourusername/admin/pkg/rules
```

## 基本用法

以下是一个简单的规则引擎使用示例：

```go
package main

import (
    "context"
    "fmt"

    "admin/pkg/rules/action"
    "admin/pkg/rules/condition"
    "admin/pkg/rules/core"
)

func main() {
    // 创建规则引擎 (非并发)
    engine := core.NewDefaultRuleEngine(false, 1) // concurrent=false, maxWorkers=1

    // 创建测试数据
    userData := map[string]any{
        "id":     1001,
        "name":   "测试用户",
        "age":    30,
        "points": 50,
        "tags":   []string{"普通"},
    }

    // 创建规则：年龄大于25的用户标记为"已验证"
    ageCondition := condition.NewSimpleCondition("age", condition.OpGreaterThan, 25)
    setVerifiedAction := action.NewSetPropertyAction("verified", true)
    appendTagAction := action.NewAppendToArrayAction("tags", "成年") // 假设有追加标签的动作
    rule1 := core.NewRule(
        "rule-001",          // ID
        "年龄验证规则",        // 名称
        "检查用户年龄是否大于25岁", // 描述
        1,                    // 优先级
        ageCondition,         // 条件
        []core.Action{setVerifiedAction, appendTagAction}, // 动作列表
        true,                 // 启用规则
    )

    // 添加规则到引擎
    err := engine.AddRule(rule1)
    if err != nil {
        fmt.Printf("添加规则失败: %v\n", err)
        return
    }

    // 执行规则
    ctx := context.Background()
    result, err := engine.Execute(ctx, userData)
    if err != nil {
        fmt.Printf("规则执行失败：%v\n", err)
        return
    }

    // 输出结果
    fmt.Printf("匹配规则数：%d\n", len(result.Matches))
    if len(result.Matches) > 0 {
        fmt.Printf("匹配的规则ID: %s\n", result.Matches[0].Rule.GetID())
    }
    fmt.Printf("用户数据：%+v\n", userData) // 查看数据是否被修改
}

```

## 主要组件

### 核心组件 ([core](/Volumes/data/Code/Go/src/admin/pkg/rules/core/))

核心组件定义了规则引擎的基础接口和实现：

- [`RuleEngine`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go)：规则引擎接口
- [`Rule`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go)：规则接口
- [`Condition`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go)：条件接口
- [`Action`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go)：动作接口
- [`DefaultRuleEngine`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine_impl.go)：规则引擎默认实现
- [`DefaultRule`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/default_rule.go)：规则默认实现
- [`RuleIndex`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/rule_index.go)：规则索引，用于高效查找匹配规则
- [`ObjectPool`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/object_pool.go)：对象池，提升性能

### 条件系统 ([condition](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/))

条件系统提供了多种条件评估功能：

- [`SimpleCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition.go)：简单条件比较（等于、大于、小于等）
- [`CompositeCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition.go)：复合条件（AND、OR、NOT）
- [`RegexCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/regex_condition.go)：正则表达式条件
- [`TimeCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/time_condition.go)：时间相关条件（早于、晚于、同一天等）
- [`FunctionCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/function_condition.go)：函数条件，通过调用注册函数评估
- [`ConditionOptimizer`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition_optimizer.go)：条件优化器，支持延迟计算、条件合并、自适应重排序等。
- [`StatsCollector`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/stats_collector.go)：条件评估统计收集器。

### 动作系统 ([action](/Volumes/data/Code/Go/src/admin/pkg/rules/action/))

动作系统定义了规则匹配后执行的操作：

- [`SetPropertyAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：设置属性值
- [`ModifyPropertyAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：修改属性值（加减乘除、拼接）
- [`AppendToArrayAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：向数组追加元素
- [`LogAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：记录日志
- [`ConditionalAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：条件动作，根据条件执行不同动作
- [`CompositeAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：组合动作，顺序执行多个动作
- [`FunctionAction`](/Volumes/data/Code/Go/src/admin/pkg/rules/action/action.go)：函数动作，执行自定义函数

### 路径解析 ([path](/Volumes/data/Code/Go/src/admin/pkg/rules/path/))

路径解析器提供了访问复杂数据结构的能力：

- [`PathParser`](/Volumes/data/Code/Go/src/admin/pkg/rules/path/path.go)：路径解析器
- 支持点表示法：`user.address.city`
- 支持数组索引：`items[0].name`
- 支持通配符：`items[*].price`
- [`LRUCache`](/Volumes/data/Code/Go/src/admin/pkg/rules/path/path_parser.go)：内置 LRU 缓存，提高重复路径解析性能。

### DSL支持 ([dsl](/Volumes/data/Code/Go/src/admin/pkg/rules/dsl/))

DSL支持提供了基于JSON的规则描述能力：

- [`SimpleExpression`](/Volumes/data/Code/Go/src/admin/pkg/rules/dsl/dsl.go)：简单表达式
- [`CompositeExpression`](/Volumes/data/Code/Go/src/admin/pkg/rules/dsl/dsl.go)：复合表达式
- [`DSLCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/dsl/dsl.go)：DSL条件
- [`ParseDSL`](/Volumes/data/Code/Go/src/admin/pkg/rules/dsl/dsl.go)：解析 DSL 字符串为表达式。

### 函数系统 ([function](/Volumes/data/Code/Go/src/admin/pkg/rules/function/))

函数系统支持注册和调用自定义函数：

- [`FunctionRegistry`](/Volumes/data/Code/Go/src/admin/pkg/rules/function/registry.go)：函数注册表
- [`FunctionInfo`](/Volumes/data/Code/Go/src/admin/pkg/rules/function/registry.go)：函数信息
- [`RegisterFunction`](/Volumes/data/Code/Go/src/admin/pkg/rules/function/registry.go)：注册自定义函数。

## 高级用法

### 1. 复合条件

```go
import "admin/pkg/rules/condition"
import "admin/pkg/rules/core"
import "admin/pkg/rules/action"

// 复合条件：用户在北京且是VIP
cityCondition := condition.NewSimpleCondition("user.address.city", condition.OpEqual, "北京")
vipCondition := condition.NewSimpleCondition("user.tags[*]", condition.OpContains, "VIP") // 假设tags是数组
compositeCondition := condition.NewAndCondition(cityCondition, vipCondition)

// 使用复合条件创建规则
rule := core.NewRule("rule-002", "VIP用户特权规则", "为北京的VIP用户增加积分", 2,
                    compositeCondition,
                    []core.Action{action.NewModifyPropertyAction("user.points", action.OpAdd, 100)},
                    true)
```

### 2. 使用DSL定义规则

```go
import "admin/pkg/rules/dsl"
import "admin/pkg/rules/core"

// DSL表达式
dslStr := `{
    "type": "composite",
    "operator": "and",
    "expressions": [
        {
            "type": "simple",
            "path": "user.age",
            "operator": "gt",
            "value": 25
        },
        {
            "type": "simple",
            "path": "user.address.city",
            "operator": "eq",
            "value": "北京"
        }
    ]
}`

// 解析DSL
expr, err := dsl.ParseDSL(dslStr)
if err != nil {
    // 处理错误
    return
}

// 转换为条件
cond, err := expr.ToCondition()
if err != nil {
    // 处理错误
    return
}

// 假设 actions 已经定义
var actions []core.Action
// ...

// 使用条件创建规则
rule := core.NewRule("rule-dsl", "DSL规则", "使用DSL定义的规则", 3, cond, actions, true)
```

### 3. 条件优化

规则引擎提供了多种条件优化功能，显著提升大型规则集和复杂条件的执行性能：

```go
import "admin/pkg/rules"
import "admin/pkg/rules/condition"

// 创建规则引擎时指定优化级别
// OptimizationLevelNone - 不优化
// OptimizationLevelBasic - 基本优化（仅延迟计算）
// OptimizationLevelIntermediate - 中级优化（延迟计算 + 条件合并）
// OptimizationLevelAdvanced - 高级优化（延迟计算 + 条件合并 + 自适应重排序）
engine := rules.CreateEngineWithOptimizationLevel(true, 10, rules.OptimizationLevelAdvanced)

// 启用统计信息收集 (用于自适应重排序)
rules.EnableStatisticsCollection()

// 获取条件统计信息
stats := rules.GetStatistics()
for _, stat := range stats {
    fmt.Printf("条件ID: %s, 评估次数: %d, 平均耗时: %dns, True概率: %.2f\n",
        stat.ID, stat.EvaluationCount, stat.AverageDuration, stat.TrueProbability)
}

// 重置统计信息
rules.ResetStatistics()
```

#### 优化特性说明

1.  **延迟计算(Lazy Evaluation)**：通过短路逻辑避免不必要的条件评估。例如，在AND条件中，一旦有子条件返回false，后续条件将不再评估。([`condition.LazyCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/lazy_condition.go))
2.  **条件合并(Condition Merging)**：识别并合并语义等价的条件，避免重复计算。当复杂规则树中存在多个等价条件时，此优化可显著提升性能。([`condition.ConditionMerger`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/condition_merger.go))
3.  **自适应重排序(Adaptive Reordering)**：基于运行时统计信息动态调整条件执行顺序。系统会优先执行更可能触发短路的条件，如在AND条件中优先评估更可能返回false的条件。([`condition.AdaptiveCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/condition/adaptive_condition.go))

示例：使用自适应复合条件

```go
import "admin/pkg/rules/condition"
import "admin/pkg/rules/core"
import "time"

// 创建一个自适应AND条件
condition1 := condition.NewSimpleCondition("user.age", condition.OpGreaterThan, 25)
condition2 := condition.NewSimpleCondition("user.points", condition.OpGreaterThan, 100)

adaptiveAnd := condition.NewAdaptiveAndCondition(condition1, condition2)
adaptiveAnd.SetReorderInterval(time.Minute * 2) // 设置重排序间隔

// 假设 actions 已经定义
var actions []core.Action
// ...

// 创建规则
rule := core.NewRule("adaptive-rule", "自适应规则", "使用自适应条件优化的规则", 1, adaptiveAnd, actions, true)
```

### 4. 并发执行规则

```go
import "admin/pkg/rules/core"
import "context"

// 创建支持并发执行的规则引擎
engine := core.NewDefaultRuleEngine(true, 10) // 第一个参数为true表示启用并发，10表示最大工作协程数

// 添加规则
// ...

// 执行规则 (并发执行)
ctx := context.Background()
// 假设 data 已经定义
var data any
// ...
result, err := engine.Execute(ctx, data)
```

### 5. 使用时间条件

```go
import "admin/pkg/rules/condition"
import "admin/pkg/rules/core"
import "admin/pkg/rules/action"

// 检查用户是否在过去24小时内创建 (假设 createTime 是时间字符串或 time.Time)
inLastDayCondition, _ := condition.NewTimeCondition("user.createTime", condition.TimeOpInLast, "24h", false)

// 假设 actions 已经定义
var actions []core.Action = []core.Action{action.NewSetPropertyAction("user.is_new_user", true)}
// ...

rule := core.NewRule(
    "time-rule",
    "检查用户是否是新用户",
    "检查创建时间是否在过去24小时内",
    1,
    inLastDayCondition,
    actions,
    true,
)
```

### 6. 自定义函数

```go
import "admin/pkg/rules/function"
import "admin/pkg/rules/condition"
import "admin/pkg/rules/core"

// 创建函数注册表
registry := function.NewFunctionRegistry()

// 注册函数: 计算折扣
err := registry.RegisterFunction("calculateDiscount", func(price float64, level int) float64 {
    if level == 1 {
        return price * 0.9
    } else if level == 2 {
        return price * 0.8
    }
    return price
}, "计算折扣")
if err != nil {
    // 处理错误
    return
}

// 创建函数条件: 折扣后的价格是否小于100
funcCond, err := condition.NewFunctionCondition(
    "calculateDiscount",
    []string{"product.price", "user.vipLevel"}, // 函数参数对应的路径
    condition.FuncOpLessThan,
    100.0,
    registry,
)
if err != nil {
    // 处理错误
    return
}

// 假设 actions 已经定义
var actions []core.Action
// ...

// 创建规则
rule := core.NewRule("discount-rule", "折扣规则", "根据VIP等级计算折扣价是否小于100", 1, funcCond, actions, true)
```

## 性能考虑

规则引擎设计考虑了性能优化：

1.  **对象池**：使用 [`sync.Pool`](https://pkg.go.dev/sync#Pool) 复用结果对象 ([`core.ActionResult`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go), [`core.CachedCondition`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go))，减少GC压力。
2.  **规则索引**：通过路径和类型索引 ([`core.RuleIndex`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/rule_index.go)) 快速查找可能匹配的规则。
3.  **并发执行**：支持规则并发评估和执行 ([`core.DefaultRuleEngine`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine_impl.go))。
4.  **路径缓存**：路径解析结果 ([`path.PathParserImpl`](/Volumes/data/Code/Go/src/admin/pkg/rules/path/path_parser.go)) 会被 LRU 缓存以提高性能。
5.  **条件优化**：提供多种优化级别 ([`rules.OptimizationLevelAdvanced`](/Volumes/data/Code/Go/src/admin/pkg/rules/engine.go))，包括延迟计算、条件合并和自适应重排序。

对于大量规则场景，请考虑以下建议：

-   设置合理的优先级 ([`core.Rule.GetPriority`](/Volumes/data/Code/Go/src/admin/pkg/rules/core/engine.go))，确保重要规则先执行。
-   使用路径索引优化规则筛选（引擎内部自动处理）。
-   在高并发场景启用并发执行，但要注意数据竞争（确保 Action 实现是并发安全的，或者数据本身支持并发访问）。
-   选择合适的条件优化级别 ([`rules.CreateEngineWithOptimizationLevel`](/Volumes/data/Code/Go/src/admin/pkg/rules/engine.go))。

## 贡献指南

欢迎对本项目进行贡献：

1.  Fork本仓库
2.  创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3.  提交您的更改 (`git commit -m 'Add some amazing feature'`)
4.  推送到分支 (`git push origin feature/amazing-feature`)
5.  开放一个Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](/Volumes/data/Code/Go/src/admin/LICENSE) 文件