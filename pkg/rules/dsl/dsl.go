// Package dsl 提供规则引擎的DSL表达式支持
package dsl

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"admin/pkg/rules/condition"
	"admin/pkg/rules/core"
)

var (
	// 错误定义
	ErrInvalidDSL      = errors.New("无效的DSL表达式")
	ErrInvalidOperator = errors.New("无效的操作符")
	ErrMissingField    = errors.New("缺少必要字段")
)

// ExpressionType DSL表达式类型
type ExpressionType string

const (
	// 表达式类型
	TypeSimple    ExpressionType = "simple"    // 简单条件
	TypeComposite ExpressionType = "composite" // 复合条件
)

// DSLExpression DSL表达式接口
type DSLExpression interface {
	// ToCondition 将DSL表达式转换为条件
	ToCondition() (core.Condition, error)
}

// SimpleExpression 简单条件表达式
type SimpleExpression struct {
	Type     ExpressionType     `json:"type"`     // 类型，必须是"simple"
	Path     string             `json:"path"`     // 要比较的路径
	Operator condition.Operator `json:"operator"` // 操作符
	Value    any                `json:"value"`    // 比较的值
}

// NewSimpleExpression 创建简单条件表达式
func NewSimpleExpression(path string, operator condition.Operator, value interface{}) *SimpleExpression {
	return &SimpleExpression{
		Type:     TypeSimple,
		Path:     path,
		Operator: operator,
		Value:    value,
	}
}

// ToCondition 将简单表达式转换为条件
func (e *SimpleExpression) ToCondition() (core.Condition, error) {
	if e.Path == "" {
		return nil, fmt.Errorf("%w: 'path'", ErrMissingField)
	}

	if e.Operator == "" {
		return nil, fmt.Errorf("%w: 'operator'", ErrMissingField)
	}

	// 创建简单条件
	return condition.NewSimpleCondition(e.Path, e.Operator, e.Value), nil
}

// CompositeExpression 复合条件表达式
type CompositeExpression struct {
	Type           ExpressionType     `json:"type"`        // 类型，必须是"composite"
	Operator       condition.Operator `json:"operator"`    // 操作符 (and, or, not)
	Expressions    []DSLExpression    `json:"-"`           // 子表达式列表（用于Go对象）
	RawExpressions []json.RawMessage  `json:"expressions"` // 原始子表达式（用于JSON解析）
}

// NewCompositeExpression 创建复合条件表达式
func NewCompositeExpression(operator condition.Operator, expressions ...DSLExpression) *CompositeExpression {
	return &CompositeExpression{
		Type:        TypeComposite,
		Operator:    operator,
		Expressions: expressions,
	}
}

// ToCondition 将复合表达式转换为条件
func (e *CompositeExpression) ToCondition() (core.Condition, error) {
	if e.Operator == "" {
		return nil, fmt.Errorf("%w: 'operator'", ErrMissingField)
	}

	if len(e.Expressions) == 0 && len(e.RawExpressions) == 0 {
		return nil, fmt.Errorf("%w: 'expressions'", ErrMissingField)
	}

	// 解析子表达式
	if len(e.Expressions) == 0 && len(e.RawExpressions) > 0 {
		for _, rawExpr := range e.RawExpressions {
			expr, err := ParseDSLExpression(rawExpr)
			if err != nil {
				return nil, err
			}
			e.Expressions = append(e.Expressions, expr)
		}
	}

	// 转换子表达式为条件
	conditions := make([]core.Condition, 0, len(e.Expressions))
	for _, expr := range e.Expressions {
		cond, err := expr.ToCondition()
		if err != nil {
			return nil, err
		}
		conditions = append(conditions, cond)
	}

	// 根据操作符创建复合条件
	switch e.Operator {
	case condition.OpAnd:
		return condition.NewAndCondition(conditions...), nil
	case condition.OpOr:
		return condition.NewOrCondition(conditions...), nil
	case condition.OpNot:
		if len(conditions) != 1 {
			return nil, fmt.Errorf("NOT 操作符只能有一个条件")
		}
		return condition.NewNotCondition(conditions[0]), nil
	default:
		return nil, fmt.Errorf("%w: %s", ErrInvalidOperator, e.Operator)
	}
}

// DSLExpressionWrapper 用于JSON反序列化的包装器
type DSLExpressionWrapper struct {
	Type ExpressionType `json:"type"`
}

// ParseDSLExpression 解析DSL表达式
func ParseDSLExpression(data json.RawMessage) (DSLExpression, error) {
	if len(data) == 0 {
		return nil, ErrInvalidDSL
	}

	// 解析类型
	var wrapper DSLExpressionWrapper
	if err := json.Unmarshal(data, &wrapper); err != nil {
		return nil, fmt.Errorf("解析表达式类型失败: %w", err)
	}

	// 根据类型创建具体表达式
	switch wrapper.Type {
	case TypeSimple:
		var expr SimpleExpression
		if err := json.Unmarshal(data, &expr); err != nil {
			return nil, fmt.Errorf("解析简单表达式失败: %w", err)
		}
		return &expr, nil

	case TypeComposite:
		var expr CompositeExpression
		if err := json.Unmarshal(data, &expr); err != nil {
			return nil, fmt.Errorf("解析复合表达式失败: %w", err)
		}
		return &expr, nil

	default:
		return nil, fmt.Errorf("未知的表达式类型: %s", wrapper.Type)
	}
}

// ParseDSL 解析DSL字符串
func ParseDSL(dslString string) (DSLExpression, error) {
	if strings.TrimSpace(dslString) == "" {
		return nil, ErrInvalidDSL
	}

	var data json.RawMessage
	if err := json.Unmarshal([]byte(dslString), &data); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}

	// 解析DSL表达式
	expr, err := ParseDSLExpression(data)
	if err != nil {
		return nil, err
	}

	// 提前验证表达式正确性，这样可以在ParseDSL阶段就捕获错误
	if expr != nil {
		// 尝试转换为条件，这将触发字段验证
		_, err = expr.ToCondition()
		if err != nil {
			return nil, err
		}
	}

	return expr, nil
}

// DSLCondition 基于DSL表达式的条件
type DSLCondition struct {
	// DSL表达式
	Expression DSLExpression

	// 内部条件（由表达式转换）
	condition core.Condition
}

// NewDSLCondition 创建DSL条件
func NewDSLCondition(expression DSLExpression) (*DSLCondition, error) {
	if expression == nil {
		return nil, errors.New("表达式不能为空")
	}

	// 将表达式转换为条件
	cond, err := expression.ToCondition()
	if err != nil {
		return nil, err
	}

	return &DSLCondition{
		Expression: expression,
		condition:  cond,
	}, nil
}

// Evaluate 评估条件
func (c *DSLCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	if c.condition == nil {
		return false, errors.New("条件未初始化")
	}

	return c.condition.Evaluate(ctx, data)
}

// DSLConditionFromString 从DSL字符串创建条件
func DSLConditionFromString(dslString string) (*DSLCondition, error) {
	expr, err := ParseDSL(dslString)
	if err != nil {
		return nil, err
	}

	return NewDSLCondition(expr)
}
