// Package condition 提供条件优化功能
package condition

import (
	"admin/pkg/rules/core"
)

// AdvancedConditionOptimizer 高级条件优化器
// 集成了延迟计算、条件重排序和条件合并功能
type AdvancedConditionOptimizer struct {
	// 是否启用延迟计算
	enableLazyEvaluation bool

	// 是否启用自适应重排序
	enableAdaptiveReordering bool

	// 是否启用条件合并
	enableConditionMerging bool

	// 是否启用深度比较模式
	deepComparisonEnabled bool

	// 条件合并优化器
	merger *ConditionMerger

	// 统计收集器
	statsCollector *StatsCollector
}

// NewAdvancedConditionOptimizer 创建高级条件优化器
func NewAdvancedConditionOptimizer() *AdvancedConditionOptimizer {
	return &AdvancedConditionOptimizer{
		enableLazyEvaluation:     true,
		enableAdaptiveReordering: true,
		enableConditionMerging:   true,
		deepComparisonEnabled:    false, // 默认不启用深度比较，避免不必要的性能开销
		merger:                   NewConditionMerger(),
		statsCollector:           GetGlobalStatsCollector(),
	}
}

// SetLazyEvaluationEnabled 设置是否启用延迟计算
func (o *AdvancedConditionOptimizer) SetLazyEvaluationEnabled(enabled bool) {
	o.enableLazyEvaluation = enabled
}

// SetAdaptiveReorderingEnabled 设置是否启用自适应重排序
func (o *AdvancedConditionOptimizer) SetAdaptiveReorderingEnabled(enabled bool) {
	o.enableAdaptiveReordering = enabled
}

// SetConditionMergingEnabled 设置是否启用条件合并
func (o *AdvancedConditionOptimizer) SetConditionMergingEnabled(enabled bool) {
	o.enableConditionMerging = enabled
}

// SetStatsCollector 设置统计收集器
func (o *AdvancedConditionOptimizer) SetStatsCollector(collector *StatsCollector) {
	if collector != nil {
		o.statsCollector = collector
	}
}

// SetDeepComparison 设置是否启用深度比较模式
// 深度比较模式会更精确地比较条件，但可能会增加CPU开销
func (o *AdvancedConditionOptimizer) SetDeepComparison(enable bool) *AdvancedConditionOptimizer {
	// 如果条件合并器已初始化，则设置深度比较模式
	if o.merger != nil {
		o.merger.SetDeepComparison(enable)
	}

	// 保存设置以用于延迟初始化
	o.deepComparisonEnabled = enable
	return o
}

// 在创建合并器时应用深度比较设置
func (o *AdvancedConditionOptimizer) initMerger() {
	// 如果合并器未初始化，则创建新实例
	if o.merger == nil {
		o.merger = NewConditionMerger()

		// 应用深度比较设置
		if o.deepComparisonEnabled {
			o.merger.SetDeepComparison(true)
		}
	}
}

// OptimizeRule 优化规则中的条件
// 实现 core.RuleOptimizer 接口
func (o *AdvancedConditionOptimizer) OptimizeRule(rule core.Rule) core.Rule {
	if rule == nil {
		return nil
	}

	// 获取规则原始条件
	originalCondition := rule.GetCondition()
	if originalCondition == nil {
		return rule
	}

	// 优化条件
	optimizedCondition := o.Optimize(originalCondition)

	// 如果规则实现了SetCondition方法，则设置优化后的条件
	if setter, ok := rule.(interface{ SetCondition(core.Condition) }); ok {
		setter.SetCondition(optimizedCondition)
	}

	return rule
}

// OptimizeRules 批量优化规则
// 实现 core.RuleOptimizer 接口
func (o *AdvancedConditionOptimizer) OptimizeRules(rules []core.Rule) []core.Rule {
	if len(rules) == 0 {
		return rules
	}

	optimizedRules := make([]core.Rule, len(rules))
	for i, rule := range rules {
		optimizedRules[i] = o.OptimizeRule(rule)
	}

	return optimizedRules
}

// Optimize 优化条件，应用多种优化策略
func (o *AdvancedConditionOptimizer) Optimize(condition core.Condition) core.Condition {
	if condition == nil {
		return nil
	}

	// 1. 首先应用条件合并优化
	if o.enableConditionMerging {
		// 确保合并器已初始化并应用了正确的设置
		o.initMerger()
		condition = o.merger.MergeCompositeCondition(condition)
	}

	// 2. 然后应用条件重排序或延迟计算
	if o.enableAdaptiveReordering {
		// 优先使用自适应重排序
		condition = o.optimizeWithAdaptiveReordering(condition)
	} else if o.enableLazyEvaluation {
		// 如果不使用自适应重排序，使用延迟计算
		condition = o.optimizeWithLazyEvaluation(condition)
	}

	return condition
}

// optimizeWithLazyEvaluation 使用延迟计算优化条件
func (o *AdvancedConditionOptimizer) optimizeWithLazyEvaluation(condition core.Condition) core.Condition {
	// 如果已经是懒加载条件，直接返回
	if _, ok := condition.(*LazyCondition); ok {
		return condition
	}

	// 如果是LazyCompositeCondition，不需要额外包装
	if _, ok := condition.(*LazyCompositeCondition); ok {
		return condition
	}

	// 处理CompositeCondition，优化为LazyCompositeCondition
	if cc, ok := condition.(*CompositeCondition); ok {
		// 递归优化子条件
		optimizedConditions := make([]core.Condition, len(cc.Conditions))
		for i, subCond := range cc.Conditions {
			optimizedConditions[i] = o.optimizeWithLazyEvaluation(subCond)
		}

		// 根据操作类型创建对应的延迟复合条件
		switch cc.Operator {
		case OpAnd:
			return NewLazyAndCondition(optimizedConditions...)
		case OpOr:
			return NewLazyOrCondition(optimizedConditions...)
		case OpNot:
			if len(optimizedConditions) > 0 {
				return NewLazyNotCondition(optimizedConditions[0])
			}
			// 空NOT条件，返回原始条件
			return cc
		default:
			// 不支持的操作符，返回原始条件
			return cc
		}
	}

	// 其他普通条件，包装为LazyCondition
	return NewLazyCondition(condition)
}

// optimizeWithAdaptiveReordering 使用自适应重排序优化条件
func (o *AdvancedConditionOptimizer) optimizeWithAdaptiveReordering(condition core.Condition) core.Condition {
	// 如果已经是自适应条件，直接返回
	if ac, ok := condition.(*AdaptiveCompositeCondition); ok {
		// 设置统计收集器
		if o.statsCollector != nil {
			ac.SetStatsCollector(o.statsCollector)
		}
		return condition
	}

	// 处理CompositeCondition，转换为AdaptiveCompositeCondition
	if cc, ok := condition.(*CompositeCondition); ok {
		// 递归优化子条件
		optimizedConditions := make([]core.Condition, len(cc.Conditions))
		for i, subCond := range cc.Conditions {
			optimizedConditions[i] = o.optimizeWithAdaptiveReordering(subCond)
		}

		// 根据操作类型创建对应的自适应条件
		var adaptiveCond *AdaptiveCompositeCondition

		switch cc.Operator {
		case OpAnd:
			adaptiveCond = NewAdaptiveAndCondition(optimizedConditions...)
		case OpOr:
			adaptiveCond = NewAdaptiveOrCondition(optimizedConditions...)
		case OpNot:
			if len(optimizedConditions) > 0 {
				adaptiveCond = NewAdaptiveNotCondition(optimizedConditions[0])
			} else {
				// 空NOT条件，使用延迟计算
				return o.optimizeWithLazyEvaluation(cc)
			}
		default:
			// 不支持的操作符，使用延迟计算
			return o.optimizeWithLazyEvaluation(cc)
		}

		// 设置统计收集器
		if adaptiveCond != nil && o.statsCollector != nil {
			adaptiveCond.SetStatsCollector(o.statsCollector)
		}

		return adaptiveCond
	}

	// 对于其他条件，使用延迟计算
	return o.optimizeWithLazyEvaluation(condition)
}
