// Package condition 提供规则条件处理功能
package condition

import (
	"context"
	"fmt"

	"admin/pkg/rules/core"
	"admin/pkg/rules/path"
)

// ArrayIteratorCondition 数组迭代器条件
// 用于迭代数组中的每个元素，并对每个元素应用相同的条件
type ArrayIteratorCondition struct {
	// 数组路径
	arrayPath string

	// 单个元素变量名
	elementVar string

	// 应用于每个元素的条件
	condition core.Condition

	// 路径解析器
	pathParser *path.PathParser
}

// NewArrayIteratorCondition 创建新的数组迭代器条件
func NewArrayIteratorCondition(arrayPath, elementVar string, condition core.Condition) *ArrayIteratorCondition {
	return &ArrayIteratorCondition{
		arrayPath:  arrayPath,
		elementVar: elementVar,
		condition:  condition,
		pathParser: path.NewPathParser(),
	}
}

// Evaluate 评估数组迭代器条件
// 如果数组中任意一个元素满足条件，则返回true
func (c *ArrayIteratorCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	// 从数据中获取数组
	array, err := c.pathParser.GetValue(data, c.arrayPath)
	if err != nil {
		return false, fmt.Errorf("获取数组失败 [%s]: %w", c.arrayPath, err)
	}

	// 检查是否为数组类型
	arraySlice, ok := toSlice(array)
	if !ok {
		return false, fmt.Errorf("路径 [%s] 不是数组类型", c.arrayPath)
	}

	// 如果数组为空，返回false
	if len(arraySlice) == 0 {
		return false, nil
	}

	// 遍历数组中的每个元素
	for _, element := range arraySlice {
		// 创建局部上下文，将当前元素保存到上下文中
		// localContext := createLocalContext(data, c.elementVar, element)

		// 对当前元素应用条件
		result, err := c.condition.Evaluate(ctx, element)
		if err != nil {
			continue // 忽略评估错误，继续检查下一个元素
		}

		// 如果有元素满足条件，立即返回true
		if result {
			return true, nil
		}
	}

	// 如果没有元素满足条件，返回false
	return false, nil
}

// GetType 返回条件类型
func (c *ArrayIteratorCondition) GetType() string {
	return "array_iterator"
}

// 将任意类型转换为切片
func toSlice(data any) ([]any, bool) {
	switch v := data.(type) {
	case []any:
		return v, true
	case []map[string]any:
		result := make([]any, len(v))
		for i, item := range v {
			result[i] = item
		}
		return result, true
	case []string:
		result := make([]any, len(v))
		for i, item := range v {
			result[i] = item
		}
		return result, true
	case []int:
		result := make([]any, len(v))
		for i, item := range v {
			result[i] = item
		}
		return result, true
	case []float64:
		result := make([]any, len(v))
		for i, item := range v {
			result[i] = item
		}
		return result, true
	case []bool:
		result := make([]any, len(v))
		for i, item := range v {
			result[i] = item
		}
		return result, true
	default:
		// 尝试处理其他切片类型
		if slice, ok := asSlice(data); ok {
			return slice, true
		}
		return nil, false
	}
}

// 使用反射处理其他类型的切片
func asSlice(data any) ([]any, bool) {
	// 简单实现，仅处理map切片
	if items, ok := data.([]map[string]any); ok {
		result := make([]any, len(items))
		for i, item := range items {
			result[i] = item
		}
		return result, true
	}

	return nil, false
}
