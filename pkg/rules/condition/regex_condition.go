// Package condition 提供规则引擎条件评估功能
package condition

import (
	"context"
	"errors"
	"fmt"
	"regexp"

	p "admin/pkg/rules/path"
)

// RegexCondition 正则表达式条件
type RegexCondition struct {
	// 基础条件类型
	ConditionType string

	// 要获取的字符串路径
	Path string

	// 正则表达式模式
	Pattern string

	// 预编译的正则表达式
	compiledRegex *regexp.Regexp

	// 是否对匹配结果取反
	Inverse bool

	// 路径解析器
	Parser *p.PathParser
}

// 正则表达式操作类型
const (
	RegexOpMatch    = "match"    // 匹配
	RegexOpNotMatch = "notMatch" // 不匹配
)

// NewRegexCondition 创建一个新的正则表达式条件
func NewRegexCondition(path, pattern string, inverse bool) (*RegexCondition, error) {
	// 预编译正则表达式
	compiledRegex, err := regexp.Compile(pattern)
	if err != nil {
		return nil, err
	}

	return &RegexCondition{
		ConditionType: "regex",
		Path:          path,
		Pattern:       pattern,
		compiledRegex: compiledRegex,
		Inverse:       inverse,
		Parser:        p.NewPathParser(),
	}, nil
}

// Evaluate 评估正则表达式条件
func (c *RegexCondition) Evaluate(ctx context.Context, data interface{}) (bool, error) {
	// 从数据中获取字符串值
	value, err := c.Parser.GetValue(data, c.Path)
	if err != nil {
		return false, err
	}

	// 确保值是字符串
	strValue, ok := c.ensureString(value)
	if !ok {
		return false, errors.New("值不是字符串类型")
	}

	// 执行正则匹配
	matched := c.compiledRegex.MatchString(strValue)

	// 根据是否取反返回结果
	if c.Inverse {
		return !matched, nil
	}
	return matched, nil
}

// ensureString 确保值是字符串类型
func (c *RegexCondition) ensureString(value interface{}) (string, bool) {
	if value == nil {
		return "", false
	}

	switch v := value.(type) {
	case string:
		return v, true
	case []byte:
		return string(v), true
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
		return fmt.Sprintf("%v", v), true
	default:
		return "", false
	}
}

// GetType 获取条件类型
func (c *RegexCondition) GetType() string {
	return c.ConditionType
}

// GetPath 获取路径
func (c *RegexCondition) GetPath() string {
	return c.Path
}

// GetPattern 获取正则表达式模式
func (c *RegexCondition) GetPattern() string {
	return c.Pattern
}

// IsInverse 是否取反
func (c *RegexCondition) IsInverse() bool {
	return c.Inverse
}
