// Package condition 提供条件表达式系统的实现
package condition

import (
	"context"
	"errors"
	"fmt"
	"math"
	"reflect"
	"regexp"
	"strings"
	"sync"

	"admin/pkg/rules/core"
	"admin/pkg/rules/path"
	p "admin/pkg/rules/path"
)

var (
	// 错误定义
	ErrInvalidOperator     = errors.New("无效的操作符")
	ErrIncomparableTypes   = errors.New("类型不可比较")
	ErrInvalidConditionDef = errors.New("无效的条件定义")
)

// Operator 表示条件比较的操作符
type Operator string

// 支持的操作符
const (
	// 比较操作符
	OpEqual              Operator = "eq"           // 等于
	OpNotEqual           Operator = "neq"          // 不等于
	OpGreaterThan        Operator = "gt"           // 大于
	OpGreaterThanOrEqual Operator = "gte"          // 大于等于
	OpLessThan           Operator = "lt"           // 小于
	OpLessThanOrEqual    Operator = "lte"          // 小于等于
	OpIn                 Operator = "in"           // 包含于集合
	OpNotIn              Operator = "not_in"       // 不包含于集合
	OpContains           Operator = "contains"     // 字符串包含
	OpNotContains        Operator = "not_contains" // 字符串不包含
	OpStartsWith         Operator = "starts_with"  // 字符串开头
	OpEndsWith           Operator = "ends_with"    // 字符串结尾
	OpMatches            Operator = "matches"      // 正则匹配
	OpExists             Operator = "exists"       // 路径存在
	OpEmpty              Operator = "empty"        // 值为空
	OpNotEmpty           Operator = "not_empty"    // 值不为空

	// 逻辑操作符 (用于组合条件)
	OpAnd Operator = "and" // 逻辑与
	OpOr  Operator = "or"  // 逻辑或
	OpNot Operator = "not" // 逻辑非
)

// SimpleCondition 表示一个简单的条件比较
type SimpleCondition struct {
	// 要比较的路径
	Path string `json:"path"`

	// 操作符
	Operator Operator `json:"operator"`

	// 比较的值
	Value any `json:"value"`

	// 路径解析器
	parser *p.PathParser
}

// NewSimpleCondition 创建一个新的简单条件
func NewSimpleCondition(path string, operator Operator, value any) *SimpleCondition {
	return &SimpleCondition{
		Path:     path,
		Operator: operator,
		Value:    value,
		parser:   p.NewPathParser(),
	}
}

// Evaluate 评估条件是否满足
func (c *SimpleCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	// 特殊处理exists操作符，只需检查路径是否存在
	if c.Operator == OpExists {
		_, err := c.parser.GetValue(data, c.Path)
		return err == nil, nil
	}

	// 特殊处理empty和not_empty操作符
	if c.Operator == OpEmpty || c.Operator == OpNotEmpty {
		value, err := c.parser.GetValue(data, c.Path)
		if err != nil {
			// 如果路径不存在，对于empty操作符返回true，对于not_empty返回false
			if errors.Is(err, p.ErrPathNotFound) {
				return c.Operator == OpEmpty, nil
			}
			return false, err
		}

		isEmpty := isEmptyValue(value)
		return c.Operator == OpEmpty && isEmpty || c.Operator == OpNotEmpty && !isEmpty, nil
	}

	// 获取路径对应的值
	actualValue, err := c.parser.GetValue(data, c.Path)
	if err != nil {
		// 路径不存在视为条件不满足
		if errors.Is(err, path.ErrPathNotFound) {
			return false, nil
		}
		return false, err
	}

	// 执行比较操作
	return c.compare(actualValue, c.Value, c.Operator)
}

// compare 比较两个值
func (c *SimpleCondition) compare(actual, expected any, operator Operator) (bool, error) {
	switch operator {
	case OpEqual:
		return compareEqual(actual, expected)
	case OpNotEqual:
		equal, err := compareEqual(actual, expected)
		return !equal, err
	case OpGreaterThan:
		return compareGreaterThan(actual, expected)
	case OpGreaterThanOrEqual:
		return compareGreaterThanOrEqual(actual, expected)
	case OpLessThan:
		return compareLessThan(actual, expected)
	case OpLessThanOrEqual:
		return compareLessThanOrEqual(actual, expected)
	case OpIn:
		return compareIn(actual, expected)
	case OpNotIn:
		in, err := compareIn(actual, expected)
		return !in, err
	case OpContains:
		return compareContains(actual, expected)
	case OpNotContains:
		contains, err := compareContains(actual, expected)
		return !contains, err
	case OpStartsWith:
		return compareStartsWith(actual, expected)
	case OpEndsWith:
		return compareEndsWith(actual, expected)
	case OpMatches:
		return compareMatches(actual, expected)
	default:
		return false, fmt.Errorf("%w: %s", ErrInvalidOperator, operator)
	}
}

// CompositeCondition 复合条件，用于组合多个条件
type CompositeCondition struct {
	// 操作符 (and, or, not)
	Operator Operator `json:"operator"`

	// 子条件列表
	Conditions []core.Condition `json:"conditions"`
}

// NewAndCondition 创建一个AND复合条件
func NewAndCondition(conditions ...core.Condition) *CompositeCondition {
	return &CompositeCondition{
		Operator:   OpAnd,
		Conditions: conditions,
	}
}

// NewOrCondition 创建一个OR复合条件
func NewOrCondition(conditions ...core.Condition) *CompositeCondition {
	return &CompositeCondition{
		Operator:   OpOr,
		Conditions: conditions,
	}
}

// NewNotCondition 创建一个NOT复合条件
func NewNotCondition(condition core.Condition) *CompositeCondition {
	return &CompositeCondition{
		Operator:   OpNot,
		Conditions: []core.Condition{condition},
	}
}

// Evaluate 评估复合条件
func (c *CompositeCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	switch c.Operator {
	case OpAnd:
		return c.evaluateAnd(ctx, data)
	case OpOr:
		return c.evaluateOr(ctx, data)
	case OpNot:
		return c.evaluateNot(ctx, data)
	default:
		return false, fmt.Errorf("%w: %s", ErrInvalidOperator, c.Operator)
	}
}

// evaluateAnd 评估AND条件
func (c *CompositeCondition) evaluateAnd(ctx context.Context, data any) (bool, error) {
	if len(c.Conditions) == 0 {
		return true, nil
	}

	for _, condition := range c.Conditions {
		result, err := condition.Evaluate(ctx, data)
		if err != nil {
			return false, err
		}

		if !result {
			// 短路求值，一个条件不满足，整个AND表达式不满足
			return false, nil
		}
	}

	return true, nil
}

// evaluateOr 评估OR条件
func (c *CompositeCondition) evaluateOr(ctx context.Context, data any) (bool, error) {
	if len(c.Conditions) == 0 {
		return false, nil
	}

	for _, condition := range c.Conditions {
		result, err := condition.Evaluate(ctx, data)
		if err != nil {
			return false, err
		}

		if result {
			// 短路求值，一个条件满足，整个OR表达式满足
			return true, nil
		}
	}

	return false, nil
}

// evaluateNot 评估NOT条件
func (c *CompositeCondition) evaluateNot(ctx context.Context, data any) (bool, error) {
	if len(c.Conditions) != 1 {
		return false, fmt.Errorf("%w: NOT 操作符只能有一个条件", ErrInvalidConditionDef)
	}

	result, err := c.Conditions[0].Evaluate(ctx, data)
	if err != nil {
		return false, err
	}

	return !result, nil
}

// 以下是各种比较操作的辅助函数

// compareEqual 比较两个值是否相等
func compareEqual(a, b any) (bool, error) {
	// 处理nil值
	if a == nil && b == nil {
		return true, nil
	}
	if a == nil || b == nil {
		return false, nil
	}

	// 尝试直接比较（Go的反射会处理很多情况）
	if reflect.DeepEqual(a, b) {
		return true, nil
	}

	// 尝试数字类型的比较
	aFloat, bFloat, err := toComparableNumbers(a, b)
	if err == nil {
		return math.Abs(aFloat-bFloat) < 1e-9, nil // 浮点数比较考虑精度
	}

	// 尝试字符串比较
	aStr, bStr, err := toComparableStrings(a, b)
	if err == nil {
		return aStr == bStr, nil
	}

	// 使用反射进行最后尝试
	aValue := reflect.ValueOf(a)
	bValue := reflect.ValueOf(b)

	// 特殊处理数组、切片
	if (aValue.Kind() == reflect.Slice || aValue.Kind() == reflect.Array) &&
		(bValue.Kind() == reflect.Slice || bValue.Kind() == reflect.Array) {
		if aValue.Len() != bValue.Len() {
			return false, nil
		}

		for i := 0; i < aValue.Len(); i++ {
			elemEqual, _ := compareEqual(aValue.Index(i).Interface(), bValue.Index(i).Interface())
			if !elemEqual {
				return false, nil
			}
		}
		return true, nil
	}

	// 最终返回深度比较结果
	return reflect.DeepEqual(a, b), nil
}

// 比较函数缓存
var (
	numberCache sync.Map // 缓存类型->转换结果
)

// toComparableNumbers 尝试将两个值转换为可比较的浮点数，加入缓存优化
func toComparableNumbers(a, b any) (float64, float64, error) {
	// 检查类型
	aType := reflect.TypeOf(a)
	bType := reflect.TypeOf(b)

	// 尝试从缓存获取a的转换结果
	var aOk bool
	var aVal float64
	if aCache, exists := numberCache.Load(aType); exists {
		if converterFunc, ok := aCache.(func(any) (float64, bool)); ok {
			aVal, aOk = converterFunc(a)
		}
	} else {
		aVal, aOk = valueToFloat64(a)
		// 创建并缓存转换函数
		if aOk {
			converterFunc := createNumberConverter(aType)
			numberCache.Store(aType, converterFunc)
		}
	}

	// 尝试从缓存获取b的转换结果
	var bOk bool
	var bVal float64
	if bCache, exists := numberCache.Load(bType); exists {
		if converterFunc, ok := bCache.(func(any) (float64, bool)); ok {
			bVal, bOk = converterFunc(b)
		}
	} else {
		bVal, bOk = valueToFloat64(b)
		// 创建并缓存转换函数
		if bOk {
			converterFunc := createNumberConverter(bType)
			numberCache.Store(bType, converterFunc)
		}
	}

	if !aOk || !bOk {
		return 0, 0, ErrIncomparableTypes
	}

	return aVal, bVal, nil
}

// createNumberConverter 根据类型创建专用的转换函数，避免反射开销
func createNumberConverter(t reflect.Type) func(any) (float64, bool) {
	switch t.Kind() {
	case reflect.Int:
		return func(v any) (float64, bool) {
			return float64(v.(int)), true
		}
	case reflect.Int8:
		return func(v any) (float64, bool) {
			return float64(v.(int8)), true
		}
	case reflect.Int16:
		return func(v any) (float64, bool) {
			return float64(v.(int16)), true
		}
	case reflect.Int32:
		return func(v any) (float64, bool) {
			return float64(v.(int32)), true
		}
	case reflect.Int64:
		return func(v any) (float64, bool) {
			return float64(v.(int64)), true
		}
	case reflect.Uint:
		return func(v any) (float64, bool) {
			return float64(v.(uint)), true
		}
	case reflect.Uint8:
		return func(v any) (float64, bool) {
			return float64(v.(uint8)), true
		}
	case reflect.Uint16:
		return func(v any) (float64, bool) {
			return float64(v.(uint16)), true
		}
	case reflect.Uint32:
		return func(v any) (float64, bool) {
			return float64(v.(uint32)), true
		}
	case reflect.Uint64:
		return func(v any) (float64, bool) {
			return float64(v.(uint64)), true
		}
	case reflect.Float32:
		return func(v any) (float64, bool) {
			return float64(v.(float32)), true
		}
	case reflect.Float64:
		return func(v any) (float64, bool) {
			return v.(float64), true
		}
	default:
		// 对于不支持的类型，返回使用反射的通用转换器
		return func(v any) (float64, bool) {
			return valueToFloat64(v)
		}
	}
}

// compareGreaterThan 比较a是否大于b
func compareGreaterThan(a, b any) (bool, error) {
	// 先尝试数值比较
	aFloat, bFloat, err := toComparableNumbers(a, b)
	if err == nil {
		return aFloat > bFloat, nil
	}

	// 尝试字符串比较
	aStr, bStr, err := toComparableStrings(a, b)
	if err == nil {
		return aStr > bStr, nil
	}

	return false, ErrIncomparableTypes
}

// compareGreaterThanOrEqual 比较a是否大于等于b
func compareGreaterThanOrEqual(a, b any) (bool, error) {
	// 先尝试等于比较
	if equal, _ := compareEqual(a, b); equal {
		return true, nil
	}

	return compareGreaterThan(a, b)
}

// compareLessThan 比较a是否小于b
func compareLessThan(a, b any) (bool, error) {
	// 先尝试数值比较
	aFloat, bFloat, err := toComparableNumbers(a, b)
	if err == nil {
		return aFloat < bFloat, nil
	}

	// 尝试字符串比较
	aStr, bStr, err := toComparableStrings(a, b)
	if err == nil {
		return aStr < bStr, nil
	}

	return false, ErrIncomparableTypes
}

// compareLessThanOrEqual 比较a是否小于等于b
func compareLessThanOrEqual(a, b any) (bool, error) {
	// 先尝试等于比较
	if equal, _ := compareEqual(a, b); equal {
		return true, nil
	}

	return compareLessThan(a, b)
}

// compareIn 检查a是否在b集合中
func compareIn(a, b any) (bool, error) {
	// b必须是数组或切片
	bValue := reflect.ValueOf(b)
	if bValue.Kind() != reflect.Array && bValue.Kind() != reflect.Slice {
		return false, fmt.Errorf("in 操作符右侧必须是数组或切片")
	}

	// 遍历数组/切片检查是否包含a
	for i := 0; i < bValue.Len(); i++ {
		item := bValue.Index(i).Interface()
		if equal, _ := compareEqual(a, item); equal {
			return true, nil
		}
	}

	return false, nil
}

// compareContains 检查字符串a是否包含b
func compareContains(a, b any) (bool, error) {
	aStr, bStr, err := toComparableStrings(a, b)
	if err != nil {
		return false, err
	}

	return strings.Contains(aStr, bStr), nil
}

// compareStartsWith 检查字符串a是否以b开头
func compareStartsWith(a, b any) (bool, error) {
	aStr, bStr, err := toComparableStrings(a, b)
	if err != nil {
		return false, err
	}

	return strings.HasPrefix(aStr, bStr), nil
}

// compareEndsWith 检查字符串a是否以b结尾
func compareEndsWith(a, b any) (bool, error) {
	aStr, bStr, err := toComparableStrings(a, b)
	if err != nil {
		return false, err
	}

	return strings.HasSuffix(aStr, bStr), nil
}

// compareMatches 使用正则表达式匹配
func compareMatches(a, b any) (bool, error) {
	aStr, bStr, err := toComparableStrings(a, b)
	if err != nil {
		return false, err
	}

	// 编译正则表达式
	re, err := regexp.Compile(bStr)
	if err != nil {
		return false, fmt.Errorf("正则表达式编译错误: %w", err)
	}

	return re.MatchString(aStr), nil
}

// toComparableStrings 尝试将两个值转换为可比较的字符串
func toComparableStrings(a, b any) (string, string, error) {
	aStr, aOk := a.(string)
	if !aOk {
		// 尝试将a转成字符串
		aStr = fmt.Sprint(a)
	}

	bStr, bOk := b.(string)
	if !bOk {
		// 尝试将b转成字符串
		bStr = fmt.Sprint(b)
	}

	return aStr, bStr, nil
}

// toFloat64 尝试将值转换为float64
func valueToFloat64(v any) (float64, bool) {
	switch val := v.(type) {
	case int:
		return float64(val), true
	case int8:
		return float64(val), true
	case int16:
		return float64(val), true
	case int32:
		return float64(val), true
	case int64:
		return float64(val), true
	case uint:
		return float64(val), true
	case uint8:
		return float64(val), true
	case uint16:
		return float64(val), true
	case uint32:
		return float64(val), true
	case uint64:
		return float64(val), true
	case float32:
		return float64(val), true
	case float64:
		return val, true
	default:
		// 尝试使用反射转换
		rv := reflect.ValueOf(v)
		switch rv.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return float64(rv.Int()), true
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return float64(rv.Uint()), true
		case reflect.Float32, reflect.Float64:
			return rv.Float(), true
		default:
			return 0, false
		}
	}
}

// isEmptyValue 检查一个值是否为空(nil, 空字符串, 空数组, 空切片, 空map等)
func isEmptyValue(v any) bool {
	if v == nil {
		return true
	}

	rv := reflect.ValueOf(v)

	switch rv.Kind() {
	case reflect.String:
		return rv.Len() == 0
	case reflect.Array, reflect.Slice, reflect.Map:
		return rv.Len() == 0
	case reflect.Ptr:
		return rv.IsNil()
	case reflect.Interface:
		return rv.IsNil()
	case reflect.Bool:
		return !rv.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return rv.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return rv.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return rv.Float() == 0
	case reflect.Struct:
		// 结构体不认为是空的
		return false
	default:
		return false
	}
}
