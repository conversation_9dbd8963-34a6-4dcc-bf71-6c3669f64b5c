// Package condition 提供条件表达式系统的实现
package condition

import (
	"context"
	"fmt"
	"sync"

	"admin/pkg/rules/core"
)

// LazyCondition 延迟计算条件，实现短路逻辑
type LazyCondition struct {
	// 内部包装的实际条件
	innerCondition core.Condition

	// 缓存结果，避免重复计算
	cachedResult *bool
	cachedErr    error
	once         sync.Once
}

// NewLazyCondition 创建一个新的延迟计算条件
func NewLazyCondition(condition core.Condition) *LazyCondition {
	return &LazyCondition{
		innerCondition: condition,
	}
}

// Evaluate 延迟评估条件，仅在需要时执行，并缓存结果
func (c *LazyCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	if c.innerCondition == nil {
		return false, fmt.Errorf("内部条件为空")
	}

	// 每次调用时都重新评估，确保数据变化时能够获取最新结果
	result, err := c.innerCondition.Evaluate(ctx, data)

	// 更新缓存
	c.cachedResult = &result
	c.cachedErr = err

	if err != nil {
		return false, err
	}
	return result, nil
}

// GetType 获取条件类型
func (c *LazyCondition) GetType() string {
	if typer, ok := c.innerCondition.(interface{ GetType() string }); ok {
		return typer.GetType()
	}
	return "lazy"
}

// GetPaths 获取条件中使用的路径
func (c *LazyCondition) GetPaths() []string {
	if pathsProvider, ok := c.innerCondition.(interface{ GetPaths() []string }); ok {
		return pathsProvider.GetPaths()
	}
	return nil
}

// Reset 重置缓存，允许重新计算
func (c *LazyCondition) Reset() {
	c.once = sync.Once{}
	c.cachedResult = nil
	c.cachedErr = nil
}

// LazyCompositeCondition 延迟计算复合条件，实现短路逻辑和延迟计算
type LazyCompositeCondition struct {
	// 操作符 (and, or, not)
	Operator Operator

	// 子条件列表
	Conditions []core.Condition

	// 上下文数据
	ctx  context.Context
	data any
}

// NewLazyAndCondition 创建一个具有短路逻辑的AND复合条件
func NewLazyAndCondition(conditions ...core.Condition) *LazyCompositeCondition {
	return &LazyCompositeCondition{
		Operator:   OpAnd,
		Conditions: conditions,
	}
}

// NewLazyOrCondition 创建一个具有短路逻辑的OR复合条件
func NewLazyOrCondition(conditions ...core.Condition) *LazyCompositeCondition {
	return &LazyCompositeCondition{
		Operator:   OpOr,
		Conditions: conditions,
	}
}

// NewLazyNotCondition 创建一个延迟计算的NOT复合条件
func NewLazyNotCondition(condition core.Condition) *LazyCompositeCondition {
	return &LazyCompositeCondition{
		Operator:   OpNot,
		Conditions: []core.Condition{condition},
	}
}

// Evaluate 评估复合条件，使用短路逻辑避免不必要的计算
func (c *LazyCompositeCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	c.ctx = ctx
	c.data = data

	switch c.Operator {
	case OpAnd:
		return c.evaluateLazyAnd()
	case OpOr:
		return c.evaluateLazyOr()
	case OpNot:
		return c.evaluateLazyNot()
	default:
		return false, fmt.Errorf("%w: %s", ErrInvalidOperator, c.Operator)
	}
}

// evaluateLazyAnd 使用短路逻辑评估AND条件
// 一旦有一个条件为false，立即返回false而不计算后续条件
func (c *LazyCompositeCondition) evaluateLazyAnd() (bool, error) {
	if len(c.Conditions) == 0 {
		return true, nil // 空条件集合默认为true
	}

	for i, condition := range c.Conditions {
		// 转换为懒加载条件
		lazyCondition := c.wrapLazy(condition)

		result, err := lazyCondition.Evaluate(c.ctx, c.data)
		if err != nil {
			return false, fmt.Errorf("条件 #%d 评估失败: %w", i+1, err)
		}

		// 短路逻辑：一旦有false，立即返回
		if !result {
			return false, nil
		}
	}

	return true, nil
}

// evaluateLazyOr 使用短路逻辑评估OR条件
// 一旦有一个条件为true，立即返回true而不计算后续条件
func (c *LazyCompositeCondition) evaluateLazyOr() (bool, error) {
	if len(c.Conditions) == 0 {
		return false, nil // 空条件集合默认为false
	}

	for i, condition := range c.Conditions {
		// 转换为懒加载条件
		lazyCondition := c.wrapLazy(condition)

		result, err := lazyCondition.Evaluate(c.ctx, c.data)
		if err != nil {
			return false, fmt.Errorf("条件 #%d 评估失败: %w", i+1, err)
		}

		// 短路逻辑：一旦有true，立即返回
		if result {
			return true, nil
		}
	}

	return false, nil
}

// evaluateLazyNot 评估NOT条件
func (c *LazyCompositeCondition) evaluateLazyNot() (bool, error) {
	if len(c.Conditions) != 1 {
		return false, fmt.Errorf("%w: NOT 操作符只能有一个条件", ErrInvalidConditionDef)
	}

	// 包装为懒加载条件
	lazyCondition := c.wrapLazy(c.Conditions[0])

	result, err := lazyCondition.Evaluate(c.ctx, c.data)
	if err != nil {
		return false, err
	}

	return !result, nil
}

// wrapLazy 如果条件不是LazyCondition，包装为LazyCondition
func (c *LazyCompositeCondition) wrapLazy(condition core.Condition) core.Condition {
	// 如果已经是LazyCondition，直接返回
	if _, ok := condition.(*LazyCondition); ok {
		return condition
	}
	// 包装为LazyCondition
	return NewLazyCondition(condition)
}

// GetType 获取条件类型
func (c *LazyCompositeCondition) GetType() string {
	return "lazy_composite"
}

// GetConditions 获取所有子条件
func (c *LazyCompositeCondition) GetConditions() []core.Condition {
	return c.Conditions
}

// GetPaths 获取所有子条件使用的路径
func (c *LazyCompositeCondition) GetPaths() []string {
	pathMap := make(map[string]struct{})

	for _, cond := range c.Conditions {
		// 尝试获取路径
		if pathsProvider, ok := cond.(interface{ GetPaths() []string }); ok {
			for _, path := range pathsProvider.GetPaths() {
				if path != "" {
					pathMap[path] = struct{}{}
				}
			}
		}
	}

	// 转换为切片
	paths := make([]string, 0, len(pathMap))
	for path := range pathMap {
		paths = append(paths, path)
	}

	return paths
}
