// Package condition 提供条件优化功能
package condition

import (
	"admin/pkg/rules/core"
)

// ConditionOptimizer 条件优化器
// 自动将条件转换为延迟计算条件，提高规则评估性能
type ConditionOptimizer struct{}

// NewConditionOptimizer 创建条件优化器
func NewConditionOptimizer() *ConditionOptimizer {
	return &ConditionOptimizer{}
}

// OptimizeRule 优化规则中的条件
// 将规则中的条件转换为延迟计算条件
// 实现core.RuleOptimizer接口
func (o *ConditionOptimizer) OptimizeRule(rule core.Rule) core.Rule {
	if rule == nil {
		return nil
	}

	// 获取规则原始条件
	originalCondition := rule.GetCondition()
	if originalCondition == nil {
		return rule
	}

	// 优化条件
	optimizedCondition := o.Optimize(originalCondition)

	// 如果规则实现了SetCondition方法，则设置优化后的条件
	if setter, ok := rule.(interface{ SetCondition(core.Condition) }); ok {
		setter.SetCondition(optimizedCondition)
	}

	return rule
}

// OptimizeRules 批量优化规则
// 实现core.RuleOptimizer接口
func (o *ConditionOptimizer) OptimizeRules(rules []core.Rule) []core.Rule {
	if len(rules) == 0 {
		return rules
	}

	optimizedRules := make([]core.Rule, len(rules))
	for i, rule := range rules {
		optimizedRules[i] = o.OptimizeRule(rule)
	}

	return optimizedRules
}

// Optimize 优化条件，转换为延迟计算条件
// 针对复合条件，会递归优化子条件
func (o *ConditionOptimizer) Optimize(condition core.Condition) core.Condition {
	if condition == nil {
		return nil
	}

	// 如果已经是懒加载条件，直接返回
	if _, ok := condition.(*LazyCondition); ok {
		return condition
	}

	// 如果是LazyCompositeCondition，不需要额外包装
	if _, ok := condition.(*LazyCompositeCondition); ok {
		return condition
	}

	// 处理CompositeCondition，优化为LazyCompositeCondition
	if cc, ok := condition.(*CompositeCondition); ok {
		return o.optimizeComposite(cc)
	}

	// 其他普通条件，包装为LazyCondition
	return NewLazyCondition(condition)
}

// optimizeComposite 优化复合条件
func (o *ConditionOptimizer) optimizeComposite(condition *CompositeCondition) core.Condition {
	if condition == nil {
		return nil
	}

	// 递归优化所有子条件
	optimizedConditions := make([]core.Condition, 0, len(condition.Conditions))
	for _, subCond := range condition.Conditions {
		optimizedConditions = append(optimizedConditions, o.Optimize(subCond))
	}

	// 根据操作类型创建对应的延迟复合条件
	switch condition.Operator {
	case OpAnd:
		return NewLazyAndCondition(optimizedConditions...)
	case OpOr:
		return NewLazyOrCondition(optimizedConditions...)
	case OpNot:
		if len(optimizedConditions) > 0 {
			return NewLazyNotCondition(optimizedConditions[0])
		}
		// 空NOT条件，返回原始条件
		return condition
	default:
		// 不支持的操作符，返回原始条件
		return condition
	}
}
