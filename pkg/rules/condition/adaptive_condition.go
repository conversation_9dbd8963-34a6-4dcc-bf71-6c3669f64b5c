// Package condition 提供条件优化与重排序功能
package condition

import (
	"context"
	"fmt"
	"sync"
	"time"

	"admin/pkg/rules/core"
)

// AdaptiveCompositeCondition 自适应复合条件
// 根据运行时收集的统计信息动态调整子条件的执行顺序
// 优化短路逻辑，提高评估效率
type AdaptiveCompositeCondition struct {
	// 条件唯一标识
	ID string

	// 条件操作符
	Operator Operator

	// 子条件列表
	Conditions []core.Condition

	// 最后一次重排序时间
	lastReorderTime time.Time

	// 重排序间隔
	reorderInterval time.Duration

	// 保护排序操作的互斥锁
	mu sync.RWMutex

	// 统计收集器
	statsCollector *StatsCollector

	// 是否已初始化
	initialized bool
}

// NewAdaptiveCompositeCondition 创建自适应复合条件
func NewAdaptiveCompositeCondition(operator Operator, conditions ...core.Condition) *AdaptiveCompositeCondition {
	// 生成唯一ID
	id := fmt.Sprintf("adaptive_%s_%p", operator, &conditions)

	return &AdaptiveCompositeCondition{
		ID:              id,
		Operator:        operator,
		Conditions:      conditions,
		lastReorderTime: time.Now().Add(-24 * time.Hour), // 设置为过去时间，确保第一次评估就进行排序
		reorderInterval: 5 * time.Minute,                 // 默认5分钟重排序一次
		statsCollector:  GetGlobalStatsCollector(),
	}
}

// NewAdaptiveAndCondition 创建自适应AND条件
func NewAdaptiveAndCondition(conditions ...core.Condition) *AdaptiveCompositeCondition {
	return NewAdaptiveCompositeCondition(OpAnd, conditions...)
}

// NewAdaptiveOrCondition 创建自适应OR条件
func NewAdaptiveOrCondition(conditions ...core.Condition) *AdaptiveCompositeCondition {
	return NewAdaptiveCompositeCondition(OpOr, conditions...)
}

// NewAdaptiveNotCondition 创建自适应NOT条件
func NewAdaptiveNotCondition(condition core.Condition) *AdaptiveCompositeCondition {
	return NewAdaptiveCompositeCondition(OpNot, condition)
}

// SetReorderInterval 设置条件重排序间隔
func (c *AdaptiveCompositeCondition) SetReorderInterval(interval time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.reorderInterval = interval
	// 重置上次重排序时间，强制下次评估时重排序
	c.lastReorderTime = time.Now().Add(-2 * interval)
}

// SetStatsCollector 设置统计收集器
func (c *AdaptiveCompositeCondition) SetStatsCollector(collector *StatsCollector) {
	c.mu.Lock()
	defer c.mu.Unlock()
	if collector != nil {
		c.statsCollector = collector
	}
}

// GetID 获取条件ID
func (c *AdaptiveCompositeCondition) GetID() string {
	return c.ID
}

// GetConditions 获取子条件列表（实现接口方法）
func (c *AdaptiveCompositeCondition) GetConditions() []core.Condition {
	c.mu.RLock()
	defer c.mu.RUnlock()
	result := make([]core.Condition, len(c.Conditions))
	copy(result, c.Conditions)
	return result
}

// GetType 获取条件类型
func (c *AdaptiveCompositeCondition) GetType() string {
	return "adaptive_composite"
}

// ForceReorder 强制重新排序
func (c *AdaptiveCompositeCondition) ForceReorder() {
	c.mu.Lock()
	c.lastReorderTime = time.Now().Add(-24 * time.Hour)
	c.mu.Unlock()
	c.reorderConditions()
}

// Evaluate 评估条件
func (c *AdaptiveCompositeCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	// 确保初始化
	if !c.initialized {
		c.initialize()
	}

	// 检查是否需要重排序
	c.checkAndReorder()

	// 记录开始时间
	startTime := time.Now()

	// 获取子条件的只读副本，避免并发访问问题
	c.mu.RLock()
	conditions := make([]core.Condition, len(c.Conditions))
	copy(conditions, c.Conditions)
	operator := c.Operator
	conditionIDs := make([]string, len(conditions))
	for i, cond := range conditions {
		// 尝试获取条件ID
		if idGetter, ok := cond.(interface{ GetID() string }); ok {
			conditionIDs[i] = idGetter.GetID()
		} else {
			// 如果条件没有ID，使用默认ID
			conditionIDs[i] = fmt.Sprintf("%s_subcond_%d", c.ID, i)
		}
	}
	c.mu.RUnlock()

	// 计算结果
	result, err := c.evaluateWithStats(ctx, data, operator, conditions, conditionIDs)

	// 记录总体评估统计信息
	if c.statsCollector != nil && c.statsCollector.IsEnabled() {
		duration := time.Since(startTime)
		c.statsCollector.RecordEvaluation(c.ID, result, duration)
	}

	return result, err
}

// initialize 执行初始化
func (c *AdaptiveCompositeCondition) initialize() {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.initialized {
		return
	}

	if c.statsCollector == nil {
		c.statsCollector = GetGlobalStatsCollector()
	}

	c.initialized = true
}

// checkAndReorder 检查是否需要重排序并执行
func (c *AdaptiveCompositeCondition) checkAndReorder() {
	c.mu.RLock()
	interval := c.reorderInterval
	lastTime := c.lastReorderTime
	c.mu.RUnlock()

	// 如果距离上次重排序还没有达到间隔时间，则跳过
	if time.Since(lastTime) < interval {
		return
	}

	// 执行重排序
	c.reorderConditions()
}

// reorderConditions 重排序子条件
func (c *AdaptiveCompositeCondition) reorderConditions() {
	// 获取写锁
	c.mu.Lock()
	defer c.mu.Unlock()

	// 设置最后重排序时间
	c.lastReorderTime = time.Now()

	// 如果条件数量过少，不需要排序
	if len(c.Conditions) <= 1 {
		return
	}

	// 收集条件信息
	type conditionInfo struct {
		index     int
		condition core.Condition
		score     float64
		falseProb float64
		trueProb  float64
		cost      int64
	}

	conditions := make([]conditionInfo, len(c.Conditions))
	for i, cond := range c.Conditions {
		conditions[i] = conditionInfo{
			index:     i,
			condition: cond,
			score:     0,
			falseProb: 0.5,  // 默认50%概率
			trueProb:  0.5,  // 默认50%概率
			cost:      1000, // 默认成本
		}

		// 尝试获取条件ID
		var condID string
		if idGetter, ok := cond.(interface{ GetID() string }); ok {
			condID = idGetter.GetID()
		} else {
			// 如果条件没有ID，使用默认ID
			condID = fmt.Sprintf("%s_subcond_%d", c.ID, i)
		}

		// 获取统计信息
		if c.statsCollector != nil {
			stat, found := c.statsCollector.GetConditionCost(condID)
			if found && stat.EvaluationCount >= 1 {
				conditions[i].trueProb = stat.TrueProbability
				conditions[i].falseProb = 1.0 - stat.TrueProbability
				conditions[i].cost = stat.TimeCost
				if conditions[i].cost <= 0 {
					conditions[i].cost = 1 // 避免除零
				}
			}
		}

		// 计算优先级得分
		switch c.Operator {
		case OpAnd:
			// 对于AND条件，更可能为false的条件得分更高
			conditions[i].score = conditions[i].falseProb / float64(conditions[i].cost)
		case OpOr:
			// 对于OR条件，更可能为true的条件得分更高
			conditions[i].score = conditions[i].trueProb / float64(conditions[i].cost)
		}
	}

	// 按得分排序（从高到低）
	reordered := false
	for i := 0; i < len(conditions); i++ {
		for j := i + 1; j < len(conditions); j++ {
			if conditions[i].score < conditions[j].score {
				conditions[i], conditions[j] = conditions[j], conditions[i]
				reordered = true
			}
		}
	}

	// 如果排序有变化，应用新顺序
	if reordered {
		newConditions := make([]core.Condition, len(conditions))
		for i, info := range conditions {
			newConditions[i] = info.condition
		}
		c.Conditions = newConditions
	}
}

// evaluateWithStats 评估条件并收集统计信息
func (c *AdaptiveCompositeCondition) evaluateWithStats(ctx context.Context, data any, operator Operator, conditions []core.Condition, condIDs []string) (bool, error) {
	switch operator {
	case OpAnd:
		// 所有条件必须为true
		for i, condition := range conditions {
			if i >= len(condIDs) {
				break // 安全检查
			}

			startTime := time.Now()
			result, err := condition.Evaluate(ctx, data)
			duration := time.Since(startTime)

			// 记录子条件的统计信息
			if c.statsCollector != nil && c.statsCollector.IsEnabled() {
				c.statsCollector.RecordEvaluation(condIDs[i], result, duration)
			}

			if err != nil {
				return false, err
			}

			// 短路逻辑：一旦有一个条件为false，立即返回false
			if !result {
				return false, nil
			}
		}
		return true, nil

	case OpOr:
		// 任一条件为true即可
		for i, condition := range conditions {
			if i >= len(condIDs) {
				break // 安全检查
			}

			startTime := time.Now()
			result, err := condition.Evaluate(ctx, data)
			duration := time.Since(startTime)

			// 记录子条件的统计信息
			if c.statsCollector != nil && c.statsCollector.IsEnabled() {
				c.statsCollector.RecordEvaluation(condIDs[i], result, duration)
			}

			if err != nil {
				return false, err
			}

			// 短路逻辑：一旦有一个条件为true，立即返回true
			if result {
				return true, nil
			}
		}
		return false, nil

	case OpNot:
		// 取反操作
		if len(conditions) == 0 {
			return false, fmt.Errorf("NOT条件必须有一个子条件")
		}

		// 只评估第一个条件
		startTime := time.Now()
		result, err := conditions[0].Evaluate(ctx, data)
		duration := time.Since(startTime)

		// 记录子条件的统计信息
		if c.statsCollector != nil && c.statsCollector.IsEnabled() && len(condIDs) > 0 {
			c.statsCollector.RecordEvaluation(condIDs[0], result, duration)
		}

		if err != nil {
			return false, err
		}
		return !result, nil

	default:
		return false, fmt.Errorf("不支持的操作符: %v", operator)
	}
}
