// Package condition 提供条件统计和优化功能
package condition

import (
	"sync"
	"time"
)

// StatsCollector 条件统计收集器
// 收集条件评估的相关统计信息，用于条件重排序
type StatsCollector struct {
	// 是否启用统计收集
	enabled bool

	// 互斥锁，保护stats访问
	mu sync.RWMutex

	// 条件统计信息映射
	stats map[string]*conditionStat
}

// conditionStat 条件统计数据
type conditionStat struct {
	// 条件ID
	ID string

	// 评估次数
	EvaluationCount int64

	// 返回true的次数
	TrueCount int64

	// 总计评估耗时（纳秒）
	TotalDuration int64

	// 平均执行时间（纳秒）
	AverageDuration int64

	// 返回true的概率 (0.0-1.0)
	TrueProbability float64

	// 平均时间成本（纳秒）
	TimeCost int64
}

// ConditionStats 对外暴露的条件统计数据
type ConditionStats struct {
	// 条件ID
	ID string

	// 评估次数
	EvaluationCount int64

	// 返回true的次数
	TrueCount int64

	// 总计评估耗时（纳秒）
	TotalDuration int64

	// 平均执行时间（纳秒）
	AverageDuration int64

	// 返回true的概率 (0.0-1.0)
	TrueProbability float64
}

// 全局统计收集器实例
var (
	globalStatsCollector *StatsCollector
	globalStatsOnce      sync.Once
)

// GetGlobalStatsCollector 获取全局统计收集器
func GetGlobalStatsCollector() *StatsCollector {
	globalStatsOnce.Do(func() {
		globalStatsCollector = NewStatsCollector()
	})
	return globalStatsCollector
}

// NewStatsCollector 创建新的统计收集器
func NewStatsCollector() *StatsCollector {
	return &StatsCollector{
		enabled: true,
		stats:   make(map[string]*conditionStat),
	}
}

// Enable 启用统计收集
func (c *StatsCollector) Enable() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.enabled = true
}

// Disable 禁用统计收集
func (c *StatsCollector) Disable() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.enabled = false
}

// IsEnabled 检查是否启用统计收集
func (c *StatsCollector) IsEnabled() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.enabled
}

// Reset 重置所有统计数据
func (c *StatsCollector) Reset() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.stats = make(map[string]*conditionStat)
}

// RecordEvaluation 记录条件评估结果
func (c *StatsCollector) RecordEvaluation(condID string, result bool, duration time.Duration) {
	if !c.IsEnabled() {
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	// 获取或创建条件统计
	stat, exists := c.stats[condID]
	if !exists {
		stat = &conditionStat{ID: condID}
		c.stats[condID] = stat
	}

	// 更新统计信息
	stat.EvaluationCount++
	if result {
		stat.TrueCount++
	}

	durationNs := duration.Nanoseconds()
	stat.TotalDuration += durationNs

	// 计算平均耗时
	stat.AverageDuration = stat.TotalDuration / stat.EvaluationCount

	// 计算true概率
	stat.TrueProbability = float64(stat.TrueCount) / float64(stat.EvaluationCount)

	// 更新时间成本
	stat.TimeCost = stat.AverageDuration
}

// GetConditionCost 获取条件成本信息
func (c *StatsCollector) GetConditionCost(condID string) (*conditionStat, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	stat, exists := c.stats[condID]
	if !exists {
		return nil, false
	}

	// 返回数据副本，避免外部修改
	copyStat := *stat
	return &copyStat, true
}

// GetAllStats 获取所有条件的统计信息
func (c *StatsCollector) GetAllStats() []ConditionStats {
	c.mu.RLock()
	defer c.mu.RUnlock()

	result := make([]ConditionStats, 0, len(c.stats))
	for _, stat := range c.stats {
		result = append(result, ConditionStats{
			ID:              stat.ID,
			EvaluationCount: stat.EvaluationCount,
			TrueCount:       stat.TrueCount,
			TotalDuration:   stat.TotalDuration,
			AverageDuration: stat.AverageDuration,
			TrueProbability: stat.TrueProbability,
		})
	}

	return result
}
