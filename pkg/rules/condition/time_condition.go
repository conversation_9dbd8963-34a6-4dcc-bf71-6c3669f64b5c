// Package condition 提供规则引擎条件评估功能
package condition

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"admin/pkg/rules/path"
)

// 时间操作常量
const (
	// 基本时间比较操作
	TimeOpBefore     Operator = "before"     // 早于某时间点
	TimeOpAfter      Operator = "after"      // 晚于某时间点
	TimeOpEqual      Operator = "equal"      // 等于某时间点(精确到秒)
	TimeOpBetween    Operator = "between"    // 在两个时间点范围内
	TimeOpNotBetween Operator = "notBetween" // 不在两个时间点范围内

	// 相对时间窗口操作
	TimeOpInLast    Operator = "inLast"    // 在过去的一段时间内(如过去3天)
	TimeOpNotInLast Operator = "notInLast" // 不在过去的一段时间内
	TimeOpInNext    Operator = "inNext"    // 在未来的一段时间内(如未来7天)
	TimeOpNotInNext Operator = "notInNext" // 不在未来的一段时间内

	// 日历相关比较
	TimeOpInDay     Operator = "inDay"     // 在某天内(0点到24点)
	TimeOpInMonth   Operator = "inMonth"   // 在某月内(1号到月末)
	TimeOpInYear    Operator = "inYear"    // 在某年内(1月1日到12月31日)
	TimeOpInWeek    Operator = "inWeek"    // 在某周内(周一到周日)
	TimeOpInQuarter Operator = "inQuarter" // 在某季度内(Q1/Q2/Q3/Q4)

	// 日期相等性判断
	TimeOpIsSameDay   Operator = "isSameDay"   // 是同一天
	TimeOpIsSameMonth Operator = "isSameMonth" // 是同一月
	TimeOpIsSameYear  Operator = "isSameYear"  // 是同一年

	// 工作日和周末判断
	TimeOpDayOfWeek Operator = "dayOfWeek" // 是星期几(0-6,0表示周日)
	TimeOpIsWorkday Operator = "isWorkday" // 是工作日(非周末且非假期)
	TimeOpIsWeekend Operator = "isWeekend" // 是周末或假期

	// 日期列表相关
	TimeOpIsInDateList Operator = "isInDateList" // 在日期列表中
)

// TimeSource 定义时间源接口，便于测试和自定义时间源
type TimeSource interface {
	// Now 返回当前时间
	Now() time.Time
}

// DefaultTimeSource 默认时间源，使用系统时间
type DefaultTimeSource struct{}

// Now 获取系统当前时间
func (s *DefaultTimeSource) Now() time.Time {
	return time.Now()
}

// TimeParser 时间解析器接口
type TimeParser interface {
	// ParseTime 解析时间值为time.Time
	ParseTime(value any) (time.Time, error)

	// ParseDuration 解析时间间隔
	ParseDuration(value any) (time.Duration, error)
}

// DefaultTimeParser 默认时间解析实现
type DefaultTimeParser struct {
	// 缓存已解析的时间格式，提高性能
	formatCache sync.Map

	// 自定义时间格式列表
	customFormats []string

	// 默认本地时区
	defaultLocation *time.Location
}

// NewDefaultTimeParser 创建默认时间解析器
func NewDefaultTimeParser() *DefaultTimeParser {
	loc, err := time.LoadLocation("Local")
	if err != nil {
		loc = time.UTC
	}

	return &DefaultTimeParser{
		formatCache:     sync.Map{},
		customFormats:   []string{},
		defaultLocation: loc,
	}
}

// SetDefaultLocation 设置默认时区
func (p *DefaultTimeParser) SetDefaultLocation(location *time.Location) {
	p.defaultLocation = location
}

// AddCustomFormat 添加自定义时间格式
func (p *DefaultTimeParser) AddCustomFormat(format string) {
	p.customFormats = append(p.customFormats, format)
}

// ParseTime 解析时间值为time.Time
func (p *DefaultTimeParser) ParseTime(value interface{}) (time.Time, error) {
	switch v := value.(type) {
	case time.Time:
		return v, nil
	case string:
		// 使用缓存的时间格式优先解析
		if cachedFormat, ok := p.formatCache.Load(v); ok {
			if format, ok := cachedFormat.(string); ok {
				t, err := time.ParseInLocation(format, v, p.defaultLocation)
				if err == nil {
					return t, nil
				}
			}
		}

		// 组合所有要尝试的格式
		formats := append([]string{
			time.RFC3339,
			"2006-01-02T15:04:05Z07:00",        // ISO8601
			"2006-01-02T15:04:05.999999Z07:00", // ISO8601 带微秒
			"2006-01-02 15:04:05",
			"2006-01-02 15:04",
			"2006-01-02",
			"15:04:05",
			"2006/01/02",
			"01/02/2006", // 美式日期
			"02/01/2006", // 欧式日期
			"20060102",   // 紧凑格式
			"2006年01月02日",
			"2006年1月2日",
			"01月02日",
			"1月2日",
		}, p.customFormats...)

		for _, format := range formats {
			if t, err := time.ParseInLocation(format, v, p.defaultLocation); err == nil {
				// 缓存成功的格式，提高后续解析性能
				p.formatCache.Store(v, format)
				return t, nil
			}
		}

		return time.Time{}, fmt.Errorf("无法解析时间字符串: %s", v)
	case int64:
		return time.Unix(v, 0), nil
	case int:
		return time.Unix(int64(v), 0), nil
	case float64:
		return time.Unix(int64(v), 0), nil
	case map[string]any:
		// 支持自定义时间对象格式
		if timeStr, ok := v["time"].(string); ok {
			if formatStr, ok := v["format"].(string); ok {
				tzStr, _ := v["timezone"].(string)
				tz := p.defaultLocation
				if tzStr != "" {
					if location, err := time.LoadLocation(tzStr); err == nil {
						tz = location
					}
				}

				if t, err := time.ParseInLocation(formatStr, timeStr, tz); err == nil {
					return t, nil
				}
			}

			// 如果没有格式，尝试常规解析
			return p.ParseTime(timeStr)
		}
		return time.Time{}, errors.New("无效的时间对象格式")
	default:
		return time.Time{}, fmt.Errorf("不支持的时间类型: %T", value)
	}
}

// ParseDuration 解析时间间隔
func (p *DefaultTimeParser) ParseDuration(value any) (time.Duration, error) {
	switch v := value.(type) {
	case time.Duration:
		return v, nil
	case string:
		// 支持扩展时间单位如 "1d", "2w", "3M", "1y"
		if strings.HasSuffix(v, "d") || strings.HasSuffix(v, "w") ||
			strings.HasSuffix(v, "M") || strings.HasSuffix(v, "y") ||
			strings.HasSuffix(v, "Q") {
			return p.parseExtendedDuration(v)
		}
		return time.ParseDuration(v)
	case int64:
		return time.Duration(v) * time.Second, nil
	case int:
		return time.Duration(v) * time.Second, nil
	case float64:
		return time.Duration(v) * time.Second, nil
	case map[string]any:
		if value, ok := v["value"].(float64); ok {
			if unit, ok := v["unit"].(string); ok {
				switch unit {
				case "second", "seconds":
					return time.Duration(value) * time.Second, nil
				case "minute", "minutes":
					return time.Duration(value) * time.Minute, nil
				case "hour", "hours":
					return time.Duration(value) * time.Hour, nil
				case "day", "days":
					return time.Duration(value) * 24 * time.Hour, nil
				case "week", "weeks":
					return time.Duration(value) * 7 * 24 * time.Hour, nil
				case "month", "months":
					// 简化处理，一个月按30天算
					return time.Duration(value) * 30 * 24 * time.Hour, nil
				case "year", "years":
					return time.Duration(value) * 365 * 24 * time.Hour, nil
				}
			}
		}
		return 0, errors.New("无效的时间间隔对象格式")
	default:
		return 0, fmt.Errorf("不支持的时间间隔类型: %T", value)
	}
}

// parseExtendedDuration 解析扩展时间单位
func (p *DefaultTimeParser) parseExtendedDuration(s string) (time.Duration, error) {
	unitMap := map[string]time.Duration{
		"d": 24 * time.Hour,       // 天
		"w": 7 * 24 * time.Hour,   // 周
		"M": 30 * 24 * time.Hour,  // 月（简化为30天）
		"Q": 90 * 24 * time.Hour,  // 季度（简化为90天）
		"y": 365 * 24 * time.Hour, // 年（简化为365天）
	}

	for unit, multiplier := range unitMap {
		if strings.HasSuffix(s, unit) {
			valueStr := s[:len(s)-len(unit)]
			value, err := strconv.ParseFloat(valueStr, 64)
			if err != nil {
				return 0, err
			}
			return time.Duration(value * float64(multiplier)), nil
		}
	}

	return 0, fmt.Errorf("无法解析时间间隔: %s", s)
}

// HolidayProvider 假期提供器接口
type HolidayProvider interface {
	// IsHoliday 判断是否为假期
	IsHoliday(t time.Time) bool

	// IsWorkday 判断是否为工作日
	IsWorkday(t time.Time) bool
}

// SimpleHolidayProvider 简单假期提供器
// 仅根据周末判断，可扩展为从配置或API获取法定节假日
type SimpleHolidayProvider struct {
	// 特殊节假日列表 (ISO日期格式 "2006-01-02" -> 是否为假期)
	specialDays map[string]bool
}

// NewSimpleHolidayProvider 创建简单假期提供器
func NewSimpleHolidayProvider() *SimpleHolidayProvider {
	return &SimpleHolidayProvider{
		specialDays: make(map[string]bool),
	}
}

// AddSpecialDay 添加特殊日期
// isHoliday: true表示是假期，false表示是工作日
func (p *SimpleHolidayProvider) AddSpecialDay(date time.Time, isHoliday bool) {
	key := date.Format("2006-01-02")
	p.specialDays[key] = isHoliday
}

// AddSpecialDayStr 添加特殊日期（字符串格式）
func (p *SimpleHolidayProvider) AddSpecialDayStr(dateStr string, isHoliday bool) error {
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return err
	}
	p.AddSpecialDay(t, isHoliday)
	return nil
}

// IsHoliday 判断是否为假期
func (p *SimpleHolidayProvider) IsHoliday(t time.Time) bool {
	// 检查是否是特殊日期
	key := t.Format("2006-01-02")
	if isHoliday, exists := p.specialDays[key]; exists {
		return isHoliday
	}

	// 周末视为假期
	weekday := t.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}

// IsWorkday 判断是否为工作日
func (p *SimpleHolidayProvider) IsWorkday(t time.Time) bool {
	return !p.IsHoliday(t)
}

// BaseCondition 基础条件的占位定义
// 需要根据实际需求实现其逻辑

type BaseCondition struct {
	ConditionType string
}

// Evaluate 基础条件的评估逻辑
func (bc *BaseCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	// 示例逻辑：始终返回true
	return true, nil
}

// GetType 获取条件类型
func (bc *BaseCondition) GetType() string {
	return bc.ConditionType
}

// TimeCondition 时间相关条件
type TimeCondition struct {
	// 基础条件类型
	ConditionType string

	// 时间操作类型
	Operation Operator

	// 基准时间定义
	// 如果为空，则使用当前时间作为基准
	BaseTimeValue  any
	IsBaseTimePath bool

	// 目标时间路径或时间值
	Target any

	// 二级目标，用于范围比较
	Target2 any

	// 是否使用路径获取时间值
	IsPath bool

	// 路径解析器
	PathParser *path.PathParser

	// 时间解析器
	TimeParser TimeParser

	// 时间源
	TimeSource TimeSource

	// 假期提供器
	HolidayProvider HolidayProvider
}

// NewTimeCondition 创建一个新的时间条件
// operation: 操作符类型
// isPath: 指示targets中的值是否为数据路径
// targets: 不定长参数，根据不同操作符有不同解释
//   - 对于基本操作符(如 TimeOpBefore, TimeOpAfter), targets[0]是目标时间/路径
//   - 对于范围操作符(如 TimeOpBetween), targets[0]和targets[1]是范围的两个时间点/路径
//   - 对于时间窗口操作符(如 TimeOpInNext), targets[0]是目标时间/路径，如果有targets[1]则表示持续时间
//   - 对于日期列表(TimeOpIsInDateList), targets是日期列表
//
// 使用示例:
//  1. 创建"当前时间在未来3天内"的条件:
//     NewTimeCondition(TimeOpInNext, false, "3d")
//  2. 创建"优惠券在未来3天内过期"的条件:
//     NewTimeCondition(TimeOpInNext, true, "$.data.expireDate", "3d")
//  3. 创建"指定时间在两个时间点之间"的条件:
//     NewTimeCondition(TimeOpBetween, false, startTime, endTime)
//  4. 创建"指定路径的时间是否在给定时间之前"的条件:
//     NewTimeCondition(TimeOpBefore, true, "$.data.expireDate")
func NewTimeCondition(operation Operator, isPath bool, targets ...any) *TimeCondition {
	tc := &TimeCondition{
		ConditionType:   "time",
		Operation:       operation,
		IsPath:          isPath,
		PathParser:      path.NewPathParser(),
		TimeParser:      NewDefaultTimeParser(),
		TimeSource:      &DefaultTimeSource{},
		HolidayProvider: NewSimpleHolidayProvider(),
	}

	// 根据参数数量和操作类型，设置相应的目标值
	if len(targets) > 0 {
		tc.Target = targets[0]
	}

	// 对于需要两个参数的操作，设置第二个目标
	if len(targets) > 1 && (isRangeOperation(operation) || isTimeWindowOperation(operation)) {
		tc.Target2 = targets[1]
	}

	return tc
}

// isTimeWindowOperation 判断是否为时间窗口操作
func isTimeWindowOperation(op Operator) bool {
	return op == TimeOpInLast || op == TimeOpNotInLast ||
		op == TimeOpInNext || op == TimeOpNotInNext
}

// isRangeOperation 判断是否为范围操作
func isRangeOperation(op Operator) bool {
	return op == TimeOpBetween || op == TimeOpNotBetween
}

// WithBaseTime 设置基准时间的链式方法
func (c *TimeCondition) WithBaseTime(baseTime any, isPath bool) *TimeCondition {
	c.BaseTimeValue = baseTime
	c.IsBaseTimePath = isPath
	return c
}

// WithTimeSource 设置时间源的链式方法
func (c *TimeCondition) WithTimeSource(source TimeSource) *TimeCondition {
	c.TimeSource = source
	return c
}

// WithTimeParser 设置时间解析器的链式方法
func (c *TimeCondition) WithTimeParser(parser TimeParser) *TimeCondition {
	c.TimeParser = parser
	return c
}

// WithHolidayProvider 设置假期提供器的链式方法
func (c *TimeCondition) WithHolidayProvider(provider HolidayProvider) *TimeCondition {
	c.HolidayProvider = provider
	return c
}

// getTimeFromPath 从数据路径获取时间
func (c *TimeCondition) getTimeFromPath(data any, pathOrValue any) (time.Time, error) {
	if pathStr, ok := pathOrValue.(string); ok {
		// 从路径获取时间值
		value, err := c.PathParser.GetValue(data, pathStr)
		if err != nil {
			return time.Time{}, err
		}

		return c.TimeParser.ParseTime(value)
	}

	// 直接解析时间值
	return c.TimeParser.ParseTime(pathOrValue)
}

// needsTime 判断操作是否需要时间对象
func needsTime(op Operator) bool {
	switch op {
	case TimeOpInLast, TimeOpNotInLast, TimeOpInNext, TimeOpNotInNext,
		TimeOpDayOfWeek, TimeOpIsWorkday, TimeOpIsWeekend, TimeOpIsInDateList:
		return false
	default:
		return true
	}
}

// Evaluate 评估时间条件
func (c *TimeCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	// 获取基准时间和目标时间
	baseTime, targetTime, targetTime2, targetValue, err := c.prepareTimeValues(data)
	if err != nil {
		return false, err
	}

	// 根据不同的操作类型进行评估
	switch c.Operation {
	// 基本时间比较
	case TimeOpBefore, TimeOpAfter, TimeOpEqual:
		return c.evaluateBasicComparison(baseTime, targetTime)

	// 时间范围比较
	case TimeOpBetween, TimeOpNotBetween:
		return c.evaluateRangeComparison(baseTime, targetTime, targetTime2)

	// 时间窗口操作
	case TimeOpInLast, TimeOpNotInLast, TimeOpInNext, TimeOpNotInNext:
		return c.evaluateTimeWindow(baseTime, targetValue)

	// 日历相关比较
	case TimeOpIsSameDay, TimeOpIsSameMonth, TimeOpIsSameYear:
		return c.evaluateSameCalendarUnit(baseTime, targetTime)

	// 日期范围比较
	case TimeOpInDay, TimeOpInMonth, TimeOpInYear, TimeOpInWeek, TimeOpInQuarter:
		return c.evaluateDateRangeCheck(baseTime, targetTime)

	// 工作日和周末判断
	case TimeOpDayOfWeek, TimeOpIsWorkday, TimeOpIsWeekend:
		return c.evaluateWeekdayCheck(baseTime, targetValue)

	// 日期列表比较
	case TimeOpIsInDateList:
		return c.evaluateDateListCheck(baseTime, targetValue)

	default:
		return false, fmt.Errorf("不支持的时间操作类型: %s", c.Operation)
	}
}

// prepareTimeValues 准备时间条件评估所需的时间值
func (c *TimeCondition) prepareTimeValues(data any) (baseTime time.Time, targetTime time.Time, targetTime2 time.Time, targetValue any, err error) {
	// 获取基准时间
	if c.BaseTimeValue != nil {
		if c.IsBaseTimePath {
			baseTime, err = c.getTimeFromPath(data, c.BaseTimeValue)
			if err != nil {
				return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取基准时间失败: %w", err)
			}
		} else {
			baseTime, err = c.TimeParser.ParseTime(c.BaseTimeValue)
			if err != nil {
				return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("解析基准时间失败: %w", err)
			}
		}
	} else {
		// 使用当前时间作为默认比较基准
		baseTime = c.TimeSource.Now()
	}

	if isTimeWindowOperation(c.Operation) {
		// 特殊处理时间窗口操作
		// 如果 Target 是路径，从路径获取时间，Target2 是持续时间
		// 如果 Target 不是路径，Target 是持续时间

		if c.IsPath && c.Target != nil {
			// 从数据路径获取时间点
			targetTime, err = c.getTimeFromPath(data, c.Target)
			if err != nil {
				return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取目标时间失败: %w", err)
			}

			// 第二个参数是持续时间，可能是字符串也可能是路径
			if c.Target2 != nil {
				// 检查是否为路径
				if durStr, ok := c.Target2.(string); ok && strings.HasPrefix(durStr, "$") {
					// 是路径，尝试获取持续时间值
					targetValue, err = c.PathParser.GetValue(data, durStr)
					if err != nil {
						return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取持续时间失败: %w", err)
					}
				} else {
					// 不是路径，直接使用
					targetValue = c.Target2
				}
			} else {
				// 没有持续时间参数，使用默认值
				targetValue = "3d" // 默认为3天
			}
		} else if c.Target != nil {
			// Target 直接是持续时间
			targetValue = c.Target
			targetTime = baseTime // 使用基准时间
		}
	} else if isRangeOperation(c.Operation) {
		// 处理范围操作(Between/NotBetween)
		if c.IsPath {
			// 从路径获取时间范围端点
			targetTime, err = c.getTimeFromPath(data, c.Target)
			if err != nil {
				return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取目标时间1失败: %w", err)
			}

			if c.Target2 != nil {
				targetTime2, err = c.getTimeFromPath(data, c.Target2)
				if err != nil {
					return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取目标时间2失败: %w", err)
				}
			}
		} else {
			// 直接使用时间范围端点
			targetTime, err = c.TimeParser.ParseTime(c.Target)
			if err != nil {
				return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("解析目标时间1失败: %w", err)
			}

			if c.Target2 != nil {
				targetTime2, err = c.TimeParser.ParseTime(c.Target2)
				if err != nil {
					return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("解析目标时间2失败: %w", err)
				}
			}
		}
	} else {
		// 处理普通时间比较操作
		if c.IsPath {
			if needsTime(c.Operation) {
				targetTime, err = c.getTimeFromPath(data, c.Target)
				if err != nil {
					return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取目标时间失败: %w", err)
				}
			} else {
				// 对于不需要时间的操作，直接获取原始值
				targetValue, err = c.PathParser.GetValue(data, c.Target.(string))
				if err != nil {
					return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("获取目标值失败: %w", err)
				}
			}
		} else {
			if needsTime(c.Operation) {
				targetTime, err = c.TimeParser.ParseTime(c.Target)
				if err != nil {
					return time.Time{}, time.Time{}, time.Time{}, nil, fmt.Errorf("解析目标时间失败: %w", err)
				}
			} else {
				// 直接使用目标值
				targetValue = c.Target
			}
		}
	}

	return baseTime, targetTime, targetTime2, targetValue, nil
}

// evaluateBasicComparison 评估基本时间比较操作
func (c *TimeCondition) evaluateBasicComparison(baseTime time.Time, targetTime time.Time) (bool, error) {
	// 标准化为UTC时区进行比较，避免时区差异导致比较错误
	baseTimeUTC := baseTime.UTC()
	targetTimeUTC := targetTime.UTC()

	switch c.Operation {
	case TimeOpBefore:
		return baseTimeUTC.Before(targetTimeUTC), nil
	case TimeOpAfter:
		return baseTimeUTC.After(targetTimeUTC), nil
	case TimeOpEqual:
		// 精确到秒的比较
		return baseTimeUTC.Unix() == targetTimeUTC.Unix(), nil
	default:
		return false, fmt.Errorf("非基本时间比较操作: %s", c.Operation)
	}
}

// evaluateRangeComparison 评估时间范围比较操作
func (c *TimeCondition) evaluateRangeComparison(baseTime time.Time, targetTime time.Time, targetTime2 time.Time) (bool, error) {
	// 确保时间范围的顺序
	if targetTime.After(targetTime2) {
		targetTime, targetTime2 = targetTime2, targetTime
	}

	switch c.Operation {
	case TimeOpBetween:
		return baseTime.After(targetTime) && baseTime.Before(targetTime2), nil
	case TimeOpNotBetween:
		return baseTime.Before(targetTime) || baseTime.After(targetTime2), nil
	default:
		return false, fmt.Errorf("非范围比较操作: %s", c.Operation)
	}
}

// evaluateTimeWindow 评估相对时间窗口操作
func (c *TimeCondition) evaluateTimeWindow(baseTime time.Time, targetValue any) (bool, error) {
	now := c.TimeSource.Now()

	// 标准化时区，确保在相同时区下比较
	nowUTC := now.UTC()
	baseTimeUTC := baseTime.UTC()

	// 解析持续时间
	duration, err := c.TimeParser.ParseDuration(targetValue)
	if err != nil {
		return false, fmt.Errorf("解析持续时间失败: %w", err)
	}

	switch c.Operation {
	case TimeOpInNext:
		// 优惠券过期场景：检查目标时间（如过期时间）是否在当前时间到(当前时间+X天)之间
		// 即：now <= baseTime < now+duration
		return (baseTimeUTC.After(nowUTC) || baseTimeUTC.Equal(nowUTC)) && baseTimeUTC.Before(nowUTC.Add(duration)), nil
	case TimeOpNotInNext:
		// 不在未来X天内：当前时间>目标时间 或 目标时间>=当前时间+X天
		return baseTimeUTC.Before(nowUTC) || baseTimeUTC.After(nowUTC.Add(duration)) || baseTimeUTC.Equal(nowUTC.Add(duration)), nil
	case TimeOpInLast:
		// 在过去X天内：当前时间-X天 < 目标时间 <= 当前时间
		return baseTimeUTC.After(nowUTC.Add(-duration)) && (baseTimeUTC.Before(nowUTC) || baseTimeUTC.Equal(nowUTC)), nil
	case TimeOpNotInLast:
		// 不在过去X天内：目标时间<=当前时间-X天 或 目标时间>当前时间
		return baseTimeUTC.Before(nowUTC.Add(-duration)) || baseTimeUTC.Equal(nowUTC.Add(-duration)) || baseTimeUTC.After(nowUTC), nil
	default:
		return false, fmt.Errorf("不支持的时间窗口操作: %s", c.Operation)
	}
}

// evaluateSameCalendarUnit 评估同一日历单元操作
func (c *TimeCondition) evaluateSameCalendarUnit(baseTime time.Time, targetTime time.Time) (bool, error) {
	switch c.Operation {
	case TimeOpIsSameDay:
		return isSameDay(baseTime, targetTime), nil
	case TimeOpIsSameMonth:
		return isSameMonth(baseTime, targetTime), nil
	case TimeOpIsSameYear:
		return isSameYear(baseTime, targetTime), nil
	default:
		return false, fmt.Errorf("不支持的日历单元比较操作: %s", c.Operation)
	}
}

// isSameDay 判断两个时间是否是同一天
func isSameDay(t1, t2 time.Time) bool {
	y1, m1, d1 := t1.Date()
	y2, m2, d2 := t2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// isSameMonth 判断两个时间是否是同一月
func isSameMonth(t1, t2 time.Time) bool {
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()
	return y1 == y2 && m1 == m2
}

// isSameYear 判断两个时间是否是同一年
func isSameYear(t1, t2 time.Time) bool {
	y1, _, _ := t1.Date()
	y2, _, _ := t2.Date()
	return y1 == y2
}

// evaluateDateRangeCheck 评估日期范围检查
func (c *TimeCondition) evaluateDateRangeCheck(baseTime time.Time, targetTime time.Time) (bool, error) {
	switch c.Operation {
	case TimeOpInDay:
		year, month, day := targetTime.Date()
		start := time.Date(year, month, day, 0, 0, 0, 0, targetTime.Location())
		end := start.Add(24 * time.Hour)
		return baseTime.After(start) && baseTime.Before(end), nil
	case TimeOpInMonth:
		year, month, _ := targetTime.Date()
		start := time.Date(year, month, 1, 0, 0, 0, 0, targetTime.Location())
		end := start.AddDate(0, 1, 0)
		return baseTime.After(start) && baseTime.Before(end), nil
	case TimeOpInYear:
		year, _, _ := targetTime.Date()
		start := time.Date(year, 1, 1, 0, 0, 0, 0, targetTime.Location())
		end := start.AddDate(1, 0, 0)
		return baseTime.After(start) && baseTime.Before(end), nil
	default:
		return false, fmt.Errorf("不支持的日期范围检查操作: %s", c.Operation)
	}
}

// evaluateWeekdayCheck 评估工作日和周末判断
func (c *TimeCondition) evaluateWeekdayCheck(baseTime time.Time, targetValue any) (bool, error) {
	switch c.Operation {
	case TimeOpDayOfWeek:
		var targetWeekday time.Weekday
		switch v := targetValue.(type) {
		case int:
			targetWeekday = time.Weekday(v)
		case string:
			weekdays := map[string]time.Weekday{
				"sunday":    time.Sunday,
				"monday":    time.Monday,
				"tuesday":   time.Tuesday,
				"wednesday": time.Wednesday,
				"thursday":  time.Thursday,
				"friday":    time.Friday,
				"saturday":  time.Saturday,
			}
			if day, ok := weekdays[strings.ToLower(v)]; ok {
				targetWeekday = day
			} else {
				return false, fmt.Errorf("无效的星期几值: %s", v)
			}
		default:
			return false, fmt.Errorf("无效的星期几类型: %T", targetValue)
		}
		return baseTime.Weekday() == targetWeekday, nil
	case TimeOpIsWorkday:
		return c.HolidayProvider.IsWorkday(baseTime), nil
	case TimeOpIsWeekend:
		return !c.HolidayProvider.IsWorkday(baseTime), nil
	default:
		return false, fmt.Errorf("不支持的工作日检查操作: %s", c.Operation)
	}
}

// evaluateDateListCheck 评估日期列表检查
func (c *TimeCondition) evaluateDateListCheck(baseTime time.Time, targetValue any) (bool, error) {
	dateList, ok := targetValue.([]any)
	if !ok {
		return false, errors.New("目标值必须是日期列表")
	}

	baseDate := time.Date(baseTime.Year(), baseTime.Month(), baseTime.Day(), 0, 0, 0, 0, baseTime.Location())

	for _, dateItem := range dateList {
		dateTime, err := c.TimeParser.ParseTime(dateItem)
		if err != nil {
			continue // 跳过无法解析的日期
		}

		checkDate := time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day(), 0, 0, 0, 0, dateTime.Location())
		if baseDate.Equal(checkDate) {
			return true, nil
		}
	}

	return false, nil
}
