// Package condition 提供条件合并功能
package condition

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strings"

	"admin/pkg/rules/core"
)

// ConditionMerger 条件合并优化器
// 识别并合并语义上等价的条件，减少重复计算
type ConditionMerger struct {
	// 条件缓存，用于快速查找等价条件
	conditionCache map[string]core.Condition

	// 启用深度比较
	enableDeepComparison bool
}

// NewConditionMerger 创建条件合并优化器
func NewConditionMerger() *ConditionMerger {
	return &ConditionMerger{
		conditionCache:       make(map[string]core.Condition),
		enableDeepComparison: true,
	}
}

// SetDeepComparison 设置是否启用深度比较
func (m *ConditionMerger) SetDeepComparison(enable bool) {
	m.enableDeepComparison = enable
}

// MergeConditions 合并一组条件中的等价条件
func (m *ConditionMerger) MergeConditions(conditions []core.Condition) []core.Condition {
	if len(conditions) <= 1 {
		return conditions
	}

	// 为可能的合并创建新缓存
	m.conditionCache = make(map[string]core.Condition)

	// 结果条件列表
	result := make([]core.Condition, 0, len(conditions))

	// 用于跟踪处理过的条件
	processed := make(map[int]bool)

	// 先寻找可直接判断相等的条件
	for i := 0; i < len(conditions); i++ {
		if processed[i] {
			continue
		}

		cond := conditions[i]
		if cond == nil {
			continue
		}

		// 尝试生成条件指纹
		fingerprint, err := m.generateConditionFingerprint(cond)
		if err != nil {
			// 如果无法生成指纹，将条件添加到结果中
			result = append(result, cond)
			processed[i] = true
			continue
		}

		// 检查是否已有相同指纹的条件
		if existingCond, found := m.conditionCache[fingerprint]; found {
			// 找到等价条件，使用缓存中的条件实例
			// 这样，当多个复合条件引用相同子条件时，只会计算一次
			conditions[i] = existingCond
			processed[i] = true
		} else {
			// 新的指纹，添加到缓存
			m.conditionCache[fingerprint] = cond
			result = append(result, cond)
			processed[i] = true

			// 查找等价条件并合并
			for j := i + 1; j < len(conditions); j++ {
				if processed[j] {
					continue
				}

				if m.areConditionsEquivalent(cond, conditions[j]) {
					// 标记为已处理，不添加到结果中
					processed[j] = true
					// 替换为已缓存的条件实例
					conditions[j] = cond
				}
			}
		}
	}

	return result
}

// MergeCompositeCondition 合并复合条件中的子条件
func (m *ConditionMerger) MergeCompositeCondition(condition core.Condition) core.Condition {
	// 检查是否为复合条件
	compCondition, ok := condition.(interface{ GetConditions() []core.Condition })
	if !ok {
		return condition
	}

	// 获取子条件
	subConditions := compCondition.GetConditions()
	if len(subConditions) <= 1 {
		return condition
	}

	// 递归合并子条件
	for i, subCond := range subConditions {
		subConditions[i] = m.MergeCompositeCondition(subCond)
	}

	// 合并等价的子条件
	mergedSubConditions := m.MergeConditions(subConditions)

	// 检查是否实际发生了合并（子条件数量减少）
	if len(mergedSubConditions) == len(subConditions) {
		// 没有合并发生，返回原始条件
		return condition
	}

	// 创建新的复合条件
	if setter, ok := condition.(interface{ SetConditions([]core.Condition) }); ok {
		// 如果条件支持设置子条件，直接修改
		setter.SetConditions(mergedSubConditions)
		return condition
	}

	// 无法直接修改，尝试创建新的复合条件
	switch c := condition.(type) {
	case *CompositeCondition:
		return &CompositeCondition{
			Operator:   c.Operator,
			Conditions: mergedSubConditions,
		}
	case *LazyCompositeCondition:
		return &LazyCompositeCondition{
			Operator:   c.Operator,
			Conditions: mergedSubConditions,
		}
	case *AdaptiveCompositeCondition:
		// Create a new instance instead of copying to avoid copying the mutex
		return &AdaptiveCompositeCondition{
			Operator:   c.Operator,
			Conditions: mergedSubConditions,
		}
	default:
		// 未知类型，返回原始条件
		return condition
	}
}

// generateConditionFingerprint 生成条件的唯一指纹
func (m *ConditionMerger) generateConditionFingerprint(condition core.Condition) (string, error) {
	if condition == nil {
		return "", fmt.Errorf("条件为空")
	}

	// 尝试使用条件的字符串表示
	switch c := condition.(type) {
	case *SimpleCondition:
		// 对于简单条件，使用路径、操作符和值生成指纹
		fingerprint := fmt.Sprintf("%s:%s:%v", c.Path, c.Operator, c.Value)
		return hashString(fingerprint), nil

	case *CompositeCondition:
		return m.generateCompositeFingerprint(c.Operator, c.Conditions)

	case *LazyCompositeCondition:
		return m.generateCompositeFingerprint(c.Operator, c.Conditions)

	case *AdaptiveCompositeCondition:
		return m.generateCompositeFingerprint(c.Operator, c.Conditions)

	case *LazyCondition:
		// 对于LazyCondition，使用内部条件生成指纹
		if c.innerCondition != nil {
			return m.generateConditionFingerprint(c.innerCondition)
		}
		return "", fmt.Errorf("LazyCondition内部条件为空")

	default:
		// 尝试序列化为JSON
		if m.enableDeepComparison {
			json, err := json.Marshal(condition)
			if err == nil {
				return hashString(string(json)), nil
			}
		}

		// 使用反射
		t := reflect.TypeOf(condition)
		_ = reflect.ValueOf(condition)

		// 尝试获取字段值
		fingerprint := fmt.Sprintf("%s:%p", t.String(), condition)
		return hashString(fingerprint), nil
	}
}

// generateCompositeFingerprint 为复合条件生成指纹
func (m *ConditionMerger) generateCompositeFingerprint(operator Operator, conditions []core.Condition) (string, error) {
	if len(conditions) == 0 {
		return hashString(string(operator) + ":empty"), nil
	}

	// 为每个子条件生成指纹
	fingerprints := make([]string, 0, len(conditions))
	for _, c := range conditions {
		fp, err := m.generateConditionFingerprint(c)
		if err != nil {
			continue
		}
		fingerprints = append(fingerprints, fp)
	}

	// 排序指纹，确保顺序无关性
	sort.Strings(fingerprints)

	// 组合指纹
	combinedFingerprint := string(operator) + ":" + strings.Join(fingerprints, ",")
	return hashString(combinedFingerprint), nil
}

// areConditionsEquivalent 检查两个条件是否在语义上等价
func (m *ConditionMerger) areConditionsEquivalent(cond1, cond2 core.Condition) bool {
	if cond1 == nil || cond2 == nil {
		return cond1 == cond2
	}

	// 如果是相同实例，直接返回true
	if cond1 == cond2 {
		return true
	}

	// 生成并比较指纹
	fp1, err1 := m.generateConditionFingerprint(cond1)
	fp2, err2 := m.generateConditionFingerprint(cond2)

	if err1 != nil || err2 != nil {
		return false
	}

	return fp1 == fp2
}

// hashString 对字符串进行哈希处理
func hashString(s string) string {
	h := sha256.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

// SetConditions 设置复合条件的子条件（接口方法）
func (c *CompositeCondition) SetConditions(conditions []core.Condition) {
	c.Conditions = conditions
}

// SetConditions 设置LazyCompositeCondition的子条件（接口方法）
func (c *LazyCompositeCondition) SetConditions(conditions []core.Condition) {
	c.Conditions = conditions
}
