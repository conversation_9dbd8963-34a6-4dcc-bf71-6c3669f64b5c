// Package condition 提供规则引擎条件评估功能
package condition

import (
	"context"
	"errors"
	"fmt"
	"math"
	"reflect"

	"admin/pkg/rules/function"
	"admin/pkg/rules/path"
)

// FunctionCondition 函数条件，允许调用函数进行条件评估
type FunctionCondition struct {
	// 基础条件类型
	ConditionType string

	// 函数名称
	FunctionName string

	// 函数参数路径列表
	ParamPaths []string

	// 比较操作类型
	Operation Operator

	// 比较目标值
	ExpectedValue any

	// 路径解析器
	Parser *path.PathParser

	// 函数注册表
	Registry *function.FunctionRegistry
}

// 函数操作常量
const (
	// 比较操作
	FuncOpEqual        Operator = "equal"        // 等于
	FuncOpNotEqual     Operator = "notEqual"     // 不等于
	FuncOpGreater      Operator = "greater"      // 大于
	FuncOpGreaterEqual Operator = "greaterEqual" // 大于等于
	FuncOpLess         Operator = "less"         // 小于
	FuncOpLessEqual    Operator = "lessEqual"    // 小于等于

	// 布尔操作
	FuncOpIsTrue  Operator = "isTrue"  // 返回值为真
	FuncOpIsFalse Operator = "isFalse" // 返回值为假
)

// NewFunctionCondition 创建函数条件
func NewFunctionCondition(
	functionName string,
	paramPaths []string,
	operation Operator,
	expectedValue any,
	registry *function.FunctionRegistry,
) *FunctionCondition {
	return &FunctionCondition{
		ConditionType: "function",
		FunctionName:  functionName,
		ParamPaths:    paramPaths,
		Operation:     operation,
		ExpectedValue: expectedValue,
		Parser:        path.NewPathParser(),
		Registry:      registry,
	}
}

// Evaluate 评估函数条件
func (c *FunctionCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	// 检查函数是否存在
	if !c.Registry.HasFunction(c.FunctionName) {
		return false, fmt.Errorf("函数 %s 未注册", c.FunctionName)
	}

	// 从数据中提取函数参数
	params := make([]any, len(c.ParamPaths))
	for i, path := range c.ParamPaths {
		var err error
		params[i], err = c.Parser.GetValue(data, path)
		if err != nil {
			return false, fmt.Errorf("从路径 %s 提取参数失败: %w", path, err)
		}
	}

	// 调用函数
	result, err := c.Registry.InvokeFunctionSingle(c.FunctionName, params...)
	if err != nil {
		return false, fmt.Errorf("调用函数 %s 失败: %w", c.FunctionName, err)
	}

	// 根据操作类型比较结果
	switch c.Operation {
	case FuncOpEqual:
		return equals(result, c.ExpectedValue), nil
	case FuncOpNotEqual:
		return !equals(result, c.ExpectedValue), nil
	case FuncOpGreater:
		return compare(result, c.ExpectedValue) > 0, nil
	case FuncOpGreaterEqual:
		return compare(result, c.ExpectedValue) >= 0, nil
	case FuncOpLess:
		return compare(result, c.ExpectedValue) < 0, nil
	case FuncOpLessEqual:
		return compare(result, c.ExpectedValue) <= 0, nil
	case FuncOpIsTrue:
		return isTruthy(result), nil
	case FuncOpIsFalse:
		return !isTruthy(result), nil
	default:
		return false, errors.New("不支持的函数操作类型")
	}
}

// equals 判断两个值是否相等
func equals(a, b any) bool {
	if a == nil && b == nil {
		return true
	}
	if a == nil || b == nil {
		return false
	}

	// 尝试将两个值转换为浮点数
	var aFloat, bFloat float64
	var aErr, bErr error

	// 数值转换
	aFloat, aErr = toFloat64(a)
	bFloat, bErr = toFloat64(b)

	// 如果都能转为数字，比较数值
	if aErr == nil && bErr == nil {
		// 浮点数比较，考虑精度
		return math.Abs(aFloat-bFloat) < 1e-9
	}

	// 否则转为字符串比较
	return fmt.Sprintf("%v", a) == fmt.Sprintf("%v", b)
}

// compare 比较两个值的大小
func compare(a, b any) int {
	if a == nil && b == nil {
		return 0
	}
	if a == nil {
		return -1
	}
	if b == nil {
		return 1
	}

	// 尝试数值比较
	aFloat, aErr := toFloat64(a)
	bFloat, bErr := toFloat64(b)

	// 如果都能转为数字，比较数值
	if aErr == nil && bErr == nil {
		if aFloat < bFloat {
			return -1
		}
		if aFloat > bFloat {
			return 1
		}
		return 0
	}

	// 否则转为字符串比较
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)

	if aStr < bStr {
		return -1
	}
	if aStr > bStr {
		return 1
	}
	return 0
}

// toFloat64 将值转换为float64（本地版本）
func toFloat64(value any) (float64, error) {
	if value == nil {
		return 0, nil
	}

	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int8:
		return float64(v), nil
	case int16:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint8:
		return float64(v), nil
	case uint16:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case string:
		// 尝试将字符串解析为浮点数
		var result float64
		_, err := fmt.Sscanf(v, "%f", &result)
		if err != nil {
			return 0, err
		}
		return result, nil
	default:
		// 如果是自定义类型，尝试使用反射获取数值
		val := reflect.ValueOf(value)
		switch val.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			return float64(val.Int()), nil
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			return float64(val.Uint()), nil
		case reflect.Float32, reflect.Float64:
			return val.Float(), nil
		default:
			return 0, errors.New("非数值数据")
		}
	}
}

// isTruthy 判断值是否为真
func isTruthy(v any) bool {
	if v == nil {
		return false
	}

	switch value := v.(type) {
	case bool:
		return value
	case int, int8, int16, int32, int64:
		return value != 0
	case uint, uint8, uint16, uint32, uint64:
		return value != 0
	case float32, float64:
		return value != 0
	case string:
		return value != ""
	case []any:
		return len(value) > 0
	case map[string]any:
		return len(value) > 0
	default:
		// 其他类型默认为真
		return true
	}
}

// GetType 获取条件类型
func (c *FunctionCondition) GetType() string {
	return c.ConditionType
}

// GetFunctionName 获取函数名称
func (c *FunctionCondition) GetFunctionName() string {
	return c.FunctionName
}

// GetOperation 获取操作类型
func (c *FunctionCondition) GetOperation() Operator {
	return c.Operation
}
