// Package condition 提供条件表达式系统的实现
package condition

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sync"

	"admin/pkg/rules/core"
	p "admin/pkg/rules/path"
)

// KeyGenerator 是一个生成缓存键的函数类型
// 根据数据生成一个字符串键，用于标识相同的数据
type KeyGenerator func(data any) (string, error)

// SmartCachedCondition 智能缓存条件
// 会根据数据内容自动判断是否使用缓存
// 当数据相同时使用缓存，不同时重新计算
type SmartCachedCondition struct {
	// 内部包装的实际条件
	innerCondition core.Condition

	// 缓存映射表，键为数据指纹，值为评估结果
	cache map[string]*cacheEntry

	// 默认的键生成器
	keyGenerator KeyGenerator

	// 缓存锁，保护并发访问
	mu sync.RWMutex

	// 最大缓存条目数
	maxCacheSize int

	// 是否启用缓存
	cacheEnabled bool

	// 缓存命中计数
	hitCount int

	// 缓存未命中计数
	missCount int
}

// cacheEntry 缓存条目
type cacheEntry struct {
	// 条件评估结果
	result bool

	// 评估错误
	err error

	// 最后访问时间（可用于LRU实现）
	lastAccess int64
}

// DefaultKeyGenerator 默认的键生成器
// 使用 JSON 序列化和 SHA256 哈希生成数据指纹
func DefaultKeyGenerator(data any) (string, error) {
	// 尝试将数据序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		// 如果不能序列化，使用字符串表示
		return fmt.Sprintf("%v", data), nil
	}

	// 计算SHA256哈希
	h := sha256.New()
	h.Write(jsonData)
	return hex.EncodeToString(h.Sum(nil)), nil
}

// NewSmartCachedCondition 创建一个新的智能缓存条件
func NewSmartCachedCondition(condition core.Condition) *SmartCachedCondition {
	return &SmartCachedCondition{
		innerCondition: condition,
		cache:          make(map[string]*cacheEntry),
		keyGenerator:   DefaultKeyGenerator,
		maxCacheSize:   100, // 默认缓存大小
		cacheEnabled:   true,
	}
}

// NewSmartCachedConditionWithKeyGen 创建一个带自定义键生成器的智能缓存条件
func NewSmartCachedConditionWithKeyGen(condition core.Condition, keyGen KeyGenerator) *SmartCachedCondition {
	cached := NewSmartCachedCondition(condition)
	if keyGen != nil {
		cached.keyGenerator = keyGen
	}
	return cached
}

// Evaluate 评估条件，自动处理缓存
func (c *SmartCachedCondition) Evaluate(ctx context.Context, data any) (bool, error) {
	if c.innerCondition == nil {
		return false, fmt.Errorf("内部条件为空")
	}

	// 如果缓存被禁用，直接评估
	if !c.cacheEnabled {
		return c.innerCondition.Evaluate(ctx, data)
	}

	// 生成缓存键
	key, err := c.keyGenerator(data)
	if err != nil {
		// 如果无法生成键，跳过缓存
		return c.innerCondition.Evaluate(ctx, data)
	}

	// 查找缓存
	c.mu.RLock()
	entry, found := c.cache[key]
	c.mu.RUnlock()

	if found {
		// 缓存命中
		c.mu.Lock()
		c.hitCount++
		c.mu.Unlock()

		if entry.err != nil {
			return false, entry.err
		}
		return entry.result, nil
	}

	// 缓存未命中，评估条件
	c.mu.Lock()
	c.missCount++
	c.mu.Unlock()

	result, err := c.innerCondition.Evaluate(ctx, data)

	// 缓存结果
	c.mu.Lock()
	defer c.mu.Unlock()

	// 检查缓存大小，如果达到上限可以实现LRU逻辑
	if len(c.cache) >= c.maxCacheSize {
		// 简单策略：清空缓存
		c.cache = make(map[string]*cacheEntry)
	}

	c.cache[key] = &cacheEntry{
		result:     result,
		err:        err,
		lastAccess: 0, // 可以设置为当前时间戳
	}

	return result, err
}

// GetType 获取条件类型
func (c *SmartCachedCondition) GetType() string {
	if typer, ok := c.innerCondition.(interface{ GetType() string }); ok {
		return typer.GetType()
	}
	return "smart_cached"
}

// GetPaths 获取条件中使用的路径
func (c *SmartCachedCondition) GetPaths() []string {
	if pathsProvider, ok := c.innerCondition.(interface{ GetPaths() []string }); ok {
		return pathsProvider.GetPaths()
	}
	return nil
}

// SetMaxCacheSize 设置最大缓存条目数
func (c *SmartCachedCondition) SetMaxCacheSize(size int) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.maxCacheSize = size

	// 如果当前缓存超过新大小，清空
	if len(c.cache) > size {
		c.cache = make(map[string]*cacheEntry)
	}
}

// SetCacheEnabled 启用或禁用缓存
func (c *SmartCachedCondition) SetCacheEnabled(enabled bool) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.cacheEnabled = enabled

	// 如果禁用缓存，清空当前缓存
	if !enabled {
		c.cache = make(map[string]*cacheEntry)
	}
}

// ClearCache 清空缓存
func (c *SmartCachedCondition) ClearCache() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.cache = make(map[string]*cacheEntry)
}

// GetCacheStats 获取缓存统计信息
func (c *SmartCachedCondition) GetCacheStats() (hits, misses int, hitRate float64) {
	c.mu.RLock()
	defer c.mu.RUnlock()

	hits = c.hitCount
	misses = c.missCount
	total := hits + misses

	if total > 0 {
		hitRate = float64(hits) / float64(total)
	}

	return
}

// NewSmartCachedAndCondition 创建一个带智能缓存的AND条件
func NewSmartCachedAndCondition(conditions ...core.Condition) *SmartCachedCondition {
	return NewSmartCachedCondition(NewAndCondition(conditions...))
}

// NewSmartCachedOrCondition 创建一个带智能缓存的OR条件
func NewSmartCachedOrCondition(conditions ...core.Condition) *SmartCachedCondition {
	return NewSmartCachedCondition(NewOrCondition(conditions...))
}

// NewSmartCachedNotCondition 创建一个带智能缓存的NOT条件
func NewSmartCachedNotCondition(condition core.Condition) *SmartCachedCondition {
	return NewSmartCachedCondition(NewNotCondition(condition))
}

// PathHashKeyGenerator 基于指定路径的数据创建缓存键
// 只考虑特定路径的数据而忽略其他数据
func PathHashKeyGenerator(paths []string) KeyGenerator {
	return func(data any) (string, error) {
		parser := p.NewPathParser()
		values := make(map[string]interface{})

		for _, path := range paths {
			val, err := parser.GetValue(data, path)
			if err == nil {
				values[path] = val
			}
		}

		// 将提取的值序列化为JSON并计算哈希
		jsonData, err := json.Marshal(values)
		if err != nil {
			return fmt.Sprintf("%v", values), nil
		}

		h := sha256.New()
		h.Write(jsonData)
		return hex.EncodeToString(h.Sum(nil)), nil
	}
}

// NewSmartCachedConditionWithPaths 创建一个只基于特定路径的智能缓存条件
// 只有当这些路径的数据变化时才重新计算条件
func NewSmartCachedConditionWithPaths(condition core.Condition, paths []string) *SmartCachedCondition {
	// 如果没有指定路径，尝试从条件中获取
	if len(paths) == 0 {
		if pathsProvider, ok := condition.(interface{ GetPaths() []string }); ok {
			paths = pathsProvider.GetPaths()
		}
	}

	return NewSmartCachedConditionWithKeyGen(condition, PathHashKeyGenerator(paths))
}
