package engine

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"admin/pkg/rules/action"
	"admin/pkg/rules/condition"
	"admin/pkg/rules/core"
	"admin/pkg/rules/function"
)

// TestRuleEngineComprehensive 规则引擎综合测试
// 测试包括:
// 1. 各种条件类型 (简单条件、复合条件、时间条件、正则条件、函数条件)
// 2. 不同的规则优先级
// 3. 顺序执行与并发执行
// 4. 优化器效果
func TestRuleEngineComprehensive(t *testing.T) {
	// 创建测试数据 - 使用指针包装确保数据可寻址
	userData := map[string]any{
		"user": map[string]any{
			"id":       1001,
			"name":     "张三",
			"age":      30,
			"points":   85,
			"vip":      true,
			"tags":     []string{"活跃", "新客户", "已认证"},
			"city":     "北京",
			"joinTime": "2025-01-15T10:30:00Z",
			"orders": []map[string]interface{}{
				{
					"id":          "ORD-001",
					"amount":      299.99,
					"createTime":  "2025-04-01T10:00:00Z",
					"status":      "已完成",
					"productName": "高级会员",
					"tags":        &[]string{"会员", "自动续费"}, // 使用指针
				},
				{
					"id":          "ORD-002",
					"amount":      99.50,
					"createTime":  "2025-04-10T14:20:00Z",
					"status":      "已完成",
					"productName": "季度课程",
					"tags":        &[]string{"课程", "已完成"}, // 使用指针
				},
			},
			"preferences": map[string]interface{}{
				"emailNotification": true,
				"theme":             "dark",
				"language":          "zh-CN",
			},
		},
		"context": map[string]interface{}{
			"deviceType":  "mobile",
			"appVersion":  "2.5.3",
			"requestTime": "2025-04-15T10:30:00Z",
			"source":      "app",
		},
	}

	// 测试各种配置的规则引擎
	testCases := []struct {
		name              string
		concurrent        bool
		optimizationLevel int
		maxWorkers        int
	}{
		{"顺序执行-无优化", false, 0, 1},
		{"顺序执行-基础优化", false, 1, 1},
		{"顺序执行-高级优化", false, 3, 1},
		{"并发执行-无优化", true, 0, 5},
		{"并发执行-高级优化", true, 3, 5},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			engine, rules := createTestRuleEngine(tc.concurrent, tc.optimizationLevel, tc.maxWorkers)

			testRuleExecution(t, engine, rules, userData)
		})
	}
}

// createTestRuleEngine 创建测试规则引擎
func createTestRuleEngine(concurrent bool, optimizationLevel, maxWorkers int) (*core.DefaultRuleEngine, []core.Rule) {
	// 创建规则引擎
	engine := core.NewDefaultRuleEngine(concurrent, maxWorkers)

	// 应用优化级别
	if optimizationLevel > 0 {
		optimizer := condition.NewAdvancedConditionOptimizer()

		// 根据优化级别配置优化器
		switch optimizationLevel {
		case 1: // 基础优化
			optimizer.SetLazyEvaluationEnabled(true)
			optimizer.SetConditionMergingEnabled(false)
			optimizer.SetAdaptiveReorderingEnabled(false)
		case 2: // 中级优化
			optimizer.SetLazyEvaluationEnabled(true)
			optimizer.SetConditionMergingEnabled(true)
			optimizer.SetAdaptiveReorderingEnabled(false)
		case 3: // 高级优化
			optimizer.SetLazyEvaluationEnabled(true)
			optimizer.SetConditionMergingEnabled(true)
			optimizer.SetAdaptiveReorderingEnabled(true)
		}

		// 设置优化器
		engine.SetOptimizer(optimizer)
		engine.SetOptimizationEnabled(true)
	} else {
		engine.SetOptimizationEnabled(false)
	}

	// 创建各种类型的规则
	rules := createTestRules()

	// 添加规则到引擎
	for _, rule := range rules {
		_ = engine.AddRule(rule)
	}

	return engine, rules
}

// createTestRules 创建测试规则集合
func createTestRules() []core.Rule {
	rules := []core.Rule{
		createAgeBasedRule(),          // 基于年龄的简单规则
		createVipCompositeRule(),      // VIP复合规则
		createOrderAmountRule(),       // 订单金额规则
		createPreferenceRegexRule(),   // 偏好设置正则规则
		createTimeBasedRule(),         // 时间条件规则
		createFunctionBasedRule(),     // 函数条件规则
		createAdaptiveCompositeRule(), // 自适应复合规则
	}

	return rules
}

// createAgeBasedRule 创建基于年龄的简单规则
func createAgeBasedRule() core.Rule {
	// 年龄大于25岁的用户标记为"成年用户"
	ageCondition := condition.NewSimpleCondition("user.age", condition.OpGreaterThan, 25)
	actions := []core.Action{
		action.NewAppendToArrayAction("user.tags", "成年用户"),
	}

	return core.NewRule(
		"年龄验证规则",
		"年龄大于25岁的用户标记为'成年用户'",
		3, // 优先级
		ageCondition,
		actions,
		true,
	)
}

// createVipCompositeRule 创建VIP复合规则
func createVipCompositeRule() core.Rule {
	// VIP用户且在北京，增加100积分
	vipCondition := condition.NewSimpleCondition("user.vip", condition.OpEqual, true)
	cityCondition := condition.NewSimpleCondition("user.city", condition.OpEqual, "北京")
	compositeCondition := condition.NewAndCondition(vipCondition, cityCondition)

	actions := []core.Action{
		action.NewModifyPropertyAction("user.points", action.OpAdd, 100),
		action.NewAppendToArrayAction("user.tags", "VIP专享"),
	}

	return core.NewRule(
		"VIP城市规则",
		"VIP用户且在北京，增加100积分并添加标签",
		2, // 优先级
		compositeCondition,
		actions,
		true,
	)
}

// createOrderAmountRule 创建订单金额规则
func createOrderAmountRule() core.Rule {
	// 所有订单总金额超过300，设置为"高价值客户"
	orderAmountCondition := condition.NewSimpleCondition("user.orders[0].amount", condition.OpGreaterThan, 200)

	actions := []core.Action{
		action.NewAppendToArrayAction("user.tags", "高价值客户"),
	}

	return core.NewRule(
		"高价值客户规则",
		"订单金额超过200，标记为高价值客户",
		4, // 优先级
		orderAmountCondition,
		actions,
		true,
	)
}

// createPreferenceRegexRule 创建偏好设置正则规则
func createPreferenceRegexRule() core.Rule {
	// 语言设置包含"zh"的用户，设置为"中文用户"
	regexCondition, _ := condition.NewRegexCondition("user.preferences.language", "^zh.*", false)

	actions := []core.Action{
		action.NewAppendToArrayAction("user.tags", "中文用户"),
		// action.NewSetPropertyAction("user.tags[5]", "中文用户"),
	}

	return core.NewRule(
		"语言偏好规则",
		"使用中文的用户添加标签",
		5, // 优先级
		regexCondition,
		actions,
		true,
	)
}

// createTimeBasedRule 创建时间条件规则
func createTimeBasedRule() core.Rule {
	// 30天内加入的用户为"新用户"
	timeCondition := condition.NewTimeCondition(condition.TimeOpInLast, false, "30d")
	timeCondition.WithBaseTime("user.joinTime", true)
	action.NewFunctionAction("createUser", "新用户", func(ctx context.Context, data any, params map[string]any) error {
		// 执行一些操作
		return nil
	}, nil)
	actions := []core.Action{
		action.NewAppendToArrayAction("user.tags", "新用户"),
	}

	return core.NewRule(
		"新用户规则",
		"30天内加入的用户标记为新用户",
		1, // 最高优先级
		timeCondition,
		actions,
		true,
	)
}

// createFunctionBasedRule 创建函数条件规则
func createFunctionBasedRule() core.Rule {
	// 创建函数注册表
	registry := function.NewFunctionRegistry()

	// 注册计算平均订单金额的函数
	err := registry.RegisterFunction("calculateAvgOrderAmount", func(orders []map[string]interface{}) float64 {
		if len(orders) == 0 {
			return 0
		}

		var total float64
		for _, order := range orders {
			if amount, ok := order["amount"].(float64); ok {
				total += amount
			}
		}

		return total / float64(len(orders))
	}, "计算平均订单金额")
	if err != nil {
	}

	// 创建函数条件：平均订单金额大于150
	funcCondition := condition.NewFunctionCondition(
		"calculateAvgOrderAmount",
		[]string{"user.orders"},
		condition.FuncOpGreater,
		150.0,
		registry,
	)

	actions := []core.Action{
		action.NewAppendToArrayAction("user.tags", "高平均消费"),
	}

	return core.NewRule(
		"高平均消费规则",
		"平均订单金额大于150的用户标记为高平均消费",
		6, // 优先级
		funcCondition,
		actions,
		true,
	)
}

// createAdaptiveCompositeRule 创建自适应复合规则 (包含嵌套)
func createAdaptiveCompositeRule() core.Rule {
	// 复杂条件:
	// (年龄 > 25 AND 积分 > 80)
	// AND
	// (是VIP OR 城市是北京)
	// NOT (语言是英文)

	// 子条件1: 年龄 > 25 AND 积分 > 80
	conditionAge := condition.NewSimpleCondition("user.age", condition.OpGreaterThan, 25)
	conditionPoints := condition.NewSimpleCondition("user.points", condition.OpGreaterThan, 80)
	compositeAnd1 := condition.NewAndCondition(conditionAge, conditionPoints)

	// 子条件2: 是VIP OR 城市是北京
	conditionVip := condition.NewSimpleCondition("user.vip", condition.OpEqual, true)
	conditionCity := condition.NewSimpleCondition("user.city", condition.OpEqual, "北京")
	compositeOr := condition.NewOrCondition(conditionVip, conditionCity)

	// 子条件3: NOT (语言是英文)
	conditionLang := condition.NewSimpleCondition("user.preferences.language", condition.OpEqual, "en-US")
	compositeNot := condition.NewNotCondition(conditionLang)

	// 创建自适应AND条件，组合上述三个复合/简单条件
	adaptiveAnd := condition.NewAdaptiveAndCondition(compositeAnd1, compositeOr, compositeNot)
	adaptiveAnd.SetReorderInterval(time.Second * 2) // 设置重排序间隔

	actions := []core.Action{
		action.NewLogAction("用户符合优质会员条件", "info", ""),
		action.NewAppendToArrayAction("user.tags", "优质会员"),
	}

	return core.NewRule(
		"优质会员规则 (嵌套条件)",
		"符合复杂嵌套条件的会员标记为优质会员",
		7, // 优先级
		adaptiveAnd,
		actions,
		true,
	)
}

// testRuleExecution 测试规则执行
func testRuleExecution(t *testing.T, engine *core.DefaultRuleEngine, rules []core.Rule, data map[string]interface{}) {
	ctx := context.Background()

	// 执行规则
	startTime := time.Now()
	result, err := engine.Execute(ctx, data)
	duration := time.Since(startTime)

	// 检查结果
	if err != nil {
		t.Fatalf("规则执行失败: %v", err)
	}

	// 输出执行结果
	t.Logf("规则执行时间: %v", duration)
	t.Logf("匹配规则数: %d", len(result.Matches))

	// 按优先级排序匹配结果
	if len(result.Matches) > 0 {
		matchedRules := make([]string, 0, len(result.Matches))
		for _, match := range result.Matches {
			matchedRules = append(matchedRules, fmt.Sprintf("%s (优先级: %d)", match.Rule.GetName(), match.Rule.GetPriority()))
		}

		t.Logf("匹配的规则: %v", matchedRules)
	}

	// 输出执行后的数据变化
	if tags, ok := getNestedValue(data, "user.tags"); ok {
		t.Logf("用户标签: %v", tags)
	}

	if points, ok := getNestedValue(data, "user.points"); ok {
		t.Logf("用户积分: %v", points)
	}
}

// getNestedValue 获取嵌套数据结构中的值
func getNestedValue(data map[string]interface{}, path string) (interface{}, bool) {
	var result interface{} = data

	for _, part := range strings.Split(path, ".") {
		m, ok := result.(map[string]interface{})
		if !ok {
			return nil, false
		}

		result, ok = m[part]
		if !ok {
			return nil, false
		}
	}

	return result, true
}
