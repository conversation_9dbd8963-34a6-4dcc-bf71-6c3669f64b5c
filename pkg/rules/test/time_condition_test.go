package test

import (
	"context"
	"testing"
	"time"

	"admin/pkg/rules/condition"
)

// 模拟时间源，用于测试中固定时间
type MockTimeSource struct {
	CurrentTime time.Time
}

func (m *MockTimeSource) Now() time.Time {
	return m.CurrentTime
}

// 辅助函数，用于创建指定时间
func createTime(year, month, day, hour, min, sec int) time.Time {
	return time.Date(year, time.Month(month), day, hour, min, sec, 0, time.UTC)
}

// TestTimeConditionBasicComparison 测试基本时间比较操作
func TestTimeConditionBasicComparison(t *testing.T) {
	baseTime := createTime(2025, 4, 15, 10, 0, 0)   // 2025-04-15 10:00:00
	targetTime := createTime(2025, 4, 15, 12, 0, 0) // 2025-04-15 12:00:00

	tests := []struct {
		name      string
		operation condition.Operator
		expected  bool
	}{
		{"Before操作-基准时间早于目标时间", condition.TimeOpBefore, true},
		{"After操作-基准时间早于目标时间", condition.TimeOpAfter, false},
		{"Equal操作-基准时间不等于目标时间", condition.TimeOpEqual, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建时间条件
			timeCondition := condition.NewTimeCondition(tt.operation, false, targetTime)
			timeCondition.WithBaseTime(baseTime, false)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeConditionRangeComparison 测试时间范围比较
func TestTimeConditionRangeComparison(t *testing.T) {
	time1 := createTime(2025, 4, 10, 0, 0, 0) // 2025-04-10
	time2 := createTime(2025, 4, 20, 0, 0, 0) // 2025-04-20

	tests := []struct {
		name      string
		baseTime  time.Time
		operation condition.Operator
		expected  bool
	}{
		{
			name:      "Between操作-时间在范围内",
			baseTime:  createTime(2025, 4, 15, 0, 0, 0), // 2025-04-15
			operation: condition.TimeOpBetween,
			expected:  true,
		},
		{
			name:      "Between操作-时间在范围外",
			baseTime:  createTime(2025, 4, 25, 0, 0, 0), // 2025-04-25
			operation: condition.TimeOpBetween,
			expected:  false,
		},
		{
			name:      "NotBetween操作-时间在范围外",
			baseTime:  createTime(2025, 4, 25, 0, 0, 0), // 2025-04-25
			operation: condition.TimeOpNotBetween,
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建时间范围条件
			timeCondition := condition.NewTimeCondition(tt.operation, false, time1, time2)
			timeCondition.WithBaseTime(tt.baseTime, false)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeConditionRelativeTimeWindow 测试相对时间窗口操作
func TestTimeConditionRelativeTimeWindow(t *testing.T) {
	now := createTime(2025, 4, 15, 0, 0, 0) // 2025-04-15

	tests := []struct {
		name      string
		baseTime  time.Time
		operation condition.Operator
		duration  string
		expected  bool
	}{
		{
			name:      "InNext操作-在未来3天内",
			baseTime:  createTime(2025, 4, 16, 0, 0, 0), // 2025-04-16
			operation: condition.TimeOpInNext,
			duration:  "3d",
			expected:  true,
		},
		{
			name:      "InNext操作-不在未来3天内",
			baseTime:  createTime(2025, 4, 19, 0, 0, 0), // 2025-04-19
			operation: condition.TimeOpInNext,
			duration:  "3d",
			expected:  false,
		},
		{
			name:      "InLast操作-在过去3天内",
			baseTime:  createTime(2025, 4, 13, 0, 0, 0), // 2025-04-13
			operation: condition.TimeOpInLast,
			duration:  "3d",
			expected:  true,
		},
		{
			name:      "InLast操作-不在过去3天内",
			baseTime:  createTime(2025, 4, 10, 0, 0, 0), // 2025-04-10
			operation: condition.TimeOpInLast,
			duration:  "3d",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模拟时间源
			mockTimeSource := &MockTimeSource{CurrentTime: now}

			// 创建时间窗口条件
			timeCondition := condition.NewTimeCondition(tt.operation, false, tt.duration)
			timeCondition.WithBaseTime(tt.baseTime, false)
			timeCondition.WithTimeSource(mockTimeSource)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeConditionCalendarUnits 测试日历单元操作
func TestTimeConditionCalendarUnits(t *testing.T) {
	tests := []struct {
		name       string
		baseTime   time.Time
		targetTime time.Time
		operation  condition.Operator
		expected   bool
	}{
		{
			name:       "IsSameDay操作-同一天",
			baseTime:   createTime(2025, 4, 15, 10, 0, 0),
			targetTime: createTime(2025, 4, 15, 15, 0, 0),
			operation:  condition.TimeOpIsSameDay,
			expected:   true,
		},
		{
			name:       "IsSameDay操作-不同天",
			baseTime:   createTime(2025, 4, 15, 10, 0, 0),
			targetTime: createTime(2025, 4, 16, 15, 0, 0),
			operation:  condition.TimeOpIsSameDay,
			expected:   false,
		},
		{
			name:       "IsSameMonth操作-同一月",
			baseTime:   createTime(2025, 4, 15, 10, 0, 0),
			targetTime: createTime(2025, 4, 20, 15, 0, 0),
			operation:  condition.TimeOpIsSameMonth,
			expected:   true,
		},
		{
			name:       "IsSameYear操作-同一年",
			baseTime:   createTime(2025, 4, 15, 10, 0, 0),
			targetTime: createTime(2025, 7, 20, 15, 0, 0),
			operation:  condition.TimeOpIsSameYear,
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建日历单元条件
			timeCondition := condition.NewTimeCondition(tt.operation, false, tt.targetTime)
			timeCondition.WithBaseTime(tt.baseTime, false)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeConditionWeekdayOperations 测试工作日和周末判断操作
func TestTimeConditionWeekdayOperations(t *testing.T) {
	// 创建SimplHolidayProvider并设置特殊日期
	holidayProvider := condition.NewSimpleHolidayProvider()
	_ = holidayProvider.AddSpecialDayStr("2025-04-15", false) // 设置为工作日
	_ = holidayProvider.AddSpecialDayStr("2025-04-19", true)  // 设置为假期（尽管是周六）

	tests := []struct {
		name      string
		baseTime  time.Time
		operation condition.Operator
		target    interface{}
		expected  bool
	}{
		{
			name:      "DayOfWeek操作-周二",
			baseTime:  createTime(2025, 4, 15, 0, 0, 0), // 2025-04-15 星期二
			operation: condition.TimeOpDayOfWeek,
			target:    2, // 星期二
			expected:  true,
		},
		{
			name:      "DayOfWeek操作-周三",
			baseTime:  createTime(2025, 4, 15, 0, 0, 0), // 2025-04-15 星期二
			operation: condition.TimeOpDayOfWeek,
			target:    3, // 星期三
			expected:  false,
		},
		{
			name:      "IsWorkday操作-工作日",
			baseTime:  createTime(2025, 4, 15, 0, 0, 0), // 2025-04-15 特殊设置为工作日
			operation: condition.TimeOpIsWorkday,
			expected:  true,
		},
		{
			name:      "IsWeekend操作-周六设为假期",
			baseTime:  createTime(2025, 4, 19, 0, 0, 0), // 2025-04-19 周六
			operation: condition.TimeOpIsWeekend,
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var timeCondition *condition.TimeCondition

			if tt.target != nil {
				timeCondition = condition.NewTimeCondition(tt.operation, false, tt.target)
			} else {
				timeCondition = condition.NewTimeCondition(tt.operation, false)
			}

			timeCondition.WithBaseTime(tt.baseTime, false)
			timeCondition.WithHolidayProvider(holidayProvider)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeParser 测试时间解析器
func TestTimeParser(t *testing.T) {
	parser := condition.NewDefaultTimeParser()

	tests := []struct {
		name  string
		input interface{}
		valid bool
	}{
		{"ISO8601格式", "2025-04-15T10:30:00Z", true},
		{"日期格式", "2025-04-15", true},
		{"日期时间格式", "2025-04-15 10:30:00", true},
		{"时间戳格式", int64(**********), true}, // 2025-04-15T10:30:00Z
		{"中文日期格式", "2025年04月15日", true},
		{"无效格式", "invalid-date", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := parser.ParseTime(tt.input)

			if tt.valid && err != nil {
				t.Errorf("期望解析成功，但得到错误: %v", err)
			}

			if !tt.valid && err == nil {
				t.Error("期望解析失败，但成功了")
			}
		})
	}
}

// TestTimeConditionWithPath 测试使用路径获取时间值的场景
func TestTimeConditionWithPath(t *testing.T) {
	// 模拟数据
	data := map[string]interface{}{
		"order": map[string]interface{}{
			"createTime": "2025-04-10T10:00:00Z",
			"expireTime": "2025-04-20T10:00:00Z",
			"duration":   "5d",
		},
	}

	// 基准时间 2025-04-15
	baseTime := createTime(2025, 4, 15, 0, 0, 0)

	// 创建模拟时间源，用于 InNext 操作
	mockTimeSource := &MockTimeSource{CurrentTime: baseTime}

	tests := []struct {
		name      string
		operation condition.Operator
		target    string
		target2   interface{}
		expected  bool
	}{
		{
			name:      "使用路径-Before操作",
			operation: condition.TimeOpBefore,
			target:    "order.expireTime",
			expected:  true,
		},
		{
			name:      "使用路径-After操作",
			operation: condition.TimeOpAfter,
			target:    "order.createTime",
			expected:  true,
		},
		{
			name:      "使用路径-InNext操作",
			operation: condition.TimeOpInNext,
			target:    "order.expireTime", // 改回使用过期时间作为目标时间点
			target2:   "10d",              // 明确指定持续时间为10天
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var timeCondition *condition.TimeCondition

			if tt.target2 != nil {
				timeCondition = condition.NewTimeCondition(tt.operation, true, tt.target, tt.target2)
			} else {
				timeCondition = condition.NewTimeCondition(tt.operation, true, tt.target)
			}

			timeCondition.WithBaseTime(baseTime, false)

			// 为所有测试设置时间源，确保时间计算一致性
			timeCondition.WithTimeSource(mockTimeSource)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), data)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeConditionDateRangeCheck 测试日期范围检查
func TestTimeConditionDateRangeCheck(t *testing.T) {
	targetDate := createTime(2025, 4, 15, 0, 0, 0) // 2025-04-15

	tests := []struct {
		name      string
		baseTime  time.Time
		operation condition.Operator
		expected  bool
	}{
		{
			name:      "InDay操作-在同一天",
			baseTime:  createTime(2025, 4, 15, 10, 0, 0),
			operation: condition.TimeOpInDay,
			expected:  true,
		},
		{
			name:      "InDay操作-不在同一天",
			baseTime:  createTime(2025, 4, 16, 10, 0, 0),
			operation: condition.TimeOpInDay,
			expected:  false,
		},
		{
			name:      "InMonth操作-在同一月",
			baseTime:  createTime(2025, 4, 20, 10, 0, 0),
			operation: condition.TimeOpInMonth,
			expected:  true,
		},
		{
			name:      "InYear操作-在同一年",
			baseTime:  createTime(2025, 7, 20, 10, 0, 0),
			operation: condition.TimeOpInYear,
			expected:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			timeCondition := condition.NewTimeCondition(tt.operation, false, targetDate)
			timeCondition.WithBaseTime(tt.baseTime, false)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}

// TestTimeConditionDateListCheck 测试日期列表检查
func TestTimeConditionDateListCheck(t *testing.T) {
	// 使用完全一致的时间格式，去掉时分秒部分
	dateList := []interface{}{
		createTime(2025, 4, 15, 0, 0, 0), // 与测试的基准时间使用相同类型
		"2025-04-20",
		"2025-05-01",
	}

	tests := []struct {
		name     string
		baseTime time.Time
		expected bool
	}{
		{
			name:     "在日期列表中",
			baseTime: createTime(2025, 4, 15, 0, 0, 0), // 去掉时分秒，只保留日期部分
			expected: true,
		},
		{
			name:     "不在日期列表中",
			baseTime: createTime(2025, 4, 16, 0, 0, 0),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			timeCondition := condition.NewTimeCondition(condition.TimeOpIsInDateList, false, dateList)
			timeCondition.WithBaseTime(tt.baseTime, false)

			// 在评估之前添加调试信息
			t.Logf("测试场景: %s, 基准时间: %v, 日期列表: %v", tt.name, tt.baseTime, dateList)

			// 评估条件
			result, err := timeCondition.Evaluate(context.Background(), nil)
			if err != nil {
				t.Fatalf("预期无错误，但得到: %v", err)
			}

			if result != tt.expected {
				t.Errorf("期望结果 %v, 但得到 %v", tt.expected, result)
			}
		})
	}
}
