// Package dsl_test 提供规则引擎DSL表达式的测试
package dsl_test

import (
	"context"
	"encoding/json"
	"testing"

	"admin/pkg/rules/core"
	"admin/pkg/rules/dsl"
)

// TestMoreDSLExpressions 测试更多DSL表达式情况
func TestMoreDSLExpressions(t *testing.T) {
	// 创建测试数据
	userData := map[string]any{
		"user": map[string]any{
			"id":        1001,
			"name":      "张三",
			"age":       30,
			"points":    85,
			"vip":       true,
			"tags":      []string{"活跃", "新客户", "已认证"},
			"city":      "北京",
			"joinTime":  "2025-01-15T10:30:00Z",
			"score":     0,
			"emptyList": []string{},
			"nullField": nil,
			"orders": []map[string]any{
				{
					"id":          "ORD-001",
					"amount":      299.99,
					"createTime":  "2025-04-01T10:00:00Z",
					"status":      "已完成",
					"productName": "高级会员",
					"tags":        []string{"会员", "自动续费"},
				},
			},
		},
	}

	testCases := []struct {
		name            string
		dslJSON         string
		expectedResult  bool
		expectedErrText string
	}{
		// 测试更多操作符
		{
			name: "less than - 年龄小于40",
			dslJSON: `{
				"type": "simple",
				"path": "user.age",
				"operator": "lt",
				"value": 40
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "less than or equal - 积分小于等于85",
			dslJSON: `{
				"type": "simple",
				"path": "user.points",
				"operator": "lte",
				"value": 85
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "in - 城市在列表中",
			dslJSON: `{
				"type": "simple",
				"path": "user.city",
				"operator": "in",
				"value": ["上海", "北京", "广州"]
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "not_in - 城市不在列表中",
			dslJSON: `{
				"type": "simple",
				"path": "user.city",
				"operator": "not_in",
				"value": ["上海", "广州", "深圳"]
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "starts_with - 姓名以张开头",
			dslJSON: `{
				"type": "simple",
				"path": "user.name",
				"operator": "starts_with",
				"value": "张"
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "ends_with - 城市以京结尾",
			dslJSON: `{
				"type": "simple",
				"path": "user.city",
				"operator": "ends_with",
				"value": "京"
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "exists - 路径存在",
			dslJSON: `{
				"type": "simple",
				"path": "user.age",
				"operator": "exists",
				"value": null
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "empty - 检查空数组",
			dslJSON: `{
				"type": "simple",
				"path": "user.emptyList",
				"operator": "empty",
				"value": null
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name: "not_empty - 检查非空数组",
			dslJSON: `{
				"type": "simple",
				"path": "user.tags",
				"operator": "not_empty",
				"value": null
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},

		// 测试无效和边缘情况
		{
			name: "缺少路径",
			dslJSON: `{
				"type": "simple",
				"operator": "eq",
				"value": 30
			}`,
			expectedResult:  false,
			expectedErrText: "缺少必要字段",
		},
		{
			name: "缺少操作符",
			dslJSON: `{
				"type": "simple",
				"path": "user.age",
				"value": 30
			}`,
			expectedResult:  false,
			expectedErrText: "缺少必要字段",
		},
		{
			name: "无效的复合操作符",
			dslJSON: `{
				"type": "composite",
				"operator": "invalid_logic",
				"expressions": [
					{
						"type": "simple",
						"path": "user.age",
						"operator": "eq",
						"value": 30
					}
				]
			}`,
			expectedResult:  false,
			expectedErrText: "无效的操作符",
		},
		{
			name: "not操作符多个表达式",
			dslJSON: `{
				"type": "composite",
				"operator": "not",
				"expressions": [
					{
						"type": "simple",
						"path": "user.age",
						"operator": "eq",
						"value": 30
					},
					{
						"type": "simple",
						"path": "user.vip",
						"operator": "eq",
						"value": true
					}
				]
			}`,
			expectedResult:  false,
			expectedErrText: "NOT 操作符只能有一个条件",
		},
		{
			name: "复合表达式没有子表达式",
			dslJSON: `{
				"type": "composite",
				"operator": "and",
				"expressions": []
			}`,
			expectedResult:  false,
			expectedErrText: "缺少必要字段",
		},
		{
			name: "无效的JSON",
			dslJSON: `{
				"type": "simple",
				"path": "user.age",
				"operator": "eq",
				"value": 30,
			}`, // 多余的逗号
			expectedResult:  false,
			expectedErrText: "解析JSON失败",
		},
		{
			name: "路径不存在",
			dslJSON: `{
				"type": "simple",
				"path": "user.nonexistent",
				"operator": "eq",
				"value": "some value"
			}`,
			expectedResult:  false,
			expectedErrText: "",
		},
		{
			name: "非布尔结果用于复合条件",
			dslJSON: `{
				"type": "composite",
				"operator": "and",
				"expressions": [
					{
						"type": "simple",
						"path": "user.age",
						"operator": "eq",
						"value": 30
					},
					{
						"type": "simple",
						"path": "user.score", 
						"operator": "eq",
						"value": 0
					}
				]
			}`,
			expectedResult:  true,
			expectedErrText: "",
		},
		{
			name:            "空字符串DSL",
			dslJSON:         "",
			expectedResult:  false,
			expectedErrText: "无效的DSL表达式",
		},
		{
			name:            "空白字符串DSL",
			dslJSON:         "   \n  \t  ",
			expectedResult:  false,
			expectedErrText: "无效的DSL表达式",
		},
		{
			name: "检查存在但无效的路径语法",
			dslJSON: `{
				"type": "simple",
				"path": "user.[0].invalid",
				"operator": "exists",
				"value": null
			}`,
			expectedResult:  false,
			expectedErrText: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 解析DSL表达式
			expression, err := dsl.ParseDSL(tc.dslJSON)
			if err != nil {
				if tc.expectedErrText == "" {
					t.Fatalf("解析DSL失败，未预期的错误: %v", err)
				} else if !containsErrorText(err.Error(), tc.expectedErrText) {
					t.Fatalf("预期错误文本包含 '%s'，但得到: '%s'", tc.expectedErrText, err.Error())
				}
				return
			}

			if tc.expectedErrText != "" {
				t.Fatalf("预期出现错误包含 '%s'，但没有错误", tc.expectedErrText)
			}

			// 创建DSL条件
			dslCondition, err := dsl.NewDSLCondition(expression)
			if err != nil {
				t.Fatalf("创建DSL条件失败: %v", err)
			}

			// 评估条件
			ctx := context.Background()
			result, err := dslCondition.Evaluate(ctx, userData)
			if err != nil {
				t.Fatalf("评估条件失败: %v", err)
			}

			// 验证结果
			if result != tc.expectedResult {
				t.Errorf("条件评估结果不符: 预期 %v, 实际 %v", tc.expectedResult, result)
				// 打印表达式内容以便调试
				exprJSON, _ := json.MarshalIndent(expression, "", "  ")
				t.Logf("表达式: %s", exprJSON)
			}
		})
	}
}

func TestDSLRuleIntegration(t *testing.T) {
	// 创建测试数据
	userData := map[string]any{
		"user": map[string]any{
			"id":     1001,
			"name":   "张三",
			"age":    30,
			"points": 85,
			"vip":    true,
			"tags":   []string{"活跃", "新客户"},
			"city":   "北京",
		},
	}

	// 创建复杂DSL表达式的规则
	complexDSL := `{
		"type": "composite",
		"operator": "and",
		"expressions": [
			{
				"type": "composite",
				"operator": "or",
				"expressions": [
					{
						"type": "simple",
						"path": "user.age",
						"operator": "gt",
						"value": 25
					},
					{
						"type": "simple",
						"path": "user.city",
						"operator": "eq",
						"value": "北京"
					}
				]
			},
			{
				"type": "simple",
				"path": "user.vip",
				"operator": "eq",
				"value": true
			}
		]
	}`

	// 解析DSL并创建条件
	dslCondition, err := dsl.DSLConditionFromString(complexDSL)
	if err != nil {
		t.Fatalf("从DSL字符串创建条件失败: %v", err)
	}

	// 创建规则引擎
	engine := core.NewDefaultRuleEngine(false, 1)

	// 创建一个使用DSL条件的规则
	rule := core.NewRule(
		"基于DSL的规则",
		"使用复杂DSL表达式的规则",
		1,
		dslCondition,
		[]core.Action{
			// 通常这里会有动作，如修改属性等
		},
		true,
	)

	// 添加规则到引擎
	err = engine.AddRule(rule)
	if err != nil {
		t.Fatalf("添加规则失败: %v", err)
	}

	// 执行规则
	ctx := context.Background()
	result, err := engine.Execute(ctx, userData)
	if err != nil {
		t.Fatalf("规则执行失败: %v", err)
	}

	// 验证规则是否匹配
	if len(result.Matches) != 1 {
		t.Errorf("预期匹配1个规则，实际匹配了%d个", len(result.Matches))
	}
}

// containsErrorText 检查错误消息是否包含指定文本
func containsErrorText(errorMsg, expectedText string) bool {
	return errorMsg != "" && expectedText != "" && (errorMsg == expectedText ||
		(len(errorMsg) >= len(expectedText) && errorMsg[0:len(expectedText)] == expectedText))
}
