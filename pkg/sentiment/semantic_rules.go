package sentiment

import "slices"

// 动词+宾语组合的特殊情感规则
func (a *Analyzer) getVerbObjectSentiment(tokens []TokenWithPOS, verbIndex int) *EmotionScore {
	if verbIndex < 0 || verbIndex >= len(tokens) {
		return nil
	}

	verb := tokens[verbIndex].Word

	// 检查动词+后续宾语的组合
	if verbIndex < len(tokens)-1 {
		object := tokens[verbIndex+1].Word
		if score := a.checkVerbObjectCombination(verb, object); score != nil {
			return score
		}
	}

	// 检查前置宾语+动词的组合（被动语态等）
	if verbIndex > 0 {
		subject := tokens[verbIndex-1].Word
		if score := a.checkObjectVerbCombination(subject, verb); score != nil {
			return score
		}
	}

	return nil
}

// 检查动词+宾语组合（使用配置）
func (a *Analyzer) checkVerbObjectCombination(verb, object string) *EmotionScore {
	if verbMap, exists := a.config.VerbObjectCombinations[verb]; exists {
		if emotionConfig, found := verbMap[object]; found {
			return &EmotionScore{
				Score:     emotionConfig.Score,
				Intensity: emotionConfig.Intensity,
			}
		}
	}
	return nil
}

// 检查宾语+动词组合（被动语态等）（使用配置）
func (a *Analyzer) checkObjectVerbCombination(object, verb string) *EmotionScore {
	if verbMap, exists := a.config.ObjectVerbCombinations[object]; exists {
		if emotionConfig, found := verbMap[verb]; found {
			return &EmotionScore{
				Score:     emotionConfig.Score,
				Intensity: emotionConfig.Intensity,
			}
		}
	}
	return nil
}

// 上下文感知的情感调整
func (a *Analyzer) adjustEmotionByContext(tokens []TokenWithPOS, index int, baseScore EmotionScore) EmotionScore {
	word := tokens[index].Word

	// 心疼的语境判断
	if word == "心疼" {
		// 检查前后文是否有表示他人的词汇
		hasOthers := false
		// 使用配置中的上下文范围
		checkRange := a.config.ContextSettings.ContextRange

		for i := max(0, index-checkRange); i < min(len(tokens), index+checkRange+1); i++ {
			if i == index {
				continue
			}
			contextWord := tokens[i].Word
			// 如果涉及到其他人或动物，"心疼"更多是同情而非自己的痛苦
			if a.isOtherEntity(contextWord) {
				hasOthers = true
				break
			}
		}

		if hasOthers {
			// 对他人的心疼是同情，情感强度减弱，倾向中性
			return EmotionScore{
				Score:     baseScore.Score * 0.3, // 大幅减弱负面程度
				Intensity: baseScore.Intensity * 0.3,
			}
		}
	}

	// 可以添加更多词汇的上下文处理
	return baseScore
}

// 判断是否为表示他人的实体（使用配置）
func (a *Analyzer) isOtherEntity(word string) bool {
	return slices.Contains(a.config.OtherEntities, word)
}

// 内置的动作情感映射（补充词典不足）（使用配置）
func (a *Analyzer) getActionEmotion(word string, pos string) *EmotionScore {
	// 检查正面动作
	if emotionConfig, exists := a.config.PositiveActions[word]; exists {
		return &EmotionScore{
			Score:     emotionConfig.Score,
			Intensity: emotionConfig.Intensity,
		}
	}

	// 检查负面动作
	if emotionConfig, exists := a.config.NegativeActions[word]; exists {
		return &EmotionScore{
			Score:     emotionConfig.Score,
			Intensity: emotionConfig.Intensity,
		}
	}

	return nil
}

// 情感形容词补充（针对词典缺失的基本情感词）（使用配置）
func (a *Analyzer) getBasicEmotion(word string, pos string) *EmotionScore {
	if emotionConfig, exists := a.config.BasicEmotions[word]; exists {
		return &EmotionScore{
			Score:     emotionConfig.Score,
			Intensity: emotionConfig.Intensity,
		}
	}
	return nil
}

// 扩展z类词（状语/拟声词）的情感映射（使用配置）
func (a *Analyzer) getStateWordEmotion(word string, pos string) *EmotionScore {
	if pos != "z" {
		return nil
	}

	if emotionConfig, exists := a.config.StateEmotions[word]; exists {
		return &EmotionScore{
			Score:     emotionConfig.Score,
			Intensity: emotionConfig.Intensity,
		}
	}

	return nil
}

// 辅助函数
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
