package sentiment

import (
	"testing"
)

// TestOptimizedAnalyzer 测试优化后的分析器
func TestOptimizedAnalyzer(t *testing.T) {
	// 使用测试用的配置文件
	analyzer, err := NewAnalyzerWithConfig(
		"/Volumes/data/Code/Go/src/admin/storage/dict.xlsx",
		"/Volumes/data/Code/Go/src/admin/storage/stop_words.txt",
		"semantic_rules.json",
	)
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 测试用例
	testCases := []struct {
		text     string
		expected string // "positive", "negative", "neutral"
		desc     string
	}{
		{
			text:     "这个小狗很可爱，我非常开心",
			expected: "positive",
			desc:     "测试程度副词和基本情感词",
		},
		{
			text:     "不是很好",
			expected: "negative",
			desc:     "测试否定词作用",
		},
		{
			text:     "小狗心疼地看着主人",
			expected: "neutral",
			desc:     "测试上下文感知调整",
		},
		{
			text:     "阳光驱散了阴霾",
			expected: "positive",
			desc:     "测试动宾组合特殊规则",
		},
		{
			text:     "他笑嘻嘻地走过来",
			expected: "positive",
			desc:     "测试状语词情感映射",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			score, err := analyzer.Analyze(tc.text)
			if err != nil {
				t.Fatalf("Analysis failed: %v", err)
			}

			var result string
			if score > 0.1 {
				result = "positive"
			} else if score < -0.1 {
				result = "negative"
			} else {
				result = "neutral"
			}

			t.Logf("Text: %s, Score: %.3f, Expected: %s, Got: %s",
				tc.text, score, tc.expected, result)

			if result != tc.expected {
				t.Errorf("Expected %s, got %s for text: %s", tc.expected, result, tc.text)
			}
		})
	}
}

// TestConcurrentSafety 测试并发安全性
func TestConcurrentSafety(t *testing.T) {
	analyzer, err := NewAnalyzerWithConfig(
		"/Volumes/data/Code/Go/src/admin/storage/dict.xlsx",
		"/Volumes/data/Code/Go/src/admin/storage/stop_words.txt",
		"semantic_rules.json",
	)
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	texts := []string{
		"我很开心",
		"这很糟糕",
		"还不错",
		"非常好",
		"太差了",
	}

	// 启动多个goroutine并发分析
	done := make(chan bool, len(texts))

	for i, text := range texts {
		go func(id int, txt string) {
			defer func() { done <- true }()

			for j := 0; j < 100; j++ {
				score, err := analyzer.Analyze(txt)
				if err != nil {
					t.Errorf("Goroutine %d iteration %d failed: %v", id, j, err)
					return
				}
				_ = score // 使用score，避免编译器警告
			}
		}(i, text)
	}

	// 等待所有goroutine完成
	for i := 0; i < len(texts); i++ {
		<-done
	}
}

// TestConfigurableRanges 测试可配置的作用范围
func TestConfigurableRanges(t *testing.T) {
	analyzer, err := NewAnalyzerWithConfig(
		"/Volumes/data/Code/Go/src/admin/storage/dict.xlsx",
		"/Volumes/data/Code/Go/src/admin/storage/stop_words.txt",
		"semantic_rules.json",
	)
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 测试否定词作用范围
	text := "我 认为 这 不是 很 好"
	score, err := analyzer.Analyze(text)
	if err != nil {
		t.Fatalf("Analysis failed: %v", err)
	}

	t.Logf("Text with negation: %s, Score: %.3f", text, score)

	// 应该是负面的，因为"不是"否定了"好"
	if score >= 0 {
		t.Errorf("Expected negative score, got %.3f", score)
	}
}

// BenchmarkOptimizedAnalysis 性能基准测试
func BenchmarkOptimizedAnalysis(b *testing.B) {
	analyzer, err := NewAnalyzerWithConfig(
		"/Volumes/data/Code/Go/src/admin/storage/dict.xlsx",
		"/Volumes/data/Code/Go/src/admin/storage/stop_words.txt",
		"semantic_rules.json",
	)
	if err != nil {
		b.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	text := "这个产品非常好，我很满意，但是价格有点贵，不过还是值得推荐的。"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := analyzer.Analyze(text)
		if err != nil {
			b.Fatalf("Analysis failed: %v", err)
		}
	}
}
