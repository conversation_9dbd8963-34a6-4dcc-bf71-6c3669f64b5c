# 情感分析包优化说明

## 优化内容总结

### 第一阶段：核心问题与性能强化

#### 1. 并发安全实现
- 引入 `sync.RWMutex` 保护共享资源
- 实现双重检查锁定模式进行懒加载
- 所有访问共享数据的方法都使用读锁保护

#### 2. 正则表达式与字符串处理优化
- 预编译正则表达式，避免重复编译开销
- 实现 O(1) 时间复杂度的标点符号判断
- 使用预构建的标点符号集合提高查找效率

#### 3. 情感得分计算逻辑重构
- 创建统一的上下文修饰应用函数 `applyContextualModifications`
- 消除重复代码，提高代码可维护性
- 统一处理否定词、程度副词和上下文调整

### 第二阶段：准确性与可扩展性提升

#### 4. 规则与词典外部化配置
- 创建 JSON 配置文件 `semantic_rules.json`
- 实现配置结构体和加载机制
- 支持运行时配置更新，无需重新编译代码

#### 5. 增强上下文依赖处理
- 可配置化否定词和程度副词作用范围
- 实现上下文感知的情感调整
- 支持复杂的句法分析和语境判断

#### 6. 优化多义词消歧
- 改进多义词处理逻辑，避免简单的中性判断
- 基于词性和上下文信息选择最佳义项
- 实现智能的条目合并和冲突解决

## 使用方法

### 基本使用

```go
// 使用默认配置
analyzer, err := sentiment.NewAnalyzer("dict.txt", "stop_words.txt")
if err != nil {
    log.Fatal(err)
}
defer analyzer.Close()

score, err := analyzer.Analyze("这个产品非常好")
if err != nil {
    log.Fatal(err)
}
fmt.Printf("情感得分: %.3f\n", score)
```

### 使用自定义配置

```go
// 使用自定义配置文件
analyzer, err := sentiment.NewAnalyzerWithConfig(
    "dict.txt", 
    "stop_words.txt",
    "custom_rules.json",
)
if err != nil {
    log.Fatal(err)
}
defer analyzer.Close()

score, err := analyzer.Analyze("这个产品非常好")
fmt.Printf("情感得分: %.3f\n", score)
```

### 配置文件示例

```json
{
  "negativeWords": ["不", "没有", "非"],
  "degreeWords": {
    "非常": 1.8,
    "很": 1.5,
    "比较": 1.2
  },
  "contextSettings": {
    "negationRange": 2,
    "degreeRange": 2,
    "contextRange": 3
  }
}
```

## 性能提升

### 优化前 vs 优化后

1. **并发安全**: 现在支持多协程环境下的并发访问
2. **性能提升**: 正则表达式预编译和标点符号 O(1) 查找显著提高处理速度
3. **内存效率**: 减少重复对象创建，优化内存使用
4. **可维护性**: 代码重构后更易读、更易维护

### 基准测试

运行基准测试查看性能改进：

```bash
go test -bench=BenchmarkOptimizedAnalysis -benchmem
```

## 扩展性

### 添加新的情感规则

1. 编辑 `semantic_rules.json` 文件
2. 添加新的词汇和规则
3. 重启应用，新规则自动生效

### 自定义上下文处理

1. 修改 `contextSettings` 中的范围参数
2. 添加新的实体类型到 `otherEntities`
3. 扩展动宾组合规则

## 注意事项

1. 配置文件必须是有效的 JSON 格式
2. 情感得分范围为 [-1, 1]，正数表示正面情感，负数表示负面情感
3. 在高并发场景下，建议复用 Analyzer 实例而不是频繁创建新实例
4. 关闭应用时记得调用 `analyzer.Close()` 释放资源
