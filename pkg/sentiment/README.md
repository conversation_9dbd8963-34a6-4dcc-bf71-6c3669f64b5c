# 情感分析包 (Sentiment Analysis Package)

基于大连理工大学情感词典(DUTSDL)的中文情感分析包，支持词典式情感计算，包含否定词处理、程度副词调节等功能。

## 功能特性

- **基于词典的情感分析**: 使用大连理工大学情感词典进行情感计算
- **否定词处理**: 自动检测并处理否定词对情感的影响
- **程度副词调节**: 支持程度副词对情感强度的调节
- **极性冲突处理**: 智能处理"兼有褒贬"词语的极性判断
- **词性匹配**: 支持基于词性的精确匹配
- **懒加载**: 资源在首次使用时加载，提高启动速度
- **可配置停用词**: 支持自定义停用词列表

## 安装要求

### Go依赖
```bash
# 核心依赖
go get github.com/yanyiwu/gojieba       # 中文分词
go get github.com/xuri/excelize/v2      # Excel文件处理
```

### 资源文件
1. **情感词典**: 大连理工大学情感词典Excel文件 (`dict.xlsx`)
2. **停用词表**: 可选的自定义停用词文件 (纯文本，每行一个词)

## 使用示例

在项目根目录下创建测试文件 `main.go`：

```go
package main

import (
    "fmt"
    "log"
    "admin/pkg/sentiment"
)

func main() {
    // 创建分析器
    analyzer, err := sentiment.NewAnalyzer("storage/dict.xlsx", "")
    if err != nil {
        log.Fatalf("创建分析器失败: %v", err)
    }
    defer analyzer.Close()

    // 测试文本
    texts := []string{
        "这个产品质量很优秀",      // 正面
        "今天天气很糟糕",          // 负面  
        "我很喜欢这个设计",        // 正面
        "这个系统不好用",          // 负面
        "今天是星期一",            // 中性
    }

    for _, text := range texts {
        score, err := analyzer.Analyze(text)
        if err != nil {
            fmt.Printf("分析失败: %v\n", err)
            continue
        }

        var sentiment string
        if score > 0.1 {
            sentiment = "正面"
        } else if score < -0.1 {
            sentiment = "负面"
        } else {
            sentiment = "中性"
        }

        fmt.Printf("文本: %s\n", text)
        fmt.Printf("得分: %.3f (%s)\n\n", score, sentiment)
    }
}
```

运行：
```bash
go run main.go
```

预期输出：
```
文本: 这个产品质量很优秀
得分: 1.000 (正面)

文本: 今天天气很糟糕  
得分: -1.000 (负面)

文本: 我很喜欢这个设计
得分: 1.000 (正面)

文本: 这个系统不好用
得分: -1.000 (负面)

文本: 今天是星期一
得分: 0.000 (中性)
```

### 详细分析 (调试用)

```go
// 获取详细的分析过程信息
score, details, err := analyzer.AnalyzeWithDetails(text)
if err != nil {
    log.Fatalf("详细分析失败: %v", err)
}

fmt.Printf("最终得分: %.3f\n", score)
fmt.Printf("分词结果: %+v\n", details["tokens"])
fmt.Printf("情感得分列表: %+v\n", details["emotion_scores"])
```

## 算法说明

### 情感计算流程

1. **文本预处理**
   - 使用jieba进行中文分词和词性标注
   - 去除停用词、标点符号、数字
   - 文本标准化处理

2. **词典匹配**
   - 优先匹配"词语+词性"组合
   - 回退到仅匹配"词语"
   - 处理同词多义的情况

3. **情感计算**
   - 基础得分 = 极性 × 强度
   - 应用程度副词调节: 调整后强度 = 原始强度 × 副词权重
   - 应用否定词影响: 极性反转，强度保持

4. **特殊处理**
   - **"极性为3"词语**: 检查邻近词的情感倾向来确定极性
   - **极性冲突**: 同词不同义项有冲突极性时视为中性
   - **辅助情感**: 主情感和辅助情感都参与计算

5. **得分归一化**
   ```
   最终得分 = Σ(有效情感得分) / Σ(|有效情感得分|)
   结果范围: [-1, 1]
   ```

### 词典格式

Excel文件包含以下列：
- 词语: 情感词
- 词性种类: 词性标注
- 词义数: 该词的义项总数
- 词义序号: 当前义项序号
- 情感分类: 情感类别
- 强度: 情感强度 (1-9)
- 极性: 情感极性 (0=中性, 1=褒义, 2=贬义, 3=兼有褒贬)
- 辅助情感分类: 辅助情感类别
- 强度(辅助): 辅助情感强度
- 极性(辅助): 辅助情感极性

### 内置词表

**否定词** (部分):
```
不、没有、非、无、否、别、勿、莫、未、不曾、不能、不会、不要、不用、不必、不该、不应、不可、不许、不准、不得、禁止、拒绝、反对
```

**程度副词** (部分):
```
极其(2.0)、非常(1.8)、很(1.5)、比较(1.2)、稍微(0.8)、太(1.6)、最(2.2)
```

## API文档

### 类型定义

```go
type Analyzer struct {
    // 私有字段
}

type DictEntry struct {
    Word          string  // 词语
    POS           string  // 词性种类  
    MeaningNum    int     // 词义数
    MeaningIndex  int     // 词义序号
    Emotion       string  // 情感分类
    Intensity     float64 // 强度 (1-9)
    Polarity      int     // 极性 (0=中性, 1=褒义, 2=贬义, 3=兼有褒贬)
    AuxEmotion    string  // 辅助情感分类
    AuxIntensity  float64 // 辅助强度
    AuxPolarity   int     // 辅助极性
}

type EmotionScore struct {
    Score     float64 // 情感得分
    Intensity float64 // 强度
}
```

### 主要方法

#### NewAnalyzer
```go
func NewAnalyzer(dictionaryPath string, stopWordsPath string) (*Analyzer, error)
```
创建新的情感分析器。

**参数:**
- `dictionaryPath`: 情感词典Excel文件路径 (必需)
- `stopWordsPath`: 停用词文件路径 (可选，空字符串使用内置停用词)

**返回:**
- `*Analyzer`: 分析器实例
- `error`: 错误信息

#### Analyze
```go
func (a *Analyzer) Analyze(text string) (float64, error)
```
分析文本的情感倾向。

**参数:**
- `text`: 要分析的文本

**返回:**
- `float64`: 情感得分 [-1, 1]，正数表示正面情感，负数表示负面情感
- `error`: 错误信息

#### Close
```go
func (a *Analyzer) Close()
```
关闭分析器，释放jieba分词器资源。建议在程序结束前调用。

## 注意事项

1. **资源管理**: 记得调用 `analyzer.Close()` 释放jieba资源
2. **文件路径**: 确保词典文件和停用词文件路径正确
3. **线程安全**: 当前实现不是线程安全的，多线程环境需要额外同步
4. **内存使用**: 词典在首次使用时全量加载到内存
5. **性能**: 建议复用Analyzer实例，避免频繁创建

## 测试

```bash
# 运行测试
go test ./pkg/sentiment/

# 运行测试并显示覆盖率
go test -cover ./pkg/sentiment/

# 运行基准测试
go test -bench=. ./pkg/sentiment/
```

## 许可证

本项目遵循项目根目录的许可证。

## 贡献

欢迎提交Issue和Pull Request。

## 更新日志

### v1.0.0
- 初始版本发布
- 基于大连理工大学情感词典的情感分析
- 支持否定词和程度副词处理
- 支持"兼有褒贬"词语的智能处理
