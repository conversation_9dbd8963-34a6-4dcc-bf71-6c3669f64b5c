package sentiment

import (
	"strings"
	"unicode"
)

// TokenWithPOS 带词性的分词结果
type TokenWithPOS struct {
	Word         string // 词语
	POS          string // 词性
	MeaningIndex int    // 词义序号 (0表示未消歧或默认词义)
}

// preprocessText 文本预处理
func (a *Analyzer) preprocessText(text string) ([]TokenWithPOS, error) {
	// 1. 基本清理：去除多余空白字符（使用预编译的正则表达式）
	text = strings.TrimSpace(text)
	text = a.whitespaceRegex.ReplaceAllString(text, " ")

	// 2. 转为小写（对于中文主要影响英文字符）
	text = strings.ToLower(text)

	// 3. 去除标点符号和数字（保留中文、英文字母）
	text = a.removePunctuationAndNumbers(text)

	if text == "" {
		return []TokenWithPOS{}, nil
	}

	// 4. 使用jieba进行分词和词性标注
	tokens := a.jieba.Tag(text)

	// 5. 过滤停用词并转换格式
	var result []TokenWithPOS
	for _, token := range tokens {
		// token是一个字符串，格式为 "word/pos"
		parts := strings.Split(token, "/")
		if len(parts) != 2 {
			continue
		}

		word := strings.TrimSpace(parts[0])
		pos := strings.TrimSpace(parts[1])

		if word == "" {
			continue
		}

		// 过滤停用词
		if a.isStopWord(word) {
			continue
		}

		// 过滤单个字符的标点符号（使用O(1)查找）
		if len(word) == 1 && a.isPunctuationFast(word) {
			continue
		}

		result = append(result, TokenWithPOS{
			Word: word,
			POS:  pos,
		})
	}

	return result, nil
}

// removePunctuationAndNumbers 去除标点符号和数字
func (a *Analyzer) removePunctuationAndNumbers(text string) string {
	var result strings.Builder

	for _, r := range text {
		// 保留中文字符、英文字母和空格
		if unicode.Is(unicode.Han, r) ||
			unicode.IsLetter(r) ||
			unicode.IsSpace(r) {
			result.WriteRune(r)
		}
	}

	return result.String()
}

// isStopWord 线程安全地检查是否为停用词
func (a *Analyzer) isStopWord(word string) bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.stopWords[word]
}

// isPunctuationFast O(1)时间复杂度判断是否为标点符号
func (a *Analyzer) isPunctuationFast(s string) bool {
	if len(s) != 1 {
		return false
	}

	r := []rune(s)[0]
	// 先检查预构建的标点符号集合
	if a.punctuationSet[r] {
		return true
	}
	// 兜底使用unicode判断
	return unicode.IsPunct(r) || unicode.IsSymbol(r)
}

// isPunctuation 判断是否为标点符号
func (a *Analyzer) isPunctuation(s string) bool {
	if len(s) != 1 {
		return false
	}
	r := []rune(s)[0]
	return unicode.IsPunct(r) || unicode.IsSymbol(r)
}
