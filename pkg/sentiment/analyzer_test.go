package sentiment

import (
	"os"
	"path/filepath"
	"testing"
)

const testDictPath = "/Volumes/data/Code/Go/src/admin/storage/dict.xlsx"

func TestNewAnalyzer(t *testing.T) {
	// 测试正常创建
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	if analyzer == nil {
		t.Fatal("Analyzer is nil")
	}
	defer analyzer.Close()

	// 测试空字典路径
	_, err = NewAnalyzer("", "")
	if err == nil {
		t.Fatal("Expected error for empty dictionary path")
	}
}

func TestPreprocessText(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 初始化停用词（模拟）
	analyzer.initDefaultStopWords()

	tests := []struct {
		input    string
		expected int // 预期的token数量
	}{
		{"这是一个测试", 4},
		{"", 0},
		{"   ", 0},
		{"123测试456", 1}, // 数字会被过滤
		{"！@#测试$%^", 1}, // 标点符号会被过滤
	}

	for _, test := range tests {
		tokens, err := analyzer.preprocessText(test.input)
		if err != nil {
			t.Errorf("Failed to preprocess text '%s': %v", test.input, err)
			continue
		}

		// 注意：由于停用词过滤，实际token数量可能少于预期
		if len(tokens) > test.expected {
			t.Errorf("Text '%s': expected at most %d tokens, got %d",
				test.input, test.expected, len(tokens))
		}
	}
}

func TestEmptyTextAnalysis(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 模拟已加载状态，跳过文件加载
	analyzer.loaded = true
	analyzer.initDefaultStopWords()

	// 测试空文本
	score, err := analyzer.Analyze("")
	if err != nil {
		t.Errorf("Failed to analyze empty text: %v", err)
	}
	if score != 0 {
		t.Errorf("Expected score 0 for empty text, got %f", score)
	}

	// 测试只有空格的文本
	score, err = analyzer.Analyze("   ")
	if err != nil {
		t.Errorf("Failed to analyze whitespace text: %v", err)
	}
	if score != 0 {
		t.Errorf("Expected score 0 for whitespace text, got %f", score)
	}
}

func TestNegativeWords(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 检查否定词是否正确初始化
	expectedNegativeWords := []string{"不", "没有", "非", "无"}
	for _, word := range expectedNegativeWords {
		if !analyzer.negativeWords[word] {
			t.Errorf("Expected negative word '%s' to be initialized", word)
		}
	}
}

func TestDegreeWords(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 检查程度副词是否正确初始化
	expectedDegreeWords := map[string]float64{
		"非常": 1.8,
		"很":  1.5,
		"稍微": 0.8,
	}

	for word, expectedWeight := range expectedDegreeWords {
		if weight, exists := analyzer.degreeWords[word]; !exists {
			t.Errorf("Expected degree word '%s' to be initialized", word)
		} else if weight != expectedWeight {
			t.Errorf("Expected degree word '%s' weight %f, got %f", word, expectedWeight, weight)
		}
	}
}

func TestPOSMapping(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 检查词性映射是否正确初始化
	expectedMappings := map[string]string{
		"a": "adj",
		"v": "verb",
		"n": "noun",
		"d": "adv",
	}

	for jiebaPOS, expectedDictPOS := range expectedMappings {
		if dictPOS, exists := analyzer.posMapping[jiebaPOS]; !exists {
			t.Errorf("Expected POS mapping for '%s' to be initialized", jiebaPOS)
		} else if dictPOS != expectedDictPOS {
			t.Errorf("Expected POS mapping '%s' -> '%s', got '%s'", jiebaPOS, expectedDictPOS, dictPOS)
		}
	}
}

func TestConsolidateEntries(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 测试相同极性条目的合并 - 现在选择强度最高的
	entries := []DictEntry{
		{Word: "好", Polarity: 1, Intensity: 5.0},
		{Word: "好", Polarity: 1, Intensity: 7.0},
	}

	result := analyzer.consolidateEntries(entries)
	if len(result) != 1 {
		t.Errorf("Expected 1 consolidated entry, got %d", len(result))
	}
	// 新逻辑：选择强度最高的条目
	if result[0].Intensity != 7.0 {
		t.Errorf("Expected max intensity 7.0, got %f", result[0].Intensity)
	}

	// 测试矛盾极性条目的处理 - 现在选择最佳义项
	conflictEntries := []DictEntry{
		{Word: "测试", Polarity: 1, Intensity: 5.0},
		{Word: "测试", Polarity: 2, Intensity: 3.0},
	}

	result = analyzer.consolidateEntries(conflictEntries)
	// 新逻辑：基于上下文选择最佳义项，这里会选择强度更高的正面义项
	if len(result) != 1 {
		t.Errorf("Expected 1 entry (best context match), got %d", len(result))
	}
	if len(result) > 0 && result[0].Polarity != 1 {
		t.Errorf("Expected positive polarity (stronger intensity), got %d", result[0].Polarity)
	}
}

// 创建测试词典文件的辅助函数
func createTestDictionary(t *testing.T) string {
	// 这里我们创建一个简单的测试，实际使用时需要真实的字典文件
	tempDir := t.TempDir()
	dictPath := filepath.Join(tempDir, "test_dict.xlsx")

	// 创建一个空文件（实际测试时应该使用真实的词典文件）
	file, err := os.Create(dictPath)
	if err != nil {
		t.Fatalf("Failed to create test dictionary: %v", err)
	}
	file.Close()

	return dictPath
}

func TestWordSenseDisambiguation(t *testing.T) {
	analyzer, err := NewAnalyzer(testDictPath, "")
	if err != nil {
		t.Fatalf("Failed to create analyzer: %v", err)
	}
	defer analyzer.Close()

	// 模拟词典数据
	analyzer.dictionary["好"] = []DictEntry{
		{
			Word:         "好",
			POS:          "adj",
			MeaningNum:   2,
			MeaningIndex: 0, // 正面词义
			Emotion:      "happy",
			Intensity:    7.0,
			Polarity:     1,
		},
		{
			Word:         "好",
			POS:          "adj",
			MeaningNum:   2,
			MeaningIndex: 1, // 负面词义
			Emotion:      "angry",
			Intensity:    5.0,
			Polarity:     2,
		},
	}

	// 添加测试上下文词的词典数据
	analyzer.dictionary["生气"] = []DictEntry{
		{
			Word:      "生气",
			POS:       "adj",
			Emotion:   "angry",
			Intensity: 6.0,
			Polarity:  2, // 负面
		},
	}

	analyzer.dictionary["愤怒"] = []DictEntry{
		{
			Word:      "愤怒",
			POS:       "adj",
			Emotion:   "angry",
			Intensity: 7.0,
			Polarity:  2, // 负面
		},
	}

	analyzer.dictionary["开心"] = []DictEntry{
		{
			Word:      "开心",
			POS:       "adj",
			Emotion:   "happy",
			Intensity: 6.0,
			Polarity:  1, // 正面
		},
	}

	// 确保否定词被识别
	analyzer.negativeWords["不"] = true

	// 测试用例
	tests := []struct {
		name     string
		context  []TokenWithPOS // 上下文token
		expected int            // 期望选择的词义序号
	}{
		{
			name: "正面上下文选择正面词义",
			context: []TokenWithPOS{
				{Word: "开心", POS: "adj"},
				{Word: "快乐", POS: "adj"},
			},
			expected: 0,
		},
		{
			name: "负面上下文选择负面词义",
			context: []TokenWithPOS{
				{Word: "生气", POS: "adj"},
				{Word: "愤怒", POS: "adj"},
			},
			expected: 1,
		},
		{
			name: "否定词反转词义选择",
			context: []TokenWithPOS{
				{Word: "不", POS: "adv"},
				{Word: "开心", POS: "adj"},
			},
			expected: 1, // 否定+正面上下文 -> 选择负面词义
		},
		{
			name: "程度副词增强词义选择",
			context: []TokenWithPOS{
				{Word: "非常", POS: "adv"},
				{Word: "开心", POS: "adj"},
			},
			expected: 0, // 程度副词增强正面情感
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// 创建上下文窗口
			contextWindow := ContextWindow{
				LeftTokens:  test.context,
				RightTokens: []TokenWithPOS{},
			}

			// 执行消歧
			token := TokenWithPOS{Word: "好", POS: "adj"}
			result, err := analyzer.disambiguateWord(token, contextWindow)
			if err != nil {
				t.Fatalf("Failed to disambiguate word: %v", err)
			}

			if result == nil {
				t.Fatal("Expected disambiguation result, got nil")
			}

			if result.MeaningIndex != test.expected {
				t.Errorf("Expected meaning index %d, got %d", test.expected, result.MeaningIndex)
			}
		})
	}
}
