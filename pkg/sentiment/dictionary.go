package sentiment

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/xuri/excelize/v2"
)

// loadDictionary 加载情感词典
func (a *Analyzer) loadDictionary() error {
	file, err := excelize.OpenFile(a.dictionaryPath)
	if err != nil {
		return fmt.Errorf("failed to open dictionary file: %w", err)
	}
	defer file.Close()

	sheets := file.GetSheetList()
	if len(sheets) == 0 {
		return fmt.Errorf("dictionary file has no sheets")
	}

	sheetName := sheets[0]
	rows, err := file.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("failed to get rows from sheet: %w", err)
	}

	if len(rows) <= 1 {
		return fmt.Errorf("dictionary file has no data rows")
	}

	// 跳过表头，从第二行开始处理
	for i, row := range rows[1:] {
		entry, err := a.parseDictionaryRow(row, i+2)
		if err != nil {
			// 记录但不中断处理
			fmt.Printf("Warning: failed to parse row %d: %v\n", i+2, err)
			continue
		}

		if entry.Word != "" {
			a.dictionary[entry.Word] = append(a.dictionary[entry.Word], entry)
		}
	}

	fmt.Printf("Loaded %d unique words from dictionary\n", len(a.dictionary))
	return nil
}

// parseDictionaryRow 解析词典行数据
func (a *Analyzer) parseDictionaryRow(row []string, rowNum int) (DictEntry, error) {
	entry := DictEntry{}

	// 确保有足够的列
	if len(row) < 7 {
		return entry, fmt.Errorf("insufficient columns in row %d (got %d, need at least 7)", rowNum, len(row))
	}

	// 补齐缺失的列
	for len(row) < 10 {
		row = append(row, "")
	}

	// 词语
	entry.Word = strings.TrimSpace(row[0])
	if entry.Word == "" {
		return entry, fmt.Errorf("empty word in row %d", rowNum)
	}

	// 词性种类
	entry.POS = strings.TrimSpace(row[1])

	// 词义数
	meaningNumStr := strings.TrimSpace(row[2])
	if meaningNumStr != "" {
		meaningNum, err := strconv.Atoi(meaningNumStr)
		if err != nil {
			return entry, fmt.Errorf("invalid meaning number '%s' in row %d", meaningNumStr, rowNum)
		}
		entry.MeaningNum = meaningNum
	}

	// 词义序号
	meaningIndexStr := strings.TrimSpace(row[3])
	if meaningIndexStr != "" {
		meaningIndex, err := strconv.Atoi(meaningIndexStr)
		if err != nil {
			return entry, fmt.Errorf("invalid meaning index '%s' in row %d", meaningIndexStr, rowNum)
		}
		entry.MeaningIndex = meaningIndex
	}

	// 情感分类
	entry.Emotion = strings.TrimSpace(row[4])

	// 强度
	intensityStr := strings.TrimSpace(row[5])
	if intensityStr != "" {
		intensity, err := strconv.ParseFloat(intensityStr, 64)
		if err != nil {
			return entry, fmt.Errorf("invalid intensity '%s' in row %d", intensityStr, rowNum)
		}
		entry.Intensity = intensity
	}

	// 极性
	polarityStr := strings.TrimSpace(row[6])
	if polarityStr != "" {
		polarity, err := strconv.Atoi(polarityStr)
		if err != nil {
			return entry, fmt.Errorf("invalid polarity '%s' in row %d", polarityStr, rowNum)
		}
		entry.Polarity = polarity
	}

	// 辅助情感分类
	if len(row) > 7 {
		entry.AuxEmotion = strings.TrimSpace(row[7])
	}

	// 辅助强度
	if len(row) > 8 {
		auxIntensityStr := strings.TrimSpace(row[8])
		if auxIntensityStr != "" {
			auxIntensity, err := strconv.ParseFloat(auxIntensityStr, 64)
			if err == nil {
				entry.AuxIntensity = auxIntensity
			}
		}
	}

	// 辅助极性
	if len(row) > 9 {
		auxPolarityStr := strings.TrimSpace(row[9])
		if auxPolarityStr != "" {
			auxPolarity, err := strconv.Atoi(auxPolarityStr)
			if err == nil {
				entry.AuxPolarity = auxPolarity
			}
		}
	}

	return entry, nil
}

// loadStopWords 加载停用词
func (a *Analyzer) loadStopWords() error {
	// 如果用户没有提供停用词路径，使用内置的停用词列表
	if a.stopWordsPath == "" {
		a.initDefaultStopWords()
		return nil
	}

	file, err := os.Open(a.stopWordsPath)
	if err != nil {
		// 如果用户提供的文件不存在，使用内置停用词列表
		fmt.Printf("Warning: cannot open stop words file %s, using default: %v\n", a.stopWordsPath, err)
		a.initDefaultStopWords()
		return nil
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	count := 0
	for scanner.Scan() {
		word := strings.TrimSpace(scanner.Text())
		if word != "" {
			a.stopWords[word] = true
			count++
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("failed to read stop words file: %w", err)
	}

	fmt.Printf("Loaded %d stop words from file\n", count)
	return nil
}

// initDefaultStopWords 初始化默认停用词列表
func (a *Analyzer) initDefaultStopWords() {
	defaultStopWords := []string{
		"的", "了", "在", "是", "我", "有", "和", "就", "人", "都", "一", "一个", "上", "也", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "自己", "这", "那", "它", "他", "她", "们", "之", "于", "为", "从", "以", "及", "与", "对", "将", "所", "把", "被", "让", "使", "给", "向", "过", "来", "又", "还", "更", "最", "只", "才", "就是", "这个", "那个", "什么", "怎么", "为什么", "哪里", "哪个", "多少", "几", "第", "每", "各", "其", "其他", "另", "别", "其它", "等", "等等", "如", "比如", "例如", "像", "似", "如同", "按照", "根据", "通过", "由于", "因为", "所以", "因此", "然后", "接着", "后来", "最后", "总之", "而且", "并且", "或者", "还是", "但是", "可是", "不过", "然而", "虽然", "尽管", "如果", "假如", "要是", "除非", "无论", "不管", "即使", "就算", "哪怕", "一旦", "当", "时", "正在", "已经", "曾经", "从来", "总是", "常常", "经常", "有时", "偶尔", "马上", "立刻", "慢慢", "渐渐", "突然", "忽然", "首先", "然后", "其次", "最后", "一方面", "另一方面", "一边", "一面", "同时", "此外", "另外", "除了", "包括", "主要", "基本", "可能", "应该", "必须", "需要", "理解", "明白", "知道", "认为", "觉得", "感觉", "相信", "确定", "肯定", "一定", "可能", "也许", "大概", "估计", "差不多", "几乎", "完全", "全部", "部分", "一些", "许多", "很多", "少数", "个别", "单独", "一起", "共同", "分别", "各自", "互相", "彼此", "大家", "我们", "你们", "他们", "本人", "本身", "亲自", "当然", "确实", "的确", "实际", "事实", "真的", "假的", "对", "错", "正确", "错误", "真正", "虚假",
	}

	for _, word := range defaultStopWords {
		a.stopWords[word] = true
	}

	fmt.Printf("Loaded %d default stop words\n", len(defaultStopWords))
}
