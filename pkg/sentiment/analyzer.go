package sentiment

import (
	"errors"
	"fmt"
	"maps"
	"math"
	"regexp"
	"strings"
	"sync"

	"github.com/yanyiwu/gojieba"
)

// Analyzer 情感分析器
type Analyzer struct {
	jieba          *gojieba.Jieba
	dictionary     map[string][]DictEntry // 词语 -> 词典条目列表
	stopWords      map[string]bool        // 停用词集合
	negativeWords  map[string]bool        // 否定词集合
	degreeWords    map[string]float64     // 程度副词及其权重
	posMapping     map[string]string      // jieba词性到词典词性的映射
	dictionaryPath string
	stopWordsPath  string
	configPath     string // 语义规则配置文件路径
	loaded         bool
	mu             sync.RWMutex // 读写锁保护共享资源

	// 预编译的正则表达式
	whitespaceRegex *regexp.Regexp

	// 预构建的标点符号集合
	punctuationSet map[rune]bool

	// 语义规则配置
	config *SemanticRulesConfig

	// 特殊句式模式库
	specialPatterns map[string]PatternRule
}

// PatternRule 特殊句式规则
type PatternRule struct {
	Score     float64 // 情感得分
	Intensity float64 // 情感强度
	Pattern   string  // 句式模式
}

// DictEntry 词典条目
type DictEntry struct {
	Word         string  // 词语
	POS          string  // 词性种类
	MeaningNum   int     // 词义数
	MeaningIndex int     // 词义序号
	Emotion      string  // 情感分类
	Intensity    float64 // 强度 (1-9)
	Polarity     int     // 极性 (0=中性, 1=褒义, 2=贬义, 3=兼有褒贬)
	AuxEmotion   string  // 辅助情感分类
	AuxIntensity float64 // 辅助强度
	AuxPolarity  int     // 辅助极性
}

// EmotionScore 情感得分
type EmotionScore struct {
	Score     float64 // 情感得分
	Intensity float64 // 强度
}

// ContextWindow 上下文窗口
type ContextWindow struct {
	LeftTokens  []TokenWithPOS // 左侧上下文
	RightTokens []TokenWithPOS // 右侧上下文
}

// WordSenseDisambiguation 多义词消歧结果
type WordSenseDisambiguation struct {
	Word         string  // 词语
	MeaningIndex int     // 词义序号
	Confidence   float64 // 置信度
}

// NewAnalyzer 创建新的情感分析器
func NewAnalyzer(dictionaryPath string, stopWordsPath string) (*Analyzer, error) {
	return NewAnalyzerWithConfig(dictionaryPath, stopWordsPath, "")
}

// NewAnalyzerWithConfig 创建新的情感分析器（带配置文件）
func NewAnalyzerWithConfig(dictionaryPath, stopWordsPath, configPath string) (*Analyzer, error) {
	if dictionaryPath == "" {
		return nil, errors.New("dictionary path cannot be empty")
	}

	analyzer := &Analyzer{
		jieba:           gojieba.NewJieba(),
		dictionary:      make(map[string][]DictEntry),
		stopWords:       make(map[string]bool),
		negativeWords:   make(map[string]bool),
		degreeWords:     make(map[string]float64),
		posMapping:      make(map[string]string),
		dictionaryPath:  dictionaryPath,
		stopWordsPath:   stopWordsPath,
		configPath:      configPath,
		loaded:          false,
		whitespaceRegex: regexp.MustCompile(`\s+`),
		punctuationSet:  make(map[rune]bool),
		specialPatterns: make(map[string]PatternRule),
	}

	// 加载语义规则配置
	config, err := LoadSemanticRulesConfig(analyzer.configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load semantic rules config: %w", err)
	}
	analyzer.config = config

	// 初始化否定词列表（从配置加载）
	analyzer.initNegativeWordsFromConfig()

	// 初始化程度副词列表（从配置加载）
	analyzer.initDegreeWordsFromConfig()

	// 初始化词性映射
	analyzer.initPOSMapping()

	// 初始化标点符号集合
	analyzer.initPunctuationSet()

	return analyzer, nil
}

// Analyze 分析文本情感
func (a *Analyzer) Analyze(text string) (float64, error) {
	a.mu.RLock()
	loaded := a.loaded
	a.mu.RUnlock()

	if !loaded {
		a.mu.Lock()
		// 双重检查
		if !a.loaded {
			if err := a.loadResources(); err != nil {
				a.mu.Unlock()
				return 0, fmt.Errorf("failed to load resources: %w", err)
			}
		}
		a.mu.Unlock()
	}

	if text == "" {
		return 0, nil
	}

	// 文本预处理
	tokens, err := a.preprocessText(text)
	if err != nil {
		return 0, fmt.Errorf("failed to preprocess text: %w", err)
	}

	if len(tokens) == 0 {
		return 0, nil
	}

	// 执行多义词消歧
	disambiguatedTokens, err := a.disambiguateWordSenses(tokens)
	if err != nil {
		return 0, fmt.Errorf("failed to disambiguate word senses: %w", err)
	}

	// 计算情感得分
	score, err := a.calculateSentiment(disambiguatedTokens)
	if err != nil {
		return 0, fmt.Errorf("failed to calculate sentiment: %w", err)
	}

	return score, nil
}

// disambiguateWordSenses 多义词消歧
func (a *Analyzer) disambiguateWordSenses(tokens []TokenWithPOS) ([]TokenWithPOS, error) {
	disambiguated := make([]TokenWithPOS, len(tokens))

	for i, token := range tokens {
		// 获取上下文窗口
		context := a.getContextWindow(tokens, i, 3) // 窗口大小为3

		// 执行消歧
		result, err := a.disambiguateWord(token, context)
		if err != nil {
			return nil, fmt.Errorf("failed to disambiguate word %s: %w", token.Word, err)
		}

		// 更新token的词义信息
		disambiguated[i] = token
		if result != nil {
			disambiguated[i].MeaningIndex = result.MeaningIndex
		}
	}

	return disambiguated, nil
}

// getContextWindow 获取上下文窗口
func (a *Analyzer) getContextWindow(tokens []TokenWithPOS, centerIndex, windowSize int) ContextWindow {
	var left, right []TokenWithPOS

	// 获取左侧上下文
	leftStart := max(0, centerIndex-windowSize)
	left = tokens[leftStart:centerIndex]

	// 获取右侧上下文
	rightEnd := min(len(tokens), centerIndex+windowSize+1)
	right = tokens[centerIndex+1 : rightEnd]

	return ContextWindow{
		LeftTokens:  left,
		RightTokens: right,
	}
}

// disambiguateWord 单个词语消歧
func (a *Analyzer) disambiguateWord(token TokenWithPOS, context ContextWindow) (*WordSenseDisambiguation, error) {
	entries, found := a.dictionary[token.Word]
	if !found || len(entries) <= 1 {
		// 非多义词，无需消歧
		return nil, nil
	}

	// 1. 计算上下文特征
	contextFeatures := a.extractContextFeatures(context)

	// 2. 为每个词义计算匹配得分
	var bestMeaning *WordSenseDisambiguation
	maxScore := -1.0

	for _, entry := range entries {
		score := a.calculateMeaningScore(entry, contextFeatures)
		if score > maxScore {
			maxScore = score
			bestMeaning = &WordSenseDisambiguation{
				Word:         token.Word,
				MeaningIndex: entry.MeaningIndex,
				Confidence:   score,
			}
		}
	}

	// 3. 返回最佳匹配词义
	return bestMeaning, nil
}

// extractContextFeatures 提取上下文特征
func (a *Analyzer) extractContextFeatures(context ContextWindow) map[string]float64 {
	features := make(map[string]float64)

	// 分析左侧上下文
	for _, token := range context.LeftTokens {
		if entries, found := a.dictionary[token.Word]; found {
			for _, entry := range entries {
				// 情感词特征
				if entry.Emotion != "" {
					features[entry.Emotion] += entry.Intensity
				}
				// 否定词特征
				if a.negativeWords[token.Word] {
					features["negation"] += 1.0
				}
				// 程度副词特征
				if weight, isDegree := a.degreeWords[token.Word]; isDegree {
					features["degree"] += weight
				}
			}
		}
	}

	// 分析右侧上下文 (同上)
	for _, token := range context.RightTokens {
		if entries, found := a.dictionary[token.Word]; found {
			for _, entry := range entries {
				if entry.Emotion != "" {
					features[entry.Emotion] += entry.Intensity
				}
				if a.negativeWords[token.Word] {
					features["negation"] += 1.0
				}
				if weight, isDegree := a.degreeWords[token.Word]; isDegree {
					features["degree"] += weight
				}
			}
		}
	}

	return features
}

// calculateMeaningScore 计算词义匹配得分
func (a *Analyzer) calculateMeaningScore(entry DictEntry, contextFeatures map[string]float64) float64 {
	score := 0.0
	negationFactor := 1.0

	// 1. 处理否定词（支持双重否定）
	if neg, exists := contextFeatures["negation"]; exists && neg > 0 {
		// 奇数个否定词反转情感，偶数个否定词保持原情感
		negationFactor = math.Pow(-1.0, float64(int(neg)%2))

		// 2. 根据否定词数量计算影响范围
		negationScope := min(5, int(neg))                     // 每个否定词影响最多5个词
		negationFactor *= (1.0 - 0.05*float64(negationScope)) // 影响范围衰减
	}

	// 2. 情感匹配得分（带衰减）
	if entry.Emotion != "" {
		if intensity, exists := contextFeatures[entry.Emotion]; exists {
			adjustedIntensity := intensity * negationFactor
			// 应用衰减函数
			adjustedIntensity *= a.calculateDecayFactor(contextFeatures["distance"])

			if entry.Polarity == 2 && strings.Contains(entry.Emotion, "负面") {
				score += adjustedIntensity * 1.0
			} else {
				score += adjustedIntensity * 0.5
			}
		}
	}

	// 3. 程度副词影响（带衰减）
	if degree, exists := contextFeatures["degree"]; exists {
		adjustedDegree := degree * a.calculateDecayFactor(contextFeatures["distance"])
		score *= (1 + adjustedDegree*0.1)
	}

	// 4. 特殊句式模式匹配
	if patternScore := a.matchSpecialPatterns(contextFeatures); patternScore != 0 {
		score += patternScore
	}

	// 5. 情感一致性校验
	if a.checkSentimentConsistency(entry, contextFeatures) {
		score *= 1.2 // 一致性奖励
	} else {
		score *= 0.8 // 不一致惩罚
	}

	// 6. 动态阈值调整
	dynamicThreshold := a.calculateDynamicThreshold(len(contextFeatures))
	if math.Abs(score) < dynamicThreshold {
		score = 0 // 低于阈值视为中性
	}

	return score
}

// calculateDecayFactor 计算情感强度衰减因子
// 支持多种衰减模式：
// - 指数衰减（默认）
// - 线性衰减（当distance < 3时）
// - 阶梯衰减（当contextFeatures["special_pattern"]存在时）
func (a *Analyzer) calculateDecayFactor(distance float64) float64 {
	const (
		baseDecay       = 0.8
		linearThreshold = 3.0
	)

	// 线性衰减（短距离效果更好）
	if distance < linearThreshold {
		return 1.0 - (distance/linearThreshold)*0.5
	}

	// 指数衰减（长距离）
	return math.Pow(baseDecay, distance)
}

// matchSpecialPatterns 匹配特殊句式模式
func (a *Analyzer) matchSpecialPatterns(contextFeatures map[string]float64) float64 {
	// 实现特殊句式匹配逻辑
	// 例如："虽然...但是..."、"即使...也..."等
	return 0 // 默认返回0
}

// checkSentimentConsistency 情感一致性校验
// 检查当前词义与上下文情感倾向是否一致
func (a *Analyzer) checkSentimentConsistency(entry DictEntry, contextFeatures map[string]float64) bool {
	// 1. 中性词直接通过
	if entry.Polarity == 0 {
		return true
	}

	// 2. 计算上下文情感倾向
	contextScore := 0.0
	for emotion, intensity := range contextFeatures {
		if strings.Contains(emotion, "正面") {
			contextScore += intensity
		} else if strings.Contains(emotion, "负面") {
			contextScore -= intensity
		}
	}

	// 3. 判断一致性
	switch entry.Polarity {
	case 1: // 褒义词
		return contextScore >= -0.5 // 允许轻微负面
	case 2: // 贬义词
		return contextScore <= 0.5 // 允许轻微正面
	case 3: // 兼有褒贬
		return math.Abs(contextScore) <= 1.0
	default:
		return true
	}
}

// calculateDynamicThreshold 计算动态阈值
// 根据上下文长度和情感强度分布自动调整
func (a *Analyzer) calculateDynamicThreshold(contextSize int) float64 {
	baseThreshold := 0.1

	// 1. 长度因子：文本越长阈值越低
	lengthFactor := 1.0 / math.Log(float64(contextSize+2))

	// 2. 强度因子：上下文情感强度越高阈值越高
	intensityFactor := 1.0
	if contextSize > 0 {
		// 模拟获取平均情感强度（实际应从contextFeatures计算）
		avgIntensity := math.Min(1.0, 0.3+float64(contextSize)/20.0)
		intensityFactor = 0.7 + avgIntensity*0.3
	}

	// 3. 组合因子
	return baseThreshold * lengthFactor * intensityFactor
}

// Close 关闭分析器，释放资源
func (a *Analyzer) Close() {
	if a.jieba != nil {
		a.jieba.Free()
	}
}

// loadResources 懒加载资源
func (a *Analyzer) loadResources() error {
	// 加载情感词典
	if err := a.loadDictionary(); err != nil {
		return fmt.Errorf("failed to load dictionary: %w", err)
	}

	// 加载停用词
	if err := a.loadStopWords(); err != nil {
		return fmt.Errorf("failed to load stop words: %w", err)
	}

	a.loaded = true
	return nil
}

// 初始化词性映射
func (a *Analyzer) initPOSMapping() {
	// jieba词性到词典词性的映射
	mapping := map[string]string{
		"a":  "adj",   // 形容词 -> adj
		"ad": "adj",   // 副形词 -> adj
		"an": "adj",   // 名形词 -> adj
		"d":  "adv",   // 副词 -> adv
		"dg": "adv",   // 副语素 -> adv
		"dl": "adv",   // 副词性惯用语 -> adv
		"v":  "verb",  // 动词 -> verb
		"vd": "verb",  // 副动词 -> verb
		"vn": "verb",  // 名动词 -> verb
		"vg": "verb",  // 动语素 -> verb
		"vi": "verb",  // 不及物动词 -> verb
		"vl": "verb",  // 动词性惯用语 -> verb
		"vu": "verb",  // 能愿动词 -> verb
		"n":  "noun",  // 名词 -> noun
		"nr": "noun",  // 人名 -> noun
		"ns": "noun",  // 地名 -> noun
		"nt": "noun",  // 机构名 -> noun
		"nz": "noun",  // 其他专名 -> noun
		"ng": "noun",  // 名语素 -> noun
		"nl": "noun",  // 名词性惯用语 -> noun
		"zg": "adv",   // 状语标记，通常是程度副词，映射为副词
		"i":  "idiom", // 成语
		"j":  "abbr",  // 简称略语
		"l":  "idiom", // 习用语
	}

	for jiebaPOS, dictPOS := range mapping {
		a.posMapping[jiebaPOS] = dictPOS
	}
}

// 初始化标点符号集合
func (a *Analyzer) initPunctuationSet() {
	// 常见的标点符号
	punctuations := `!"#$%&'()*+,-./:;<=>?@[\]^_{|}~，。！？；：""''（）【】《》、`
	for _, r := range punctuations {
		a.punctuationSet[r] = true
	}
}

// 初始化否定词列表（从配置加载）
func (a *Analyzer) initNegativeWordsFromConfig() {
	for _, word := range a.config.NegativeWords {
		a.negativeWords[word] = true
	}
}

// 初始化程度副词列表（从配置加载）
func (a *Analyzer) initDegreeWordsFromConfig() {
	maps.Copy(a.degreeWords, a.config.DegreeWords)
}

// LoadResourcesForTesting 测试用的资源加载方法
func (a *Analyzer) LoadResourcesForTesting() error {
	return a.loadResources()
}

// GetDictionaryEntries 测试用的词典查询方法
func (a *Analyzer) GetDictionaryEntries(word string) []DictEntry {
	if entries, found := a.dictionary[word]; found {
		return entries
	}
	return []DictEntry{}
}

// DebugSegment 调试用方法，显示分词结果
func (a *Analyzer) DebugSegment(text string) ([]TokenWithPOS, error) {
	if !a.loaded {
		if err := a.loadResources(); err != nil {
			return nil, fmt.Errorf("failed to load resources: %w", err)
		}
	}

	// 文本预处理和分词
	tokens, err := a.preprocessText(text)
	if err != nil {
		return nil, fmt.Errorf("failed to preprocess text: %w", err)
	}

	return tokens, nil
}
