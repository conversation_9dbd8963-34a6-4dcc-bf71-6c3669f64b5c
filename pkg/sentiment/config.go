package sentiment

import (
	"encoding/json"
	"fmt"
	"os"
)

// EmotionConfig 情感配置结构
type EmotionConfig struct {
	Score     float64 `json:"score"`
	Intensity float64 `json:"intensity"`
}

// SemanticRulesConfig 语义规则配置
type SemanticRulesConfig struct {
	NegativeWords          []string                            `json:"negativeWords"`
	DegreeWords            map[string]float64                  `json:"degreeWords"`
	VerbObjectCombinations map[string]map[string]EmotionConfig `json:"verbObjectCombinations"`
	ObjectVerbCombinations map[string]map[string]EmotionConfig `json:"objectVerbCombinations"`
	PositiveActions        map[string]EmotionConfig            `json:"positiveActions"`
	NegativeActions        map[string]EmotionConfig            `json:"negativeActions"`
	BasicEmotions          map[string]EmotionConfig            `json:"basicEmotions"`
	StateEmotions          map[string]EmotionConfig            `json:"stateEmotions"`
	OtherEntities          []string                            `json:"otherEntities"`
	ContextSettings        ContextSettings                     `json:"contextSettings"`
}

// ContextSettings 上下文设置
type ContextSettings struct {
	NegationRange int `json:"negationRange"`
	DegreeRange   int `json:"degreeRange"`
	ContextRange  int `json:"contextRange"`
}

// LoadSemanticRulesConfig 从文件加载语义规则配置
func LoadSemanticRulesConfig(configPath string) (*SemanticRulesConfig, error) {
	if configPath == "" {
		return getDefaultConfig(), nil
	}

	file, err := os.Open(configPath)
	if err != nil {
		fmt.Printf("Warning: cannot open config file %s, using default: %v\n", configPath, err)
		return getDefaultConfig(), nil
	}
	defer file.Close()

	var config SemanticRulesConfig
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&config); err != nil {
		return nil, fmt.Errorf("failed to parse config file %s: %w", configPath, err)
	}

	return &config, nil
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *SemanticRulesConfig {
	return &SemanticRulesConfig{
		NegativeWords: []string{
			"不", "不是", "没", "没有", "非", "无", "否", "别", "勿", "莫",
			"未", "不曾", "不能", "不会", "不要", "不用", "不必", "不该",
			"不应", "不可", "不许", "不准", "不得", "禁止", "拒绝", "反对",
		},
		DegreeWords: map[string]float64{
			"极其": 2.0, "极度": 2.0, "极为": 2.0, "极": 2.0,
			"非常": 1.8, "特别": 1.8, "超级": 1.8, "十分": 1.8,
			"很": 1.5, "相当": 1.5, "挺": 1.5, "蛮": 1.5,
			"比较": 1.2, "较": 1.2, "还": 1.2, "算": 1.2,
			"稍微": 0.8, "略": 0.8, "有点": 0.8, "一点": 0.8,
			"太": 1.6, "过于": 1.6, "过分": 1.6,
			"最": 2.2, "首屈一指": 2.2, "无比": 2.2,
		},
		OtherEntities: []string{
			"小狗", "狗", "猫", "动物",
			"孩子", "小孩", "婴儿",
			"老人", "病人", "患者",
			"他", "她", "它",
			"朋友", "家人", "亲人",
			"同事", "邻居", "陌生人",
		},
		ContextSettings: ContextSettings{
			NegationRange: 2,
			DegreeRange:   2,
			ContextRange:  3,
		},
	}
}
