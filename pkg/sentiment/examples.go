package sentiment

import (
	"fmt"
	"log"
)

// Example 展示如何使用情感分析包
func Example() {
	// 创建分析器实例
	// 需要提供词典文件路径，停用词文件路径可选
	analyzer, err := NewAnalyzer("storage/dict.xlsx", "")
	if err != nil {
		log.Fatalf("创建分析器失败: %v", err)
	}
	defer analyzer.Close() // 记得释放资源

	// 分析单个文本
	texts := []string{
		"这部电影非常好看，我很喜欢",
		"今天天气真糟糕，让人很不开心",
		"这个产品质量一般，没什么特别的",
		"我不喜欢这个设计，太难看了",
		"虽然有些小问题，但总体还是不错的",
	}

	for _, text := range texts {
		score, err := analyzer.Analyze(text)
		if err != nil {
			fmt.Printf("分析文本失败: %v\n", err)
			continue
		}

		// 解释得分
		var sentiment string
		if score > 0.1 {
			sentiment = "正面"
		} else if score < -0.1 {
			sentiment = "负面"
		} else {
			sentiment = "中性"
		}

		fmt.Printf("文本: %s\n", text)
		fmt.Printf("得分: %.3f (%s)\n", score, sentiment)
		fmt.Println("---")
	}
}

// AnalyzeWithDetails 提供详细的分析信息（用于调试）
func (a *Analyzer) AnalyzeWithDetails(text string) (float64, map[string]interface{}, error) {
	if !a.loaded {
		if err := a.loadResources(); err != nil {
			return 0, nil, fmt.Errorf("failed to load resources: %w", err)
		}
	}

	details := make(map[string]interface{})

	// 文本预处理
	tokens, err := a.preprocessText(text)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to preprocess text: %w", err)
	}

	details["tokens"] = tokens
	details["token_count"] = len(tokens)

	if len(tokens) == 0 {
		details["emotion_scores"] = []EmotionScore{}
		details["sum_effective_scores"] = 0.0
		details["sum_absolute_effective_scores"] = 0.0
		return 0, details, nil
	}

	// 收集所有情感得分
	var allScores []EmotionScore
	var sumEffectiveScores float64
	var sumAbsoluteEffectiveScores float64

	for i, token := range tokens {
		scores := a.getEmotionScores(token, tokens, i)
		for _, score := range scores {
			if score.Score != 0 {
				allScores = append(allScores, score)
				sumEffectiveScores += score.Score
				sumAbsoluteEffectiveScores += abs(score.Score)
			}
		}
	}

	details["emotion_scores"] = allScores
	details["sum_effective_scores"] = sumEffectiveScores
	details["sum_absolute_effective_scores"] = sumAbsoluteEffectiveScores

	// 计算最终得分
	var finalScore float64
	if sumAbsoluteEffectiveScores != 0 {
		finalScore = sumEffectiveScores / sumAbsoluteEffectiveScores
		if finalScore > 1 {
			finalScore = 1
		} else if finalScore < -1 {
			finalScore = -1
		}
	}

	details["final_score"] = finalScore

	return finalScore, details, nil
}

// abs 计算浮点数的绝对值
func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
