package sentiment

import (
	"math"
)

// calculateSentiment 计算情感得分
func (a *Analyzer) calculateSentiment(tokens []TokenWithPOS) (float64, error) {
	if len(tokens) == 0 {
		return 0, nil
	}

	var sumEffectiveScores float64
	var sumAbsoluteEffectiveScores float64
	var skipIndices map[int]bool = make(map[int]bool) // 记录需要跳过的词语索引

	// 先找出所有的动宾组合，标记需要跳过的索引
	for i, token := range tokens {
		if token.POS == "v" { // 只对动词检查
			if specialScore := a.getVerbObjectSentiment(tokens, i); specialScore != nil {
				// 添加组合得分
				sumEffectiveScores += specialScore.Score
				sumAbsoluteEffectiveScores += math.Abs(specialScore.Score)

				// 标记参与组合的词语，避免重复计算
				skipIndices[i] = true // 动词本身
				if i > 0 {            // 如果有前置词语参与组合
					skipIndices[i-1] = true
				}
				if i < len(tokens)-1 { // 如果有后续词语参与组合
					skipIndices[i+1] = true
				}
			}
		}
	}

	// 处理其他没有参与动宾组合的词语
	for i, token := range tokens {
		if skipIndices[i] { // 跳过已经在动宾组合中计算过的词语
			continue
		}

		// 获取当前词语的情感得分（排除动宾组合检查）
		scores := a.getEmotionScoresExcludeVerbObject(token, tokens, i)

		for _, score := range scores {
			if score.Score != 0 {
				sumEffectiveScores += score.Score
				sumAbsoluteEffectiveScores += math.Abs(score.Score)
			}
		}
	}

	// 归一化
	if sumAbsoluteEffectiveScores == 0 {
		return 0, nil
	}

	result := sumEffectiveScores / sumAbsoluteEffectiveScores

	// 确保结果在 [-1, 1] 范围内
	if result > 1 {
		result = 1
	} else if result < -1 {
		result = -1
	}

	return result, nil
}

// getEmotionScores 获取词语的情感得分
func (a *Analyzer) getEmotionScores(token TokenWithPOS, tokens []TokenWithPOS, index int) []EmotionScore {
	var scores []EmotionScore

	// 1. 检查动词+宾语的特殊组合 (只对动词调用)
	if token.POS == "v" { // 只对动词调用
		if specialScore := a.getVerbObjectSentiment(tokens, index); specialScore != nil {
			scores = append(scores, *specialScore)
			return scores // 特殊组合优先，直接返回
		}
	}

	// 2. 检查z类状语词的情感映射
	if stateScore := a.getStateWordEmotion(token.Word, token.POS); stateScore != nil {
		adjustedScore := a.applyContextualModifications(*stateScore, tokens, index)
		scores = append(scores, adjustedScore)
		return scores
	}

	// 3. 检查动作情感映射
	if actionScore := a.getActionEmotion(token.Word, token.POS); actionScore != nil {
		adjustedScore := a.applyContextualModifications(*actionScore, tokens, index)
		scores = append(scores, adjustedScore)
		return scores
	}

	// 4. 查找词典中的匹配项
	entries := a.findDictionaryEntries(token)
	if len(entries) == 0 {
		// 5. 如果词典中没有，尝试基本情感词补充
		if basicScore := a.getBasicEmotion(token.Word, token.POS); basicScore != nil {
			adjustedScore := a.applyContextualModifications(*basicScore, tokens, index)
			scores = append(scores, adjustedScore)
		}
		return scores
	}

	// 检查是否有否定词修饰
	isNegated := a.isNegated(tokens, index)

	// 检查是否有程度副词修饰
	degreeMultiplier := a.getDegreeMultiplier(tokens, index)

	// 处理每个匹配的词典条目
	for _, entry := range entries {
		// 处理主要情感
		if entry.Polarity != 0 && entry.Intensity > 0 {
			score := a.calculateEntryScore(entry, entry.Polarity, entry.Intensity,
				isNegated, degreeMultiplier, tokens, index)
			if score.Score != 0 {
				// 应用上下文感知调整
				adjustedScore := a.adjustEmotionByContext(tokens, index, score)
				scores = append(scores, adjustedScore)
			}
		}

		// 处理辅助情感
		if entry.AuxPolarity != 0 && entry.AuxIntensity > 0 {
			score := a.calculateEntryScore(entry, entry.AuxPolarity, entry.AuxIntensity,
				isNegated, degreeMultiplier, tokens, index)
			if score.Score != 0 {
				// 应用上下文感知调整
				adjustedScore := a.adjustEmotionByContext(tokens, index, score)
				scores = append(scores, adjustedScore)
			}
		}
	}

	return scores
}

// applyContextualModifications 统一应用上下文修饰（否定词、程度副词、上下文调整）
func (a *Analyzer) applyContextualModifications(score EmotionScore, tokens []TokenWithPOS, index int) EmotionScore {
	// 检查是否有否定词修饰
	isNegated := a.isNegated(tokens, index)

	// 检查是否有程度副词修饰
	degreeMultiplier := a.getDegreeMultiplier(tokens, index)

	// 应用程度副词
	adjustedScore := score
	adjustedScore.Intensity *= degreeMultiplier
	adjustedScore.Score *= degreeMultiplier

	// 应用否定词
	if isNegated {
		adjustedScore.Score = -adjustedScore.Score
	}

	// 应用上下文感知调整
	finalScore := a.adjustEmotionByContext(tokens, index, adjustedScore)

	return finalScore
}

// getEmotionScoresExcludeVerbObject 获取词语的情感得分(排除动宾组合检查)
func (a *Analyzer) getEmotionScoresExcludeVerbObject(token TokenWithPOS, tokens []TokenWithPOS, index int) []EmotionScore {
	var scores []EmotionScore

	// 1. 检查z类状语词的情感映射
	if stateScore := a.getStateWordEmotion(token.Word, token.POS); stateScore != nil {
		adjustedScore := a.applyContextualModifications(*stateScore, tokens, index)
		scores = append(scores, adjustedScore)
		return scores
	}

	// 2. 检查动作情感映射
	if actionScore := a.getActionEmotion(token.Word, token.POS); actionScore != nil {
		adjustedScore := a.applyContextualModifications(*actionScore, tokens, index)
		scores = append(scores, adjustedScore)
		return scores
	}

	// 3. 查找词典中的匹配项
	entries := a.findDictionaryEntries(token)
	if len(entries) == 0 {
		// 4. 如果词典中没有，尝试基本情感词补充
		if basicScore := a.getBasicEmotion(token.Word, token.POS); basicScore != nil {
			adjustedScore := a.applyContextualModifications(*basicScore, tokens, index)
			scores = append(scores, adjustedScore)
		}
		return scores
	}

	// 检查是否有否定词修饰
	isNegated := a.isNegated(tokens, index)

	// 检查是否有程度副词修饰
	degreeMultiplier := a.getDegreeMultiplier(tokens, index)

	// 处理每个匹配的词典条目
	for _, entry := range entries {
		// 处理主要情感
		if entry.Polarity != 0 && entry.Intensity > 0 {
			score := a.calculateEntryScore(entry, entry.Polarity, entry.Intensity,
				isNegated, degreeMultiplier, tokens, index)
			if score.Score != 0 {
				// 应用上下文感知调整
				adjustedScore := a.adjustEmotionByContext(tokens, index, score)
				scores = append(scores, adjustedScore)
			}
		}

		// 处理辅助情感
		if entry.AuxPolarity != 0 && entry.AuxIntensity > 0 {
			score := a.calculateEntryScore(entry, entry.AuxPolarity, entry.AuxIntensity,
				isNegated, degreeMultiplier, tokens, index)
			if score.Score != 0 {
				// 应用上下文感知调整
				adjustedScore := a.adjustEmotionByContext(tokens, index, score)
				scores = append(scores, adjustedScore)
			}
		}
	}

	return scores
}

// findDictionaryEntries 查找词典条目
func (a *Analyzer) findDictionaryEntries(token TokenWithPOS) []DictEntry {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// 1. 优先匹配 "词语+词性"
	if dictPOS, exists := a.posMapping[token.POS]; exists {
		if entries, found := a.dictionary[token.Word]; found {
			var matchedEntries []DictEntry
			for _, entry := range entries {
				if entry.POS == dictPOS {
					matchedEntries = append(matchedEntries, entry)
				}
			}
			if len(matchedEntries) > 0 {
				return a.consolidateEntries(matchedEntries)
			}
		}
	}

	// 2. 回退到仅匹配 "词语"
	if entries, found := a.dictionary[token.Word]; found {
		return a.consolidateEntries(entries)
	}

	return []DictEntry{}
}

// consolidateEntries 智能合并多义词条目，改进消歧逻辑
func (a *Analyzer) consolidateEntries(entries []DictEntry) []DictEntry {
	if len(entries) == 1 {
		return entries
	}

	// 按极性分组
	polarityGroups := make(map[int][]DictEntry)
	for _, entry := range entries {
		polarityGroups[entry.Polarity] = append(polarityGroups[entry.Polarity], entry)
	}

	var result []DictEntry
	for _, group := range polarityGroups {
		if len(group) == 1 {
			result = append(result, group[0])
		} else {
			// 合并同极性条目时，选择强度最高的或计算加权平均
			merged := a.mergeEntriesWithSamePolarity(group)
			result = append(result, merged)
		}
	}

	// 智能处理多义词：如果有多个不同极性的条目
	if len(result) > 1 {
		// 不是简单地视为中性，而是基于上下文选择最合适的义项
		bestEntry := a.selectBestEntryByContext(result)
		if bestEntry != nil {
			return []DictEntry{*bestEntry}
		}
		// 如果无法确定上下文，则返回空表示中性
		return []DictEntry{}
	}

	return result
}

// mergeEntriesWithSamePolarity 合并同极性条目
func (a *Analyzer) mergeEntriesWithSamePolarity(entries []DictEntry) DictEntry {
	if len(entries) == 1 {
		return entries[0]
	}

	// 选择强度最高的条目作为基础
	bestEntry := entries[0]
	maxIntensity := entries[0].Intensity

	for _, entry := range entries[1:] {
		if entry.Intensity > maxIntensity {
			maxIntensity = entry.Intensity
			bestEntry = entry
		}
	}

	// 可以考虑使用加权平均，但这里选择最强的义项
	return bestEntry
}

// selectBestEntryByContext 基于上下文选择最佳义项
func (a *Analyzer) selectBestEntryByContext(entries []DictEntry) *DictEntry {
	// 这里可以实现更复杂的上下文分析
	// 目前的简化实现：优先选择强度更高的义项
	var bestEntry *DictEntry
	maxIntensity := 0.0

	for i := range entries {
		if entries[i].Intensity > maxIntensity {
			maxIntensity = entries[i].Intensity
			bestEntry = &entries[i]
		}
	}

	return bestEntry
}

// calculateEntryScore 计算单个条目的情感得分
func (a *Analyzer) calculateEntryScore(entry DictEntry, polarity int, intensity float64,
	isNegated bool, degreeMultiplier float64, tokens []TokenWithPOS, index int,
) EmotionScore {
	// 处理"极性为3"（兼有褒贬）的词语
	if polarity == 3 {
		polarity = a.resolveAmbiguousPolarity(tokens, index)
		if polarity == 0 {
			return EmotionScore{Score: 0, Intensity: 0}
		}
	}

	// 转换极性：0=中性(跳过), 1=正面(+1), 2=负面(-1)
	var polarityValue float64
	switch polarity {
	case 0:
		return EmotionScore{Score: 0, Intensity: 0}
	case 1:
		polarityValue = 1.0
	case 2:
		polarityValue = -1.0
	default:
		return EmotionScore{Score: 0, Intensity: 0}
	}

	// 应用程度副词
	adjustedIntensity := intensity * degreeMultiplier

	// 计算基础得分
	score := polarityValue * adjustedIntensity

	// 应用否定词（极性反转，强度不变）
	if isNegated {
		score = -score
	}

	return EmotionScore{
		Score:     score,
		Intensity: adjustedIntensity,
	}
}

// resolveAmbiguousPolarity 解决"极性为3"词语的极性
func (a *Analyzer) resolveAmbiguousPolarity(tokens []TokenWithPOS, index int) int {
	// 检查前一个词
	var prevPolarity int
	if index > 0 {
		prevEntries := a.findDictionaryEntries(tokens[index-1])
		for _, entry := range prevEntries {
			if entry.Polarity == 1 || entry.Polarity == 2 {
				prevPolarity = entry.Polarity
				break
			}
		}
	}

	// 检查后一个词
	var nextPolarity int
	if index < len(tokens)-1 {
		nextEntries := a.findDictionaryEntries(tokens[index+1])
		for _, entry := range nextEntries {
			if entry.Polarity == 1 || entry.Polarity == 2 {
				nextPolarity = entry.Polarity
				break
			}
		}
	}

	// 判断邻近词的情感方向
	if prevPolarity != 0 && nextPolarity != 0 {
		// 如果前后都有明确情感且方向一致
		if prevPolarity == nextPolarity {
			return prevPolarity
		}
		// 如果方向冲突，视为中性
		return 0
	} else if prevPolarity != 0 {
		// 只有前词有明确情感
		return prevPolarity
	} else if nextPolarity != 0 {
		// 只有后词有明确情感
		return nextPolarity
	}

	// 没有明确的情感信号，视为中性
	return 0
}

// isNegated 检查是否被否定词修饰（使用可配置的范围）
func (a *Analyzer) isNegated(tokens []TokenWithPOS, index int) bool {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// 使用配置中的否定词范围
	negationRange := a.config.ContextSettings.NegationRange
	for i := index - 1; i >= 0 && i >= index-negationRange; i-- {
		if a.negativeWords[tokens[i].Word] {
			return true
		}
	}
	return false
}

// getDegreeMultiplier 获取程度副词权重（使用可配置的范围）
func (a *Analyzer) getDegreeMultiplier(tokens []TokenWithPOS, index int) float64 {
	a.mu.RLock()
	defer a.mu.RUnlock()

	// 使用配置中的程度副词范围
	degreeRange := a.config.ContextSettings.DegreeRange
	for i := index - 1; i >= 0 && i >= index-degreeRange; i-- {
		if multiplier, exists := a.degreeWords[tokens[i].Word]; exists {
			return multiplier
		}
	}
	return 1.0 // 默认权重
}
