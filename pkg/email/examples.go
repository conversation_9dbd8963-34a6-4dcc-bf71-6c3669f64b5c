package email

import (
	"context"
	"time"
)

// Examples 提供邮件工具的使用示例
type Examples struct{}

// BasicEmailExample 基本邮件发送示例
func (e *Examples) BasicEmailExample() error {
	// 创建邮件配置
	config := &EmailConfig{
		ServerHost:  "smtp.gmail.com",
		ServerPort:  587,
		Username:    "<EMAIL>",
		Password:    "your-app-password",
		FromName:    "系统管理员",
		UseTLS:      true,
		MaxRetries:  3,
		RetryDelay:  time.Second * 5,
		PoolSize:    10,
		MaxFileSize: 25 * 1024 * 1024, // 25MB
	}

	// 创建邮件客户端
	client, err := NewEmailClient(config)
	if err != nil {
		return err
	}
	defer client.Close()

	// 创建邮件消息
	msg := &EmailMessage{
		To:       []string{"<EMAIL>"},
		CC:       []string{"<EMAIL>"},
		Subject:  "测试邮件",
		TextBody: "这是一封测试邮件的文本内容",
		HTMLBody: "<h1>测试邮件</h1><p>这是一封测试邮件的HTML内容</p>",
		IsHTML:   true,
		Priority: PriorityNormal,
	}

	// 发送邮件
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute*5)
	defer cancel()

	return client.SendEmail(ctx, msg)
}

// TemplateEmailExample 模板邮件发送示例
func (e *Examples) TemplateEmailExample() error {
	config := &EmailConfig{
		ServerHost: "smtp.gmail.com",
		ServerPort: 587,
		Username:   "<EMAIL>",
		Password:   "your-app-password",
		FromName:   "系统管理员",
		UseTLS:     true,
	}

	client, err := NewEmailClient(config)
	if err != nil {
		return err
	}
	defer client.Close()

	// 注册邮件模板
	welcomeTemplate := &EmailTemplate{
		Name:    "welcome",
		Subject: "欢迎 {{.Name}} 加入我们！",
		HTMLBody: `
			<h1>欢迎 {{.Name}}！</h1>
			<p>感谢您注册我们的服务。您的用户名是：<strong>{{.Username}}</strong></p>
			<p>注册时间：{{.RegisterTime}}</p>
			<p>如有任何问题，请随时联系我们。</p>
		`,
		TextBody: "欢迎 {{.Name}}！感谢您注册我们的服务。用户名：{{.Username}}，注册时间：{{.RegisterTime}}",
	}
	client.RegisterTemplate(welcomeTemplate)

	// 准备模板数据
	templateData := map[string]interface{}{
		"Name":         "张三",
		"Username":     "zhangsan",
		"RegisterTime": time.Now().Format("2006-01-02 15:04:05"),
	}

	// 创建邮件消息
	msg := &EmailMessage{
		To: []string{"<EMAIL>"},
	}

	// 使用模板发送邮件
	ctx := context.Background()
	return client.SendTemplate(ctx, "welcome", templateData, msg)
}

// BatchEmailExample 批量邮件发送示例
func (e *Examples) BatchEmailExample() error {
	config := &EmailConfig{
		ServerHost: "smtp.gmail.com",
		ServerPort: 587,
		Username:   "<EMAIL>",
		Password:   "your-app-password",
		FromName:   "系统管理员",
		UseTLS:     true,
		PoolSize:   20, // 增大连接池以提高并发性能
	}

	client, err := NewEmailClient(config)
	if err != nil {
		return err
	}
	defer client.Close()

	// 批量发送邮件
	recipients := []string{
		"<EMAIL>",
		"<EMAIL>",
		"<EMAIL>",
	}

	ctx := context.Background()

	for _, recipient := range recipients {
		msg := &EmailMessage{
			To:       []string{recipient},
			Subject:  "批量通知邮件",
			HTMLBody: "<p>这是一封批量发送的通知邮件</p>",
			IsHTML:   true,
			Priority: PriorityNormal,
		}

		if err := client.SendEmail(ctx, msg); err != nil {
			// 记录失败但继续发送其他邮件
			// log.Printf("Failed to send email to %s: %v", recipient, err)
			continue
		}
	}

	return nil
}

// AttachmentEmailExample 带附件的邮件示例
func (e *Examples) AttachmentEmailExample() error {
	config := &EmailConfig{
		ServerHost:  "smtp.gmail.com",
		ServerPort:  587,
		Username:    "<EMAIL>",
		Password:    "your-app-password",
		FromName:    "系统管理员",
		UseTLS:      true,
		MaxFileSize: 50 * 1024 * 1024, // 50MB
	}

	client, err := NewEmailClient(config)
	if err != nil {
		return err
	}
	defer client.Close()

	msg := &EmailMessage{
		To:      []string{"<EMAIL>"},
		Subject: "带附件的邮件",
		HTMLBody: `
			<p>您好，</p>
			<p>请查收附件中的文档。</p>
			<p>谢谢！</p>
		`,
		IsHTML: true,
		Attachments: []string{
			"/path/to/document.pdf",
			"/path/to/image.jpg",
		},
		Priority: PriorityHigh,
	}

	ctx := context.Background()
	return client.SendEmail(ctx, msg)
}

// ProviderConfigExample 使用预设提供商配置的示例
func (e *Examples) ProviderConfigExample() error {
	// 使用预设的Gmail配置
	config := NewConfigForProvider("gmail", "<EMAIL>", "your-app-password")
	config.FromName = "我的应用"

	client, err := NewEmailClient(config)
	if err != nil {
		return err
	}
	defer client.Close()

	msg := &EmailMessage{
		To:       []string{"<EMAIL>"},
		Subject:  "使用预设配置发送的邮件",
		HTMLBody: "<p>这是使用Gmail预设配置发送的邮件</p>",
		IsHTML:   true,
	}

	ctx := context.Background()
	return client.SendEmail(ctx, msg)
}

// EnvConfigExample 使用环境变量配置的示例
func (e *Examples) EnvConfigExample() error {
	// 从环境变量加载配置
	config := LoadEmailConfigFromEnv()

	client, err := NewEmailClient(config)
	if err != nil {
		return err
	}
	defer client.Close()

	msg := &EmailMessage{
		To:       []string{"<EMAIL>"},
		Subject:  "使用环境变量配置发送的邮件",
		HTMLBody: "<p>这是使用环境变量配置发送的邮件</p>",
		IsHTML:   true,
	}

	ctx := context.Background()
	return client.SendEmail(ctx, msg)
}
