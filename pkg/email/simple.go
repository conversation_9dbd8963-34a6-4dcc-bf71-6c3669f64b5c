package email

import (
	"context"

	"github.com/pkg/errors"
)

// SimpleEmailSender 简单邮件发送器（兼容旧接口）
type SimpleEmailSender struct {
	client *EmailClient
}

// NewSimpleEmailSender 创建简单邮件发送器
func NewSimpleEmailSender(config *EmailConfig) (*SimpleEmailSender, error) {
	client, err := NewEmailClient(config)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create email client")
	}

	return &SimpleEmailSender{
		client: client,
	}, nil
}

// SendMessage 发送邮件（兼容原有接口）
func (s *SimpleEmailSender) SendMessage(targets []string, subject, content string, attachments []string) error {
	msg := &EmailMessage{
		To:          targets,
		Subject:     subject,
		TextBody:    content,
		Attachments: attachments,
		IsHTML:      false,
	}

	return s.client.SendEmail(context.Background(), msg)
}

// SendHTMLMessage 发送HTML邮件
func (s *SimpleEmailSender) SendHTMLMessage(targets []string, subject, htmlContent string, attachments []string) error {
	msg := &EmailMessage{
		To:          targets,
		Subject:     subject,
		HTMLBody:    htmlContent,
		Attachments: attachments,
		IsHTML:      true,
	}

	return s.client.SendEmail(context.Background(), msg)
}

// SendMessageWithCC 发送带抄送的邮件
func (s *SimpleEmailSender) SendMessageWithCC(to, cc []string, subject, content string, attachments []string) error {
	msg := &EmailMessage{
		To:          to,
		CC:          cc,
		Subject:     subject,
		TextBody:    content,
		Attachments: attachments,
		IsHTML:      false,
	}

	return s.client.SendEmail(context.Background(), msg)
}

// Close 关闭发送器
func (s *SimpleEmailSender) Close() error {
	return s.client.Close()
}

// 全局简单发送函数（不推荐在生产环境使用，仅用于快速测试）

// QuickSend 快速发送邮件（使用默认配置）
func QuickSend(serverHost string, serverPort int, username, password string, to []string, subject, content string) error {
	config := &EmailConfig{
		ServerHost: serverHost,
		ServerPort: serverPort,
		Username:   username,
		Password:   password,
		UseTLS:     true,
	}

	sender, err := NewSimpleEmailSender(config)
	if err != nil {
		return err
	}
	defer sender.Close()

	return sender.SendMessage(to, subject, content, nil)
}

// QuickSendHTML 快速发送HTML邮件
func QuickSendHTML(serverHost string, serverPort int, username, password string, to []string, subject, htmlContent string) error {
	config := &EmailConfig{
		ServerHost: serverHost,
		ServerPort: serverPort,
		Username:   username,
		Password:   password,
		UseTLS:     true,
	}

	sender, err := NewSimpleEmailSender(config)
	if err != nil {
		return err
	}
	defer sender.Close()

	return sender.SendHTMLMessage(to, subject, htmlContent, nil)
}
