# Email Package

一个功能丰富的Go邮件发送包，支持连接池、模板、重试机制等企业级特性。

## 特性

- ✅ **连接池管理** - 高效的SMTP连接复用
- ✅ **HTML邮件支持** - 支持富文本邮件格式
- ✅ **邮件模板** - 使用Go template语法渲染邮件
- ✅ **抄送/密送** - 支持CC和BCC功能
- ✅ **邮件优先级** - 低、普通、高、紧急四个级别
- ✅ **重试机制** - 自动重试失败的发送
- ✅ **SSL/TLS支持** - 安全的邮件传输
- ✅ **附件支持** - 支持多文件附件，大小限制
- ✅ **配置验证** - 严格的参数验证
- ✅ **环境变量配置** - 灵活的配置方式
- ✅ **预设配置** - 常用邮件服务商配置
- ✅ **并发安全** - 线程安全设计
- ✅ **详细日志** - 完整的发送日志记录

## 安装

```bash
go get -u your-module/pkg/email
```

## 快速开始

### 基本使用

```go
package main

import (
    "context"
    "log"
    "your-module/pkg/email"
)

func main() {
    // 创建邮件配置
    config := &email.EmailConfig{
        ServerHost: "smtp.gmail.com",
        ServerPort: 587,
        Username:   "<EMAIL>",
        Password:   "your-app-password",
        FromName:   "我的应用",
        UseTLS:     true,
    }

    // 创建邮件客户端
    client, err := email.NewEmailClient(config)
    if err != nil {
        log.Fatal(err)
    }
    defer client.Close()

    // 创建邮件消息
    msg := &email.EmailMessage{
        To:       []string{"<EMAIL>"},
        Subject:  "测试邮件",
        HTMLBody: "<h1>Hello World!</h1><p>这是一封测试邮件</p>",
        IsHTML:   true,
    }

    // 发送邮件
    ctx := context.Background()
    if err := client.SendEmail(ctx, msg); err != nil {
        log.Fatal(err)
    }

    log.Println("邮件发送成功!")
}
```

### 使用预设配置

```go
// 使用Gmail预设配置
config := email.NewConfigForProvider("gmail", "<EMAIL>", "your-password")
config.FromName = "我的应用"

client, err := email.NewEmailClient(config)
if err != nil {
    log.Fatal(err)
}
defer client.Close()
```

### 环境变量配置

设置环境变量：
```bash
export EMAIL_SERVER_HOST=smtp.gmail.com
export EMAIL_SERVER_PORT=587
export EMAIL_USERNAME=<EMAIL>
export EMAIL_PASSWORD=your-app-password
export EMAIL_FROM_NAME=我的应用
export EMAIL_USE_TLS=true
```

使用配置：
```go
config := email.LoadEmailConfigFromEnv()
client, err := email.NewEmailClient(config)
```

### 邮件模板

```go
// 注册模板
template := &email.EmailTemplate{
    Name:    "welcome",
    Subject: "欢迎 {{.Name}} 加入我们！",
    HTMLBody: `
        <h1>欢迎 {{.Name}}！</h1>
        <p>您的用户名：<strong>{{.Username}}</strong></p>
        <p>注册时间：{{.RegisterTime}}</p>
    `,
}
client.RegisterTemplate(template)

// 使用模板发送邮件
templateData := map[string]interface{}{
    "Name":         "张三",
    "Username":     "zhangsan",
    "RegisterTime": time.Now().Format("2006-01-02 15:04:05"),
}

msg := &email.EmailMessage{
    To: []string{"<EMAIL>"},
}

err := client.SendTemplate(ctx, "welcome", templateData, msg)
```

### 简单接口（兼容旧版本）

```go
// 创建简单发送器
sender, err := email.NewSimpleEmailSender(config)
if err != nil {
    log.Fatal(err)
}
defer sender.Close()

// 发送纯文本邮件
err = sender.SendMessage(
    []string{"<EMAIL>"},
    "主题",
    "邮件内容",
    []string{"/path/to/attachment.pdf"},
)

// 发送HTML邮件
err = sender.SendHTMLMessage(
    []string{"<EMAIL>"},
    "主题",
    "<h1>HTML内容</h1>",
    nil,
)
```

### 快速发送（测试用）

```go
// 快速发送纯文本邮件
err := email.QuickSend(
    "smtp.gmail.com", 587,
    "<EMAIL>", "your-password",
    []string{"<EMAIL>"},
    "测试邮件", "这是测试内容",
)

// 快速发送HTML邮件
err := email.QuickSendHTML(
    "smtp.gmail.com", 587,
    "<EMAIL>", "your-password",
    []string{"<EMAIL>"},
    "测试邮件", "<h1>HTML测试内容</h1>",
)
```

## 配置选项

| 参数 | 类型 | 说明 | 默认值 |
|-----|------|------|--------|
| ServerHost | string | SMTP服务器地址 | - |
| ServerPort | int | SMTP服务器端口 | - |
| Username | string | 用户名/邮箱地址 | - |
| Password | string | 密码/应用密码 | - |
| FromName | string | 发件人显示名称 | "系统管理员" |
| UseSSL | bool | 是否使用SSL | false |
| UseTLS | bool | 是否使用TLS | true |
| MaxRetries | int | 最大重试次数 | 3 |
| RetryDelay | time.Duration | 重试间隔 | 5s |
| PoolSize | int | 连接池大小 | 10 |
| MaxFileSize | int64 | 附件最大大小(字节) | 25MB |

## 支持的邮件服务商

- Gmail (gmail)
- Outlook (outlook) 
- Yahoo (yahoo)
- QQ邮箱 (qq)
- 163邮箱 (163)
- 126邮箱 (126)

## 环境变量

| 变量名 | 说明 |
|--------|------|
| EMAIL_SERVER_HOST | SMTP服务器地址 |
| EMAIL_SERVER_PORT | SMTP服务器端口 |
| EMAIL_USERNAME | 用户名 |
| EMAIL_PASSWORD | 密码 |
| EMAIL_FROM_NAME | 发件人名称 |
| EMAIL_USE_SSL | 是否使用SSL |
| EMAIL_USE_TLS | 是否使用TLS |
| EMAIL_MAX_RETRIES | 最大重试次数 |
| EMAIL_RETRY_DELAY_SECONDS | 重试间隔(秒) |
| EMAIL_POOL_SIZE | 连接池大小 |
| EMAIL_MAX_FILE_SIZE_MB | 附件最大大小(MB) |

## 最佳实践

1. **生产环境**：使用环境变量配置敏感信息
2. **连接池**：根据发送量调整连接池大小
3. **错误处理**：合理处理发送失败的情况
4. **资源管理**：记得调用 `Close()` 方法释放资源
5. **模板缓存**：预先注册常用模板以提高性能
6. **日志监控**：关注邮件发送日志，及时发现问题

## 许可证

MIT License
