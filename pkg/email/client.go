package email

import (
	"bytes"
	"context"
	"crypto/tls"
	"html/template"
	"log"
	"os"
	"sync"
	"time"

	"github.com/pkg/errors"
	"gopkg.in/gomail.v2"
)

// EmailConfig 结构体用于存储邮件服务器相关配置信息
type EmailConfig struct {
	ServerHost  string        `json:"serverHost" validate:"required"`
	ServerPort  int           `json:"serverPort" validate:"required,min=1,max=65535"`
	Username    string        `json:"username" validate:"required,email"`
	Password    string        `json:"password" validate:"required"`
	FromName    string        `json:"fromName"`    // 发件人显示名称
	UseSSL      bool          `json:"useSSL"`      // 是否使用SSL
	UseTLS      bool          `json:"useTLS"`      // 是否使用TLS
	MaxRetries  int           `json:"maxRetries"`  // 最大重试次数
	RetryDelay  time.Duration `json:"retryDelay"`  // 重试间隔
	PoolSize    int           `json:"poolSize"`    // 连接池大小
	MaxFileSize int64         `json:"maxFileSize"` // 附件最大文件大小(字节)
}

// EmailMessage 邮件消息结构体
type EmailMessage struct {
	To          []string          `json:"to" validate:"required,dive,email"`
	CC          []string          `json:"cc,omitempty" validate:"dive,email"`
	BCC         []string          `json:"bcc,omitempty" validate:"dive,email"`
	Subject     string            `json:"subject" validate:"required"`
	TextBody    string            `json:"textBody,omitempty"`
	HTMLBody    string            `json:"htmlBody,omitempty"`
	Attachments []string          `json:"attachments,omitempty"`
	Priority    Priority          `json:"priority,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`
	IsHTML      bool              `json:"isHTML"`
}

// Priority 邮件优先级
type Priority int

const (
	PriorityLow Priority = iota
	PriorityNormal
	PriorityHigh
	PriorityUrgent
)

// EmailTemplate 邮件模板结构体
type EmailTemplate struct {
	Name     string
	Subject  string
	HTMLBody string
	TextBody string
}

// EmailSender 邮件发送器接口
type EmailSender interface {
	SendEmail(ctx context.Context, msg *EmailMessage) error
	SendTemplate(ctx context.Context, templateName string, data interface{}, msg *EmailMessage) error
	Close() error
}

// EmailClient 邮件客户端
type EmailClient struct {
	config    *EmailConfig
	dialer    *gomail.Dialer
	pool      chan gomail.SendCloser
	templates map[string]*EmailTemplate
	mu        sync.RWMutex
	logger    *log.Logger
}

// NewEmailClient 创建邮件客户端实例
func NewEmailClient(config *EmailConfig) (*EmailClient, error) {
	if err := validateEmailConfig(config); err != nil {
		return nil, errors.Wrap(err, "invalid email config")
	}

	// 设置默认值
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.RetryDelay == 0 {
		config.RetryDelay = time.Second * 5
	}
	if config.PoolSize == 0 {
		config.PoolSize = 10
	}
	if config.MaxFileSize == 0 {
		config.MaxFileSize = 25 * 1024 * 1024 // 25MB
	}

	dialer := gomail.NewDialer(config.ServerHost, config.ServerPort, config.Username, config.Password)

	// 配置TLS
	if config.UseSSL {
		dialer.SSL = true
	}
	if config.UseTLS {
		dialer.TLSConfig = &tls.Config{
			ServerName:         config.ServerHost,
			InsecureSkipVerify: false,
		}
	}

	client := &EmailClient{
		config:    config,
		dialer:    dialer,
		pool:      make(chan gomail.SendCloser, config.PoolSize),
		templates: make(map[string]*EmailTemplate),
		logger:    log.New(os.Stdout, "[EMAIL] ", log.LstdFlags),
	}

	// 初始化连接池
	for i := 0; i < config.PoolSize; i++ {
		sender, err := dialer.Dial()
		if err != nil {
			client.logger.Printf("Failed to create connection %d: %v", i, err)
			continue
		}
		client.pool <- sender
	}

	return client, nil
}

// validateEmailConfig 验证邮件配置
func validateEmailConfig(config *EmailConfig) error {
	if config == nil {
		return errors.New("config cannot be nil")
	}
	if config.ServerHost == "" {
		return errors.New("server host is required")
	}
	if config.ServerPort <= 0 || config.ServerPort > 65535 {
		return errors.New("invalid server port")
	}
	if config.Username == "" {
		return errors.New("username is required")
	}
	if config.Password == "" {
		return errors.New("password is required")
	}
	return nil
}

// getSender 从连接池获取发送器
func (c *EmailClient) getSender() (gomail.SendCloser, error) {
	select {
	case sender := <-c.pool:
		return sender, nil
	case <-time.After(time.Second * 30):
		// 如果连接池为空，创建新连接
		return c.dialer.Dial()
	}
}

// returnSender 将发送器归还到连接池
func (c *EmailClient) returnSender(sender gomail.SendCloser) {
	select {
	case c.pool <- sender:
	default:
		// 连接池已满，关闭连接
		if sender != nil {
			sender.Close()
		}
	}
}

// SendEmail 发送邮件
func (c *EmailClient) SendEmail(ctx context.Context, msg *EmailMessage) error {
	if err := c.validateMessage(msg); err != nil {
		return errors.Wrap(err, "invalid message")
	}

	var lastErr error
	for attempt := 0; attempt <= c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			c.logger.Printf("Retrying email send, attempt %d", attempt)
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(c.config.RetryDelay):
			}
		}

		if err := c.sendEmailAttempt(ctx, msg); err != nil {
			lastErr = err
			c.logger.Printf("Email send attempt %d failed: %v", attempt+1, err)
			continue
		}

		c.logger.Printf("Email sent successfully to %v", msg.To)
		return nil
	}

	return errors.Wrapf(lastErr, "failed to send email after %d attempts", c.config.MaxRetries+1)
}

// sendEmailAttempt 单次发送尝试
func (c *EmailClient) sendEmailAttempt(ctx context.Context, msg *EmailMessage) error {
	sender, err := c.getSender()
	if err != nil {
		return errors.Wrap(err, "failed to get sender")
	}
	defer c.returnSender(sender)

	m := c.buildMessage(msg)

	// 检查附件大小
	if err := c.validateAttachments(msg.Attachments); err != nil {
		return errors.Wrap(err, "attachment validation failed")
	}

	// 构建收件人列表（包括To, CC, BCC）
	allRecipients := make([]string, 0, len(msg.To)+len(msg.CC)+len(msg.BCC))
	allRecipients = append(allRecipients, msg.To...)
	allRecipients = append(allRecipients, msg.CC...)
	allRecipients = append(allRecipients, msg.BCC...)

	// 发送邮件
	return sender.Send(c.config.Username, allRecipients, m)
}

// buildMessage 构建邮件消息
func (c *EmailClient) buildMessage(msg *EmailMessage) *gomail.Message {
	m := gomail.NewMessage()

	// 设置发件人
	if c.config.FromName != "" {
		m.SetAddressHeader("From", c.config.Username, c.config.FromName)
	} else {
		m.SetHeader("From", c.config.Username)
	}

	// 设置收件人
	m.SetHeader("To", msg.To...)
	if len(msg.CC) > 0 {
		m.SetHeader("Cc", msg.CC...)
	}
	if len(msg.BCC) > 0 {
		m.SetHeader("Bcc", msg.BCC...)
	}

	// 设置主题
	m.SetHeader("Subject", msg.Subject)

	// 设置优先级
	switch msg.Priority {
	case PriorityHigh:
		m.SetHeader("X-Priority", "2")
		m.SetHeader("X-MSMail-Priority", "High")
	case PriorityUrgent:
		m.SetHeader("X-Priority", "1")
		m.SetHeader("X-MSMail-Priority", "High")
	case PriorityLow:
		m.SetHeader("X-Priority", "4")
		m.SetHeader("X-MSMail-Priority", "Low")
	default:
		m.SetHeader("X-Priority", "3")
		m.SetHeader("X-MSMail-Priority", "Normal")
	}

	// 设置自定义头部
	for key, value := range msg.Headers {
		m.SetHeader(key, value)
	}

	// 设置邮件正文
	if msg.IsHTML && msg.HTMLBody != "" {
		m.SetBody("text/html", msg.HTMLBody)
		if msg.TextBody != "" {
			m.AddAlternative("text/plain", msg.TextBody)
		}
	} else if msg.TextBody != "" {
		m.SetBody("text/plain", msg.TextBody)
	}

	// 添加附件
	for _, attachment := range msg.Attachments {
		m.Attach(attachment)
	}

	return m
}

// validateMessage 验证邮件消息
func (c *EmailClient) validateMessage(msg *EmailMessage) error {
	if msg == nil {
		return errors.New("message cannot be nil")
	}
	if len(msg.To) == 0 {
		return errors.New("at least one recipient is required")
	}
	if msg.Subject == "" {
		return errors.New("subject is required")
	}
	if msg.TextBody == "" && msg.HTMLBody == "" {
		return errors.New("message body is required")
	}
	return nil
}

// validateAttachments 验证附件
func (c *EmailClient) validateAttachments(attachments []string) error {
	for _, attachment := range attachments {
		info, err := os.Stat(attachment)
		if err != nil {
			return errors.Wrapf(err, "attachment file not found: %s", attachment)
		}
		if info.Size() > c.config.MaxFileSize {
			return errors.Errorf("attachment %s exceeds maximum file size limit", attachment)
		}
	}
	return nil
}

// RegisterTemplate 注册邮件模板
func (c *EmailClient) RegisterTemplate(template *EmailTemplate) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.templates[template.Name] = template
}

// SendTemplate 使用模板发送邮件
func (c *EmailClient) SendTemplate(ctx context.Context, templateName string, data interface{}, msg *EmailMessage) error {
	c.mu.RLock()
	tmpl, exists := c.templates[templateName]
	c.mu.RUnlock()

	if !exists {
		return errors.Errorf("template %s not found", templateName)
	}

	// 渲染模板
	renderedMsg, err := c.renderTemplate(tmpl, data)
	if err != nil {
		return errors.Wrap(err, "failed to render template")
	}

	// 合并消息内容
	if msg.Subject == "" {
		msg.Subject = renderedMsg.Subject
	}
	if msg.HTMLBody == "" {
		msg.HTMLBody = renderedMsg.HTMLBody
	}
	if msg.TextBody == "" {
		msg.TextBody = renderedMsg.TextBody
	}
	msg.IsHTML = renderedMsg.HTMLBody != ""

	return c.SendEmail(ctx, msg)
}

// renderTemplate 渲染模板
func (c *EmailClient) renderTemplate(tmpl *EmailTemplate, data interface{}) (*EmailMessage, error) {
	msg := &EmailMessage{}

	// 渲染主题
	if tmpl.Subject != "" {
		subjectTmpl, err := template.New("subject").Parse(tmpl.Subject)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse subject template")
		}
		var buf bytes.Buffer
		if err := subjectTmpl.Execute(&buf, data); err != nil {
			return nil, errors.Wrap(err, "failed to execute subject template")
		}
		msg.Subject = buf.String()
	}

	// 渲染HTML正文
	if tmpl.HTMLBody != "" {
		htmlTmpl, err := template.New("html").Parse(tmpl.HTMLBody)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse HTML template")
		}
		var buf bytes.Buffer
		if err := htmlTmpl.Execute(&buf, data); err != nil {
			return nil, errors.Wrap(err, "failed to execute HTML template")
		}
		msg.HTMLBody = buf.String()
	}

	// 渲染文本正文
	if tmpl.TextBody != "" {
		textTmpl, err := template.New("text").Parse(tmpl.TextBody)
		if err != nil {
			return nil, errors.Wrap(err, "failed to parse text template")
		}
		var buf bytes.Buffer
		if err := textTmpl.Execute(&buf, data); err != nil {
			return nil, errors.Wrap(err, "failed to execute text template")
		}
		msg.TextBody = buf.String()
	}

	return msg, nil
}

// Close 关闭邮件客户端
func (c *EmailClient) Close() error {
	close(c.pool)
	for sender := range c.pool {
		if sender != nil {
			sender.Close()
		}
	}
	return nil
}
