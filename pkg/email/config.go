package email

import (
	"os"
	"strconv"
	"time"
)

// LoadEmailConfigFromEnv 从环境变量加载邮件配置
func LoadEmailConfigFromEnv() *EmailConfig {
	config := &EmailConfig{
		ServerHost:  getEnv("EMAIL_SERVER_HOST", "smtp.gmail.com"),
		ServerPort:  getEnvAsInt("EMAIL_SERVER_PORT", 587),
		Username:    getEnv("EMAIL_USERNAME", ""),
		Password:    getEnv("EMAIL_PASSWORD", ""),
		FromName:    getEnv("EMAIL_FROM_NAME", "系统管理员"),
		UseSSL:      getEnvAsBool("EMAIL_USE_SSL", false),
		UseTLS:      getEnvAsBool("EMAIL_USE_TLS", true),
		MaxRetries:  getEnvAsInt("EMAIL_MAX_RETRIES", 3),
		RetryDelay:  time.Duration(getEnvAsInt("EMAIL_RETRY_DELAY_SECONDS", 5)) * time.Second,
		PoolSize:    getEnvAsInt("EMAIL_POOL_SIZE", 10),
		MaxFileSize: int64(getEnvAsInt("EMAIL_MAX_FILE_SIZE_MB", 25)) * 1024 * 1024,
	}
	return config
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool 获取环境变量并转换为布尔值
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// DefaultEmailConfig 返回默认的邮件配置
func DefaultEmailConfig() *EmailConfig {
	return &EmailConfig{
		ServerHost:  "smtp.gmail.com",
		ServerPort:  587,
		UseTLS:      true,
		UseSSL:      false,
		MaxRetries:  3,
		RetryDelay:  time.Second * 5,
		PoolSize:    10,
		MaxFileSize: 25 * 1024 * 1024, // 25MB
		FromName:    "系统管理员",
	}
}

// CommonEmailConfigs 预设的常用邮件服务器配置
var CommonEmailConfigs = map[string]*EmailConfig{
	"gmail": {
		ServerHost: "smtp.gmail.com",
		ServerPort: 587,
		UseTLS:     true,
		UseSSL:     false,
	},
	"outlook": {
		ServerHost: "smtp-mail.outlook.com",
		ServerPort: 587,
		UseTLS:     true,
		UseSSL:     false,
	},
	"yahoo": {
		ServerHost: "smtp.mail.yahoo.com",
		ServerPort: 587,
		UseTLS:     true,
		UseSSL:     false,
	},
	"qq": {
		ServerHost: "smtp.qq.com",
		ServerPort: 587,
		UseTLS:     true,
		UseSSL:     false,
	},
	"163": {
		ServerHost: "smtp.163.com",
		ServerPort: 25,
		UseTLS:     false,
		UseSSL:     false,
	},
	"126": {
		ServerHost: "smtp.126.com",
		ServerPort: 25,
		UseTLS:     false,
		UseSSL:     false,
	},
}

// NewConfigForProvider 根据服务提供商创建配置
func NewConfigForProvider(provider, username, password string) *EmailConfig {
	baseConfig, exists := CommonEmailConfigs[provider]
	if !exists {
		return DefaultEmailConfig()
	}

	config := *baseConfig
	config.Username = username
	config.Password = password
	config.MaxRetries = 3
	config.RetryDelay = time.Second * 5
	config.PoolSize = 10
	config.MaxFileSize = 25 * 1024 * 1024
	config.FromName = "系统管理员"

	return &config
}
