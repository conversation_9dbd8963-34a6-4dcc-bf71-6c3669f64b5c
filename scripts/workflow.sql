DROP TABLE IF EXISTS `model_detail`;
CREATE TABLE `model_detail` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `model_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模板id',
    `model_title` varchar(256) NOT NULL DEFAULT '' COMMENT '模板标题',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `form_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '表单定义id',
    `model_group_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模版组id',
    `icon_url` varchar(256) NOT NULL DEFAULT '' COMMENT 'icon图标地址',
    `status` ENUM('DRAFT', 'PUBLISHED', 'DISABLED') NOT NULL DEFAULT 'DRAFT' COMMENT '模板状态【DRAFT：草稿；PUBLISHED：发布；DISABLED：停用】默认草稿',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '描述',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `model_detail_id_index` (`model_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板详情表';

DROP TABLE IF EXISTS `model_ext_prop`;
CREATE TABLE `model_ext_prop` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `model_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模板id',
    `notice_url` varchar(256) NOT NULL DEFAULT '' COMMENT '回调通知推送url',
    `title_props` JSON COMMENT '标题配置',
    `ext_config` JSON COMMENT '扩展配置JSON',
    PRIMARY KEY (`id`),
    UNIQUE KEY `model_id_index` (`model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板详情扩展属性表';

DROP TABLE IF EXISTS `model_version`;
CREATE TABLE `model_version` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `model_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模板id',
    `model_title` varchar(256) NOT NULL DEFAULT '' COMMENT '模板版本标题',
    `version_id` varchar(32) NOT NULL DEFAULT '' COMMENT '版本id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `form_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '表单定义id',
    `use_status` ENUM('USING', 'NOT_USING') NOT NULL DEFAULT 'USING' COMMENT '使用状态【USING：使用；NOT_USING：未使用】',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '描述',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
    `notice_url` varchar(256) NOT NULL DEFAULT '' COMMENT '回调通知推送url',
    `title_props` JSON COMMENT '标题配置',
    PRIMARY KEY (`id`),
    UNIQUE KEY `model_and_version_unique` (`model_id`,`version_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板版本表';

DROP TABLE IF EXISTS `model_auth`;
CREATE TABLE `model_auth` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `model_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模板id',
    `auth_obj_type` ENUM('USER', 'DEPT_WITH_SUB', 'DEPT_WITHOUT_SUB', 'ROLE_WITH_SUB', 'ROLE_WITHOUT_SUB') NOT NULL DEFAULT 'USER' COMMENT '授权对象类型【USER：人员；DEPT_WITH_SUB：部门（包含子部门）；DEPT_WITHOUT_SUB：部门（不含子部门）；ROLE_WITH_SUB：角色（包含子角色）；ROLE_WITHOUT_SUB：角色（不包含子角色）】',
    `obj_id` varchar(32) NOT NULL COMMENT '授权对象id;根据授权对象类型取值',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `model_id_index` (`model_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板授权表';

DROP TABLE IF EXISTS `process_def_info`;
CREATE TABLE `process_def_info` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `process_def_name` varchar(128) NOT NULL DEFAULT '' COMMENT '流程定义名称',
    `status` ENUM('DRAFT', 'PUBLISHED', 'DISABLED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态【DRAFT：草稿；PUBLISHED：发布可用；DISABLED：停用】',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '描述',
    `struct_data` JSON COMMENT '流程定义数据',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `process_def_id_index` (`process_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程定义表';

DROP TABLE IF EXISTS `process_def_node`;
CREATE TABLE `process_def_node` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `node_model` ENUM('START', 'APPROVE', 'HANDLE', 'CC', 'CUSTOM', 'CONDITION', 'BRANCH', 'CONVERGE', 'END') NOT NULL DEFAULT 'START' COMMENT '节点模型【START：开始节点；APPROVE：审批节点；HANDLE：办理节点；CC：抄送节点；CUSTOM：自定义节点；CONDITION：条件节点；BRANCH：分支节点；CONVERGE：汇聚节点；END：结束节点】',
    `node_name` varchar(128) NOT NULL DEFAULT '' COMMENT '节点名称',
    `parent_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点父ID',
    `approve_type` ENUM('MANUAL', 'AUTO_PASS', 'AUTO_REJECT') NOT NULL DEFAULT 'MANUAL' COMMENT '审批类型【MANUAL：人工审批；AUTO_PASS：自动通过；AUTO_REJECT：自动拒绝】默认人工审批',
    `none_handler` ENUM('AUTO_PASS', 'TO_ADMIN', 'APPOINT') NOT NULL DEFAULT 'AUTO_PASS' COMMENT '审批人为空时【AUTO_PASS：自动通过；TO_ADMIN：自动转交管理员；APPOINT：指定审批人】默认自动通过',
    `appoint_handler` varchar(128) NOT NULL DEFAULT '' COMMENT '审批人为空时指定审批人ID',
    `handle_mode` ENUM('SEQUENTIAL', 'COUNTERSIGN', 'EITHER') NOT NULL DEFAULT 'COUNTERSIGN' COMMENT '审批方式【SEQUENTIAL：依次审批；COUNTERSIGN：会签（需要完成人数的审批人同意或拒绝才可完成节点）；EITHER：或签（其中一名审批人同意或拒绝即可）】默认会签',
    `finish_mode` int NOT NULL DEFAULT 0 COMMENT '完成人数：依次审批默认0所有人不可选人，会签默认0所有人（可选人大于0），或签默认1一个人（可选人大于0）',
    `branch_mode` ENUM('SINGLE', 'MULTI') NOT NULL DEFAULT 'MULTI' COMMENT '分支执行方式【SINGLE：单分支；MULTI：多分支】默认多分支',
    `default_branch` int NOT NULL DEFAULT 0 COMMENT '单分支处理需要默认分支，在条件优先级无法处理时候执行默认分支，取值分支下标',
    `branch_level` int NOT NULL DEFAULT 0 COMMENT '优先级，分支执行方式为多分支处理方式无优先级应为0',
    `condition_group` JSON COMMENT '条件组前端描述展示条件组',
    `condition_expr` varchar(4000) NOT NULL DEFAULT '' COMMENT '条件组解析后的表达式',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '节点描述',
    `pre_nodes`  JSON NOT NULL DEFAULT '' COMMENT '上节点ID集合,多个用逗号隔开',
    `next_nodes` JSON NOT NULL DEFAULT '' COMMENT '下节点ID集合,多个用逗号隔开',
    `last_nodes` JSON NOT NULL DEFAULT '' COMMENT '尾节点ID集合,多个用逗号隔开',
    `node_index` int NOT NULL DEFAULT 0 COMMENT '节点下标',
    `branch_index` int NOT NULL DEFAULT 0 COMMENT '分支节点下标',
    `timeout_config` JSON COMMENT '节点超时配置',
    PRIMARY KEY (`id`),
    KEY `process_def_id_index` (`process_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程定义节点信息表';

DROP TABLE IF EXISTS `process_def_node_formper`;
CREATE TABLE `process_def_node_formper` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `elem_id` varchar(128) NOT NULL DEFAULT '' COMMENT '表单元素ID',
    `elem_pid` varchar(128) NOT NULL DEFAULT '' COMMENT '表单元素父ID',
    `permission` ENUM('EDITABLE', 'READONLY', 'HIDDEN', 'REQUIRED') NOT NULL DEFAULT 'READONLY' COMMENT '表单权限【EDITABLE：可编辑；READONLY：只读；HIDDEN：隐藏；REQUIRED：必填】',
    PRIMARY KEY (`id`),
    KEY `process_def_id_index` (`process_def_id`,`node_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程定义节点表单权限表';

DROP TABLE IF EXISTS `process_def_node_user`;
CREATE TABLE `process_def_node_user` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `type` ENUM('SPECIFIED', 'INITIATOR', 'INITIATOR_SELECT', 'ROLE', 'DEPARTMENT', 'DIRECT_SUPERVISOR', 'DEPT_SUPERVISOR', 'MULTI_LEVEL_SUPERVISOR', 'FORM_DEPT_SUPERVISOR', 'FORM_USER', 'FORM_DEPT', 'FORM_ROLE') NOT NULL DEFAULT 'SPECIFIED' COMMENT '处理人类型【常用审批人：SPECIFIED-指定成员, INITIATOR-发起人自己, INITIATOR_SELECT-发起人自选, ROLE-角色, DEPARTMENT-部门; 主管：DIRECT_SUPERVISOR-直属主管, DEPT_SUPERVISOR-部门主管, MULTI_LEVEL_SUPERVISOR-连续多级主管, FORM_DEPT_SUPERVISOR-部门控件对应主管; 其他：FORM_USER-表单人员控件, FORM_DEPT-部门控件, FORM_ROLE-角色控件】',
    `strategy` ENUM('NORMAL', 'SUPERVISOR', 'OTHER') NOT NULL DEFAULT 'NORMAL' COMMENT '处理人策略【NORMAL：常用审批人；SUPERVISOR：主管（相对岗位）；OTHER：其他】',
    `node_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '处理人名称',
    `node_user_id` varchar(128) NOT NULL DEFAULT '' COMMENT '处理人id',
    `sort` int NOT NULL DEFAULT 1 COMMENT '处理人顺序;正序排序',
    `obj` JSON COMMENT '扩展字段',
    `relative` JSON COMMENT '相对发起人的直属主管信息',
    PRIMARY KEY (`id`),
    KEY `process_def_id_index` (`process_def_id`,`node_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程定义节点用户表';

DROP TABLE IF EXISTS `form_def_info`;
CREATE TABLE `form_def_info` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `form_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '表单模板id;唯一id',
    `form_def_name` varchar(128) NOT NULL DEFAULT '' COMMENT '表单名称',
    `html_content` JSON COMMENT '表单定义结构化数据',
    `html_page_url` varchar(256) NOT NULL DEFAULT '' COMMENT 'html页面访问路径;html页面文件访问路径',
    `status` ENUM('DRAFT', 'PUBLISHED', 'DISABLED') NOT NULL DEFAULT 'DRAFT' COMMENT '模板状态【DRAFT：草稿；PUBLISHED：发布；DISABLED：停用】',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '表单描述',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `form_def_id_index` (`form_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单定义信息表';

DROP TABLE IF EXISTS `form_def_element`;
CREATE TABLE `form_def_element` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `form_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '表单模板id;唯一id',
    `ele_name` varchar(128) NOT NULL DEFAULT '' COMMENT '元素名称',
    `ele_id` varchar(128) NOT NULL DEFAULT '' COMMENT '元素标识id;表单内元素唯一id，可通过该id关联表单内元素',
    `label_type` varchar(16) NOT NULL DEFAULT 'input' COMMENT '元素标签类型;html标签：input、select等',
    `ele_type` ENUM('TEXT', 'CHECKBOX', 'SELECT', 'RADIO') NOT NULL DEFAULT 'TEXT' COMMENT '元素类型【TEXT：文本；CHECKBOX：复选：SELECT：下拉；RADIO：单选】',
    `ele_default` varchar(1024) NOT NULL DEFAULT '' COMMENT '元素默认值',
    `ele_config` JSON COMMENT '元素配置JSON',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '元素描述',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user` varchar(50) DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `form_def_id_index` (`form_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单定义元素表';

DROP TABLE IF EXISTS `inst_task_detail`;
CREATE TABLE `inst_task_detail` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `model_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模板id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `form_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '表单定义id',
    `version_id` varchar(32) NOT NULL DEFAULT '' COMMENT '版本id',
    `task_name` varchar(512) NOT NULL DEFAULT '' COMMENT '实例任务名称',
    `status` ENUM('DRAFT', 'RUNNING', 'TERMINATED', 'COMPLETED', 'SUSPENDED', 'REJECTED') NOT NULL DEFAULT 'DRAFT' COMMENT '任务状态【DRAFT：创建中(草稿)；RUNNING：进行中；TERMINATED：终止；COMPLETED：完成；SUSPENDED：挂起；REJECTED：回退】',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '描述',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_user_id` varchar(32) DEFAULT '' COMMENT '更新人id',
    `update_user_name` varchar(128) DEFAULT '' COMMENT '更新人名称',
    `start_time` timestamp NULL DEFAULT NULL COMMENT '发起时间',
    `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `inst_user_task_unique` (`inst_task_id`),
    KEY `inst_task_id_index` (`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='执行实例任务信息表';

DROP TABLE IF EXISTS `inst_node_task`;
CREATE TABLE `inst_node_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `parent_id` varchar(32) NOT NULL DEFAULT '' COMMENT '父节点id',
    `node_model` ENUM('START', 'APPROVE', 'HANDLE', 'CC', 'CUSTOM', 'CONDITION', 'BRANCH', 'CONVERGE', 'END') NOT NULL DEFAULT 'START' COMMENT '节点模型【START：开始节点；APPROVE：审批节点；HANDLE：办理节点；CC：抄送节点；CUSTOM：自定义节点；CONDITION：条件节点；BRANCH：分支节点；CONVERGE：汇聚节点；END：结束节点】',
    `node_name` varchar(128) NOT NULL DEFAULT '' COMMENT '节点名称',
    `approve_type` ENUM('MANUAL', 'AUTO_PASS', 'AUTO_REJECT') NOT NULL DEFAULT 'MANUAL' COMMENT '审批类型【MANUAL：人工审批；AUTO_PASS：自动通过；AUTO_REJECT：自动拒绝】默认人工审批',
    `none_handler` ENUM('AUTO_PASS', 'TO_ADMIN', 'APPOINT') NOT NULL DEFAULT 'AUTO_PASS' COMMENT '审批人为空时【AUTO_PASS：自动通过；TO_ADMIN：自动转交管理员；APPOINT：指定审批人】默认自动通过',
    `appoint_handler` varchar(128) NOT NULL DEFAULT '' COMMENT '审批人为空时指定审批人ID',
    `handle_mode` ENUM('SEQUENTIAL', 'COUNTERSIGN', 'EITHER') NOT NULL DEFAULT 'COUNTERSIGN' COMMENT '审批方式【SEQUENTIAL：依次审批；COUNTERSIGN：会签（需要完成人数的审批人同意或拒绝才可完成节点）；EITHER：或签（其中一名审批人同意或拒绝即可）】默认会签',
    `finish_mode` int NOT NULL DEFAULT 0 COMMENT '完成人数：依次审批默认0所有人不可选人，会签默认0所有人（可选人大于0），或签默认1一个人（可选人大于0）',
    `branch_mode` ENUM('SINGLE', 'MULTI') NOT NULL DEFAULT 'MULTI' COMMENT '分支执行方式【SINGLE：单分支；MULTI：多分支】默认多分支',
    `default_branch` int NOT NULL DEFAULT 0 COMMENT '单分支处理需要默认分支，在条件优先级无法处理时候执行默认分支，取值分支下标',
    `branch_level` int NOT NULL DEFAULT 0 COMMENT '优先级，分支执行方式为多分支处理方式无优先级应为0',
    `condition_group` JSON COMMENT '条件组前端描述展示条件组',
    `condition_expr` varchar(4000) NOT NULL DEFAULT '' COMMENT '条件组解析后的表达式',
    `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '节点描述',
    `status` ENUM('NOT_STARTED', 'PROCESSING', 'COMPLETED', 'REJECTED', 'TERMINATED', 'NOT_PASSED') NOT NULL DEFAULT 'NOT_STARTED' COMMENT '任务状态【NOT_STARTED：未开始；PROCESSING：处理中；COMPLETED：完成；REJECTED：回退；TERMINATED：终止；NOT_PASSED：不通过】',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `start_time` timestamp NULL DEFAULT NULL COMMENT '节点开始时间',
    `end_time` timestamp NULL DEFAULT NULL COMMENT '节点结束时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `inst_user_task_unique` (`inst_task_id`,`node_task_id`),
    KEY `inst_node_task_id_index` (`inst_task_id`,`node_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例节点任务表';

DROP TABLE IF EXISTS `inst_node_task_formper`;
CREATE TABLE `inst_node_task_formper` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `elem_id` varchar(128) NOT NULL DEFAULT '' COMMENT '表单元素ID',
    `elem_pid` varchar(128) NOT NULL DEFAULT '' COMMENT '表单元素父ID',
    `permission` ENUM('EDITABLE', 'READONLY', 'HIDDEN', 'REQUIRED') NOT NULL DEFAULT 'READONLY' COMMENT '表单权限【EDITABLE：可编辑；READONLY：只读；HIDDEN：隐藏；REQUIRED：必填】默认只读',
    PRIMARY KEY (`id`),
    KEY `inst_node_task_id_index` (`node_task_id`,`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例节点任务表单权限表';

DROP TABLE IF EXISTS `inst_user_task`;
CREATE TABLE `inst_user_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `user_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '处理人任务id',
    `type` ENUM('SPECIFIED', 'INITIATOR', 'INITIATOR_SELECT', 'ROLE', 'DEPARTMENT', 'DIRECT_SUPERVISOR', 'DEPT_SUPERVISOR', 'MULTI_LEVEL_SUPERVISOR', 'FORM_DEPT_SUPERVISOR', 'FORM_USER', 'FORM_DEPT', 'FORM_ROLE') NOT NULL DEFAULT 'SPECIFIED' COMMENT '处理人类型【常用审批人：SPECIFIED-指定成员, INITIATOR-发起人自己, INITIATOR_SELECT-发起人自选, ROLE-角色, DEPARTMENT-部门; 主管：DIRECT_SUPERVISOR-直属主管, DEPT_SUPERVISOR-部门主管, MULTI_LEVEL_SUPERVISOR-连续多级主管, FORM_DEPT_SUPERVISOR-部门控件对应主管; 其他：FORM_USER-表单人员控件, FORM_DEPT-部门控件, FORM_ROLE-角色控件】',
    `strategy` ENUM('NORMAL', 'SUPERVISOR', 'OTHER') NOT NULL DEFAULT 'NORMAL' COMMENT '处理人策略【NORMAL：常用审批人；SUPERVISOR：主管（相对岗位）；OTHER：其他】',
    `node_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '处理人名称',
    `node_user_id` varchar(128) NOT NULL DEFAULT '' COMMENT '处理人id',
    `sort` int NOT NULL DEFAULT 1 COMMENT '处理人顺序;正序排序',
    `obj` JSON COMMENT '扩展字段',
    `relative` JSON COMMENT '相对发起人的直属主管信息',
    `status` ENUM('PROCESSING', 'COMPLETED', 'REJECTED', 'RETURNED', 'TERMINATED') NOT NULL DEFAULT 'PROCESSING' COMMENT '实例用户任务状态【PROCESSING：处理中；COMPLETED：完成（同意）；REJECTED：不通过（不同意）；RETURNED：回退；TERMINATED：终止】',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `handle_time` timestamp NULL DEFAULT NULL COMMENT '处理时间',
    `op_user_id` bigint NOT NULL DEFAULT '' COMMENT '操作用户id',
    `op_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '操作用户名称',
    `opinion` ENUM('NOT_STATED', 'AGREE', 'DISAGREE', 'OTHER') NOT NULL DEFAULT 'NOT_STATED' COMMENT '任务处理意见【NOT_STATED：未发表；AGREE：同意；DISAGREE：不同意；OTHER：其他】',
    `opinion_desc` varchar(3000) NOT NULL DEFAULT '' COMMENT '处理意见描述',
    `is_delegated` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被委托的任务',
    `delegated_from_user_id` bigint DEFAULT NULL COMMENT '委托来源用户ID',
    `is_transferred` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被转办的任务',
    `transferred_from_user_id` bigint DEFAULT NULL COMMENT '转办来源用户ID',
    `is_added_sign` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否加签任务',
    `add_sign_from_user_id` bigint DEFAULT NULL COMMENT '加签来源用户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `inst_user_task_unique` (`inst_task_id`,`user_task_id`),
    KEY `inst_user_task_index` (`node_task_id`,`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例用户任务表';

DROP TABLE IF EXISTS `inst_user_task_opinion`;
CREATE TABLE `inst_user_task_opinion` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `user_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户任务id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点id',
    `opinion_id` varchar(32) NOT NULL DEFAULT '' COMMENT '意见id',
    `opinion` ENUM('NOT_PROCESSED', 'AGREE', 'DISAGREE', 'RETURN', 'TERMINATE', 'REVOKE') NOT NULL DEFAULT 'NOT_PROCESSED' COMMENT '处理意见【NOT_PROCESSED：未处理；AGREE：同意；DISAGREE：不同意；RETURN：回退；TERMINATE：终止；REVOKE：撤回】',
    `opinion_desc` varchar(3000) NOT NULL DEFAULT '' COMMENT '处理意见描述',
    `op_user_id` bigint NOT NULL DEFAULT '' COMMENT '操作用户id',
    `op_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '操作用户名称',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `opinion_time` timestamp NULL DEFAULT NULL COMMENT '发表意见时间',
    PRIMARY KEY (`id`),
    KEY `inst_user_task_id_index` (`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例用户任务意见表';

DROP TABLE IF EXISTS `inst_task_param`;
CREATE TABLE `inst_task_param` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `param_id` varchar(128) NOT NULL DEFAULT '' COMMENT '参数id;参数的唯一标识',
    `param_name` varchar(128) NOT NULL DEFAULT '' COMMENT '参数名称;参数的名称',
    `param_value` varchar(4000) NOT NULL DEFAULT '' COMMENT '参数值',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `param_data_type` ENUM('STRING', 'INT', 'FLOAT', 'OBJECT', 'ARRAY', 'DECIMAL', 'LONG', 'TABLE', 'BOOLEAN') NOT NULL DEFAULT 'STRING' COMMENT '参数数据类型【STRING：字符串；INT：整形数值；FLOAT：浮点型数值；OBJECT：对象；ARRAY：数组；DECIMAL：金额；LONG：长整型；TABLE：表格；BOOLEAN：布尔】',
    `param_binary_file` varchar(256) DEFAULT NULL COMMENT '参数二进制文件路径',
    PRIMARY KEY (`id`),
    KEY `inst_task_id_index` (`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例任务参数表';

DROP TABLE IF EXISTS `inst_task_param_attr`;
CREATE TABLE `inst_task_param_attr` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `param_id` varchar(128) NOT NULL DEFAULT '' COMMENT '参数id',
    `param_name` varchar(128) NOT NULL DEFAULT '' COMMENT '参数名称',
    `param_attr` JSON COMMENT '参数值JSON',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `inst_task_id_index` (`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例任务参数扩展表';

DROP TABLE IF EXISTS `inst_task_op_log`;
CREATE TABLE `inst_task_op_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `node_name` varchar(128) NOT NULL DEFAULT '' COMMENT '节点名称',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `type` ENUM('NODE', 'TASK', 'OTHER') NOT NULL DEFAULT 'NODE' COMMENT '类型【NODE：节点；TASK：任务；OTHER：其他】',
    `operation` ENUM('CREATE', 'START', 'AGREE', 'REJECT', 'TRANSFER', 'DELEGATE', 'RETURN', 'ADD_SIGN', 'REDUCE_SIGN', 'REVOKE', 'CANCEL', 'SUSPEND', 'RESUME', 'TERMINATE', 'URGE', 'COMPLETE') NOT NULL DEFAULT 'CREATE' COMMENT '操作类型',
    `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '描述',
    `op_user_id` bigint NOT NULL DEFAULT '' COMMENT '操作用户id',
    `op_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '操作用户名称',
    `op_data` JSON COMMENT '操作相关数据',
    PRIMARY KEY (`id`),
    KEY `inst_task_id_index` (`inst_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例任务操作日志表';

-- 新增的表，用于记录任务委托信息
DROP TABLE IF EXISTS `inst_task_delegation`;
CREATE TABLE `inst_task_delegation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `user_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户任务id',
    `delegator_user_id` bigint NOT NULL DEFAULT '' COMMENT '委托人用户id',
    `delegator_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '委托人用户名称',
    `delegatee_user_id` bigint NOT NULL DEFAULT '' COMMENT '被委托人用户id',
    `delegatee_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '被委托人用户名称',
    `status` ENUM('ACTIVE', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'ACTIVE' COMMENT '委托状态【ACTIVE：生效中；COMPLETED：已完成；CANCELLED：已取消】',
    `delegated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '委托时间',
    `complete_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
    `delegation_reason` varchar(1024) NOT NULL DEFAULT '' COMMENT '委托原因',
    `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `inst_task_delegation_index` (`inst_task_id`,`user_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务委托表';

-- 新增的表，用于记录任务转办信息
DROP TABLE IF EXISTS `inst_task_transfer`;
CREATE TABLE `inst_task_transfer` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `user_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户任务id',
    `from_user_id` bigint NOT NULL DEFAULT '' COMMENT '转出人用户id',
    `from_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '转出人用户名称',
    `to_user_id` bigint NOT NULL DEFAULT '' COMMENT '转入人用户id',
    `to_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '转入人用户名称',
    `transfer_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '转办时间',
    `transfer_reason` varchar(1024) NOT NULL DEFAULT '' COMMENT '转办原因',
    `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `inst_task_transfer_index` (`inst_task_id`,`user_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务转办表';

-- 新增的表，用于记录任务加签减签信息
DROP TABLE IF EXISTS `inst_task_sign`;
CREATE TABLE `inst_task_sign` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `user_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户任务id',
    `sign_type` ENUM('ADD', 'REDUCE') NOT NULL DEFAULT 'ADD' COMMENT '签类型【ADD：加签；REDUCE：减签】',
    `sign_mode` ENUM('BEFORE', 'AFTER', 'PARALLEL') NOT NULL DEFAULT 'PARALLEL' COMMENT '加签方式【BEFORE：前加签；AFTER：后加签；PARALLEL：并行加签】',
    `from_user_id` bigint NOT NULL DEFAULT '' COMMENT '发起用户id',
    `from_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '发起用户名称',
    `target_user_id` bigint NOT NULL DEFAULT '' COMMENT '目标用户id',
    `target_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '目标用户名称',
    `sign_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加/减签时间',
    `sign_reason` varchar(1024) NOT NULL DEFAULT '' COMMENT '加/减签原因',
    `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `inst_task_sign_index` (`inst_task_id`,`node_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务加签减签表';

-- 新增的表，用于记录任务催办信息
DROP TABLE IF EXISTS `inst_task_urge`;
CREATE TABLE `inst_task_urge` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `node_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '节点任务id',
    `user_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户任务id',
    `urge_user_id` bigint NOT NULL DEFAULT '' COMMENT '催办人用户id',
    `urge_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '催办人用户名称',
    `target_user_id` bigint NOT NULL DEFAULT '' COMMENT '被催办人用户id',
    `target_user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '被催办人用户名称',
    `urge_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '催办时间',
    `urge_reason` varchar(1024) NOT NULL DEFAULT '' COMMENT '催办原因',
    `urge_mode` ENUM('SYSTEM_MESSAGE', 'EMAIL', 'SMS', 'OTHER') NOT NULL DEFAULT 'SYSTEM_MESSAGE' COMMENT '催办方式【SYSTEM_MESSAGE：系统消息；EMAIL：邮件；SMS：短信；OTHER：其他】',
    PRIMARY KEY (`id`),
    KEY `inst_task_urge_index` (`inst_task_id`,`user_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务催办表';

-- 新增的表，用于记录流程监控和统计数据
DROP TABLE IF EXISTS `workflow_monitor_stats`;
CREATE TABLE `workflow_monitor_stats` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    `inst_task_id` varchar(32) NOT NULL DEFAULT '' COMMENT '实例任务id',
    `model_id` varchar(32) NOT NULL DEFAULT '' COMMENT '模板id',
    `process_def_id` varchar(32) NOT NULL DEFAULT '' COMMENT '流程定义id',
    `start_time` timestamp NULL DEFAULT NULL COMMENT '流程开始时间',
    `end_time` timestamp NULL DEFAULT NULL COMMENT '流程结束时间',
    `duration_seconds` bigint DEFAULT NULL COMMENT '流程持续时间(秒)',
    `status` ENUM('RUNNING', 'COMPLETED', 'TERMINATED', 'SUSPENDED') NOT NULL DEFAULT 'RUNNING' COMMENT '流程状态',
    `creator_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `creator_name` varchar(128) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `node_stats` JSON COMMENT '节点统计数据',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `inst_task_id_unique` (`inst_task_id`),
    KEY `model_process_index` (`model_id`,`process_def_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流程监控统计表';