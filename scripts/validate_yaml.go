package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"

	"gopkg.in/yaml.v3"
)

type MethodTemplate struct {
	Name        string   `yaml:"name"`
	Description string   `yaml:"description"`
	Condition   string   `yaml:"condition"`
	Imports     []string `yaml:"imports"`
	Template    string   `yaml:"template"`
}

type TemplateConfig struct {
	Templates []MethodTemplate `yaml:"templates"`
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run validate_yaml.go <yaml_file>")
		os.Exit(1)
	}

	filename := os.Args[1]
	
	// 读取文件
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		log.Fatalf("Error reading file: %v", err)
	}

	// 解析 YAML
	var config TemplateConfig
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		log.Fatalf("Error parsing YAML: %v", err)
	}

	// 验证成功
	fmt.Printf("✅ YAML file '%s' is valid!\n", filename)
	fmt.Printf("Found %d templates:\n", len(config.Templates))
	
	for i, template := range config.Templates {
		fmt.Printf("  %d. %s - %s\n", i+1, template.Name, template.Description)
	}
}
