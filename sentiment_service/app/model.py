
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from pathlib import Path
class SentimentModel:
    def __init__(self, model_name: str, model_dir: Path):
        self.model_name = model_name
        self.model_dir = model_dir
        self.device = self._get_device()
        self.tokenizer, self.model = self._load_model()

    def _get_device(self):
        # Check for Apple's Metal Performance Shaders (MPS) for M1/M2 chips
        if torch.backends.mps.is_available():
            print("MPS device found, using M1 GPU.")
            return torch.device("mps")
        # Fallback to CPU if MPS is not available
        print("MPS device not found, using CPU.")
        return torch.device("cpu")

    def _load_model(self):
        """
        Loads the model and tokenizer, downloading them if necessary.
        Saves them to the specified local directory for future use.
        """
        # Create the directory if it doesn't exist
        self.model_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"Loading tokenizer and model from {self.model_name}...")
        print(f"Models will be saved to: {self.model_dir.resolve()}")

        try:
            # Try to load from the local directory first
            tokenizer = AutoTokenizer.from_pretrained(str(self.model_dir))
            model = AutoModelForSequenceClassification.from_pretrained(str(self.model_dir))
            print("Model loaded successfully from local cache.")
        except (OSError, ValueError, TypeError) as e:
            print(f"Error loading model from local cache: {e}")
            # If not found locally or an error occurred, remove the incomplete directory and download from Hugging Face Hub
            if self.model_dir.exists():
                import shutil
                shutil.rmtree(self.model_dir)
                print(f"Removed incomplete model directory: {self.model_dir.resolve()}")

            print("Downloading from Hugging Face Hub...")
            tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            
            tokenizer.save_pretrained(str(self.model_dir))
            model.save_pretrained(str(self.model_dir))
            print(f"Model downloaded and saved to {self.model_dir.resolve()}")

        model.to(self.device)
        model.eval() # Set the model to evaluation mode
        return tokenizer, model

    def predict(self, text: str):
        """
        Performs sentiment analysis on the input text.
        Returns the predicted sentiment label, score, and probability distribution for all classes.
        Adjusts the prediction based on probability thresholds for better handling of low-confidence predictions.
        """
        inputs = self.tokenizer(text, return_tensors="pt", truncation=True, padding=True)
        
        # Move tensors to the selected device
        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        with torch.no_grad():
            outputs = self.model(**inputs)
        
        logits = outputs.logits
        probabilities = torch.softmax(logits, dim=-1)
        predicted_class_id = torch.argmax(probabilities, dim=-1).item()
        
        # Get the label and score for the predicted class
        score = probabilities[0][predicted_class_id].item()
        label = self.model.config.id2label[predicted_class_id]
        # Get probability distribution for all classes
        prob_dist = {self.model.config.id2label[i]: prob.item() for i, prob in enumerate(probabilities[0])}
        print(f"Predicted class ID: {predicted_class_id}, Label: {label}, Score: {score} prob_dist: {prob_dist}")
        return {"sentiment": label, "score": score, "probabilities": prob_dist}

# --- Global instance ---
# Define the model name and the directory to save it
MODEL_NAME = "jackietung/bert-base-chinese-finetuned-sentiment"
# Use a path relative to this file's location to find the 'models' directory
MODEL_DIR = Path(__file__).parent.parent / "models" / MODEL_NAME.replace("/", "_")

# Create a single, globally accessible model instance
# This ensures the model is loaded only once when the application starts.
sentiment_model = SentimentModel(model_name=MODEL_NAME, model_dir=MODEL_DIR)

# Example usage (for direct testing of this file)
if __name__ == '__main__':
    test_text = "我今天感到非常开心！"
    result = sentiment_model.predict(test_text)
    print(f"Text: '{test_text}'")
    print(f"Prediction: {result}")

    test_text_2 = "这个产品的质量太差了，我很失望。"
    result_2 = sentiment_model.predict(test_text_2)
    print(f"Text: '{test_text_2}'")
    print(f"Prediction: {result_2}")

    test_text_3 = "产品功能还行，但包装有点简陋，性价比一般"
    result_3 = sentiment_model.predict(test_text_3)
    print(f"Text: '{test_text_3}'")
    print(f"Prediction: {result_3}")
