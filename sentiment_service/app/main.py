from fastapi import FastAPI
from .schemas import SentimentRequest, SentimentResponse
from .model import sentiment_model

app = FastAPI(
    title="Sentiment Analysis API",
    description="A simple API to perform sentiment analysis on Chinese text.",
    version="1.0.0",
)

@app.post("/analyze", response_model=SentimentResponse)
def analyze_sentiment(request: SentimentRequest):
    """
    Analyzes the sentiment of a given text.

    - **text**: The Chinese text to analyze.
    """
    prediction = sentiment_model.predict(request.text)
    return SentimentResponse(
        text=request.text,
        sentiment=prediction['sentiment'],
        score=prediction['score']
    )

@app.get("/")
def read_root():
    return {"message": "Welcome to the Sentiment Analysis API. Please use the /docs endpoint to see the API documentation."}
