#!/bin/bash

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the script's directory
cd "$SCRIPT_DIR"

# Check if a virtual environment exists, if not, create one
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate the virtual environment
# Note: Activation works differently in scripts. We source it.
source venv/bin/activate

# Install/update dependencies
# Check if requirements are already satisfied to speed up subsequent runs
pip install -r requirements.txt

# Run the FastAPI application
# The --host 0.0.0.0 makes it accessible from outside the Docker container
# The --port 8000 is a standard port for web services
echo "Starting FastAPI server..."
uvicorn app.main:app --host 0.0.0.0 --port 8000
