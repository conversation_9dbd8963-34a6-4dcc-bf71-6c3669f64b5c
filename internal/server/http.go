package server

import (
	"admin/docs"
	"admin/internal/controller"
	"admin/internal/middleware"
	"admin/internal/utils"
	"admin/pkg/jwt"
	"admin/pkg/log"
	"admin/pkg/server/http"

	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"
)

func NewHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	jwt *jwt.JWT,
	e *casbin.SyncedEnforcer,
	userController *controller.UserController,
	loginController *controller.LoginController,
	postController *controller.PostController,
	deptController *controller.DeptController,
	roleController *controller.RoleController,
	routeController *controller.RouteController,
) *http.Server {
	gin.SetMode(gin.DebugMode)
	
	// 初始化验证器中文翻译
	if err := utils.InitValidator(); err != nil {
		logger.Error("初始化验证器失败", zap.Error(err))
	}
	s := http.NewServer(
		gin.Default(),
		logger,
		http.WithServerHost(conf.GetString("http.host")),
		http.WithServerPort(conf.GetInt("http.port")),
	)

	// swagger doc
	docs.SwaggerInfo.BasePath = "/"
	s.GET("/swagger/*any", ginSwagger.WrapHandler(
		swaggerfiles.Handler,
		// ginSwagger.URL(fmt.Sprintf("http://localhost:%d/swagger/doc.json", conf.GetInt("app.http.port"))),
		ginSwagger.DefaultModelsExpandDepth(-1),
		ginSwagger.PersistAuthorization(true),
	))
	s.Use(
		middleware.CORSMiddleware(),
	)

	admin := s.Group("/admin")
	{
		// No route group has permission
		noAuthRouter := admin.Group("/")
		{
			noAuthRouter.POST("/login", loginController.Login)
		}

		// Strict permission routing group
		admin.Use(middleware.StrictAuth(jwt, logger), middleware.AuthMiddleware(e))
		{
			// 获取用户信息
			admin.POST("/getUserInfo", loginController.GetUserInfo)
			// 获取用户公告
			system := admin.Group("/system")
			{
				// 用户
				user := system.Group("user")
				{
					user.POST("/check", userController.Check)
					user.POST("/query", userController.Query)
					user.POST("/queryPage", userController.QueryPage)
					user.POST("/create", userController.Create)
					user.POST("/update", userController.Update)
					user.POST("/delete", userController.Delete)
					user.POST("/queryById", userController.QueryById)
					user.POST("/enable", userController.Enable)
					user.POST("/disable", userController.Disable)
				}
				// 岗位
				post := system.Group("post")
				{
					post.POST("/query", postController.Query)
					post.POST("/queryPage", postController.QueryPage)
					post.POST("/create", postController.Create)
					post.POST("/update", postController.Update)
					post.POST("/delete", postController.Delete)
					post.POST("/queryById", postController.QueryById)
					post.POST("/enable", postController.Enable)
					post.POST("/disable", postController.Disable)
				}
				// 部门
				dept := system.Group("dept")
				{
					dept.POST("/query", deptController.Query)
					dept.POST("/queryPage", deptController.QueryPage)
					dept.POST("/create", deptController.Create)
					dept.POST("/update", deptController.Update)
					dept.POST("/delete", deptController.Delete)
					dept.POST("/queryById", deptController.QueryById)
					dept.POST("/enable", deptController.Enable)
					dept.POST("/disable", deptController.Disable)
				}
				// 角色
				role := system.Group("role")
				{
					role.POST("/query", roleController.QueryRole)
					role.POST("/queryPage", roleController.QueryRolePage)
					role.POST("/create", roleController.CreateRole)
					role.POST("/update", roleController.UpdateRole)
					role.POST("/delete", roleController.DeleteRole)
					role.POST("/queryById", roleController.QueryRoleById)
					role.POST("/enable", roleController.EnableRole)
					role.POST("/disable", roleController.DisableRole)
					role.POST("/updateRoleRouters", roleController.UpdateRoleRouters)
				}
				// 路由
				route := system.Group("route")
				{
					route.POST("/query", routeController.QueryRoute)
					route.POST("/create", routeController.CreateRoute)
					route.POST("/update", routeController.UpdateRoute)
					route.POST("/delete", routeController.DeleteRoute)
					route.POST("/queryById", routeController.QueryRouteById)
					route.POST("/updatePermissions", routeController.UpdateRoutePermissions)
				}
			}
		}

	}
	return s
}
