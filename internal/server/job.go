package server

import (
	"context"

	"admin/internal/job"
	"admin/pkg/log"
)

type JobServer struct {
	log     *log.Logger
	userJob job.UserJob
}

func NewJobServer(
	log *log.Logger,
	userJob job.UserJob,
) *JobServer {
	return &JobServer{
		log:     log,
		userJob: userJob,
	}
}

func (j *JobServer) Start(ctx context.Context) error {
	// 启动调度器
	// err := j.schedulerc.StartScheduler(ctx)
	// if err != nil {
	// 	return err
	// }

	return nil
}

func (j *JobServer) Stop(ctx context.Context) error {
	// Tips: If you want job to start as a separate process, just refer to the task implementation and adjust the code accordingly.
	// eg: kafka consumer
	// 停止调度器
	// err := j.schedulerc.StopScheduler(ctx)
	// if err != nil {
	// 	return err
	// }
	return nil
}
