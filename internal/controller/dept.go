package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
)

type DeptController struct {
	*Controller
	deptService service.DeptService
}

func NewDeptController(
	controller *Controller,
	deptService service.DeptService,
) *DeptController {
	return &DeptController{
		Controller:  controller,
		deptService: deptService,
	}
}

// QueryById 通过ID查询部门
func (c *DeptController) QueryById(ctx *gin.Context) {
	id := &v1.QID{}
	if err := ctx.ShouldBindJSON(id); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	user, err := c.deptService.QueryById(ctx, id)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, user)
}

// Create 创建部门
func (c *DeptController) Create(ctx *gin.Context) {
	dept := &v1.Dept{}
	if err := ctx.ShouldBindJSON(dept); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.deptService.Create(ctx, dept); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// QueryPage 分页查询部门
func (c *DeptController) QueryPage(ctx *gin.Context) {
	deptPage := &v1.DeptPage{}
	if err := ctx.ShouldBindJSON(deptPage); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	page, err := c.deptService.QueryPage(ctx, deptPage)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, page)
}

// Delete 删除部门
func (c *DeptController) Delete(ctx *gin.Context) {
	ids := &v1.QIDs[int64]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.deptService.Delete(ctx, ids); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Update 更新部门
func (c *DeptController) Update(ctx *gin.Context) {
	dept := &v1.Dept{}
	if err := ctx.ShouldBindJSON(dept); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.deptService.Update(ctx, dept); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有部门
func (c *DeptController) Query(ctx *gin.Context) {
	dept, err := c.deptService.Query(ctx)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, dept)
}

// Enable 启用部门
func (c *DeptController) Enable(ctx *gin.Context) {
	ids := &v1.QIDs[int64]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.deptService.Enable(ctx, ids); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用部门
func (c *DeptController) Disable(ctx *gin.Context) {
	ids := &v1.QIDs[int64]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.deptService.Disable(ctx, ids); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}
