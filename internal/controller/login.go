package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LoginController struct {
	*Controller
	loginService service.LoginService
}

func NewLoginController(
	controller *Controller,
	loginService service.LoginService,
) *LoginController {
	return &LoginController{
		Controller:   controller,
		loginService: loginService,
	}
}

// Login 登录
func (c *LoginController) Login(ctx *gin.Context) {
	ip := c.GetIpFromCtx(ctx)
	login := &v1.Login{
		IP: ip,
	}
	if err := ctx.ShouldBindJSON(&login); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	loginResp, err := c.loginService.Login(ctx, login)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	c.logger.Info("login success", zap.String("登录人ip", ip),
		zap.String("登录人账号", login.Account),
	)
	v1.HandleSuccess(ctx, loginResp)
}

// GetUserInfo 获取登录信息
func (c *LoginController) GetUserInfo(ctx *gin.Context) {
	userID := c.GetUserIdFromCtx(ctx)
	if userID == "" {
		c.logger.Error("用户未登录")
		v1.HandleError(ctx, v1.NewError("E9001", "用户未登录"))
		return
	}
	info, err := c.loginService.GetUserInfo(ctx, userID)
	if err != nil {
		c.logger.Error("GetUserInfo", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, info)
}
