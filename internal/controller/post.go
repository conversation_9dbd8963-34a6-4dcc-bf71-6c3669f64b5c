package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
)

type PostController struct {
	*Controller
	postService service.PostService
}

func NewPostController(
	controller *Controller,
	postService service.PostService,
) *PostController {
	return &PostController{
		Controller:  controller,
		postService: postService,
	}
}

// QueryById 通过ID查询用户
func (c *PostController) QueryById(ctx *gin.Context) {
	id := &v1.QID{}
	if err := ctx.ShouldBindJSON(id); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	user, err := c.postService.QueryById(ctx, id)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, user)
}

// Create 创建用户
func (c *PostController) Create(ctx *gin.Context) {
	post := &v1.Post{}
	if err := ctx.ShouldBindJSON(post); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.postService.Create(ctx, post); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// QueryPage 分页查询用户
func (c *PostController) QueryPage(ctx *gin.Context) {
	postPage := &v1.PostPage{}
	if err := ctx.ShouldBindJSON(postPage); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	page, err := c.postService.QueryPage(ctx, postPage)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, page)
}

// Delete 删除用户
func (c *PostController) Delete(ctx *gin.Context) {
	ids := &v1.QIDs[int64]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.postService.Delete(ctx, ids); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Update 更新用户
func (c *PostController) Update(ctx *gin.Context) {
	post := &v1.Post{}
	if err := ctx.ShouldBindJSON(post); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.postService.Update(ctx, post); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有用户
func (c *PostController) Query(ctx *gin.Context) {
	users, err := c.postService.Query(ctx)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, users)
}

// Enable 启用用户
func (c *PostController) Enable(ctx *gin.Context) {
	ids := &v1.QIDs[int64]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.postService.Enable(ctx, ids); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用用户
func (c *PostController) Disable(ctx *gin.Context) {
	ids := &v1.QIDs[int64]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}
	if err := c.postService.Disable(ctx, ids); err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}
