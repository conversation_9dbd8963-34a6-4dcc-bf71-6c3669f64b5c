package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
)

type PostController struct {
	*Controller
	postService service.PostService
}

func NewPostController(
	controller *Controller,
	postService service.PostService,
) *PostController {
	return &PostController{
		Controller:  controller,
		postService: postService,
	}
}

// QueryById 通过ID查询岗位
func (c *PostController) QueryById(ctx *gin.Context) {
	id := &v1.ID{}
	if err := ctx.ShouldBindJSON(id); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	user, err := c.postService.QueryById(ctx, id)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, user)
}

// Create 创建岗位
func (c *PostController) Create(ctx *gin.Context) {
	post := &v1.Post{}
	if err := ctx.ShouldBindJSON(post); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.postService.Create(ctx, post); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// QueryPage 分页查询岗位
func (c *PostController) QueryPage(ctx *gin.Context) {
	postPage := &v1.PostPage{}
	if err := ctx.ShouldBindJSON(postPage); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	page, err := c.postService.QueryPage(ctx, postPage)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, page)
}

// Delete 删除岗位
func (c *PostController) Delete(ctx *gin.Context) {
	ids := &v1.IDS[string]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.postService.Delete(ctx, ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Update 更新岗位
func (c *PostController) Update(ctx *gin.Context) {
	post := &v1.Post{}
	if err := ctx.ShouldBindJSON(post); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.postService.Update(ctx, post); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有岗位
func (c *PostController) Query(ctx *gin.Context) {
	users, err := c.postService.Query(ctx)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, users)
}

// Enable 启用岗位
func (c *PostController) Enable(ctx *gin.Context) {
	ids := &v1.IDS[string]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.postService.Enable(ctx, ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用岗位
func (c *PostController) Disable(ctx *gin.Context) {
	ids := &v1.IDS[string]{}
	if err := ctx.ShouldBindJSON(ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.postService.Disable(ctx, ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}
