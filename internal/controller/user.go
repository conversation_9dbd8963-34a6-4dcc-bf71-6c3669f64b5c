package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type UserController struct {
	*Controller
	userService service.UserService
}

func NewUserController(
	controller *Controller,
	userService service.UserService,
) *UserController {
	return &UserController{
		Controller:  controller,
		userService: userService,
	}
}

// Check 检查用户是否存在
func (c *UserController) Check(ctx *gin.Context) {
	var account v1.Account
	if err := ctx.ShouldBindJSON(&account); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	check := c.userService.Check(ctx, &account)
	v1.HandleSuccess(ctx, check)
}

// QueryById 通过ID查询用户
func (c *UserController) QueryById(ctx *gin.Context) {
	var id v1.ID
	if err := ctx.ShouldBindJSON(&id); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	user, err := c.userService.QueryById(ctx, &id)
	if err != nil {
		c.logger.Error("查询用户失败", zap.Error(err), zap.Any("id", id))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, user)
}

// Create 创建用户
func (c *UserController) Create(ctx *gin.Context) {
	var user v1.User
	if err := ctx.ShouldBindJSON(&user); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.userService.Create(ctx, &user); err != nil {
		c.logger.Error("创建用户失败", zap.Error(err), zap.Any("user", user))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// QueryPage 分页查询用户
func (c *UserController) QueryPage(ctx *gin.Context) {
	var userPage v1.UserPage
	if err := ctx.ShouldBindJSON(&userPage); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	page, err := c.userService.QueryPage(ctx, &userPage)
	if err != nil {
		c.logger.Error("分页查询用户失败", zap.Error(err), zap.Any("query", userPage))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, page)
}

// Delete 删除用户
func (c *UserController) Delete(ctx *gin.Context) {
	var ids v1.IDS[string]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.userService.Delete(ctx, &ids); err != nil {
		c.logger.Error("删除用户失败", zap.Error(err), zap.Any("ids", ids))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Update 更新用户
func (c *UserController) Update(ctx *gin.Context) {
	var user v1.User
	if err := ctx.ShouldBindJSON(&user); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.userService.Update(ctx, &user); err != nil {
		c.logger.Error("更新用户失败", zap.Error(err), zap.Any("user", user))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有用户
func (c *UserController) Query(ctx *gin.Context) {
	users, err := c.userService.Query(ctx)
	if err != nil {
		c.logger.Error("查询所有用户失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, users)
}

// Enable 启用用户
func (c *UserController) Enable(ctx *gin.Context) {
	var ids v1.IDS[string]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.userService.Enable(ctx, &ids); err != nil {
		c.logger.Error("启用用户失败", zap.Error(err), zap.Any("ids", ids))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用用户
func (c *UserController) Disable(ctx *gin.Context) {
	var ids v1.IDS[string]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.userService.Disable(ctx, &ids); err != nil {
		c.logger.Error("禁用用户失败", zap.Error(err), zap.Any("ids", ids))
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// 获取用户登录之后所有的菜单
func (c *UserController) GetMenus(ctx *gin.Context) {
	// user, err := c.userService.QueryById(ctx, &v1.QID{ID: ctx.GetInt64("user_id")})
	// if err != nil {
	// 	c.logger.Error("查询用户失败", zap.Error(err), zap.Any("id", ctx.GetInt64("user_id")))
	// 	v1.HandleError(ctx, v1.InternalError, err)
	// 	return
	// }
	// menus, err := c.menuService.QueryMenusByUserId(ctx, user.ID)
	// if err != nil {
	// 	c.logger.Error("查询用户菜单失败", zap.Error(err), zap.Any("user_id", user.ID))
	// 	v1.HandleError(ctx, v1.InternalError, err)
	// 	return
	// }
	// v1.HandleSuccess(ctx, menus)
}
