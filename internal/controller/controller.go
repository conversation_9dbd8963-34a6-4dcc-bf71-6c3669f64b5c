package controller

import (
	"strconv"

	"admin/pkg/jwt"
	"admin/pkg/log"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	logger *log.Logger
}

func NewController(logger *log.Logger) *Controller {
	return &Controller{
		logger: logger,
	}
}

func (c *Controller) GetUserIdFromCtx(ctx *gin.Context) int64 {
	v, exists := ctx.Get("claims")
	if !exists {
		return 0
	}
	return v.(*jwt.MyCustomClaims).UserId
}

// 获取登录ip
func (c *Controller) GetIpFromCtx(ctx *gin.Context) string {
	return ctx.ClientIP()
}

// GetCurrentUserID 获取当前用户ID
func GetCurrentUserID(ctx *gin.Context) int64 {
	// 从上下文中获取用户ID
	userID, exists := ctx.Get("userID")
	if !exists {
		return 0
	}
	// 转换为int64
	id, ok := userID.(int64)
	if !ok {
		return 0
	}
	return id
}

// GetIntParam 获取整数参数
func GetIntParam(ctx *gin.Context, key string, defaultValue int) int {
	valueStr := ctx.Query(key)
	if valueStr == "" {
		return defaultValue
	}
	value, err := strconv.Atoi(valueStr)
	if err != nil {
		return defaultValue
	}

	return value
}

// GetInt64Param 获取int64参数
func GetInt64Param(ctx *gin.Context, key string, defaultValue int64) int64 {
	valueStr := ctx.Query(key)
	if valueStr == "" {
		return defaultValue
	}

	value, err := strconv.ParseInt(valueStr, 10, 64)
	if err != nil {
		return defaultValue
	}

	return value
}
