package controller

import (
	"strconv"

	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
)

type RouteController struct {
	*Controller
	routeService service.RouteService
}

func NewRouteController(controller *Controller, routeService service.RouteService) *RouteController {
	return &RouteController{
		Controller:   controller,
		routeService: routeService,
	}
}

// CreateRoute 创建路由
func (c *RouteController) CreateRoute(ctx *gin.Context) {
	var route v1.Route
	if err := ctx.ShouldBindJSON(&route); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	err := c.routeService.Create(ctx, &route)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// UpdateRoute 更新路由
func (c *RouteController) UpdateRoute(ctx *gin.Context) {
	var route v1.Route
	if err := ctx.ShouldBindJSON(&route); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	err := c.routeService.Update(ctx, &route)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// DeleteRoute 删除路由
func (c *RouteController) DeleteRoute(ctx *gin.Context) {
	var ids v1.QIDs[int64]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	err := c.routeService.Delete(ctx, &ids)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// QueryRoute 查询路由列表
func (c *RouteController) QueryRoute(ctx *gin.Context) {
	routes, err := c.routeService.Query(ctx)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}
	v1.HandleSuccess(ctx, routes)
}

// QueryRouteById 查询路由详情
func (c *RouteController) QueryRouteById(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	route, err := c.routeService.QueryById(ctx, &v1.QID{ID: id})
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}

	v1.HandleSuccess(ctx, route)
}

// GetRoutePermissions 获取路由权限
func (c *RouteController) GetRoutePermissions(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	permissions, err := c.routeService.GetPermissions(ctx, id)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}

	v1.HandleSuccess(ctx, permissions)
}

// UpdateRoutePermissions 更新路由权限
func (c *RouteController) UpdateRoutePermissions(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	var permissions []string
	if err := ctx.ShouldBindJSON(&permissions); err != nil {
		v1.HandleValidationError(ctx, err)
		return
	}

	err = c.routeService.UpdatePermissions(ctx, id, permissions)
	if err != nil {
		v1.HandleError(ctx, v1.InternalError, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}
