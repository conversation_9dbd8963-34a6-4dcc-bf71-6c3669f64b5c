package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"

	"github.com/gin-gonic/gin"
)

type RoleController struct {
	*Controller
	roleService service.RoleService
}

func NewRoleController(controller *Controller, roleService service.RoleService) *RoleController {
	return &RoleController{
		Controller:  controller,
		roleService: roleService,
	}
}

// CreateRole 创建角色
func (c *RoleController) CreateRole(ctx *gin.Context) {
	var role v1.Role
	if err := ctx.ShouldBindJSON(&role); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.Create(ctx, &role); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// UpdateRole 更新角色
func (c *RoleController) UpdateRole(ctx *gin.Context) {
	var role v1.Role
	if err := ctx.ShouldBindJSON(&role); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.Update(ctx, &role); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// DeleteRole 删除角色
func (c *RoleController) DeleteRole(ctx *gin.Context) {
	var ids v1.IDS[string]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.Delete(ctx, &ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// QueryRolePage 分页查询角色
func (c *RoleController) QueryRolePage(ctx *gin.Context) {
	var page v1.RolePage
	if err := ctx.ShouldBindQuery(&page); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	rolePage, err := c.roleService.QueryPage(ctx, &page)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, rolePage)
}

// QueryRoleById 查询角色详情
func (c *RoleController) QueryRoleById(ctx *gin.Context) {
	id := ctx.Param("id")
	role, err := c.roleService.QueryById(ctx, &v1.ID{ID: id})
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, role)
}

// EnableRole 启用角色
func (c *RoleController) EnableRole(ctx *gin.Context) {
	var ids v1.IDS[string]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.Enable(ctx, &ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// DisableRole 禁用角色
func (c *RoleController) DisableRole(ctx *gin.Context) {
	var ids v1.IDS[string]
	if err := ctx.ShouldBindJSON(&ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.Disable(ctx, &ids); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// GetRolePermissions 获取角色权限
func (c *RoleController) GetRolePermissions(ctx *gin.Context) {
	id := ctx.Param("id")
	permissions, err := c.roleService.GetPermissions(ctx, id)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, permissions)
}

// UpdateRolePermissions 更新角色权限
func (c *RoleController) UpdateRolePermissions(ctx *gin.Context) {
	var rolePermission v1.RolePermission
	if err := ctx.ShouldBindJSON(&rolePermission); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.UpdatePermissions(ctx, rolePermission.RoleID, rolePermission.Permissions); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// GetRoleRouters 获取角色关联的路由
func (c *RoleController) GetRoleRouters(ctx *gin.Context) {
	id := ctx.Param("id")
	routers, err := c.roleService.GetRoutersByRoleId(ctx, id)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, routers)
}

// UpdateRoleRouters 更新角色关联的路由
// @Tags Role
// @Summary 更新角色关联的路由
// @Description 更新角色关联的路由
// @Accept json
// @Produce json
// @Param data body v1.RoleRouter true "角色路由信息"
// @Success 200 {object} v1.Response
// @Router /v1/admin/role/routers [put]
func (c *RoleController) UpdateRoleRouters(ctx *gin.Context) {
	var roleRouter v1.RoleRouter
	if err := ctx.ShouldBindJSON(&roleRouter); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	if err := c.roleService.UpdateRoleRouters(ctx, roleRouter.RoleID, roleRouter.RouterIDS); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, nil)
}

// QueryRole 查询角色列表
func (c *RoleController) QueryRole(ctx *gin.Context) {
	roles, err := c.roleService.Query(ctx)
	if err != nil {
		v1.HandleError(ctx, err)
		return
	}
	v1.HandleSuccess(ctx, roles)
}
