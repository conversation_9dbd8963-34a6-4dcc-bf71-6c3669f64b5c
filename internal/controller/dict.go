package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// DictController Dict 字典表控制器
type DictController struct {
	*Controller
	dictService service.DictService
}

// NewDictController 创建Dict 字典表控制器
func NewDictController(
	controller *Controller,
	dictService service.DictService,
) *DictController {
	return &DictController{
		Controller:  controller,
		dictService: dictService,
	}
}

// Create 创建Dict 字典表
// @Summary 创建Dict 字典表
// @Description 创建新的Dict 字典表记录
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.DictCreateReq true "创建Dict 字典表请求"
// @Success 200 {object} v1.Response "创建成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict [post]
func (c *DictController) Create(ctx *gin.Context) {
	var req v1.DictCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.dictService.Create(ctx, &req); err != nil {
		c.logger.Error("创建Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// QueryById 根据ID查询Dict 字典表
// @Summary 根据ID查询Dict 字典表
// @Description 根据ID获取Dict 字典表详细信息
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.ID true "查询ID请求"
// @Success 200 {object} v1.Response{data=v1.DictResp} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 404 {object} v1.Response "记录不存在"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict/detail [post]
func (c *DictController) QueryById(ctx *gin.Context) {
	var req v1.ID
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	result, err := c.dictService.QueryById(ctx, &req)
	if err != nil {
		c.logger.Error("查询Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// QueryPage 分页查询Dict 字典表
// @Summary 分页查询Dict 字典表
// @Description 分页获取Dict 字典表列表，支持搜索和排序
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.DictPageReq true "分页查询请求"
// @Success 200 {object} v1.Response{data=v1.Page[v1.DictResp]} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict/page [post]
func (c *DictController) QueryPage(ctx *gin.Context) {
	var req v1.DictPageReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	result, err := c.dictService.QueryPage(ctx, &req)
	if err != nil {
		c.logger.Error("分页查询Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// Update 更新Dict 字典表
// @Summary 更新Dict 字典表
// @Description 根据ID更新Dict 字典表信息
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.DictUpdateReq true "更新Dict 字典表请求"
// @Success 200 {object} v1.Response "更新成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 404 {object} v1.Response "记录不存在"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict [put]
func (c *DictController) Update(ctx *gin.Context) {
	var req v1.DictUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.dictService.Update(ctx, &req); err != nil {
		c.logger.Error("更新Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete 删除Dict 字典表
// @Summary 删除Dict 字典表
// @Description 根据ID列表批量删除Dict 字典表
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "删除ID列表请求"
// @Success 200 {object} v1.Response "删除成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict [delete]
func (c *DictController) Delete(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.dictService.Delete(ctx, &req); err != nil {
		c.logger.Error("删除Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有Dict 字典表
// @Summary 查询所有Dict 字典表
// @Description 获取所有Dict 字典表列表，不分页
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Success 200 {object} v1.Response{data=[]v1.DictResp} "查询成功"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict/all [get]
func (c *DictController) Query(ctx *gin.Context) {
	result, err := c.dictService.Query(ctx)
	if err != nil {
		c.logger.Error("查询所有Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// Enable 启用Dict 字典表
// @Summary 启用Dict 字典表
// @Description 根据ID列表批量启用Dict 字典表
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "启用ID列表请求"
// @Success 200 {object} v1.Response "启用成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict/enable [post]
func (c *DictController) Enable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.dictService.UpdateStatus(ctx, &req, "enabled"); err != nil {
		c.logger.Error("启用Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用Dict 字典表
// @Summary 禁用Dict 字典表
// @Description 根据ID列表批量禁用Dict 字典表
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "禁用ID列表请求"
// @Success 200 {object} v1.Response "禁用成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict/disable [post]
func (c *DictController) Disable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.dictService.UpdateStatus(ctx, &req, "disabled"); err != nil {
		c.logger.Error("禁用Dict 字典表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// QueryTree 查询Dict 字典表树形结构
// @Summary 查询Dict 字典表树形结构
// @Description 获取Dict 字典表的树形结构数据，包含父子关系
// @Tags Dict 字典表管理
// @Accept json
// @Produce json
// @Param request body v1.DictTreeReq false "树形查询请求"
// @Success 200 {object} v1.Response{data=[]v1.DictResp} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/dict/tree [post]
func (c *DictController) QueryTree(ctx *gin.Context) {
	result, err := c.dictService.QueryTree(ctx)
	if err != nil {
		c.logger.Error("查询Dict 字典表树形结构失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}
