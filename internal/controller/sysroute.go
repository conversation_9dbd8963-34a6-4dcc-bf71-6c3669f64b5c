package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SysRouteController SysRoute 路由表控制器
type SysRouteController struct {
	*Controller
	sys_routeService service.SysRouteService
}

// NewSysRouteController 创建SysRoute 路由表控制器
func NewSysRouteController(
	controller *Controller,
	sys_routeService service.SysRouteService,
) *SysRouteController {
	return &SysRouteController{
		Controller:       controller,
		sys_routeService: sys_routeService,
	}
}

// Create 创建SysRoute 路由表
// @Summary 创建SysRoute 路由表
// @Description 创建新的SysRoute 路由表记录
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.SysRouteCreateReq true "创建SysRoute 路由表请求"
// @Success 200 {object} v1.Response "创建成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route [post]
func (c *SysRouteController) Create(ctx *gin.Context) {
	var req v1.SysRouteCreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.sys_routeService.Create(ctx, &req); err != nil {
		c.logger.Error("创建SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// QueryById 根据ID查询SysRoute 路由表
// @Summary 根据ID查询SysRoute 路由表
// @Description 根据ID获取SysRoute 路由表详细信息
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.ID true "查询ID请求"
// @Success 200 {object} v1.Response{data=v1.SysRouteResp} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 404 {object} v1.Response "记录不存在"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route/detail [post]
func (c *SysRouteController) QueryById(ctx *gin.Context) {
	var req v1.ID
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	result, err := c.sys_routeService.QueryById(ctx, &req)
	if err != nil {
		c.logger.Error("查询SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// QueryPage 分页查询SysRoute 路由表
// @Summary 分页查询SysRoute 路由表
// @Description 分页获取SysRoute 路由表列表，支持搜索和排序
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.SysRoutePageReq true "分页查询请求"
// @Success 200 {object} v1.Response{data=v1.Page[v1.SysRouteResp]} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route/page [post]
func (c *SysRouteController) QueryPage(ctx *gin.Context) {
	var req v1.SysRoutePageReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	result, err := c.sys_routeService.QueryPage(ctx, &req)
	if err != nil {
		c.logger.Error("分页查询SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// Update 更新SysRoute 路由表
// @Summary 更新SysRoute 路由表
// @Description 根据ID更新SysRoute 路由表信息
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.SysRouteUpdateReq true "更新SysRoute 路由表请求"
// @Success 200 {object} v1.Response "更新成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 404 {object} v1.Response "记录不存在"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route [put]
func (c *SysRouteController) Update(ctx *gin.Context) {
	var req v1.SysRouteUpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.sys_routeService.Update(ctx, &req); err != nil {
		c.logger.Error("更新SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete 删除SysRoute 路由表
// @Summary 删除SysRoute 路由表
// @Description 根据ID列表批量删除SysRoute 路由表
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "删除ID列表请求"
// @Success 200 {object} v1.Response "删除成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route [delete]
func (c *SysRouteController) Delete(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.sys_routeService.Delete(ctx, &req); err != nil {
		c.logger.Error("删除SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有SysRoute 路由表
// @Summary 查询所有SysRoute 路由表
// @Description 获取所有SysRoute 路由表列表，不分页
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Success 200 {object} v1.Response{data=[]v1.SysRouteResp} "查询成功"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route/all [get]
func (c *SysRouteController) Query(ctx *gin.Context) {
	result, err := c.sys_routeService.Query(ctx)
	if err != nil {
		c.logger.Error("查询所有SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// Enable 启用SysRoute 路由表
// @Summary 启用SysRoute 路由表
// @Description 根据ID列表批量启用SysRoute 路由表
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "启用ID列表请求"
// @Success 200 {object} v1.Response "启用成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route/enable [post]
func (c *SysRouteController) Enable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.sys_routeService.UpdateStatus(ctx, &req, "enabled"); err != nil {
		c.logger.Error("启用SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用SysRoute 路由表
// @Summary 禁用SysRoute 路由表
// @Description 根据ID列表批量禁用SysRoute 路由表
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "禁用ID列表请求"
// @Success 200 {object} v1.Response "禁用成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route/disable [post]
func (c *SysRouteController) Disable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}

	if err := c.sys_routeService.UpdateStatus(ctx, &req, "disabled"); err != nil {
		c.logger.Error("禁用SysRoute 路由表失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// QueryTree 查询SysRoute 路由表树形结构
// @Summary 查询SysRoute 路由表树形结构
// @Description 获取SysRoute 路由表的树形结构数据，包含父子关系
// @Tags SysRoute 路由表管理
// @Accept json
// @Produce json
// @Param request body v1.SysRouteTreeReq false "树形查询请求"
// @Success 200 {object} v1.Response{data=[]v1.SysRouteResp} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/sys_route/tree [post]
func (c *SysRouteController) QueryTree(ctx *gin.Context) {
	result, err := c.sys_routeService.QueryTree(ctx)
	if err != nil {
		c.logger.Error("查询SysRoute 路由表树形结构失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}

	v1.HandleSuccess(ctx, result)
}
