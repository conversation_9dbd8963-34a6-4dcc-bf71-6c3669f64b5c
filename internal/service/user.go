package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"

	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type UserService interface {
	Check(ctx context.Context, user *v1.Account) bool
	QueryById(ctx context.Context, id *v1.QID) (*model.User, error)
	Create(ctx context.Context, user *v1.User) error
	QueryPage(ctx context.Context, user *v1.UserPage) (*v1.Page[*model.User], error)
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	Update(ctx context.Context, user *v1.User) error
	Query(ctx context.Context) ([]*model.User, error)
	Enable(ctx context.Context, ids *v1.QIDs[int64]) error
	Disable(ctx context.Context, ids *v1.QIDs[int64]) error
}

func NewUserService(
	service *Service,
	userRepository repository.UserRepository,
) UserService {
	return &userService{
		Service:        service,
		userRepository: userRepository,
	}
}

type userService struct {
	*Service
	userRepository repository.UserRepository
}

func (s *userService) Check(ctx context.Context, user *v1.Account) bool {
	// 构建缓存键
	var cacheKeyParts []string
	if user.Email != "" {
		cacheKeyParts = append(cacheKeyParts, user.Email)
	}
	if user.Phone != "" {
		cacheKeyParts = append(cacheKeyParts, user.Phone)
	}
	cacheKey := fmt.Sprintf("user_check:%s", strings.Join(cacheKeyParts, ":"))
	// 尝试从 Redis 缓存中获取结果
	existsCount, err := s.redis.Exists(ctx, cacheKey).Result()
	if err != nil {
		s.logger.Warn("查询 Redis 缓存失败", zap.Error(err))
	} else if existsCount > 0 {
		isCacheHit, err := s.redis.Get(ctx, cacheKey).Result()
		if err != nil {
			s.logger.Warn("获取 Redis 缓存失败", zap.Error(err))
		} else {
			return isCacheHit == "true"
		}
	}
	exists, err := s.userRepository.Check(ctx, user)
	if err != nil {
		s.logger.Warn("用户查询失败", zap.Error(err))
		return false
	}
	// 判断是否存在并更新 Redis 缓存
	result := exists != nil
	cacheValue := "false"
	if result {
		cacheValue = "true"
	}
	setSuccess, err := s.redis.SetEx(ctx, cacheKey, cacheValue, time.Minute*10).Result()
	if err != nil {
		s.logger.Warn("写入 Redis 缓存失败", zap.Error(errors.New("setex operation failed")))
	}
	return setSuccess == "OK"
}

func (s *userService) QueryById(ctx context.Context, id *v1.QID) (*model.User, error) {
	user, err := s.userRepository.QueryById(ctx, id)
	if err != nil {
		s.logger.Warn("用户查询失败", zap.Error(err))
		return nil, errors.Wrap(err, "用户查询失败")
	}
	return user, nil
}

func (s *userService) Create(ctx context.Context, user *v1.User) error {
	return s.userRepository.Update(ctx, &model.User{
		ID:       user.ID,
		Account:  user.Account,
		Email:    &user.Email,
		Phone:    &user.Phone,
		Sex:      &user.Sex,
		Status:   &user.Status,
		Username: user.Username,
		Remark:   &user.Remark,
		Sort:     &user.Sort,
	})
}

func (s *userService) QueryPage(ctx context.Context, user *v1.UserPage) (*v1.Page[*model.User], error) {
	return s.userRepository.QueryPage(ctx, user)
}

func (s *userService) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	s.logger.Info("删除用户", zap.Int64s("ids", ids.Ids))
	return s.userRepository.Delete(ctx, ids)
}

// Update 更新用户
func (s *userService) Update(ctx context.Context, user *v1.User) error {
	return s.userRepository.Update(ctx, &model.User{
		ID:       user.ID,
		Account:  user.Account,
		Email:    &user.Email,
		Phone:    &user.Phone,
		Sex:      &user.Sex,
		Status:   &user.Status,
		Username: user.Username,
		Remark:   &user.Remark,
		Sort:     &user.Sort,
	})
}

func (s *userService) Enable(ctx context.Context, ids *v1.QIDs[int64]) error {
	return s.userRepository.UpdateStatus(ctx, ids, "enable")
}

// Disable 禁用User
func (s *userService) Disable(ctx context.Context, ids *v1.QIDs[int64]) error {
	return s.userRepository.UpdateStatus(ctx, ids, "disable")
}

func (s *userService) Query(ctx context.Context) ([]*model.User, error) {
	s.logger.Info("查询用户列表")
	return s.userRepository.Query(ctx)
}
