package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"
)

type RouteService interface {
	QueryById(ctx context.Context, id *v1.QID) (*v1.Route, error)
	Create(ctx context.Context, route *v1.Route) error
	Update(ctx context.Context, route *v1.Route) error
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	Query(ctx context.Context) ([]*v1.Route, error)
	GetPermissions(ctx context.Context, roleId int64) ([]string, error)
	UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error
	AddRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error
	DeleteRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error
	GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error)
}

type routeService struct {
	repo repository.RouteRepository
}

func NewRouteService(repo repository.RouteRepository) RouteService {
	return &routeService{
		repo: repo,
	}
}

func (s *routeService) QueryById(ctx context.Context, id *v1.QID) (*v1.Route, error) {
	return s.repo.QueryById(ctx, id)
}

func (s *routeService) Create(ctx context.Context, route *v1.Route) error {
	return s.repo.Create(ctx, route)
}

func (s *routeService) Update(ctx context.Context, route *v1.Route) error {
	return s.repo.Update(ctx, route)
}

func (s *routeService) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	return s.repo.Delete(ctx, ids)
}

func (s *routeService) Query(ctx context.Context) ([]*v1.Route, error) {
	return s.repo.Query(ctx)
}

func (s *routeService) GetPermissions(ctx context.Context, roleId int64) ([]string, error) {
	return s.repo.GetPermissions(ctx, roleId)
}

func (s *routeService) UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error {
	return s.repo.UpdatePermissions(ctx, roleId, permissions)
}

func (s *routeService) AddRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error {
	return s.repo.AddRoutersForRole(ctx, roleId, routerIds)
}

func (s *routeService) DeleteRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error {
	return s.repo.DeleteRoutersForRole(ctx, roleId, routerIds)
}

func (s *routeService) GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error) {
	return s.repo.GetRoutersByRoleId(ctx, roleId)
}
