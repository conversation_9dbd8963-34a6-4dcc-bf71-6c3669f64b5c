package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"

	"go.uber.org/zap"
)

type RoleService interface {
	QueryById(ctx context.Context, id *v1.QID) (*model.Role, error)
	Create(ctx context.Context, role *v1.Role) error
	QueryPage(ctx context.Context, page *v1.RolePage) (*v1.Page[*model.Role], error)
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	Update(ctx context.Context, role *v1.Role) error
	Query(ctx context.Context) ([]*model.Role, error)
	Enable(ctx context.Context, ids *v1.QIDs[int64]) error
	Disable(ctx context.Context, ids *v1.QIDs[int64]) error
	GetPermissions(ctx context.Context, roleId int64) ([]string, error)
	UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error
	GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error)
	UpdateRoleRouters(ctx context.Context, roleId int64, routerIds []int64) error
}

type roleService struct {
	*Service
	roleRepository repository.RoleRepository
}

func NewRoleService(
	service *Service,
	roleRepository repository.RoleRepository,
) RoleService {
	return &roleService{
		Service:        service,
		roleRepository: roleRepository,
	}
}

func (s *roleService) QueryById(ctx context.Context, id *v1.QID) (*model.Role, error) {
	return s.roleRepository.QueryById(ctx, id)
}

func (s *roleService) Create(ctx context.Context, role *v1.Role) error {
	s.logger.Info("创建角色", zap.String("name", role.Name))

	status := "enable"
	// 构造角色模型
	roleModel := &model.Role{
		Name:     role.Name,
		Status:   &status,
		ParentID: role.ParentID,
		Sort:     role.Sort,
	}

	return s.roleRepository.Create(ctx, roleModel)
}

func (s *roleService) QueryPage(ctx context.Context, page *v1.RolePage) (*v1.Page[*model.Role], error) {
	s.logger.Info("查询角色分页列表", zap.Int("page", page.Page), zap.Int("pageSize", page.PageSize))
	return s.roleRepository.QueryPage(ctx, page)
}

func (s *roleService) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	s.logger.Info("删除角色", zap.Int64s("ids", ids.Ids))
	return s.roleRepository.Delete(ctx, ids)
}

func (s *roleService) Update(ctx context.Context, role *v1.Role) error {
	s.logger.Info("更新角色", zap.Int64("id", role.ID), zap.String("name", role.Name))

	// 构造角色模型
	roleModel := &model.Role{
		ID:       role.ID,
		Name:     role.Name,
		ParentID: role.ParentID,
		Sort:     role.Sort,
	}

	// 如果有状态
	if role.Status != nil {
		roleModel.Status = role.Status
	}
	return s.roleRepository.Update(ctx, roleModel)
}

func (s *roleService) Query(ctx context.Context) ([]*model.Role, error) {
	s.logger.Info("查询角色列表")
	return s.roleRepository.Query(ctx)
}

func (s *roleService) Enable(ctx context.Context, ids *v1.QIDs[int64]) error {
	s.logger.Info("启用角色", zap.Int64s("ids", ids.Ids))
	return s.roleRepository.UpdateStatus(ctx, ids, "enable")
}

func (s *roleService) Disable(ctx context.Context, ids *v1.QIDs[int64]) error {
	s.logger.Info("禁用角色", zap.Int64s("ids", ids.Ids))
	return s.roleRepository.UpdateStatus(ctx, ids, "disabled")
}

func (s *roleService) GetPermissions(ctx context.Context, roleId int64) ([]string, error) {
	s.logger.Info("获取角色权限", zap.Int64("roleId", roleId))
	return s.roleRepository.GetPermissions(ctx, roleId)
}

func (s *roleService) UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error {
	s.logger.Info("更新角色权限", zap.Int64("roleId", roleId), zap.Strings("permissions", permissions))
	return s.roleRepository.UpdatePermissions(ctx, roleId, permissions)
}

func (s *roleService) GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error) {
	s.logger.Info("获取角色路由", zap.Int64("roleId", roleId))
	return s.roleRepository.GetRoutersByRoleId(ctx, roleId)
}

func (s *roleService) UpdateRoleRouters(ctx context.Context, roleId int64, routerIds []int64) error {
	s.logger.Info("更新角色路由", zap.Int64("roleId", roleId), zap.Int64s("routerIds", routerIds))
	// 首先获取现有的角色路由
	currentRouters, err := s.roleRepository.GetRoutersByRoleId(ctx, roleId)
	if err != nil {
		return err
	}

	// 转换为 map 方便查找
	currentRouterMap := make(map[int64]struct{}, len(currentRouters))
	for _, router := range currentRouters {
		currentRouterMap[router.RouteID] = struct{}{}
	}

	// 计算需要新增的路由
	routersToAdd := make([]int64, 0)
	for _, routerId := range routerIds {
		if _, exists := currentRouterMap[routerId]; !exists {
			routersToAdd = append(routersToAdd, routerId)
		}
		delete(currentRouterMap, routerId) // 删除匹配的，剩下的就是需要移除的
	}

	// 计算需要删除的路由
	routersToDelete := make([]int64, 0, len(currentRouterMap))
	for routerId := range currentRouterMap {
		routersToDelete = append(routersToDelete, routerId)
	}

	// 执行添加和删除操作
	if len(routersToAdd) > 0 {
		if err := s.roleRepository.AddRoutersForRole(ctx, roleId, routersToAdd); err != nil {
			return err
		}
	}

	if len(routersToDelete) > 0 {
		if err := s.roleRepository.DeleteRoutersForRole(ctx, roleId, routersToDelete); err != nil {
			return err
		}
	}

	return nil
}
