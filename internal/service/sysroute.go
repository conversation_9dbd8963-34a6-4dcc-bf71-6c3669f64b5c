package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"

	"github.com/pkg/errors"
)

// SysRouteService SysRoute 路由表服务接口
type SysRouteService interface {
	Create(ctx context.Context, req *v1.SysRouteCreateReq) error
	QueryById(ctx context.Context, req *v1.ID) (*v1.SysRouteResp, error)
	QueryPage(ctx context.Context, req *v1.SysRoutePageReq) (*v1.Page[*v1.SysRouteResp], error)
	Update(ctx context.Context, req *v1.SysRouteUpdateReq) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	Query(ctx context.Context) ([]*model.SysRoute, error)

	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error
	Enable(ctx context.Context, req *v1.IDS[string]) error
	Disable(ctx context.Context, req *v1.IDS[string]) error

	QueryTree(ctx context.Context) ([]*model.SysRoute, error)
}

// sys_routeService SysRoute 路由表服务实现
type sys_routeService struct {
	*Service
	sys_routeRepository repository.SysRouteRepository
}

// NewSysRouteService 创建SysRoute 路由表服务
func NewSysRouteService(
	service *Service,
	sys_routeRepository repository.SysRouteRepository,
) SysRouteService {
	return &sys_routeService{
		Service:             service,
		sys_routeRepository: sys_routeRepository,
	}
}

// Create 创建SysRoute 路由表
func (s *sys_routeService) Create(ctx context.Context, req *v1.SysRouteCreateReq) error {
	// 请求参数已在Controller层通过binding标签验证
	entity := &model.SysRoute{
		Name: req.Name,

		Path: req.Path,

		Component: req.Component,

		Redirect: req.Redirect,

		ParentID: req.ParentID,

		Sort: req.Sort,

		Status: req.Status,

		IsLayout: req.IsLayout,

		Title: req.Title,

		Icon: req.Icon,

		Hidden: req.Hidden,

		ActiveMenu: req.ActiveMenu,

		KeepAlive: req.KeepAlive,

		Auth: req.Auth,

		Breadcrumb: req.Breadcrumb,

		External: req.External,

		ExternalURL: req.ExternalURL,

		TenantID: req.TenantID,
	}

	err := s.sys_routeRepository.Create(ctx, entity)
	if err != nil {
		return v1.ErrCreateFailed
	}

	return nil
}

// QueryById 根据ID查询SysRoute 路由表
func (s *sys_routeService) QueryById(ctx context.Context, req *v1.ID) (*v1.SysRouteResp, error) {
	entity, err := s.sys_routeRepository.QueryById(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	if entity == nil {
		return nil, v1.ErrNotFound
	}

	return s.modelToResp(entity), nil
}

// QueryPage 分页查询SysRoute 路由表
func (s *sys_routeService) QueryPage(ctx context.Context, req *v1.SysRoutePageReq) (*v1.Page[*v1.SysRouteResp], error) {
	result, err := s.sys_routeRepository.QueryPage(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	// 转换为响应结构
	respList := s.modelListToRespList(result.List)
	page := &v1.Page[*v1.SysRouteResp]{
		Current:  result.Current,
		PageSize: result.PageSize,
		Total:    result.Total,
		List:     respList,
	}

	return page, nil
}

// Update 更新SysRoute 路由表
func (s *sys_routeService) Update(ctx context.Context, req *v1.SysRouteUpdateReq) error {
	// 请求参数已在Controller层通过binding标签验证

	// 检查记录是否存在
	existing, err := s.sys_routeRepository.QueryById(ctx, &v1.ID{ID: req.ID})
	if err != nil {
		return v1.ErrQueryFailed
	}
	if existing == nil {
		return v1.ErrNotFound
	}

	// 只更新非系统字段
	entity := &model.SysRoute{
		UUID: req.ID, // 设置UUID用于查找记录

		Name: req.Name,

		Path: req.Path,

		Component: req.Component,

		Redirect: req.Redirect,

		ParentID: req.ParentID,

		Sort: req.Sort,

		Status: req.Status,

		IsLayout: req.IsLayout,

		Title: req.Title,

		Icon: req.Icon,

		Hidden: req.Hidden,

		ActiveMenu: req.ActiveMenu,

		KeepAlive: req.KeepAlive,

		Auth: req.Auth,

		Breadcrumb: req.Breadcrumb,

		External: req.External,

		ExternalURL: req.ExternalURL,

		TenantID: req.TenantID,
	}

	err = s.sys_routeRepository.Update(ctx, entity)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	return nil
}

// Delete 删除SysRoute 路由表
func (s *sys_routeService) Delete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("删除ID列表不能为空")
	}

	err := s.sys_routeRepository.Delete(ctx, req)
	if err != nil {
		return v1.ErrDeleteFailed
	}

	return nil
}

// Query 查询所有SysRoute 路由表
func (s *sys_routeService) Query(ctx context.Context) ([]*model.SysRoute, error) {
	entities, err := s.sys_routeRepository.Query(ctx)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	return entities, nil
}

// UpdateStatus 更新SysRoute 路由表状态
func (s *sys_routeService) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	err := s.sys_routeRepository.UpdateStatus(ctx, req, status)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	return nil
}

// Enable 启用SysRoute 路由表
func (s *sys_routeService) Enable(ctx context.Context, req *v1.IDS[string]) error {
	return s.UpdateStatus(ctx, req, "enabled")
}

// Disable 禁用SysRoute 路由表
func (s *sys_routeService) Disable(ctx context.Context, req *v1.IDS[string]) error {
	return s.UpdateStatus(ctx, req, "disabled")
}

// QueryTree 查询SysRoute 路由表树形结构
func (s *sys_routeService) QueryTree(ctx context.Context) ([]*model.SysRoute, error) {
	list, err := s.sys_routeRepository.Query(ctx)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	// 转换为树形结构
	var tree []*model.SysRoute
	if len(list) > 0 {
		tree = list[0].ToTree(list)
	} else {
		tree = list
	}

	return tree, nil
}

// modelToResp 将模型转换为响应结构体
func (s *sys_routeService) modelToResp(entity *model.SysRoute) *v1.SysRouteResp {
	if entity == nil {
		return nil
	}

	resp := &v1.SysRouteResp{
		ID:        entity.ID,
		UUID:      entity.UUID,
		CreatedAt: v1.TimeToModelTimeNormal(entity.CreatedAt),
		UpdatedAt: v1.TimeToModelTimeNormal(entity.UpdatedAt),

		Name: entity.Name,

		Path: entity.Path,

		Component: entity.Component,

		Redirect: entity.Redirect,

		ParentID: entity.ParentID,

		Sort: entity.Sort,

		Status: entity.Status,

		IsLayout: entity.IsLayout,

		Title: entity.Title,

		Icon: entity.Icon,

		Hidden: entity.Hidden,

		ActiveMenu: entity.ActiveMenu,

		KeepAlive: entity.KeepAlive,

		Auth: entity.Auth,

		Breadcrumb: entity.Breadcrumb,

		External: entity.External,

		ExternalURL: entity.ExternalURL,

		TenantID: entity.TenantID,
	}

	// 处理子节点
	if len(entity.Children) > 0 {
		resp.Children = s.modelListToRespList(entity.Children)
	}

	return resp
}

// modelListToRespList 将模型列表转换为响应列表
func (s *sys_routeService) modelListToRespList(entities []*model.SysRoute) []*v1.SysRouteResp {
	if len(entities) == 0 {
		return nil
	}

	respList := make([]*v1.SysRouteResp, len(entities))
	for i, entity := range entities {
		respList[i] = s.modelToResp(entity)
	}
	return respList
}
