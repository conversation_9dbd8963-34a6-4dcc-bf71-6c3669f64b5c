package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"

	"go.uber.org/zap"
)

type PostService interface {
	QueryById(ctx context.Context, id *v1.QID) (*model.Post, error)
	Create(ctx context.Context, user *v1.Post) error
	QueryPage(ctx context.Context, user *v1.PostPage) (*v1.Page[*model.Post], error)
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	Update(ctx context.Context, user *v1.Post) error
	Query(ctx context.Context) ([]*model.Post, error)
	Enable(ctx context.Context, ids *v1.QIDs[int64]) error
	Disable(ctx context.Context, ids *v1.QIDs[int64]) error
}

func NewPostService(
	service *Service,
	postRepository repository.PostRepository,
) PostService {
	return &postService{
		Service:        service,
		postRepository: postRepository,
	}
}

type postService struct {
	*Service
	postRepository repository.PostRepository
}

func (s *postService) QueryById(ctx context.Context, id *v1.QID) (*model.Post, error) {
	return s.postRepository.QueryById(ctx, id)
}

func (s *postService) Create(ctx context.Context, post *v1.Post) error {
	return s.postRepository.Create(ctx, &model.Post{
		Name:        post.Name,
		Description: &post.Description,
	})
}

func (s *postService) QueryPage(ctx context.Context, user *v1.PostPage) (*v1.Page[*model.Post], error) {
	return s.postRepository.QueryPage(ctx, user)
}

func (s *postService) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	s.logger.Info("删除岗位", zap.Int64s("ids", ids.Ids))
	return s.postRepository.Delete(ctx, ids)
}

// Update 更新用户
func (s *postService) Update(ctx context.Context, post *v1.Post) error {
	return s.postRepository.Update(ctx, &model.Post{
		ID:          post.ID,
		Name:        post.Name,
		Description: &post.Description,
	})
}

// Disable 启用岗位
func (s *postService) Enable(ctx context.Context, ids *v1.QIDs[int64]) error {
	return s.postRepository.UpdateStatus(ctx, ids, "enable")
}

// Disable 禁用岗位
func (s *postService) Disable(ctx context.Context, ids *v1.QIDs[int64]) error {
	return s.postRepository.UpdateStatus(ctx, ids, "disable")
}

func (s *postService) Query(ctx context.Context) ([]*model.Post, error) {
	s.logger.Info("查询用户列表")
	return s.postRepository.Query(ctx)
}
