package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"
	"admin/internal/utils"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	// 密码错误error
	PasswordError = errors.New("password error")
	// 用户不存在error
	UserNotFoundError = errors.New("user not found")
	// 用户被禁用error
	UserDisableError = errors.New("user is disable")
)

type LoginService interface {
	Login(ctx context.Context, req *v1.Login) (*v1.LoginResp, error)               // 登录
	WriteLoginInfo(ctx context.Context, id int64, ip string) error                 // 登录成功后写入登录信息
	GetUserInfo(ctx context.Context, userID int64) (user *v1.LoginUser, err error) // 获取登录信息
}
type loginService struct {
	*Service
	loginRepository repository.LoginRepository
}

func NewLoginService(s *Service, loginRepository repository.LoginRepository) LoginService {
	return &loginService{
		Service:         s,
		loginRepository: loginRepository,
	}
}

// GetByPhoneEmailAccount 根据手机号码或邮件或账号 登录
func (s *loginService) getByPhoneEmailAccount(ctx context.Context, account string) (*model.User, error) {
	if utils.IsEmail(account) {
		return s.loginRepository.GetByEmail(ctx, account)
	}
	if utils.IsPhone(account) {
		return s.loginRepository.GetByMobile(ctx, account)
	}
	return s.loginRepository.GetByAccount(ctx, account)
}

func (s *loginService) Login(ctx context.Context, req *v1.Login) (*v1.LoginResp, error) {
	user, err := s.getByPhoneEmailAccount(ctx, req.Account)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, UserNotFoundError
		}
		return nil, errors.Wrap(err, "getByPhoneEmailAccount")
	}
	// TODO: 密码校验
	// if !utils.ComparePassword(user.Password, req.Password) {
	// 	return "", nil, errors.New("password error")
	// }
	if user.Password != req.Password {
		return nil, PasswordError
	}
	if *user.Status != model.StatusEnable {
		return nil, UserDisableError
	}
	tenantID := ""
	if user.TenantKey != nil {
		tenantID = *user.TenantKey
	}
	token, err := s.jwt.GenToken(user.ID, tenantID)
	if err != nil {
		return nil, errors.Wrap(err, "jwt.GenToken")
	}
	// 登录成功后写入登录信息
	// 异步写入登录信息
	go func() {
		if err := s.WriteLoginInfo(ctx, user.ID, req.IP); err != nil {
			s.logger.Error("WriteLoginInfo", zap.Error(err))
		}
	}()
	return &v1.LoginResp{
		Token: token,
	}, nil
}

// WriteLoginInfo 登录成功后写入登录信息
func (s *loginService) WriteLoginInfo(ctx context.Context, id int64, ip string) error {
	return s.loginRepository.LoginSuccess(ctx, id, ip)
}

// GetUserInfo 获取登录信息
func (s *loginService) GetUserInfo(ctx context.Context, userID int64) (user *v1.LoginUser, err error) {
	return s.loginRepository.GetUserInfo(ctx, userID)
}
