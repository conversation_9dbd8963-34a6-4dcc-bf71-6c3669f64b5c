package service

import (
	"admin/pkg/jwt"
	"admin/pkg/log"
	"admin/pkg/sid"

	"github.com/redis/go-redis/v9"
)

type Service struct {
	logger *log.Logger
	sid    *sid.Sid
	jwt    *jwt.JWT
	redis  *redis.Client
}

func NewService(
	logger *log.Logger,
	sid *sid.Sid,
	jwt *jwt.JWT,
	redis *redis.Client,
) *Service {
	return &Service{
		logger: logger,
		sid:    sid,
		jwt:    jwt,
		redis:  redis,
	}
}
