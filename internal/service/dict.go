package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"

	"github.com/pkg/errors"
)

// DictService Dict 字典表服务接口
type DictService interface {
	Create(ctx context.Context, req *v1.DictCreateReq) error
	QueryById(ctx context.Context, req *v1.ID) (*v1.DictResp, error)
	QueryPage(ctx context.Context, req *v1.DictPageReq) (*v1.Page[*v1.DictResp], error)
	Update(ctx context.Context, req *v1.DictUpdateReq) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	Query(ctx context.Context) ([]*model.Dict, error)

	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error
	Enable(ctx context.Context, req *v1.IDS[string]) error
	Disable(ctx context.Context, req *v1.IDS[string]) error

	QueryTree(ctx context.Context) ([]*v1.DictResp, error)
}

// dictService Dict 字典表服务实现
type dictService struct {
	*Service
	dictRepository repository.DictRepository
}

// NewDictService 创建Dict 字典表服务
func NewDictService(
	service *Service,
	dictRepository repository.DictRepository,
) DictService {
	return &dictService{
		Service:        service,
		dictRepository: dictRepository,
	}
}

// Create 创建Dict 字典表
func (s *dictService) Create(ctx context.Context, req *v1.DictCreateReq) error {
	// 请求参数已在Controller层通过binding标签验证
	entity := &model.Dict{
		Sort:       req.Sort,
		Name:       req.Name,
		Code:       req.Code,
		ParentCode: req.ParentCode,
		Status:     req.Status,
	}

	err := s.dictRepository.Create(ctx, entity)
	if err != nil {
		return v1.ErrCreateFailed
	}

	return nil
}

// QueryById 根据ID查询Dict 字典表
func (s *dictService) QueryById(ctx context.Context, req *v1.ID) (*v1.DictResp, error) {
	entity, err := s.dictRepository.QueryById(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	if entity == nil {
		return nil, v1.ErrNotFound
	}

	return s.modelToResp(entity), nil
}

// QueryPage 分页查询Dict 字典表
func (s *dictService) QueryPage(ctx context.Context, req *v1.DictPageReq) (*v1.Page[*v1.DictResp], error) {
	result, err := s.dictRepository.QueryPage(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	// 转换为响应结构
	respList := s.modelListToRespList(result.List)
	page := &v1.Page[*v1.DictResp]{
		Current:  result.Current,
		PageSize: result.PageSize,
		Total:    result.Total,
		List:     respList,
	}

	return page, nil
}

// Update 更新Dict 字典表
func (s *dictService) Update(ctx context.Context, req *v1.DictUpdateReq) error {
	// 请求参数已在Controller层通过binding标签验证

	// 检查记录是否存在
	existing, err := s.dictRepository.QueryById(ctx, &v1.ID{ID: req.ID})
	if err != nil {
		return v1.ErrQueryFailed
	}
	if existing == nil {
		return v1.ErrNotFound
	}

	// 只更新非系统字段
	entity := &model.Dict{
		UUID: req.ID, // 设置UUID用于查找记录

		Sort: req.Sort,

		Name: req.Name,

		Code: req.Code,

		ParentID: req.ParentID,

		ParentCode: req.ParentCode,

		Status: req.Status,
	}

	err = s.dictRepository.Update(ctx, entity)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	return nil
}

// Delete 删除Dict 字典表
func (s *dictService) Delete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("删除ID列表不能为空")
	}

	err := s.dictRepository.Delete(ctx, req)
	if err != nil {
		return v1.ErrDeleteFailed
	}

	return nil
}

// Query 查询所有Dict 字典表
func (s *dictService) Query(ctx context.Context) ([]*model.Dict, error) {
	entities, err := s.dictRepository.Query(ctx)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	return entities, nil
}

// UpdateStatus 更新Dict 字典表状态
func (s *dictService) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	err := s.dictRepository.UpdateStatus(ctx, req, status)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	return nil
}

// Enable 启用Dict 字典表
func (s *dictService) Enable(ctx context.Context, req *v1.IDS[string]) error {
	return s.UpdateStatus(ctx, req, "enabled")
}

// Disable 禁用Dict 字典表
func (s *dictService) Disable(ctx context.Context, req *v1.IDS[string]) error {
	return s.UpdateStatus(ctx, req, "disabled")
}

// QueryTree 查询Dict 字典表树形结构
func (s *dictService) QueryTree(ctx context.Context) ([]*v1.DictResp, error) {
	list, err := s.dictRepository.Query(ctx)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	tree := (&model.Dict{}).ToTree(list)
	return s.modelListToRespList(tree), nil
}

// modelToResp 将模型转换为响应结构体
func (s *dictService) modelToResp(entity *model.Dict) *v1.DictResp {
	if entity == nil {
		return nil
	}

	resp := &v1.DictResp{
		ID:        entity.ID,
		UUID:      entity.UUID,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,

		Sort: entity.Sort,

		Name: entity.Name,

		Code: entity.Code,

		ParentID: entity.ParentID,

		ParentCode: entity.ParentCode,

		Status: entity.Status,

		// Children 字段在树形查询时处理

	}

	// 处理子节点
	if len(entity.Children) > 0 {
		resp.Children = s.modelListToRespList(entity.Children)
	}

	return resp
}

// modelListToRespList 将模型列表转换为响应列表
func (s *dictService) modelListToRespList(entities []*model.Dict) []*v1.DictResp {
	if len(entities) == 0 {
		return nil
	}

	respList := make([]*v1.DictResp, len(entities))
	for i, entity := range entities {
		respList[i] = s.modelToResp(entity)
	}
	return respList
}
