package service

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"

	"go.uber.org/zap"
)

type DeptService interface {
	QueryById(ctx context.Context, id *v1.ID) (*model.Dept, error)
	Create(ctx context.Context, user *v1.Dept) error
	QueryPage(ctx context.Context, user *v1.DeptPage) (*v1.Page[*model.Dept], error)
	Delete(ctx context.Context, ids *v1.IDS[string]) error
	Update(ctx context.Context, user *v1.Dept) error
	Query(ctx context.Context) ([]*model.Dept, error)
	Enable(ctx context.Context, ids *v1.IDS[string]) error
	Disable(ctx context.Context, ids *v1.IDS[string]) error
}

func NewDeptService(
	service *Service,
	deptRepository repository.DeptRepository,
) DeptService {
	return &deptService{
		Service:        service,
		deptRepository: deptRepository,
	}
}

type deptService struct {
	*Service
	deptRepository repository.DeptRepository
}

func (s *deptService) QueryById(ctx context.Context, id *v1.ID) (*model.Dept, error) {
	return s.deptRepository.QueryById(ctx, id)
}

func (s *deptService) Create(ctx context.Context, Dept *v1.Dept) error {
	return s.deptRepository.Update(ctx, &model.Dept{
		Name:      Dept.Name,
		ParentID:  &Dept.ParentID,
		ManagerID: &Dept.ManagerID,
		Status:    &Dept.Status,
		Remark:    &Dept.Remark,
		Sort:      Dept.Sort,
	})
}

func (s *deptService) QueryPage(ctx context.Context, dept *v1.DeptPage) (*v1.Page[*model.Dept], error) {
	return s.deptRepository.QueryPage(ctx, dept)
}

func (s *deptService) Delete(ctx context.Context, ids *v1.IDS[string]) error {
	s.logger.Info("删除组织", zap.Strings("ids", ids.Ids))
	return s.deptRepository.Delete(ctx, ids)
}

// Update 更新用户
func (s *deptService) Update(ctx context.Context, Dept *v1.Dept) error {
	return s.deptRepository.Update(ctx, &model.Dept{
		UUID:      Dept.ID,
		Name:      Dept.Name,
		ParentID:  &Dept.ParentID,
		ManagerID: &Dept.ManagerID,
		Status:    &Dept.Status,
		Remark:    &Dept.Remark,
		Sort:      Dept.Sort,
	})
}

// Disable 启用岗位
func (s *deptService) Enable(ctx context.Context, ids *v1.IDS[string]) error {
	return s.deptRepository.UpdateStatus(ctx, ids, "enable")
}

// Disable 禁用岗位
func (s *deptService) Disable(ctx context.Context, ids *v1.IDS[string]) error {
	return s.deptRepository.UpdateStatus(ctx, ids, "disable")
}

func (s *deptService) Query(ctx context.Context) ([]*model.Dept, error) {
	return s.deptRepository.Query(ctx)
}
