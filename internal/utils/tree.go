package utils

import "encoding/json"

type TreeNode[T any, IDType comparable] struct {
	ID       IDType                 `json:"id"`
	Data     T                      `json:"data"` // 使用更清晰的字段名
	ParentID IDType                 `json:"parent_id"`
	Children []*TreeNode[T, IDType] `json:"children"`
	Level    int                    `json:"level"`
	Path     []string               `json:"path"`
}

func BuildTree[T any, IDType comparable](
	nodes []T,
	idFn func(T) IDType,
	parentFn func(T) IDType,
	pathToString func(IDType) string,
) ([]*TreeNode[T, IDType], func()) {
	nodeMap := make(map[IDType]*TreeNode[T, IDType])
	var roots []*TreeNode[T, IDType]

	// 第一步：将所有节点存入map
	for _, node := range nodes {
		id := idFn(node)
		nodeMap[id] = &TreeNode[T, IDType]{
			ID:   id,
			Data: node,
		}
	}

	// 第二步：建立父子关系
	for _, rawNode := range nodes {
		nodeID := idFn(rawNode)
		parentID := parentFn(rawNode)
		currentNode := nodeMap[nodeID]

		// 判断是否为根节点
		var zeroID IDType
		if parentID == zeroID {
			roots = append(roots, currentNode)
			continue
		}

		parentNode, exists := nodeMap[parentID]
		if !exists {
			// 如果父节点不存在，当前节点作为根节点
			roots = append(roots, currentNode)
		} else {
			parentNode.Children = append(parentNode.Children, currentNode)
		}
	}

	// 第三步：设置层级和路径
	for _, root := range roots {
		setLevelAndPath(root, 0, pathToString, []string{})
	}

	return roots, func() {}
}

// 辅助函数：递归设置层级和路径
func setLevelAndPath[T any, IDType comparable](
	node *TreeNode[T, IDType],
	level int,
	pathToString func(IDType) string,
	path []string,
) {
	node.Level = level
	node.Path = append([]string(nil), path...) // 拷贝路径避免引用污染
	newPath := append(path, pathToString(node.ID))
	for _, child := range node.Children {
		setLevelAndPath(child, level+1, pathToString, newPath)
	}
}

// 便捷方法：获取原始数据
func (tn *TreeNode[T, IDType]) GetData() T {
	return tn.Data
}

// 便捷方法：检查是否为叶子节点
func (tn *TreeNode[T, IDType]) IsLeaf() bool {
	return len(tn.Children) == 0
}

// 便捷方法：检查是否为根节点
func (tn *TreeNode[T, IDType]) IsRoot() bool {
	return tn.Level == 0
}

// 便捷方法：获取子节点数量
func (tn *TreeNode[T, IDType]) ChildCount() int {
	return len(tn.Children)
}

// FlatNode 用于扁平化输出，包含树形信息和原始数据的所有字段
type FlatNode[T any, IDType comparable] struct {
	TreeInfo TreeInfo[IDType] `json:"tree_info"`
	Data     T                `json:"data"`
}

// TreeInfo 包含树形结构的元信息
type TreeInfo[IDType comparable] struct {
	ID         IDType   `json:"id"`
	ParentID   IDType   `json:"parent_id"`
	Level      int      `json:"level"`
	Path       []string `json:"path"`
	IsLeaf     bool     `json:"is_leaf"`
	IsRoot     bool     `json:"is_root"`
	ChildCount int      `json:"child_count"`
}

// ToFlat 将树形结构转换为扁平化结构
func (tn *TreeNode[T, IDType]) ToFlat() FlatNode[T, IDType] {
	return FlatNode[T, IDType]{
		TreeInfo: TreeInfo[IDType]{
			ID:         tn.ID,
			ParentID:   tn.ParentID,
			Level:      tn.Level,
			Path:       tn.Path,
			IsLeaf:     tn.IsLeaf(),
			IsRoot:     tn.IsRoot(),
			ChildCount: tn.ChildCount(),
		},
		Data: tn.Data,
	}
}

// FlattenTree 将整棵树转换为扁平化数组
func FlattenTree[T any, IDType comparable](roots []*TreeNode[T, IDType]) []FlatNode[T, IDType] {
	var result []FlatNode[T, IDType]

	var traverse func(*TreeNode[T, IDType])
	traverse = func(node *TreeNode[T, IDType]) {
		result = append(result, node.ToFlat())
		for _, child := range node.Children {
			traverse(child)
		}
	}

	for _, root := range roots {
		traverse(root)
	}

	return result
}

// MarshalJSON 自定义JSON序列化，将Data字段的内容展开到顶层
func (tn *TreeNode[T, IDType]) MarshalJSON() ([]byte, error) {
	// 首先序列化原始数据
	dataBytes, err := json.Marshal(tn.Data)
	if err != nil {
		return nil, err
	}

	// 将数据解析为map
	var dataMap map[string]interface{}
	if err := json.Unmarshal(dataBytes, &dataMap); err != nil {
		return nil, err
	}

	// 添加树形结构的字段
	dataMap["id"] = tn.ID
	dataMap["parent_id"] = tn.ParentID
	dataMap["level"] = tn.Level
	dataMap["path"] = tn.Path
	dataMap["children"] = tn.Children
	dataMap["is_leaf"] = tn.IsLeaf()
	dataMap["is_root"] = tn.IsRoot()
	dataMap["child_count"] = tn.ChildCount()

	return json.Marshal(dataMap)
}

// BuildTreeWithFlatJSON 构建树形结构并支持扁平化JSON输出
func BuildTreeWithFlatJSON[T any, IDType comparable](
	nodes []T,
	idFn func(T) IDType,
	parentFn func(T) IDType,
	pathToString func(IDType) string,
) ([]*TreeNode[T, IDType], func()) {
	return BuildTree(nodes, idFn, parentFn, pathToString)
}
