package utils

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

// TaskID 任务唯一标识
type TaskID string

// TaskError 存储任务执行的错误信息
type TaskError struct {
	ID      TaskID    `json:"id"`      // 任务ID
	Time    time.Time `json:"time"`    // 错误发生时间
	Message string    `json:"message"` // 错误消息
}

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"   // 等待中
	TaskStatusRunning   TaskStatus = "running"   // 运行中
	TaskStatusCompleted TaskStatus = "completed" // 已完成
	TaskStatusFailed    TaskStatus = "failed"    // 失败
	TaskStatusCancelled TaskStatus = "cancelled" // 已取消
)

// TaskStats 任务统计
type TaskStats struct {
	TotalSubmitted int64         `json:"total_submitted"` // 提交总数
	TotalCompleted int64         `json:"total_completed"` // 完成总数
	TotalFailed    int64         `json:"total_failed"`    // 失败总数
	TotalCancelled int64         `json:"total_cancelled"` // 取消总数
	AverageTime    time.Duration `json:"average_time"`    // 平均执行时间
	MinTime        time.Duration `json:"min_time"`        // 最小执行时间
	MaxTime        time.Duration `json:"max_time"`        // 最大执行时间
	LastUpdated    time.Time     `json:"last_updated"`    // 最后更新时间
}

// WorkerPoolConfig 工作池配置
type WorkerPoolConfig struct {
	Capacity         int           `json:"capacity"`           // 最大工作协程数
	InitialWorkers   int           `json:"initial_workers"`    // 初始工作协程数
	TaskQueueSize    int           `json:"task_queue_size"`    // 任务队列大小
	MaxErrorHistory  int           `json:"max_error_history"`  // 最大错误历史记录数
	WorkerIdleTime   time.Duration `json:"worker_idle_time"`   // 工作协程空闲超时时间
	StatsCleanupTime time.Duration `json:"stats_cleanup_time"` // 统计清理时间间隔
	EnableMetrics    bool          `json:"enable_metrics"`     // 是否启用详细指标
}

// DefaultConfig 返回默认配置
func DefaultConfig() *WorkerPoolConfig {
	return &WorkerPoolConfig{
		Capacity:         runtime.NumCPU() * 100, // 基于CPU核心数
		InitialWorkers:   runtime.NumCPU(),       // 初始为CPU核心数
		TaskQueueSize:    1000,                   // 适中的队列大小
		MaxErrorHistory:  1000,                   // 限制错误历史
		WorkerIdleTime:   30 * time.Second,       // 30秒空闲超时
		StatsCleanupTime: 5 * time.Minute,        // 5分钟清理一次
		EnableMetrics:    true,                   // 默认启用指标
	}
}

// WorkerPool 改进的协程池
type WorkerPool struct {
	config *WorkerPoolConfig

	// 核心状态
	active     int64 // 活跃工作协程数
	pending    int64 // 等待处理的任务数
	tasks      chan *task
	closeChan  chan struct{}
	isReleased atomic.Bool

	// 错误管理
	errors      []TaskError
	errorsMutex sync.RWMutex

	// 任务管理
	waitGroup sync.WaitGroup
	taskMap   sync.Map // TaskID -> *task

	// 统计信息 - 使用互斥锁保护
	stats      TaskStats
	statsMutex sync.RWMutex

	// 监控和清理
	cleanupTicker *time.Ticker
	cleanupDone   chan struct{}

	// 性能指标
	metrics struct {
		sync.RWMutex
		taskDurations []time.Duration
		lastCleanup   time.Time
	}
}

// task 表示一个任务
type task struct {
	id       TaskID
	fn       func(context.Context) error
	ctx      context.Context
	cancel   context.CancelFunc // 用于取消任务
	status   atomic.Value       // TaskStatus - 原子操作
	createAt time.Time
	startAt  time.Time
	endAt    time.Time
}

// getStatus 原子获取任务状态
func (t *task) getStatus() TaskStatus {
	if status := t.status.Load(); status != nil {
		return status.(TaskStatus)
	}
	return TaskStatusPending
}

// setStatus 原子设置任务状态
func (t *task) setStatus(status TaskStatus) {
	t.status.Store(status)
}

var (
	defaultPool     *WorkerPool
	defaultPoolOnce sync.Once
)

// NewWorkerPool 创建一个新的协程池
func NewWorkerPool(config *WorkerPoolConfig) (*WorkerPool, error) {
	if config == nil {
		config = DefaultConfig()
	}

	if config.Capacity <= 0 {
		return nil, fmt.Errorf("工作池容量必须大于0")
	}

	if config.InitialWorkers > config.Capacity {
		config.InitialWorkers = config.Capacity
	}

	if config.TaskQueueSize <= 0 {
		config.TaskQueueSize = config.Capacity * 2
	}

	pool := &WorkerPool{
		config:        config,
		tasks:         make(chan *task, config.TaskQueueSize),
		closeChan:     make(chan struct{}),
		cleanupDone:   make(chan struct{}),
		cleanupTicker: time.NewTicker(config.StatsCleanupTime),
	}

	// 初始化统计信息
	pool.stats = TaskStats{
		LastUpdated: time.Now(),
		MinTime:     time.Duration(^uint64(0) >> 1), // 最大值
	}

	// 启动清理协程
	go pool.cleanupRoutine()

	// 预启动初始工作协程
	for i := 0; i < config.InitialWorkers; i++ {
		pool.newWorker()
	}

	return pool, nil
}

// GetDefaultPool 获取默认的协程池实例（单例模式）
func GetDefaultPool() *WorkerPool {
	defaultPoolOnce.Do(func() {
		var err error
		defaultPool, err = NewWorkerPool(DefaultConfig())
		if err != nil {
			panic(fmt.Sprintf("创建默认工作池失败: %v", err))
		}
	})
	return defaultPool
}

// cleanupRoutine 定期清理协程
func (p *WorkerPool) cleanupRoutine() {
	defer close(p.cleanupDone)

	for {
		select {
		case <-p.cleanupTicker.C:
			p.performCleanup()
		case <-p.closeChan:
			return
		}
	}
}

// performCleanup 执行清理操作
func (p *WorkerPool) performCleanup() {
	// 清理过期的性能指标
	if p.config.EnableMetrics {
		p.metrics.Lock()
		now := time.Now()
		if now.Sub(p.metrics.lastCleanup) > p.config.StatsCleanupTime {
			// 只保留最近的1000个样本
			if len(p.metrics.taskDurations) > 1000 {
				p.metrics.taskDurations = p.metrics.taskDurations[len(p.metrics.taskDurations)-1000:]
			}
			p.metrics.lastCleanup = now
		}
		p.metrics.Unlock()
	}

	// 清理过期的错误记录
	p.errorsMutex.Lock()
	if len(p.errors) > p.config.MaxErrorHistory {
		// 保留最新的错误记录
		p.errors = p.errors[len(p.errors)-p.config.MaxErrorHistory:]
	}
	p.errorsMutex.Unlock()
}

// newWorker 创建新的工作协程
func (p *WorkerPool) newWorker() {
	atomic.AddInt64(&p.active, 1)

	go func() {
		defer atomic.AddInt64(&p.active, -1)

		idleTimer := time.NewTimer(p.config.WorkerIdleTime)
		defer idleTimer.Stop()

		for {
			select {
			case t, ok := <-p.tasks:
				if !ok {
					return // 通道已关闭
				}

				// 重置空闲计时器
				if !idleTimer.Stop() {
					select {
					case <-idleTimer.C:
					default:
					}
				}
				idleTimer.Reset(p.config.WorkerIdleTime)

				p.executeTask(t)

			case <-idleTimer.C:
				// 空闲超时，检查是否应该退出
				currentActive := atomic.LoadInt64(&p.active)
				if currentActive > int64(p.config.InitialWorkers) && len(p.tasks) == 0 {
					return // 退出多余的工作协程
				}
				idleTimer.Reset(p.config.WorkerIdleTime)

			case <-p.closeChan:
				return // 池已关闭
			}
		}
	}()
}

// executeTask 执行任务
func (p *WorkerPool) executeTask(t *task) {
	defer p.waitGroup.Done()
	defer atomic.AddInt64(&p.pending, -1)

	// 检查任务是否已被取消
	if t.getStatus() == TaskStatusCancelled {
		p.taskMap.Delete(t.id)
		return
	}

	// 更新任务状态
	t.setStatus(TaskStatusRunning)
	t.startAt = time.Now()

	var err error
	var duration time.Duration

	// 使用defer确保清理
	defer func() {
		t.endAt = time.Now()
		duration = t.endAt.Sub(t.startAt)

		// 更新统计信息
		status := t.getStatus()
		p.updateStats(duration, status)

		// 从任务映射中移除
		p.taskMap.Delete(t.id)

		// 记录性能指标
		if p.config.EnableMetrics {
			p.recordMetrics(duration)
		}
	}()

	// 处理panic
	defer func() {
		if r := recover(); r != nil {
			t.setStatus(TaskStatusFailed)
			err = fmt.Errorf("任务panic: %v", r)
			p.recordError(t.id, err)
		}
	}()

	// 执行任务
	err = t.fn(t.ctx)
	if err != nil {
		t.setStatus(TaskStatusFailed)
		p.recordError(t.id, err)
		return
	}

	t.setStatus(TaskStatusCompleted)
}

// updateStats 线程安全地更新统计信息
func (p *WorkerPool) updateStats(duration time.Duration, status TaskStatus) {
	p.statsMutex.Lock()
	defer p.statsMutex.Unlock()

	switch status {
	case TaskStatusCompleted:
		p.stats.TotalCompleted++
	case TaskStatusFailed:
		p.stats.TotalFailed++
	case TaskStatusCancelled:
		p.stats.TotalCancelled++
	}

	// 更新时间统计
	if status == TaskStatusCompleted || status == TaskStatusFailed {
		totalTasks := p.stats.TotalCompleted + p.stats.TotalFailed
		if totalTasks == 1 {
			p.stats.AverageTime = duration
			p.stats.MinTime = duration
			p.stats.MaxTime = duration
		} else {
			// 计算新的平均时间
			totalTime := p.stats.AverageTime.Nanoseconds() * (totalTasks - 1)
			p.stats.AverageTime = time.Duration((totalTime + duration.Nanoseconds()) / totalTasks)

			// 更新最小/最大时间
			if duration < p.stats.MinTime {
				p.stats.MinTime = duration
			}
			if duration > p.stats.MaxTime {
				p.stats.MaxTime = duration
			}
		}
	}

	p.stats.LastUpdated = time.Now()
}

// recordMetrics 记录性能指标
func (p *WorkerPool) recordMetrics(duration time.Duration) {
	p.metrics.Lock()
	defer p.metrics.Unlock()

	p.metrics.taskDurations = append(p.metrics.taskDurations, duration)

	// 限制指标数量，防止内存泄漏
	if len(p.metrics.taskDurations) > 10000 {
		p.metrics.taskDurations = p.metrics.taskDurations[1000:]
	}
}

// recordError 记录错误信息（线程安全）
func (p *WorkerPool) recordError(id TaskID, err error) {
	p.errorsMutex.Lock()
	defer p.errorsMutex.Unlock()

	p.errors = append(p.errors, TaskError{
		ID:      id,
		Time:    time.Now(),
		Message: err.Error(),
	})

	// 实时限制错误历史大小
	if len(p.errors) > p.config.MaxErrorHistory {
		p.errors = p.errors[len(p.errors)-p.config.MaxErrorHistory:]
	}
}

// generateTaskID 生成唯一的任务ID
func generateTaskID() TaskID {
	return TaskID(uuid.New().String())
}

// Submit 提交一个任务到工作池
func (p *WorkerPool) Submit(fn func(context.Context) error) (TaskID, error) {
	return p.SubmitWithContext(context.Background(), fn)
}

// SubmitWithContext 提交带上下文的任务
func (p *WorkerPool) SubmitWithContext(ctx context.Context, fn func(context.Context) error) (TaskID, error) {
	if p.isReleased.Load() {
		return "", fmt.Errorf("工作池已关闭")
	}

	// 生成唯一任务ID
	taskID := generateTaskID()

	// 创建可取消的上下文
	taskCtx, cancel := context.WithCancel(ctx)

	// 创建任务
	t := &task{
		id:       taskID,
		fn:       fn,
		ctx:      taskCtx,
		cancel:   cancel,
		createAt: time.Now(),
	}
	t.setStatus(TaskStatusPending)

	// 存储任务到映射
	p.taskMap.Store(taskID, t)

	// 更新提交统计
	p.statsMutex.Lock()
	p.stats.TotalSubmitted++
	p.statsMutex.Unlock()

	// 增加计数器
	atomic.AddInt64(&p.pending, 1)
	p.waitGroup.Add(1)

	// 尝试提交任务
	select {
	case p.tasks <- t:
		// 动态扩展工作协程
		p.tryExpandWorkers()
		return taskID, nil

	default:
		// 队列已满，尝试创建新的工作协程
		if atomic.LoadInt64(&p.active) < int64(p.config.Capacity) {
			p.newWorker()
			select {
			case p.tasks <- t:
				return taskID, nil
			default:
				// 仍然无法提交
			}
		}

		// 清理并返回错误
		atomic.AddInt64(&p.pending, -1)
		p.waitGroup.Done()
		p.taskMap.Delete(taskID)
		cancel()

		return "", fmt.Errorf("工作池队列已满，当前活跃协程: %d, 队列长度: %d",
			atomic.LoadInt64(&p.active), len(p.tasks))
	}
}

// tryExpandWorkers 尝试扩展工作协程
func (p *WorkerPool) tryExpandWorkers() {
	currentActive := atomic.LoadInt64(&p.active)
	queueLength := len(p.tasks)

	// 如果队列长度超过阈值且还有容量，则创建新的工作协程
	threshold := p.config.Capacity / 8
	if queueLength > threshold && currentActive < int64(p.config.Capacity) {
		p.newWorker()
	}
}

// CancelTask 取消指定的任务
func (p *WorkerPool) CancelTask(id TaskID) bool {
	if val, ok := p.taskMap.Load(id); ok {
		if t, ok := val.(*task); ok {
			t.setStatus(TaskStatusCancelled)
			t.cancel()
			return true
		}
	}
	return false
}

// GetTaskStatus 获取任务状态
func (p *WorkerPool) GetTaskStatus(id TaskID) (TaskStatus, bool) {
	if val, ok := p.taskMap.Load(id); ok {
		if t, ok := val.(*task); ok {
			return t.getStatus(), true
		}
	}
	return "", false
}

// GetTaskInfo 获取任务详细信息
func (p *WorkerPool) GetTaskInfo(id TaskID) (map[string]interface{}, bool) {
	if val, ok := p.taskMap.Load(id); ok {
		if t, ok := val.(*task); ok {
			info := map[string]interface{}{
				"id":        t.id,
				"status":    t.getStatus(),
				"create_at": t.createAt,
				"start_at":  t.startAt,
				"end_at":    t.endAt,
			}
			if !t.startAt.IsZero() && !t.endAt.IsZero() {
				info["duration"] = t.endAt.Sub(t.startAt)
			}
			return info, true
		}
	}
	return nil, false
}

// Wait 等待所有任务完成
func (p *WorkerPool) Wait() {
	p.waitGroup.Wait()
}

// WaitWithTimeout 带超时的等待
func (p *WorkerPool) WaitWithTimeout(timeout time.Duration) bool {
	done := make(chan struct{})
	go func() {
		defer close(done)
		p.waitGroup.Wait()
	}()

	select {
	case <-done:
		return true // 所有任务完成
	case <-time.After(timeout):
		return false // 超时
	}
}

// GetErrors 获取所有错误信息
func (p *WorkerPool) GetErrors() []TaskError {
	p.errorsMutex.RLock()
	defer p.errorsMutex.RUnlock()

	result := make([]TaskError, len(p.errors))
	copy(result, p.errors)
	return result
}

// ClearErrors 清除所有错误信息
func (p *WorkerPool) ClearErrors() {
	p.errorsMutex.Lock()
	defer p.errorsMutex.Unlock()
	p.errors = p.errors[:0] // 保留底层数组
}

// GetStats 获取任务统计信息
func (p *WorkerPool) GetStats() TaskStats {
	p.statsMutex.RLock()
	defer p.statsMutex.RUnlock()
	return p.stats
}

// GetMetrics 获取性能指标
func (p *WorkerPool) GetMetrics() map[string]interface{} {
	if !p.config.EnableMetrics {
		return map[string]interface{}{"enabled": false}
	}

	p.metrics.RLock()
	defer p.metrics.RUnlock()

	metrics := map[string]interface{}{
		"enabled":        true,
		"sample_count":   len(p.metrics.taskDurations),
		"last_cleanup":   p.metrics.lastCleanup,
		"active_workers": atomic.LoadInt64(&p.active),
		"pending_tasks":  atomic.LoadInt64(&p.pending),
		"queue_length":   len(p.tasks),
		"queue_capacity": cap(p.tasks),
	}

	// 计算百分位数
	if len(p.metrics.taskDurations) > 0 {
		durations := make([]time.Duration, len(p.metrics.taskDurations))
		copy(durations, p.metrics.taskDurations)

		// 简单排序计算百分位数
		for i := 0; i < len(durations)-1; i++ {
			for j := i + 1; j < len(durations); j++ {
				if durations[i] > durations[j] {
					durations[i], durations[j] = durations[j], durations[i]
				}
			}
		}

		metrics["p50"] = durations[len(durations)*50/100]
		metrics["p90"] = durations[len(durations)*90/100]
		metrics["p99"] = durations[len(durations)*99/100]
	}

	return metrics
}

// Running 返回当前正在运行的协程数量
func (p *WorkerPool) Running() int64 {
	return atomic.LoadInt64(&p.active)
}

// Cap 返回协程池的容量
func (p *WorkerPool) Cap() int {
	return p.config.Capacity
}

// Free 返回协程池中可用的协程数量
func (p *WorkerPool) Free() int {
	return p.config.Capacity - int(atomic.LoadInt64(&p.active))
}

// Waiting 返回等待执行的任务数量
func (p *WorkerPool) Waiting() int {
	return len(p.tasks)
}

// Pending 返回待处理的任务总数
func (p *WorkerPool) Pending() int64 {
	return atomic.LoadInt64(&p.pending)
}

// GetConfig 获取配置信息
func (p *WorkerPool) GetConfig() *WorkerPoolConfig {
	return p.config
}

// Release 释放协程池资源
func (p *WorkerPool) Release() {
	if !p.isReleased.CompareAndSwap(false, true) {
		return // 已经释放过了
	}

	// 停止清理协程
	p.cleanupTicker.Stop()
	close(p.closeChan)

	// 取消所有待处理的任务
	p.taskMap.Range(func(key, value interface{}) bool {
		if t, ok := value.(*task); ok {
			t.setStatus(TaskStatusCancelled)
			t.cancel()
		}
		return true
	})

	// 关闭任务通道
	close(p.tasks)

	// 等待所有任务完成
	p.Wait()

	// 等待清理协程结束
	<-p.cleanupDone
}

// ReleaseTimeout 带超时的释放协程池资源
func (p *WorkerPool) ReleaseTimeout(timeout time.Duration) bool {
	if !p.isReleased.CompareAndSwap(false, true) {
		return true // 已经释放过了
	}

	// 停止清理协程
	p.cleanupTicker.Stop()
	close(p.closeChan)

	// 取消所有待处理的任务
	p.taskMap.Range(func(key, value interface{}) bool {
		if t, ok := value.(*task); ok {
			t.setStatus(TaskStatusCancelled)
			t.cancel()
		}
		return true
	})

	// 关闭任务通道
	close(p.tasks)

	// 带超时等待
	success := p.WaitWithTimeout(timeout)

	// 等待清理协程结束（带超时）
	select {
	case <-p.cleanupDone:
	case <-time.After(time.Second):
		// 清理协程超时，但不影响整体结果
	}

	return success
}

// IsReleased 检查工作池是否已释放
func (p *WorkerPool) IsReleased() bool {
	return p.isReleased.Load()
}

// Health 健康检查
func (p *WorkerPool) Health() map[string]interface{} {
	stats := p.GetStats()

	health := map[string]interface{}{
		"status":             "healthy",
		"active_workers":     p.Running(),
		"capacity":           p.Cap(),
		"pending_tasks":      p.Pending(),
		"queue_length":       p.Waiting(),
		"queue_utilization":  float64(p.Waiting()) / float64(p.config.TaskQueueSize),
		"worker_utilization": float64(p.Running()) / float64(p.config.Capacity),
		"total_submitted":    stats.TotalSubmitted,
		"total_completed":    stats.TotalCompleted,
		"total_failed":       stats.TotalFailed,
		"success_rate":       0.0,
		"is_released":        p.IsReleased(),
	}

	// 计算成功率
	if stats.TotalSubmitted > 0 {
		health["success_rate"] = float64(stats.TotalCompleted) / float64(stats.TotalSubmitted)
	}

	// 健康状态判断
	queueUtil := health["queue_utilization"].(float64)
	workerUtil := health["worker_utilization"].(float64)
	successRate := health["success_rate"].(float64)

	if p.IsReleased() {
		health["status"] = "released"
	} else if queueUtil > 0.9 || workerUtil > 0.95 {
		health["status"] = "overloaded"
	} else if successRate < 0.9 && stats.TotalSubmitted > 10 {
		health["status"] = "degraded"
	}

	return health
}
