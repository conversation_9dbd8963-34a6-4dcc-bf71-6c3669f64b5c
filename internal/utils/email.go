package utils

import (
	"admin/pkg/email"
)

// EmailConfig 类型别名，保持向后兼容
type EmailConfig = email.EmailConfig

// EmailMessage 类型别名，保持向后兼容
type EmailMessage = email.EmailMessage

// Priority 类型别名，保持向后兼容
type Priority = email.Priority

// 优先级常量
const (
	PriorityLow    = email.PriorityLow
	PriorityNormal = email.PriorityNormal
	PriorityHigh   = email.PriorityHigh
	PriorityUrgent = email.PriorityUrgent
)

// NewEmailClient 创建邮件客户端实例（兼容性包装）
func NewEmailClient(config *EmailConfig) (*email.EmailClient, error) {
	return email.NewEmailClient(config)
}
