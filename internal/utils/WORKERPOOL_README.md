# 改进版工作池 (Improved Worker Pool)

这是一个生产级别的Go协程池实现，解决了原版本的所有问题，并添加了许多企业级特性。

## 🚀 主要改进

### 1. 内存安全
- ✅ **错误列表限制**: 自动限制错误历史记录数量，防止内存泄漏
- ✅ **性能指标管理**: 定期清理性能指标，控制内存使用
- ✅ **任务映射清理**: 确保任务完成后从映射中移除
- ✅ **定期清理机制**: 后台协程定期清理过期数据

### 2. 并发安全
- ✅ **原子操作**: 所有计数器使用原子操作
- ✅ **线程安全统计**: 使用互斥锁保护统计信息更新
- ✅ **任务状态管理**: 原子操作管理任务状态
- ✅ **安全的资源释放**: 防止重复释放和竞态条件

### 3. 任务管理
- ✅ **唯一任务ID**: 使用UUID生成唯一任务标识
- ✅ **任务取消**: 支持取消正在执行或等待的任务
- ✅ **上下文支持**: 完整的context.Context支持
- ✅ **任务状态跟踪**: 详细的任务生命周期状态

### 4. 性能优化
- ✅ **动态扩缩容**: 根据负载自动调整工作协程数量
- ✅ **智能队列管理**: 优化的任务队列大小和处理逻辑
- ✅ **工作协程回收**: 空闲协程自动回收机制
- ✅ **性能指标收集**: 详细的性能统计和百分位数

### 5. 监控和诊断
- ✅ **健康检查**: 实时健康状态监控
- ✅ **详细统计**: 完整的任务执行统计
- ✅ **性能指标**: P50/P90/P99延迟统计
- ✅ **错误追踪**: 完整的错误历史记录

## 📋 API 文档

### 配置选项

```go
type WorkerPoolConfig struct {
    Capacity         int           // 最大工作协程数
    InitialWorkers   int           // 初始工作协程数
    TaskQueueSize    int           // 任务队列大小
    MaxErrorHistory  int           // 最大错误历史记录数
    WorkerIdleTime   time.Duration // 工作协程空闲超时时间
    StatsCleanupTime time.Duration // 统计清理时间间隔
    EnableMetrics    bool          // 是否启用详细指标
}
```

### 核心方法

#### 创建工作池
```go
// 使用默认配置
pool := GetDefaultPool()

// 使用自定义配置
config := DefaultConfig()
config.Capacity = 100
pool, err := NewWorkerPool(config)
```

#### 任务提交
```go
// 基本任务提交
taskID, err := pool.Submit(func(ctx context.Context) error {
    // 任务逻辑
    return nil
})

// 带上下文的任务提交
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()
taskID, err := pool.SubmitWithContext(ctx, func(ctx context.Context) error {
    // 任务逻辑
    return nil
})
```

#### 任务管理
```go
// 获取任务状态
status, exists := pool.GetTaskStatus(taskID)

// 获取任务详细信息
info, exists := pool.GetTaskInfo(taskID)

// 取消任务
success := pool.CancelTask(taskID)
```

#### 等待和同步
```go
// 等待所有任务完成
pool.Wait()

// 带超时的等待
success := pool.WaitWithTimeout(30 * time.Second)
```

#### 监控和统计
```go
// 获取统计信息
stats := pool.GetStats()

// 获取性能指标
metrics := pool.GetMetrics()

// 健康检查
health := pool.Health()

// 获取错误信息
errors := pool.GetErrors()
```

#### 资源管理
```go
// 优雅关闭
pool.Release()

// 带超时的关闭
success := pool.ReleaseTimeout(30 * time.Second)

// 检查是否已释放
if pool.IsReleased() {
    // 已释放
}
```

## 🔧 使用示例

### 基本使用
```go
pool, err := NewWorkerPool(DefaultConfig())
if err != nil {
    log.Fatal(err)
}
defer pool.Release()

taskID, err := pool.Submit(func(ctx context.Context) error {
    fmt.Println("执行任务")
    return nil
})

if err != nil {
    log.Printf("提交失败: %v", err)
    return
}

pool.Wait()
fmt.Printf("任务 %s 完成\n", taskID)
```

### 批量处理
```go
pool, err := NewWorkerPool(DefaultConfig())
if err != nil {
    log.Fatal(err)
}
defer pool.Release()

data := []int{1, 2, 3, 4, 5}
for _, item := range data {
    pool.Submit(func(ctx context.Context) error {
        result := item * item
        fmt.Printf("处理 %d = %d\n", item, result)
        return nil
    })
}

pool.Wait()
```

### 错误处理
```go
pool, err := NewWorkerPool(DefaultConfig())
if err != nil {
    log.Fatal(err)
}
defer pool.Release()

taskID, err := pool.Submit(func(ctx context.Context) error {
    return errors.New("模拟错误")
})

pool.Wait()

// 检查任务状态
if status, exists := pool.GetTaskStatus(taskID); exists {
    if status == TaskStatusFailed {
        errors := pool.GetErrors()
        for _, taskErr := range errors {
            if taskErr.ID == taskID {
                fmt.Printf("任务失败: %s\n", taskErr.Message)
            }
        }
    }
}
```

### 监控示例
```go
pool, err := NewWorkerPool(DefaultConfig())
if err != nil {
    log.Fatal(err)
}
defer pool.Release()

// 启动监控协程
go func() {
    ticker := time.NewTicker(5 * time.Second)
    defer ticker.Stop()
    
    for range ticker.C {
        health := pool.Health()
        stats := pool.GetStats()
        
        fmt.Printf("状态: %s, 活跃: %d, 完成: %d, 失败: %d\n",
            health["status"], 
            pool.Running(),
            stats.TotalCompleted,
            stats.TotalFailed)
    }
}()

// 提交任务...
```

## 📊 性能特性

### 内存使用
- 错误历史自动限制 (默认1000条)
- 性能指标定期清理 (默认5分钟)
- 任务映射及时清理
- 工作协程自动回收

### 并发性能
- 支持高并发任务提交
- 动态工作协程扩缩容
- 无锁的任务状态管理
- 优化的任务队列处理

### 监控指标
- 实时统计信息
- P50/P90/P99延迟
- 成功率和错误率
- 资源使用情况

## 🛡️ 生产环境建议

### 配置建议
```go
// 高并发场景
config := &WorkerPoolConfig{
    Capacity:         runtime.NumCPU() * 50,  // 基于CPU核心数
    InitialWorkers:   runtime.NumCPU() * 2,   // 初始协程数
    TaskQueueSize:    2000,                   // 较大的队列
    MaxErrorHistory:  1000,                   // 适中的错误历史
    WorkerIdleTime:   30 * time.Second,       // 30秒空闲超时
    StatsCleanupTime: 5 * time.Minute,        // 5分钟清理
    EnableMetrics:    true,                   // 启用监控
}

// 低延迟场景
config := &WorkerPoolConfig{
    Capacity:         runtime.NumCPU() * 10,
    InitialWorkers:   runtime.NumCPU() * 5,   // 更多初始协程
    TaskQueueSize:    500,                    // 较小的队列
    MaxErrorHistory:  500,
    WorkerIdleTime:   60 * time.Second,       // 更长的空闲时间
    StatsCleanupTime: 10 * time.Minute,
    EnableMetrics:    true,
}
```

### 监控集成
```go
// 集成到监控系统
func monitorWorkerPool(pool *WorkerPool) {
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        
        for range ticker.C {
            health := pool.Health()
            stats := pool.GetStats()
            metrics := pool.GetMetrics()
            
            // 发送到监控系统
            sendMetrics(map[string]interface{}{
                "worker_pool.active_workers":    pool.Running(),
                "worker_pool.pending_tasks":     pool.Pending(),
                "worker_pool.queue_length":      pool.Waiting(),
                "worker_pool.total_completed":   stats.TotalCompleted,
                "worker_pool.total_failed":      stats.TotalFailed,
                "worker_pool.success_rate":      health["success_rate"],
                "worker_pool.average_duration":  stats.AverageTime.Milliseconds(),
                "worker_pool.p99_duration":      metrics["p99"],
                "worker_pool.health_status":     health["status"],
            })
        }
    }()
}
```

### 优雅关闭
```go
// 在应用关闭时优雅释放资源
func gracefulShutdown(pool *WorkerPool) {
    // 监听关闭信号
    c := make(chan os.Signal, 1)
    signal.Notify(c, os.Interrupt, syscall.SIGTERM)
    
    go func() {
        <-c
        log.Println("开始优雅关闭工作池...")
        
        // 给30秒时间完成任务
        if pool.ReleaseTimeout(30 * time.Second) {
            log.Println("工作池优雅关闭完成")
        } else {
            log.Println("工作池关闭超时，强制退出")
        }
        
        os.Exit(0)
    }()
}
```

## 🧪 测试

运行测试：
```bash
go test -v ./internal/utils -run TestWorkerPool
```

运行基准测试：
```bash
go test -v ./internal/utils -bench=BenchmarkWorkerPool
```

## 📈 性能对比

| 特性 | 原版本 | 改进版本 |
|------|--------|----------|
| 内存安全 | ❌ 有泄漏风险 | ✅ 完全安全 |
| 并发安全 | ⚠️ 部分问题 | ✅ 完全安全 |
| 任务取消 | ❌ 不支持 | ✅ 完全支持 |
| 监控指标 | ⚠️ 基础统计 | ✅ 详细指标 |
| 错误处理 | ⚠️ 基础处理 | ✅ 完善处理 |
| 动态扩缩容 | ⚠️ 简单实现 | ✅ 智能实现 |
| 生产就绪 | ❌ 需要修复 | ✅ 完全就绪 |

## 🔄 迁移指南

从原版本迁移到改进版本：

1. **更新导入**：
   ```go
   // 原版本
   pool, _ := NewWorkerPool(1000)
   
   // 改进版本
   config := DefaultConfig()
   config.Capacity = 1000
   pool, _ := NewWorkerPool(config)
   ```

2. **任务提交**：
   ```go
   // API保持兼容
   taskID, err := pool.Submit(func(ctx context.Context) error {
       // 任务逻辑
       return nil
   })
   ```

3. **添加监控**：
   ```go
   // 新增的监控功能
   health := pool.Health()
   metrics := pool.GetMetrics()
   ```

4. **优雅关闭**：
   ```go
   // 推荐使用带超时的关闭
   pool.ReleaseTimeout(30 * time.Second)
   ```

## 📝 注意事项

1. **UUID依赖**: 需要添加 `github.com/google/uuid` 依赖
2. **内存使用**: 启用详细指标会增加内存使用，可通过配置控制
3. **协程数量**: 建议根据实际负载调整协程池大小
4. **监控频率**: 避免过于频繁的监控查询影响性能
5. **错误处理**: 建议定期清理错误历史或集成到日志系统

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工作池实现。

## 📄 许可证

与项目主许可证保持一致。