package utils

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
)

var trans ut.Translator

// InitValidator 初始化验证器并设置中文翻译
func InitValidator() error {
	// 获取gin的验证器
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册一个获取json tag的自定义方法
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			if name == "-" {
				return ""
			}
			return name
		})

		// 创建中文翻译器
		zhT := zh.New()
		uni := ut.New(zhT, zhT)
		var found bool
		trans, found = uni.GetTranslator("zh")
		if !found {
			return fmt.Errorf("未找到中文翻译器")
		}

		// 注册中文翻译
		if err := zh_translations.RegisterDefaultTranslations(v, trans); err != nil {
			return err
		}

		// 注册自定义翻译（这会覆盖一些默认翻译）
		registerCustomTranslations(v, trans)
	}
	return nil
}

// registerCustomTranslations 注册自定义翻译
func registerCustomTranslations(v *validator.Validate, trans ut.Translator) {
	// 注册自定义字段翻译
	translations := []struct {
		tag         string
		translation string
	}{
		{
			tag:         "required",
			translation: "{0}为必填字段",
		},
		{
			tag:         "min",
			translation: "{0}长度不能少于{1}个字符",
		},
		{
			tag:         "max",
			translation: "{0}长度不能超过{1}个字符",
		},
		{
			tag:         "len",
			translation: "{0}长度必须是{1}个字符",
		},
		{
			tag:         "gte",
			translation: "{0}必须大于或等于{1}",
		},
		{
			tag:         "lte",
			translation: "{0}必须小于或等于{1}",
		},
		{
			tag:         "gt",
			translation: "{0}必须大于{1}",
		},
		{
			tag:         "lt",
			translation: "{0}必须小于{1}",
		},
		{
			tag:         "alphanum",
			translation: "{0}只能包含字母和数字",
		},
		{
			tag:         "numeric",
			translation: "{0}必须是数字",
		},
		{
			tag:         "url",
			translation: "{0}必须是有效的URL",
		},
		{
			tag:         "uri",
			translation: "{0}必须是有效的URI",
		},
		{
			tag:         "oneof",
			translation: "{0}必须是以下值之一: {1}",
		},
	}

	for _, t := range translations {
		registerTranslation(v, trans, t.tag, t.translation)
	}

	// 注册自定义字段名称翻译
	fieldTranslations := map[string]string{
		"account":     "账号",
		"password":    "密码",
		"username":    "用户名",
		"phone":       "手机号",
		"email":       "邮箱",
		"currentPage": "当前页码",
		"pageSize":    "页面大小",
		"name":        "名称",
		"code":        "编码",
		"status":      "状态",
		"remark":      "备注",
		"sort":        "排序",
		"id":          "ID",
		"ids":         "ID列表",
		"parentId":    "父级ID",
		"type":        "类型",
		"title":       "标题",
		"content":     "内容",
		"sex":         "性别",
	}

	// 注册字段名称翻译
	for field, translation := range fieldTranslations {
		// 检查返回值，lint 友好
		_ = v.RegisterTranslation(field, trans, func(ut ut.Translator) error {
			return ut.Add(field, translation, true)
		}, func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T(field)
			return t
		})
	}
}

// registerTranslation 注册翻译
func registerTranslation(v *validator.Validate, trans ut.Translator, tag string, translation string) {
	// 检查返回值，lint 友好
	_ = v.RegisterTranslation(tag, trans, func(ut ut.Translator) error {
		return ut.Add(tag, translation, true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		// 获取字段的中文名称
		fieldName := fe.Field()
		if fieldTranslations := map[string]string{
			"account":     "账号",
			"password":    "密码",
			"username":    "用户名",
			"phone":       "手机号",
			"email":       "邮箱",
			"currentPage": "当前页码",
			"pageSize":    "页面大小",
			"name":        "名称",
			"code":        "编码",
			"status":      "状态",
			"remark":      "备注",
			"sort":        "排序",
			"id":          "ID",
			"ids":         "ID列表",
			"parentId":    "父级ID",
			"type":        "类型",
			"title":       "标题",
			"content":     "内容",
			"sex":         "性别",
		}; fieldTranslations[fieldName] != "" {
			fieldName = fieldTranslations[fieldName]
		}
		t, _ := ut.T(tag, fieldName, fe.Param())
		return t
	})
}

// TranslateValidationErrors 翻译验证错误为中文
func TranslateValidationErrors(err error) string {
	if trans == nil {
		return err.Error()
	}

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		var errMsgs []string
		for _, e := range validationErrors {
			// 特殊处理email验证错误
			if e.Tag() == "email" {
				fieldName := "邮箱"
				if e.Field() == "Email" {
					fieldName = "邮箱"
				}
				errMsgs = append(errMsgs, fieldName+"必须是一个有效的邮箱")
			} else {
				errMsgs = append(errMsgs, e.Translate(trans))
			}
		}
		return strings.Join(errMsgs, "; ")
	}
	return err.Error()
}

// IsValidationError 判断是否为验证错误
func IsValidationError(err error) bool {
	_, ok := err.(validator.ValidationErrors)
	return ok
}

// GetTranslator 获取翻译器
func GetTranslator() ut.Translator {
	return trans
}
