package utils

import (
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/gofrs/uuid"
	"github.com/yitter/idgenerator-go/idgen"
)

// ==================== 全局变量与常量 ====================

var (
	// seededRand 是一个以当前时间纳秒数作为种子的随机数生成器，以确保每次程序运行时都能产生不同的随机序列。
	// 使用 rand.New 创建的 *rand.Rand 不是并发安全的，因此我们配合 a sync.Mutex 来保证并发环境下的正确性。
	seededRand = rand.New(rand.NewSource(time.Now().UnixNano()))
	randLock   sync.Mutex

	// snowflakeOnce 确保雪花ID生成器的初始化逻辑（设置workerId等）在整个程序生命周期中只执行一次。
	snowflakeOnce sync.Once

	// chineseRunes 是预先转换好的中文字符 rune 切片，用于优化 GenerateChineseName 的性能，避免每次调用时都进行转换。
	chineseRunes = []rune("赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎鲁韦昌马苗凤花方俞任袁柳酆鲍史唐费廉岑薛雷贺倪汤滕殷罗毕郝邬安常乐于时傅皮卞齐康伍余元卜顾孟平黄和穆萧尹姚邵湛汪祁毛禹狄米贝明臧计伏成戴谈宋茅庞熊纪舒屈项祝董粱杜阮蓝闵席季麻强贾路娄危江童颜郭梅盛林刁钟徐邱骆高夏蔡田樊胡凌霍虞万支柯昝管卢莫经房裘缪干解应宗丁宣贲邓郁单杭洪包诸左石崔吉钮龚程嵇邢滑裴陆荣翁荀羊於惠甄曲家封芮羿储靳汲邴糜松井段富巫乌焦巴弓牧隗山谷车侯宓蓬全郗班仰秋仲伊宫宁仇栾暴甘钭厉戎祖武符刘景詹束龙叶幸司韶郜黎蓟薄印宿白怀蒲邰从鄂索咸籍赖卓蔺屠蒙池乔阴鬱胥能苍双闻莘党翟谭贡劳逄姬申扶堵冉宰郦雍卻璩桑桂濮牛寿通边扈燕冀郏浦尚农温别庄晏柴瞿阎充慕连茹习宦艾鱼容向古易慎戈廖庾终暨居衡步都耿满弘匡国文寇广禄阙东欧殳沃利蔚越夔隆师巩厍聂晁勾敖融冷訾辛阚那简饶空曾毋沙乜养鞠须丰巢关蒯相查后荆红游竺权逯盖益桓公晋楚闫法汝鄢涂钦归海帅缑亢况后有琴梁丘左丘商牟佘佴伯赏南宫墨哈谯笪年爱阳佟言福百家姓")
)

const (
	// letterBytes 定义了用于生成随机编码的字符集。
	letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
)

// ==================== 随机工具 ====================

// GenerateChineseName 随机生成一个指定长度的中文姓名。
// 性能优化：
// 1. 使用预先转换的 `chineseRunes` 切片，避免在函数调用时重复进行 string 到 []rune 的转换。
// 2. 使用带锁的、有时间种子的 `seededRand`，确保随机性并支持并发安全。
func GenerateChineseName(length int) string {
	if length <= 0 {
		return ""
	}
	name := make([]rune, length)

	randLock.Lock()
	for i := range name {
		name[i] = chineseRunes[seededRand.Intn(len(chineseRunes))]
	}
	randLock.Unlock()

	return string(name)
}

// GenerateRandomCode 随机生成一个指定长度的编码。
// 性能优化：使用带锁的、有时间种子的 `seededRand`，确保随机性并支持并发安全。
func GenerateRandomCode(length int) string {
	if length <= 0 {
		return ""
	}
	code := make([]byte, length)

	randLock.Lock()
	for i := range code {
		code[i] = letterBytes[seededRand.Intn(len(letterBytes))]
	}
	randLock.Unlock()

	return string(code)
}

// ==================== 唯一ID工具 ====================

// GetUUID 获取一个32位、大写、无短横线的UUID。
// 优先使用 UUID v7，它基于时间生成，对数据库索引更友好。如果生成失败，则回退到 UUID v4。
// 代码整理：使用 strings.ReplaceAll 提高代码可读性。
func GetUUID() string {
	var uid uuid.UUID
	uid, err := uuid.NewV7()
	if err != nil {
		// 当 v7 创建失败时（罕见情况，如系统时钟问题），回退到纯随机的 v4
		uid = uuid.Must(uuid.NewV4())
	}
	// 移除UUID字符串中的"-"并转换为大写
	return strings.ToUpper(strings.ReplaceAll(uid.String(), "-", ""))
}

// newSnowflakeIDGenerator 是一个内部函数，用于初始化雪花ID生成器。
// workerId 应在分布式环境中的每个节点上保持唯一。
func newSnowflakeIDGenerator(workerId uint16) {
	options := idgen.NewIdGeneratorOptions(workerId)
	idgen.SetIdGenerator(options)
}

// GenerateSnowflakeID 生成一个 int64 类型的雪花ID。
// 采用 sync.Once 来确保初始化过程只被执行一次，从而实现线程安全的单例生成器。
func GenerateSnowflakeID() int64 {
	snowflakeOnce.Do(func() {
		// 默认使用 workerId 为 1。在分布式部署时，请确保为每个服务实例分配唯一的 workerId。
		newSnowflakeIDGenerator(1)
	})
	return idgen.NextId()
}

// ==================== 版本号工具 ====================

// GenerateVersion 生成一个新的版本号，格式为 YYYYMMDDHHMMSS。
// 此函数实现简洁明了，无需改动。
func GenerateVersion() string {
	return time.Now().Format("20060102150405")
}
