package utils

import (
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/yitter/idgenerator-go/idgen"
)

var once sync.Once

// GenerateChineseName 随机生成一个长度为length的中文姓名
func GenerateChineseName(length int) string {
	// 中文字符范围 \u4e00-\u9fa5
	chineseChars := []rune("赵李吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜戚谢邹喻柏水窦章云苏潘葛奚范彭郎鲁韦昌马苗凤花方俞任袁柳酆鲍史唐费廉岑薛雷贺倪汤滕殷罗毕郝邬安常乐于时傅卞齐康伍余元卜顾孟平黄和穆萧尹姚邵湛汪祁毛禹狄米贝明臧计伏成戴谈宋茅庞熊纪舒屈项祝董粱杜阮蓝闵席季麻强贾路娄危江童颜郭梅盛林刁钟徐邱骆高夏蔡田樊胡凌霍虞万支柯昝管卢莫经房裘缪干解应宗丁宣贲邓郁单杭洪包诸左石崔吉钮龚程嵇邢滑裴陆荣翁荀羊於惠甄曲家封芮羿储靳汲邴糜松井段富巫乌焦巴弓牧隗山谷车侯宓蓬全郗班仰秋仲伊宫宁仇栾暴甘钭厉戎祖武符刘景詹束龙叶幸司韶郜黎蓟薄印宿白怀蒲邰从鄂索咸籍赖卓蔺屠蒙池乔阴鬱胥能苍双闻莘党翟谭贡劳逄姬申扶堵冉宰郦雍卻璩桑桂濮牛寿通边扈燕冀郏浦尚农温别庄晏柴瞿阎充慕连茹习宦艾鱼容向古易慎戈廖庾终暨居衡步都耿满弘匡国文寇广禄阙东欧殳沃利蔚越夔隆师巩厍聂晁勾敖融冷訾辛阚那简饶空曾毋沙乜养鞠须丰巢关蒯相查后荆红游竺权逯盖益桓公晋楚闫法汝鄢涂钦归海帅缑亢况后有琴梁丘左丘商牟佘佴伯赏南宫墨哈谯笪年爱阳佟言福百家姓")

	name := make([]rune, length)
	for i := range name {
		name[i] = chineseChars[rand.Intn(len(chineseChars))]
	}
	return string(name)
}

// GenerateRandomCode 随机生成一个长度为length的编码
func GenerateRandomCode(length int) string {
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	code := make([]byte, length)
	for i := range code {
		code[i] = letters[rand.Intn(len(letters))]
	}
	return string(code)
}

// GetUUID  获取uuid
func GetUUID() string {
	uid := uuid.New().String()
	rep := strings.Replace(uid, "-", "", -1)
	upper := strings.ToUpper(rep)
	return upper
}

// NewSnowflakeIDGenerator 初始化并返回一个新的雪花ID生成器
// 在单机环境中，dataCenterId 和 workerId 可以任意设置，不需要保证全局唯一
func NewSnowflakeIDGenerator(workerId uint16) {
	// 创建 IdGeneratorOptions 对象，可在构造函数中输入 WorkerId：
	options := idgen.NewIdGeneratorOptions(workerId)
	// 保存参数（务必调用，否则参数设置不生效）：
	idgen.SetIdGenerator(options)
}

// GenerateSnowflakeID 生成一个新的雪花ID
// 在单机环境中，自动设置数据中心ID和机器ID
func GenerateSnowflakeID() int64 {
	once.Do(func() { NewSnowflakeIDGenerator(1) })
	return idgen.NextId()
}

// GenerateVersion 生成一个新的版本号
// 版本号格式为：YYYYMMDDHHMMSS
func GenerateVersion() string {
	// 获取当前时间
	now := time.Now()
	// 格式化为 YYYYMMDDHHMMSS
	return now.Format("20060102150405")
}
