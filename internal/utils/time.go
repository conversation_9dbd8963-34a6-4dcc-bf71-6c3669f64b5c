package utils

import "time"

// 预判正则（简化版）
// 判断时间是否为纯日期（时间部分为 00:00:00）
func isDateOnly(t time.Time) bool {
	return t.Hour() == 0 && t.Minute() == 0 && t.Second() == 0
}
func GenerateTimeRange(inputTime time.Time) (start, end time.Time) {
	if isDateOnly(inputTime) {
		// 输入是纯日期：范围是当天 00:00:00 到 23:59:59
		start = inputTime
		end = inputTime.Add(24*time.Hour - 1*time.Second)
	} else {
		// 输入包含时间部分：范围是输入时间到当天 23:59:59
		start = inputTime
		end = time.Date(inputTime.Year(), inputTime.Month(), inputTime.Day(), 23, 59, 59, 0, inputTime.Location())
	}
	return
}
