package utils

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// VersionGenerator 版本号生成器接口
type VersionGenerator interface {
	Generate(current string) (string, error)
}

// SemVerGenerator 语义化版本生成器
type SemVerGenerator struct {
	PreRelease string
	Metadata   string
}

func (g *SemVerGenerator) Generate(current string) (string, error) {
	var major, minor, patch int
	var err error

	if current == "" {
		// 初始版本
		major, minor, patch = 1, 0, 0
	} else {
		// 解析现有版本
		re := regexp.MustCompile(`^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-.]+))?(?:\+([0-9A-Za-z-.]+))?$`)
		matches := re.FindStringSubmatch(current)
		if matches == nil {
			return "", errors.New("invalid semver format")
		}

		major, err = strconv.Atoi(matches[1])
		if err != nil {
			return "", fmt.Errorf("invalid major version: %s", matches[1])
		}

		minor, err = strconv.Atoi(matches[2])
		if err != nil {
			return "", fmt.Errorf("invalid minor version: %s", matches[2])
		}

		patch, err = strconv.Atoi(matches[3])
		if err != nil {
			return "", fmt.Errorf("invalid patch version: %s", matches[3])
		}

		// 自动增加补丁版本
		patch++

		// 如果提供了预发布或元数据，使用新的值
		if g.PreRelease == "" && len(matches) > 4 {
			g.PreRelease = matches[4]
		}
		if g.Metadata == "" && len(matches) > 5 {
			g.Metadata = matches[5]
		}
	}

	version := fmt.Sprintf("%d.%d.%d", major, minor, patch)

	// 添加预发布标签
	if g.PreRelease != "" {
		version += "-" + g.PreRelease
	}

	// 添加构建元数据
	if g.Metadata != "" {
		version += "+" + g.Metadata
	}

	return version, nil
}

// DateVersionGenerator 日期版本生成器
type DateVersionGenerator struct {
	PreRelease string
	Metadata   string
	Format     string // 支持 "short" (YYMMDD) 或 "long" (YYYY.MM.DD)
}

func (g *DateVersionGenerator) Generate(current string) (string, error) {
	now := time.Now()
	var datePart string

	switch g.Format {
	case "short":
		datePart = fmt.Sprintf("%02d%02d%02d", now.Year()%100, now.Month(), now.Day())
	case "long":
		fallthrough
	default:
		datePart = fmt.Sprintf("%d.%02d.%02d", now.Year(), now.Month(), now.Day())
	}

	// 解析构建号
	buildNumber := 1
	if current != "" {
		parts := strings.Split(current, ".")
		if len(parts) > 0 {
			lastPart := parts[len(parts)-1]
			if num, err := strconv.Atoi(lastPart); err == nil {
				buildNumber = num + 1
			}
		}
	}

	version := fmt.Sprintf("%s.%d", datePart, buildNumber)

	// 添加预发布标签
	if g.PreRelease != "" {
		version += "-" + g.PreRelease
	}

	// 添加构建元数据
	if g.Metadata != "" && !strings.HasPrefix(g.Metadata, "build.") {
		version += "+" + g.Metadata
	}

	return version, nil
}

// IncrementalVersionGenerator 增量版本生成器
type IncrementalVersionGenerator struct {
	Prefix     string
	PreRelease string
	Metadata   string
}

func (g *IncrementalVersionGenerator) Generate(current string) (string, error) {
	var versionNumber int
	var err error

	if current == "" {
		versionNumber = 1
	} else {
		// 去掉前缀
		numStr := strings.TrimPrefix(current, g.Prefix)
		versionNumber, err = strconv.Atoi(numStr)
		if err != nil {
			return "", fmt.Errorf("invalid incremental version: %s", current)
		}
		versionNumber++
	}

	version := fmt.Sprintf("%s%d", g.Prefix, versionNumber)

	// 添加预发布标签
	if g.PreRelease != "" {
		version += "-" + g.PreRelease
	}

	// 添加构建元数据
	if g.Metadata != "" {
		version += "+" + g.Metadata
	}

	return version, nil
}

// NumericVersionGenerator 纯数字版本生成器
type NumericVersionGenerator struct {
	Increment int // 每次增加的数值
}

func (g *NumericVersionGenerator) Generate(current string) (string, error) {
	var versionNumber int
	var err error

	if current == "" {
		versionNumber = 1
	} else {
		versionNumber, err = strconv.Atoi(current)
		if err != nil {
			return "", fmt.Errorf("invalid numeric version: %s", current)
		}
		versionNumber += g.Increment
	}

	return fmt.Sprintf("%d", versionNumber), nil
}

// DecimalVersionGenerator 小数版本生成器
type DecimalVersionGenerator struct {
	MinorDigits int // 小版本号位数 (默认2)
	CarryOver   int // 进位阈值 (默认100)
}

func (g *DecimalVersionGenerator) Generate(current string) (string, error) {
	if g.MinorDigits <= 0 {
		g.MinorDigits = 2
	}
	if g.CarryOver <= 0 {
		g.CarryOver = 100
	}

	var major, minor int
	var err error

	if current == "" {
		// 初始版本
		return "0.01", nil
	}

	// 解析现有版本
	parts := strings.Split(current, ".")
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid decimal version format: %s (expected X.Y)", current)
	}

	major, err = strconv.Atoi(parts[0])
	if err != nil {
		return "", fmt.Errorf("invalid major part in decimal version: %s", parts[0])
	}

	minor, err = strconv.Atoi(parts[1])
	if err != nil {
		return "", fmt.Errorf("invalid minor part in decimal version: %s", parts[1])
	}

	// 自动增加小版本号
	minor++

	// 进位检查
	if minor >= g.CarryOver {
		major++
		minor = 0
	}

	// 格式化小版本号
	format := fmt.Sprintf("%%d.%%0%dd", g.MinorDigits)
	return fmt.Sprintf(format, major, minor), nil
}

// NewGenerator 创建版本生成器
func NewGenerator(strategy string, options ...Option) (VersionGenerator, error) {
	opts := &Options{
		Prefix:      "v",
		PreRelease:  "",
		Metadata:    "",
		Format:      "long",
		Increment:   1,
		MinorDigits: 2,
		CarryOver:   100,
	}

	for _, opt := range options {
		opt(opts)
	}

	switch strings.ToLower(strategy) {
	case "semver":
		return &SemVerGenerator{
			PreRelease: opts.PreRelease,
			Metadata:   opts.Metadata,
		}, nil
	case "date":
		return &DateVersionGenerator{
			PreRelease: opts.PreRelease,
			Metadata:   opts.Metadata,
			Format:     opts.Format,
		}, nil
	case "incremental":
		return &IncrementalVersionGenerator{
			Prefix:     opts.Prefix,
			PreRelease: opts.PreRelease,
			Metadata:   opts.Metadata,
		}, nil
	case "numeric":
		return &NumericVersionGenerator{
			Increment: opts.Increment,
		}, nil
	case "decimal":
		return &DecimalVersionGenerator{
			MinorDigits: opts.MinorDigits,
			CarryOver:   opts.CarryOver,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported version strategy: %s", strategy)
	}
}

// Option 配置选项
type Option func(*Options)

// Options 生成器配置
type Options struct {
	Prefix      string
	PreRelease  string
	Metadata    string
	Format      string
	Increment   int
	MinorDigits int
	CarryOver   int
}

func WithPrefix(prefix string) Option {
	return func(o *Options) {
		o.Prefix = prefix
	}
}

func WithPreRelease(preRelease string) Option {
	return func(o *Options) {
		o.PreRelease = preRelease
	}
}

func WithMetadata(metadata string) Option {
	return func(o *Options) {
		o.Metadata = metadata
	}
}

func WithFormat(format string) Option {
	return func(o *Options) {
		o.Format = format
	}
}

func WithIncrement(increment int) Option {
	return func(o *Options) {
		o.Increment = increment
	}
}

func WithMinorDigits(digits int) Option {
	return func(o *Options) {
		o.MinorDigits = digits
	}
}

func WithCarryOver(threshold int) Option {
	return func(o *Options) {
		o.CarryOver = threshold
	}
}
