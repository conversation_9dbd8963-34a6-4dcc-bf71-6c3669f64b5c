package utils

import (
	"bytes"
	"strings"
	"unicode"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

// UnderscoreToCamelCase 将下划线命名法的字符串转换为小驼峰命名法
func UnderscoreToCamelCase(s string) string {
	var result string
	words := strings.Split(s, "_") // 将字符串按下划线分割成单词列表

	for i, word := range words {
		if i == 0 {
			result += word // 第一个单词保持原样
		} else {
			// 使用 cases 包将单词首字母大写
			result += cases.Title(language.English, cases.NoLower).String(word)
		}
	}

	return result
}

// CamelToSnake 将驼峰式命名的字符串转换为下划线式命名
func CamelToSnake(s string) string {
	if strings.Contains(s, "createTime") || strings.Contains(s, "updateTime") {
		return strings.Replace(s, "Time", "d_at", 1)
	}
	var output bytes.Buffer
	for i, text := range s {
		if i > 0 && unicode.IsUpper(text) {
			output.WriteByte('_')
		}
		output.WriteRune(unicode.ToLower(text))
	}
	return output.String()
}

// ToLike 给字符串添加%
func ToLike(s string) string {
	return "%" + s + "%"
}

// ConvertCamelCaseToLowerCamelCase 将驼峰命名法转换为小驼峰命名法
func ConvertCamelCaseToLowerCamelCase(input string) string {
	// 将首字母转换为小写
	firstChar := strings.ToLower(string(input[0]))
	// 截取剩余部分
	restOfString := input[1:]
	// 拼接首字母和剩余部分
	return firstChar + restOfString
}

// CapitalizeFirstLetter 字符串首字母大写
func CapitalizeFirstLetter(s string) string {
	if len(s) == 0 {
		return s
	}
	firstLetter := strings.ToUpper(string(s[0]))
	restOfString := s[1:]
	return firstLetter + restOfString
}

// IsStrBlank 判断字符串是否为空
func IsStrBlank(data string) bool {
	if data == "" || len(data) == 0 {
		return true
	}
	return false
}

// IsStrNotBlank 判断字符串是否不为空
func IsStrNotBlank(data string) bool {
	return !IsStrBlank(data)
}

// IsNumeric 判断字符串是否为数字
func IsNumeric(s string) bool {
	for _, r := range s {
		if !unicode.IsDigit(r) {
			return false
		}
	}
	return true
}

// IsAlphabetic 判断字符串是否为字母
func IsAlphabetic(s string) bool {
	for _, r := range s {
		if !unicode.IsLetter(r) {
			return false
		}
	}
	return true
}

// IsChinese 判断字符串是否为中文
func IsChinese(s string) bool {
	for _, r := range s {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

// SplitPermission 将权限字符串分割为资源和操作
// 例如："resource:action" -> ["resource", "action"]
func SplitPermission(permission string) []string {
	return strings.Split(permission, ":")
}
