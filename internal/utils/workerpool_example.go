package utils

import (
	"context"
	"fmt"
	"log"
	"time"
)

// ExampleBasicUsage 基本使用示例
func ExampleBasicUsage() {
	// 创建工作池
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	// 提交任务
	taskID, err := pool.Submit(func(ctx context.Context) error {
		fmt.Println("执行任务")
		time.Sleep(100 * time.Millisecond)
		return nil
	})

	if err != nil {
		log.Printf("提交任务失败: %v", err)
		return
	}

	fmt.Printf("任务ID: %s\n", taskID)

	// 等待任务完成
	pool.Wait()

	// 获取统计信息
	stats := pool.GetStats()
	fmt.Printf("统计信息: %+v\n", stats)
}

// ExampleCustomConfig 自定义配置示例
func ExampleCustomConfig() {
	config := &WorkerPoolConfig{
		Capacity:         50,                // 最大50个工作协程
		InitialWorkers:   10,                // 初始10个工作协程
		TaskQueueSize:    200,               // 任务队列大小200
		MaxErrorHistory:  500,               // 最大错误历史500条
		WorkerIdleTime:   30 * time.Second,  // 工作协程空闲30秒后退出
		StatsCleanupTime: 5 * time.Minute,   // 5分钟清理一次统计
		EnableMetrics:    true,              // 启用详细指标
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	fmt.Printf("工作池配置: %+v\n", pool.GetConfig())
}

// ExampleErrorHandling 错误处理示例
func ExampleErrorHandling() {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	// 提交会失败的任务
	taskID, err := pool.Submit(func(ctx context.Context) error {
		return fmt.Errorf("模拟错误")
	})

	if err != nil {
		log.Printf("提交任务失败: %v", err)
		return
	}

	pool.Wait()

	// 检查任务状态
	status, exists := pool.GetTaskStatus(taskID)
	if exists {
		fmt.Printf("任务状态: %s\n", status)
	}

	// 获取错误信息
	errors := pool.GetErrors()
	for _, taskErr := range errors {
		fmt.Printf("错误: %s - %s\n", taskErr.ID, taskErr.Message)
	}
}

// ExampleCancellation 任务取消示例
func ExampleCancellation() {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	// 提交长时间运行的任务
	taskID, err := pool.Submit(func(ctx context.Context) error {
		select {
		case <-ctx.Done():
			fmt.Println("任务被取消")
			return ctx.Err()
		case <-time.After(5 * time.Second):
			fmt.Println("任务完成")
			return nil
		}
	})

	if err != nil {
		log.Printf("提交任务失败: %v", err)
		return
	}

	// 等待一段时间后取消任务
	time.Sleep(100 * time.Millisecond)
	if pool.CancelTask(taskID) {
		fmt.Println("任务取消成功")
	}

	pool.Wait()
}

// ExampleMonitoring 监控示例
func ExampleMonitoring() {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	// 提交一些任务
	for i := 0; i < 10; i++ {
		pool.Submit(func(ctx context.Context) error {
			time.Sleep(time.Duration(i*10) * time.Millisecond)
			return nil
		})
	}

	// 监控工作池状态
	go func() {
		ticker := time.NewTicker(500 * time.Millisecond)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				fmt.Printf("活跃协程: %d, 待处理任务: %d, 队列长度: %d\n",
					pool.Running(), pool.Pending(), pool.Waiting())

				// 健康检查
				health := pool.Health()
				fmt.Printf("健康状态: %s\n", health["status"])

			case <-time.After(3 * time.Second):
				return
			}
		}
	}()

	pool.Wait()

	// 获取最终统计
	stats := pool.GetStats()
	fmt.Printf("最终统计: %+v\n", stats)

	// 获取性能指标
	metrics := pool.GetMetrics()
	fmt.Printf("性能指标: %+v\n", metrics)
}

// ExampleBatchProcessing 批处理示例
func ExampleBatchProcessing() {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	// 模拟批量数据处理
	data := make([]int, 100)
	for i := range data {
		data[i] = i
	}

	// 批量提交任务
	taskIDs := make([]TaskID, 0, len(data))
	for _, item := range data {
		taskID, err := pool.Submit(func(ctx context.Context) error {
			// 模拟数据处理
			result := item * item
			fmt.Printf("处理数据 %d, 结果: %d\n", item, result)
			time.Sleep(10 * time.Millisecond)
			return nil
		})

		if err != nil {
			log.Printf("提交任务失败: %v", err)
			continue
		}

		taskIDs = append(taskIDs, taskID)
	}

	fmt.Printf("提交了 %d 个任务\n", len(taskIDs))

	// 等待所有任务完成
	pool.Wait()

	fmt.Println("所有任务处理完成")
}

// ExampleGracefulShutdown 优雅关闭示例
func ExampleGracefulShutdown() {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}

	// 提交一些长时间运行的任务
	for i := 0; i < 5; i++ {
		pool.Submit(func(ctx context.Context) error {
			select {
			case <-ctx.Done():
				fmt.Println("任务被中断")
				return ctx.Err()
			case <-time.After(2 * time.Second):
				fmt.Println("任务正常完成")
				return nil
			}
		})
	}

	// 模拟接收到关闭信号
	go func() {
		time.Sleep(1 * time.Second)
		fmt.Println("开始优雅关闭...")

		// 带超时的优雅关闭
		if pool.ReleaseTimeout(3 * time.Second) {
			fmt.Println("优雅关闭成功")
		} else {
			fmt.Println("关闭超时，强制退出")
		}
	}()

	// 等待关闭完成
	time.Sleep(5 * time.Second)
}

// ExampleContextUsage 上下文使用示例
func ExampleContextUsage() {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
	defer cancel()

	// 提交带上下文的任务
	taskID, err := pool.SubmitWithContext(ctx, func(taskCtx context.Context) error {
		select {
		case <-taskCtx.Done():
			fmt.Println("任务因上下文取消而终止")
			return taskCtx.Err()
		case <-time.After(1 * time.Second):
			fmt.Println("任务完成")
			return nil
		}
	})

	if err != nil {
		log.Printf("提交任务失败: %v", err)
		return
	}

	pool.Wait()

	// 检查任务状态
	status, _ := pool.GetTaskStatus(taskID)
	fmt.Printf("任务最终状态: %s\n", status)
}

// ExampleLoadTesting 负载测试示例
func ExampleLoadTesting() {
	config := &WorkerPoolConfig{
		Capacity:         100,
		InitialWorkers:   20,
		TaskQueueSize:    500,
		MaxErrorHistory:  1000,
		WorkerIdleTime:   10 * time.Second,
		StatsCleanupTime: 1 * time.Minute,
		EnableMetrics:    true,
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	const numTasks = 1000
	start := time.Now()

	// 并发提交大量任务
	for i := 0; i < numTasks; i++ {
		go func(taskNum int) {
			_, err := pool.Submit(func(ctx context.Context) error {
				// 模拟不同的工作负载
				workTime := time.Duration(taskNum%100) * time.Millisecond
				time.Sleep(workTime)
				
				// 模拟一定的失败率
				if taskNum%50 == 0 {
					return fmt.Errorf("模拟错误 %d", taskNum)
				}
				
				return nil
			})

			if err != nil {
				log.Printf("提交任务 %d 失败: %v", taskNum, err)
			}
		}(i)
	}

	// 等待所有任务完成
	pool.Wait()

	duration := time.Since(start)
	stats := pool.GetStats()

	fmt.Printf("负载测试结果:\n")
	fmt.Printf("  总耗时: %v\n", duration)
	fmt.Printf("  提交任务: %d\n", stats.TotalSubmitted)
	fmt.Printf("  完成任务: %d\n", stats.TotalCompleted)
	fmt.Printf("  失败任务: %d\n", stats.TotalFailed)
	fmt.Printf("  平均执行时间: %v\n", stats.AverageTime)
	fmt.Printf("  最小执行时间: %v\n", stats.MinTime)
	fmt.Printf("  最大执行时间: %v\n", stats.MaxTime)

	// 获取详细指标
	metrics := pool.GetMetrics()
	if p50, ok := metrics["p50"]; ok {
		fmt.Printf("  P50延迟: %v\n", p50)
	}
	if p90, ok := metrics["p90"]; ok {
		fmt.Printf("  P90延迟: %v\n", p90)
	}
	if p99, ok := metrics["p99"]; ok {
		fmt.Printf("  P99延迟: %v\n", p99)
	}

	// 健康检查
	health := pool.Health()
	fmt.Printf("  健康状态: %s\n", health["status"])
	fmt.Printf("  成功率: %.2f%%\n", health["success_rate"].(float64)*100)
}