package utils

import "regexp"

func IsEmail(s string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(s)
}

func IsPhone(s string) bool {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(s)
}

func IsUsername(s string) bool {
	usernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_]{3,16}$`)
	return usernameRegex.MatchString(s)
}

func IsPassword(s string) bool {
	if len(s) < 8 {
		return false
	}
	hasLetter := false
	hasDigit := false
	for _, c := range s {
		switch {
		case c >= 'A' && c <= 'Z', c >= 'a' && c <= 'z':
			hasLetter = true
		case c >= '0' && c <= '9':
			hasDigit = true
		default:
			return false // 只允许字母和数字
		}
	}
	return hasLetter && hasDigit
}

func IsIDCard(s string) bool {
	idCardRegex := regexp.MustCompile(`^\d{15}|\d{18}$`)
	return idCardRegex.MatchString(s)
}

func IsURL(s string) bool {
	urlRegex := regexp.MustCompile(`^(http|https)://[a-zA-Z0-9.-]+(\.[a-zA-Z]{2,})+(/.*)?$`)
	return urlRegex.MatchString(s)
}

func IsIP(s string) bool {
	ipRegex := regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`)
	return ipRegex.MatchString(s)
}
