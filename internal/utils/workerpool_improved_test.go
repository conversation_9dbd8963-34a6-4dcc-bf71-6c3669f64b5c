package utils

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

func TestWorkerPoolBasicFunctionality(t *testing.T) {
	config := &WorkerPoolConfig{
		Capacity:         5,
		InitialWorkers:   2,
		TaskQueueSize:    10,
		MaxErrorHistory:  100,
		WorkerIdleTime:   5 * time.Second,
		StatsCleanupTime: 1 * time.Minute,
		EnableMetrics:    true,
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	// 测试基本任务提交
	var counter int64
	var wg sync.WaitGroup

	for i := 0; i < 10; i++ {
		wg.Add(1)
		taskID, err := pool.Submit(func(ctx context.Context) error {
			defer wg.Done()
			atomic.AddInt64(&counter, 1)
			time.Sleep(10 * time.Millisecond)
			return nil
		})

		if err != nil {
			t.Errorf("提交任务失败: %v", err)
			wg.Done()
		} else {
			t.Logf("提交任务成功: %s", taskID)
		}
	}

	wg.Wait()
	pool.Wait()

	if atomic.LoadInt64(&counter) != 10 {
		t.Errorf("期望执行10个任务，实际执行了%d个", counter)
	}

	stats := pool.GetStats()
	if stats.TotalSubmitted != 10 {
		t.Errorf("期望提交10个任务，实际提交了%d个", stats.TotalSubmitted)
	}

	if stats.TotalCompleted != 10 {
		t.Errorf("期望完成10个任务，实际完成了%d个", stats.TotalCompleted)
	}
}

func TestWorkerPoolErrorHandling(t *testing.T) {
	config := DefaultConfig()
	config.Capacity = 3
	config.MaxErrorHistory = 5

	pool, err := NewWorkerPool(config)
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	// 提交会失败的任务
	for i := 0; i < 3; i++ {
		_, err := pool.Submit(func(ctx context.Context) error {
			return fmt.Errorf("测试错误 %d", i)
		})
		if err != nil {
			t.Errorf("提交任务失败: %v", err)
		}
	}

	pool.Wait()

	stats := pool.GetStats()
	if stats.TotalFailed != 3 {
		t.Errorf("期望3个失败任务，实际%d个", stats.TotalFailed)
	}

	errors := pool.GetErrors()
	if len(errors) != 3 {
		t.Errorf("期望3个错误记录，实际%d个", len(errors))
	}

	// 测试错误历史限制
	for i := 0; i < 10; i++ {
		pool.Submit(func(ctx context.Context) error {
			return fmt.Errorf("额外错误 %d", i)
		})
	}

	pool.Wait()

	errors = pool.GetErrors()
	if len(errors) > config.MaxErrorHistory {
		t.Errorf("错误历史超过限制: %d > %d", len(errors), config.MaxErrorHistory)
	}
}

func TestWorkerPoolPanicHandling(t *testing.T) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	// 提交会panic的任务
	taskID, err := pool.Submit(func(ctx context.Context) error {
		panic("测试panic")
	})
	if err != nil {
		t.Errorf("提交任务失败: %v", err)
	}

	pool.Wait()

	// 检查任务状态
	status, exists := pool.GetTaskStatus(taskID)
	if !exists {
		t.Error("任务不存在")
	}

	if status != TaskStatusFailed {
		t.Errorf("期望任务状态为失败，实际为%s", status)
	}

	// 检查错误记录
	errors := pool.GetErrors()
	if len(errors) == 0 {
		t.Error("期望有错误记录")
	}
}

func TestWorkerPoolCancellation(t *testing.T) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	// 提交长时间运行的任务
	taskID, err := pool.Submit(func(ctx context.Context) error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(5 * time.Second):
			return nil
		}
	})
	if err != nil {
		t.Errorf("提交任务失败: %v", err)
	}

	// 等待任务开始
	time.Sleep(100 * time.Millisecond)

	// 取消任务
	if !pool.CancelTask(taskID) {
		t.Error("取消任务失败")
	}

	// 检查任务状态
	status, exists := pool.GetTaskStatus(taskID)
	if !exists {
		t.Error("任务不存在")
	}

	if status != TaskStatusCancelled {
		t.Errorf("期望任务状态为已取消，实际为%s", status)
	}
}

func TestWorkerPoolConcurrency(t *testing.T) {
	config := &WorkerPoolConfig{
		Capacity:         50,
		InitialWorkers:   10,
		TaskQueueSize:    100,
		MaxErrorHistory:  1000,
		WorkerIdleTime:   30 * time.Second,
		StatsCleanupTime: 5 * time.Minute,
		EnableMetrics:    true,
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	const numTasks = 1000
	var counter int64
	var wg sync.WaitGroup

	start := time.Now()

	// 并发提交大量任务
	for i := 0; i < numTasks; i++ {
		wg.Add(1)
		go func(taskNum int) {
			defer wg.Done()
			_, err := pool.Submit(func(ctx context.Context) error {
				atomic.AddInt64(&counter, 1)
				time.Sleep(time.Millisecond) // 模拟工作
				return nil
			})
			if err != nil {
				t.Errorf("提交任务%d失败: %v", taskNum, err)
			}
		}(i)
	}

	wg.Wait()
	pool.Wait()

	duration := time.Since(start)
	t.Logf("执行%d个任务耗时: %v", numTasks, duration)

	if atomic.LoadInt64(&counter) != numTasks {
		t.Errorf("期望执行%d个任务，实际执行了%d个", numTasks, counter)
	}

	stats := pool.GetStats()
	t.Logf("统计信息: %+v", stats)

	metrics := pool.GetMetrics()
	t.Logf("性能指标: %+v", metrics)
}

func TestWorkerPoolTimeout(t *testing.T) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	// 提交长时间运行的任务
	for i := 0; i < 3; i++ {
		pool.Submit(func(ctx context.Context) error {
			time.Sleep(2 * time.Second)
			return nil
		})
	}

	// 测试超时等待
	start := time.Now()
	success := pool.WaitWithTimeout(500 * time.Millisecond)
	duration := time.Since(start)

	if success {
		t.Error("期望等待超时，但任务完成了")
	}

	if duration > 600*time.Millisecond {
		t.Errorf("超时等待时间过长: %v", duration)
	}

	// 等待任务真正完成
	pool.Wait()
}

func TestWorkerPoolRelease(t *testing.T) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}

	// 提交一些任务
	for i := 0; i < 5; i++ {
		pool.Submit(func(ctx context.Context) error {
			time.Sleep(100 * time.Millisecond)
			return nil
		})
	}

	// 测试带超时的释放
	start := time.Now()
	success := pool.ReleaseTimeout(2 * time.Second)
	duration := time.Since(start)

	if !success {
		t.Error("释放超时")
	}

	if duration > 2*time.Second {
		t.Errorf("释放时间过长: %v", duration)
	}

	// 检查是否已释放
	if !pool.IsReleased() {
		t.Error("工作池应该已释放")
	}

	// 尝试提交新任务应该失败
	_, err = pool.Submit(func(ctx context.Context) error {
		return nil
	})

	if err == nil {
		t.Error("期望提交任务失败，但成功了")
	}
}

func TestWorkerPoolHealth(t *testing.T) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	// 提交一些任务
	for i := 0; i < 10; i++ {
		pool.Submit(func(ctx context.Context) error {
			time.Sleep(10 * time.Millisecond)
			return nil
		})
	}

	// 提交一些失败的任务
	for i := 0; i < 2; i++ {
		pool.Submit(func(ctx context.Context) error {
			return errors.New("测试错误")
		})
	}

	pool.Wait()

	health := pool.Health()
	t.Logf("健康状态: %+v", health)

	if health["status"] != "healthy" {
		t.Errorf("期望健康状态为healthy，实际为%s", health["status"])
	}

	successRate := health["success_rate"].(float64)
	expectedRate := 10.0 / 12.0 // 10个成功，2个失败
	if successRate < expectedRate-0.01 || successRate > expectedRate+0.01 {
		t.Errorf("成功率不正确: 期望%.2f，实际%.2f", expectedRate, successRate)
	}
}

func TestWorkerPoolDynamicScaling(t *testing.T) {
	config := &WorkerPoolConfig{
		Capacity:         20,
		InitialWorkers:   2,
		TaskQueueSize:    50,
		MaxErrorHistory:  100,
		WorkerIdleTime:   1 * time.Second, // 短空闲时间用于测试
		StatsCleanupTime: 1 * time.Minute,
		EnableMetrics:    true,
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		t.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	initialWorkers := pool.Running()
	t.Logf("初始工作协程数: %d", initialWorkers)

	// 提交大量任务触发扩容
	for i := 0; i < 30; i++ {
		pool.Submit(func(ctx context.Context) error {
			time.Sleep(100 * time.Millisecond)
			return nil
		})
	}

	// 等待一段时间让工作协程扩展
	time.Sleep(200 * time.Millisecond)

	expandedWorkers := pool.Running()
	t.Logf("扩展后工作协程数: %d", expandedWorkers)

	if expandedWorkers <= initialWorkers {
		t.Error("工作协程数量应该增加")
	}

	// 等待任务完成
	pool.Wait()

	// 等待工作协程收缩
	time.Sleep(2 * time.Second)

	finalWorkers := pool.Running()
	t.Logf("最终工作协程数: %d", finalWorkers)

	// 工作协程应该收缩但不会低于初始数量
	if finalWorkers > expandedWorkers {
		t.Error("工作协程数量应该收缩")
	}
}

// 基准测试
func BenchmarkWorkerPoolSubmit(b *testing.B) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		b.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			pool.Submit(func(ctx context.Context) error {
				return nil
			})
		}
	})

	pool.Wait()
}

func BenchmarkWorkerPoolExecution(b *testing.B) {
	pool, err := NewWorkerPool(DefaultConfig())
	if err != nil {
		b.Fatalf("创建工作池失败: %v", err)
	}
	defer pool.Release()

	b.ResetTimer()

	var wg sync.WaitGroup
	for i := 0; i < b.N; i++ {
		wg.Add(1)
		pool.Submit(func(ctx context.Context) error {
			defer wg.Done()
			// 模拟一些工作
			for j := 0; j < 1000; j++ {
				_ = j * j
			}
			return nil
		})
	}

	wg.Wait()
}
