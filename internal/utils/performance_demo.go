package utils

import (
	"context"
	"fmt"
	"log"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// PerformanceDemo 性能演示
func PerformanceDemo() {
	fmt.Println("🚀 工作池性能演示")
	fmt.Println("==================")

	// 创建高性能配置
	config := &WorkerPoolConfig{
		Capacity:         runtime.NumCPU() * 20,
		InitialWorkers:   runtime.NumCPU() * 2,
		TaskQueueSize:    1000,
		MaxErrorHistory:  500,
		WorkerIdleTime:   30 * time.Second,
		StatsCleanupTime: 5 * time.Minute,
		EnableMetrics:    true,
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	fmt.Printf("配置信息:\n")
	fmt.Printf("  最大协程数: %d\n", config.Capacity)
	fmt.Printf("  初始协程数: %d\n", config.InitialWorkers)
	fmt.Printf("  队列大小: %d\n", config.TaskQueueSize)
	fmt.Printf("  CPU核心数: %d\n", runtime.NumCPU())
	fmt.Println()

	// 演示1: 基础性能测试
	basicPerformanceTest(pool)

	// 演示2: 并发压力测试
	concurrencyStressTest(pool)

	// 演示3: 错误处理测试
	errorHandlingTest(pool)

	// 演示4: 任务取消测试
	taskCancellationTest(pool)

	// 演示5: 监控功能展示
	monitoringDemo(pool)

	fmt.Println("✅ 所有演示完成!")
}

// basicPerformanceTest 基础性能测试
func basicPerformanceTest(pool *WorkerPool) {
	fmt.Println("📊 基础性能测试")
	fmt.Println("----------------")

	const numTasks = 1000
	start := time.Now()

	var completed int64
	var wg sync.WaitGroup

	for i := 0; i < numTasks; i++ {
		wg.Add(1)
		taskNum := i
		_, err := pool.Submit(func(ctx context.Context) error {
			defer wg.Done()
			
			// 模拟工作负载
			time.Sleep(time.Duration(taskNum%10) * time.Millisecond)
			atomic.AddInt64(&completed, 1)
			return nil
		})

		if err != nil {
			wg.Done()
			fmt.Printf("任务提交失败: %v\n", err)
		}
	}

	wg.Wait()
	duration := time.Since(start)

	fmt.Printf("结果:\n")
	fmt.Printf("  提交任务: %d\n", numTasks)
	fmt.Printf("  完成任务: %d\n", atomic.LoadInt64(&completed))
	fmt.Printf("  总耗时: %v\n", duration)
	fmt.Printf("  平均TPS: %.2f\n", float64(numTasks)/duration.Seconds())
	fmt.Printf("  活跃协程: %d\n", pool.Running())
	fmt.Println()
}

// concurrencyStressTest 并发压力测试
func concurrencyStressTest(pool *WorkerPool) {
	fmt.Println("⚡ 并发压力测试")
	fmt.Println("----------------")

	const numGoroutines = 50
	const tasksPerGoroutine = 20
	const totalTasks = numGoroutines * tasksPerGoroutine

	start := time.Now()
	var submitted, completed int64
	var wg sync.WaitGroup

	// 启动多个协程并发提交任务
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < tasksPerGoroutine; j++ {
				wg.Add(1)
				_, err := pool.Submit(func(ctx context.Context) error {
					defer wg.Done()
					
					// 模拟不同的工作负载
					workTime := time.Duration((goroutineID*tasksPerGoroutine+j)%50) * time.Microsecond
					time.Sleep(workTime)
					
					atomic.AddInt64(&completed, 1)
					return nil
				})

				if err != nil {
					wg.Done()
					fmt.Printf("协程%d任务%d提交失败: %v\n", goroutineID, j, err)
				} else {
					atomic.AddInt64(&submitted, 1)
				}
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(start)

	fmt.Printf("结果:\n")
	fmt.Printf("  并发协程: %d\n", numGoroutines)
	fmt.Printf("  预期任务: %d\n", totalTasks)
	fmt.Printf("  提交任务: %d\n", atomic.LoadInt64(&submitted))
	fmt.Printf("  完成任务: %d\n", atomic.LoadInt64(&completed))
	fmt.Printf("  总耗时: %v\n", duration)
	fmt.Printf("  并发TPS: %.2f\n", float64(atomic.LoadInt64(&completed))/duration.Seconds())
	fmt.Printf("  成功率: %.2f%%\n", float64(atomic.LoadInt64(&completed))/float64(atomic.LoadInt64(&submitted))*100)
	fmt.Println()
}

// errorHandlingTest 错误处理测试
func errorHandlingTest(pool *WorkerPool) {
	fmt.Println("🚨 错误处理测试")
	fmt.Println("----------------")

	const numTasks = 50
	var wg sync.WaitGroup

	// 清理之前的错误
	pool.ClearErrors()

	for i := 0; i < numTasks; i++ {
		wg.Add(1)
		taskNum := i
		_, err := pool.Submit(func(ctx context.Context) error {
			defer wg.Done()
			
			// 模拟不同类型的错误
			switch taskNum % 5 {
			case 0:
				return fmt.Errorf("业务错误: 任务%d", taskNum)
			case 1:
				panic(fmt.Sprintf("panic错误: 任务%d", taskNum))
			case 2:
				time.Sleep(10 * time.Millisecond)
				return fmt.Errorf("超时错误: 任务%d", taskNum)
			default:
				time.Sleep(5 * time.Millisecond)
				return nil // 成功
			}
		})

		if err != nil {
			wg.Done()
			fmt.Printf("任务提交失败: %v\n", err)
		}
	}

	wg.Wait()

	// 检查错误统计
	stats := pool.GetStats()
	errors := pool.GetErrors()

	fmt.Printf("结果:\n")
	fmt.Printf("  提交任务: %d\n", stats.TotalSubmitted)
	fmt.Printf("  成功任务: %d\n", stats.TotalCompleted)
	fmt.Printf("  失败任务: %d\n", stats.TotalFailed)
	fmt.Printf("  错误记录: %d\n", len(errors))
	fmt.Printf("  成功率: %.2f%%\n", float64(stats.TotalCompleted)/float64(stats.TotalSubmitted)*100)

	// 显示部分错误
	fmt.Printf("  错误示例:\n")
	for i, err := range errors {
		if i >= 3 { // 只显示前3个错误
			fmt.Printf("    ... 还有 %d 个错误\n", len(errors)-3)
			break
		}
		fmt.Printf("    %s: %s\n", err.ID, err.Message)
	}
	fmt.Println()
}

// taskCancellationTest 任务取消测试
func taskCancellationTest(pool *WorkerPool) {
	fmt.Println("🛑 任务取消测试")
	fmt.Println("----------------")

	// 提交长时间运行的任务
	taskIDs := make([]TaskID, 0, 10)
	for i := 0; i < 10; i++ {
		taskID, err := pool.Submit(func(ctx context.Context) error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(5 * time.Second):
				return nil
			}
		})

		if err != nil {
			fmt.Printf("任务提交失败: %v\n", err)
			continue
		}

		taskIDs = append(taskIDs, taskID)
	}

	// 等待任务开始
	time.Sleep(100 * time.Millisecond)

	// 取消一半的任务
	cancelledCount := 0
	for i, taskID := range taskIDs {
		if i%2 == 0 { // 取消偶数索引的任务
			if pool.CancelTask(taskID) {
				cancelledCount++
			}
		}
	}

	// 等待所有任务完成
	pool.Wait()

	// 检查任务状态
	completedCount := 0
	actualCancelledCount := 0
	for _, taskID := range taskIDs {
		status, exists := pool.GetTaskStatus(taskID)
		if exists {
			switch status {
			case TaskStatusCompleted:
				completedCount++
			case TaskStatusCancelled:
				actualCancelledCount++
			}
		}
	}

	fmt.Printf("结果:\n")
	fmt.Printf("  提交任务: %d\n", len(taskIDs))
	fmt.Printf("  尝试取消: %d\n", cancelledCount)
	fmt.Printf("  实际取消: %d\n", actualCancelledCount)
	fmt.Printf("  正常完成: %d\n", completedCount)
	fmt.Println()
}

// monitoringDemo 监控功能展示
func monitoringDemo(pool *WorkerPool) {
	fmt.Println("📈 监控功能展示")
	fmt.Println("----------------")

	// 提交一些任务以生成监控数据
	for i := 0; i < 100; i++ {
		pool.Submit(func(ctx context.Context) error {
			time.Sleep(time.Duration(i%20) * time.Millisecond)
			if i%10 == 0 {
				return fmt.Errorf("模拟错误")
			}
			return nil
		})
	}

	pool.Wait()

	// 显示统计信息
	stats := pool.GetStats()
	fmt.Printf("统计信息:\n")
	fmt.Printf("  总提交: %d\n", stats.TotalSubmitted)
	fmt.Printf("  总完成: %d\n", stats.TotalCompleted)
	fmt.Printf("  总失败: %d\n", stats.TotalFailed)
	fmt.Printf("  平均时间: %v\n", stats.AverageTime)
	fmt.Printf("  最小时间: %v\n", stats.MinTime)
	fmt.Printf("  最大时间: %v\n", stats.MaxTime)
	fmt.Printf("  最后更新: %v\n", stats.LastUpdated.Format("15:04:05"))

	// 显示性能指标
	metrics := pool.GetMetrics()
	fmt.Printf("\n性能指标:\n")
	if metrics["enabled"].(bool) {
		fmt.Printf("  样本数量: %v\n", metrics["sample_count"])
		fmt.Printf("  P50延迟: %v\n", metrics["p50"])
		fmt.Printf("  P90延迟: %v\n", metrics["p90"])
		fmt.Printf("  P99延迟: %v\n", metrics["p99"])
	}

	// 显示健康状态
	health := pool.Health()
	fmt.Printf("\n健康状态:\n")
	fmt.Printf("  状态: %s\n", health["status"])
	fmt.Printf("  活跃协程: %v\n", health["active_workers"])
	fmt.Printf("  容量: %v\n", health["capacity"])
	fmt.Printf("  队列利用率: %.2f%%\n", health["queue_utilization"].(float64)*100)
	fmt.Printf("  协程利用率: %.2f%%\n", health["worker_utilization"].(float64)*100)
	fmt.Printf("  成功率: %.2f%%\n", health["success_rate"].(float64)*100)

	// 显示配置信息
	config := pool.GetConfig()
	fmt.Printf("\n配置信息:\n")
	fmt.Printf("  最大容量: %d\n", config.Capacity)
	fmt.Printf("  初始协程: %d\n", config.InitialWorkers)
	fmt.Printf("  队列大小: %d\n", config.TaskQueueSize)
	fmt.Printf("  错误历史: %d\n", config.MaxErrorHistory)
	fmt.Printf("  空闲超时: %v\n", config.WorkerIdleTime)
	fmt.Printf("  清理间隔: %v\n", config.StatsCleanupTime)
	fmt.Printf("  启用指标: %v\n", config.EnableMetrics)
	fmt.Println()
}

// LoadTestDemo 负载测试演示
func LoadTestDemo() {
	fmt.Println("🔥 负载测试演示")
	fmt.Println("================")

	// 创建高负载配置
	config := &WorkerPoolConfig{
		Capacity:         runtime.NumCPU() * 100,
		InitialWorkers:   runtime.NumCPU() * 10,
		TaskQueueSize:    5000,
		MaxErrorHistory:  1000,
		WorkerIdleTime:   10 * time.Second,
		StatsCleanupTime: 1 * time.Minute,
		EnableMetrics:    true,
	}

	pool, err := NewWorkerPool(config)
	if err != nil {
		log.Fatal(err)
	}
	defer pool.Release()

	const duration = 30 * time.Second
	const numProducers = 20

	fmt.Printf("负载测试配置:\n")
	fmt.Printf("  测试时长: %v\n", duration)
	fmt.Printf("  生产者数: %d\n", numProducers)
	fmt.Printf("  最大协程: %d\n", config.Capacity)
	fmt.Println()

	var totalSubmitted, totalCompleted int64
	start := time.Now()

	// 启动监控协程
	stopMonitor := make(chan struct{})
	go func() {
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				health := pool.Health()
				fmt.Printf("[%v] 状态: %s, 活跃: %v, 队列: %v, 成功率: %.2f%%\n",
					time.Since(start).Truncate(time.Second),
					health["status"],
					health["active_workers"],
					pool.Waiting(),
					health["success_rate"].(float64)*100)
			case <-stopMonitor:
				return
			}
		}
	}()

	// 启动生产者
	var wg sync.WaitGroup
	for i := 0; i < numProducers; i++ {
		wg.Add(1)
		go func(producerID int) {
			defer wg.Done()
			
			for time.Since(start) < duration {
				_, err := pool.Submit(func(ctx context.Context) error {
					// 模拟不同的工作负载
					workTime := time.Duration(producerID%100) * time.Microsecond
					time.Sleep(workTime)
					
					// 模拟少量错误
					if producerID%100 == 0 {
						return fmt.Errorf("模拟错误")
					}
					
					atomic.AddInt64(&totalCompleted, 1)
					return nil
				})

				if err == nil {
					atomic.AddInt64(&totalSubmitted, 1)
				}

				// 控制提交频率
				time.Sleep(time.Millisecond)
			}
		}(i)
	}

	wg.Wait()
	pool.Wait()
	close(stopMonitor)

	totalDuration := time.Since(start)
	
	fmt.Printf("\n负载测试结果:\n")
	fmt.Printf("  实际时长: %v\n", totalDuration)
	fmt.Printf("  提交任务: %d\n", atomic.LoadInt64(&totalSubmitted))
	fmt.Printf("  完成任务: %d\n", atomic.LoadInt64(&totalCompleted))
	fmt.Printf("  平均TPS: %.2f\n", float64(atomic.LoadInt64(&totalCompleted))/totalDuration.Seconds())
	
	// 最终统计
	stats := pool.GetStats()
	metrics := pool.GetMetrics()
	health := pool.Health()
	
	fmt.Printf("  成功率: %.2f%%\n", health["success_rate"].(float64)*100)
	fmt.Printf("  平均延迟: %v\n", stats.AverageTime)
	if metrics["enabled"].(bool) {
		fmt.Printf("  P99延迟: %v\n", metrics["p99"])
	}
	fmt.Printf("  最大协程: %d\n", pool.Running())
	fmt.Println()
}

// ComparisonDemo 对比演示
func ComparisonDemo() {
	fmt.Println("⚖️  版本对比演示")
	fmt.Println("================")

	// 这里可以添加与原版本的性能对比
	// 由于原版本可能有内存泄漏等问题，这里只展示改进版本的优势

	fmt.Println("改进版本优势:")
	fmt.Println("✅ 内存安全 - 无泄漏风险")
	fmt.Println("✅ 并发安全 - 完全线程安全")
	fmt.Println("✅ 任务取消 - 支持运行时取消")
	fmt.Println("✅ 详细监控 - 丰富的性能指标")
	fmt.Println("✅ 智能扩缩容 - 根据负载自动调整")
	fmt.Println("✅ 配置灵活 - 多种配置选项")
	fmt.Println("✅ 生产就绪 - 企业级稳定性")
	fmt.Println()

	// 运行一个简单的演示
	PerformanceDemo()
}