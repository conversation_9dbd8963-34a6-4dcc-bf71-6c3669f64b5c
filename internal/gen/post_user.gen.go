// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newPostUser(db *gorm.DB, opts ...gen.DOOption) postUser {
	_postUser := postUser{}

	_postUser.postUserDo.UseDB(db, opts...)
	_postUser.postUserDo.UseModel(&model.PostUser{})

	tableName := _postUser.postUserDo.TableName()
	_postUser.ALL = field.NewAsterisk(tableName)
	_postUser.ID = field.NewInt64(tableName, "id")
	_postUser.PostID = field.NewInt64(tableName, "post_id")
	_postUser.UserID = field.NewInt64(tableName, "user_id")

	_postUser.fillFieldMap()

	return _postUser
}

// postUser 岗位、用户关联表
type postUser struct {
	postUserDo

	ALL field.Asterisk
	/*
		主键id

	*/
	ID     field.Int64
	PostID field.Int64 // 岗位id
	UserID field.Int64 // 用户主键

	fieldMap map[string]field.Expr
}

func (p postUser) Table(newTableName string) *postUser {
	p.postUserDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p postUser) As(alias string) *postUser {
	p.postUserDo.DO = *(p.postUserDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *postUser) updateTableName(table string) *postUser {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.PostID = field.NewInt64(table, "post_id")
	p.UserID = field.NewInt64(table, "user_id")

	p.fillFieldMap()

	return p
}

func (p *postUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *postUser) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 3)
	p.fieldMap["id"] = p.ID
	p.fieldMap["post_id"] = p.PostID
	p.fieldMap["user_id"] = p.UserID
}

func (p postUser) clone(db *gorm.DB) postUser {
	p.postUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p postUser) replaceDB(db *gorm.DB) postUser {
	p.postUserDo.ReplaceDB(db)
	return p
}

type postUserDo struct{ gen.DO }

type IPostUserDo interface {
	gen.SubQuery
	Debug() IPostUserDo
	WithContext(ctx context.Context) IPostUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPostUserDo
	WriteDB() IPostUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPostUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPostUserDo
	Not(conds ...gen.Condition) IPostUserDo
	Or(conds ...gen.Condition) IPostUserDo
	Select(conds ...field.Expr) IPostUserDo
	Where(conds ...gen.Condition) IPostUserDo
	Order(conds ...field.Expr) IPostUserDo
	Distinct(cols ...field.Expr) IPostUserDo
	Omit(cols ...field.Expr) IPostUserDo
	Join(table schema.Tabler, on ...field.Expr) IPostUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPostUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPostUserDo
	Group(cols ...field.Expr) IPostUserDo
	Having(conds ...gen.Condition) IPostUserDo
	Limit(limit int) IPostUserDo
	Offset(offset int) IPostUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPostUserDo
	Unscoped() IPostUserDo
	Create(values ...*model.PostUser) error
	CreateInBatches(values []*model.PostUser, batchSize int) error
	Save(values ...*model.PostUser) error
	First() (*model.PostUser, error)
	Take() (*model.PostUser, error)
	Last() (*model.PostUser, error)
	Find() ([]*model.PostUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PostUser, err error)
	FindInBatches(result *[]*model.PostUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PostUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPostUserDo
	Assign(attrs ...field.AssignExpr) IPostUserDo
	Joins(fields ...field.RelationField) IPostUserDo
	Preload(fields ...field.RelationField) IPostUserDo
	FirstOrInit() (*model.PostUser, error)
	FirstOrCreate() (*model.PostUser, error)
	FindByPage(offset int, limit int) (result []*model.PostUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPostUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p postUserDo) Debug() IPostUserDo {
	return p.withDO(p.DO.Debug())
}

func (p postUserDo) WithContext(ctx context.Context) IPostUserDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p postUserDo) ReadDB() IPostUserDo {
	return p.Clauses(dbresolver.Read)
}

func (p postUserDo) WriteDB() IPostUserDo {
	return p.Clauses(dbresolver.Write)
}

func (p postUserDo) Session(config *gorm.Session) IPostUserDo {
	return p.withDO(p.DO.Session(config))
}

func (p postUserDo) Clauses(conds ...clause.Expression) IPostUserDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p postUserDo) Returning(value interface{}, columns ...string) IPostUserDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p postUserDo) Not(conds ...gen.Condition) IPostUserDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p postUserDo) Or(conds ...gen.Condition) IPostUserDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p postUserDo) Select(conds ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p postUserDo) Where(conds ...gen.Condition) IPostUserDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p postUserDo) Order(conds ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p postUserDo) Distinct(cols ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p postUserDo) Omit(cols ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p postUserDo) Join(table schema.Tabler, on ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p postUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p postUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p postUserDo) Group(cols ...field.Expr) IPostUserDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p postUserDo) Having(conds ...gen.Condition) IPostUserDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p postUserDo) Limit(limit int) IPostUserDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p postUserDo) Offset(offset int) IPostUserDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p postUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPostUserDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p postUserDo) Unscoped() IPostUserDo {
	return p.withDO(p.DO.Unscoped())
}

func (p postUserDo) Create(values ...*model.PostUser) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p postUserDo) CreateInBatches(values []*model.PostUser, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p postUserDo) Save(values ...*model.PostUser) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p postUserDo) First() (*model.PostUser, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostUser), nil
	}
}

func (p postUserDo) Take() (*model.PostUser, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostUser), nil
	}
}

func (p postUserDo) Last() (*model.PostUser, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostUser), nil
	}
}

func (p postUserDo) Find() ([]*model.PostUser, error) {
	result, err := p.DO.Find()
	return result.([]*model.PostUser), err
}

func (p postUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PostUser, err error) {
	buf := make([]*model.PostUser, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p postUserDo) FindInBatches(result *[]*model.PostUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p postUserDo) Attrs(attrs ...field.AssignExpr) IPostUserDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p postUserDo) Assign(attrs ...field.AssignExpr) IPostUserDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p postUserDo) Joins(fields ...field.RelationField) IPostUserDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p postUserDo) Preload(fields ...field.RelationField) IPostUserDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p postUserDo) FirstOrInit() (*model.PostUser, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostUser), nil
	}
}

func (p postUserDo) FirstOrCreate() (*model.PostUser, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PostUser), nil
	}
}

func (p postUserDo) FindByPage(offset int, limit int) (result []*model.PostUser, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p postUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p postUserDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p postUserDo) Delete(models ...*model.PostUser) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *postUserDo) withDO(do gen.Dao) *postUserDo {
	p.DO = *do.(*gen.DO)
	return p
}
