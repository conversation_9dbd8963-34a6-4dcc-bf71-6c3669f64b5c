// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newSysRoute(db *gorm.DB, opts ...gen.DOOption) sysRoute {
	_sysRoute := sysRoute{}

	_sysRoute.sysRouteDo.UseDB(db, opts...)
	_sysRoute.sysRouteDo.UseModel(&model.SysRoute{})

	tableName := _sysRoute.sysRouteDo.TableName()
	_sysRoute.ALL = field.NewAsterisk(tableName)
	_sysRoute.ID = field.NewInt64(tableName, "id")
	_sysRoute.Name = field.NewString(tableName, "name")
	_sysRoute.Path = field.NewString(tableName, "path")
	_sysRoute.Component = field.NewString(tableName, "component")
	_sysRoute.Redirect = field.NewString(tableName, "redirect")
	_sysRoute.ParentID = field.NewString(tableName, "parent_id")
	_sysRoute.Sort = field.NewInt64(tableName, "sort")
	_sysRoute.Status = field.NewString(tableName, "status")
	_sysRoute.IsLayout = field.NewBool(tableName, "is_layout")
	_sysRoute.Title = field.NewString(tableName, "title")
	_sysRoute.Icon = field.NewString(tableName, "icon")
	_sysRoute.Hidden = field.NewBool(tableName, "hidden")
	_sysRoute.ActiveMenu = field.NewString(tableName, "active_menu")
	_sysRoute.KeepAlive = field.NewBool(tableName, "keep_alive")
	_sysRoute.Auth = field.NewString(tableName, "auth")
	_sysRoute.Breadcrumb = field.NewBool(tableName, "breadcrumb")
	_sysRoute.External = field.NewBool(tableName, "external")
	_sysRoute.ExternalURL = field.NewString(tableName, "external_url")
	_sysRoute.CreatedAt = field.NewTime(tableName, "created_at")
	_sysRoute.UpdatedAt = field.NewTime(tableName, "updated_at")
	_sysRoute.UUID = field.NewString(tableName, "uuid")
	_sysRoute.TenantID = field.NewString(tableName, "tenant_id")

	_sysRoute.fillFieldMap()

	return _sysRoute
}

// sysRoute 路由表
type sysRoute struct {
	sysRouteDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键
	Name        field.String // 路由唯一名称
	Path        field.String // 路由路径
	Component   field.String // 组件路径
	Redirect    field.String // 重定向路径
	ParentID    field.String
	Sort        field.Int64  // 排序（越大越靠前）
	Status      field.String // 状态（enable启用 disabled禁用）
	IsLayout    field.Bool   // 是否一级路由（1是 0否）
	Title       field.String
	Icon        field.String
	Hidden      field.Bool
	ActiveMenu  field.String
	KeepAlive   field.Bool
	Auth        field.String
	Breadcrumb  field.Bool
	External    field.Bool
	ExternalURL field.String
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 更新时间
	UUID        field.String // 唯一ID
	TenantID    field.String // 租户ID

	fieldMap map[string]field.Expr
}

func (s sysRoute) Table(newTableName string) *sysRoute {
	s.sysRouteDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysRoute) As(alias string) *sysRoute {
	s.sysRouteDo.DO = *(s.sysRouteDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysRoute) updateTableName(table string) *sysRoute {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.Name = field.NewString(table, "name")
	s.Path = field.NewString(table, "path")
	s.Component = field.NewString(table, "component")
	s.Redirect = field.NewString(table, "redirect")
	s.ParentID = field.NewString(table, "parent_id")
	s.Sort = field.NewInt64(table, "sort")
	s.Status = field.NewString(table, "status")
	s.IsLayout = field.NewBool(table, "is_layout")
	s.Title = field.NewString(table, "title")
	s.Icon = field.NewString(table, "icon")
	s.Hidden = field.NewBool(table, "hidden")
	s.ActiveMenu = field.NewString(table, "active_menu")
	s.KeepAlive = field.NewBool(table, "keep_alive")
	s.Auth = field.NewString(table, "auth")
	s.Breadcrumb = field.NewBool(table, "breadcrumb")
	s.External = field.NewBool(table, "external")
	s.ExternalURL = field.NewString(table, "external_url")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.UUID = field.NewString(table, "uuid")
	s.TenantID = field.NewString(table, "tenant_id")

	s.fillFieldMap()

	return s
}

func (s *sysRoute) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysRoute) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 23)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["path"] = s.Path
	s.fieldMap["component"] = s.Component
	s.fieldMap["redirect"] = s.Redirect
	s.fieldMap["parent_id"] = s.ParentID
	s.fieldMap["sort"] = s.Sort
	s.fieldMap["status"] = s.Status
	s.fieldMap["is_layout"] = s.IsLayout
	s.fieldMap["title"] = s.Title
	s.fieldMap["icon"] = s.Icon
	s.fieldMap["hidden"] = s.Hidden
	s.fieldMap["active_menu"] = s.ActiveMenu
	s.fieldMap["keep_alive"] = s.KeepAlive
	s.fieldMap["auth"] = s.Auth
	s.fieldMap["breadcrumb"] = s.Breadcrumb
	s.fieldMap["external"] = s.External
	s.fieldMap["external_url"] = s.ExternalURL
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["uuid"] = s.UUID
	s.fieldMap["tenant_id"] = s.TenantID

}

func (s sysRoute) clone(db *gorm.DB) sysRoute {
	s.sysRouteDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysRoute) replaceDB(db *gorm.DB) sysRoute {
	s.sysRouteDo.ReplaceDB(db)
	return s
}

type sysRouteDo struct{ gen.DO }

type ISysRouteDo interface {
	gen.SubQuery
	Debug() ISysRouteDo
	WithContext(ctx context.Context) ISysRouteDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISysRouteDo
	WriteDB() ISysRouteDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISysRouteDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISysRouteDo
	Not(conds ...gen.Condition) ISysRouteDo
	Or(conds ...gen.Condition) ISysRouteDo
	Select(conds ...field.Expr) ISysRouteDo
	Where(conds ...gen.Condition) ISysRouteDo
	Order(conds ...field.Expr) ISysRouteDo
	Distinct(cols ...field.Expr) ISysRouteDo
	Omit(cols ...field.Expr) ISysRouteDo
	Join(table schema.Tabler, on ...field.Expr) ISysRouteDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISysRouteDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISysRouteDo
	Group(cols ...field.Expr) ISysRouteDo
	Having(conds ...gen.Condition) ISysRouteDo
	Limit(limit int) ISysRouteDo
	Offset(offset int) ISysRouteDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISysRouteDo
	Unscoped() ISysRouteDo
	Create(values ...*model.SysRoute) error
	CreateInBatches(values []*model.SysRoute, batchSize int) error
	Save(values ...*model.SysRoute) error
	First() (*model.SysRoute, error)
	Take() (*model.SysRoute, error)
	Last() (*model.SysRoute, error)
	Find() ([]*model.SysRoute, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysRoute, err error)
	FindInBatches(result *[]*model.SysRoute, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SysRoute) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISysRouteDo
	Assign(attrs ...field.AssignExpr) ISysRouteDo
	Joins(fields ...field.RelationField) ISysRouteDo
	Preload(fields ...field.RelationField) ISysRouteDo
	FirstOrInit() (*model.SysRoute, error)
	FirstOrCreate() (*model.SysRoute, error)
	FindByPage(offset int, limit int) (result []*model.SysRoute, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISysRouteDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s sysRouteDo) Debug() ISysRouteDo {
	return s.withDO(s.DO.Debug())
}

func (s sysRouteDo) WithContext(ctx context.Context) ISysRouteDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysRouteDo) ReadDB() ISysRouteDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysRouteDo) WriteDB() ISysRouteDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysRouteDo) Session(config *gorm.Session) ISysRouteDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysRouteDo) Clauses(conds ...clause.Expression) ISysRouteDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysRouteDo) Returning(value interface{}, columns ...string) ISysRouteDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysRouteDo) Not(conds ...gen.Condition) ISysRouteDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysRouteDo) Or(conds ...gen.Condition) ISysRouteDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysRouteDo) Select(conds ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysRouteDo) Where(conds ...gen.Condition) ISysRouteDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysRouteDo) Order(conds ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysRouteDo) Distinct(cols ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysRouteDo) Omit(cols ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysRouteDo) Join(table schema.Tabler, on ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysRouteDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysRouteDo) RightJoin(table schema.Tabler, on ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysRouteDo) Group(cols ...field.Expr) ISysRouteDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysRouteDo) Having(conds ...gen.Condition) ISysRouteDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysRouteDo) Limit(limit int) ISysRouteDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysRouteDo) Offset(offset int) ISysRouteDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysRouteDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISysRouteDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysRouteDo) Unscoped() ISysRouteDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysRouteDo) Create(values ...*model.SysRoute) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysRouteDo) CreateInBatches(values []*model.SysRoute, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysRouteDo) Save(values ...*model.SysRoute) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysRouteDo) First() (*model.SysRoute, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoute), nil
	}
}

func (s sysRouteDo) Take() (*model.SysRoute, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoute), nil
	}
}

func (s sysRouteDo) Last() (*model.SysRoute, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoute), nil
	}
}

func (s sysRouteDo) Find() ([]*model.SysRoute, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysRoute), err
}

func (s sysRouteDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysRoute, err error) {
	buf := make([]*model.SysRoute, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysRouteDo) FindInBatches(result *[]*model.SysRoute, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysRouteDo) Attrs(attrs ...field.AssignExpr) ISysRouteDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysRouteDo) Assign(attrs ...field.AssignExpr) ISysRouteDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysRouteDo) Joins(fields ...field.RelationField) ISysRouteDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysRouteDo) Preload(fields ...field.RelationField) ISysRouteDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysRouteDo) FirstOrInit() (*model.SysRoute, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoute), nil
	}
}

func (s sysRouteDo) FirstOrCreate() (*model.SysRoute, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoute), nil
	}
}

func (s sysRouteDo) FindByPage(offset int, limit int) (result []*model.SysRoute, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysRouteDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysRouteDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysRouteDo) Delete(models ...*model.SysRoute) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysRouteDo) withDO(do gen.Dao) *sysRouteDo {
	s.DO = *do.(*gen.DO)
	return s
}
