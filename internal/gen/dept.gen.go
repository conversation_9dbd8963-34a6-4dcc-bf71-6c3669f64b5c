// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newDept(db *gorm.DB, opts ...gen.DOOption) dept {
	_dept := dept{}

	_dept.deptDo.UseDB(db, opts...)
	_dept.deptDo.UseModel(&model.Dept{})

	tableName := _dept.deptDo.TableName()
	_dept.ALL = field.NewAsterisk(tableName)
	_dept.ID = field.NewInt64(tableName, "id")
	_dept.Name = field.NewString(tableName, "name")
	_dept.ParentID = field.NewInt64(tableName, "parent_id")
	_dept.ManagerID = field.NewInt64(tableName, "manager_id")
	_dept.CreatedAt = field.NewTime(tableName, "created_at")
	_dept.UpdatedAt = field.NewTime(tableName, "updated_at")
	_dept.DeletedAt = field.NewField(tableName, "deleted_at")
	_dept.Status = field.NewString(tableName, "status")
	_dept.Sort = field.NewInt64(tableName, "sort")
	_dept.Remark = field.NewString(tableName, "remark")
	_dept.CreateID = field.NewInt64(tableName, "create_id")
	_dept.UpdateID = field.NewInt64(tableName, "update_id")
	_dept.UuID = field.NewString(tableName, "uu_id")

	_dept.fillFieldMap()

	return _dept
}

// dept 部门表，用于存储组织内的部门信息
type dept struct {
	deptDo

	ALL       field.Asterisk
	ID        field.Int64  // 主键ID
	Name      field.String // 部门名称
	ParentID  field.Int64  // 父级部门ID，顶级部门为NULL
	ManagerID field.Int64  // 部门负责人ID
	CreatedAt field.Time   // 创建时间
	UpdatedAt field.Time   // 更新时间
	DeletedAt field.Field  // 删除时间
	Status    field.String // 状态，enable表示启用，disabled表示禁用
	Sort      field.Int64  // 排序字段
	Remark    field.String // 备注或描述
	CreateID  field.Int64  // 创建者用户ID
	UpdateID  field.Int64  // 更新人id
	UuID      field.String // 唯一id

	fieldMap map[string]field.Expr
}

func (d dept) Table(newTableName string) *dept {
	d.deptDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d dept) As(alias string) *dept {
	d.deptDo.DO = *(d.deptDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *dept) updateTableName(table string) *dept {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.Name = field.NewString(table, "name")
	d.ParentID = field.NewInt64(table, "parent_id")
	d.ManagerID = field.NewInt64(table, "manager_id")
	d.CreatedAt = field.NewTime(table, "created_at")
	d.UpdatedAt = field.NewTime(table, "updated_at")
	d.DeletedAt = field.NewField(table, "deleted_at")
	d.Status = field.NewString(table, "status")
	d.Sort = field.NewInt64(table, "sort")
	d.Remark = field.NewString(table, "remark")
	d.CreateID = field.NewInt64(table, "create_id")
	d.UpdateID = field.NewInt64(table, "update_id")
	d.UuID = field.NewString(table, "uu_id")

	d.fillFieldMap()

	return d
}

func (d *dept) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *dept) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 13)
	d.fieldMap["id"] = d.ID
	d.fieldMap["name"] = d.Name
	d.fieldMap["parent_id"] = d.ParentID
	d.fieldMap["manager_id"] = d.ManagerID
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["deleted_at"] = d.DeletedAt
	d.fieldMap["status"] = d.Status
	d.fieldMap["sort"] = d.Sort
	d.fieldMap["remark"] = d.Remark
	d.fieldMap["create_id"] = d.CreateID
	d.fieldMap["update_id"] = d.UpdateID
	d.fieldMap["uu_id"] = d.UuID
}

func (d dept) clone(db *gorm.DB) dept {
	d.deptDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d dept) replaceDB(db *gorm.DB) dept {
	d.deptDo.ReplaceDB(db)
	return d
}

type deptDo struct{ gen.DO }

type IDeptDo interface {
	gen.SubQuery
	Debug() IDeptDo
	WithContext(ctx context.Context) IDeptDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDeptDo
	WriteDB() IDeptDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDeptDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDeptDo
	Not(conds ...gen.Condition) IDeptDo
	Or(conds ...gen.Condition) IDeptDo
	Select(conds ...field.Expr) IDeptDo
	Where(conds ...gen.Condition) IDeptDo
	Order(conds ...field.Expr) IDeptDo
	Distinct(cols ...field.Expr) IDeptDo
	Omit(cols ...field.Expr) IDeptDo
	Join(table schema.Tabler, on ...field.Expr) IDeptDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDeptDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDeptDo
	Group(cols ...field.Expr) IDeptDo
	Having(conds ...gen.Condition) IDeptDo
	Limit(limit int) IDeptDo
	Offset(offset int) IDeptDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDeptDo
	Unscoped() IDeptDo
	Create(values ...*model.Dept) error
	CreateInBatches(values []*model.Dept, batchSize int) error
	Save(values ...*model.Dept) error
	First() (*model.Dept, error)
	Take() (*model.Dept, error)
	Last() (*model.Dept, error)
	Find() ([]*model.Dept, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Dept, err error)
	FindInBatches(result *[]*model.Dept, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Dept) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDeptDo
	Assign(attrs ...field.AssignExpr) IDeptDo
	Joins(fields ...field.RelationField) IDeptDo
	Preload(fields ...field.RelationField) IDeptDo
	FirstOrInit() (*model.Dept, error)
	FirstOrCreate() (*model.Dept, error)
	FindByPage(offset int, limit int) (result []*model.Dept, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDeptDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d deptDo) Debug() IDeptDo {
	return d.withDO(d.DO.Debug())
}

func (d deptDo) WithContext(ctx context.Context) IDeptDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d deptDo) ReadDB() IDeptDo {
	return d.Clauses(dbresolver.Read)
}

func (d deptDo) WriteDB() IDeptDo {
	return d.Clauses(dbresolver.Write)
}

func (d deptDo) Session(config *gorm.Session) IDeptDo {
	return d.withDO(d.DO.Session(config))
}

func (d deptDo) Clauses(conds ...clause.Expression) IDeptDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d deptDo) Returning(value interface{}, columns ...string) IDeptDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d deptDo) Not(conds ...gen.Condition) IDeptDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d deptDo) Or(conds ...gen.Condition) IDeptDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d deptDo) Select(conds ...field.Expr) IDeptDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d deptDo) Where(conds ...gen.Condition) IDeptDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d deptDo) Order(conds ...field.Expr) IDeptDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d deptDo) Distinct(cols ...field.Expr) IDeptDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d deptDo) Omit(cols ...field.Expr) IDeptDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d deptDo) Join(table schema.Tabler, on ...field.Expr) IDeptDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d deptDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDeptDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d deptDo) RightJoin(table schema.Tabler, on ...field.Expr) IDeptDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d deptDo) Group(cols ...field.Expr) IDeptDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d deptDo) Having(conds ...gen.Condition) IDeptDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d deptDo) Limit(limit int) IDeptDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d deptDo) Offset(offset int) IDeptDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d deptDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDeptDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d deptDo) Unscoped() IDeptDo {
	return d.withDO(d.DO.Unscoped())
}

func (d deptDo) Create(values ...*model.Dept) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d deptDo) CreateInBatches(values []*model.Dept, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d deptDo) Save(values ...*model.Dept) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d deptDo) First() (*model.Dept, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dept), nil
	}
}

func (d deptDo) Take() (*model.Dept, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dept), nil
	}
}

func (d deptDo) Last() (*model.Dept, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dept), nil
	}
}

func (d deptDo) Find() ([]*model.Dept, error) {
	result, err := d.DO.Find()
	return result.([]*model.Dept), err
}

func (d deptDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Dept, err error) {
	buf := make([]*model.Dept, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d deptDo) FindInBatches(result *[]*model.Dept, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d deptDo) Attrs(attrs ...field.AssignExpr) IDeptDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d deptDo) Assign(attrs ...field.AssignExpr) IDeptDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d deptDo) Joins(fields ...field.RelationField) IDeptDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d deptDo) Preload(fields ...field.RelationField) IDeptDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d deptDo) FirstOrInit() (*model.Dept, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dept), nil
	}
}

func (d deptDo) FirstOrCreate() (*model.Dept, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dept), nil
	}
}

func (d deptDo) FindByPage(offset int, limit int) (result []*model.Dept, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d deptDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d deptDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d deptDo) Delete(models ...*model.Dept) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *deptDo) withDO(do gen.Dao) *deptDo {
	d.DO = *do.(*gen.DO)
	return d
}
