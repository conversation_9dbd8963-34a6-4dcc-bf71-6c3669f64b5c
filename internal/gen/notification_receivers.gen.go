// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newNotificationReceiver(db *gorm.DB, opts ...gen.DOOption) notificationReceiver {
	_notificationReceiver := notificationReceiver{}

	_notificationReceiver.notificationReceiverDo.UseDB(db, opts...)
	_notificationReceiver.notificationReceiverDo.UseModel(&model.NotificationReceiver{})

	tableName := _notificationReceiver.notificationReceiverDo.TableName()
	_notificationReceiver.ALL = field.NewAsterisk(tableName)
	_notificationReceiver.ID = field.NewInt64(tableName, "id")
	_notificationReceiver.NotificationID = field.NewString(tableName, "notification_id")
	_notificationReceiver.ReceiverID = field.NewString(tableName, "receiver_id")
	_notificationReceiver.ReadStatus = field.NewString(tableName, "read_status")
	_notificationReceiver.ReadTime = field.NewTime(tableName, "read_time")
	_notificationReceiver.CreatedAt = field.NewTime(tableName, "created_at")
	_notificationReceiver.UUID = field.NewString(tableName, "uuid")
	_notificationReceiver.TenantID = field.NewString(tableName, "tenant_id")

	_notificationReceiver.fillFieldMap()

	return _notificationReceiver
}

// notificationReceiver 公告接收记录表
type notificationReceiver struct {
	notificationReceiverDo

	ALL            field.Asterisk
	ID             field.Int64  // 主键ID
	NotificationID field.String // 关联的公告ID
	ReceiverID     field.String // 接收者用户ID
	ReadStatus     field.String // 阅读状态：unread=未读, read=已读
	ReadTime       field.Time   // 阅读时间
	CreatedAt      field.Time   // 创建时间
	UUID           field.String // 唯一ID
	TenantID       field.String // 租户ID

	fieldMap map[string]field.Expr
}

func (n notificationReceiver) Table(newTableName string) *notificationReceiver {
	n.notificationReceiverDo.UseTable(newTableName)
	return n.updateTableName(newTableName)
}

func (n notificationReceiver) As(alias string) *notificationReceiver {
	n.notificationReceiverDo.DO = *(n.notificationReceiverDo.As(alias).(*gen.DO))
	return n.updateTableName(alias)
}

func (n *notificationReceiver) updateTableName(table string) *notificationReceiver {
	n.ALL = field.NewAsterisk(table)
	n.ID = field.NewInt64(table, "id")
	n.NotificationID = field.NewString(table, "notification_id")
	n.ReceiverID = field.NewString(table, "receiver_id")
	n.ReadStatus = field.NewString(table, "read_status")
	n.ReadTime = field.NewTime(table, "read_time")
	n.CreatedAt = field.NewTime(table, "created_at")
	n.UUID = field.NewString(table, "uuid")
	n.TenantID = field.NewString(table, "tenant_id")

	n.fillFieldMap()

	return n
}

func (n *notificationReceiver) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := n.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (n *notificationReceiver) fillFieldMap() {
	n.fieldMap = make(map[string]field.Expr, 8)
	n.fieldMap["id"] = n.ID
	n.fieldMap["notification_id"] = n.NotificationID
	n.fieldMap["receiver_id"] = n.ReceiverID
	n.fieldMap["read_status"] = n.ReadStatus
	n.fieldMap["read_time"] = n.ReadTime
	n.fieldMap["created_at"] = n.CreatedAt
	n.fieldMap["uuid"] = n.UUID
	n.fieldMap["tenant_id"] = n.TenantID
}

func (n notificationReceiver) clone(db *gorm.DB) notificationReceiver {
	n.notificationReceiverDo.ReplaceConnPool(db.Statement.ConnPool)
	return n
}

func (n notificationReceiver) replaceDB(db *gorm.DB) notificationReceiver {
	n.notificationReceiverDo.ReplaceDB(db)
	return n
}

type notificationReceiverDo struct{ gen.DO }

type INotificationReceiverDo interface {
	gen.SubQuery
	Debug() INotificationReceiverDo
	WithContext(ctx context.Context) INotificationReceiverDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() INotificationReceiverDo
	WriteDB() INotificationReceiverDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) INotificationReceiverDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) INotificationReceiverDo
	Not(conds ...gen.Condition) INotificationReceiverDo
	Or(conds ...gen.Condition) INotificationReceiverDo
	Select(conds ...field.Expr) INotificationReceiverDo
	Where(conds ...gen.Condition) INotificationReceiverDo
	Order(conds ...field.Expr) INotificationReceiverDo
	Distinct(cols ...field.Expr) INotificationReceiverDo
	Omit(cols ...field.Expr) INotificationReceiverDo
	Join(table schema.Tabler, on ...field.Expr) INotificationReceiverDo
	LeftJoin(table schema.Tabler, on ...field.Expr) INotificationReceiverDo
	RightJoin(table schema.Tabler, on ...field.Expr) INotificationReceiverDo
	Group(cols ...field.Expr) INotificationReceiverDo
	Having(conds ...gen.Condition) INotificationReceiverDo
	Limit(limit int) INotificationReceiverDo
	Offset(offset int) INotificationReceiverDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) INotificationReceiverDo
	Unscoped() INotificationReceiverDo
	Create(values ...*model.NotificationReceiver) error
	CreateInBatches(values []*model.NotificationReceiver, batchSize int) error
	Save(values ...*model.NotificationReceiver) error
	First() (*model.NotificationReceiver, error)
	Take() (*model.NotificationReceiver, error)
	Last() (*model.NotificationReceiver, error)
	Find() ([]*model.NotificationReceiver, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.NotificationReceiver, err error)
	FindInBatches(result *[]*model.NotificationReceiver, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.NotificationReceiver) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) INotificationReceiverDo
	Assign(attrs ...field.AssignExpr) INotificationReceiverDo
	Joins(fields ...field.RelationField) INotificationReceiverDo
	Preload(fields ...field.RelationField) INotificationReceiverDo
	FirstOrInit() (*model.NotificationReceiver, error)
	FirstOrCreate() (*model.NotificationReceiver, error)
	FindByPage(offset int, limit int) (result []*model.NotificationReceiver, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) INotificationReceiverDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (n notificationReceiverDo) Debug() INotificationReceiverDo {
	return n.withDO(n.DO.Debug())
}

func (n notificationReceiverDo) WithContext(ctx context.Context) INotificationReceiverDo {
	return n.withDO(n.DO.WithContext(ctx))
}

func (n notificationReceiverDo) ReadDB() INotificationReceiverDo {
	return n.Clauses(dbresolver.Read)
}

func (n notificationReceiverDo) WriteDB() INotificationReceiverDo {
	return n.Clauses(dbresolver.Write)
}

func (n notificationReceiverDo) Session(config *gorm.Session) INotificationReceiverDo {
	return n.withDO(n.DO.Session(config))
}

func (n notificationReceiverDo) Clauses(conds ...clause.Expression) INotificationReceiverDo {
	return n.withDO(n.DO.Clauses(conds...))
}

func (n notificationReceiverDo) Returning(value interface{}, columns ...string) INotificationReceiverDo {
	return n.withDO(n.DO.Returning(value, columns...))
}

func (n notificationReceiverDo) Not(conds ...gen.Condition) INotificationReceiverDo {
	return n.withDO(n.DO.Not(conds...))
}

func (n notificationReceiverDo) Or(conds ...gen.Condition) INotificationReceiverDo {
	return n.withDO(n.DO.Or(conds...))
}

func (n notificationReceiverDo) Select(conds ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.Select(conds...))
}

func (n notificationReceiverDo) Where(conds ...gen.Condition) INotificationReceiverDo {
	return n.withDO(n.DO.Where(conds...))
}

func (n notificationReceiverDo) Order(conds ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.Order(conds...))
}

func (n notificationReceiverDo) Distinct(cols ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.Distinct(cols...))
}

func (n notificationReceiverDo) Omit(cols ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.Omit(cols...))
}

func (n notificationReceiverDo) Join(table schema.Tabler, on ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.Join(table, on...))
}

func (n notificationReceiverDo) LeftJoin(table schema.Tabler, on ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.LeftJoin(table, on...))
}

func (n notificationReceiverDo) RightJoin(table schema.Tabler, on ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.RightJoin(table, on...))
}

func (n notificationReceiverDo) Group(cols ...field.Expr) INotificationReceiverDo {
	return n.withDO(n.DO.Group(cols...))
}

func (n notificationReceiverDo) Having(conds ...gen.Condition) INotificationReceiverDo {
	return n.withDO(n.DO.Having(conds...))
}

func (n notificationReceiverDo) Limit(limit int) INotificationReceiverDo {
	return n.withDO(n.DO.Limit(limit))
}

func (n notificationReceiverDo) Offset(offset int) INotificationReceiverDo {
	return n.withDO(n.DO.Offset(offset))
}

func (n notificationReceiverDo) Scopes(funcs ...func(gen.Dao) gen.Dao) INotificationReceiverDo {
	return n.withDO(n.DO.Scopes(funcs...))
}

func (n notificationReceiverDo) Unscoped() INotificationReceiverDo {
	return n.withDO(n.DO.Unscoped())
}

func (n notificationReceiverDo) Create(values ...*model.NotificationReceiver) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Create(values)
}

func (n notificationReceiverDo) CreateInBatches(values []*model.NotificationReceiver, batchSize int) error {
	return n.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (n notificationReceiverDo) Save(values ...*model.NotificationReceiver) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Save(values)
}

func (n notificationReceiverDo) First() (*model.NotificationReceiver, error) {
	if result, err := n.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.NotificationReceiver), nil
	}
}

func (n notificationReceiverDo) Take() (*model.NotificationReceiver, error) {
	if result, err := n.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.NotificationReceiver), nil
	}
}

func (n notificationReceiverDo) Last() (*model.NotificationReceiver, error) {
	if result, err := n.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.NotificationReceiver), nil
	}
}

func (n notificationReceiverDo) Find() ([]*model.NotificationReceiver, error) {
	result, err := n.DO.Find()
	return result.([]*model.NotificationReceiver), err
}

func (n notificationReceiverDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.NotificationReceiver, err error) {
	buf := make([]*model.NotificationReceiver, 0, batchSize)
	err = n.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (n notificationReceiverDo) FindInBatches(result *[]*model.NotificationReceiver, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return n.DO.FindInBatches(result, batchSize, fc)
}

func (n notificationReceiverDo) Attrs(attrs ...field.AssignExpr) INotificationReceiverDo {
	return n.withDO(n.DO.Attrs(attrs...))
}

func (n notificationReceiverDo) Assign(attrs ...field.AssignExpr) INotificationReceiverDo {
	return n.withDO(n.DO.Assign(attrs...))
}

func (n notificationReceiverDo) Joins(fields ...field.RelationField) INotificationReceiverDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Joins(_f))
	}
	return &n
}

func (n notificationReceiverDo) Preload(fields ...field.RelationField) INotificationReceiverDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Preload(_f))
	}
	return &n
}

func (n notificationReceiverDo) FirstOrInit() (*model.NotificationReceiver, error) {
	if result, err := n.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.NotificationReceiver), nil
	}
}

func (n notificationReceiverDo) FirstOrCreate() (*model.NotificationReceiver, error) {
	if result, err := n.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.NotificationReceiver), nil
	}
}

func (n notificationReceiverDo) FindByPage(offset int, limit int) (result []*model.NotificationReceiver, count int64, err error) {
	result, err = n.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = n.Offset(-1).Limit(-1).Count()
	return
}

func (n notificationReceiverDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = n.Count()
	if err != nil {
		return
	}

	err = n.Offset(offset).Limit(limit).Scan(result)
	return
}

func (n notificationReceiverDo) Scan(result interface{}) (err error) {
	return n.DO.Scan(result)
}

func (n notificationReceiverDo) Delete(models ...*model.NotificationReceiver) (result gen.ResultInfo, err error) {
	return n.DO.Delete(models)
}

func (n *notificationReceiverDo) withDO(do gen.Dao) *notificationReceiverDo {
	n.DO = *do.(*gen.DO)
	return n
}
