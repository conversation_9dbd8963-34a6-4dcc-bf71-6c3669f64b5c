// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newSysRouteMetum(db *gorm.DB, opts ...gen.DOOption) sysRouteMetum {
	_sysRouteMetum := sysRouteMetum{}

	_sysRouteMetum.sysRouteMetumDo.UseDB(db, opts...)
	_sysRouteMetum.sysRouteMetumDo.UseModel(&model.SysRouteMetum{})

	tableName := _sysRouteMetum.sysRouteMetumDo.TableName()
	_sysRouteMetum.ALL = field.NewAsterisk(tableName)
	_sysRouteMetum.ID = field.NewInt64(tableName, "id")
	_sysRouteMetum.RouteID = field.NewInt64(tableName, "route_id")
	_sysRouteMetum.Title = field.NewString(tableName, "title")
	_sysRouteMetum.Icon = field.NewString(tableName, "icon")
	_sysRouteMetum.Hidden = field.NewInt64(tableName, "hidden")
	_sysRouteMetum.ActiveMenu = field.NewString(tableName, "active_menu")
	_sysRouteMetum.KeepAlive = field.NewInt64(tableName, "keep_alive")
	_sysRouteMetum.Auth = field.NewString(tableName, "auth")
	_sysRouteMetum.Breadcrumb = field.NewInt64(tableName, "breadcrumb")
	_sysRouteMetum.External = field.NewInt64(tableName, "external")
	_sysRouteMetum.ExternalURL = field.NewString(tableName, "external_url")

	_sysRouteMetum.fillFieldMap()

	return _sysRouteMetum
}

// sysRouteMetum 路由元信息表
type sysRouteMetum struct {
	sysRouteMetumDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键
	RouteID     field.Int64  // 关联的路由ID
	Title       field.String // 路由标题
	Icon        field.String // 导航图标
	Hidden      field.Int64  // 是否隐藏菜单（1是 0否）
	ActiveMenu  field.String // 高亮菜单路径
	KeepAlive   field.Int64  // 是否缓存页面（1是 0否）
	Auth        field.String // 访问权限（逗号分隔）
	Breadcrumb  field.Int64  // 是否显示面包屑（1是 0否）
	External    field.Int64  // 是否外部链接（1是 0否）
	ExternalURL field.String // 外部链接地址

	fieldMap map[string]field.Expr
}

func (s sysRouteMetum) Table(newTableName string) *sysRouteMetum {
	s.sysRouteMetumDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysRouteMetum) As(alias string) *sysRouteMetum {
	s.sysRouteMetumDo.DO = *(s.sysRouteMetumDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysRouteMetum) updateTableName(table string) *sysRouteMetum {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.RouteID = field.NewInt64(table, "route_id")
	s.Title = field.NewString(table, "title")
	s.Icon = field.NewString(table, "icon")
	s.Hidden = field.NewInt64(table, "hidden")
	s.ActiveMenu = field.NewString(table, "active_menu")
	s.KeepAlive = field.NewInt64(table, "keep_alive")
	s.Auth = field.NewString(table, "auth")
	s.Breadcrumb = field.NewInt64(table, "breadcrumb")
	s.External = field.NewInt64(table, "external")
	s.ExternalURL = field.NewString(table, "external_url")

	s.fillFieldMap()

	return s
}

func (s *sysRouteMetum) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysRouteMetum) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 11)
	s.fieldMap["id"] = s.ID
	s.fieldMap["route_id"] = s.RouteID
	s.fieldMap["title"] = s.Title
	s.fieldMap["icon"] = s.Icon
	s.fieldMap["hidden"] = s.Hidden
	s.fieldMap["active_menu"] = s.ActiveMenu
	s.fieldMap["keep_alive"] = s.KeepAlive
	s.fieldMap["auth"] = s.Auth
	s.fieldMap["breadcrumb"] = s.Breadcrumb
	s.fieldMap["external"] = s.External
	s.fieldMap["external_url"] = s.ExternalURL
}

func (s sysRouteMetum) clone(db *gorm.DB) sysRouteMetum {
	s.sysRouteMetumDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysRouteMetum) replaceDB(db *gorm.DB) sysRouteMetum {
	s.sysRouteMetumDo.ReplaceDB(db)
	return s
}

type sysRouteMetumDo struct{ gen.DO }

type ISysRouteMetumDo interface {
	gen.SubQuery
	Debug() ISysRouteMetumDo
	WithContext(ctx context.Context) ISysRouteMetumDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISysRouteMetumDo
	WriteDB() ISysRouteMetumDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISysRouteMetumDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISysRouteMetumDo
	Not(conds ...gen.Condition) ISysRouteMetumDo
	Or(conds ...gen.Condition) ISysRouteMetumDo
	Select(conds ...field.Expr) ISysRouteMetumDo
	Where(conds ...gen.Condition) ISysRouteMetumDo
	Order(conds ...field.Expr) ISysRouteMetumDo
	Distinct(cols ...field.Expr) ISysRouteMetumDo
	Omit(cols ...field.Expr) ISysRouteMetumDo
	Join(table schema.Tabler, on ...field.Expr) ISysRouteMetumDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISysRouteMetumDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISysRouteMetumDo
	Group(cols ...field.Expr) ISysRouteMetumDo
	Having(conds ...gen.Condition) ISysRouteMetumDo
	Limit(limit int) ISysRouteMetumDo
	Offset(offset int) ISysRouteMetumDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISysRouteMetumDo
	Unscoped() ISysRouteMetumDo
	Create(values ...*model.SysRouteMetum) error
	CreateInBatches(values []*model.SysRouteMetum, batchSize int) error
	Save(values ...*model.SysRouteMetum) error
	First() (*model.SysRouteMetum, error)
	Take() (*model.SysRouteMetum, error)
	Last() (*model.SysRouteMetum, error)
	Find() ([]*model.SysRouteMetum, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysRouteMetum, err error)
	FindInBatches(result *[]*model.SysRouteMetum, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SysRouteMetum) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISysRouteMetumDo
	Assign(attrs ...field.AssignExpr) ISysRouteMetumDo
	Joins(fields ...field.RelationField) ISysRouteMetumDo
	Preload(fields ...field.RelationField) ISysRouteMetumDo
	FirstOrInit() (*model.SysRouteMetum, error)
	FirstOrCreate() (*model.SysRouteMetum, error)
	FindByPage(offset int, limit int) (result []*model.SysRouteMetum, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISysRouteMetumDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s sysRouteMetumDo) Debug() ISysRouteMetumDo {
	return s.withDO(s.DO.Debug())
}

func (s sysRouteMetumDo) WithContext(ctx context.Context) ISysRouteMetumDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysRouteMetumDo) ReadDB() ISysRouteMetumDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysRouteMetumDo) WriteDB() ISysRouteMetumDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysRouteMetumDo) Session(config *gorm.Session) ISysRouteMetumDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysRouteMetumDo) Clauses(conds ...clause.Expression) ISysRouteMetumDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysRouteMetumDo) Returning(value interface{}, columns ...string) ISysRouteMetumDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysRouteMetumDo) Not(conds ...gen.Condition) ISysRouteMetumDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysRouteMetumDo) Or(conds ...gen.Condition) ISysRouteMetumDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysRouteMetumDo) Select(conds ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysRouteMetumDo) Where(conds ...gen.Condition) ISysRouteMetumDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysRouteMetumDo) Order(conds ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysRouteMetumDo) Distinct(cols ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysRouteMetumDo) Omit(cols ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysRouteMetumDo) Join(table schema.Tabler, on ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysRouteMetumDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysRouteMetumDo) RightJoin(table schema.Tabler, on ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysRouteMetumDo) Group(cols ...field.Expr) ISysRouteMetumDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysRouteMetumDo) Having(conds ...gen.Condition) ISysRouteMetumDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysRouteMetumDo) Limit(limit int) ISysRouteMetumDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysRouteMetumDo) Offset(offset int) ISysRouteMetumDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysRouteMetumDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISysRouteMetumDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysRouteMetumDo) Unscoped() ISysRouteMetumDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysRouteMetumDo) Create(values ...*model.SysRouteMetum) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysRouteMetumDo) CreateInBatches(values []*model.SysRouteMetum, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysRouteMetumDo) Save(values ...*model.SysRouteMetum) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysRouteMetumDo) First() (*model.SysRouteMetum, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRouteMetum), nil
	}
}

func (s sysRouteMetumDo) Take() (*model.SysRouteMetum, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRouteMetum), nil
	}
}

func (s sysRouteMetumDo) Last() (*model.SysRouteMetum, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRouteMetum), nil
	}
}

func (s sysRouteMetumDo) Find() ([]*model.SysRouteMetum, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysRouteMetum), err
}

func (s sysRouteMetumDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysRouteMetum, err error) {
	buf := make([]*model.SysRouteMetum, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysRouteMetumDo) FindInBatches(result *[]*model.SysRouteMetum, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysRouteMetumDo) Attrs(attrs ...field.AssignExpr) ISysRouteMetumDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysRouteMetumDo) Assign(attrs ...field.AssignExpr) ISysRouteMetumDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysRouteMetumDo) Joins(fields ...field.RelationField) ISysRouteMetumDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysRouteMetumDo) Preload(fields ...field.RelationField) ISysRouteMetumDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysRouteMetumDo) FirstOrInit() (*model.SysRouteMetum, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRouteMetum), nil
	}
}

func (s sysRouteMetumDo) FirstOrCreate() (*model.SysRouteMetum, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRouteMetum), nil
	}
}

func (s sysRouteMetumDo) FindByPage(offset int, limit int) (result []*model.SysRouteMetum, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysRouteMetumDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysRouteMetumDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysRouteMetumDo) Delete(models ...*model.SysRouteMetum) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysRouteMetumDo) withDO(do gen.Dao) *sysRouteMetumDo {
	s.DO = *do.(*gen.DO)
	return s
}
