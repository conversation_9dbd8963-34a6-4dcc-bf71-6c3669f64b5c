// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newSystemOperationLog(db *gorm.DB, opts ...gen.DOOption) systemOperationLog {
	_systemOperationLog := systemOperationLog{}

	_systemOperationLog.systemOperationLogDo.UseDB(db, opts...)
	_systemOperationLog.systemOperationLogDo.UseModel(&model.SystemOperationLog{})

	tableName := _systemOperationLog.systemOperationLogDo.TableName()
	_systemOperationLog.ALL = field.NewAsterisk(tableName)
	_systemOperationLog.ID = field.NewUint64(tableName, "id")
	_systemOperationLog.CreateTime = field.NewTime(tableName, "create_time")
	_systemOperationLog.LogTimestamp = field.NewTime(tableName, "log_timestamp")
	_systemOperationLog.LogLevel = field.NewString(tableName, "log_level")
	_systemOperationLog.LogSource = field.NewString(tableName, "log_source")
	_systemOperationLog.LogMessage = field.NewString(tableName, "log_message")
	_systemOperationLog.UserID = field.NewUint64(tableName, "user_id")
	_systemOperationLog.OperationType = field.NewString(tableName, "operation_type")
	_systemOperationLog.ResponseTime = field.NewFloat64(tableName, "response_time")
	_systemOperationLog.UUID = field.NewString(tableName, "uuid")
	_systemOperationLog.TenantID = field.NewString(tableName, "tenant_id")

	_systemOperationLog.fillFieldMap()

	return _systemOperationLog
}

// systemOperationLog 日志表
type systemOperationLog struct {
	systemOperationLogDo

	ALL           field.Asterisk
	ID            field.Uint64
	CreateTime    field.Time    // 日志创建时间，自动记录插入时刻
	LogTimestamp  field.Time    // 操作发生的时间戳，由应用程序设定
	LogLevel      field.String  // 日志级别，例如 'DEBUG', 'INFO', 'WARN', 'ERROR'
	LogSource     field.String  // 日志来源，例如模块名称或类名
	LogMessage    field.String  // 日志详细信息
	UserID        field.Uint64  // 执行操作的用户ID，关联 users 表
	OperationType field.String  // 操作类型，如 'CREATE', 'UPDATE', 'DELETE' 等
	ResponseTime  field.Float64 // 请求响应时间，精确到微秒
	UUID          field.String  // 唯一ID
	TenantID      field.String  // 租户ID

	fieldMap map[string]field.Expr
}

func (s systemOperationLog) Table(newTableName string) *systemOperationLog {
	s.systemOperationLogDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s systemOperationLog) As(alias string) *systemOperationLog {
	s.systemOperationLogDo.DO = *(s.systemOperationLogDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *systemOperationLog) updateTableName(table string) *systemOperationLog {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewUint64(table, "id")
	s.CreateTime = field.NewTime(table, "create_time")
	s.LogTimestamp = field.NewTime(table, "log_timestamp")
	s.LogLevel = field.NewString(table, "log_level")
	s.LogSource = field.NewString(table, "log_source")
	s.LogMessage = field.NewString(table, "log_message")
	s.UserID = field.NewUint64(table, "user_id")
	s.OperationType = field.NewString(table, "operation_type")
	s.ResponseTime = field.NewFloat64(table, "response_time")
	s.UUID = field.NewString(table, "uuid")
	s.TenantID = field.NewString(table, "tenant_id")

	s.fillFieldMap()

	return s
}

func (s *systemOperationLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *systemOperationLog) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 11)
	s.fieldMap["id"] = s.ID
	s.fieldMap["create_time"] = s.CreateTime
	s.fieldMap["log_timestamp"] = s.LogTimestamp
	s.fieldMap["log_level"] = s.LogLevel
	s.fieldMap["log_source"] = s.LogSource
	s.fieldMap["log_message"] = s.LogMessage
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["operation_type"] = s.OperationType
	s.fieldMap["response_time"] = s.ResponseTime
	s.fieldMap["uuid"] = s.UUID
	s.fieldMap["tenant_id"] = s.TenantID
}

func (s systemOperationLog) clone(db *gorm.DB) systemOperationLog {
	s.systemOperationLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s systemOperationLog) replaceDB(db *gorm.DB) systemOperationLog {
	s.systemOperationLogDo.ReplaceDB(db)
	return s
}

type systemOperationLogDo struct{ gen.DO }

type ISystemOperationLogDo interface {
	gen.SubQuery
	Debug() ISystemOperationLogDo
	WithContext(ctx context.Context) ISystemOperationLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISystemOperationLogDo
	WriteDB() ISystemOperationLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISystemOperationLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISystemOperationLogDo
	Not(conds ...gen.Condition) ISystemOperationLogDo
	Or(conds ...gen.Condition) ISystemOperationLogDo
	Select(conds ...field.Expr) ISystemOperationLogDo
	Where(conds ...gen.Condition) ISystemOperationLogDo
	Order(conds ...field.Expr) ISystemOperationLogDo
	Distinct(cols ...field.Expr) ISystemOperationLogDo
	Omit(cols ...field.Expr) ISystemOperationLogDo
	Join(table schema.Tabler, on ...field.Expr) ISystemOperationLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISystemOperationLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISystemOperationLogDo
	Group(cols ...field.Expr) ISystemOperationLogDo
	Having(conds ...gen.Condition) ISystemOperationLogDo
	Limit(limit int) ISystemOperationLogDo
	Offset(offset int) ISystemOperationLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISystemOperationLogDo
	Unscoped() ISystemOperationLogDo
	Create(values ...*model.SystemOperationLog) error
	CreateInBatches(values []*model.SystemOperationLog, batchSize int) error
	Save(values ...*model.SystemOperationLog) error
	First() (*model.SystemOperationLog, error)
	Take() (*model.SystemOperationLog, error)
	Last() (*model.SystemOperationLog, error)
	Find() ([]*model.SystemOperationLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SystemOperationLog, err error)
	FindInBatches(result *[]*model.SystemOperationLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SystemOperationLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISystemOperationLogDo
	Assign(attrs ...field.AssignExpr) ISystemOperationLogDo
	Joins(fields ...field.RelationField) ISystemOperationLogDo
	Preload(fields ...field.RelationField) ISystemOperationLogDo
	FirstOrInit() (*model.SystemOperationLog, error)
	FirstOrCreate() (*model.SystemOperationLog, error)
	FindByPage(offset int, limit int) (result []*model.SystemOperationLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISystemOperationLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s systemOperationLogDo) Debug() ISystemOperationLogDo {
	return s.withDO(s.DO.Debug())
}

func (s systemOperationLogDo) WithContext(ctx context.Context) ISystemOperationLogDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s systemOperationLogDo) ReadDB() ISystemOperationLogDo {
	return s.Clauses(dbresolver.Read)
}

func (s systemOperationLogDo) WriteDB() ISystemOperationLogDo {
	return s.Clauses(dbresolver.Write)
}

func (s systemOperationLogDo) Session(config *gorm.Session) ISystemOperationLogDo {
	return s.withDO(s.DO.Session(config))
}

func (s systemOperationLogDo) Clauses(conds ...clause.Expression) ISystemOperationLogDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s systemOperationLogDo) Returning(value interface{}, columns ...string) ISystemOperationLogDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s systemOperationLogDo) Not(conds ...gen.Condition) ISystemOperationLogDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s systemOperationLogDo) Or(conds ...gen.Condition) ISystemOperationLogDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s systemOperationLogDo) Select(conds ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s systemOperationLogDo) Where(conds ...gen.Condition) ISystemOperationLogDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s systemOperationLogDo) Order(conds ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s systemOperationLogDo) Distinct(cols ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s systemOperationLogDo) Omit(cols ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s systemOperationLogDo) Join(table schema.Tabler, on ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s systemOperationLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s systemOperationLogDo) RightJoin(table schema.Tabler, on ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s systemOperationLogDo) Group(cols ...field.Expr) ISystemOperationLogDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s systemOperationLogDo) Having(conds ...gen.Condition) ISystemOperationLogDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s systemOperationLogDo) Limit(limit int) ISystemOperationLogDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s systemOperationLogDo) Offset(offset int) ISystemOperationLogDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s systemOperationLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISystemOperationLogDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s systemOperationLogDo) Unscoped() ISystemOperationLogDo {
	return s.withDO(s.DO.Unscoped())
}

func (s systemOperationLogDo) Create(values ...*model.SystemOperationLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s systemOperationLogDo) CreateInBatches(values []*model.SystemOperationLog, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s systemOperationLogDo) Save(values ...*model.SystemOperationLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s systemOperationLogDo) First() (*model.SystemOperationLog, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SystemOperationLog), nil
	}
}

func (s systemOperationLogDo) Take() (*model.SystemOperationLog, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SystemOperationLog), nil
	}
}

func (s systemOperationLogDo) Last() (*model.SystemOperationLog, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SystemOperationLog), nil
	}
}

func (s systemOperationLogDo) Find() ([]*model.SystemOperationLog, error) {
	result, err := s.DO.Find()
	return result.([]*model.SystemOperationLog), err
}

func (s systemOperationLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SystemOperationLog, err error) {
	buf := make([]*model.SystemOperationLog, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s systemOperationLogDo) FindInBatches(result *[]*model.SystemOperationLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s systemOperationLogDo) Attrs(attrs ...field.AssignExpr) ISystemOperationLogDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s systemOperationLogDo) Assign(attrs ...field.AssignExpr) ISystemOperationLogDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s systemOperationLogDo) Joins(fields ...field.RelationField) ISystemOperationLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s systemOperationLogDo) Preload(fields ...field.RelationField) ISystemOperationLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s systemOperationLogDo) FirstOrInit() (*model.SystemOperationLog, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SystemOperationLog), nil
	}
}

func (s systemOperationLogDo) FirstOrCreate() (*model.SystemOperationLog, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SystemOperationLog), nil
	}
}

func (s systemOperationLogDo) FindByPage(offset int, limit int) (result []*model.SystemOperationLog, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s systemOperationLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s systemOperationLogDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s systemOperationLogDo) Delete(models ...*model.SystemOperationLog) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *systemOperationLogDo) withDO(do gen.Dao) *systemOperationLogDo {
	s.DO = *do.(*gen.DO)
	return s
}
