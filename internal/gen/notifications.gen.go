// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newNotification(db *gorm.DB, opts ...gen.DOOption) notification {
	_notification := notification{}

	_notification.notificationDo.UseDB(db, opts...)
	_notification.notificationDo.UseModel(&model.Notification{})

	tableName := _notification.notificationDo.TableName()
	_notification.ALL = field.NewAsterisk(tableName)
	_notification.ID = field.NewInt64(tableName, "id")
	_notification.Title = field.NewString(tableName, "title")
	_notification.Content = field.NewString(tableName, "content")
	_notification.SenderID = field.NewInt64(tableName, "sender_id")
	_notification.SenderType = field.NewString(tableName, "sender_type")
	_notification.SendType = field.NewString(tableName, "send_type")
	_notification.TargetID = field.NewInt64(tableName, "target_id")
	_notification.IsScheduled = field.NewBool(tableName, "is_scheduled")
	_notification.SendTime = field.NewTime(tableName, "send_time")
	_notification.ExpireTime = field.NewTime(tableName, "expire_time")
	_notification.IsExpired = field.NewString(tableName, "is_expired")
	_notification.Status = field.NewString(tableName, "status")
	_notification.CreatedAt = field.NewTime(tableName, "created_at")
	_notification.UpdatedAt = field.NewTime(tableName, "updated_at")

	_notification.fillFieldMap()

	return _notification
}

// notification 公告主表
type notification struct {
	notificationDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键ID
	Title       field.String // 公告标题
	Content     field.String // 公告内容
	SenderID    field.Int64  // 发送者ID（系统发送为0）
	SenderType  field.String // 发送者类型：system=系统, admin=管理员, other=其他
	SendType    field.String // 发送类型：broadcast=全体, group=分组, private=私信
	TargetID    field.Int64  // 目标ID（用户ID或分组ID）
	IsScheduled field.Bool   // 是否定时发送（0=否，1=是）
	SendTime    field.Time   // 计划发送时间
	ExpireTime  field.Time   // 过期时间（NULL表示永不过期）
	IsExpired   field.String // 过期状态：active=有效, expired=已过期
	Status      field.String // 发送状态：pending=待发送, sent=已发送, canceled=已取消
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (n notification) Table(newTableName string) *notification {
	n.notificationDo.UseTable(newTableName)
	return n.updateTableName(newTableName)
}

func (n notification) As(alias string) *notification {
	n.notificationDo.DO = *(n.notificationDo.As(alias).(*gen.DO))
	return n.updateTableName(alias)
}

func (n *notification) updateTableName(table string) *notification {
	n.ALL = field.NewAsterisk(table)
	n.ID = field.NewInt64(table, "id")
	n.Title = field.NewString(table, "title")
	n.Content = field.NewString(table, "content")
	n.SenderID = field.NewInt64(table, "sender_id")
	n.SenderType = field.NewString(table, "sender_type")
	n.SendType = field.NewString(table, "send_type")
	n.TargetID = field.NewInt64(table, "target_id")
	n.IsScheduled = field.NewBool(table, "is_scheduled")
	n.SendTime = field.NewTime(table, "send_time")
	n.ExpireTime = field.NewTime(table, "expire_time")
	n.IsExpired = field.NewString(table, "is_expired")
	n.Status = field.NewString(table, "status")
	n.CreatedAt = field.NewTime(table, "created_at")
	n.UpdatedAt = field.NewTime(table, "updated_at")

	n.fillFieldMap()

	return n
}

func (n *notification) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := n.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (n *notification) fillFieldMap() {
	n.fieldMap = make(map[string]field.Expr, 14)
	n.fieldMap["id"] = n.ID
	n.fieldMap["title"] = n.Title
	n.fieldMap["content"] = n.Content
	n.fieldMap["sender_id"] = n.SenderID
	n.fieldMap["sender_type"] = n.SenderType
	n.fieldMap["send_type"] = n.SendType
	n.fieldMap["target_id"] = n.TargetID
	n.fieldMap["is_scheduled"] = n.IsScheduled
	n.fieldMap["send_time"] = n.SendTime
	n.fieldMap["expire_time"] = n.ExpireTime
	n.fieldMap["is_expired"] = n.IsExpired
	n.fieldMap["status"] = n.Status
	n.fieldMap["created_at"] = n.CreatedAt
	n.fieldMap["updated_at"] = n.UpdatedAt
}

func (n notification) clone(db *gorm.DB) notification {
	n.notificationDo.ReplaceConnPool(db.Statement.ConnPool)
	return n
}

func (n notification) replaceDB(db *gorm.DB) notification {
	n.notificationDo.ReplaceDB(db)
	return n
}

type notificationDo struct{ gen.DO }

type INotificationDo interface {
	gen.SubQuery
	Debug() INotificationDo
	WithContext(ctx context.Context) INotificationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() INotificationDo
	WriteDB() INotificationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) INotificationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) INotificationDo
	Not(conds ...gen.Condition) INotificationDo
	Or(conds ...gen.Condition) INotificationDo
	Select(conds ...field.Expr) INotificationDo
	Where(conds ...gen.Condition) INotificationDo
	Order(conds ...field.Expr) INotificationDo
	Distinct(cols ...field.Expr) INotificationDo
	Omit(cols ...field.Expr) INotificationDo
	Join(table schema.Tabler, on ...field.Expr) INotificationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) INotificationDo
	RightJoin(table schema.Tabler, on ...field.Expr) INotificationDo
	Group(cols ...field.Expr) INotificationDo
	Having(conds ...gen.Condition) INotificationDo
	Limit(limit int) INotificationDo
	Offset(offset int) INotificationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) INotificationDo
	Unscoped() INotificationDo
	Create(values ...*model.Notification) error
	CreateInBatches(values []*model.Notification, batchSize int) error
	Save(values ...*model.Notification) error
	First() (*model.Notification, error)
	Take() (*model.Notification, error)
	Last() (*model.Notification, error)
	Find() ([]*model.Notification, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Notification, err error)
	FindInBatches(result *[]*model.Notification, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Notification) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) INotificationDo
	Assign(attrs ...field.AssignExpr) INotificationDo
	Joins(fields ...field.RelationField) INotificationDo
	Preload(fields ...field.RelationField) INotificationDo
	FirstOrInit() (*model.Notification, error)
	FirstOrCreate() (*model.Notification, error)
	FindByPage(offset int, limit int) (result []*model.Notification, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) INotificationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (n notificationDo) Debug() INotificationDo {
	return n.withDO(n.DO.Debug())
}

func (n notificationDo) WithContext(ctx context.Context) INotificationDo {
	return n.withDO(n.DO.WithContext(ctx))
}

func (n notificationDo) ReadDB() INotificationDo {
	return n.Clauses(dbresolver.Read)
}

func (n notificationDo) WriteDB() INotificationDo {
	return n.Clauses(dbresolver.Write)
}

func (n notificationDo) Session(config *gorm.Session) INotificationDo {
	return n.withDO(n.DO.Session(config))
}

func (n notificationDo) Clauses(conds ...clause.Expression) INotificationDo {
	return n.withDO(n.DO.Clauses(conds...))
}

func (n notificationDo) Returning(value interface{}, columns ...string) INotificationDo {
	return n.withDO(n.DO.Returning(value, columns...))
}

func (n notificationDo) Not(conds ...gen.Condition) INotificationDo {
	return n.withDO(n.DO.Not(conds...))
}

func (n notificationDo) Or(conds ...gen.Condition) INotificationDo {
	return n.withDO(n.DO.Or(conds...))
}

func (n notificationDo) Select(conds ...field.Expr) INotificationDo {
	return n.withDO(n.DO.Select(conds...))
}

func (n notificationDo) Where(conds ...gen.Condition) INotificationDo {
	return n.withDO(n.DO.Where(conds...))
}

func (n notificationDo) Order(conds ...field.Expr) INotificationDo {
	return n.withDO(n.DO.Order(conds...))
}

func (n notificationDo) Distinct(cols ...field.Expr) INotificationDo {
	return n.withDO(n.DO.Distinct(cols...))
}

func (n notificationDo) Omit(cols ...field.Expr) INotificationDo {
	return n.withDO(n.DO.Omit(cols...))
}

func (n notificationDo) Join(table schema.Tabler, on ...field.Expr) INotificationDo {
	return n.withDO(n.DO.Join(table, on...))
}

func (n notificationDo) LeftJoin(table schema.Tabler, on ...field.Expr) INotificationDo {
	return n.withDO(n.DO.LeftJoin(table, on...))
}

func (n notificationDo) RightJoin(table schema.Tabler, on ...field.Expr) INotificationDo {
	return n.withDO(n.DO.RightJoin(table, on...))
}

func (n notificationDo) Group(cols ...field.Expr) INotificationDo {
	return n.withDO(n.DO.Group(cols...))
}

func (n notificationDo) Having(conds ...gen.Condition) INotificationDo {
	return n.withDO(n.DO.Having(conds...))
}

func (n notificationDo) Limit(limit int) INotificationDo {
	return n.withDO(n.DO.Limit(limit))
}

func (n notificationDo) Offset(offset int) INotificationDo {
	return n.withDO(n.DO.Offset(offset))
}

func (n notificationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) INotificationDo {
	return n.withDO(n.DO.Scopes(funcs...))
}

func (n notificationDo) Unscoped() INotificationDo {
	return n.withDO(n.DO.Unscoped())
}

func (n notificationDo) Create(values ...*model.Notification) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Create(values)
}

func (n notificationDo) CreateInBatches(values []*model.Notification, batchSize int) error {
	return n.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (n notificationDo) Save(values ...*model.Notification) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Save(values)
}

func (n notificationDo) First() (*model.Notification, error) {
	if result, err := n.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Notification), nil
	}
}

func (n notificationDo) Take() (*model.Notification, error) {
	if result, err := n.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Notification), nil
	}
}

func (n notificationDo) Last() (*model.Notification, error) {
	if result, err := n.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Notification), nil
	}
}

func (n notificationDo) Find() ([]*model.Notification, error) {
	result, err := n.DO.Find()
	return result.([]*model.Notification), err
}

func (n notificationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Notification, err error) {
	buf := make([]*model.Notification, 0, batchSize)
	err = n.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (n notificationDo) FindInBatches(result *[]*model.Notification, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return n.DO.FindInBatches(result, batchSize, fc)
}

func (n notificationDo) Attrs(attrs ...field.AssignExpr) INotificationDo {
	return n.withDO(n.DO.Attrs(attrs...))
}

func (n notificationDo) Assign(attrs ...field.AssignExpr) INotificationDo {
	return n.withDO(n.DO.Assign(attrs...))
}

func (n notificationDo) Joins(fields ...field.RelationField) INotificationDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Joins(_f))
	}
	return &n
}

func (n notificationDo) Preload(fields ...field.RelationField) INotificationDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Preload(_f))
	}
	return &n
}

func (n notificationDo) FirstOrInit() (*model.Notification, error) {
	if result, err := n.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Notification), nil
	}
}

func (n notificationDo) FirstOrCreate() (*model.Notification, error) {
	if result, err := n.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Notification), nil
	}
}

func (n notificationDo) FindByPage(offset int, limit int) (result []*model.Notification, count int64, err error) {
	result, err = n.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = n.Offset(-1).Limit(-1).Count()
	return
}

func (n notificationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = n.Count()
	if err != nil {
		return
	}

	err = n.Offset(offset).Limit(limit).Scan(result)
	return
}

func (n notificationDo) Scan(result interface{}) (err error) {
	return n.DO.Scan(result)
}

func (n notificationDo) Delete(models ...*model.Notification) (result gen.ResultInfo, err error) {
	return n.DO.Delete(models)
}

func (n *notificationDo) withDO(do gen.Dao) *notificationDo {
	n.DO = *do.(*gen.DO)
	return n
}
