// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newUserGroupRelation(db *gorm.DB, opts ...gen.DOOption) userGroupRelation {
	_userGroupRelation := userGroupRelation{}

	_userGroupRelation.userGroupRelationDo.UseDB(db, opts...)
	_userGroupRelation.userGroupRelationDo.UseModel(&model.UserGroupRelation{})

	tableName := _userGroupRelation.userGroupRelationDo.TableName()
	_userGroupRelation.ALL = field.NewAsterisk(tableName)
	_userGroupRelation.ID = field.NewInt64(tableName, "id")
	_userGroupRelation.UserID = field.NewInt64(tableName, "user_id")
	_userGroupRelation.GroupID = field.NewInt64(tableName, "group_id")
	_userGroupRelation.CreatedAt = field.NewTime(tableName, "created_at")
	_userGroupRelation.UUID = field.NewString(tableName, "uuid")
	_userGroupRelation.TenantID = field.NewString(tableName, "tenant_id")

	_userGroupRelation.fillFieldMap()

	return _userGroupRelation
}

// userGroupRelation 用户分组关系表
type userGroupRelation struct {
	userGroupRelationDo

	ALL       field.Asterisk
	ID        field.Int64  // 主键ID
	UserID    field.Int64  // 用户ID
	GroupID   field.Int64  // 分组ID
	CreatedAt field.Time   // 加入时间
	UUID      field.String // 唯一ID
	TenantID  field.String // 租户ID

	fieldMap map[string]field.Expr
}

func (u userGroupRelation) Table(newTableName string) *userGroupRelation {
	u.userGroupRelationDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userGroupRelation) As(alias string) *userGroupRelation {
	u.userGroupRelationDo.DO = *(u.userGroupRelationDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userGroupRelation) updateTableName(table string) *userGroupRelation {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewInt64(table, "user_id")
	u.GroupID = field.NewInt64(table, "group_id")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UUID = field.NewString(table, "uuid")
	u.TenantID = field.NewString(table, "tenant_id")

	u.fillFieldMap()

	return u
}

func (u *userGroupRelation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userGroupRelation) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 6)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["group_id"] = u.GroupID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["uuid"] = u.UUID
	u.fieldMap["tenant_id"] = u.TenantID
}

func (u userGroupRelation) clone(db *gorm.DB) userGroupRelation {
	u.userGroupRelationDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userGroupRelation) replaceDB(db *gorm.DB) userGroupRelation {
	u.userGroupRelationDo.ReplaceDB(db)
	return u
}

type userGroupRelationDo struct{ gen.DO }

type IUserGroupRelationDo interface {
	gen.SubQuery
	Debug() IUserGroupRelationDo
	WithContext(ctx context.Context) IUserGroupRelationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserGroupRelationDo
	WriteDB() IUserGroupRelationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserGroupRelationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserGroupRelationDo
	Not(conds ...gen.Condition) IUserGroupRelationDo
	Or(conds ...gen.Condition) IUserGroupRelationDo
	Select(conds ...field.Expr) IUserGroupRelationDo
	Where(conds ...gen.Condition) IUserGroupRelationDo
	Order(conds ...field.Expr) IUserGroupRelationDo
	Distinct(cols ...field.Expr) IUserGroupRelationDo
	Omit(cols ...field.Expr) IUserGroupRelationDo
	Join(table schema.Tabler, on ...field.Expr) IUserGroupRelationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserGroupRelationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserGroupRelationDo
	Group(cols ...field.Expr) IUserGroupRelationDo
	Having(conds ...gen.Condition) IUserGroupRelationDo
	Limit(limit int) IUserGroupRelationDo
	Offset(offset int) IUserGroupRelationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserGroupRelationDo
	Unscoped() IUserGroupRelationDo
	Create(values ...*model.UserGroupRelation) error
	CreateInBatches(values []*model.UserGroupRelation, batchSize int) error
	Save(values ...*model.UserGroupRelation) error
	First() (*model.UserGroupRelation, error)
	Take() (*model.UserGroupRelation, error)
	Last() (*model.UserGroupRelation, error)
	Find() ([]*model.UserGroupRelation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserGroupRelation, err error)
	FindInBatches(result *[]*model.UserGroupRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserGroupRelation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserGroupRelationDo
	Assign(attrs ...field.AssignExpr) IUserGroupRelationDo
	Joins(fields ...field.RelationField) IUserGroupRelationDo
	Preload(fields ...field.RelationField) IUserGroupRelationDo
	FirstOrInit() (*model.UserGroupRelation, error)
	FirstOrCreate() (*model.UserGroupRelation, error)
	FindByPage(offset int, limit int) (result []*model.UserGroupRelation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserGroupRelationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userGroupRelationDo) Debug() IUserGroupRelationDo {
	return u.withDO(u.DO.Debug())
}

func (u userGroupRelationDo) WithContext(ctx context.Context) IUserGroupRelationDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userGroupRelationDo) ReadDB() IUserGroupRelationDo {
	return u.Clauses(dbresolver.Read)
}

func (u userGroupRelationDo) WriteDB() IUserGroupRelationDo {
	return u.Clauses(dbresolver.Write)
}

func (u userGroupRelationDo) Session(config *gorm.Session) IUserGroupRelationDo {
	return u.withDO(u.DO.Session(config))
}

func (u userGroupRelationDo) Clauses(conds ...clause.Expression) IUserGroupRelationDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userGroupRelationDo) Returning(value interface{}, columns ...string) IUserGroupRelationDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userGroupRelationDo) Not(conds ...gen.Condition) IUserGroupRelationDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userGroupRelationDo) Or(conds ...gen.Condition) IUserGroupRelationDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userGroupRelationDo) Select(conds ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userGroupRelationDo) Where(conds ...gen.Condition) IUserGroupRelationDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userGroupRelationDo) Order(conds ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userGroupRelationDo) Distinct(cols ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userGroupRelationDo) Omit(cols ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userGroupRelationDo) Join(table schema.Tabler, on ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userGroupRelationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userGroupRelationDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userGroupRelationDo) Group(cols ...field.Expr) IUserGroupRelationDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userGroupRelationDo) Having(conds ...gen.Condition) IUserGroupRelationDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userGroupRelationDo) Limit(limit int) IUserGroupRelationDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userGroupRelationDo) Offset(offset int) IUserGroupRelationDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userGroupRelationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserGroupRelationDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userGroupRelationDo) Unscoped() IUserGroupRelationDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userGroupRelationDo) Create(values ...*model.UserGroupRelation) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userGroupRelationDo) CreateInBatches(values []*model.UserGroupRelation, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userGroupRelationDo) Save(values ...*model.UserGroupRelation) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userGroupRelationDo) First() (*model.UserGroupRelation, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroupRelation), nil
	}
}

func (u userGroupRelationDo) Take() (*model.UserGroupRelation, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroupRelation), nil
	}
}

func (u userGroupRelationDo) Last() (*model.UserGroupRelation, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroupRelation), nil
	}
}

func (u userGroupRelationDo) Find() ([]*model.UserGroupRelation, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserGroupRelation), err
}

func (u userGroupRelationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserGroupRelation, err error) {
	buf := make([]*model.UserGroupRelation, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userGroupRelationDo) FindInBatches(result *[]*model.UserGroupRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userGroupRelationDo) Attrs(attrs ...field.AssignExpr) IUserGroupRelationDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userGroupRelationDo) Assign(attrs ...field.AssignExpr) IUserGroupRelationDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userGroupRelationDo) Joins(fields ...field.RelationField) IUserGroupRelationDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userGroupRelationDo) Preload(fields ...field.RelationField) IUserGroupRelationDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userGroupRelationDo) FirstOrInit() (*model.UserGroupRelation, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroupRelation), nil
	}
}

func (u userGroupRelationDo) FirstOrCreate() (*model.UserGroupRelation, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserGroupRelation), nil
	}
}

func (u userGroupRelationDo) FindByPage(offset int, limit int) (result []*model.UserGroupRelation, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userGroupRelationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userGroupRelationDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userGroupRelationDo) Delete(models ...*model.UserGroupRelation) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userGroupRelationDo) withDO(do gen.Dao) *userGroupRelationDo {
	u.DO = *do.(*gen.DO)
	return u
}
