// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newDict(db *gorm.DB, opts ...gen.DOOption) dict {
	_dict := dict{}

	_dict.dictDo.UseDB(db, opts...)
	_dict.dictDo.UseModel(&model.Dict{})

	tableName := _dict.dictDo.TableName()
	_dict.ALL = field.NewAsterisk(tableName)
	_dict.ID = field.NewInt64(tableName, "id")
	_dict.CreatedAt = field.NewTime(tableName, "created_at")
	_dict.UpdatedAt = field.NewTime(tableName, "updated_at")
	_dict.DeletedAt = field.NewField(tableName, "deleted_at")
	_dict.Sort = field.NewInt64(tableName, "sort")
	_dict.UuID = field.NewString(tableName, "uu_id")
	_dict.CreateID = field.NewInt64(tableName, "create_id")
	_dict.UpdateID = field.NewInt64(tableName, "update_id")
	_dict.Name = field.NewString(tableName, "name")
	_dict.Code = field.NewString(tableName, "code")
	_dict.ParentID = field.NewInt64(tableName, "parent_id")
	_dict.ParentCode = field.NewString(tableName, "parent_code")
	_dict.Status = field.NewString(tableName, "status")

	_dict.fillFieldMap()

	return _dict
}

// dict 字典表
type dict struct {
	dictDo

	ALL        field.Asterisk
	ID         field.Int64  // 主键id
	CreatedAt  field.Time   // 创建时间
	UpdatedAt  field.Time   // 更新时间
	DeletedAt  field.Field  // 软删除标记
	Sort       field.Int64  // 排序字段
	UuID       field.String // 全局唯一id
	CreateID   field.Int64  // 创建人id
	UpdateID   field.Int64  // 更新人id
	Name       field.String // 字典名称
	Code       field.String // 字典键值
	ParentID   field.Int64  // 父级字典
	ParentCode field.String // 父code
	Status     field.String // 状态，enable表示启用，disabled表示禁用

	fieldMap map[string]field.Expr
}

func (d dict) Table(newTableName string) *dict {
	d.dictDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d dict) As(alias string) *dict {
	d.dictDo.DO = *(d.dictDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *dict) updateTableName(table string) *dict {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.CreatedAt = field.NewTime(table, "created_at")
	d.UpdatedAt = field.NewTime(table, "updated_at")
	d.DeletedAt = field.NewField(table, "deleted_at")
	d.Sort = field.NewInt64(table, "sort")
	d.UuID = field.NewString(table, "uu_id")
	d.CreateID = field.NewInt64(table, "create_id")
	d.UpdateID = field.NewInt64(table, "update_id")
	d.Name = field.NewString(table, "name")
	d.Code = field.NewString(table, "code")
	d.ParentID = field.NewInt64(table, "parent_id")
	d.ParentCode = field.NewString(table, "parent_code")
	d.Status = field.NewString(table, "status")

	d.fillFieldMap()

	return d
}

func (d *dict) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *dict) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 13)
	d.fieldMap["id"] = d.ID
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["deleted_at"] = d.DeletedAt
	d.fieldMap["sort"] = d.Sort
	d.fieldMap["uu_id"] = d.UuID
	d.fieldMap["create_id"] = d.CreateID
	d.fieldMap["update_id"] = d.UpdateID
	d.fieldMap["name"] = d.Name
	d.fieldMap["code"] = d.Code
	d.fieldMap["parent_id"] = d.ParentID
	d.fieldMap["parent_code"] = d.ParentCode
	d.fieldMap["status"] = d.Status
}

func (d dict) clone(db *gorm.DB) dict {
	d.dictDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d dict) replaceDB(db *gorm.DB) dict {
	d.dictDo.ReplaceDB(db)
	return d
}

type dictDo struct{ gen.DO }

type IDictDo interface {
	gen.SubQuery
	Debug() IDictDo
	WithContext(ctx context.Context) IDictDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDictDo
	WriteDB() IDictDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDictDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDictDo
	Not(conds ...gen.Condition) IDictDo
	Or(conds ...gen.Condition) IDictDo
	Select(conds ...field.Expr) IDictDo
	Where(conds ...gen.Condition) IDictDo
	Order(conds ...field.Expr) IDictDo
	Distinct(cols ...field.Expr) IDictDo
	Omit(cols ...field.Expr) IDictDo
	Join(table schema.Tabler, on ...field.Expr) IDictDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDictDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDictDo
	Group(cols ...field.Expr) IDictDo
	Having(conds ...gen.Condition) IDictDo
	Limit(limit int) IDictDo
	Offset(offset int) IDictDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDictDo
	Unscoped() IDictDo
	Create(values ...*model.Dict) error
	CreateInBatches(values []*model.Dict, batchSize int) error
	Save(values ...*model.Dict) error
	First() (*model.Dict, error)
	Take() (*model.Dict, error)
	Last() (*model.Dict, error)
	Find() ([]*model.Dict, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Dict, err error)
	FindInBatches(result *[]*model.Dict, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Dict) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDictDo
	Assign(attrs ...field.AssignExpr) IDictDo
	Joins(fields ...field.RelationField) IDictDo
	Preload(fields ...field.RelationField) IDictDo
	FirstOrInit() (*model.Dict, error)
	FirstOrCreate() (*model.Dict, error)
	FindByPage(offset int, limit int) (result []*model.Dict, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDictDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d dictDo) Debug() IDictDo {
	return d.withDO(d.DO.Debug())
}

func (d dictDo) WithContext(ctx context.Context) IDictDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d dictDo) ReadDB() IDictDo {
	return d.Clauses(dbresolver.Read)
}

func (d dictDo) WriteDB() IDictDo {
	return d.Clauses(dbresolver.Write)
}

func (d dictDo) Session(config *gorm.Session) IDictDo {
	return d.withDO(d.DO.Session(config))
}

func (d dictDo) Clauses(conds ...clause.Expression) IDictDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d dictDo) Returning(value interface{}, columns ...string) IDictDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d dictDo) Not(conds ...gen.Condition) IDictDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d dictDo) Or(conds ...gen.Condition) IDictDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d dictDo) Select(conds ...field.Expr) IDictDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d dictDo) Where(conds ...gen.Condition) IDictDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d dictDo) Order(conds ...field.Expr) IDictDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d dictDo) Distinct(cols ...field.Expr) IDictDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d dictDo) Omit(cols ...field.Expr) IDictDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d dictDo) Join(table schema.Tabler, on ...field.Expr) IDictDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d dictDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDictDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d dictDo) RightJoin(table schema.Tabler, on ...field.Expr) IDictDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d dictDo) Group(cols ...field.Expr) IDictDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d dictDo) Having(conds ...gen.Condition) IDictDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d dictDo) Limit(limit int) IDictDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d dictDo) Offset(offset int) IDictDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d dictDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDictDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d dictDo) Unscoped() IDictDo {
	return d.withDO(d.DO.Unscoped())
}

func (d dictDo) Create(values ...*model.Dict) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d dictDo) CreateInBatches(values []*model.Dict, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d dictDo) Save(values ...*model.Dict) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d dictDo) First() (*model.Dict, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dict), nil
	}
}

func (d dictDo) Take() (*model.Dict, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dict), nil
	}
}

func (d dictDo) Last() (*model.Dict, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dict), nil
	}
}

func (d dictDo) Find() ([]*model.Dict, error) {
	result, err := d.DO.Find()
	return result.([]*model.Dict), err
}

func (d dictDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Dict, err error) {
	buf := make([]*model.Dict, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d dictDo) FindInBatches(result *[]*model.Dict, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d dictDo) Attrs(attrs ...field.AssignExpr) IDictDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d dictDo) Assign(attrs ...field.AssignExpr) IDictDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d dictDo) Joins(fields ...field.RelationField) IDictDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d dictDo) Preload(fields ...field.RelationField) IDictDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d dictDo) FirstOrInit() (*model.Dict, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dict), nil
	}
}

func (d dictDo) FirstOrCreate() (*model.Dict, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Dict), nil
	}
}

func (d dictDo) FindByPage(offset int, limit int) (result []*model.Dict, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d dictDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d dictDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d dictDo) Delete(models ...*model.Dict) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *dictDo) withDO(do gen.Dao) *dictDo {
	d.DO = *do.(*gen.DO)
	return d
}
