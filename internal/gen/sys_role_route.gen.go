// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newSysRoleRoute(db *gorm.DB, opts ...gen.DOOption) sysRoleRoute {
	_sysRoleRoute := sysRoleRoute{}

	_sysRoleRoute.sysRoleRouteDo.UseDB(db, opts...)
	_sysRoleRoute.sysRoleRouteDo.UseModel(&model.SysRoleRoute{})

	tableName := _sysRoleRoute.sysRoleRouteDo.TableName()
	_sysRoleRoute.ALL = field.NewAsterisk(tableName)
	_sysRoleRoute.ID = field.NewInt64(tableName, "id")
	_sysRoleRoute.RoleID = field.NewInt64(tableName, "role_id")
	_sysRoleRoute.RouteID = field.NewInt64(tableName, "route_id")

	_sysRoleRoute.fillFieldMap()

	return _sysRoleRoute
}

// sysRoleRoute 角色路由关联表
type sysRoleRoute struct {
	sysRoleRouteDo

	ALL     field.Asterisk
	ID      field.Int64 // 主键
	RoleID  field.Int64 // 角色ID
	RouteID field.Int64 // 路由ID

	fieldMap map[string]field.Expr
}

func (s sysRoleRoute) Table(newTableName string) *sysRoleRoute {
	s.sysRoleRouteDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysRoleRoute) As(alias string) *sysRoleRoute {
	s.sysRoleRouteDo.DO = *(s.sysRoleRouteDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysRoleRoute) updateTableName(table string) *sysRoleRoute {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.RoleID = field.NewInt64(table, "role_id")
	s.RouteID = field.NewInt64(table, "route_id")

	s.fillFieldMap()

	return s
}

func (s *sysRoleRoute) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysRoleRoute) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 3)
	s.fieldMap["id"] = s.ID
	s.fieldMap["role_id"] = s.RoleID
	s.fieldMap["route_id"] = s.RouteID
}

func (s sysRoleRoute) clone(db *gorm.DB) sysRoleRoute {
	s.sysRoleRouteDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysRoleRoute) replaceDB(db *gorm.DB) sysRoleRoute {
	s.sysRoleRouteDo.ReplaceDB(db)
	return s
}

type sysRoleRouteDo struct{ gen.DO }

type ISysRoleRouteDo interface {
	gen.SubQuery
	Debug() ISysRoleRouteDo
	WithContext(ctx context.Context) ISysRoleRouteDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISysRoleRouteDo
	WriteDB() ISysRoleRouteDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISysRoleRouteDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISysRoleRouteDo
	Not(conds ...gen.Condition) ISysRoleRouteDo
	Or(conds ...gen.Condition) ISysRoleRouteDo
	Select(conds ...field.Expr) ISysRoleRouteDo
	Where(conds ...gen.Condition) ISysRoleRouteDo
	Order(conds ...field.Expr) ISysRoleRouteDo
	Distinct(cols ...field.Expr) ISysRoleRouteDo
	Omit(cols ...field.Expr) ISysRoleRouteDo
	Join(table schema.Tabler, on ...field.Expr) ISysRoleRouteDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISysRoleRouteDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISysRoleRouteDo
	Group(cols ...field.Expr) ISysRoleRouteDo
	Having(conds ...gen.Condition) ISysRoleRouteDo
	Limit(limit int) ISysRoleRouteDo
	Offset(offset int) ISysRoleRouteDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISysRoleRouteDo
	Unscoped() ISysRoleRouteDo
	Create(values ...*model.SysRoleRoute) error
	CreateInBatches(values []*model.SysRoleRoute, batchSize int) error
	Save(values ...*model.SysRoleRoute) error
	First() (*model.SysRoleRoute, error)
	Take() (*model.SysRoleRoute, error)
	Last() (*model.SysRoleRoute, error)
	Find() ([]*model.SysRoleRoute, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysRoleRoute, err error)
	FindInBatches(result *[]*model.SysRoleRoute, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SysRoleRoute) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISysRoleRouteDo
	Assign(attrs ...field.AssignExpr) ISysRoleRouteDo
	Joins(fields ...field.RelationField) ISysRoleRouteDo
	Preload(fields ...field.RelationField) ISysRoleRouteDo
	FirstOrInit() (*model.SysRoleRoute, error)
	FirstOrCreate() (*model.SysRoleRoute, error)
	FindByPage(offset int, limit int) (result []*model.SysRoleRoute, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISysRoleRouteDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s sysRoleRouteDo) Debug() ISysRoleRouteDo {
	return s.withDO(s.DO.Debug())
}

func (s sysRoleRouteDo) WithContext(ctx context.Context) ISysRoleRouteDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysRoleRouteDo) ReadDB() ISysRoleRouteDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysRoleRouteDo) WriteDB() ISysRoleRouteDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysRoleRouteDo) Session(config *gorm.Session) ISysRoleRouteDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysRoleRouteDo) Clauses(conds ...clause.Expression) ISysRoleRouteDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysRoleRouteDo) Returning(value interface{}, columns ...string) ISysRoleRouteDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysRoleRouteDo) Not(conds ...gen.Condition) ISysRoleRouteDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysRoleRouteDo) Or(conds ...gen.Condition) ISysRoleRouteDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysRoleRouteDo) Select(conds ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysRoleRouteDo) Where(conds ...gen.Condition) ISysRoleRouteDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysRoleRouteDo) Order(conds ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysRoleRouteDo) Distinct(cols ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysRoleRouteDo) Omit(cols ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysRoleRouteDo) Join(table schema.Tabler, on ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysRoleRouteDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysRoleRouteDo) RightJoin(table schema.Tabler, on ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysRoleRouteDo) Group(cols ...field.Expr) ISysRoleRouteDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysRoleRouteDo) Having(conds ...gen.Condition) ISysRoleRouteDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysRoleRouteDo) Limit(limit int) ISysRoleRouteDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysRoleRouteDo) Offset(offset int) ISysRoleRouteDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysRoleRouteDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISysRoleRouteDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysRoleRouteDo) Unscoped() ISysRoleRouteDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysRoleRouteDo) Create(values ...*model.SysRoleRoute) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysRoleRouteDo) CreateInBatches(values []*model.SysRoleRoute, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysRoleRouteDo) Save(values ...*model.SysRoleRoute) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysRoleRouteDo) First() (*model.SysRoleRoute, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoleRoute), nil
	}
}

func (s sysRoleRouteDo) Take() (*model.SysRoleRoute, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoleRoute), nil
	}
}

func (s sysRoleRouteDo) Last() (*model.SysRoleRoute, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoleRoute), nil
	}
}

func (s sysRoleRouteDo) Find() ([]*model.SysRoleRoute, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysRoleRoute), err
}

func (s sysRoleRouteDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysRoleRoute, err error) {
	buf := make([]*model.SysRoleRoute, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysRoleRouteDo) FindInBatches(result *[]*model.SysRoleRoute, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysRoleRouteDo) Attrs(attrs ...field.AssignExpr) ISysRoleRouteDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysRoleRouteDo) Assign(attrs ...field.AssignExpr) ISysRoleRouteDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysRoleRouteDo) Joins(fields ...field.RelationField) ISysRoleRouteDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysRoleRouteDo) Preload(fields ...field.RelationField) ISysRoleRouteDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysRoleRouteDo) FirstOrInit() (*model.SysRoleRoute, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoleRoute), nil
	}
}

func (s sysRoleRouteDo) FirstOrCreate() (*model.SysRoleRoute, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysRoleRoute), nil
	}
}

func (s sysRoleRouteDo) FindByPage(offset int, limit int) (result []*model.SysRoleRoute, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysRoleRouteDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysRoleRouteDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysRoleRouteDo) Delete(models ...*model.SysRoleRoute) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysRoleRouteDo) withDO(do gen.Dao) *sysRoleRouteDo {
	s.DO = *do.(*gen.DO)
	return s
}
