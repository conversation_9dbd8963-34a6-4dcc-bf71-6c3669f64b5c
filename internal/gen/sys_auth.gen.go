// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"admin/internal/model"
)

func newSysAuth(db *gorm.DB, opts ...gen.DOOption) sysAuth {
	_sysAuth := sysAuth{}

	_sysAuth.sysAuthDo.UseDB(db, opts...)
	_sysAuth.sysAuthDo.UseModel(&model.SysAuth{})

	tableName := _sysAuth.sysAuthDo.TableName()
	_sysAuth.ALL = field.NewAsterisk(tableName)
	_sysAuth.ID = field.NewInt64(tableName, "id")
	_sysAuth.RouteID = field.NewString(tableName, "route_id")
	_sysAuth.AuthKey = field.NewString(tableName, "auth_key")
	_sysAuth.AuthName = field.NewString(tableName, "auth_name")
	_sysAuth.UUID = field.NewString(tableName, "uuid")
	_sysAuth.TenantID = field.NewString(tableName, "tenant_id")

	_sysAuth.fillFieldMap()

	return _sysAuth
}

// sysAuth 权限表
type sysAuth struct {
	sysAuthDo

	ALL      field.Asterisk
	ID       field.Int64  // 主键
	RouteID  field.String // 关联的路由ID
	AuthKey  field.String // 权限标识
	AuthName field.String // 权限名称
	UUID     field.String // 唯一ID
	TenantID field.String // 租户ID

	fieldMap map[string]field.Expr
}

func (s sysAuth) Table(newTableName string) *sysAuth {
	s.sysAuthDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysAuth) As(alias string) *sysAuth {
	s.sysAuthDo.DO = *(s.sysAuthDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysAuth) updateTableName(table string) *sysAuth {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.RouteID = field.NewString(table, "route_id")
	s.AuthKey = field.NewString(table, "auth_key")
	s.AuthName = field.NewString(table, "auth_name")
	s.UUID = field.NewString(table, "uuid")
	s.TenantID = field.NewString(table, "tenant_id")

	s.fillFieldMap()

	return s
}

func (s *sysAuth) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysAuth) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["route_id"] = s.RouteID
	s.fieldMap["auth_key"] = s.AuthKey
	s.fieldMap["auth_name"] = s.AuthName
	s.fieldMap["uuid"] = s.UUID
	s.fieldMap["tenant_id"] = s.TenantID
}

func (s sysAuth) clone(db *gorm.DB) sysAuth {
	s.sysAuthDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysAuth) replaceDB(db *gorm.DB) sysAuth {
	s.sysAuthDo.ReplaceDB(db)
	return s
}

type sysAuthDo struct{ gen.DO }

type ISysAuthDo interface {
	gen.SubQuery
	Debug() ISysAuthDo
	WithContext(ctx context.Context) ISysAuthDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISysAuthDo
	WriteDB() ISysAuthDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISysAuthDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISysAuthDo
	Not(conds ...gen.Condition) ISysAuthDo
	Or(conds ...gen.Condition) ISysAuthDo
	Select(conds ...field.Expr) ISysAuthDo
	Where(conds ...gen.Condition) ISysAuthDo
	Order(conds ...field.Expr) ISysAuthDo
	Distinct(cols ...field.Expr) ISysAuthDo
	Omit(cols ...field.Expr) ISysAuthDo
	Join(table schema.Tabler, on ...field.Expr) ISysAuthDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISysAuthDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISysAuthDo
	Group(cols ...field.Expr) ISysAuthDo
	Having(conds ...gen.Condition) ISysAuthDo
	Limit(limit int) ISysAuthDo
	Offset(offset int) ISysAuthDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISysAuthDo
	Unscoped() ISysAuthDo
	Create(values ...*model.SysAuth) error
	CreateInBatches(values []*model.SysAuth, batchSize int) error
	Save(values ...*model.SysAuth) error
	First() (*model.SysAuth, error)
	Take() (*model.SysAuth, error)
	Last() (*model.SysAuth, error)
	Find() ([]*model.SysAuth, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysAuth, err error)
	FindInBatches(result *[]*model.SysAuth, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SysAuth) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISysAuthDo
	Assign(attrs ...field.AssignExpr) ISysAuthDo
	Joins(fields ...field.RelationField) ISysAuthDo
	Preload(fields ...field.RelationField) ISysAuthDo
	FirstOrInit() (*model.SysAuth, error)
	FirstOrCreate() (*model.SysAuth, error)
	FindByPage(offset int, limit int) (result []*model.SysAuth, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISysAuthDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s sysAuthDo) Debug() ISysAuthDo {
	return s.withDO(s.DO.Debug())
}

func (s sysAuthDo) WithContext(ctx context.Context) ISysAuthDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysAuthDo) ReadDB() ISysAuthDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysAuthDo) WriteDB() ISysAuthDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysAuthDo) Session(config *gorm.Session) ISysAuthDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysAuthDo) Clauses(conds ...clause.Expression) ISysAuthDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysAuthDo) Returning(value interface{}, columns ...string) ISysAuthDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysAuthDo) Not(conds ...gen.Condition) ISysAuthDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysAuthDo) Or(conds ...gen.Condition) ISysAuthDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysAuthDo) Select(conds ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysAuthDo) Where(conds ...gen.Condition) ISysAuthDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysAuthDo) Order(conds ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysAuthDo) Distinct(cols ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysAuthDo) Omit(cols ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysAuthDo) Join(table schema.Tabler, on ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysAuthDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysAuthDo) RightJoin(table schema.Tabler, on ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysAuthDo) Group(cols ...field.Expr) ISysAuthDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysAuthDo) Having(conds ...gen.Condition) ISysAuthDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysAuthDo) Limit(limit int) ISysAuthDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysAuthDo) Offset(offset int) ISysAuthDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysAuthDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISysAuthDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysAuthDo) Unscoped() ISysAuthDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysAuthDo) Create(values ...*model.SysAuth) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysAuthDo) CreateInBatches(values []*model.SysAuth, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysAuthDo) Save(values ...*model.SysAuth) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysAuthDo) First() (*model.SysAuth, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysAuth), nil
	}
}

func (s sysAuthDo) Take() (*model.SysAuth, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysAuth), nil
	}
}

func (s sysAuthDo) Last() (*model.SysAuth, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysAuth), nil
	}
}

func (s sysAuthDo) Find() ([]*model.SysAuth, error) {
	result, err := s.DO.Find()
	return result.([]*model.SysAuth), err
}

func (s sysAuthDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SysAuth, err error) {
	buf := make([]*model.SysAuth, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysAuthDo) FindInBatches(result *[]*model.SysAuth, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysAuthDo) Attrs(attrs ...field.AssignExpr) ISysAuthDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysAuthDo) Assign(attrs ...field.AssignExpr) ISysAuthDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysAuthDo) Joins(fields ...field.RelationField) ISysAuthDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysAuthDo) Preload(fields ...field.RelationField) ISysAuthDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysAuthDo) FirstOrInit() (*model.SysAuth, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysAuth), nil
	}
}

func (s sysAuthDo) FirstOrCreate() (*model.SysAuth, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SysAuth), nil
	}
}

func (s sysAuthDo) FindByPage(offset int, limit int) (result []*model.SysAuth, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysAuthDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysAuthDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysAuthDo) Delete(models ...*model.SysAuth) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysAuthDo) withDO(do gen.Dao) *sysAuthDo {
	s.DO = *do.(*gen.DO)
	return s
}
