// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package gen

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                    = new(Query)
	CasbinRule           *casbinRule
	Dept                 *dept
	Dict                 *dict
	Notification         *notification
	NotificationReceiver *notificationReceiver
	Post                 *post
	PostUser             *postUser
	Role                 *role
	SysAuth              *sysAuth
	SysRoleRoute         *sysRoleRoute
	SysRoute             *sysRoute
	SysRouteMetum        *sysRouteMetum
	SystemOperationLog   *systemOperationLog
	Tenant               *tenant
	User                 *user
	UserGroup            *userGroup
	UserGroupRelation    *userGroupRelation
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	CasbinRule = &Q.CasbinRule
	Dept = &Q.Dept
	Dict = &Q.Dict
	Notification = &Q.Notification
	NotificationReceiver = &Q.NotificationReceiver
	Post = &Q.Post
	PostUser = &Q.PostUser
	Role = &Q.Role
	SysAuth = &Q.SysAuth
	SysRoleRoute = &Q.SysRoleRoute
	SysRoute = &Q.SysRoute
	SysRouteMetum = &Q.SysRouteMetum
	SystemOperationLog = &Q.SystemOperationLog
	Tenant = &Q.Tenant
	User = &Q.User
	UserGroup = &Q.UserGroup
	UserGroupRelation = &Q.UserGroupRelation
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                   db,
		CasbinRule:           newCasbinRule(db, opts...),
		Dept:                 newDept(db, opts...),
		Dict:                 newDict(db, opts...),
		Notification:         newNotification(db, opts...),
		NotificationReceiver: newNotificationReceiver(db, opts...),
		Post:                 newPost(db, opts...),
		PostUser:             newPostUser(db, opts...),
		Role:                 newRole(db, opts...),
		SysAuth:              newSysAuth(db, opts...),
		SysRoleRoute:         newSysRoleRoute(db, opts...),
		SysRoute:             newSysRoute(db, opts...),
		SysRouteMetum:        newSysRouteMetum(db, opts...),
		SystemOperationLog:   newSystemOperationLog(db, opts...),
		Tenant:               newTenant(db, opts...),
		User:                 newUser(db, opts...),
		UserGroup:            newUserGroup(db, opts...),
		UserGroupRelation:    newUserGroupRelation(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CasbinRule           casbinRule
	Dept                 dept
	Dict                 dict
	Notification         notification
	NotificationReceiver notificationReceiver
	Post                 post
	PostUser             postUser
	Role                 role
	SysAuth              sysAuth
	SysRoleRoute         sysRoleRoute
	SysRoute             sysRoute
	SysRouteMetum        sysRouteMetum
	SystemOperationLog   systemOperationLog
	Tenant               tenant
	User                 user
	UserGroup            userGroup
	UserGroupRelation    userGroupRelation
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                   db,
		CasbinRule:           q.CasbinRule.clone(db),
		Dept:                 q.Dept.clone(db),
		Dict:                 q.Dict.clone(db),
		Notification:         q.Notification.clone(db),
		NotificationReceiver: q.NotificationReceiver.clone(db),
		Post:                 q.Post.clone(db),
		PostUser:             q.PostUser.clone(db),
		Role:                 q.Role.clone(db),
		SysAuth:              q.SysAuth.clone(db),
		SysRoleRoute:         q.SysRoleRoute.clone(db),
		SysRoute:             q.SysRoute.clone(db),
		SysRouteMetum:        q.SysRouteMetum.clone(db),
		SystemOperationLog:   q.SystemOperationLog.clone(db),
		Tenant:               q.Tenant.clone(db),
		User:                 q.User.clone(db),
		UserGroup:            q.UserGroup.clone(db),
		UserGroupRelation:    q.UserGroupRelation.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                   db,
		CasbinRule:           q.CasbinRule.replaceDB(db),
		Dept:                 q.Dept.replaceDB(db),
		Dict:                 q.Dict.replaceDB(db),
		Notification:         q.Notification.replaceDB(db),
		NotificationReceiver: q.NotificationReceiver.replaceDB(db),
		Post:                 q.Post.replaceDB(db),
		PostUser:             q.PostUser.replaceDB(db),
		Role:                 q.Role.replaceDB(db),
		SysAuth:              q.SysAuth.replaceDB(db),
		SysRoleRoute:         q.SysRoleRoute.replaceDB(db),
		SysRoute:             q.SysRoute.replaceDB(db),
		SysRouteMetum:        q.SysRouteMetum.replaceDB(db),
		SystemOperationLog:   q.SystemOperationLog.replaceDB(db),
		Tenant:               q.Tenant.replaceDB(db),
		User:                 q.User.replaceDB(db),
		UserGroup:            q.UserGroup.replaceDB(db),
		UserGroupRelation:    q.UserGroupRelation.replaceDB(db),
	}
}

type queryCtx struct {
	CasbinRule           ICasbinRuleDo
	Dept                 IDeptDo
	Dict                 IDictDo
	Notification         INotificationDo
	NotificationReceiver INotificationReceiverDo
	Post                 IPostDo
	PostUser             IPostUserDo
	Role                 IRoleDo
	SysAuth              ISysAuthDo
	SysRoleRoute         ISysRoleRouteDo
	SysRoute             ISysRouteDo
	SysRouteMetum        ISysRouteMetumDo
	SystemOperationLog   ISystemOperationLogDo
	Tenant               ITenantDo
	User                 IUserDo
	UserGroup            IUserGroupDo
	UserGroupRelation    IUserGroupRelationDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CasbinRule:           q.CasbinRule.WithContext(ctx),
		Dept:                 q.Dept.WithContext(ctx),
		Dict:                 q.Dict.WithContext(ctx),
		Notification:         q.Notification.WithContext(ctx),
		NotificationReceiver: q.NotificationReceiver.WithContext(ctx),
		Post:                 q.Post.WithContext(ctx),
		PostUser:             q.PostUser.WithContext(ctx),
		Role:                 q.Role.WithContext(ctx),
		SysAuth:              q.SysAuth.WithContext(ctx),
		SysRoleRoute:         q.SysRoleRoute.WithContext(ctx),
		SysRoute:             q.SysRoute.WithContext(ctx),
		SysRouteMetum:        q.SysRouteMetum.WithContext(ctx),
		SystemOperationLog:   q.SystemOperationLog.WithContext(ctx),
		Tenant:               q.Tenant.WithContext(ctx),
		User:                 q.User.WithContext(ctx),
		UserGroup:            q.UserGroup.WithContext(ctx),
		UserGroupRelation:    q.UserGroupRelation.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
