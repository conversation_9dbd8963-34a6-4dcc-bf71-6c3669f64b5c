package middleware

import (
	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/pkg/jwt"

	"github.com/casbin/casbin/v2"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
)

func getTenantIDFromContext(ctx *gin.Context) string {
	// 此函数需要根据实际情况实现，例如从 JWT 令牌或上下文获取租户ID
	// 示例：从 claims 中获取租户ID，假设已存储在 JWT 中
	if claims, exists := ctx.Get("claims"); exists {
		if customClaims, ok := claims.(*jwt.MyCustomClaims); ok {
			// 假设租户ID存储在 customClaims 中，需要根据实际字段调整
			return customClaims.TenantID
		}
	}
	// 默认返回空字符串或默认租户ID，需要根据项目需求调整
	return ""
}

func AuthMiddleware(e *casbin.SyncedEnforcer) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 从上下文获取用户信息（假设通过 JWT 或其他方式设置）
		v, exists := ctx.Get("claims")
		if !exists {
			v1.HandleError(ctx, v1.ErrAuthNotLogin)
			ctx.Abort()
			return
		}
		uid := v.(*jwt.MyCustomClaims).UserId
		if convertor.ToString(uid) == model.AdminUserID {
			// 防呆设计，超管跳过API权限检查
			ctx.Next()
			return
		}

		// 获取请求的资源和操作
		sub := convertor.ToString(uid)
		// 获取租户ID，需要根据实际情况实现此函数
		tenantID := getTenantIDFromContext(ctx)
		obj := model.ApiResourcePrefix + ctx.Request.URL.Path
		act := ctx.Request.Method

		// 检查权限
		allowed, err := e.Enforce(sub, tenantID, obj, act)
		if err != nil {
			v1.HandleError(ctx, v1.ErrUserPermissionNotAllowed)
			ctx.Abort()
			return
		}
		if !allowed {
			v1.HandleError(ctx, v1.ErrUserPermissionNotAllowed)
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}
