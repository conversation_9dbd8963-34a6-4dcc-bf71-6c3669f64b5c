// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameDict = "dict"

// Dict 字典表
type Dict struct {
	ID         int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;index:dict_id_index,priority:1;comment:主键id" json:"id"`    // 主键id
	CreatedAt  time.Time      `gorm:"column:created_at;type:datetime(3);not null;comment:创建时间" json:"createTime"`                                   // 创建时间
	UpdatedAt  time.Time      `gorm:"column:updated_at;type:datetime(3);not null;comment:更新时间" json:"updateTime"`                                   // 更新时间
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);index:idx_dict_deleted,priority:1;comment:软删除标记" json:"-"`                  // 软删除标记
	Sort       *int64         `gorm:"column:sort;type:bigint;comment:排序字段" json:"sort"`                                                             // 排序字段
	UuID       string         `gorm:"column:uu_id;type:varchar(50);not null;index:idx_dict_uuid,priority:1;comment:全局唯一id" json:"uuId"`             // 全局唯一id
	CreateID   *int64         `gorm:"column:create_id;type:bigint;comment:创建人id" json:"createId"`                                                   // 创建人id
	UpdateID   *int64         `gorm:"column:update_id;type:bigint;comment:更新人id" json:"updateId"`                                                   // 更新人id
	Name       string         `gorm:"column:name;type:char(30);not null;comment:字典名称" json:"name"`                                                  // 字典名称
	Code       string         `gorm:"column:code;type:char(30);not null;comment:字典键值" json:"code"`                                                  // 字典键值
	ParentID   *int64         `gorm:"column:parent_id;type:bigint;index:dict_parent_id,priority:1;comment:父级字典" json:"parentId"`                    // 父级字典
	ParentCode *string        `gorm:"column:parent_code;type:varchar(30);comment:父code" json:"parentCode"`                                          // 父code
	Status     *string        `gorm:"column:status;type:enum('enable','disabled');default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"` // 状态，enable表示启用，disabled表示禁用
}

// TableName Dict's table name
func (*Dict) TableName() string {
	return TableNameDict
}
