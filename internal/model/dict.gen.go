// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
	"admin/internal/utils"
	"sort"
)

const TableNameDict = "dict"

// Dict 字典表
type Dict struct {
	ID         int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键id" json:"id"`                                   // 主键id
	CreatedAt  time.Time      `gorm:"column:created_at;type:datetime(3);not null;comment:创建时间" json:"createTime"`                                   // 创建时间
	UpdatedAt  time.Time      `gorm:"column:updated_at;type:datetime(3);not null;comment:更新时间" json:"updateTime"`                                   // 更新时间
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);comment:软删除标记" json:"-"`                                                    // 软删除标记
	Sort       *int64         `gorm:"column:sort;type:bigint;comment:排序字段" json:"sort"`                                                             // 排序字段
	UUID       string         `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_dict_uuid_unique,priority:1;comment:全局唯一id" json:"uuid"` // 全局唯一id
	CreateID   *string        `gorm:"column:create_id;type:varchar(50);comment:创建人id" json:"createId"`                                              // 创建人id
	UpdateID   *string        `gorm:"column:update_id;type:varchar(50);comment:更新人id" json:"updateId"`                                              // 更新人id
	Name       string         `gorm:"column:name;type:char(30);not null;comment:字典名称" json:"name"`                                                  // 字典名称
	Code       string         `gorm:"column:code;type:char(30);not null;comment:字典键值" json:"code"`                                                  // 字典键值
	ParentID   *string        `gorm:"column:parent_id;type:varchar(50);comment:父级字典" json:"parentId"`                                               // 父级字典
	ParentCode *string        `gorm:"column:parent_code;type:varchar(30);comment:父code" json:"parentCode"`                                          // 父code
	Status     *string        `gorm:"column:status;type:enum('enable','disabled');default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"` // 状态，enable表示启用，disabled表示禁用
	Children   []*Dict        `gorm:"-" json:"children"`
}

// TableName Dict's table name
func (*Dict) TableName() string {
	return TableNameDict
}

// BeforeCreate 在创建记录前自动生成UUID
func (m *Dict) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
	return nil
}

// ToTree 将平级数据转换为树形结构
func (t *Dict) ToTree(nodeList []*Dict) []*Dict {
	var roots []*Dict

	// 先遍历一遍节点列表，将每个节点的Children初始化为空切片
	for _, node := range nodeList {
		node.Children = make([]*Dict, 0)
	}

	// 对节点列表按照ID进行排序
	sort.Slice(nodeList, func(i, j int) bool {
		return nodeList[i].ID < nodeList[j].ID
	})

	// 创建一个map，用于快速查找节点，键为节点UUID，值为节点指针
	nodeMap := make(map[string]*Dict)
	for _, node := range nodeList {
		nodeMap[node.UUID] = node
	}

	// 遍历节点列表，构建树形结构
	for _, node := range nodeList {
		// 检查节点是否有明确的父节点
		if node.ParentID != nil && *node.ParentID != "" {
			parent, ok := nodeMap[*node.ParentID]
			if ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果找不到父节点，则当作顶级节点处理
				roots = append(roots, node)
			}
		} else {
			// 如果节点没有父节点或者父节点ID为空，直接作为顶级节点
			roots = append(roots, node)
		}
	}

	return roots
}

// FlattenDictTree 将树形结构转换为平级列表
func FlattenDictTree(roots []*Dict) []*Dict {
	var result []*Dict
	
	var flatten func([]*Dict)
	flatten = func(nodes []*Dict) {
		for _, node := range nodes {
			result = append(result, node)
			if len(node.Children) > 0 {
				flatten(node.Children)
			}
		}
	}
	
	flatten(roots)
	return result
}

// IsDeleted 检查记录是否已被软删除
func (m *Dict) IsDeleted() bool {
	return m.DeletedAt.Valid
}

// Restore 恢复软删除的记录
func (m *Dict) Restore(db *gorm.DB) error {
	return db.Model(m).Update("deleted_at", nil).Error
}

// IsEnabled 检查记录是否启用
func (m *Dict) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *Dict) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *Dict) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
