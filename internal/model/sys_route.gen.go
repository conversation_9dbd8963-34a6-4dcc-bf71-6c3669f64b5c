// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
	"sort"
)

const TableNameSysRoute = "sys_route"

// SysRoute 路由表
type SysRoute struct {
	ID          int64       `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	Name        string      `gorm:"column:name;type:varchar(64);not null;comment:路由唯一名称" json:"name"`         // 路由唯一名称
	Path        string      `gorm:"column:path;type:varchar(128);not null;comment:路由路径" json:"path"`          // 路由路径
	Component   *string     `gorm:"column:component;type:varchar(255);comment:组件路径" json:"component"`         // 组件路径
	Redirect    *string     `gorm:"column:redirect;type:varchar(128);comment:重定向路径" json:"redirect"`          // 重定向路径
	ParentID    *string     `gorm:"column:parent_id;type:varchar(50)" json:"parentId"`
	Sort        *int64      `gorm:"column:sort;type:int;comment:排序（越大越靠前）" json:"sort"`                                                                 // 排序（越大越靠前）
	Status      *string     `gorm:"column:status;type:enum('enable','disabled');not null;default:enable;comment:状态（enable启用 disabled禁用）" json:"status"` // 状态（enable启用 disabled禁用）
	IsLayout    bool        `gorm:"column:is_layout;type:tinyint(1);not null;comment:是否一级路由（1是 0否）" json:"isLayout"`                                    // 是否一级路由（1是 0否）
	Title       string      `gorm:"column:title;type:varchar(64);not null" json:"title"`
	Icon        *string     `gorm:"column:icon;type:varchar(64)" json:"icon"`
	Hidden      *bool       `gorm:"column:hidden;type:tinyint(1)" json:"hidden"`
	ActiveMenu  *string     `gorm:"column:active_menu;type:varchar(128)" json:"activeMenu"`
	KeepAlive   *bool       `gorm:"column:keep_alive;type:tinyint(1)" json:"keepAlive"`
	Auth        *string     `gorm:"column:auth;type:varchar(255)" json:"auth"`
	Breadcrumb  *bool       `gorm:"column:breadcrumb;type:tinyint(1);default:1" json:"breadcrumb"`
	External    *bool       `gorm:"column:external;type:tinyint(1)" json:"external"`
	ExternalURL *string     `gorm:"column:external_url;type:varchar(255)" json:"externalUrl"`
	CreatedAt   *time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`               // 创建时间
	UpdatedAt   *time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`               // 更新时间
	UUID        string      `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_sys_route_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID    *string     `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                  // 租户ID
	Children    []*SysRoute `gorm:"-" json:"children"`
}

// TableName SysRoute's table name
func (*SysRoute) TableName() string {
	return TableNameSysRoute
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *SysRoute) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}

// ToTree 将平级数据转换为树形结构
func (t *SysRoute) ToTree(nodeList []*SysRoute) []*SysRoute {
	var roots []*SysRoute

	// 先遍历一遍节点列表，将每个节点的Children初始化为空切片
	for _, node := range nodeList {
		node.Children = make([]*SysRoute, 0)
	}

	// 对节点列表按照ID进行排序
	sort.Slice(nodeList, func(i, j int) bool {
		return nodeList[i].ID < nodeList[j].ID
	})

	// 创建一个map，用于快速查找节点，键为节点UUID，值为节点指针
	nodeMap := make(map[string]*SysRoute)
	for _, node := range nodeList {
		nodeMap[node.UUID] = node
	}

	// 遍历节点列表，构建树形结构
	for _, node := range nodeList {
		// 检查节点是否有明确的父节点
		if node.ParentID != nil && *node.ParentID != "" {
			parent, ok := nodeMap[*node.ParentID]
			if ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果找不到父节点，则当作顶级节点处理
				roots = append(roots, node)
			}
		} else {
			// 如果节点没有父节点或者父节点ID为空，直接作为顶级节点
			roots = append(roots, node)
		}
	}

	return roots
}

// FlattenSysRouteTree 将树形结构转换为平级列表
func FlattenSysRouteTree(roots []*SysRoute) []*SysRoute {
	var result []*SysRoute
	
	var flatten func([]*SysRoute)
	flatten = func(nodes []*SysRoute) {
		for _, node := range nodes {
			result = append(result, node)
			if len(node.Children) > 0 {
				flatten(node.Children)
			}
		}
	}
	
	flatten(roots)
	return result
}

// IsEnabled 检查记录是否启用
func (m *SysRoute) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *SysRoute) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *SysRoute) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
