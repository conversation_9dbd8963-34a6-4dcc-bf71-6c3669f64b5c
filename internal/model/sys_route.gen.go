// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSysRoute = "sys_route"

// SysRoute 路由表
type SysRoute struct {
	ID        int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                          // 主键
	Name      string     `gorm:"column:name;type:varchar(64);not null;uniqueIndex:uk_name,priority:1;comment:路由唯一名称" json:"name"`   // 路由唯一名称
	Path      string     `gorm:"column:path;type:varchar(128);not null;comment:路由路径" json:"path"`                                   // 路由路径
	Component *string    `gorm:"column:component;type:varchar(255);comment:组件路径" json:"component"`                                  // 组件路径
	Redirect  *string    `gorm:"column:redirect;type:varchar(128);comment:重定向路径" json:"redirect"`                                   // 重定向路径
	ParentID  *int64     `gorm:"column:parent_id;type:bigint;index:idx_parent_id,priority:1;comment:父路由ID" json:"parentId"`         // 父路由ID
	Sort      *int64     `gorm:"column:sort;type:int;comment:排序（越大越靠前）" json:"sort"`                                                // 排序（越大越靠前）
	Status    *int64     `gorm:"column:status;type:tinyint;not null;default:1;comment:状态（1启用 0禁用）" json:"status"`                   // 状态（1启用 0禁用）
	IsLayout  int64      `gorm:"column:is_layout;type:tinyint;not null;comment:是否一级路由（1是 0否）" json:"isLayout"`                      // 是否一级路由（1是 0否）
	CreatedAt *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"` // 创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"` // 更新时间
}

// TableName SysRoute's table name
func (*SysRoute) TableName() string {
	return TableNameSysRoute
}
