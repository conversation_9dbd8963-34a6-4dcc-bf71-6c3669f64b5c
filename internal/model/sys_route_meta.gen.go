// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSysRouteMetum = "sys_route_meta"

// SysRouteMetum 路由元信息表
type SysRouteMetum struct {
	ID          int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                               // 主键
	RouteID     int64   `gorm:"column:route_id;type:bigint;not null;uniqueIndex:uk_route_id,priority:1;comment:关联的路由ID" json:"routeId"` // 关联的路由ID
	Title       string  `gorm:"column:title;type:varchar(64);not null;comment:路由标题" json:"title"`                                       // 路由标题
	Icon        *string `gorm:"column:icon;type:varchar(64);comment:导航图标" json:"icon"`                                                  // 导航图标
	Hidden      *int64  `gorm:"column:hidden;type:tinyint;comment:是否隐藏菜单（1是 0否）" json:"hidden"`                                         // 是否隐藏菜单（1是 0否）
	ActiveMenu  *string `gorm:"column:active_menu;type:varchar(128);comment:高亮菜单路径" json:"activeMenu"`                                  // 高亮菜单路径
	KeepAlive   *int64  `gorm:"column:keep_alive;type:tinyint;comment:是否缓存页面（1是 0否）" json:"keepAlive"`                                  // 是否缓存页面（1是 0否）
	Auth        *string `gorm:"column:auth;type:varchar(255);comment:访问权限（逗号分隔）" json:"auth"`                                           // 访问权限（逗号分隔）
	Breadcrumb  *int64  `gorm:"column:breadcrumb;type:tinyint;default:1;comment:是否显示面包屑（1是 0否）" json:"breadcrumb"`                      // 是否显示面包屑（1是 0否）
	External    *int64  `gorm:"column:external;type:tinyint;comment:是否外部链接（1是 0否）" json:"external"`                                     // 是否外部链接（1是 0否）
	ExternalURL *string `gorm:"column:external_url;type:varchar(255);comment:外部链接地址" json:"externalUrl"`                                // 外部链接地址
}

// TableName SysRouteMetum's table name
func (*SysRouteMetum) TableName() string {
	return TableNameSysRouteMetum
}
