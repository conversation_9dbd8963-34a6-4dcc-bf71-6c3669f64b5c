// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameNotification = "notifications"

// Notification 公告主表
type Notification struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                                    // 主键ID
	Title       string     `gorm:"column:title;type:varchar(100);not null;comment:公告标题" json:"title"`                                                                             // 公告标题
	Content     string     `gorm:"column:content;type:text;not null;comment:公告内容" json:"content"`                                                                                 // 公告内容
	SenderID    string     `gorm:"column:sender_id;type:varchar(50);not null;comment:发送者ID（系统发送为0）" json:"senderId"`                                                              // 发送者ID（系统发送为0）
	SenderType  *string    `gorm:"column:sender_type;type:enum('system','admin','other');not null;default:system;comment:发送者类型：system=系统, admin=管理员, other=其他" json:"senderType"` // 发送者类型：system=系统, admin=管理员, other=其他
	SendType    string     `gorm:"column:send_type;type:enum('broadcast','group','private');not null;comment:发送类型：broadcast=全体, group=分组, private=私信" json:"sendType"`            // 发送类型：broadcast=全体, group=分组, private=私信
	TargetID    *string    `gorm:"column:target_id;type:varchar(50);comment:目标ID（用户ID或分组ID）" json:"targetId"`                                                                     // 目标ID（用户ID或分组ID）
	IsScheduled bool       `gorm:"column:is_scheduled;type:tinyint(1);not null;comment:是否定时发送（0=否，1=是）" json:"isScheduled"`                                                       // 是否定时发送（0=否，1=是）
	SendTime    *time.Time `gorm:"column:send_time;type:datetime;comment:计划发送时间" json:"sendTime"`                                                                                 // 计划发送时间
	ExpireTime  *time.Time `gorm:"column:expire_time;type:datetime;comment:过期时间（NULL表示永不过期）" json:"expireTime"`                                                                   // 过期时间（NULL表示永不过期）
	IsExpired   *string    `gorm:"column:is_expired;type:enum('active','expired');not null;default:active;comment:过期状态：active=有效, expired=已过期" json:"isExpired"`                  // 过期状态：active=有效, expired=已过期
	Status      *string    `gorm:"column:status;type:enum('pending','sent','canceled');not null;default:pending;comment:发送状态：pending=待发送, sent=已发送, canceled=已取消" json:"status"`  // 发送状态：pending=待发送, sent=已发送, canceled=已取消
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                                             // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`                                             // 更新时间
	UUID        string     `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_notifications_uuid_unique,priority:1;comment:唯一ID" json:"uuid"`                           // 唯一ID
	TenantID    *string    `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                                                // 租户ID
}

// TableName Notification's table name
func (*Notification) TableName() string {
	return TableNameNotification
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *Notification) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}

// IsEnabled 检查记录是否启用
func (m *Notification) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *Notification) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *Notification) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
