// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameNotification = "notifications"

// Notification 公告主表
type Notification struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                                                                        // 主键ID
	Title       string     `gorm:"column:title;type:varchar(100);not null;comment:公告标题" json:"title"`                                                                                                                 // 公告标题
	Content     string     `gorm:"column:content;type:text;not null;comment:公告内容" json:"content"`                                                                                                                     // 公告内容
	SenderID    int64      `gorm:"column:sender_id;type:bigint;not null;comment:发送者ID（系统发送为0）" json:"senderId"`                                                                                                       // 发送者ID（系统发送为0）
	SenderType  *string    `gorm:"column:sender_type;type:enum('system','admin','other');not null;default:system;comment:发送者类型：system=系统, admin=管理员, other=其他" json:"senderType"`                                     // 发送者类型：system=系统, admin=管理员, other=其他
	SendType    string     `gorm:"column:send_type;type:enum('broadcast','group','private');not null;index:idx_send_type_target,priority:1;comment:发送类型：broadcast=全体, group=分组, private=私信" json:"sendType"`          // 发送类型：broadcast=全体, group=分组, private=私信
	TargetID    *int64     `gorm:"column:target_id;type:bigint;index:idx_send_type_target,priority:2;comment:目标ID（用户ID或分组ID）" json:"targetId"`                                                                        // 目标ID（用户ID或分组ID）
	IsScheduled bool       `gorm:"column:is_scheduled;type:tinyint(1);not null;comment:是否定时发送（0=否，1=是）" json:"isScheduled"`                                                                                           // 是否定时发送（0=否，1=是）
	SendTime    *time.Time `gorm:"column:send_time;type:datetime;index:idx_status_sendtime,priority:2;comment:计划发送时间" json:"sendTime"`                                                                                // 计划发送时间
	ExpireTime  *time.Time `gorm:"column:expire_time;type:datetime;index:idx_expire,priority:2;comment:过期时间（NULL表示永不过期）" json:"expireTime"`                                                                           // 过期时间（NULL表示永不过期）
	IsExpired   *string    `gorm:"column:is_expired;type:enum('active','expired');not null;index:idx_expire,priority:1;default:active;comment:过期状态：active=有效, expired=已过期" json:"isExpired"`                          // 过期状态：active=有效, expired=已过期
	Status      *string    `gorm:"column:status;type:enum('pending','sent','canceled');not null;index:idx_status_sendtime,priority:1;default:pending;comment:发送状态：pending=待发送, sent=已发送, canceled=已取消" json:"status"` // 发送状态：pending=待发送, sent=已发送, canceled=已取消
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                                                                                 // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`                                                                                 // 更新时间
}

// TableName Notification's table name
func (*Notification) TableName() string {
	return TableNameNotification
}
