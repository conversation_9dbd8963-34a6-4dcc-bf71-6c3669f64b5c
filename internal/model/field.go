package model

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"

	"github.com/bytedance/sonic"
)

const (
	AdminRole          = "admin"
	AdminUserID        = "1"
	MenuResourcePrefix = "menu:"
	ApiResourcePrefix  = "api:"
	PermSep            = ","
)

// Status 状态
var (
	StatusEnable  = "enable"
	StatusDisable = "disabled"
)

type TimeNormal struct {
	time.Time
	IsDateOnly bool // 标记是否仅包含日期部分
}

// inferTimeLayout 动态推断时间字符串的格式
func inferTimeLayout(timeStr string) (string, error) {
	// 检查是否包含 T 和 Z，可能是 ISO8601 格式
	if strings.Contains(timeStr, "T") && strings.Contains(timeStr, "Z") {
		return time.RFC3339, nil
	}

	// 检查是否包含 T，可能是 ISO8601 格式（无时区）
	if strings.Contains(timeStr, "T") {
		return "2006-01-02T15:04:05", nil
	}

	// 检查是否包含空格，可能是常见的日期时间格式
	if strings.Contains(timeStr, " ") {
		// 检查是否包含时区信息
		if len(timeStr) > 19 && (strings.Contains(timeStr, "+") || strings.Contains(timeStr, "-")) {
			return "2006-01-02 15:04:05-07:00", nil
		}
		return "2006-01-02 15:04:05", nil
	}

	// 检查是否仅包含日期部分（斜杠分隔）
	if len(timeStr) == 10 && strings.Count(timeStr, "/") == 2 {
		return "2006/01/02", nil
	}

	// 检查是否仅包含日期部分（短横线分隔）
	if len(timeStr) == 10 && strings.Count(timeStr, "-") == 2 {
		return "2006-01-02", nil
	}

	// 检查是否仅包含时间部分
	if len(timeStr) == 8 && strings.Count(timeStr, ":") == 2 {
		return "15:04:05", nil
	}

	// 如果以上都不匹配，返回错误
	return "", fmt.Errorf("无法推断时间字符串的格式: %s", timeStr)
}

// GetTime 返回原始的 time.Time 类型
func (t *TimeNormal) GetTime() time.Time {
	return t.Time
}

// MarshalJSON 实现 json.Marshaler 接口
func (t *TimeNormal) MarshalJSON() ([]byte, error) {
	return sonic.ConfigDefault.Marshal(t.FormatTime())
}

// Value insert timestamp into mysql need this function.
func (t TimeNormal) Value() (driver.Value, error) {
	var zeroTime time.Time
	if t.Time.UnixNano() == zeroTime.UnixNano() {
		return nil, nil
	}
	return t.Time, nil
}

// Scan value time.Time
func (t *TimeNormal) Scan(v interface{}) error {
	value, ok := v.(time.Time)
	if ok {
		*t = TimeNormal{Time: value}
		return nil
	}
	return fmt.Errorf("can not convert %v to timestamp", v)
}

// UnmarshalJSON 实现 JSON 反序列化方法
func (t *TimeNormal) UnmarshalJSON(b []byte) error {
	var s string
	if err := sonic.Unmarshal(b, &s); err != nil {
		return err
	}

	// 动态推断时间格式
	layout, err := inferTimeLayout(s)
	if err != nil {
		return err
	}

	ts, err := time.Parse(layout, s)
	if err != nil {
		return fmt.Errorf("无法解析时间字符串: %s，推断的格式为: %s", s, layout)
	}

	// 如果是仅时间格式，将日期设置为当前日期
	if layout == "15:04:05" {
		now := time.Now()
		ts = time.Date(now.Year(), now.Month(), now.Day(), t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), t.Location())
	}
	t.Time = ts
	return nil
}

func (t *TimeNormal) FormatTime() string {
	if t.IsDateOnly {
		return t.Time.Format(time.DateOnly)
	}
	return t.Time.Format(time.DateTime)
}

type Posts struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}
