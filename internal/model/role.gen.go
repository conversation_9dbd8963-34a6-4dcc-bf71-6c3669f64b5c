// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameRole = "role"

// Role 角色表（树形结构）
type Role struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:角色ID" json:"id"`                                         // 角色ID
	Name        string     `gorm:"column:name;type:varchar(64);not null;comment:角色名称" json:"name"`                                                     // 角色名称
	Code        string     `gorm:"column:code;type:varchar(64);not null;uniqueIndex:uk_code,priority:1;comment:角色唯一编码" json:"code"`                    // 角色唯一编码
	Description *string    `gorm:"column:description;type:varchar(255);comment:角色描述" json:"description"`                                               // 角色描述
	ParentID    *int64     `gorm:"column:parent_id;type:bigint;index:idx_parent_id,priority:1;comment:父角色ID（NULL表示根角色）" json:"parentId"`               // 父角色ID（NULL表示根角色）
	LevelPath   string     `gorm:"column:level_path;type:varchar(255);not null;index:idx_level_path,priority:1;comment:层级路径（如1.2.3）" json:"levelPath"` // 层级路径（如1.2.3）
	Sort        *int64     `gorm:"column:sort;type:int;comment:排序（越大越靠前）" json:"sort"`                                                                 // 排序（越大越靠前）
	Status      *string    `gorm:"column:status;type:enum('enable','disabled');not null;default:enable;comment:状态（1启用 disabled禁用）" json:"status"`      // 状态（1启用 disabled禁用）
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                  // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`                  // 更新时间
}

// TableName Role's table name
func (*Role) TableName() string {
	return TableNameRole
}
