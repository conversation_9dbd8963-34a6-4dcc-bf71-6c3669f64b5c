// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
	"sort"
)

const TableNameRole = "role"

// Role 角色表（树形结构）
type Role struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:角色ID" json:"id"`                                    // 角色ID
	Name        string     `gorm:"column:name;type:varchar(64);not null;comment:角色名称" json:"name"`                                                // 角色名称
	Code        string     `gorm:"column:code;type:varchar(64);not null;comment:角色唯一编码" json:"code"`                                              // 角色唯一编码
	Description *string    `gorm:"column:description;type:varchar(255);comment:角色描述" json:"description"`                                          // 角色描述
	ParentID    *string    `gorm:"column:parent_id;type:varchar(50);comment:父角色ID（NULL表示根角色）" json:"parentId"`                                    // 父角色ID（NULL表示根角色）
	LevelPath   string     `gorm:"column:level_path;type:varchar(255);not null;comment:层级路径（如1.2.3）" json:"levelPath"`                            // 层级路径（如1.2.3）
	Sort        *int64     `gorm:"column:sort;type:int;comment:排序（越大越靠前）" json:"sort"`                                                            // 排序（越大越靠前）
	Status      *string    `gorm:"column:status;type:enum('enable','disabled');not null;default:enable;comment:状态（1启用 disabled禁用）" json:"status"` // 状态（1启用 disabled禁用）
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`             // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`             // 更新时间
	UUID        string     `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_role_uuid_unique,priority:1;comment:唯一ID" json:"uuid"`    // 唯一ID
	TenantID    *string    `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                // 租户ID
	Children    []*Role    `gorm:"-" json:"children"`
}

// TableName Role's table name
func (*Role) TableName() string {
	return TableNameRole
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *Role) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}

// ToTree 将平级数据转换为树形结构
func (t *Role) ToTree(nodeList []*Role) []*Role {
	var roots []*Role

	// 先遍历一遍节点列表，将每个节点的Children初始化为空切片
	for _, node := range nodeList {
		node.Children = make([]*Role, 0)
	}

	// 对节点列表按照ID进行排序
	sort.Slice(nodeList, func(i, j int) bool {
		return nodeList[i].ID < nodeList[j].ID
	})

	// 创建一个map，用于快速查找节点，键为节点UUID，值为节点指针
	nodeMap := make(map[string]*Role)
	for _, node := range nodeList {
		nodeMap[node.UUID] = node
	}

	// 遍历节点列表，构建树形结构
	for _, node := range nodeList {
		// 检查节点是否有明确的父节点
		if node.ParentID != nil && *node.ParentID != "" {
			parent, ok := nodeMap[*node.ParentID]
			if ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果找不到父节点，则当作顶级节点处理
				roots = append(roots, node)
			}
		} else {
			// 如果节点没有父节点或者父节点ID为空，直接作为顶级节点
			roots = append(roots, node)
		}
	}

	return roots
}

// FlattenRoleTree 将树形结构转换为平级列表
func FlattenRoleTree(roots []*Role) []*Role {
	var result []*Role
	
	var flatten func([]*Role)
	flatten = func(nodes []*Role) {
		for _, node := range nodes {
			result = append(result, node)
			if len(node.Children) > 0 {
				flatten(node.Children)
			}
		}
	}
	
	flatten(roots)
	return result
}

// IsEnabled 检查记录是否启用
func (m *Role) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *Role) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *Role) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
