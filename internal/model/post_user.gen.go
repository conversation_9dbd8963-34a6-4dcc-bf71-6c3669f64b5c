// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNamePostUser = "post_user"

// PostUser 岗位、用户关联表
type PostUser struct {

	/*
		主键id

	*/
	ID       uint64  `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id\n" json:"id"`
	PostID   string  `gorm:"column:post_id;type:varchar(50);not null;comment:岗位id" json:"postId"`                                             // 岗位id
	UserID   string  `gorm:"column:user_id;type:varchar(50);not null;comment:用户主键" json:"userId"`                                             // 用户主键
	UUID     string  `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_post_user_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID *string `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                  // 租户ID
}

// TableName PostUser's table name
func (*PostUser) TableName() string {
	return TableNamePostUser
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *PostUser) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
