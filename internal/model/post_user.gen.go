// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNamePostUser = "post_user"

// PostUser 岗位、用户关联表
type PostUser struct {

	/*
		主键id

	*/
	ID     int64 `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id\n" json:"id"`
	PostID int64 `gorm:"column:post_id;type:bigint unsigned;not null;index:idx_post_user_post_id,priority:1;index:idx_post_user_post_user,priority:1;index:idx_user_post,priority:2;comment:岗位id" json:"postId"` // 岗位id
	UserID int64 `gorm:"column:user_id;type:bigint unsigned;not null;index:idx_post_user_post_user,priority:2;index:idx_post_user_user_id,priority:1;index:idx_user_post,priority:1;comment:用户主键" json:"userId"` // 用户主键
}

// TableName PostUser's table name
func (*PostUser) TableName() string {
	return TableNamePostUser
}
