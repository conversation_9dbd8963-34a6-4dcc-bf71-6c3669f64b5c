// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameSystemOperationLog = "system_operation_logs"

// SystemOperationLog 日志表
type SystemOperationLog struct {
	ID            uint64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	CreateTime    *time.Time `gorm:"column:create_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:日志创建时间，自动记录插入时刻" json:"createTime"`                                  // 日志创建时间，自动记录插入时刻
	LogTimestamp  *time.Time `gorm:"column:log_timestamp;type:timestamp;comment:操作发生的时间戳，由应用程序设定" json:"logTimestamp"`                                                       // 操作发生的时间戳，由应用程序设定
	LogLevel      string     `gorm:"column:log_level;type:varchar(10);not null;comment:日志级别，例如 'DEBUG', 'INFO', 'WARN', 'ERROR'" json:"logLevel"`                            // 日志级别，例如 'DEBUG', 'INFO', 'WARN', 'ERROR'
	LogSource     string     `gorm:"column:log_source;type:varchar(50);not null;comment:日志来源，例如模块名称或类名" json:"logSource"`                                                    // 日志来源，例如模块名称或类名
	LogMessage    string     `gorm:"column:log_message;type:text;not null;comment:日志详细信息" json:"logMessage"`                                                                 // 日志详细信息
	UserID        *uint64    `gorm:"column:user_id;type:bigint unsigned;index:idx_user_id,priority:1;comment:执行操作的用户ID，关联 users 表" json:"userId"`                            // 执行操作的用户ID，关联 users 表
	OperationType *string    `gorm:"column:operation_type;type:enum('CREATE','UPDATE','DELETE','QUERY');comment:操作类型，如 'CREATE', 'UPDATE', 'DELETE' 等" json:"operationType"` // 操作类型，如 'CREATE', 'UPDATE', 'DELETE' 等
	ResponseTime  *float64   `gorm:"column:response_time;type:decimal(10,6);comment:请求响应时间，精确到微秒" json:"responseTime"`                                                       // 请求响应时间，精确到微秒
	UUID          string     `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_system_operation_logs_uuid_unique,priority:1;comment:唯一ID" json:"uuid"`            // 唯一ID
	TenantID      *string    `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                                         // 租户ID
}

// TableName SystemOperationLog's table name
func (*SystemOperationLog) TableName() string {
	return TableNameSystemOperationLog
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *SystemOperationLog) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
