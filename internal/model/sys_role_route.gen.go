// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameSysRoleRoute = "sys_role_route"

// SysRoleRoute 角色路由关联表
type SysRoleRoute struct {
	ID       int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	RoleID   string  `gorm:"column:role_id;type:varchar(50);not null" json:"roleId"`
	RouteID  string  `gorm:"column:route_id;type:varchar(50);not null" json:"routeId"`
	UUID     string  `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_sys_role_route_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID *string `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                       // 租户ID
}

// TableName SysRoleRoute's table name
func (*SysRoleRoute) TableName() string {
	return TableNameSysRoleRoute
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *SysRoleRoute) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
