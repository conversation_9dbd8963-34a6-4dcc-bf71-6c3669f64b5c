// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSysRoleRoute = "sys_role_route"

// SysRoleRoute 角色路由关联表
type SysRoleRoute struct {
	ID      int64 `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                              // 主键
	RoleID  int64 `gorm:"column:role_id;type:bigint;not null;uniqueIndex:uk_role_route,priority:1;comment:角色ID" json:"roleId"`   // 角色ID
	RouteID int64 `gorm:"column:route_id;type:bigint;not null;uniqueIndex:uk_role_route,priority:2;comment:路由ID" json:"routeId"` // 路由ID
}

// TableName SysRoleRoute's table name
func (*SysRoleRoute) TableName() string {
	return TableNameSysRoleRoute
}
