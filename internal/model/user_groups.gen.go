// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameUserGroup = "user_groups"

// UserGroup 用户分组表
type UserGroup struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:分组ID" json:"id"`                                        // 分组ID
	Name        string     `gorm:"column:name;type:varchar(50);not null;comment:分组名称" json:"name"`                                                    // 分组名称
	Description *string    `gorm:"column:description;type:varchar(255);comment:分组描述" json:"description"`                                              // 分组描述
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                 // 创建时间
	UUID        string     `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_user_groups_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID    *string    `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                    // 租户ID
}

// TableName UserGroup's table name
func (*UserGroup) TableName() string {
	return TableNameUserGroup
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *UserGroup) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
