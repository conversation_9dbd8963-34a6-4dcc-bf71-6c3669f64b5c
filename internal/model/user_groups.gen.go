// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserGroup = "user_groups"

// UserGroup 用户分组表
type UserGroup struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:分组ID" json:"id"`                        // 分组ID
	Name        string     `gorm:"column:name;type:varchar(50);not null;comment:分组名称" json:"name"`                                    // 分组名称
	Description *string    `gorm:"column:description;type:varchar(255);comment:分组描述" json:"description"`                              // 分组描述
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"` // 创建时间
}

// TableName UserGroup's table name
func (*UserGroup) TableName() string {
	return TableNameUserGroup
}
