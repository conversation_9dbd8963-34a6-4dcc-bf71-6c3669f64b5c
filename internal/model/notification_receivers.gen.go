// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameNotificationReceiver = "notification_receivers"

// NotificationReceiver 公告接收记录表
type NotificationReceiver struct {
	ID             int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                                                   // 主键ID
	NotificationID int64      `gorm:"column:notification_id;type:bigint;not null;uniqueIndex:uk_notification_receiver,priority:1;comment:关联的公告ID" json:"notificationId"`                            // 关联的公告ID
	ReceiverID     int64      `gorm:"column:receiver_id;type:bigint;not null;uniqueIndex:uk_notification_receiver,priority:2;index:idx_receiver_read,priority:1;comment:接收者用户ID" json:"receiverId"` // 接收者用户ID
	ReadStatus     *string    `gorm:"column:read_status;type:enum('unread','read');not null;index:idx_receiver_read,priority:2;default:unread;comment:阅读状态：unread=未读, read=已读" json:"readStatus"`   // 阅读状态：unread=未读, read=已读
	ReadTime       *time.Time `gorm:"column:read_time;type:datetime;comment:阅读时间" json:"readTime"`                                                                                                  // 阅读时间
	CreatedAt      *time.Time `gorm:"column:created_at;type:datetime;not null;index:idx_created,priority:1;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                               // 创建时间
}

// TableName NotificationReceiver's table name
func (*NotificationReceiver) TableName() string {
	return TableNameNotificationReceiver
}
