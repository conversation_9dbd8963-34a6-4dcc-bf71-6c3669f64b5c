// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameNotificationReceiver = "notification_receivers"

// NotificationReceiver 公告接收记录表
type NotificationReceiver struct {
	ID             int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                   // 主键ID
	NotificationID string     `gorm:"column:notification_id;type:varchar(50);not null;comment:关联的公告ID" json:"notificationId"`                                       // 关联的公告ID
	ReceiverID     string     `gorm:"column:receiver_id;type:varchar(50);not null;comment:接收者用户ID" json:"receiverId"`                                               // 接收者用户ID
	ReadStatus     *string    `gorm:"column:read_status;type:enum('unread','read');not null;default:unread;comment:阅读状态：unread=未读, read=已读" json:"readStatus"`      // 阅读状态：unread=未读, read=已读
	ReadTime       *time.Time `gorm:"column:read_time;type:datetime;comment:阅读时间" json:"readTime"`                                                                  // 阅读时间
	CreatedAt      *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                            // 创建时间
	UUID           string     `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_notification_receivers_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID       *string    `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                               // 租户ID
}

// TableName NotificationReceiver's table name
func (*NotificationReceiver) TableName() string {
	return TableNameNotificationReceiver
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *NotificationReceiver) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
