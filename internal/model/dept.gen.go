// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameDept = "dept"

// Dept 部门表，用于存储组织内的部门信息
type Dept struct {
	ID        int64          `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;index:idx_dept_id,priority:1;comment:主键ID" json:"id"`                          // 主键ID
	Name      string         `gorm:"column:name;type:varchar(100);not null;comment:部门名称" json:"name"`                                                                           // 部门名称
	ParentID  *int64         `gorm:"column:parent_id;type:bigint unsigned;index:idx_parent,priority:1;index:idx_parent_id,priority:1;comment:父级部门ID，顶级部门为NULL" json:"parentId"` // 父级部门ID，顶级部门为NULL
	ManagerID *int64         `gorm:"column:manager_id;type:bigint unsigned;index:idx_manager_id,priority:1;comment:部门负责人ID" json:"managerId"`                                   // 部门负责人ID
	CreatedAt *time.Time     `gorm:"column:created_at;type:datetime;not null;index:idx_created_at,priority:1;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`         // 创建时间
	UpdatedAt *time.Time     `gorm:"column:updated_at;type:datetime;not null;index:idx_updated_at,priority:1;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`         // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime;index:idx_deleted_at,priority:1;comment:删除时间" json:"-"`                                                     // 删除时间
	Status    *string        `gorm:"column:status;type:enum('enable','disabled');default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"`                              // 状态，enable表示启用，disabled表示禁用
	Sort      int64          `gorm:"column:sort;type:bigint;not null;index:idx_sort,priority:1;comment:排序字段" json:"sort"`                                                       // 排序字段
	Remark    *string        `gorm:"column:remark;type:varchar(255);comment:备注或描述" json:"remark"`                                                                               // 备注或描述
	CreateID  *int64         `gorm:"column:create_id;type:bigint unsigned;comment:创建者用户ID" json:"createId"`                                                                     // 创建者用户ID
	UpdateID  *int64         `gorm:"column:update_id;type:bigint;comment:更新人id" json:"updateId"`                                                                                // 更新人id
	UuID      string         `gorm:"column:uu_id;type:varchar(50);not null;comment:唯一id" json:"uuId"`                                                                           // 唯一id
}

// TableName Dept's table name
func (*Dept) TableName() string {
	return TableNameDept
}
