// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
	"admin/internal/utils"
	"admin/pkg/jwt"
	"sort"
)

const TableNameDept = "dept"

// Dept 部门表，用于存储组织内的部门信息
type Dept struct {
	ID        uint64         `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	Name      string         `gorm:"column:name;type:varchar(100);not null;comment:部门名称" json:"name"`                                              // 部门名称
	ParentID  *string        `gorm:"column:parent_id;type:varchar(50);comment:父级部门ID，顶级部门为NULL" json:"parentId"`                                   // 父级部门ID，顶级部门为NULL
	ManagerID *string        `gorm:"column:manager_id;type:varchar(50);index:idx_manager_id,priority:1;comment:部门负责人ID" json:"managerId"`          // 部门负责人ID
	CreatedAt *time.Time     `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`            // 创建时间
	UpdatedAt *time.Time     `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`            // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime;comment:删除时间" json:"-"`                                                        // 删除时间
	Status    *string        `gorm:"column:status;type:enum('enable','disabled');default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"` // 状态，enable表示启用，disabled表示禁用
	Sort      int64          `gorm:"column:sort;type:bigint;not null;comment:排序字段" json:"sort"`                                                    // 排序字段
	Remark    *string        `gorm:"column:remark;type:varchar(255);comment:备注或描述" json:"remark"`                                                  // 备注或描述
	CreateID  *string        `gorm:"column:create_id;type:varchar(50);comment:创建者用户ID" json:"createId"`                                            // 创建者用户ID
	UpdateID  *string        `gorm:"column:update_id;type:varchar(50);comment:更新人id" json:"updateId"`                                              // 更新人id
	UUID      string         `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_dept_uuid_unique,priority:1;comment:唯一id" json:"uuid"`   // 唯一id
	TenantID  *string        `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                               // 租户ID
	Children  []*Dept        `gorm:"-" json:"children"`
}

// TableName Dept's table name
func (*Dept) TableName() string {
	return TableNameDept
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *Dept) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}

// ToTree 将平级数据转换为树形结构
func (t *Dept) ToTree(nodeList []*Dept) []*Dept {
	var roots []*Dept

	// 先遍历一遍节点列表，将每个节点的Children初始化为空切片
	for _, node := range nodeList {
		node.Children = make([]*Dept, 0)
	}

	// 对节点列表按照ID进行排序
	sort.Slice(nodeList, func(i, j int) bool {
		return nodeList[i].ID < nodeList[j].ID
	})

	// 创建一个map，用于快速查找节点，键为节点UUID，值为节点指针
	nodeMap := make(map[string]*Dept)
	for _, node := range nodeList {
		nodeMap[node.UUID] = node
	}

	// 遍历节点列表，构建树形结构
	for _, node := range nodeList {
		// 检查节点是否有明确的父节点
		if node.ParentID != nil && *node.ParentID != "" {
			parent, ok := nodeMap[*node.ParentID]
			if ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果找不到父节点，则当作顶级节点处理
				roots = append(roots, node)
			}
		} else {
			// 如果节点没有父节点或者父节点ID为空，直接作为顶级节点
			roots = append(roots, node)
		}
	}

	return roots
}

// FlattenDeptTree 将树形结构转换为平级列表
func FlattenDeptTree(roots []*Dept) []*Dept {
	var result []*Dept
	
	var flatten func([]*Dept)
	flatten = func(nodes []*Dept) {
		for _, node := range nodes {
			result = append(result, node)
			if len(node.Children) > 0 {
				flatten(node.Children)
			}
		}
	}
	
	flatten(roots)
	return result
}

// IsDeleted 检查记录是否已被软删除
func (m *Dept) IsDeleted() bool {
	return m.DeletedAt.Valid
}

// Restore 恢复软删除的记录
func (m *Dept) Restore(db *gorm.DB) error {
	return db.Model(m).Update("deleted_at", nil).Error
}

// IsEnabled 检查记录是否启用
func (m *Dept) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *Dept) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *Dept) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
