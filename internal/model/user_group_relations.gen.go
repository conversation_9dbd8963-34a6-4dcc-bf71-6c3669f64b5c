// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserGroupRelation = "user_group_relations"

// UserGroupRelation 用户分组关系表
type UserGroupRelation struct {
	ID        int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                       // 主键ID
	UserID    int64      `gorm:"column:user_id;type:bigint;not null;uniqueIndex:uk_user_group,priority:1;comment:用户ID" json:"userId"`                              // 用户ID
	GroupID   int64      `gorm:"column:group_id;type:bigint;not null;uniqueIndex:uk_user_group,priority:2;index:idx_group,priority:1;comment:分组ID" json:"groupId"` // 分组ID
	CreatedAt *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:加入时间" json:"createTime"`                                // 加入时间
}

// TableName UserGroupRelation's table name
func (*UserGroupRelation) TableName() string {
	return TableNameUserGroupRelation
}
