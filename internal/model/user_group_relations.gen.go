// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameUserGroupRelation = "user_group_relations"

// UserGroupRelation 用户分组关系表
type UserGroupRelation struct {
	ID        int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                                 // 主键ID
	UserID    int64      `gorm:"column:user_id;type:bigint;not null;comment:用户ID" json:"userId"`                                                             // 用户ID
	GroupID   int64      `gorm:"column:group_id;type:bigint;not null;index:idx_group,priority:1;comment:分组ID" json:"groupId"`                                // 分组ID
	CreatedAt *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:加入时间" json:"createTime"`                          // 加入时间
	UUID      string     `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_user_group_relations_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID  *string    `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                             // 租户ID
}

// TableName UserGroupRelation's table name
func (*UserGroupRelation) TableName() string {
	return TableNameUserGroupRelation
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *UserGroupRelation) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
