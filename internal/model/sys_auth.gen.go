// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameSysAuth = "sys_auth"

// SysAuth 权限表
type SysAuth struct {
	ID       int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                 // 主键
	RouteID  *int64 `gorm:"column:route_id;type:bigint;index:idx_route_id,priority:1;comment:关联的路由ID" json:"routeId"` // 关联的路由ID
	AuthKey  string `gorm:"column:auth_key;type:varchar(64);not null;comment:权限标识" json:"authKey"`                    // 权限标识
	AuthName string `gorm:"column:auth_name;type:varchar(64);not null;comment:权限名称" json:"authName"`                  // 权限名称
}

// TableName SysAuth's table name
func (*SysAuth) TableName() string {
	return TableNameSysAuth
}
