// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"admin/internal/utils"
	"gorm.io/gorm"
	"admin/pkg/jwt"
)

const TableNameSysAuth = "sys_auth"

// SysAuth 权限表
type SysAuth struct {
	ID       int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                       // 主键
	RouteID  *string `gorm:"column:route_id;type:varchar(50);comment:关联的路由ID" json:"routeId"`                                                // 关联的路由ID
	AuthKey  string  `gorm:"column:auth_key;type:varchar(64);not null;comment:权限标识" json:"authKey"`                                          // 权限标识
	AuthName string  `gorm:"column:auth_name;type:varchar(64);not null;comment:权限名称" json:"authName"`                                        // 权限名称
	UUID     string  `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_sys_auth_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID *string `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                                 // 租户ID
}

// TableName SysAuth's table name
func (*SysAuth) TableName() string {
	return TableNameSysAuth
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *SysAuth) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}
