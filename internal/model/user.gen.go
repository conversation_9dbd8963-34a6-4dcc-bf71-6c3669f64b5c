// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
	"admin/internal/utils"
	"admin/pkg/jwt"
)

const TableNameUser = "user"

// User 用户表
type User struct {
	ID          uint64         `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                                   // 主键ID
	CreatedAt   *time.Time     `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"createTime"`               // 创建时间
	UpdatedAt   *time.Time     `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updateTime"`               // 更新时间
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);comment:删除时间" json:"-"`                                                              // 删除时间
	Status      *string        `gorm:"column:status;type:enum('enable','disabled');not null;default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"` // 状态，enable表示启用，disabled表示禁用
	Sort        *int64         `gorm:"column:sort;type:bigint;comment:排序字段" json:"sort"`                                                                      // 排序字段
	UUID        string         `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_user_uuid_unique,priority:1;comment:全局唯一ID" json:"uuid"`          // 全局唯一ID
	CreateID    *int64         `gorm:"column:create_id;type:bigint;comment:创建人ID" json:"createId"`                                                            // 创建人ID
	UpdateID    *int64         `gorm:"column:update_id;type:bigint;comment:更新人ID" json:"updateId"`                                                            // 更新人ID
	Sex         *string        `gorm:"column:sex;type:enum('male','female','secrecy');default:secrecy;comment:性别，男：male，女：female，保密：secrecy" json:"sex"`      // 性别，男：male，女：female，保密：secrecy
	Account     string         `gorm:"column:account;type:varchar(30);not null;comment:账号" json:"account"`                                                    // 账号
	Username    string         `gorm:"column:username;type:varchar(30);not null;comment:账号名称" json:"username"`                                                // 账号名称
	Password    string         `gorm:"column:password;type:varchar(255);not null;comment:密码（加密存储）" json:"-"`                                                  // 密码（加密存储）
	Phone       *string        `gorm:"column:phone;type:varchar(15);comment:手机号码" json:"phone"`                                                               // 手机号码
	Email       *string        `gorm:"column:email;type:varchar(100);comment:邮箱" json:"email"`                                                                // 邮箱
	Avatar      *string        `gorm:"column:avatar;type:varchar(1000);comment:头像二进制" json:"avatar"`                                                          // 头像二进制
	Remark      *string        `gorm:"column:remark;type:varchar(1000);comment:备注或描述" json:"remark"`                                                          // 备注或描述
	LastLoginIP *string        `gorm:"column:last_login_ip;type:varchar(15);comment:最后登录IP" json:"lastLoginIp"`                                               // 最后登录IP
	LastLoginAt *time.Time     `gorm:"column:last_login_at;type:datetime(3);comment:最后登录时间" json:"lastLoginAt"`                                               // 最后登录时间
	LoginCount  *int64         `gorm:"column:login_count;type:bigint;comment:登录次数" json:"loginCount"`                                                         // 登录次数
	TenantID    *string        `gorm:"column:tenant_id;type:varchar(50);comment:租户" json:"tenantId"`                                                          // 租户
	RoleID      int64          `gorm:"column:role_id;type:bigint;not null;comment:角色id" json:"roleId"`                                                        // 角色id
	Post        []*Posts       `gorm:"serializer:json; <-:false" json:"post,omitzero"`
}

// TableName User's table name
func (*User) TableName() string {
	return TableNameUser
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *User) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}

// IsDeleted 检查记录是否已被软删除
func (m *User) IsDeleted() bool {
	return m.DeletedAt.Valid
}

// Restore 恢复软删除的记录
func (m *User) Restore(db *gorm.DB) error {
	return db.Model(m).Update("deleted_at", nil).Error
}

// IsEnabled 检查记录是否启用
func (m *User) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *User) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *User) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
