// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameTenant = "tenant"

// Tenant 租户表
type Tenant struct {
	ID         int64           `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;uniqueIndex:id,priority:1;comment:租户主键id" json:"id"` // 租户主键id
	TenantName *string         `gorm:"column:tenant_name;type:varchar(255)" json:"tenantName"`
	DeletedAt  *gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp;index:deleted_at,priority:1" json:"-"`
	TenantKey  string          `gorm:"column:tenant_key;type:varchar(50);not null;uniqueIndex:tenant_key,priority:1" json:"tenantKey"`
}

// TableName Tenant's table name
func (*Tenant) TableName() string {
	return TableNameTenant
}
