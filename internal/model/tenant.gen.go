// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
	"admin/internal/utils"
)

const TableNameTenant = "tenant"

// Tenant 租户表
type Tenant struct {
	ID         int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:租户主键id" json:"id"` // 租户主键id
	TenantName *string        `gorm:"column:tenant_name;type:varchar(255)" json:"tenantName"`
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);comment:删除时间" json:"-"`                                                     // 删除时间
	UUID       string         `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_tenant_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
}

// TableName Tenant's table name
func (*Tenant) TableName() string {
	return TableNameTenant
}

// BeforeCreate 在创建记录前自动生成UUID
func (m *Tenant) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
	return nil
}

// IsDeleted 检查记录是否已被软删除
func (m *Tenant) IsDeleted() bool {
	return m.DeletedAt.Valid
}

// Restore 恢复软删除的记录
func (m *Tenant) Restore(db *gorm.DB) error {
	return db.Model(m).Update("deleted_at", nil).Error
}
