// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNamePost = "post"

// Post 岗位表，用于存储部门内的岗位信息
type Post struct {
	ID          int64           `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;index:idx_post_id,priority:1;index:idx_post_id_name,priority:1;comment:主键ID" json:"id"` // 主键ID
	Name        string          `gorm:"column:name;type:varchar(100);not null;index:idx_post_id_name,priority:2;comment:岗位名称" json:"name"`                                                  // 岗位名称
	DeptID      int64           `gorm:"column:dept_id;type:bigint unsigned;not null;index:idx_dept,priority:1;comment:所属部门ID" json:"deptId"`                                                // 所属部门ID
	Description *string         `gorm:"column:description;type:varchar(255);comment:岗位描述" json:"description"`                                                                               // 岗位描述
	CreatedAt   *time.Time      `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`                                                  // 创建时间
	UpdatedAt   *time.Time      `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`                                                  // 更新时间
	CreateID    *int64          `gorm:"column:create_id;type:bigint unsigned;comment:创建者用户ID" json:"createId"`                                                                              // 创建者用户ID
	UpdateID    *int64          `gorm:"column:update_id;type:bigint unsigned;comment:更新者用户ID" json:"updateId"`                                                                              // 更新者用户ID
	Status      *string         `gorm:"column:status;type:enum('enable','disabled');default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"`                                       // 状态，enable表示启用，disabled表示禁用
	DeletedAt   *gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(3);comment:删除时间" json:"-"`                                                                                          // 删除时间
	CreateAt    *time.Time      `gorm:"column:create_at;type:datetime(3)" json:"createAt"`
	UpdateAt    *time.Time      `gorm:"column:update_at;type:datetime(3)" json:"updateAt"`
}

// TableName Post's table name
func (*Post) TableName() string {
	return TableNamePost
}
