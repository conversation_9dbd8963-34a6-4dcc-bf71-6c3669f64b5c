// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
	"admin/internal/utils"
	"admin/pkg/jwt"
)

const TableNamePost = "post"

// Post 岗位表，用于存储部门内的岗位信息
type Post struct {
	ID          uint64         `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	Name        string         `gorm:"column:name;type:varchar(100);not null;comment:岗位名称" json:"name"`                                              // 岗位名称
	DeptID      string         `gorm:"column:dept_id;type:varchar(50);not null;index:idx_dept,priority:1;comment:所属部门ID" json:"deptId"`              // 所属部门ID
	Description *string        `gorm:"column:description;type:varchar(255);comment:岗位描述" json:"description"`                                         // 岗位描述
	CreatedAt   *time.Time     `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createTime"`            // 创建时间
	UpdatedAt   *time.Time     `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updateTime"`            // 更新时间
	CreateID    *string        `gorm:"column:create_id;type:varchar(50);comment:创建者用户ID" json:"createId"`                                            // 创建者用户ID
	UpdateID    *string        `gorm:"column:update_id;type:varchar(50);comment:更新者用户ID" json:"updateId"`                                            // 更新者用户ID
	Status      *string        `gorm:"column:status;type:enum('enable','disabled');default:enable;comment:状态，enable表示启用，disabled表示禁用" json:"status"` // 状态，enable表示启用，disabled表示禁用
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at;type:datetime(3);comment:删除时间" json:"-"`                                                     // 删除时间
	CreateAt    *time.Time     `gorm:"column:create_at;type:datetime(3)" json:"createAt"`
	UpdateAt    *time.Time     `gorm:"column:update_at;type:datetime(3)" json:"updateAt"`
	UUID        string         `gorm:"column:uuid;type:varchar(50);not null;uniqueIndex:idx_post_uuid_unique,priority:1;comment:唯一ID" json:"uuid"` // 唯一ID
	TenantID    *string        `gorm:"column:tenant_id;type:varchar(50);comment:租户ID" json:"tenantId"`                                             // 租户ID
}

// TableName Post's table name
func (*Post) TableName() string {
	return TableNamePost
}

// BeforeCreate 在创建记录前自动生成UUID和设置租户ID
func (m *Post) BeforeCreate(tx *gorm.DB) error {
	if m.UUID == "" {
		m.UUID = utils.GetUUID()
	}
   // 从上下文中获取租户ID
  if m.TenantID==nil {
    if userInfo, ok := tx.Statement.Context.Value("claims").(*jwt.MyCustomClaims); ok {
       m.TenantID = &userInfo.TenantID
   }
  }
	return nil
}

// IsDeleted 检查记录是否已被软删除
func (m *Post) IsDeleted() bool {
	return m.DeletedAt.Valid
}

// Restore 恢复软删除的记录
func (m *Post) Restore(db *gorm.DB) error {
	return db.Model(m).Update("deleted_at", nil).Error
}

// IsEnabled 检查记录是否启用
func (m *Post) IsEnabled() bool {
	return m.Status != nil && *m.Status == "enable"
}

// Enable 启用记录
func (m *Post) Enable(db *gorm.DB) error {
	status := "enable"
	return db.Model(m).Update("status", status).Error
}

// Disable 禁用记录
func (m *Post) Disable(db *gorm.DB) error {
	status := "disabled"
	return db.Model(m).Update("status", status).Error
}
