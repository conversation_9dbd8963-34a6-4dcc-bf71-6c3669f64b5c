package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/utils"

	"gorm.io/gen/field"
)

type DeptRepository interface {
	QueryById(ctx context.Context, id *v1.ID) (*model.Dept, error)
	Create(ctx context.Context, post *model.Dept) error
	Update(ctx context.Context, post *model.Dept) error
	Delete(ctx context.Context, ids *v1.IDS[string]) error
	QueryPage(ctx context.Context, post *v1.DeptPage) (*v1.Page[*model.Dept], error)
	UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error
	Query(ctx context.Context) ([]*model.Dept, error)
}

type deptRepository struct {
	*Repository
}

func NewDeptRepository(r *Repository) DeptRepository {
	return &deptRepository{
		Repository: r,
	}
}

func (r *deptRepository) getColumn() []field.Expr {
	return []field.Expr{
		r.gen.Dept.ID,
		r.gen.Dept.Name,
		r.gen.Dept.Remark,
		r.gen.Dept.Status,
		r.gen.Dept.Sort,
		r.gen.Dept.ParentID,
		r.gen.Dept.ManagerID,
		r.gen.Dept.CreatedAt,
		r.gen.Dept.UpdatedAt,
		r.gen.Dept.UUID,
	}
}

// Create implements PostRepository.
func (r *deptRepository) Create(ctx context.Context, dept *model.Dept) error {
	err := r.gen.WithContext(ctx).Dept.Create(dept)
	if err != nil {
		return v1.ErrCreateFailed
	}
	return nil
}

// UpdateStatus implements PostRepository.
func (r *deptRepository) UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error {
	_, err := r.gen.WithContext(ctx).Dept.Where(r.gen.Dept.UUID.In(ids.Ids...)).Update(r.gen.Dept.Status, status)
	if err != nil {
		return err
	}
	return nil
}

// Delete implements PostRepository.
func (r *deptRepository) Delete(ctx context.Context, ids *v1.IDS[string]) error {
	_, err := r.gen.WithContext(ctx).Dept.Where(r.gen.Dept.UUID.In(ids.Ids...)).Delete()
	if err != nil {
		return v1.ErrDeleteFailed
	}
	return nil
}

// QueryById implements PostRepository.
func (r *deptRepository) QueryById(ctx context.Context, id *v1.ID) (*model.Dept, error) {
	return r.gen.WithContext(ctx).Dept.Where(r.gen.Dept.UUID.Eq(id.ID)).Select(r.getColumn()...).First()
}

// QueryPage implements PostRepository.
func (r *deptRepository) QueryPage(ctx context.Context, dept *v1.DeptPage) (*v1.Page[*model.Dept], error) {
	queryFind := r.gen.WithContext(ctx).Dept
	if dept.CreateTime.CreateTime.IsZero() {
		startTime, endTime := utils.GenerateTimeRange(dept.CreateTime.CreateTime.GetTime())
		queryFind = queryFind.Where(r.gen.Dept.CreatedAt.Between(startTime, endTime))
	}
	if dept.Name != "" {
		queryFind = queryFind.Where(r.gen.Dept.Name.Like(dept.Name))
	}
	if dept.ManagerID != "" {
		queryFind = queryFind.Where(r.gen.Dept.ManagerID.Eq(dept.ManagerID))
	}
	if dept.Status != "" {
		queryFind = queryFind.Where(r.gen.Dept.Status.Eq(dept.Status))
	}
	postList, count, err := queryFind.FindByPage(dept.Page, dept.PageSize)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	return &v1.Page[*model.Dept]{
		Current:  dept.Page,
		PageSize: dept.PageSize,
		Total:    count,
		List:     postList,
	}, nil
}

// Update implements PostRepository.
func (r *deptRepository) Update(ctx context.Context, post *model.Dept) error {
	_, err := r.gen.WithContext(ctx).Dept.Where(r.gen.Dept.UUID.Eq(post.UUID)).Updates(post)
	if err != nil {
		return v1.ErrUpdateFailed
	}
	return nil
}

// Query implements PostRepository.
func (r *deptRepository) Query(ctx context.Context) ([]*model.Dept, error) {
	postList, err := r.gen.WithContext(ctx).Dept.Select(r.getColumn()...).
		Order(r.gen.Dept.CreatedAt.Desc()).Find()
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	return postList, nil
}
