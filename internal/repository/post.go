package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/utils"

	"gorm.io/gen/field"
)

type PostRepository interface {
	QueryById(ctx context.Context, id *v1.ID) (*model.Post, error)
	Create(ctx context.Context, post *model.Post) error
	Update(ctx context.Context, post *model.Post) error
	Delete(ctx context.Context, ids *v1.IDS[string]) error
	QueryPage(ctx context.Context, post *v1.PostPage) (*v1.Page[*model.Post], error)
	UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error
	Query(ctx context.Context) ([]*model.Post, error)
}

type postRepository struct {
	*Repository
}

func NewPostRepository(r *Repository) PostRepository {
	return &postRepository{
		Repository: r,
	}
}

func (r *postRepository) getColumn() []field.Expr {
	return []field.Expr{
		r.gen.Post.ID,
		r.gen.Post.Name,
		r.gen.Post.Description,
		r.gen.Post.CreatedAt,
		r.gen.Post.UpdatedAt,
		r.gen.Post.UUID,
	}
}

// Create implements PostRepository.
func (r *postRepository) Create(ctx context.Context, post *model.Post) error {
	return r.gen.WithContext(ctx).Post.Create(post)
}

// UpdateStatus implements PostRepository.
func (r *postRepository) UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error {
	_, err := r.gen.WithContext(ctx).Post.
		Where(r.gen.Post.UUID.In(ids.Ids...)).
		Update(r.gen.Post.Status, status)
	return err
}

// Delete implements PostRepository.
func (r *postRepository) Delete(ctx context.Context, ids *v1.IDS[string]) error {
	_, err := r.gen.WithContext(ctx).Post.
		Where(r.gen.Post.UUID.In(ids.Ids...)).
		Delete()
	return err
}

// QueryById implements PostRepository.
func (r *postRepository) QueryById(ctx context.Context, id *v1.ID) (*model.Post, error) {
	return r.gen.WithContext(ctx).Post.Where(r.gen.Post.UUID.Eq(id.ID)).
		Select(r.getColumn()...).
		First()
}

// QueryPage implements PostRepository.
func (r *postRepository) QueryPage(ctx context.Context, post *v1.PostPage) (*v1.Page[*model.Post], error) {
	queryFind := r.gen.WithContext(ctx).Post
	if post.CreateTime.CreateTime.IsZero() {
		startTime, endTime := utils.GenerateTimeRange(post.CreateTime.CreateTime.GetTime())
		queryFind = queryFind.Where(r.gen.Post.CreatedAt.Between(startTime, endTime))
	}
	if post.Name != "" {
		queryFind = queryFind.Where(r.gen.Post.Name.Like(post.Name))
	}
	postList, count, err := queryFind.FindByPage(post.Page, post.PageSize)
	if err != nil {
		return nil, v1.ErrPostPageQueryFailed
	}
	return &v1.Page[*model.Post]{
		Current:  post.Page,
		PageSize: post.PageSize,
		Total:    count,
		List:     postList,
	}, nil
}

// Update implements PostRepository.
func (r *postRepository) Update(ctx context.Context, post *model.Post) error {
	_, err := r.gen.WithContext(ctx).Post.Where(r.gen.Post.UUID.
		Eq(post.UUID)).
		Updates(post)
	return err
}

// Query implements PostRepository.
func (r *postRepository) Query(ctx context.Context) ([]*model.Post, error) {
	postList, err := r.gen.WithContext(ctx).Post.
		Select(r.getColumn()...).
		Order(r.gen.Post.CreateAt.Desc()).
		Find()
	if err != nil {
		return nil, v1.ErrPostQueryFailed
	}
	return postList, nil
}
