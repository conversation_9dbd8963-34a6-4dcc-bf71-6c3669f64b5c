package repository

import (
	"context"
	"fmt"

	v1 "admin/api/v1"
	"admin/internal/gen"
	"admin/internal/model"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gen/field"
)

type RouteRepository interface {
	QueryById(ctx context.Context, id *v1.QID) (*v1.Route, error)
	Create(ctx context.Context, route *v1.Route) error
	Update(ctx context.Context, route *v1.Route) error
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	Query(ctx context.Context) ([]*v1.Route, error)
	GetPermissions(ctx context.Context, roleId int64) ([]string, error)
	UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error
	AddRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error
	DeleteRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error
	GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error)
}

type routeRepository struct {
	*Repository
}

func NewRouteRepository(r *Repository) RouteRepository {
	return &routeRepository{
		Repository: r,
	}
}

func (r *routeRepository) getColumn() []field.Expr {
	return []field.Expr{
		r.gen.SysRoute.ID,
		r.gen.SysRoute.Name,
		r.gen.SysRoute.Path,
		r.gen.SysRoute.Component,
		r.gen.SysRoute.Redirect,
		r.gen.SysRoute.ParentID,
		r.gen.SysRoute.Sort,
		r.gen.SysRoute.Status,
		r.gen.SysRoute.IsLayout,
		r.gen.SysRoute.CreatedAt,
		r.gen.SysRoute.UpdatedAt,
		r.gen.SysRouteMetum.RouteID,
		r.gen.SysRouteMetum.Title,
		r.gen.SysRouteMetum.Icon,
		r.gen.SysRouteMetum.Hidden,
		r.gen.SysRouteMetum.ActiveMenu,
		r.gen.SysRouteMetum.KeepAlive,
		r.gen.SysRouteMetum.Auth,
		r.gen.SysRouteMetum.Breadcrumb,
		r.gen.SysRouteMetum.External,
		r.gen.SysRouteMetum.ExternalURL,
	}
}

func (r *routeRepository) Create(ctx context.Context, route *v1.Route) error {
	// 检查路由是否已存在
	exists, err := r.checkRouteExists(ctx, route.Name, route.Path)
	if err != nil {
		return fmt.Errorf("检查路由存在性失败: %w", err)
	}
	if exists {
		return errors.New(v1.ErrRouteAlreadyExists) // 使用预定义错误类型
	}

	// 使用更简洁的事务写法
	return r.gen.Transaction(func(tx *gen.Query) error {
		// 创建路由记录
		if err := tx.WithContext(ctx).SysRoute.
			Omit(r.gen.SysRoute.ID).
			Create(route.SysRoute); err != nil {
			return fmt.Errorf("创建路由记录失败: %w", err)
		}

		// 如果有元数据则创建
		if route.SysRouteMetum != nil {
			route.SysRouteMetum.RouteID = route.SysRoute.ID
			if err := tx.WithContext(ctx).
				SysRouteMetum.
				Create(route.SysRouteMetum); err != nil {
				return fmt.Errorf("创建路由元数据失败: %w", err)
			}
		}

		return nil
	})
}

// checkRouteExists 检查路由名称或路径是否已存在
func (r *routeRepository) checkRouteExists(ctx context.Context, name, path string) (bool, error) {
	count, err := r.gen.WithContext(ctx).SysRoute.
		Where(r.gen.SysRoute.Name.Eq(name)).
		Or(r.gen.SysRoute.Path.Eq(path)).
		Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateStatus implements routeRepository.
func (r *routeRepository) UpdateStatus(ctx context.Context, ids *v1.QIDs[int64], status string) error {
	_, err := r.gen.WithContext(ctx).SysRoute.Where(r.gen.SysRoute.ID.In(ids.Ids...)).Update(r.gen.SysRoute.Status, status)
	return err
}

// Delete implements routeRepository.
func (r *routeRepository) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		// 根据ids 查出 SysRouteMetum 的 RouteID
		routeMetumList, err := r.gen.WithContext(ctx).SysRouteMetum.
			Where(r.gen.SysRouteMetum.RouteID.In(ids.Ids...)).
			Select(r.gen.SysRouteMetum.RouteID).
			Find()
		if err != nil {
			return errors.Wrap(err, "查询路由元数据失败")
		}
		if len(routeMetumList) == 0 {
			return errors.New("没有找到对应的路由元数据")
		}
		// 提取 RouteID
		routeIds := lo.Map(routeMetumList, func(m *model.SysRouteMetum, _ int) int64 {
			return m.RouteID
		})
		// 删除 SysRouteMetum
		_, err = r.gen.WithContext(ctx).SysRouteMetum.Where(r.gen.SysRouteMetum.RouteID.In(routeIds...)).Delete()
		if err != nil {
			return errors.Wrap(err, "删除路由元数据失败")
		}
		// 删除 SysRoute
		_, err = r.gen.WithContext(ctx).SysRoute.Where(r.gen.SysRoute.ID.In(ids.Ids...)).Delete()
		if err != nil {
			return errors.Wrap(err, "删除路由失败")
		}
		return nil
	})
}

// QueryById implements routeRepository.
func (r *routeRepository) QueryById(ctx context.Context, id *v1.QID) (route *v1.Route, err error) {
	err = r.gen.WithContext(ctx).SysRoute.
		Where(r.gen.SysRoute.ID.Eq(id.ID)).Select(r.getColumn()...).
		LeftJoin(r.gen.SysRouteMetum, r.gen.SysRouteMetum.RouteID).Scan(&route)
	return
}

// Update implements routeRepository.
func (r *routeRepository) Update(ctx context.Context, route *v1.Route) error {
	// 使用事务处理更新操作
	return r.gen.Transaction(func(tx *gen.Query) error {
		// 更新路由记录
		if _, err := tx.WithContext(ctx).SysRoute.
			Where(r.gen.SysRoute.ID.Eq(route.SysRoute.ID)).
			Updates(route.SysRoute); err != nil {
			return fmt.Errorf("更新路由记录失败: %w", err)
		}
		// 如果有元数据则更新
		if route.SysRouteMetum != nil {
			if _, err := tx.WithContext(ctx).SysRouteMetum.
				Where(r.gen.SysRouteMetum.RouteID.Eq(route.SysRouteMetum.RouteID)).
				Updates(route.SysRouteMetum); err != nil {
				return fmt.Errorf("更新路由元数据失败: %w", err)
			}
		}

		return nil
	})
}

// Query implements routeRepository.
func (r *routeRepository) Query(ctx context.Context) (routes []*v1.Route, err error) {
	err = r.gen.WithContext(ctx).SysRoute.
		Select(r.getColumn()...).
		LeftJoin(r.gen.SysRouteMetum, r.gen.SysRouteMetum.RouteID).
		Order(r.gen.SysRoute.Sort).
		Scan(&routes)
	if err != nil {
		return nil, fmt.Errorf("查询路由列表失败: %w", err)
	}
	return routes, nil
}

// GetPermissions 获取角色的权限
func (r *routeRepository) GetPermissions(ctx context.Context, roleId int64) ([]string, error) {
	return []string{}, errors.New("获取角色权限功能未实现")
}

// UpdatePermissions 更新角色的权限
func (r *routeRepository) UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error {
	return errors.New("更新角色权限功能未实现")
}

// AddRoutersForRole 为角色添加路由
func (r *routeRepository) AddRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error {
	if len(routerIds) == 0 {
		return nil
	}

	roleRouters := make([]*model.SysRoleRoute, 0, len(routerIds))
	for _, routerId := range routerIds {
		roleRouters = append(roleRouters, &model.SysRoleRoute{
			RoleID:  roleId,
			RouteID: routerId,
		})
	}

	return r.gen.WithContext(ctx).SysRoleRoute.Create(roleRouters...)
}

// DeleteRoutersForRole 删除角色的路由
func (r *routeRepository) DeleteRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error {
	if len(routerIds) == 0 {
		return nil
	}

	_, err := r.gen.WithContext(ctx).SysRoleRoute.
		Where(r.gen.SysRoleRoute.RoleID.Eq(roleId)).
		Where(r.gen.SysRoleRoute.RouteID.In(routerIds...)).
		Delete()

	return err
}

// GetRoutersByRoleId 获取角色的路由
func (r *routeRepository) GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error) {
	return r.gen.WithContext(ctx).SysRoleRoute.
		Where(r.gen.SysRoleRoute.RoleID.Eq(roleId)).
		Find()
}
