package repository

import (
	"context"
	"fmt"
	"time"

	v1 "admin/api/v1"
	"admin/internal/model"

	"gorm.io/gen/field"
)

type LoginRepository interface {
	GetByEmail(ctx context.Context, email string) (*model.User, error)              // 根据邮箱获取用户
	GetByMobile(ctx context.Context, phone string) (*model.User, error)             // 根据手机号获取用户
	GetByAccount(ctx context.Context, account string) (*model.User, error)          // 根据用户名获取用户
	LoginSuccess(ctx context.Context, id string, IP string) error                   // 登录成功后更新用户登录信息
	GetUserInfo(ctx context.Context, userID string) (user *v1.LoginUser, err error) // 获取登录信息
}
type loginRepository struct {
	*Repository
}

func NewLoginRepository(repo *Repository) LoginRepository {
	return &loginRepository{repo}
}

func (r *loginRepository) getonds() []field.Expr {
	return []field.Expr{
		r.gen.User.ID,
		r.gen.User.Account,
		r.gen.User.Password,
		r.gen.User.Username,
		r.gen.User.TenantID,
		r.gen.User.RoleID,
		r.gen.User.Status,
		r.gen.User.UUID,
	}
}

// GetByEmail 根据邮箱获取用户
func (r *loginRepository) GetByEmail(ctx context.Context, email string) (*model.User, error) {
	return r.gen.WithContext(ctx).User.Select(r.getonds()...).
		Where(r.gen.User.Email.Eq(email)).First()
}

// GetByMobile 根据手机号获取用户
func (r *loginRepository) GetByMobile(ctx context.Context, phone string) (*model.User, error) {
	return r.gen.WithContext(ctx).User.Select(r.getonds()...).Where(r.gen.User.Phone.Eq(phone)).
		First()
}

// GetByAccount 根据用户名获取用户
func (r *loginRepository) GetByAccount(ctx context.Context, account string) (*model.User, error) {
	return r.gen.WithContext(ctx).User.Select(r.getonds()...).
		Where(r.gen.User.Account.Eq(account)).First()
}

// 登录成功后更新用户登录信息
// LoginSuccess 登录成功后写入信息存储到
func (s *loginRepository) LoginSuccess(ctx context.Context, id string, IP string) error {
	u := s.gen.User
	_, err := s.gen.WithContext(ctx).User.Where(u.UUID.Eq(id)).
		UpdateColumnSimple(u.LoginCount.Add(1),
			u.LastLoginIP.Value(IP),
			u.LastLoginAt.Value(time.Now()))
	if err != nil {
		s.logger.Error("登录成功后写入信息存储到数据库失败")
		return fmt.Errorf("写入登录信息失败: %w", err)
	}
	return nil
}

// GetUserInfo 获取登录信息
func (s *loginRepository) GetUserInfo(ctx context.Context, userID string) (user *v1.LoginUser, err error) {
	u := s.gen.User
	err = s.gen.WithContext(ctx).User.Select(u.Avatar, u.Username, u.ID, u.Account).Where(s.gen.User.UUID.Eq(userID)).Scan(&user)
	user.Roles = []string{"role_admin"}
	user.Permissions = []string{"*:*:*"}
	return
}
