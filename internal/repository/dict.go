package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/gen"
	"admin/internal/model"

	"github.com/pkg/errors"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

// DictRepository Dict 字典表仓储接口
type DictRepository interface {
	Create(ctx context.Context, entity *model.Dict) error
	QueryById(ctx context.Context, req *v1.ID) (*model.Dict, error)
	QueryPage(ctx context.Context, req *v1.DictPageReq) (*v1.Page[*model.Dict], error)
	Update(ctx context.Context, entity *model.Dict) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	Query(ctx context.Context) ([]*model.Dict, error)

	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error

	Restore(ctx context.Context, req *v1.IDS[string]) error
	ForceDelete(ctx context.Context, req *v1.IDS[string]) error
}

// dictRepository Dict 字典表仓储实现
type dictRepository struct {
	*Repository
}

// NewDictRepository 创建Dict 字典表仓储
func NewDictRepository(r *Repository) DictRepository {
	return &dictRepository{
		Repository: r,
	}
}

// getColumns 获取查询字段
func (r *dictRepository) getColumns() []field.Expr {
	t := r.gen.Dict
	return []field.Expr{
		t.ID,

		t.CreatedAt,

		t.UpdatedAt,

		t.DeletedAt,

		t.Sort,

		t.UUID,

		t.CreateID,

		t.UpdateID,

		t.Name,

		t.Code,

		t.ParentID,

		t.ParentCode,

		t.Status,
	}
}

// buildQuery 构建基础查询
func (r *dictRepository) buildQuery(ctx context.Context) gen.IDictDo {
	return r.gen.Dict.WithContext(ctx).Select(r.getColumns()...)
}

// Create 创建Dict 字典表
func (r *dictRepository) Create(ctx context.Context, entity *model.Dict) error {
	if entity == nil {
		return v1.ErrInvalidParams
	}

	if err := r.gen.Dict.WithContext(ctx).Create(entity); err != nil {
		return v1.ErrCreateFailed
	}

	return nil
}

// QueryById 根据ID查询Dict 字典表
func (r *dictRepository) QueryById(ctx context.Context, req *v1.ID) (*model.Dict, error) {
	if req.ID == "" {
		return nil, v1.ErrInvalidParams
	}

	t := r.gen.Dict
	entity, err := r.buildQuery(ctx).
		Where(t.UUID.Eq(req.ID)).
		Where(t.DeletedAt.IsNull()).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, v1.ErrQueryFailed
	}

	return entity, nil
}

// QueryPage 分页查询Dict 字典表
func (r *dictRepository) QueryPage(ctx context.Context, req *v1.DictPageReq) (*v1.Page[*model.Dict], error) {
	t := r.gen.Dict
	query := r.buildQuery(ctx)

	// 构建查询条件 - 只包含实际的搜索字段

	if req.Name != "" {
		query = query.Where(t.Name.Like("%" + req.Name + "%"))
	}

	if req.Code != "" {
		query = query.Where(t.Code.Like("%" + req.Code + "%"))
	}

	if len(req.Status) > 0 {
		query = query.Where(t.Status.In(req.Status...))
	}

	if req.ParentCode != "" {
		query = query.Where(t.ParentCode.Like("%" + req.ParentCode + "%"))
	}

	// 默认排序

	query = query.Order(t.Sort.Desc(), t.CreatedAt.Desc())

	// 分页查询
	list, count, err := query.FindByPage(req.GetPage()-1, req.GetPageSize())
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	return &v1.Page[*model.Dict]{
		Current:  req.GetPage(),
		PageSize: req.GetPageSize(),
		Total:    count,
		List:     list,
	}, nil
}

// Update 更新Dict 字典表
func (r *dictRepository) Update(ctx context.Context, entity *model.Dict) error {
	if entity == nil {
		return v1.ErrInvalidParams
	}

	if entity.UUID == "" {
		return v1.ErrInvalidParams
	}

	t := r.gen.Dict
	result, err := t.WithContext(ctx).
		Where(t.UUID.Eq(entity.UUID)).
		Where(t.DeletedAt.IsNull()).
		Updates(entity)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	if result.RowsAffected == 0 {
		return v1.ErrRecordNotFound
	}

	return nil
}

// Delete 删除Dict 字典表
func (r *dictRepository) Delete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("删除ID列表不能为空")
	}

	t := r.gen.Dict
	query := t.WithContext(ctx).Where(t.UUID.In(req.Ids...))
	// 软删除（GORM会自动处理软删除逻辑）
	result, err := query.Delete()
	if err != nil {
		return v1.ErrDeleteFailed
	}

	if result.RowsAffected == 0 {
		return errors.New("没有找到要删除的记录")
	}

	return nil
}

// Query 查询所有Dict 字典表
func (r *dictRepository) Query(ctx context.Context) ([]*model.Dict, error) {
	t := r.gen.Dict
	query := r.buildQuery(ctx)
	// 排除软删除的记录
	query = query.Where(t.DeletedAt.IsNull())
	// 排序
	query = query.Order(t.Sort)
	entities, err := query.Find()
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	return entities, nil
}

// UpdateStatus 更新Dict 字典表状态
func (r *dictRepository) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	if status == "" {
		return errors.New("状态不能为空")
	}

	t := r.gen.Dict
	result, err := t.WithContext(ctx).
		Where(t.UUID.In(req.Ids...)).
		Where(t.DeletedAt.IsNull()).
		Update(t.Status, status)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	if result.RowsAffected == 0 {
		return errors.New("没有找到要更新的记录")
	}

	return nil
}

// Restore 恢复软删除的Dict 字典表
func (r *dictRepository) Restore(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	t := r.gen.Dict
	result, err := t.WithContext(ctx).
		Unscoped().
		Where(t.UUID.In(req.Ids...)).
		Where(t.DeletedAt.IsNotNull()).
		Update(t.DeletedAt, nil)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	if result.RowsAffected == 0 {
		return errors.New("没有找到要恢复的记录")
	}

	return nil
}

// ForceDelete 强制删除Dict 字典表（物理删除）
func (r *dictRepository) ForceDelete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	t := r.gen.Dict
	result, err := t.WithContext(ctx).
		Unscoped().
		Where(t.UUID.In(req.Ids...)).
		Delete()
	if err != nil {
		return v1.ErrDeleteFailed
	}

	if result.RowsAffected == 0 {
		return errors.New("没有找到要删除的记录")
	}

	return nil
}

// Count 统计Dict 字典表数量
func (r *dictRepository) Count(ctx context.Context) (int64, error) {
	t := r.gen.Dict
	query := t.WithContext(ctx)

	query = query.Where(t.DeletedAt.IsNull())

	count, err := query.Count()
	if err != nil {
		return 0, v1.ErrQueryFailed
	}

	return count, nil
}

// Exists 检查Dict 字典表是否存在
func (r *dictRepository) Exists(ctx context.Context, id string) (bool, error) {
	if id == "" {
		return false, errors.New("ID不能为空")
	}

	t := r.gen.Dict
	count, err := t.WithContext(ctx).
		Where(t.UUID.Eq(id)).
		Where(t.DeletedAt.IsNull()).
		Count()
	if err != nil {
		return false, v1.ErrQueryFailed
	}

	return count > 0, nil
}
