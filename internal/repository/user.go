package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/gen"
	"admin/internal/model"
	"admin/internal/utils"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

type UserRepository interface {
	Check(ctx context.Context, user *v1.Account) (*model.User, error)
	QueryById(ctx context.Context, id *v1.ID) (*model.User, error)
	UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error
	CreatePostUserList(user *model.User) []*model.PostUser
	Create(ctx context.Context, user *model.User) error
	QueryPage(ctx context.Context, user *v1.UserPage) (*v1.Page[*model.User], error)
	Delete(ctx context.Context, ids *v1.IDS[string]) error
	Update(ctx context.Context, user *model.User) error
	Query(ctx context.Context) ([]*model.User, error)
}

type userRepository struct {
	*Repository
}

func NewUserRepository(r *Repository) UserRepository {
	return &userRepository{
		Repository: r,
	}
}

func (r *userRepository) getColumn() []field.Expr {
	u := r.gen.User
	return []field.Expr{
		u.ID,
		u.Status,
		u.Account,
		u.Username,
		u.Phone,
		u.Email,
		u.Sex,
		u.Avatar,
		u.TenantID,
		u.RoleID,
		u.Status,
		u.Sort,
		u.CreatedAt,
		u.UpdatedAt,
		u.DeletedAt,
		u.LastLoginAt,
		u.LastLoginIP,
		u.LoginCount,
		u.Remark,
		u.UUID,
	}
}

// Check 检查手机号或邮箱是否存在
func (r *userRepository) Check(ctx context.Context, user *v1.Account) (*model.User, error) {
	// 构建查询条件
	query := r.gen.WithContext(ctx).User
	var conditions []field.Expr
	if user.Email != "" {
		conditions = append(conditions, r.gen.User.Email.Eq(user.Email))
	}
	if user.Phone != "" {
		conditions = append(conditions, r.gen.User.Phone.Eq(user.Phone))
	}
	// 如果没有提供邮箱或手机号，直接返回 false
	if len(conditions) == 0 {
		return nil, nil
	}
	// 使用 OR 条件合并查询
	exists, err := query.Where(field.Or(conditions...)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		r.logger.Warn("查询用户邮箱或手机号失败", zap.Error(err))
		return nil, err
	}
	return exists, nil
}

func (r *userRepository) UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error {
	_, err := r.gen.WithContext(ctx).User.Where(r.gen.User.UUID.In(ids.Ids...)).Update(r.gen.User.Status, "disable")
	if err != nil {
		return v1.ErrUserDisableFailed
	}
	return nil
}

// QueryById 根据ID查询User详情
func (r *userRepository) QueryById(ctx context.Context, id *v1.ID) (*model.User, error) {
	u := r.gen.User
	pu := r.gen.PostUser
	p := r.gen.Post
	user, err := u.WithContext(ctx).
		Select(append(r.getColumn(),
			field.NewUnsafeFieldRaw("IF(SUM(CASE WHEN (post.id IS NULL OR post.id = '') AND (post.name IS NULL OR post.name = '') THEN 1 ELSE 0 END) = COUNT(*), NULL, JSON_ARRAYAGG(IF((post.id IS NULL OR post.id = '') AND (post.name IS NULL OR post.name = ''), NULL, JSON_OBJECT('id', post.id, 'name', post.name)))) AS post"))...).
		LeftJoin(pu, pu.UserID.EqCol(u.UUID)).LeftJoin(p, p.UUID.EqCol(pu.PostID)).
		Where(r.gen.User.UUID.Eq(id.ID)).Group(r.gen.User.ID).First()
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, v1.ErrUserQueryDetailFailed
	}
	return user, nil
}

// CreatePostUserList 根据user动态创建postUser类型
func (r *userRepository) CreatePostUserList(user *model.User) []*model.PostUser {
	var postList []*model.PostUser
	for _, post := range user.Post {
		postList = append(postList, &model.PostUser{
			UserID: user.UUID,
			PostID: post.ID,
		})
	}
	return postList
}

// Create 创建User
func (r *userRepository) Create(ctx context.Context, user *model.User) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		err := r.gen.WithContext(ctx).User.Create(user)
		if err != nil {
			return v1.ErrUserCreateFailed
		}
		postList := r.CreatePostUserList(user)
		err = tx.WithContext(ctx).PostUser.CreateInBatches(postList, 100)
		if err != nil {
			return v1.ErrUserPostBatchCreateFailed
		}
		return nil
	})
}

// QueryPage 分页查询User
func (r *userRepository) QueryPage(ctx context.Context, user *v1.UserPage) (*v1.Page[*model.User], error) {
	u := r.gen.User
	pu := r.gen.PostUser
	p := r.gen.Post
	// 构建基础查询
	queryFind := u.WithContext(ctx).
		Select(append(r.getColumn(),
			field.NewUnsafeFieldRaw("IF(SUM(CASE WHEN (post.id IS NULL OR post.id = '') AND (post.name IS NULL OR post.name = '') THEN 1 ELSE 0 END) = COUNT(*), NULL, JSON_ARRAYAGG(IF((post.id IS NULL OR post.id = '') AND (post.name IS NULL OR post.name = ''), NULL, JSON_OBJECT('id', post.id, 'name', post.name)))) AS post"),
		)...).
		LeftJoin(pu, pu.UserID.EqCol(u.ID)).
		LeftJoin(p, p.UUID.EqCol(pu.PostID))

	// 动态查询条件
	if user.Email != "" {
		queryFind = queryFind.Where(u.Email.Like(utils.ToLike(user.Email)))
	}
	if user.Account != "" {
		queryFind = queryFind.Where(u.Account.Like(utils.ToLike(user.Account)))
	}
	if user.Username != "" {
		queryFind = queryFind.Where(u.Username.Like(utils.ToLike(user.Username)))
	}
	if user.Phone != "" {
		queryFind = queryFind.Where(u.Phone.Like(utils.ToLike(user.Phone)))
	}
	if len(user.PostIds) != 0 {
		queryFind = queryFind.Where(pu.PostID.In(user.PostIds...))
	}
	if !user.CreateTime.CreateTime.IsZero() {
		startTime, endTime := utils.GenerateTimeRange(user.CreateTime.CreateTime.GetTime())
		queryFind = queryFind.Where(u.CreatedAt.Between(startTime, endTime))
	}

	// 查询分页结果
	list, count, err := queryFind.Order(u.CreatedAt.Desc()).Group(r.gen.User.ID).FindByPage(user.Page-1, user.PageSize)
	if err != nil {
		return nil, v1.ErrUserPageQueryFailed
	}
	return &v1.Page[*model.User]{
		Total:    count,
		List:     list,
		Current:  user.Page,
		PageSize: user.PageSize,
	}, nil
}

// Delete 删除User
func (r *userRepository) Delete(ctx context.Context, ids *v1.IDS[string]) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		_, err := tx.WithContext(ctx).User.Where(tx.User.UUID.In(ids.Ids...)).Delete()
		if err != nil {
			return v1.ErrUserDeleteFailed
		}
		_, err = tx.WithContext(ctx).PostUser.Where(tx.PostUser.UserID.In(ids.Ids...)).Delete()
		if err != nil {
			return v1.ErrUserPostDeleteFailed
		}
		return nil
	})
}

// Update 更新User
func (r *userRepository) Update(ctx context.Context, user *model.User) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		_, err := tx.WithContext(ctx).User.Where(tx.User.UUID.Eq(user.UUID)).Updates(user)
		if err != nil {
			return v1.ErrUserUpdateFailed
		}
		if len(user.Post) != 0 {
			_, err = tx.WithContext(ctx).PostUser.Where(tx.PostUser.UserID.Eq(user.UUID)).Delete()
			if err != nil {
				return v1.ErrUserPostDeleteFailed
			}
			postList := r.CreatePostUserList(user)
			err = tx.WithContext(ctx).PostUser.CreateInBatches(postList, 20)
			if err != nil {
				return v1.ErrUserPostBatchCreateFailed
			}
		}
		return nil
	})
}

// Query 不分页查询User
func (r *userRepository) Query(ctx context.Context) ([]*model.User, error) {
	return r.gen.WithContext(ctx).User.Find()
}
