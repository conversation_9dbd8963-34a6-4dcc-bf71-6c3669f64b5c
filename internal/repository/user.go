package repository

import (
	"context"
	"strconv"
	"strings"

	v1 "admin/api/v1"
	"admin/internal/gen"
	"admin/internal/model"
	"admin/internal/utils"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

type UserRepository interface {
	Check(ctx context.Context, user *v1.Account) (*model.User, error)
	QueryById(ctx context.Context, id *v1.QID) (*model.User, error)
	UpdateStatus(ctx context.Context, ids *v1.QIDs[int64], status string) error
	CreatePostUserList(user *model.User) []*model.PostUser
	Create(ctx context.Context, user *model.User) error
	ParsePostIds(postIdStr string) ([]int64, error)
	QueryPage(ctx context.Context, user *v1.UserPage) (*v1.Page[*model.User], error)
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	Update(ctx context.Context, user *model.User) error
	Query(ctx context.Context) ([]*model.User, error)
}

type userRepository struct {
	*Repository
}

func NewUserRepository(r *Repository) UserRepository {
	return &userRepository{
		Repository: r,
	}
}

// ParsePostIds 并发解析 PostId 字符串为整数数组
func (r *userRepository) ParsePostIds(postIdStr string) ([]int64, error) {
	postIdStrs := strings.Split(postIdStr, ",")
	postIds := make([]int64, 0, len(postIdStrs))
	for _, postIdStr := range postIdStrs {
		postId, err := strconv.ParseInt(postIdStr, 10, 64)
		if err != nil {
			return nil, err
		}
		postIds = append(postIds, postId)
	}
	return postIds, nil
}

func (r *userRepository) getColumn() []field.Expr {
	u := r.gen.User
	return []field.Expr{
		u.ID,
		u.Status, u.Account, u.Username, u.Phone, u.Email, u.Sex, u.Avatar, u.TenantKey, u.RoleID,
		u.Status,
		u.Sort,
		u.CreatedAt,
		u.UpdatedAt,
		u.DeletedAt,
		u.LastLoginAt,
		u.LastLoginIP,
		u.LoginCount,
		u.Remark,
	}
}

// Check 检查手机号或邮箱是否存在
func (r *userRepository) Check(ctx context.Context, user *v1.Account) (*model.User, error) {
	// 构建查询条件
	query := r.gen.WithContext(ctx).User
	var conditions []field.Expr
	if user.Email != "" {
		conditions = append(conditions, r.gen.User.Email.Eq(user.Email))
	}
	if user.Phone != "" {
		conditions = append(conditions, r.gen.User.Phone.Eq(user.Phone))
	}
	// 如果没有提供邮箱或手机号，直接返回 false
	if len(conditions) == 0 {
		return nil, nil
	}
	// 使用 OR 条件合并查询
	exists, err := query.Where(field.Or(conditions...)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		r.logger.Warn("查询用户邮箱或手机号失败", zap.Error(err))
		return nil, err
	}
	return exists, nil
}

func (r *userRepository) UpdateStatus(ctx context.Context, ids *v1.QIDs[int64], status string) error {
	_, err := r.gen.WithContext(ctx).User.Where(r.gen.User.ID.In(ids.Ids...)).Update(r.gen.User.Status, "disable")
	return errors.Wrap(err, "用户禁用失败")
}

// QueryById 根据ID查询User详情
func (r *userRepository) QueryById(ctx context.Context, id *v1.QID) (*model.User, error) {
	u := r.gen.User
	pu := r.gen.PostUser
	p := r.gen.Post

	user, err := u.WithContext(ctx).
		Select(append(r.getColumn(), p.ID.GroupConcat().As("post_id"),
			p.Name.GroupConcat().As("post_name"))...).
		LeftJoin(pu, pu.UserID.EqCol(u.ID)).LeftJoin(p, p.ID.EqCol(pu.PostID)).
		Where(r.gen.User.ID.Eq(id.ID)).Group(r.gen.User.ID).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "查询用户详情失败")
	}
	return user, nil
}

// CreatePostUserList 根据user动态创建postUser类型
func (r *userRepository) CreatePostUserList(user *model.User) []*model.PostUser {
	var postList []*model.PostUser
	for _, post := range user.Post {
		postList = append(postList, &model.PostUser{
			UserID: user.ID,
			PostID: post.ID,
		})
	}
	return postList
}

// Create 创建User
func (r *userRepository) Create(ctx context.Context, user *model.User) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		err := r.gen.WithContext(ctx).User.Create(user)
		if err != nil {
			return errors.Wrap(err, "创建用户失败")
		}
		postList := r.CreatePostUserList(user)
		err = tx.WithContext(ctx).PostUser.CreateInBatches(postList, 100)
		if err != nil {
			return errors.Wrap(err, "插入关联岗位失败")
		}
		return nil
	})
}

// QueryPage 分页查询User
func (r *userRepository) QueryPage(ctx context.Context, user *v1.UserPage) (*v1.Page[*model.User], error) {
	u := r.gen.User
	pu := r.gen.PostUser
	p := r.gen.Post
	// 构建基础查询
	queryFind := u.WithContext(ctx).
		Select(append(r.getColumn(),
			field.NewUnsafeFieldRaw("JSON_ARRAYAGG(JSON_OBJECT('id', post.id, 'name', post.name)) AS post"),
		)...).
		LeftJoin(pu, pu.UserID.EqCol(u.ID)).
		LeftJoin(p, p.ID.EqCol(pu.PostID))

	// 动态查询条件
	if user.Email != "" {
		queryFind = queryFind.Where(u.Email.Like(utils.ToLike(user.Email)))
	}
	if user.Account != "" {
		queryFind = queryFind.Where(u.Account.Like(utils.ToLike(user.Account)))
	}
	if user.Username != "" {
		queryFind = queryFind.Where(u.Username.Like(utils.ToLike(user.Username)))
	}
	if user.Phone != "" {
		queryFind = queryFind.Where(u.Phone.Like(utils.ToLike(user.Phone)))
	}
	if len(user.PostIds) != 0 {
		queryFind = queryFind.Where(pu.PostID.In(user.PostIds...))
	}
	if !user.CreateTime.CreateTime.IsZero() {
		startTime, endTime := utils.GenerateTimeRange(user.CreateTime.CreateTime.GetTime())
		queryFind = queryFind.Where(u.CreatedAt.Between(startTime, endTime))
	}

	// 查询分页结果
	list, count, err := queryFind.Order(u.CreatedAt.Desc()).Group(r.gen.User.ID).FindByPage(user.Page-1, user.PageSize)
	if err != nil {
		return nil, errors.Wrap(err, "用户分页查询失败")
	}
	return &v1.Page[*model.User]{
		Total:    count,
		List:     list,
		Current:  user.Page,
		PageSize: user.PageSize,
	}, nil
}

// Delete 删除User
func (r *userRepository) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		_, err := tx.WithContext(ctx).User.Where(tx.User.ID.In(ids.Ids...)).Delete()
		if err != nil {
			return errors.Wrap(err, "删除用户失败")
		}
		_, err = tx.WithContext(ctx).PostUser.Where(tx.PostUser.UserID.In(ids.Ids...)).Delete()
		if err != nil {
			return errors.Wrap(err, "删除关联岗位失败")
		}
		return nil
	})
}

// Update 更新User
func (r *userRepository) Update(ctx context.Context, user *model.User) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		// 先更新用户
		_, err := tx.WithContext(ctx).User.Where(tx.User.ID.Eq(user.ID)).Updates(user)
		if err != nil {
			return errors.Wrap(err, "更新用户失败")
		}
		if len(user.Post) != 0 {
			// 批量删除岗位与用户关联数据
			_, err = tx.WithContext(ctx).PostUser.Where(tx.PostUser.UserID.Eq(user.ID)).Delete()
			if err != nil {
				return errors.Wrap(err, "删除关联岗位数据失败")
			}
			postList := r.CreatePostUserList(user)
			// 批量插入岗位与用户关联的数据
			err = tx.WithContext(ctx).PostUser.CreateInBatches(postList, 20)
			if err != nil {
				return errors.Wrap(err, "插入关联岗位失败")
			}
		}

		return nil
	})
}

// Query 不分页查询User
func (r *userRepository) Query(ctx context.Context) ([]*model.User, error) {
	return r.gen.WithContext(ctx).User.Find()
}
