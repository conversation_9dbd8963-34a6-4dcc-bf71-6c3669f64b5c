package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"

	"github.com/pkg/errors"
	"gorm.io/gen/field"
)

type RoleRepository interface {
	QueryById(ctx context.Context, id *v1.QID) (*model.Role, error)
	Create(ctx context.Context, role *model.Role) error
	Update(ctx context.Context, role *model.Role) error
	Delete(ctx context.Context, ids *v1.QIDs[int64]) error
	QueryPage(ctx context.Context, role *v1.RolePage) (*v1.Page[*model.Role], error)
	UpdateStatus(ctx context.Context, ids *v1.QIDs[int64], status string) error
	Query(ctx context.Context) ([]*model.Role, error)
	GetPermissions(ctx context.Context, roleId int64) ([]string, error)
	UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error
	AddRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error
	DeleteRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error
	GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error)
}

type roleRepository struct {
	*Repository
}

func NewRoleRepository(r *Repository) RoleRepository {
	return &roleRepository{
		Repository: r,
	}
}

func (r *roleRepository) getColumn() []field.Expr {
	return []field.Expr{
		r.gen.Role.ID,
		r.gen.Role.Name,
		r.gen.Role.ParentID,
		r.gen.Role.Status,
		r.gen.Role.Sort,
		r.gen.Role.CreatedAt,
		r.gen.Role.UpdatedAt,
	}
}

// Create implements RoleRepository.
func (r *roleRepository) Create(ctx context.Context, role *model.Role) error {
	return r.gen.WithContext(ctx).Role.Create(role)
}

// UpdateStatus implements RoleRepository.
func (r *roleRepository) UpdateStatus(ctx context.Context, ids *v1.QIDs[int64], status string) error {
	_, err := r.gen.WithContext(ctx).Role.Where(r.gen.Role.ID.In(ids.Ids...)).Update(r.gen.Role.Status, status)
	return err
}

// Delete implements RoleRepository.
func (r *roleRepository) Delete(ctx context.Context, ids *v1.QIDs[int64]) error {
	_, err := r.gen.WithContext(ctx).Role.Where(r.gen.Role.ID.In(ids.Ids...)).Delete()
	return err
}

// QueryById implements RoleRepository.
func (r *roleRepository) QueryById(ctx context.Context, id *v1.QID) (*model.Role, error) {
	return r.gen.WithContext(ctx).Role.Where(r.gen.Role.ID.Eq(id.ID)).Select(r.getColumn()...).First()
}

// QueryPage implements RoleRepository.
func (r *roleRepository) QueryPage(ctx context.Context, page *v1.RolePage) (*v1.Page[*model.Role], error) {
	queryFind := r.gen.WithContext(ctx).Role

	// 添加排序
	queryFind = queryFind.Order(r.gen.Role.Sort)

	roleList, count, err := queryFind.FindByPage(page.Page, page.PageSize)
	if err != nil {
		return nil, errors.Wrap(err, "角色分页查询失败")
	}
	return &v1.Page[*model.Role]{
		Current:  page.Page,
		PageSize: page.PageSize,
		Total:    count,
		List:     roleList,
	}, nil
}

// Update implements RoleRepository.
func (r *roleRepository) Update(ctx context.Context, role *model.Role) error {
	_, err := r.gen.WithContext(ctx).Role.Where(r.gen.Role.ID.Eq(role.ID)).Updates(role)
	return err
}

// Query implements RoleRepository.
func (r *roleRepository) Query(ctx context.Context) ([]*model.Role, error) {
	roleList, err := r.gen.WithContext(ctx).Role.Select(r.getColumn()...).Order(r.gen.Role.Sort).Find()
	if err != nil {
		return nil, errors.Wrap(err, "角色查询失败")
	}
	return roleList, nil
}

// GetPermissions 获取角色的权限
func (r *roleRepository) GetPermissions(ctx context.Context, roleId int64) ([]string, error) {
	return []string{}, errors.New("获取角色权限功能未实现")
}

// UpdatePermissions 更新角色的权限
func (r *roleRepository) UpdatePermissions(ctx context.Context, roleId int64, permissions []string) error {
	return errors.New("更新角色权限功能未实现")
}

// AddRoutersForRole 为角色添加路由
func (r *roleRepository) AddRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error {
	if len(routerIds) == 0 {
		return nil
	}

	roleRouters := make([]*model.SysRoleRoute, 0, len(routerIds))
	for _, routerId := range routerIds {
		roleRouters = append(roleRouters, &model.SysRoleRoute{
			RoleID:  roleId,
			RouteID: routerId,
		})
	}

	return r.gen.WithContext(ctx).SysRoleRoute.Create(roleRouters...)
}

// DeleteRoutersForRole 删除角色的路由
func (r *roleRepository) DeleteRoutersForRole(ctx context.Context, roleId int64, routerIds []int64) error {
	if len(routerIds) == 0 {
		return nil
	}
	_, err := r.gen.WithContext(ctx).SysRoleRoute.
		Where(r.gen.SysRoleRoute.RoleID.Eq(roleId)).
		Where(r.gen.SysRoleRoute.RouteID.In(routerIds...)).
		Delete()

	return err
}

// GetRoutersByRoleId 获取角色的路由
func (r *roleRepository) GetRoutersByRoleId(ctx context.Context, roleId int64) ([]*model.SysRoleRoute, error) {
	return r.gen.WithContext(ctx).SysRoleRoute.
		Where(r.gen.SysRoleRoute.RoleID.Eq(roleId)).
		Find()
}
