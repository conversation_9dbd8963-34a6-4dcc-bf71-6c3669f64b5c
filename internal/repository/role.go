package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/model"

	"gorm.io/gen/field"
)

type RoleRepository interface {
	QueryById(ctx context.Context, id *v1.ID) (*model.Role, error)
	Create(ctx context.Context, role *model.Role) error
	Update(ctx context.Context, role *model.Role) error
	Delete(ctx context.Context, ids *v1.IDS[string]) error
	QueryPage(ctx context.Context, role *v1.RolePage) (*v1.Page[*model.Role], error)
	UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error
	Query(ctx context.Context) ([]*model.Role, error)
	GetPermissions(ctx context.Context, roleId string) ([]string, error)
	UpdatePermissions(ctx context.Context, roleId string, permissions []string) error
	AddRoutersForRole(ctx context.Context, roleId string, routerIds []string) error
	DeleteRoutersForRole(ctx context.Context, roleId string, routerIds []string) error
	GetRoutersByRoleId(ctx context.Context, roleId string) ([]*model.SysRoleRoute, error)
}

type roleRepository struct {
	*Repository
}

func NewRoleRepository(r *Repository) RoleRepository {
	return &roleRepository{
		Repository: r,
	}
}

func (r *roleRepository) getColumn() []field.Expr {
	return []field.Expr{
		r.gen.Role.ID,
		r.gen.Role.Name,
		r.gen.Role.ParentID,
		r.gen.Role.Status,
		r.gen.Role.Sort,
		r.gen.Role.CreatedAt,
		r.gen.Role.UpdatedAt,
		r.gen.Role.UUID,
	}
}

// Create implements RoleRepository.
func (r *roleRepository) Create(ctx context.Context, role *model.Role) error {
	return r.gen.WithContext(ctx).Role.Create(role)
}

// UpdateStatus implements RoleRepository.
func (r *roleRepository) UpdateStatus(ctx context.Context, ids *v1.IDS[string], status string) error {
	_, err := r.gen.WithContext(ctx).Role.Where(r.gen.Role.UUID.In(ids.Ids...)).Update(r.gen.Role.Status, status)
	return err
}

// Delete implements RoleRepository.
func (r *roleRepository) Delete(ctx context.Context, ids *v1.IDS[string]) error {
	_, err := r.gen.WithContext(ctx).Role.Where(r.gen.Role.UUID.In(ids.Ids...)).Delete()
	return err
}

// QueryById implements RoleRepository.
func (r *roleRepository) QueryById(ctx context.Context, id *v1.ID) (*model.Role, error) {
	return r.gen.WithContext(ctx).Role.Where(r.gen.Role.UUID.Eq(id.ID)).Select(r.getColumn()...).First()
}

// QueryPage implements RoleRepository.
func (r *roleRepository) QueryPage(ctx context.Context, page *v1.RolePage) (*v1.Page[*model.Role], error) {
	queryFind := r.gen.WithContext(ctx).Role

	// 添加排序
	queryFind = queryFind.Order(r.gen.Role.CreatedAt)

	roleList, count, err := queryFind.FindByPage(page.Page, page.PageSize)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	return &v1.Page[*model.Role]{
		Current:  page.Page,
		PageSize: page.PageSize,
		Total:    count,
		List:     roleList,
	}, nil
}

// Update implements RoleRepository.
func (r *roleRepository) Update(ctx context.Context, role *model.Role) error {
	_, err := r.gen.WithContext(ctx).Role.Where(r.gen.Role.UUID.Eq(role.UUID)).
		Updates(role)
	return err
}

// Query implements RoleRepository.
func (r *roleRepository) Query(ctx context.Context) ([]*model.Role, error) {
	roleList, err := r.gen.WithContext(ctx).Role.Select(r.getColumn()...).Order(r.gen.Role.Sort).Find()
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	return roleList, nil
}

// GetPermissions 获取角色的权限
func (r *roleRepository) GetPermissions(ctx context.Context, roleId string) ([]string, error) {
	return []string{}, v1.ErrBusinessLogic
}

// UpdatePermissions 更新角色的权限
func (r *roleRepository) UpdatePermissions(ctx context.Context, roleId string, permissions []string) error {
	return v1.ErrBusinessLogic
}

// AddRoutersForRole 为角色添加路由
func (r *roleRepository) AddRoutersForRole(ctx context.Context, roleId string, routerIds []string) error {
	if len(routerIds) == 0 {
		return nil
	}

	roleRouters := make([]*model.SysRoleRoute, 0, len(routerIds))
	for _, routerId := range routerIds {
		roleRouters = append(roleRouters, &model.SysRoleRoute{
			RoleID:  roleId,
			RouteID: routerId,
		})
	}

	return r.gen.WithContext(ctx).SysRoleRoute.Create(roleRouters...)
}

// DeleteRoutersForRole 删除角色的路由
func (r *roleRepository) DeleteRoutersForRole(ctx context.Context, roleId string, routerIds []string) error {
	if len(routerIds) == 0 {
		return nil
	}
	_, err := r.gen.WithContext(ctx).SysRoleRoute.
		Where(r.gen.SysRoleRoute.RoleID.Eq(roleId)).
		Where(r.gen.SysRoleRoute.RouteID.In(routerIds...)).
		Delete()

	return err
}

// GetRoutersByRoleId 获取角色的路由
func (r *roleRepository) GetRoutersByRoleId(ctx context.Context, roleId string) ([]*model.SysRoleRoute, error) {
	return r.gen.WithContext(ctx).SysRoleRoute.
		Where(r.gen.SysRoleRoute.RoleID.Eq(roleId)).
		Find()
}
