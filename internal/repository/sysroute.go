package repository

import (
	"context"

	v1 "admin/api/v1"
	"admin/internal/gen"
	"admin/internal/model"

	"github.com/pkg/errors"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

// SysRouteRepository SysRoute 路由表仓储接口
type SysRouteRepository interface {
	Create(ctx context.Context, entity *model.SysRoute) error
	QueryById(ctx context.Context, req *v1.ID) (*model.SysRoute, error)
	QueryPage(ctx context.Context, req *v1.SysRoutePageReq) (*v1.Page[*model.SysRoute], error)
	Update(ctx context.Context, entity *model.SysRoute) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	Query(ctx context.Context) ([]*model.SysRoute, error)

	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error
}

// sys_routeRepository SysRoute 路由表仓储实现
type sys_routeRepository struct {
	*Repository
}

// NewSysRouteRepository 创建SysRoute 路由表仓储
func NewSysRouteRepository(r *Repository) SysRouteRepository {
	return &sys_routeRepository{
		Repository: r,
	}
}

// getColumns 获取查询字段
func (r *sys_routeRepository) getColumns() []field.Expr {
	t := r.gen.SysRoute
	return []field.Expr{
		t.ID,

		t.Name,

		t.Path,

		t.Component,

		t.Redirect,

		t.ParentID,

		t.Sort,

		t.Status,

		t.IsLayout,

		t.Title,

		t.Icon,

		t.Hidden,

		t.ActiveMenu,

		t.KeepAlive,

		t.Auth,

		t.Breadcrumb,

		t.External,

		t.ExternalURL,

		t.CreatedAt,

		t.UpdatedAt,

		t.UUID,

		t.TenantID,
	}
}

// buildQuery 构建基础查询
func (r *sys_routeRepository) buildQuery(ctx context.Context) gen.ISysRouteDo {
	return r.gen.SysRoute.WithContext(ctx).Select(r.getColumns()...)
}

// Create 创建SysRoute 路由表
func (r *sys_routeRepository) Create(ctx context.Context, entity *model.SysRoute) error {
	if entity == nil {
		return v1.ErrInvalidParams
	}

	if err := r.gen.SysRoute.WithContext(ctx).Create(entity); err != nil {
		return v1.ErrCreateFailed
	}

	return nil
}

// QueryById 根据ID查询SysRoute 路由表
func (r *sys_routeRepository) QueryById(ctx context.Context, req *v1.ID) (*model.SysRoute, error) {
	if req.ID == "" {
		return nil, v1.ErrInvalidParams
	}

	t := r.gen.SysRoute
	entity, err := r.buildQuery(ctx).
		Where(t.UUID.Eq(req.ID)).
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, v1.ErrQueryFailed
	}

	return entity, nil
}

// QueryPage 分页查询SysRoute 路由表
func (r *sys_routeRepository) QueryPage(ctx context.Context, req *v1.SysRoutePageReq) (*v1.Page[*model.SysRoute], error) {
	t := r.gen.SysRoute
	query := r.buildQuery(ctx)

	// 构建查询条件 - 只包含实际的搜索字段

	if req.Name != "" {
		query = query.Where(t.Name.Like("%" + req.Name + "%"))
	}
	if req.Title != "" {
		query = query.Where(t.Title.Like("%" + req.Title + "%"))
	}
	if len(req.Status) > 0 {
		query = query.Where(t.Status.In(req.Status...))
	}

	// 多租户过滤
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}

	// 默认排序

	query = query.Order(t.Sort.Desc(), t.CreatedAt.Desc())

	// 分页查询
	list, count, err := query.FindByPage(req.GetPage()-1, req.GetPageSize())
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	return &v1.Page[*model.SysRoute]{
		Current:  req.GetPage(),
		PageSize: req.GetPageSize(),
		Total:    count,
		List:     list,
	}, nil
}

// Update 更新SysRoute 路由表
func (r *sys_routeRepository) Update(ctx context.Context, entity *model.SysRoute) error {
	if entity == nil {
		return v1.ErrInvalidParams
	}

	if entity.UUID == "" {
		return v1.ErrInvalidParams
	}

	t := r.gen.SysRoute
	result, err := t.WithContext(ctx).
		Where(t.UUID.Eq(entity.UUID)).
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		Updates(entity)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	if result.RowsAffected == 0 {
		return v1.ErrRecordNotFound
	}

	return nil
}

// Delete 删除SysRoute 路由表
func (r *sys_routeRepository) Delete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("删除ID列表不能为空")
	}

	t := r.gen.SysRoute
	query := t.WithContext(ctx).Where(t.UUID.In(req.Ids...))

	// 多租户过滤
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}

	// 软删除（GORM会自动处理软删除逻辑）
	result, err := query.Delete()
	if err != nil {
		return v1.ErrDeleteFailed
	}

	if result.RowsAffected == 0 {
		return errors.New("没有找到要删除的记录")
	}

	return nil
}

// Query 查询所有SysRoute 路由表
func (r *sys_routeRepository) Query(ctx context.Context) ([]*model.SysRoute, error) {
	t := r.gen.SysRoute
	query := r.buildQuery(ctx)

	// 多租户过滤
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}

	// 排序

	query = query.Order(t.Sort)

	entities, err := query.Find()
	if err != nil {
		return nil, v1.ErrQueryFailed
	}

	return entities, nil
}

// UpdateStatus 更新SysRoute 路由表状态
func (r *sys_routeRepository) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	if status == "" {
		return errors.New("状态不能为空")
	}

	t := r.gen.SysRoute
	result, err := t.WithContext(ctx).
		Where(t.UUID.In(req.Ids...)).
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		Update(t.Status, status)
	if err != nil {
		return v1.ErrUpdateFailed
	}

	if result.RowsAffected == 0 {
		return errors.New("没有找到要更新的记录")
	}

	return nil
}

// getTenantID 从上下文获取租户ID
func (r *sys_routeRepository) getTenantID(ctx context.Context) string {
	if tenantID, ok := ctx.Value("tenant_id").(string); ok {
		return tenantID
	}
	return ""
}

// Count 统计SysRoute 路由表数量
func (r *sys_routeRepository) Count(ctx context.Context) (int64, error) {
	t := r.gen.SysRoute
	query := t.WithContext(ctx)

	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}

	count, err := query.Count()
	if err != nil {
		return 0, v1.ErrQueryFailed
	}

	return count, nil
}

// Exists 检查SysRoute 路由表是否存在
func (r *sys_routeRepository) Exists(ctx context.Context, id string) (bool, error) {
	if id == "" {
		return false, errors.New("ID不能为空")
	}

	t := r.gen.SysRoute
	count, err := t.WithContext(ctx).
		Where(t.UUID.Eq(id)).
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		Count()
	if err != nil {
		return false, v1.ErrQueryFailed
	}

	return count > 0, nil
}
