package controller

import (
	v1 "{{.ModuleName}}/api/v1"
	"{{.ModuleName}}/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// {{.ModelName}}Controller {{.ModelComment}}控制器
type {{.ModelName}}Controller struct {
	*Controller
	{{toSnakeCase .ModelName}}Service service.{{.ModelName}}Service
}

// New{{.ModelName}}Controller 创建{{.ModelComment}}控制器
func New{{.ModelName}}Controller(
	controller *Controller,
	{{toSnakeCase .ModelName}}Service service.{{.ModelName}}Service,
) *{{.ModelName}}Controller {
	return &{{.ModelName}}Controller{
		Controller: controller,
		{{toSnakeCase .ModelName}}Service: {{toSnakeCase .ModelName}}Service,
	}
}

// Create 创建{{.ModelComment}}
// @Summary 创建{{.ModelComment}}
// @Description 创建新的{{.ModelComment}}记录
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.{{.ModelName}}CreateReq true "创建{{.ModelComment}}请求"
// @Success 200 {object} v1.Response "创建成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}} [post]
func (c *{{.ModelName}}Controller) Create(ctx *gin.Context) {
	var req v1.{{.ModelName}}CreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{toSnakeCase .ModelName}}Service.Create(ctx, &req); err != nil {
		c.logger.Error("创建{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// QueryById 根据ID查询{{.ModelComment}}
// @Summary 根据ID查询{{.ModelComment}}
// @Description 根据ID获取{{.ModelComment}}详细信息
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.ID true "查询ID请求"
// @Success 200 {object} v1.Response{data=v1.{{.ModelName}}Resp} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 404 {object} v1.Response "记录不存在"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}}/detail [post]
func (c *{{.ModelName}}Controller) QueryById(ctx *gin.Context) {
	var req v1.ID
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{toSnakeCase .ModelName}}Service.QueryById(ctx, &req)
	if err != nil {
		c.logger.Error("查询{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

// QueryPage 分页查询{{.ModelComment}}
// @Summary 分页查询{{.ModelComment}}
// @Description 分页获取{{.ModelComment}}列表，支持搜索和排序
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.{{.ModelName}}PageReq true "分页查询请求"
// @Success 200 {object} v1.Response{data=v1.Page[v1.{{.ModelName}}Resp]} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}}/page [post]
func (c *{{.ModelName}}Controller) QueryPage(ctx *gin.Context) {
	var req v1.{{.ModelName}}PageReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{toSnakeCase .ModelName}}Service.QueryPage(ctx, &req)
	if err != nil {
		c.logger.Error("分页查询{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

// Update 更新{{.ModelComment}}
// @Summary 更新{{.ModelComment}}
// @Description 根据ID更新{{.ModelComment}}信息
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.{{.ModelName}}UpdateReq true "更新{{.ModelComment}}请求"
// @Success 200 {object} v1.Response "更新成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 404 {object} v1.Response "记录不存在"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}} [put]
func (c *{{.ModelName}}Controller) Update(ctx *gin.Context) {
	var req v1.{{.ModelName}}UpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{toSnakeCase .ModelName}}Service.Update(ctx, &req); err != nil {
		c.logger.Error("更新{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Delete 删除{{.ModelComment}}
// @Summary 删除{{.ModelComment}}
// @Description 根据ID列表批量删除{{.ModelComment}}
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "删除ID列表请求"
// @Success 200 {object} v1.Response "删除成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}} [delete]
func (c *{{.ModelName}}Controller) Delete(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{toSnakeCase .ModelName}}Service.Delete(ctx, &req); err != nil {
		c.logger.Error("删除{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有{{.ModelComment}}
// @Summary 查询所有{{.ModelComment}}
// @Description 获取所有{{.ModelComment}}列表，不分页
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Success 200 {object} v1.Response{data=[]v1.{{.ModelName}}Resp} "查询成功"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}}/all [get]
func (c *{{.ModelName}}Controller) Query(ctx *gin.Context) {
	result, err := c.{{toSnakeCase .ModelName}}Service.Query(ctx)
	if err != nil {
		c.logger.Error("查询所有{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

{{if .ModelFeatures.HasStatus}}
// Enable 启用{{.ModelComment}}
// @Summary 启用{{.ModelComment}}
// @Description 根据ID列表批量启用{{.ModelComment}}
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "启用ID列表请求"
// @Success 200 {object} v1.Response "启用成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}}/enable [post]
func (c *{{.ModelName}}Controller) Enable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{toSnakeCase .ModelName}}Service.UpdateStatus(ctx, &req, "enabled"); err != nil {
		c.logger.Error("启用{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用{{.ModelComment}}
// @Summary 禁用{{.ModelComment}}
// @Description 根据ID列表批量禁用{{.ModelComment}}
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.IDS[string] true "禁用ID列表请求"
// @Success 200 {object} v1.Response "禁用成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}}/disable [post]
func (c *{{.ModelName}}Controller) Disable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{toSnakeCase .ModelName}}Service.UpdateStatus(ctx, &req, "disabled"); err != nil {
		c.logger.Error("禁用{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}
{{end}}

{{if .ModelFeatures.IsTreeModel}}
// QueryTree 查询{{.ModelComment}}树形结构
// @Summary 查询{{.ModelComment}}树形结构
// @Description 获取{{.ModelComment}}的树形结构数据，包含父子关系
// @Tags {{.ModelComment}}管理
// @Accept json
// @Produce json
// @Param request body v1.{{.ModelName}}TreeReq false "树形查询请求"
// @Success 200 {object} v1.Response{data=[]v1.{{.ModelName}}Resp} "查询成功"
// @Failure 400 {object} v1.Response "请求参数错误"
// @Failure 500 {object} v1.Response "服务器内部错误"
// @Router /api/v1/{{toSnakeCase .ModelName}}/tree [post]
func (c *{{.ModelName}}Controller) QueryTree(ctx *gin.Context) {
	result, err := c.{{toSnakeCase .ModelName}}Service.QueryTree(ctx)
	if err != nil {
		c.logger.Error("查询{{.ModelComment}}树形结构失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}
{{end}}