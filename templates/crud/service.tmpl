package service

import (
	v1 "{{.ModuleName}}/api/v1"
	"{{.ModuleName}}/internal/model"
	"{{.ModuleName}}/internal/repository"
	"github.com/pkg/errors"
	"context"
	{{if .Features.EnableCache}}"encoding/json"{{end}}
	{{if .Features.EnableCache}}"fmt"{{end}}
	{{if .Features.EnableCache}}"strings"{{end}}
	{{if .Features.EnableCache}}"time"{{end}}
)

// {{.ModelName}}Service {{.ModelComment}}服务接口
type {{.ModelName}}Service interface {
	Create(ctx context.Context, req *v1.{{.ModelName}}CreateReq) error
	QueryById(ctx context.Context, req *v1.ID) (*v1.{{.ModelName}}Resp, error)
	QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*v1.{{.ModelName}}Resp], error)
	Update(ctx context.Context, req *v1.{{.ModelName}}UpdateReq) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	Query(ctx context.Context) ([]*model.{{.ModelName}}, error)
	{{if .ModelFeatures.HasStatus}}
	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error
	Enable(ctx context.Context, req *v1.IDS[string]) error
	Disable(ctx context.Context, req *v1.IDS[string]) error
	{{end}}
	{{if .ModelFeatures.IsTreeModel}}
	QueryTree(ctx context.Context) ([]*model.{{.ModelName}}, error)
	{{end}}
	

}

// {{toSnakeCase .ModelName}}Service {{.ModelComment}}服务实现
type {{toSnakeCase .ModelName}}Service struct {
	*Service
	{{toSnakeCase .ModelName}}Repository repository.{{.ModelName}}Repository
}

// New{{.ModelName}}Service 创建{{.ModelComment}}服务
func New{{.ModelName}}Service(
	service *Service,
	{{toSnakeCase .ModelName}}Repository repository.{{.ModelName}}Repository,
) {{.ModelName}}Service {
	return &{{toSnakeCase .ModelName}}Service{
		Service: service,
		{{toSnakeCase .ModelName}}Repository: {{toSnakeCase .ModelName}}Repository,
	}
}

// Create 创建{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) Create(ctx context.Context, req *v1.{{.ModelName}}CreateReq) error {
	// 请求参数已在Controller层通过binding标签验证
	entity := &model.{{.ModelName}}{
		{{range .Fields}}
		{{$isSystemField := or (eq .Name "ID") (or (eq .Name "UUID") (or (eq .Name "CreatedAt") (or (eq .Name "UpdatedAt") (or (eq .Name "DeletedAt") (or (eq .Name "CreateID") (or (eq .Name "UpdateID") (eq .Name "Children")))))))}}
		{{if not $isSystemField}}
		{{.Name}}: req.{{.Name}},
		{{end}}
		{{end}}
	}
	
	{{if .Features.EnableTransaction}}
	err := s.{{toSnakeCase .ModelName}}Repository.CreateWithTx(ctx, entity)
	{{else}}
	err := s.{{toSnakeCase .ModelName}}Repository.Create(ctx, entity)
	{{end}}
	
	if err != nil {
		return v1.ErrCreateFailed
	}
	
	return nil
}

// QueryById 根据ID查询{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) QueryById(ctx context.Context, req *v1.ID) (*v1.{{.ModelName}}Resp, error) {
	{{if .Features.EnableCache}}
	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("{{toSnakeCase .ModelName}}:id:%s", req.ID)
	if cached, err := s.redis.Get(ctx, cacheKey).Result(); err == nil && cached != "" {
		var entity model.{{.ModelName}}
		if err := json.Unmarshal([]byte(cached), &entity); err == nil {
			return s.modelToResp(&entity), nil
		}
	}
	{{end}}
	
	entity, err := s.{{toSnakeCase .ModelName}}Repository.QueryById(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	if entity == nil {
		return nil, v1.ErrNotFound
	}
	
	{{if .Features.EnableCache}}
	// 缓存结果
	if data, err := json.Marshal(entity); err == nil {
		s.redis.SetEx(ctx, cacheKey, string(data), time.Minute*10)
	}
	{{end}}
	
	return s.modelToResp(entity), nil
}

// QueryPage 分页查询{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*v1.{{.ModelName}}Resp], error) {
	{{if .Features.EnableCache}}
	// 构建缓存键
	cacheKey := s.buildPageCacheKey(req)
	if cached, err := s.redis.Get(ctx, cacheKey).Result(); err == nil && cached != "" {
		var page v1.Page[*v1.{{.ModelName}}Resp]
		if err := json.Unmarshal([]byte(cached), &page); err == nil {
			return &page, nil
		}
	}
	{{end}}
	
	result, err := s.{{toSnakeCase .ModelName}}Repository.QueryPage(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	// 转换为响应结构
	respList := s.modelListToRespList(result.List)
	page := &v1.Page[*v1.{{.ModelName}}Resp]{
		Current:  result.Current,
		PageSize: result.PageSize,
		Total:    result.Total,
		List:     respList,
	}
	
	{{if .Features.EnableCache}}
	// 缓存结果
	if data, err := json.Marshal(page); err == nil {
		s.redis.SetEx(ctx, cacheKey, string(data), time.Minute*5)
	}
	{{end}}
	
	return page, nil
}

// Update 更新{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) Update(ctx context.Context, req *v1.{{.ModelName}}UpdateReq) error {
	// 请求参数已在Controller层通过binding标签验证
	
	// 检查记录是否存在
	existing, err := s.{{toSnakeCase .ModelName}}Repository.QueryById(ctx, &v1.ID{ID: req.ID})
	if err != nil {
		return v1.ErrQueryFailed
	}
	if existing == nil {
		return v1.ErrNotFound
	}
	
	// 只更新非系统字段
	entity := &model.{{.ModelName}}{
		UUID: req.ID, // 设置UUID用于查找记录
		{{range .Fields}}
		{{$isSystemField := or (eq .Name "ID") (or (eq .Name "UUID") (or (eq .Name "CreatedAt") (or (eq .Name "UpdatedAt") (or (eq .Name "DeletedAt") (or (eq .Name "CreateID") (or (eq .Name "UpdateID") (eq .Name "Children")))))))}}
		{{if not $isSystemField}}
		{{.Name}}: req.{{.Name}},
		{{end}}
		{{end}}
	}
	
	{{if .Features.EnableTransaction}}
	err = s.{{toSnakeCase .ModelName}}Repository.UpdateWithTx(ctx, entity)
	{{else}}
	err = s.{{toSnakeCase .ModelName}}Repository.Update(ctx, entity)
	{{end}}
	
	if err != nil {
		return v1.ErrUpdateFailed
	}
	
	{{if .Features.EnableCache}}
	// 清除相关缓存
	s.clearCache(ctx, req.ID)
	{{end}}
	
	return nil
}

// Delete 删除{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) Delete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("删除ID列表不能为空")
	}
	
	{{if .Features.EnableTransaction}}
	err := s.{{toSnakeCase .ModelName}}Repository.DeleteWithTx(ctx, req)
	{{else}}
	err := s.{{toSnakeCase .ModelName}}Repository.Delete(ctx, req)
	{{end}}
	
	if err != nil {
		return v1.ErrDeleteFailed
	}
	
	{{if .Features.EnableCache}}
	// 清除相关缓存
	for _, id := range req.Ids {
		s.clearCache(ctx, id)
	}
	{{end}}
	
	return nil
}

// Query 查询所有{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) Query(ctx context.Context) ([]*model.{{.ModelName}}, error) {
	{{if .Features.EnableCache}}
	// 尝试从缓存获取
	cacheKey := "{{toSnakeCase .ModelName}}:all"
	if cached, err := s.redis.Get(ctx, cacheKey).Result(); err == nil && cached != "" {
		var entities []*model.{{.ModelName}}
		if err := json.Unmarshal([]byte(cached), &entities); err == nil {
			return entities, nil
		}
	}
	{{end}}
	
	entities, err := s.{{toSnakeCase .ModelName}}Repository.Query(ctx)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	{{if .Features.EnableCache}}
	// 缓存结果
	if data, err := json.Marshal(entities); err == nil {
		s.redis.SetEx(ctx, cacheKey, string(data), time.Minute*10)
	}
	{{end}}
	
	return entities, nil
}

{{if .ModelFeatures.HasStatus}}
// UpdateStatus 更新{{.ModelComment}}状态
func (s *{{toSnakeCase .ModelName}}Service) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}
	
	err := s.{{toSnakeCase .ModelName}}Repository.UpdateStatus(ctx, req, status)
	if err != nil {
		return v1.ErrUpdateFailed
	}
	
	{{if .Features.EnableCache}}
	// 清除相关缓存
	for _, id := range req.Ids {
		s.clearCache(ctx, id)
	}
	{{end}}
	
	return nil
}

// Enable 启用{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) Enable(ctx context.Context, req *v1.IDS[string]) error {
	return s.UpdateStatus(ctx, req, "enabled")
}

// Disable 禁用{{.ModelComment}}
func (s *{{toSnakeCase .ModelName}}Service) Disable(ctx context.Context, req *v1.IDS[string]) error {
	return s.UpdateStatus(ctx, req, "disabled")
}
{{end}}

{{if .ModelFeatures.IsTreeModel}}
// QueryTree 查询{{.ModelComment}}树形结构
func (s *{{toSnakeCase .ModelName}}Service) QueryTree(ctx context.Context) ([]*model.{{.ModelName}}, error) {
	{{if .Features.EnableCache}}
	// 尝试从缓存获取
	cacheKey := "{{toSnakeCase .ModelName}}:tree"
	if cached, err := s.redis.Get(ctx, cacheKey).Result(); err == nil && cached != "" {
		var tree []*model.{{.ModelName}}
		if err := json.Unmarshal([]byte(cached), &tree); err == nil {
			return tree, nil
		}
	}
	{{end}}
	
	list, err := s.{{toSnakeCase .ModelName}}Repository.Query(ctx)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	// 转换为树形结构
	var tree []*model.{{.ModelName}}
	if len(list) > 0 {
		tree = list[0].ToTree(list)
	} else {
		tree = list
	}
	
	{{if .Features.EnableCache}}
	// 缓存结果
	if data, err := json.Marshal(tree); err == nil {
		s.redis.SetEx(ctx, cacheKey, string(data), time.Minute*10)
	}
	{{end}}
	
	return tree, nil
}
{{end}}

{{if .Features.EnableExport}}
// Export 导出{{.ModelComment}}数据
func (s *{{toSnakeCase .ModelName}}Service) Export(ctx context.Context, req *v1.{{.ModelName}}PageReq) ([]byte, error) {
	// 获取所有数据
	req.PageSize = 10000 // 设置大的页面大小
	page, err := s.QueryPage(ctx, req)
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	// 转换为Excel格式
	data, err := s.convertToExcel(page.List)
	if err != nil {
		return nil, v1.ErrInternalError
	}
	
	return data, nil
}
{{end}}

{{if .Features.EnableImport}}
// Import 导入{{.ModelComment}}数据
func (s *{{toSnakeCase .ModelName}}Service) Import(ctx context.Context, data []byte) error {
	// 解析Excel数据
	entities, err := s.parseExcelData(data)
	if err != nil {
		return v1.ErrInvalidFormat
	}
	
	// 批量创建
	for _, entity := range entities {
		if err := s.{{toSnakeCase .ModelName}}Repository.Create(ctx, entity); err != nil {
			s.logger.Error("导入{{.ModelComment}}失败", zap.Error(err), zap.Any("entity", entity))
			// 继续处理其他记录
		}
	}
	
	return nil
}
{{end}}


{{if .Features.EnableCache}}
// buildPageCacheKey 构建分页缓存键
func (s *{{toSnakeCase .ModelName}}Service) buildPageCacheKey(req *v1.{{.ModelName}}PageReq) string {
	var parts []string
	parts = append(parts, "{{toSnakeCase .ModelName}}:page")
	parts = append(parts, fmt.Sprintf("p:%d", req.GetPage()))
	parts = append(parts, fmt.Sprintf("s:%d", req.GetPageSize()))
	
	{{range .SearchFields}}
	if req.{{.Name}} != "" {
		parts = append(parts, fmt.Sprintf("{{toSnakeCase .Name}}:%s", req.{{.Name}}))
	}
	{{end}}
	
	return strings.Join(parts, ":")
}

// clearCache 清除缓存
func (s *{{toSnakeCase .ModelName}}Service) clearCache(ctx context.Context, id string) {
	// 清除单个记录缓存
	s.redis.Del(ctx, fmt.Sprintf("{{toSnakeCase .ModelName}}:id:%s", id))
	
	// 清除列表缓存
	s.redis.Del(ctx, "{{toSnakeCase .ModelName}}:all")
	
	{{if .ModelFeatures.IsTreeModel}}
	// 清除树形结构缓存
	s.redis.Del(ctx, "{{toSnakeCase .ModelName}}:tree")
	{{end}}
	
	// 清除分页缓存（使用模式匹配）
	keys, err := s.redis.Keys(ctx, "{{toSnakeCase .ModelName}}:page:*").Result()
	if err == nil && len(keys) > 0 {
		s.redis.Del(ctx, keys...)
	}
}
{{end}}

{{if .Features.EnableExport}}
// convertToExcel 转换为Excel格式
func (s *{{toSnakeCase .ModelName}}Service) convertToExcel(entities []*model.{{.ModelName}}) ([]byte, error) {
	// TODO: 实现Excel转换逻辑
	return nil, nil
}
{{end}}

{{if .Features.EnableImport}}
// parseExcelData 解析Excel数据
func (s *{{toSnakeCase .ModelName}}Service) parseExcelData(data []byte) ([]*model.{{.ModelName}}, error) {
	// TODO: 实现Excel解析逻辑
	return nil, nil
}
{{end}}

// modelToResp 将模型转换为响应结构体
func (s *{{toSnakeCase .ModelName}}Service) modelToResp(entity *model.{{.ModelName}}) *v1.{{.ModelName}}Resp {
	if entity == nil {
		return nil
	}
	
	resp := &v1.{{.ModelName}}Resp{
		ID:        entity.ID,
		UUID:      entity.UUID,
		CreatedAt: entity.CreatedAt,
		UpdatedAt: entity.UpdatedAt,
		{{range .Fields}}
		{{$isExcluded := or (eq .Name "ID") (or (eq .Name "UUID") (or (eq .Name "CreatedAt") (or (eq .Name "UpdatedAt") (or (eq .Name "DeletedAt") (or (eq .Name "CreateID") (or (eq .Name "UpdateID") (eq .Name "Password")))))))}}
		{{if not $isExcluded}}
		{{if eq .Name "Children"}}
		// Children 字段在树形查询时处理
		{{else}}
		{{.Name}}: entity.{{.Name}},
		{{end}}
		{{end}}
		{{end}}
	}

	{{if .ModelFeatures.IsTreeModel}}
	// 处理子节点
	if len(entity.Children) > 0 {
		resp.Children = s.modelListToRespList(entity.Children)
	}
	{{end}}

	return resp
}

// modelListToRespList 将模型列表转换为响应列表
func (s *{{toSnakeCase .ModelName}}Service) modelListToRespList(entities []*model.{{.ModelName}}) []*v1.{{.ModelName}}Resp {
	if len(entities) == 0 {
		return nil
	}
	
	respList := make([]*v1.{{.ModelName}}Resp, len(entities))
	for i, entity := range entities {
		respList[i] = s.modelToResp(entity)
	}
	return respList
}