package v1

import (
	"time"

	"{{.ModuleName}}/internal/model"
)

// {{.ModelName}}CreateReq 创建{{.ModelComment}}请求
// @Description 创建{{.ModelComment}}请求参数
type {{.ModelName}}CreateReq struct {
	{{range .Fields}}
	{{if and (ne .Name "ID") (and (ne .Name "UUID") (and (ne .Name "CreatedAt") (and (ne .Name "UpdatedAt") (and (ne .Name "DeletedAt") (and (ne .Name "CreateID") (and (ne .Name "UpdateID") (ne .Name "Children")))))))}}
	{{.Name}} {{.Type}} `json:"{{.JSONTag}}"{{if .Validation}} binding:"{{.Validation}}"{{end}}`{{if .Comment}} // {{.Comment}}{{end}}
	{{end}}
	{{end}}
}

// {{.ModelName}}UpdateReq 更新{{.ModelComment}}请求
// @Description 更新{{.ModelComment}}请求参数
type {{.ModelName}}UpdateReq struct {
	ID string `json:"id" binding:"required"` // {{.ModelComment}}UUID
	{{range .Fields}}
	{{if and (ne .Name "ID") (and (ne .Name "UUID") (and (ne .Name "CreatedAt") (and (ne .Name "UpdatedAt") (and (ne .Name "DeletedAt") (and (ne .Name "CreateID") (and (ne .Name "UpdateID") (ne .Name "Children")))))))}}
	{{.Name}} {{.Type}} `json:"{{.JSONTag}}"{{if .Validation}} binding:"{{.Validation}}"{{end}}`{{if .Comment}} // {{.Comment}}{{end}}
	{{end}}
	{{end}}
}

// {{.ModelName}}PageReq 分页查询{{.ModelComment}}请求
// @Description 分页查询{{.ModelComment}}请求参数
type {{.ModelName}}PageReq struct {
	*QueryPage
	*CreateTime
	*UpdateTime
	OrderBy  string `json:"orderBy" example:"created_at"`                    // 排序字段
	OrderDesc bool  `json:"orderDesc" example:"true"`                       // 是否降序
	{{range .SmartQueryFields}}
	{{if and (ne .Name "Children") (and (ne .Name "UUID") (and (ne .Name "CreateID") (and (ne .Name "UpdateID") (ne .Name "ParentID"))))}}
	{{if eq .QueryType "in"}}
	{{.Name}} []string `json:"{{.JSONTag}}"` // {{.Comment}}多值查询
	{{else if or (eq .Type "string") (eq .Type "*string")}}
	{{.Name}} string `json:"{{.JSONTag}}"` // {{.Comment}}{{if eq .QueryType "like"}}模糊搜索{{else}}查询{{end}}
	{{end}}
	{{end}}
	{{end}}
}

{{if .ModelFeatures.IsTreeModel}}
// {{.ModelName}}TreeReq 查询{{.ModelComment}}树形结构请求
// @Description 查询{{.ModelComment}}树形结构请求参数
type {{.ModelName}}TreeReq struct {
	{{range .SmartQueryFields}}
	{{if and (ne .Name "Children") (and (ne .Name "UUID") (and (ne .Name "CreateID") (and (ne .Name "UpdateID") (ne .Name "ParentID"))))}}
	{{if or (eq .Type "string") (eq .Type "*string")}}
	{{.Name}} string `json:"{{.JSONTag}}"` // {{.Comment}}模糊搜索
	{{end}}
	{{end}}
	{{end}}
}
{{end}}

// {{.ModelName}}Resp {{.ModelComment}}响应
// @Description {{.ModelComment}}响应数据
type {{.ModelName}}Resp struct {
	ID          int64      `json:"id"`          // 主键ID
	{{if .ModelFeatures.HasUUID}}
	UUID        string     `json:"uuid"`        // 全局唯一ID
	{{end}}
	{{if .ModelFeatures.HasCreatedAt}}
	CreatedAt   time.Time  `json:"createdAt"`   // 创建时间
	{{end}}
	{{if .ModelFeatures.HasUpdatedAt}}
	UpdatedAt   time.Time  `json:"updatedAt"`   // 更新时间
	{{end}}
	{{range .Fields}}
	{{if and (ne .Name "ID") (and (ne .Name "UUID") (and (ne .Name "CreatedAt") (and (ne .Name "UpdatedAt") (and (ne .Name "DeletedAt") (and (ne .Name "Password") (and (ne .Name "CreateID") (ne .Name "UpdateID")))))))}}
	{{if eq .Name "Children"}}
	{{.Name}} []*{{$.ModelName}}Resp `json:"{{.JSONTag}}"` // {{.Comment}}
	{{else}}
	{{.Name}} {{.Type}} `json:"{{.JSONTag}}"` // {{.Comment}}
	{{end}}
	{{end}}
	{{end}}
}

// To{{.ModelName}}Resp 转换为{{.ModelComment}}响应
// @Description 将数据库实体转换为API响应结构
func To{{.ModelName}}Resp(entity *model.{{.ModelName}}) *{{.ModelName}}Resp {
	if entity == nil {
		return nil
	}
	
	resp := &{{.ModelName}}Resp{
		ID:          entity.ID,
		{{if .ModelFeatures.HasUUID}}
		UUID:        entity.UUID,
		{{end}}
		{{if .ModelFeatures.HasCreatedAt}}
		CreatedAt:   entity.CreatedAt,
		{{end}}
		{{if .ModelFeatures.HasUpdatedAt}}
		UpdatedAt:   entity.UpdatedAt,
		{{end}}
		{{range .Fields}}
		{{if and (ne .Name "ID") (and (ne .Name "UUID") (and (ne .Name "CreatedAt") (and (ne .Name "UpdatedAt") (and (ne .Name "DeletedAt") (and (ne .Name "Password") (and (ne .Name "CreateID") (and (ne .Name "UpdateID") (ne .Name "Children"))))))))}}
		{{.Name}}: entity.{{.Name}},
		{{end}}
		{{end}}
	}

	{{if .ModelFeatures.IsTreeModel}}
	// 转换子节点
	if len(entity.Children) > 0 {
		resp.Children = make([]*{{.ModelName}}Resp, len(entity.Children))
		for i, child := range entity.Children {
			resp.Children[i] = To{{.ModelName}}Resp(child)
		}
	}
	{{end}}

	return resp
}

// To{{.ModelName}}RespList 批量转换为{{.ModelComment}}响应列表
// @Description 批量将数据库实体转换为API响应结构列表
func To{{.ModelName}}RespList(entities []*model.{{.ModelName}}) []*{{.ModelName}}Resp {
	if len(entities) == 0 {
		return []*{{.ModelName}}Resp{}
	}

	result := make([]*{{.ModelName}}Resp, len(entities))
	for i, entity := range entities {
		result[i] = To{{.ModelName}}Resp(entity)
	}
	return result
}