package repository

import (
	"context"
	v1 "{{.ModuleName}}/api/v1"
	"{{.ModuleName}}/internal/gen"
	"{{.ModuleName}}/internal/model"
	"github.com/pkg/errors"
	"gorm.io/gen/field"
	{{if .Features.EnableTransaction}}"gorm.io/gorm"{{end}}
)

// {{.ModelName}}Repository {{.ModelComment}}仓储接口
type {{.ModelName}}Repository interface {
	Create(ctx context.Context, entity *model.{{.ModelName}}) error
	QueryById(ctx context.Context, req *v1.ID) (*model.{{.ModelName}}, error)
	QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error)
	Update(ctx context.Context, entity *model.{{.ModelName}}) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	Query(ctx context.Context) ([]*model.{{.ModelName}}, error)
	{{if .ModelFeatures.HasStatus}}
	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error
	{{end}}
	{{if .Features.EnableBatch}}
	BatchCreate(ctx context.Context, entities []*model.{{.ModelName}}) error
	BatchUpdate(ctx context.Context, entities []*model.{{.ModelName}}) error
	BatchDelete(ctx context.Context, ids []string) error
	{{end}}
	{{if .Features.EnableTransaction}}
	CreateWithTx(ctx context.Context, entity *model.{{.ModelName}}) error
	UpdateWithTx(ctx context.Context, entity *model.{{.ModelName}}) error
	DeleteWithTx(ctx context.Context, req *v1.IDS[string]) error
	{{end}}
	{{if .ModelFeatures.HasSoftDelete}}
	Restore(ctx context.Context, req *v1.IDS[string]) error
	ForceDelete(ctx context.Context, req *v1.IDS[string]) error
	{{end}}
}

// {{toSnakeCase .ModelName}}Repository {{.ModelComment}}仓储实现
type {{toSnakeCase .ModelName}}Repository struct {
	*Repository
}

// New{{.ModelName}}Repository 创建{{.ModelComment}}仓储
func New{{.ModelName}}Repository(r *Repository) {{.ModelName}}Repository {
	return &{{toSnakeCase .ModelName}}Repository{
		Repository: r,
	}
}

// getColumns 获取查询字段
func (r *{{toSnakeCase .ModelName}}Repository) getColumns() []field.Expr {
	t := r.gen.{{.ModelName}}
	return []field.Expr{
		{{range .Fields}}
		{{if ne .Name "Children"}}
		t.{{.Name}},
		{{end}}
		{{end}}
	}
}

// buildQuery 构建基础查询
func (r *{{toSnakeCase .ModelName}}Repository) buildQuery(ctx context.Context) gen.I{{.ModelName}}Do {
	return r.gen.{{.ModelName}}.WithContext(ctx).Select(r.getColumns()...)
}

// Create 创建{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) Create(ctx context.Context, entity *model.{{.ModelName}}) error {
	if entity == nil {
		return v1.ErrInvalidParams
	}
	
	{{if .Features.EnableValidation}}
	// 数据验证
	if err := r.validateEntity(entity); err != nil {
		return v1.ErrInvalidParams
	}
	{{end}}
	
	if err := r.gen.{{.ModelName}}.WithContext(ctx).Create(entity); err != nil {
		return v1.ErrCreateFailed
	}
	
	return nil
}

// QueryById 根据ID查询{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) QueryById(ctx context.Context, req *v1.ID) (*model.{{.ModelName}}, error) {
	if req.ID == "" {
		return nil, v1.ErrInvalidParams
	}
	
	t := r.gen.{{.ModelName}}
	entity, err := r.buildQuery(ctx).
		Where(t.UUID.Eq(req.ID)).
		{{if .ModelFeatures.HasSoftDelete}}
		Where(t.DeletedAt.IsNull()).
		{{end}}
		{{if .ModelFeatures.HasTenantID}}
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		{{end}}
		First()
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, v1.ErrQueryFailed
	}
	
	return entity, nil
}

// QueryPage 分页查询{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error) {
	t := r.gen.{{.ModelName}}
	query := r.buildQuery(ctx)
	
	// 构建查询条件 - 只包含实际的搜索字段
	{{range .SmartQueryFields}}
	{{if and (ne .Name "UUID") (and (ne .Name "CreateID") (and (ne .Name "UpdateID") (ne .Name "ParentID")))}}
	{{if eq .QueryType "in"}}
	if len(req.{{.Name}}) > 0 {
		query = query.Where(t.{{.Name}}.In(req.{{.Name}}...))
	}
	{{else if eq .QueryType "like"}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Like("%" + req.{{.Name}} + "%"))
	}
	{{else if eq .QueryType "eq"}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Eq(req.{{.Name}}))
	}
	{{else}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Eq(req.{{.Name}}))
	}
	{{end}}
	{{end}}
	{{end}}


	{{if .ModelFeatures.HasTenantID}}
	// 多租户过滤
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}
	{{end}}
	// 默认排序
	{{if .ModelFeatures.HasSort}}
	query = query.Order(t.Sort.Desc(), t.CreatedAt.Desc())
	{{else if .ModelFeatures.HasCreatedAt}}
	query = query.Order(t.CreatedAt.Desc())
	{{else}}
	query = query.Order(t.ID.Desc())
	{{end}}
	
	// 分页查询
	list, count, err := query.FindByPage(req.GetPage()-1, req.GetPageSize())
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	return &v1.Page[*model.{{.ModelName}}]{
		Current:  req.GetPage(),
		PageSize: req.GetPageSize(),
		Total:    count,
		List:     list,
	}, nil
}

// Update 更新{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) Update(ctx context.Context, entity *model.{{.ModelName}}) error {
	if entity == nil {
		return v1.ErrInvalidParams
	}
	
	if entity.UUID == "" {
		return v1.ErrInvalidParams
	}
	
	{{if .Features.EnableValidation}}
	// 数据验证
	if err := r.validateEntity(entity); err != nil {
		return v1.ErrInvalidParams
	}
	{{end}}
	
	t := r.gen.{{.ModelName}}
	result, err := t.WithContext(ctx).
		Where(t.UUID.Eq(entity.UUID)).
		{{if .ModelFeatures.HasSoftDelete}}
		Where(t.DeletedAt.IsNull()).
		{{end}}
		{{if .ModelFeatures.HasTenantID}}
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		{{end}}
		Updates(entity)
	
	if err != nil {
		return v1.ErrUpdateFailed
	}
	
	if result.RowsAffected == 0 {
		return v1.ErrRecordNotFound
	}
	
	return nil
}

// Delete 删除{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) Delete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("删除ID列表不能为空")
	}
	
	t := r.gen.{{.ModelName}}
	query := t.WithContext(ctx).Where(t.UUID.In(req.Ids...))
	
	{{if .ModelFeatures.HasTenantID}}
	// 多租户过滤
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}
	{{end}}
	
	// 软删除（GORM会自动处理软删除逻辑）
	result, err := query.Delete()
	
	if err != nil {
		return v1.ErrDeleteFailed
	}
	
	if result.RowsAffected == 0 {
		return errors.New("没有找到要删除的记录")
	}
	
	return nil
}

// Query 查询所有{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) Query(ctx context.Context) ([]*model.{{.ModelName}}, error) {
	t := r.gen.{{.ModelName}}
	query := r.buildQuery(ctx)
	
	{{if .ModelFeatures.HasSoftDelete}}
	// 排除软删除的记录
	query = query.Where(t.DeletedAt.IsNull())
	{{end}}
	
	{{if .ModelFeatures.HasTenantID}}
	// 多租户过滤
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}
	{{end}}
	
	// 排序
	{{if .ModelFeatures.HasSort}}
	query = query.Order(t.Sort)
	{{else if .ModelFeatures.HasCreatedAt}}
	query = query.Order(t.CreatedAt.Desc())
	{{else}}
	query = query.Order(t.ID.Desc())
	{{end}}
	
	entities, err := query.Find()
	if err != nil {
		return nil, v1.ErrQueryFailed
	}
	
	return entities, nil
}

{{if .ModelFeatures.HasStatus}}
// UpdateStatus 更新{{.ModelComment}}状态
func (r *{{toSnakeCase .ModelName}}Repository) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}
	
	if status == "" {
		return errors.New("状态不能为空")
	}
	
	t := r.gen.{{.ModelName}}
	result, err := t.WithContext(ctx).
		Where(t.UUID.In(req.Ids...)).
		{{if .ModelFeatures.HasSoftDelete}}
		Where(t.DeletedAt.IsNull()).
		{{end}}
		{{if .ModelFeatures.HasTenantID}}
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		{{end}}
		Update(t.Status, status)
	
	if err != nil {
		return v1.ErrUpdateFailed
	}
	
	if result.RowsAffected == 0 {
		return errors.New("没有找到要更新的记录")
	}
	
	return nil
}
{{end}}

{{if .Features.EnableBatch}}
// BatchCreate 批量创建{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) BatchCreate(ctx context.Context, entities []*model.{{.ModelName}}) error {
	if len(entities) == 0 {
		return errors.New("实体列表不能为空")
	}
	
	{{if .Features.EnableValidation}}
	// 批量验证
	for i, entity := range entities {
		if err := r.validateEntity(entity); err != nil {
			return v1.ErrValidationFailed
		}
	}
	{{end}}
	
	// 批量创建，每批最多1000条
	batchSize := 1000
	for i := 0; i < len(entities); i += batchSize {
		end := i + batchSize
		if end > len(entities) {
			end = len(entities)
		}
		
		batch := entities[i:end]
		if err := r.gen.{{.ModelName}}.WithContext(ctx).CreateInBatches(batch, len(batch)); err != nil {
			return v1.ErrCreateFailed
		}
	}
	
	return nil
}

// BatchUpdate 批量更新{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) BatchUpdate(ctx context.Context, entities []*model.{{.ModelName}}) error {
	if len(entities) == 0 {
		return errors.New("实体列表不能为空")
	}
	
	{{if .Features.EnableValidation}}
	// 批量验证
	for i, entity := range entities {
		if err := r.validateEntity(entity); err != nil {
			return v1.ErrValidationFailed
		}
	}
	{{end}}
	
	// 逐个更新（GORM不支持批量更新不同数据）
	for i, entity := range entities {
		if err := r.Update(ctx, entity); err != nil {
			return v1.ErrUpdateFailed
		}
	}
	
	return nil
}

// BatchDelete 批量删除{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) BatchDelete(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}
	
	return r.Delete(ctx, &v1.IDS[string]{Ids: ids})
}
{{end}}

{{if .Features.EnableTransaction}}
// CreateWithTx 在事务中创建{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) CreateWithTx(ctx context.Context, entity *model.{{.ModelName}}) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		if err := tx.WithContext(ctx).{{.ModelName}}.Create(entity); err != nil {
			return v1.ErrTransactionFailed
		}
		return nil
	})
}

// UpdateWithTx 在事务中更新{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) UpdateWithTx(ctx context.Context, entity *model.{{.ModelName}}) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		result, err := tx.WithContext(ctx).{{.ModelName}}.
			Where(tx.{{.ModelName}}.UUID.Eq(entity.UUID)).
			Updates(entity)
		
		if err != nil {
			return v1.ErrTransactionFailed
		}
		
		if result.RowsAffected == 0 {
			return errors.New("没有找到要更新的记录")
		}
		
		return nil
	})
}

// DeleteWithTx 在事务中删除{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) DeleteWithTx(ctx context.Context, req *v1.IDS[string]) error {
	return r.gen.Transaction(func(tx *gen.Query) error {
		result, err := tx.WithContext(ctx).{{.ModelName}}.Where(tx.{{.ModelName}}.UUID.In(req.Ids...)).Delete()
		
		if err != nil {
			return v1.ErrTransactionFailed
		}
		
		if result.RowsAffected == 0 {
			return errors.New("没有找到要删除的记录")
		}
		
		return nil
	})
}
{{end}}

{{if .ModelFeatures.HasSoftDelete}}
// Restore 恢复软删除的{{.ModelComment}}
func (r *{{toSnakeCase .ModelName}}Repository) Restore(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}
	
	t := r.gen.{{.ModelName}}
	result, err := t.WithContext(ctx).
		Unscoped().
		Where(t.UUID.In(req.Ids...)).
		Where(t.DeletedAt.IsNotNull()).
		{{if .ModelFeatures.HasTenantID}}
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		{{end}}
		Update(t.DeletedAt, nil)
	
	if err != nil {
		return v1.ErrUpdateFailed
	}
	
	if result.RowsAffected == 0 {
		return errors.New("没有找到要恢复的记录")
	}
	
	return nil
}

// ForceDelete 强制删除{{.ModelComment}}（物理删除）
func (r *{{toSnakeCase .ModelName}}Repository) ForceDelete(ctx context.Context, req *v1.IDS[string]) error {
	if len(req.Ids) == 0 {
		return errors.New("ID列表不能为空")
	}
	
	t := r.gen.{{.ModelName}}
	result, err := t.WithContext(ctx).
		Unscoped().
		Where(t.UUID.In(req.Ids...)).
		{{if .ModelFeatures.HasTenantID}}
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		{{end}}
		Delete()
	
	if err != nil {
		return v1.ErrDeleteFailed
	}
	
	if result.RowsAffected == 0 {
		return errors.New("没有找到要删除的记录")
	}
	
	return nil
}
{{end}}



{{if .ModelFeatures.HasTenantID}}
// getTenantID 从上下文获取租户ID
func (r *{{toSnakeCase .ModelName}}Repository) getTenantID(ctx context.Context) string {
	if tenantID, ok := ctx.Value("tenant_id").(string); ok {
		return tenantID
	}
	return ""
}
{{end}}

{{if .Features.EnableValidation}}
// validateEntity 验证实体
func (r *{{toSnakeCase .ModelName}}Repository) validateEntity(entity *model.{{.ModelName}}) error {
	if entity == nil {
		return errors.New("实体不能为空")
	}
	
	{{range .RequiredFields}}
	{{if eq .Type "string"}}
	if entity.{{.Name}} == "" {
		return errors.New("{{.Comment}}不能为空")
	}
	{{else if eq .Type "*string"}}
	if entity.{{.ModelName}} == nil || *entity.{{.ModelName}} == "" {
		return errors.New("{{.Comment}}不能为空")
	}
	{{end}}
	{{end}}
	
	// 其他验证逻辑
	return nil
}
{{end}}

// Count 统计{{.ModelComment}}数量
func (r *{{toSnakeCase .ModelName}}Repository) Count(ctx context.Context) (int64, error) {
	t := r.gen.{{.ModelName}}
	query := t.WithContext(ctx)
	
	{{if .ModelFeatures.HasSoftDelete}}
	query = query.Where(t.DeletedAt.IsNull())
	{{end}}
	
	{{if .ModelFeatures.HasTenantID}}
	if tenantID := r.getTenantID(ctx); tenantID != "" {
		query = query.Where(t.TenantID.Eq(tenantID))
	}
	{{end}}
	
	count, err := query.Count()
	if err != nil {
		return 0, v1.ErrQueryFailed
	}
	
	return count, nil
}

// Exists 检查{{.ModelComment}}是否存在
func (r *{{toSnakeCase .ModelName}}Repository) Exists(ctx context.Context, id string) (bool, error) {
	if id == "" {
		return false, errors.New("ID不能为空")
	}
	
	t := r.gen.{{.ModelName}}
	count, err := t.WithContext(ctx).
		Where(t.UUID.Eq(id)).
		{{if .ModelFeatures.HasSoftDelete}}
		Where(t.DeletedAt.IsNull()).
		{{end}}
		{{if .ModelFeatures.HasTenantID}}
		Where(t.TenantID.Eq(r.getTenantID(ctx))).
		{{end}}
		Count()
	
	if err != nil {
		return false, v1.ErrQueryFailed
	}
	
	return count > 0, nil
}