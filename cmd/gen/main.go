package main

import (
	"flag"
	"fmt"
	"strings"

	"admin/internal/utils"
	"admin/pkg/config"
	genext "admin/pkg/gen-ext"
	"admin/pkg/log"
	"admin/pkg/zapgorm2"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

// getModelNameByTable 根据表名返回模型名，支持特殊映射和默认规则，便于维护和扩展
func getModelNameByTable(tableName string) string {
	// 特殊表名到模型名的映射
	specialMap := map[string]string{
		"notification_receivers": "NotificationReceiver",
		"notifications":          "Notification",
		"sys_route_meta":         "SysRouteMetum",
		"system_operation_logs":  "SystemOperationLog",
		"user_group_relations":   "UserGroupRelation",
		"user_groups":            "UserGroup",
	}
	if model, ok := specialMap[tableName]; ok {
		return model
	}
	// 默认规则：下划线转驼峰并首字母大写
	return utils.CapitalizeFirstLetter(utils.UnderscoreToCamelCase(tableName))
}

// TableInfo 表信息
type TableInfo struct {
	TableName string
	ModelName string
	HasFields map[string]bool
}

func main() {
	envConf := flag.String("conf", "config/local.yml", "config path, eg: -conf ./config/local.toml")
	flag.Parse()
	conf := config.NewConfig(*envConf)
	logger := log.NewLog(conf)
	loggers := zapgorm2.New(logger.Logger)
	loggers.LogMode(gormlogger.Info)

	dsn := conf.GetString("data.db.user.dsn")
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger:                 loggers,
		SkipDefaultTransaction: true,
		PrepareStmt:            true,
	})
	if err != nil {
		panic(err)
	}

	g := gen.NewGenerator(gen.Config{
		OutPath:           "./internal/gen",
		FieldNullable:     true,
		FieldCoverable:    true,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
		FieldSignable:     true,
		Mode:              gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
	})
	g.UseDB(db)

	// 自定义字段的数据类型
	dataMap := map[string]func(detailType gorm.ColumnType) (dataType string){
		"tinyint": func(detailType gorm.ColumnType) (dataType string) {
			columnType, _ := detailType.ColumnType()
			if strings.Contains(columnType, "tinyint(1)") {
				return "bool"
			}
			return "int64"
		},
		"smallint":  func(detailType gorm.ColumnType) (dataType string) { return "int64" },
		"mediumint": func(detailType gorm.ColumnType) (dataType string) { return "int64" },
		"bigint":    func(detailType gorm.ColumnType) (dataType string) { return "int64" },
		"int":       func(detailType gorm.ColumnType) (dataType string) { return "int64" },
		"json":      func(detailType gorm.ColumnType) (dataType string) { return "datatypes.JSON" },
		"enum":      func(detailType gorm.ColumnType) (dataType string) { return "string" },
		"timestamp": func(detailType gorm.ColumnType) (dataType string) {
			if detailType.Name() == "deleted_at" {
				return "gorm.DeletedAt"
			}
			return "time.Time"
		},
	}
	g.WithDataTypeMap(dataMap)

	// 自定义 JSON tag
	jsonTagMap := map[string]string{
		"created_at": "createTime",
		"updated_at": "updateTime",
		"deleted_at": "-",
		"password":   "-",
	}
	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
		if val, ok := jsonTagMap[columnName]; ok {
			return val
		}
		return utils.UnderscoreToCamelCase(columnName)
	})

	// 获取所有表
	tables, err := db.Migrator().GetTables()
	if err != nil {
		panic(fmt.Errorf("failed to get tables: %w", err))
	}

	// 收集所有表的信息
	var tableInfos []TableInfo

	// 遍历所有表，动态应用规则
	for _, tableName := range tables {
		// 检查表是否含有特定字段
		cols, err := db.Migrator().ColumnTypes(tableName)
		if err != nil {
			panic(err)
		}

		// 收集字段信息
		hasFields := make(map[string]bool)
		for _, col := range cols {
			hasFields[col.Name()] = true
		}

		hasParentID := hasFields["parent_id"]
		hasUUID := hasFields["uuid"]

		// 定义一个 options 切片，用来存放要应用的自定义规则
		var opts []gen.ModelOpt

		if tableName == "user" {
			opts = append(opts, gen.FieldNew("Post", "[]*Posts", field.Tag{"json": "post,omitzero", "gorm": "serializer:json; <-:false"}))
		}

		// 如果有 parent_id，则添加 Children 字段
		if hasParentID {
			fmt.Printf("  -> Found 'parent_id' in %s, adding 'Children' field and tree methods.\n", tableName)
			modelName := utils.CapitalizeFirstLetter(utils.UnderscoreToCamelCase(tableName))
			// 添加 Children 字段
			childrenField := gen.FieldNew("Children", fmt.Sprintf("[]*%s", modelName), field.Tag{
				"gorm": "-",
				"json": "children",
			})
			opts = append(opts, childrenField)
		}

		// 为所有表应用通用规则和动态规则
		softDeleteField := gen.FieldType("deleted_at", "gorm.DeletedAt")
		softDeleteFieldJsonTag := gen.FieldJSONTag("deleted_at", "-")

		finalOpts := append([]gen.ModelOpt{softDeleteField, softDeleteFieldJsonTag}, opts...)

		// 生成模型
		modelName := getModelNameByTable(tableName)

		// 收集表信息
		tableInfos = append(tableInfos, TableInfo{
			TableName: tableName,
			ModelName: modelName,
			HasFields: hasFields,
		})

		fmt.Printf("  -> Processing table '%s' -> model '%s'\n", tableName, modelName)

		g.ApplyBasic(g.GenerateModel(tableName, finalOpts...))

		// 检查并为 uuid 字段添加唯一索引（排除 casbin_rule 表）
		if hasUUID && tableName != "casbin_rule" {
			idxName := fmt.Sprintf("idx_%s_uuid_unique", tableName)
			// createIdxSQL := fmt.Sprintf("CREATE UNIQUE INDEX IF NOT EXISTS %s ON %s (uuid)", idxName, tableName) // 已废弃，MySQL 不支持 IF NOT EXISTS
			// MySQL 不支持 IF NOT EXISTS，需先判断索引是否存在
			var count int64
			checkIdxSQL := fmt.Sprintf("SELECT COUNT(1) FROM information_schema.statistics WHERE table_schema=DATABASE() AND table_name='%s' AND index_name='%s'", tableName, idxName)
			if err := db.Raw(checkIdxSQL).Scan(&count).Error; err != nil {
				fmt.Printf("  -> Failed to check index for %s: %v\n", tableName, err)
			} else if count == 0 {
				if err := db.Exec(fmt.Sprintf("CREATE UNIQUE INDEX %s ON %s (uuid)", idxName, tableName)).Error; err != nil {
					fmt.Printf("  -> Failed to create unique index on uuid for %s: %v\n", tableName, err)
				} else {
					fmt.Printf("  -> Created unique index on uuid for %s\n", tableName)
				}
			} else {
				fmt.Printf("  -> Unique index on uuid already exists for %s\n", tableName)
			}
		}
	}

	g.Execute()

	// 使用模板扩展系统生成方法
	fmt.Println("Generating model methods using template system...")
	generator := genext.NewModelMethodsGenerator("./internal/model")

	// 加载模板配置
	if err := generator.LoadTemplatesFromConfig("./config/method_templates.yaml"); err != nil {
		fmt.Printf("Warning: Failed to load template config, using defaults: %v\n", err)
		// 如果配置文件不存在，只添加BeforeCreate模板
	}

	// 为每个表生成方法
	for _, tableInfo := range tableInfos {
		if err := generator.GenerateForTable(tableInfo.TableName, tableInfo.ModelName, tableInfo.HasFields); err != nil {
			fmt.Printf("Error generating methods for %s: %v\n", tableInfo.TableName, err)
		} else {
			fmt.Printf("  -> Generated methods for %s\n", tableInfo.TableName)
		}
	}

	fmt.Println("Model methods generation completed!")
}

// 移除旧的generateUUIDHooks函数，因为现在使用模板系统
