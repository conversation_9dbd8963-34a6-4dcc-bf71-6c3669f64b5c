package main

import (
	"flag"
	"strings"

	"admin/internal/utils"
	"admin/pkg/config"
	"admin/pkg/log"
	"admin/pkg/zapgorm2"

	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

func main() {
	envConf := flag.String("conf", "config/local.yml", "config path, eg: -conf ./config/local.toml")
	flag.Parse()
	conf := config.NewConfig(*envConf)
	logger := log.NewLog(conf)
	loggers := zapgorm2.New(logger.Logger)
	loggers.LogMode(gormlogger.Info)
	loggers.LogMode(gormlogger.Info)

	dsn := conf.GetString("data.db.user.dsn")
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger:                 loggers,
		SkipDefaultTransaction: true,
		PrepareStmt:            true,
	})
	if err != nil {
		panic(err)
	}

	g := gen.NewGenerator(gen.Config{
		// 指定 model 的输出路径
		OutPath:           "./internal/gen",
		FieldNullable:     true, // generate field nullable
		FieldCoverable:    true, // generate field coverable
		FieldWithIndexTag: true, // generate field with gorm index tag
		FieldWithTypeTag:  true, // generate field with gorm column type tag
		// 指定查询方法的输出路径
		Mode: gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})
	g.UseDB(db)

	// 自定义字段的数据类型
	// 统一数字类型为int64,兼容protobuf
	dataMap := map[string]func(detailType gorm.ColumnType) (dataType string){
		"tinyint": func(detailType gorm.ColumnType) (dataType string) {
			columnType, _ := detailType.ColumnType()
			if strings.Contains(columnType, "tinyint(1)") {
				return "bool"
			}
			return "int64"
		},
		"smallint": func(detailType gorm.ColumnType) (dataType string) {
			if n, ok := detailType.Nullable(); ok && n {
				return "int64"
			}
			return "int64"
		},
		"mediumint": func(detailType gorm.ColumnType) (dataType string) {
			if n, ok := detailType.Nullable(); ok && n {
				return "int64"
			}
			return "int64"
		},
		"bigint": func(detailType gorm.ColumnType) (dataType string) {
			if n, ok := detailType.Nullable(); ok && n {
				return "int64"
			}
			return "int64"
		},
		"int": func(detailType gorm.ColumnType) (dataType string) {
			if n, ok := detailType.Nullable(); ok && n {
				return "int64"
			}
			return "int64"
		},
		"json": func(detailType gorm.ColumnType) (dataType string) {
			return "datatypes.JSON"
		},
		"enum": func(detailType gorm.ColumnType) (dataType string) {
			return "string"
		},

		"timestamp": func(detailType gorm.ColumnType) (dataType string) {
			if detailType.Name() == "deleted_at" {
				return "gorm.DeletedAt"
			}
			return "time.Time"
		},
	}
	// 要先于`ApplyBasic`执行
	g.WithDataTypeMap(dataMap)

	jsonTagMap := map[string]string{
		"created_at": "createTime",
		"updated_at": "updateTime",
		"deleted_at": "-",
		"password":   "-",
	}
	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
		if val, ok := jsonTagMap[columnName]; ok {
			return val
		}
		return utils.UnderscoreToCamelCase(columnName)
	})
	softDeleteField := gen.FieldType("deleted_at", "gorm.DeletedAt")
	softDeleteFieldJsonTag := gen.FieldJSONTag("deleted_at", "-")
	g.GenerateAllTable(softDeleteField, softDeleteFieldJsonTag)

	g.ApplyBasic(
		g.GenerateAllTable()...,
	)
	g.GenerateModel("user",
		gen.FieldNew("Post", "[]*Posts", field.Tag{"json": "post", "gorm": "serializer:json; <-:false"}),
	)
	g.Execute()
}
