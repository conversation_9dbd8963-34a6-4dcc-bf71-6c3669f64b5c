package main

import (
	"admin/internal/utils"
	"flag"
	"fmt"
	"os"
)

func main() {
	var demoType = flag.String("type", "performance", "演示类型: performance, load, comparison")
	flag.Parse()

	fmt.Println("🚀 工作池演示程序")
	fmt.Println("==================")
	fmt.Printf("演示类型: %s\n\n", *demoType)

	switch *demoType {
	case "performance":
		utils.PerformanceDemo()
	case "load":
		utils.LoadTestDemo()
	case "comparison":
		utils.ComparisonDemo()
	default:
		fmt.Printf("未知的演示类型: %s\n", *demoType)
		fmt.Println("可用类型: performance, load, comparison")
		os.Exit(1)
	}
}