package interactive

import (
	"bufio"
	"fmt"
	"os"
	"strconv"
	"strings"

	"admin/cmd/crud-gen/config"
	"admin/cmd/crud-gen/generator"
)

// CLI 交互式命令行界面
type CLI struct {
	scanner   *bufio.Scanner
	generator *generator.Generator
	config    *config.Config
}

// NewCLI 创建交互式CLI
func NewCLI(gen *generator.Generator, cfg *config.Config) *CLI {
	return &CLI{
		scanner:   bufio.NewScanner(os.Stdin),
		generator: gen,
		config:    cfg,
	}
}

// Run 运行交互式界面
func (c *CLI) Run() error {
	return c.run()
}

func (c *CLI) run() error {
	fmt.Println("🎯 欢迎使用CRUD代码生成器交互式界面")
	fmt.Println(strings.Repeat("=", 50))

	for {
		choice, err := c.showMainMenu()
		if err != nil {
			return err
		}

		switch choice {
		case 1:
			if err := c.generateCRUD(); err != nil {
				fmt.Printf("❌ 生成失败: %v\n", err)
			}
		case 2:
			if err := c.previewGeneration(); err != nil {
				fmt.Printf("❌ 预览失败: %v\n", err)
			}
		case 3:
			if err := c.configureSettings(); err != nil {
				fmt.Printf("❌ 配置失败: %v\n", err)
			}
		case 4:
			if err := c.viewModels(); err != nil {
				fmt.Printf("❌ 查看模型失败: %v\n", err)
			}
		case 5:
			if err := c.manageTemplates(); err != nil {
				fmt.Printf("❌ 管理模板失败: %v\n", err)
			}
		case 6:
			fmt.Println("👋 感谢使用CRUD代码生成器！")
			return nil
		default:
			fmt.Println("❌ 无效选择，请重新输入")
		}

		fmt.Println()
	}
}

func (c *CLI) showMainMenu() (int, error) {
	fmt.Println("📋 主菜单:")
	fmt.Println("  1. 生成CRUD代码")
	fmt.Println("  2. 预览生成结果")
	fmt.Println("  3. 配置设置")
	fmt.Println("  4. 查看模型")
	fmt.Println("  5. 管理模板")
	fmt.Println("  6. 退出")
	fmt.Print("请选择操作 (1-6): ")

	input := c.readInput()
	choice, err := strconv.Atoi(input)
	if err != nil || choice < 1 || choice > 6 {
		return 0, fmt.Errorf("无效选择")
	}

	return choice, nil
}

func (c *CLI) generateCRUD() error {
	fmt.Println("\n🚀 生成CRUD代码")
	fmt.Println(strings.Repeat("-", 30))

	// 分析模型
	models, err := c.generator.AnalyzeModels()
	if err != nil {
		return fmt.Errorf("分析模型失败: %w", err)
	}

	if len(models) == 0 {
		fmt.Println("⚠️  没有找到可生成的模型")
		return nil
	}

	// 选择模型
	selectedModels, err := c.selectModels(models)
	if err != nil {
		return err
	}

	if len(selectedModels) == 0 {
		fmt.Println("⚠️  没有选择任何模型")
		return nil
	}

	// 选择生成层级
	if err := c.selectLayers(); err != nil {
		return err
	}

	// 确认生成
	if !c.confirmGeneration(selectedModels) {
		fmt.Println("❌ 已取消生成")
		return nil
	}

	// 执行生成
	opts := generator.GenerateOptions{
		Force:   c.askYesNo("是否强制覆盖已存在的文件?"),
		Verbose: true,
		Backup:  c.askYesNo("是否备份现有文件?"),
	}

	result, err := c.generator.Generate(selectedModels, opts)
	if err != nil {
		return err
	}

	// 显示结果
	c.showGenerationResult(result)
	return nil
}

func (c *CLI) previewGeneration() error {
	fmt.Println("\n🔍 预览生成结果")
	fmt.Println(strings.Repeat("-", 30))

	// 分析模型
	models, err := c.generator.AnalyzeModels()
	if err != nil {
		return fmt.Errorf("分析模型失败: %w", err)
	}

	if len(models) == 0 {
		fmt.Println("⚠️  没有找到可生成的模型")
		return nil
	}

	// 选择模型
	selectedModels, err := c.selectModels(models)
	if err != nil {
		return err
	}

	if len(selectedModels) == 0 {
		fmt.Println("⚠️  没有选择任何模型")
		return nil
	}

	// 显示预览
	fmt.Println("\n📁 将要生成的文件:")
	fmt.Println(strings.Repeat("=", 50))

	for i, model := range selectedModels {
		fmt.Printf("\n%d. 模型: %s (%s)\n", i+1, model.Name, model.Comment)
		files := c.generator.GetGeneratedFiles(model)
		for _, file := range files {
			fmt.Printf("   📄 %s\n", file)
		}
	}

	fmt.Println("\n💡 使用主菜单选项1执行实际生成")
	return nil
}

func (c *CLI) selectModels(models []generator.ModelInfo) ([]generator.ModelInfo, error) {
	fmt.Printf("\n📊 发现 %d 个模型:\n", len(models))

	for i, model := range models {
		status := "✅"
		if c.config.IsModelExcluded(model.Name) {
			status = "❌"
		}
		fmt.Printf("  %d. %s %s (%s)\n", i+1, status, model.Name, model.Comment)
	}

	fmt.Println("\n选择模型:")
	fmt.Println("  a. 全部模型")
	fmt.Println("  s. 指定模型 (输入序号，用逗号分隔)")
	fmt.Print("请选择 (a/s): ")

	choice := strings.ToLower(c.readInput())

	switch choice {
	case "a":
		var selected []generator.ModelInfo
		for _, model := range models {
			if !c.config.IsModelExcluded(model.Name) {
				selected = append(selected, model)
			}
		}
		return selected, nil

	case "s":
		fmt.Print("请输入模型序号 (用逗号分隔): ")
		input := c.readInput()
		indices := strings.Split(input, ",")

		var selected []generator.ModelInfo
		for _, indexStr := range indices {
			indexStr = strings.TrimSpace(indexStr)
			index, err := strconv.Atoi(indexStr)
			if err != nil || index < 1 || index > len(models) {
				fmt.Printf("⚠️  无效序号: %s\n", indexStr)
				continue
			}

			model := models[index-1]
			if !c.config.IsModelExcluded(model.Name) {
				selected = append(selected, model)
			} else {
				fmt.Printf("⚠️  模型 %s 已被排除\n", model.Name)
			}
		}
		return selected, nil

	default:
		return nil, fmt.Errorf("无效选择")
	}
}

func (c *CLI) selectLayers() error {
	fmt.Println("\n📋 选择生成层级:")

	layers := []struct {
		name    string
		enabled *bool
		desc    string
	}{
		{"Controller", &c.config.Generator.GenerateController, "HTTP请求处理层"},
		{"Service", &c.config.Generator.GenerateService, "业务逻辑层"},
		{"Repository", &c.config.Generator.GenerateRepository, "数据访问层"},
		{"API结构体", &c.config.Generator.GenerateAPIStructs, "请求/响应结构体"},
		{"路由配置", &c.config.Generator.GenerateRoutes, "路由注册"},
		{"测试文件", &c.config.Generator.GenerateTests, "单元测试"},
	}

	for i, layer := range layers {
		status := "❌"
		if *layer.enabled {
			status = "✅"
		}
		fmt.Printf("  %d. %s %s - %s\n", i+1, status, layer.name, layer.desc)
	}

	fmt.Print("\n是否修改层级选择? (y/n): ")
	if !c.askYesNo("") {
		return nil
	}

	for _, layer := range layers {
		fmt.Printf("生成 %s? (y/n): ", layer.name)
		*layer.enabled = c.askYesNo("")
	}

	return nil
}

func (c *CLI) confirmGeneration(models []generator.ModelInfo) bool {
	fmt.Printf("\n📋 生成确认:\n")
	fmt.Printf("  - 模型数量: %d\n", len(models))
	fmt.Printf("  - Controller: %v\n", c.config.Generator.GenerateController)
	fmt.Printf("  - Service: %v\n", c.config.Generator.GenerateService)
	fmt.Printf("  - Repository: %v\n", c.config.Generator.GenerateRepository)
	fmt.Printf("  - API结构体: %v\n", c.config.Generator.GenerateAPIStructs)
	fmt.Printf("  - 路由配置: %v\n", c.config.Generator.GenerateRoutes)
	fmt.Printf("  - 测试文件: %v\n", c.config.Generator.GenerateTests)

	return c.askYesNo("\n确认生成?")
}

func (c *CLI) showGenerationResult(result *generator.GenerationResult) {
	fmt.Println("\n🎯 生成完成!")
	fmt.Println(strings.Repeat("=", 40))
	fmt.Printf("📊 生成统计:\n")
	fmt.Printf("  - 模型数量: %d\n", result.ModelCount)
	fmt.Printf("  - Controller: %d 个文件\n", result.ControllerCount)
	fmt.Printf("  - Service: %d 个文件\n", result.ServiceCount)
	fmt.Printf("  - Repository: %d 个文件\n", result.RepositoryCount)
	fmt.Printf("  - API结构体: %d 个文件\n", result.APICount)
	fmt.Printf("  - 路由配置: %d 个文件\n", result.RouteCount)
	fmt.Printf("  - 测试文件: %d 个文件\n", result.TestCount)
	fmt.Printf("\n⏱️  总耗时: %.2fs\n", result.Duration.Seconds())
	fmt.Printf("💾 总文件大小: %.1fKB\n", float64(result.TotalSize)/1024)

	if len(result.Errors) > 0 {
		fmt.Printf("\n❌ 错误 (%d):\n", len(result.Errors))
		for _, err := range result.Errors {
			fmt.Printf("  - %s\n", err)
		}
	} else {
		fmt.Println("\n🎉 所有文件生成成功!")
	}

	if len(result.Features) > 0 {
		fmt.Printf("\n📋 生成的功能特性:\n")
		for _, feature := range result.Features {
			fmt.Printf("  ✅ %s\n", feature)
		}
	}
}

func (c *CLI) configureSettings() error {
	fmt.Println("\n⚙️  配置设置")
	fmt.Println(strings.Repeat("-", 30))

	fmt.Println("1. 基础配置")
	fmt.Println("2. 输出配置")
	fmt.Println("3. 功能特性")
	fmt.Println("4. 代码风格")
	fmt.Println("5. 返回主菜单")
	fmt.Print("请选择 (1-5): ")

	choice := c.readInput()
	switch choice {
	case "1":
		return c.configureBasic()
	case "2":
		return c.configureOutput()
	case "3":
		return c.configureFeatures()
	case "4":
		return c.configureCodeStyle()
	case "5":
		return nil
	default:
		return fmt.Errorf("无效选择")
	}
}

func (c *CLI) configureBasic() error {
	fmt.Println("\n📝 基础配置")

	fmt.Printf("模型目录 [%s]: ", c.config.Generator.ModelDir)
	if input := c.readInput(); input != "" {
		c.config.Generator.ModelDir = input
	}

	fmt.Printf("包名 [%s]: ", c.config.Generator.PackageName)
	if input := c.readInput(); input != "" {
		c.config.Generator.PackageName = input
	}

	fmt.Printf("模块名 [%s]: ", c.config.Generator.ModuleName)
	if input := c.readInput(); input != "" {
		c.config.Generator.ModuleName = input
	}

	fmt.Printf("作者 [%s]: ", c.config.Generator.Author)
	if input := c.readInput(); input != "" {
		c.config.Generator.Author = input
	}

	return nil
}

func (c *CLI) configureOutput() error {
	fmt.Println("\n📁 输出配置")

	fmt.Printf("Controller目录 [%s]: ", c.config.Output.ControllerDir)
	if input := c.readInput(); input != "" {
		c.config.Output.ControllerDir = input
	}

	fmt.Printf("Service目录 [%s]: ", c.config.Output.ServiceDir)
	if input := c.readInput(); input != "" {
		c.config.Output.ServiceDir = input
	}

	fmt.Printf("Repository目录 [%s]: ", c.config.Output.RepositoryDir)
	if input := c.readInput(); input != "" {
		c.config.Output.RepositoryDir = input
	}

	fmt.Printf("API目录 [%s]: ", c.config.Output.APIDir)
	if input := c.readInput(); input != "" {
		c.config.Output.APIDir = input
	}

	return nil
}

func (c *CLI) configureFeatures() error {
	fmt.Println("\n🎛️  功能特性配置")

	features := []struct {
		name    string
		enabled *bool
		desc    string
	}{
		{"缓存支持", &c.config.Features.EnableCache, "启用Redis缓存"},
		{"数据验证", &c.config.Features.EnableValidation, "启用请求数据验证"},
		{"软删除", &c.config.Features.EnableSoftDelete, "启用软删除功能"},
		{"审计日志", &c.config.Features.EnableAudit, "启用操作审计"},
		{"多租户", &c.config.Features.EnableTenant, "启用多租户支持"},
		{"树形结构", &c.config.Features.EnableTree, "启用树形数据结构"},
		{"状态管理", &c.config.Features.EnableStatus, "启用状态字段管理"},
		{"分页查询", &c.config.Features.EnablePagination, "启用分页功能"},
		{"搜索功能", &c.config.Features.EnableSearch, "启用条件搜索"},
		{"排序功能", &c.config.Features.EnableSort, "启用结果排序"},
	}

	for _, feature := range features {
		fmt.Printf("%s (%s) [%v]: ", feature.name, feature.desc, *feature.enabled)
		if c.askYesNo("") != *feature.enabled {
			*feature.enabled = !*feature.enabled
		}
	}

	return nil
}

func (c *CLI) configureCodeStyle() error {
	fmt.Println("\n🎨 代码风格配置")

	fmt.Printf("Controller后缀 [%s]: ", c.config.CodeStyle.Naming.ControllerSuffix)
	if input := c.readInput(); input != "" {
		c.config.CodeStyle.Naming.ControllerSuffix = input
	}

	fmt.Printf("Service后缀 [%s]: ", c.config.CodeStyle.Naming.ServiceSuffix)
	if input := c.readInput(); input != "" {
		c.config.CodeStyle.Naming.ServiceSuffix = input
	}

	fmt.Printf("Repository后缀 [%s]: ", c.config.CodeStyle.Naming.RepositorySuffix)
	if input := c.readInput(); input != "" {
		c.config.CodeStyle.Naming.RepositorySuffix = input
	}

	return nil
}

func (c *CLI) viewModels() error {
	fmt.Println("\n📊 查看模型")
	fmt.Println(strings.Repeat("-", 30))

	models, err := c.generator.AnalyzeModels()
	if err != nil {
		return fmt.Errorf("分析模型失败: %w", err)
	}

	if len(models) == 0 {
		fmt.Println("⚠️  没有找到任何模型")
		return nil
	}

	for i, model := range models {
		status := "✅ 包含"
		if c.config.IsModelExcluded(model.Name) {
			status = "❌ 排除"
		}

		fmt.Printf("\n%d. %s %s\n", i+1, status, model.Name)
		fmt.Printf("   表名: %s\n", model.TableName)
		fmt.Printf("   注释: %s\n", model.Comment)
		fmt.Printf("   字段数: %d\n", len(model.Fields))

		// 显示特性
		features := []string{}
		if model.Features.HasUUID {
			features = append(features, "UUID")
		}
		if model.Features.HasTenantID {
			features = append(features, "多租户")
		}
		if model.Features.HasSoftDelete {
			features = append(features, "软删除")
		}
		if model.Features.IsTreeModel {
			features = append(features, "树形结构")
		}
		if model.Features.HasStatus {
			features = append(features, "状态管理")
		}

		if len(features) > 0 {
			fmt.Printf("   特性: %s\n", strings.Join(features, ", "))
		}
	}

	return nil
}

func (c *CLI) manageTemplates() error {
	fmt.Println("\n📝 管理模板")
	fmt.Println(strings.Repeat("-", 30))
	fmt.Println("1. 查看模板配置")
	fmt.Println("2. 修改模板目录")
	fmt.Println("3. 返回主菜单")
	fmt.Print("请选择 (1-3): ")

	choice := c.readInput()
	switch choice {
	case "1":
		return c.viewTemplateConfig()
	case "2":
		return c.changeTemplateDir()
	case "3":
		return nil
	default:
		return fmt.Errorf("无效选择")
	}
}

func (c *CLI) viewTemplateConfig() error {
	fmt.Println("\n📋 模板配置:")
	fmt.Printf("  模板目录: %s\n", c.config.Templates.TemplateDir)
	fmt.Printf("  Controller模板: %s\n", c.config.Templates.Controller)
	fmt.Printf("  Service模板: %s\n", c.config.Templates.Service)
	fmt.Printf("  Repository模板: %s\n", c.config.Templates.Repository)
	fmt.Printf("  API结构体模板: %s\n", c.config.Templates.APIStruct)
	fmt.Printf("  路由模板: %s\n", c.config.Templates.Routes)
	fmt.Printf("  测试模板: %s\n", c.config.Templates.Test)
	return nil
}

func (c *CLI) changeTemplateDir() error {
	fmt.Printf("当前模板目录: %s\n", c.config.Templates.TemplateDir)
	fmt.Print("新的模板目录: ")
	if input := c.readInput(); input != "" {
		c.config.Templates.TemplateDir = input
		fmt.Println("✅ 模板目录已更新")
	}
	return nil
}

func (c *CLI) readInput() string {
	c.scanner.Scan()
	return strings.TrimSpace(c.scanner.Text())
}

func (c *CLI) askYesNo(question string) bool {
	if question != "" {
		fmt.Print(question + " (y/n): ")
	}
	input := strings.ToLower(c.readInput())
	return input == "y" || input == "yes"
}
