package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// Loader 配置加载器
type Loader struct {
	configPath string
	config     *Config
}

// NewLoader 创建配置加载器
func NewLoader(configPath string) *Loader {
	return &Loader{
		configPath: configPath,
	}
}

// LoadConfig 加载配置
func (l *Loader) LoadConfig() (*Config, error) {
	if l.config != nil {
		return l.config, nil
	}

	config, err := Load(l.configPath)
	if err != nil {
		return nil, err
	}

	l.config = config
	return config, nil
}

// ReloadConfig 重新加载配置
func (l *Loader) ReloadConfig() (*Config, error) {
	l.config = nil
	return l.LoadConfig()
}

// SaveConfig 保存配置
func (l *Loader) SaveConfig(config *Config) error {
	if err := Save(config, l.configPath); err != nil {
		return err
	}
	l.config = config
	return nil
}

// ValidateConfigFile 验证配置文件
func ValidateConfigFile(configPath string) error {
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("配置文件不存在: %s", configPath)
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("配置文件格式错误: %w", err)
	}

	return config.Validate()
}

// CreateDefaultConfigFile 创建默认配置文件
func CreateDefaultConfigFile(configPath string) error {
	// 检查文件是否已存在
	if _, err := os.Stat(configPath); err == nil {
		return fmt.Errorf("配置文件已存在: %s", configPath)
	}

	// 创建目录
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 创建默认配置
	config := GetDefaultConfig()
	return Save(config, configPath)
}

// MergeConfigs 合并配置
func MergeConfigs(base, override *Config) *Config {
	merged := base.Clone()

	// 合并生成器配置
	if override.Generator.ModelDir != "" {
		merged.Generator.ModelDir = override.Generator.ModelDir
	}
	if len(override.Generator.ExcludeModels) > 0 {
		merged.Generator.ExcludeModels = override.Generator.ExcludeModels
	}
	if len(override.Generator.IncludeModels) > 0 {
		merged.Generator.IncludeModels = override.Generator.IncludeModels
	}
	if override.Generator.PackageName != "" {
		merged.Generator.PackageName = override.Generator.PackageName
	}
	if override.Generator.ModuleName != "" {
		merged.Generator.ModuleName = override.Generator.ModuleName
	}

	// 合并模板配置
	if override.Templates.TemplateDir != "" {
		merged.Templates.TemplateDir = override.Templates.TemplateDir
	}

	// 合并输出配置
	if override.Output.ControllerDir != "" {
		merged.Output.ControllerDir = override.Output.ControllerDir
	}
	if override.Output.ServiceDir != "" {
		merged.Output.ServiceDir = override.Output.ServiceDir
	}
	if override.Output.RepositoryDir != "" {
		merged.Output.RepositoryDir = override.Output.RepositoryDir
	}
	if override.Output.APIDir != "" {
		merged.Output.APIDir = override.Output.APIDir
	}

	return merged
}

// ConfigFromEnv 从环境变量创建配置覆盖
func ConfigFromEnv() *Config {
	config := &Config{}

	// 从环境变量读取配置
	if modelDir := os.Getenv("CRUD_MODEL_DIR"); modelDir != "" {
		config.Generator.ModelDir = modelDir
	}
	if packageName := os.Getenv("CRUD_PACKAGE_NAME"); packageName != "" {
		config.Generator.PackageName = packageName
	}
	if moduleName := os.Getenv("CRUD_MODULE_NAME"); moduleName != "" {
		config.Generator.ModuleName = moduleName
	}
	if templateDir := os.Getenv("CRUD_TEMPLATE_DIR"); templateDir != "" {
		config.Templates.TemplateDir = templateDir
	}
	if outputDir := os.Getenv("CRUD_OUTPUT_DIR"); outputDir != "" {
		config.Output.ControllerDir = filepath.Join(outputDir, "controller")
		config.Output.ServiceDir = filepath.Join(outputDir, "service")
		config.Output.RepositoryDir = filepath.Join(outputDir, "repository")
		config.Output.APIDir = filepath.Join(outputDir, "api", "v1")
	}

	// 解析排除模型列表
	if excludeModels := os.Getenv("CRUD_EXCLUDE_MODELS"); excludeModels != "" {
		config.Generator.ExcludeModels = strings.Split(excludeModels, ",")
		for i := range config.Generator.ExcludeModels {
			config.Generator.ExcludeModels[i] = strings.TrimSpace(config.Generator.ExcludeModels[i])
		}
	}

	// 解析包含模型列表
	if includeModels := os.Getenv("CRUD_INCLUDE_MODELS"); includeModels != "" {
		config.Generator.IncludeModels = strings.Split(includeModels, ",")
		for i := range config.Generator.IncludeModels {
			config.Generator.IncludeModels[i] = strings.TrimSpace(config.Generator.IncludeModels[i])
		}
	}

	return config
}

// LoadWithEnvOverride 加载配置并应用环境变量覆盖
func LoadWithEnvOverride(configPath string) (*Config, error) {
	// 加载基础配置
	baseConfig, err := Load(configPath)
	if err != nil {
		return nil, err
	}

	// 获取环境变量覆盖
	envConfig := ConfigFromEnv()

	// 合并配置
	return MergeConfigs(baseConfig, envConfig), nil
}

// GetConfigPaths 获取配置文件搜索路径
func GetConfigPaths() []string {
	return []string{
		"config/crud_generator.yaml",
		"config/crud_generator.yml",
		"crud_generator.yaml",
		"crud_generator.yml",
		".crud_generator.yaml",
		".crud_generator.yml",
		os.ExpandEnv("$HOME/.crud_generator.yaml"),
		os.ExpandEnv("$HOME/.config/crud_generator.yaml"),
	}
}

// FindConfigFile 查找配置文件
func FindConfigFile() (string, error) {
	paths := GetConfigPaths()

	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	return "", fmt.Errorf("未找到配置文件，搜索路径: %v", paths)
}

// AutoLoadConfig 自动加载配置
func AutoLoadConfig() (*Config, error) {
	// 首先尝试查找配置文件
	configPath, err := FindConfigFile()
	if err != nil {
		// 如果找不到配置文件，使用默认配置
		return GetDefaultConfig(), nil
	}

	// 加载配置文件
	return LoadWithEnvOverride(configPath)
}

// ConfigValidator 配置验证器
type ConfigValidator struct {
	errors []string
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() *ConfigValidator {
	return &ConfigValidator{
		errors: make([]string, 0),
	}
}

// ValidateConfig 验证配置
func (v *ConfigValidator) ValidateConfig(config *Config) error {
	v.errors = make([]string, 0)

	// 验证生成器配置
	v.validateGenerator(&config.Generator)

	// 验证模板配置
	v.validateTemplates(&config.Templates)

	// 验证输出配置
	v.validateOutput(&config.Output)

	// 验证字段映射
	v.validateFieldMappings(config.FieldMappings)

	if len(v.errors) > 0 {
		return fmt.Errorf("配置验证失败:\n%s", strings.Join(v.errors, "\n"))
	}

	return nil
}

func (v *ConfigValidator) validateGenerator(gen *GeneratorConfig) {
	if gen.ModelDir == "" {
		v.addError("generator.model_dir 不能为空")
	} else if _, err := os.Stat(gen.ModelDir); os.IsNotExist(err) {
		v.addError(fmt.Sprintf("generator.model_dir 目录不存在: %s", gen.ModelDir))
	}

	if gen.PackageName == "" {
		v.addError("generator.package_name 不能为空")
	}

	if gen.ModuleName == "" {
		v.addError("generator.module_name 不能为空")
	}

	// 验证至少启用一个生成选项
	if !gen.GenerateController && !gen.GenerateService && !gen.GenerateRepository &&
		!gen.GenerateAPIStructs && !gen.GenerateRoutes {
		v.addError("至少需要启用一个生成选项")
	}
}

func (v *ConfigValidator) validateTemplates(tmpl *TemplateConfig) {
	if tmpl.TemplateDir == "" {
		v.addError("templates.template_dir 不能为空")
	}

	// 验证模板文件是否存在（如果目录存在的话）
	if _, err := os.Stat(tmpl.TemplateDir); err == nil {
		templates := map[string]string{
			"controller": tmpl.Controller,
			"service":    tmpl.Service,
			"repository": tmpl.Repository,
			"api_struct": tmpl.APIStruct,
			"routes":     tmpl.Routes,
		}

		for name, file := range templates {
			if file != "" {
				fullPath := filepath.Join(tmpl.TemplateDir, file)
				if _, err := os.Stat(fullPath); os.IsNotExist(err) {
					v.addError(fmt.Sprintf("模板文件不存在: %s (%s)", fullPath, name))
				}
			}
		}
	}
}

func (v *ConfigValidator) validateOutput(output *OutputConfig) {
	if output.ControllerDir == "" {
		v.addError("output.controller_dir 不能为空")
	}
	if output.ServiceDir == "" {
		v.addError("output.service_dir 不能为空")
	}
	if output.RepositoryDir == "" {
		v.addError("output.repository_dir 不能为空")
	}
	if output.APIDir == "" {
		v.addError("output.api_dir 不能为空")
	}
}

func (v *ConfigValidator) validateFieldMappings(mappings map[string]FieldMapping) {
	for fieldType, mapping := range mappings {
		if mapping.Type == "" {
			v.addError(fmt.Sprintf("字段映射 %s 的 type 不能为空", fieldType))
		}
	}
}

func (v *ConfigValidator) addError(msg string) {
	v.errors = append(v.errors, msg)
}

// GetErrors 获取验证错误
func (v *ConfigValidator) GetErrors() []string {
	return v.errors
}

// HasErrors 是否有错误
func (v *ConfigValidator) HasErrors() bool {
	return len(v.errors) > 0
}
