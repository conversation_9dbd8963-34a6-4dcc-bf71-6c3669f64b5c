package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config CRUD生成器配置
type Config struct {
	Generator     GeneratorConfig              `yaml:"generator"`
	Templates     TemplateConfig               `yaml:"templates"`
	Output        OutputConfig                 `yaml:"output"`
	Features      FeatureConfig                `yaml:"features"`
	CodeStyle     CodeStyleConfig              `yaml:"code_style"`
	FieldMappings map[string]FieldMapping      `yaml:"field_mappings"`
	Validation    ValidationConfig             `yaml:"validation"`
	Database      DatabaseConfig               `yaml:"database"`
}

// GeneratorConfig 生成器配置
type GeneratorConfig struct {
	ModelDir           string   `yaml:"model_dir"`
	ExcludeModels      []string `yaml:"exclude_models"`
	IncludeModels      []string `yaml:"include_models"`
	ExcludeFields      []string `yaml:"exclude_fields"`
	GenerateController bool     `yaml:"generate_controller"`
	GenerateService    bool     `yaml:"generate_service"`
	GenerateRepository bool     `yaml:"generate_repository"`
	GenerateAPIStructs bool     `yaml:"generate_api_structs"`
	GenerateRoutes     bool     `yaml:"generate_routes"`
	GenerateTests      bool     `yaml:"generate_tests"`
	GenerateWireSet    bool     `yaml:"generate_wire_set"`
	PackageName        string   `yaml:"package_name"`
	ModuleName         string   `yaml:"module_name"`
	Author             string   `yaml:"author"`
	Copyright          string   `yaml:"copyright"`
}

// TemplateConfig 模板配置
type TemplateConfig struct {
	TemplateDir     string            `yaml:"template_dir"`
	Controller      string            `yaml:"controller"`
	Service         string            `yaml:"service"`
	Repository      string            `yaml:"repository"`
	APIStruct       string            `yaml:"api_struct"`
	Routes          string            `yaml:"routes"`
	Test            string            `yaml:"test"`
	WireSet         string            `yaml:"wire_set"`
	CustomTemplates map[string]string `yaml:"custom_templates"`
}

// OutputConfig 输出配置
type OutputConfig struct {
	ControllerDir  string `yaml:"controller_dir"`
	ServiceDir     string `yaml:"service_dir"`
	RepositoryDir  string `yaml:"repository_dir"`
	APIDir         string `yaml:"api_dir"`
	RoutesFile     string `yaml:"routes_file"`
	TestDir        string `yaml:"test_dir"`
	WireFile       string `yaml:"wire_file"`
	BackupDir      string `yaml:"backup_dir"`
	FilePermission string `yaml:"file_permission"`
}

// FeatureConfig 功能特性配置
type FeatureConfig struct {
	EnableCache       bool `yaml:"enable_cache"`
	EnableValidation  bool `yaml:"enable_validation"`
	EnableSoftDelete  bool `yaml:"enable_soft_delete"`
	EnableAudit       bool `yaml:"enable_audit"`
	EnableTenant      bool `yaml:"enable_tenant"`
	EnableTree        bool `yaml:"enable_tree"`
	EnableStatus      bool `yaml:"enable_status"`
	EnablePagination  bool `yaml:"enable_pagination"`
	EnableSearch      bool `yaml:"enable_search"`
	EnableSort        bool `yaml:"enable_sort"`
	EnableExport      bool `yaml:"enable_export"`
	EnableImport      bool `yaml:"enable_import"`
	EnableBatch       bool `yaml:"enable_batch"`
	EnableTransaction bool `yaml:"enable_transaction"`
}

// CodeStyleConfig 代码风格配置
type CodeStyleConfig struct {
	Naming     NamingConfig     `yaml:"naming"`
	Comments   CommentsConfig   `yaml:"comments"`
	Formatting FormattingConfig `yaml:"formatting"`
}

// NamingConfig 命名配置
type NamingConfig struct {
	ControllerSuffix  string `yaml:"controller_suffix"`
	ServiceSuffix     string `yaml:"service_suffix"`
	RepositorySuffix  string `yaml:"repository_suffix"`
	InterfaceSuffix   string `yaml:"interface_suffix"`
	TestSuffix        string `yaml:"test_suffix"`
}

// CommentsConfig 注释配置
type CommentsConfig struct {
	Controller string `yaml:"controller"`
	Service    string `yaml:"service"`
	Repository string `yaml:"repository"`
	APIStruct  string `yaml:"api_struct"`
}

// FormattingConfig 格式化配置
type FormattingConfig struct {
	Indent      string `yaml:"indent"`
	LineLength  int    `yaml:"line_length"`
	SortImports bool   `yaml:"sort_imports"`
}

// FieldMapping 字段映射配置
type FieldMapping struct {
	Type         string            `yaml:"type"`
	Validation   string            `yaml:"validation"`
	JSONTag      string            `yaml:"json_tag"`
	GORMTag      string            `yaml:"gorm_tag"`
	IsSearchable bool              `yaml:"is_searchable"`
	IsRequired   bool              `yaml:"is_required"`
	IsSortable   bool              `yaml:"is_sortable"`
	CustomRules  map[string]string `yaml:"custom_rules"`
}

// ValidationConfig 验证配置
type ValidationConfig struct {
	EnableCustomValidators bool     `yaml:"enable_custom_validators"`
	CustomValidators       []string `yaml:"custom_validators"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver           string `yaml:"driver"`
	ConnectionString string `yaml:"connection_string"`
	MaxOpenConns     int    `yaml:"max_open_conns"`
	MaxIdleConns     int    `yaml:"max_idle_conns"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	// 如果配置文件不存在，返回默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("⚠️  配置文件不存在，使用默认配置: %s\n", configPath)
		return GetDefaultConfig(), nil
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	cfg := GetDefaultConfig()
	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return cfg, nil
}

// Save 保存配置文件
func Save(cfg *Config, configPath string) error {
	// 创建目录
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	data, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return &Config{
		Generator: GeneratorConfig{
			ModelDir:           "internal/model",
			ExcludeModels:      []string{"CasbinRule", "Migration"},
			IncludeModels:      []string{},
			ExcludeFields:      []string{"Password", "Salt"},
			GenerateController: true,
			GenerateService:    true,
			GenerateRepository: true,
			GenerateAPIStructs: true,
			GenerateRoutes:     true,
			GenerateTests:      false,
			GenerateWireSet:    true,
			PackageName:        "admin",
			ModuleName:         "admin",
		},
		Templates: TemplateConfig{
			TemplateDir:     "templates/crud",
			Controller:      "controller.tmpl",
			Service:         "service.tmpl",
			Repository:      "repository.tmpl",
			APIStruct:       "api_struct.tmpl",
			Routes:          "routes.tmpl",
			Test:            "test.tmpl",
			WireSet:         "wire_set.tmpl",
			CustomTemplates: make(map[string]string),
		},
		Output: OutputConfig{
			ControllerDir:  "internal/controller",
			ServiceDir:     "internal/service",
			RepositoryDir:  "internal/repository",
			APIDir:         "api/v1",
			RoutesFile:     "internal/server/http_generated.go",
			TestDir:        "test/generated",
			WireFile:       "internal/wire/wire_generated.go",
			BackupDir:      ".crud_backup",
			FilePermission: "0644",
		},
		Features: FeatureConfig{
			EnableCache:       true,
			EnableValidation:  true,
			EnableSoftDelete:  true,
			EnableAudit:       true,
			EnableTenant:      true,
			EnableTree:        true,
			EnableStatus:      true,
			EnablePagination:  true,
			EnableSearch:      true,
			EnableSort:        true,
			EnableExport:      false,
			EnableImport:      false,
			EnableBatch:       true,
			EnableTransaction: true,
		},
		CodeStyle: CodeStyleConfig{
			Naming: NamingConfig{
				ControllerSuffix:  "Controller",
				ServiceSuffix:     "Service",
				RepositorySuffix:  "Repository",
				InterfaceSuffix:   "",
				TestSuffix:        "_test",
			},
			Comments: CommentsConfig{
				Controller: "{{.ModelComment}}控制器",
				Service:    "{{.ModelComment}}服务",
				Repository: "{{.ModelComment}}仓储",
				APIStruct:  "{{.ModelComment}}API结构体",
			},
			Formatting: FormattingConfig{
				Indent:      "\t",
				LineLength:  120,
				SortImports: true,
			},
		},
		FieldMappings: map[string]FieldMapping{
			"string": {
				Validation:   "omitempty,max=255",
				IsSearchable: true,
				IsSortable:   true,
			},
			"int64": {
				Validation:   "omitempty,gte=0",
				IsSearchable: false,
				IsSortable:   true,
			},
			"time.Time": {
				Validation:   "omitempty",
				IsSearchable: false,
				IsSortable:   true,
			},
			"email": {
				Validation:   "omitempty,email",
				IsSearchable: true,
				IsSortable:   false,
			},
			"phone": {
				Validation:   "omitempty,len=11,numeric",
				IsSearchable: true,
				IsSortable:   false,
			},
			"password": {
				Validation:   "required,min=6,max=20",
				JSONTag:      "-",
				IsSearchable: false,
				IsSortable:   false,
			},
		},
		Validation: ValidationConfig{
			EnableCustomValidators: true,
			CustomValidators:       []string{"unique", "exists", "enum"},
		},
		Database: DatabaseConfig{
			Driver:       "mysql",
			MaxOpenConns: 100,
			MaxIdleConns: 10,
		},
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	// 验证必需的目录
	if c.Generator.ModelDir == "" {
		return fmt.Errorf("model_dir 不能为空")
	}

	// 验证模板目录
	if c.Templates.TemplateDir == "" {
		return fmt.Errorf("template_dir 不能为空")
	}

	// 验证输出目录
	if c.Output.ControllerDir == "" {
		return fmt.Errorf("controller_dir 不能为空")
	}
	if c.Output.ServiceDir == "" {
		return fmt.Errorf("service_dir 不能为空")
	}
	if c.Output.RepositoryDir == "" {
		return fmt.Errorf("repository_dir 不能为空")
	}
	if c.Output.APIDir == "" {
		return fmt.Errorf("api_dir 不能为空")
	}

	// 验证包名和模块名
	if c.Generator.PackageName == "" {
		return fmt.Errorf("package_name 不能为空")
	}
	if c.Generator.ModuleName == "" {
		return fmt.Errorf("module_name 不能为空")
	}

	return nil
}

// GetFieldMapping 获取字段映射
func (c *Config) GetFieldMapping(fieldType string) FieldMapping {
	if mapping, exists := c.FieldMappings[fieldType]; exists {
		return mapping
	}
	
	// 返回默认映射
	return FieldMapping{
		Validation:   "omitempty",
		IsSearchable: false,
		IsSortable:   false,
	}
}

// IsModelExcluded 检查模型是否被排除
func (c *Config) IsModelExcluded(modelName string) bool {
	for _, excluded := range c.Generator.ExcludeModels {
		if excluded == modelName {
			return true
		}
	}
	return false
}

// IsModelIncluded 检查模型是否被包含
func (c *Config) IsModelIncluded(modelName string) bool {
	// 如果没有指定包含列表，则包含所有非排除的模型
	if len(c.Generator.IncludeModels) == 0 {
		return !c.IsModelExcluded(modelName)
	}
	
	// 检查是否在包含列表中
	for _, included := range c.Generator.IncludeModels {
		if included == modelName {
			return !c.IsModelExcluded(modelName)
		}
	}
	return false
}

// IsFieldExcluded 检查字段是否被排除
func (c *Config) IsFieldExcluded(fieldName string) bool {
	for _, excluded := range c.Generator.ExcludeFields {
		if excluded == fieldName {
			return true
		}
	}
	return false
}

// GetTemplateFile 获取模板文件路径
func (c *Config) GetTemplateFile(templateType string) string {
	switch templateType {
	case "controller":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.Controller)
	case "service":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.Service)
	case "repository":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.Repository)
	case "api_struct":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.APIStruct)
	case "routes":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.Routes)
	case "test":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.Test)
	case "wire_set":
		return filepath.Join(c.Templates.TemplateDir, c.Templates.WireSet)
	default:
		if customTemplate, exists := c.Templates.CustomTemplates[templateType]; exists {
			return filepath.Join(c.Templates.TemplateDir, customTemplate)
		}
		return ""
	}
}

// GetOutputFile 获取输出文件路径
func (c *Config) GetOutputFile(templateType, modelName string) string {
	fileName := fmt.Sprintf("%s.go", strings.ToLower(modelName))
	
	switch templateType {
	case "controller":
		return filepath.Join(c.Output.ControllerDir, fileName)
	case "service":
		return filepath.Join(c.Output.ServiceDir, fileName)
	case "repository":
		return filepath.Join(c.Output.RepositoryDir, fileName)
	case "api_struct":
		return filepath.Join(c.Output.APIDir, fileName)
	case "test":
		return filepath.Join(c.Output.TestDir, fileName)
	case "routes":
		return c.Output.RoutesFile
	case "wire_set":
		return c.Output.WireFile
	default:
		return ""
	}
}

// Clone 克隆配置
func (c *Config) Clone() *Config {
	data, _ := yaml.Marshal(c)
	var clone Config
	yaml.Unmarshal(data, &clone)
	return &clone
}