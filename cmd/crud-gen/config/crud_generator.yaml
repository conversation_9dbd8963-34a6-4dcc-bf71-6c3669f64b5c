generator:
    model_dir: internal/model
    exclude_models:
        - CasbinRule
        - Migration
    include_models: []
    exclude_fields:
        - Password
        - Salt
    generate_controller: true
    generate_service: true
    generate_repository: true
    generate_api_structs: true
    generate_routes: true
    generate_tests: false
    generate_wire_set: true
    package_name: admin
    module_name: admin
    author: ""
    copyright: ""
templates:
    template_dir: templates/crud
    controller: controller.tmpl
    service: service.tmpl
    repository: repository.tmpl
    api_struct: api_struct.tmpl
    routes: routes.tmpl
    test: test.tmpl
    wire_set: wire_set.tmpl
    custom_templates: {}
output:
    controller_dir: internal/controller
    service_dir: internal/service
    repository_dir: internal/repository
    api_dir: api/v1
    routes_file: internal/server/http_generated.go
    test_dir: test/generated
    wire_file: internal/wire/wire_generated.go
    backup_dir: .crud_backup
    file_permission: "0644"
features:
    enable_cache: true
    enable_validation: true
    enable_soft_delete: true
    enable_audit: true
    enable_tenant: true
    enable_tree: true
    enable_status: true
    enable_pagination: true
    enable_search: true
    enable_sort: true
    enable_export: false
    enable_import: false
    enable_batch: true
    enable_transaction: true
code_style:
    naming:
        controller_suffix: Controller
        service_suffix: Service
        repository_suffix: Repository
        interface_suffix: ""
        test_suffix: _test
    comments:
        controller: '{{.ModelComment}}控制器'
        service: '{{.ModelComment}}服务'
        repository: '{{.ModelComment}}仓储'
        api_struct: '{{.ModelComment}}API结构体'
    formatting:
        indent: "\t"
        line_length: 120
        sort_imports: true
field_mappings:
    email:
        type: ""
        validation: omitempty,email
        json_tag: ""
        gorm_tag: ""
        is_searchable: true
        is_required: false
        is_sortable: false
        custom_rules: {}
    int64:
        type: ""
        validation: omitempty,gte=0
        json_tag: ""
        gorm_tag: ""
        is_searchable: false
        is_required: false
        is_sortable: true
        custom_rules: {}
    password:
        type: ""
        validation: required,min=6,max=20
        json_tag: '-'
        gorm_tag: ""
        is_searchable: false
        is_required: false
        is_sortable: false
        custom_rules: {}
    phone:
        type: ""
        validation: omitempty,len=11,numeric
        json_tag: ""
        gorm_tag: ""
        is_searchable: true
        is_required: false
        is_sortable: false
        custom_rules: {}
    string:
        type: ""
        validation: omitempty,max=255
        json_tag: ""
        gorm_tag: ""
        is_searchable: true
        is_required: false
        is_sortable: true
        custom_rules: {}
    time.Time:
        type: ""
        validation: omitempty
        json_tag: ""
        gorm_tag: ""
        is_searchable: false
        is_required: false
        is_sortable: true
        custom_rules: {}
validation:
    enable_custom_validators: true
    custom_validators:
        - unique
        - exists
        - enum
database:
    driver: mysql
    connection_string: ""
    max_open_conns: 100
    max_idle_conns: 10
