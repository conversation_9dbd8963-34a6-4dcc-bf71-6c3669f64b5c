package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"admin/cmd/crud-gen/config"
	"admin/cmd/crud-gen/generator"
	"admin/cmd/crud-gen/interactive"
)

const (
	Version = "1.0.0"
	Banner  = `
 ██████╗██████╗ ██╗   ██╗██████╗      ██████╗ ███████╗███╗   ██╗
██╔════╝██╔══██╗██║   ██║██╔══██╗    ██╔════╝ ██╔════╝████╗  ██║
██║     ██████╔╝██║   ██║██║  ██║    ██║  ███╗█████╗  ██╔██╗ ██║
██║     ██╔══██╗██║   ██║██║  ██║    ██║   ██║██╔══╝  ██║╚██╗██║
╚██████╗██║  ██║╚██████╔╝██████╔╝    ╚██████╔╝███████╗██║ ╚████║
 ╚═════╝╚═╝  ╚═╝ ╚═════╝ ╚═════╝      ╚═════╝ ╚══════╝╚═╝  ╚═══╝
                                                                  
N-Admin CRUD Generator v%s - 让CRUD开发更简单
`
)

type Options struct {
	ConfigFile  string
	Models      []string
	Layers      []string
	OutputDir   string
	TemplateDir string
	Preview     bool
	Interactive bool
	All         bool
	Force       bool
	Verbose     bool
	DryRun      bool
	Backup      bool
	Init        bool
	Version     bool
	Help        bool
}

func main() {
	opts := parseFlags()

	// 显示版本信息
	if opts.Version {
		fmt.Printf("CRUD Generator v%s\n", Version)
		return
	}

	// 显示帮助信息
	if opts.Help {
		showHelp()
		return
	}

	// 显示Banner
	if !opts.Help && !opts.Version {
		fmt.Printf(Banner, Version)
		fmt.Println()
	}

	// 初始化配置文件
	if opts.Init {
		if err := initConfigFile(opts.ConfigFile); err != nil {
			log.Fatalf("❌ 初始化配置文件失败: %v", err)
		}
		fmt.Printf("✅ 配置文件已创建: %s\n", opts.ConfigFile)
		return
	}

	// 验证参数
	if err := validateOptions(opts); err != nil {
		log.Fatalf("❌ 参数验证失败: %v", err)
	}

	// 加载配置
	cfg, err := config.Load(opts.ConfigFile)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 应用命令行参数覆盖配置
	applyOptionsToConfig(cfg, opts)

	if opts.Verbose {
		fmt.Printf("📋 使用配置文件: %s\n", opts.ConfigFile)
		fmt.Printf("📁 模型目录: %s\n", cfg.Generator.ModelDir)
	}

	// 创建生成器
	gen, err := generator.New(cfg)
	if err != nil {
		log.Fatalf("❌ 创建生成器失败: %v", err)
	}

	// 交互式模式
	if opts.Interactive {
		cli := interactive.NewCLI(gen, cfg)
		if err := cli.Run(); err != nil {
			log.Fatalf("交互式模式失败: %v", err)
		}
		return
	}

	// 执行生成
	if err := runGeneration(gen, opts); err != nil {
		log.Fatalf("生成失败: %v", err)
	}
}

func parseFlags() *Options {
	opts := &Options{}

	var modelsFlag, layersFlag string

	flag.StringVar(&opts.ConfigFile, "config", "config/crud_generator.yaml", "配置文件路径")
	flag.StringVar(&modelsFlag, "models", "", "要生成的模型列表，逗号分隔")
	flag.StringVar(&layersFlag, "layers", "", "要生成的层级，逗号分隔 (controller,service,repository,api,routes)")
	flag.StringVar(&opts.OutputDir, "output", "", "输出目录")
	flag.StringVar(&opts.TemplateDir, "template-dir", "", "自定义模板目录")
	flag.BoolVar(&opts.Preview, "preview", false, "预览模式，不实际生成文件")
	flag.BoolVar(&opts.Interactive, "interactive", false, "交互式模式")
	flag.BoolVar(&opts.All, "all", false, "生成所有模型")
	flag.BoolVar(&opts.Force, "force", false, "强制覆盖已存在的文件")
	flag.BoolVar(&opts.Verbose, "verbose", false, "详细输出")
	flag.BoolVar(&opts.DryRun, "dry-run", false, "模拟运行，显示将要生成的文件")
	flag.BoolVar(&opts.Backup, "backup", false, "生成前备份现有文件")
	flag.BoolVar(&opts.Init, "init", false, "初始化配置文件")
	flag.BoolVar(&opts.Version, "version", false, "显示版本信息")
	flag.BoolVar(&opts.Help, "help", false, "显示帮助信息")

	flag.Parse()

	// 解析模型和层级列表
	if modelsFlag != "" {
		opts.Models = strings.Split(modelsFlag, ",")
		for i, model := range opts.Models {
			opts.Models[i] = strings.TrimSpace(model)
		}
	}
	if layersFlag != "" {
		opts.Layers = strings.Split(layersFlag, ",")
		for i, layer := range opts.Layers {
			opts.Layers[i] = strings.TrimSpace(layer)
		}
	}

	return opts
}

func validateOptions(opts *Options) error {
	// 检查是否指定了模型
	if !opts.All && len(opts.Models) == 0 && !opts.Interactive {
		return fmt.Errorf("必须指定要生成的模型 (使用 --models 或 --all 或 --interactive)")
	}

	// 验证层级参数
	validLayers := map[string]bool{
		"controller": true,
		"service":    true,
		"repository": true,
		"api":        true,
		"routes":     true,
		"test":       true,
	}

	for _, layer := range opts.Layers {
		if !validLayers[layer] {
			return fmt.Errorf("无效的层级: %s，有效值: controller,service,repository,api,routes,test", layer)
		}
	}

	return nil
}

func applyOptionsToConfig(cfg *config.Config, opts *Options) {
	// 应用输出目录覆盖
	if opts.OutputDir != "" {
		cfg.Output.ControllerDir = opts.OutputDir + "/controller"
		cfg.Output.ServiceDir = opts.OutputDir + "/service"
		cfg.Output.RepositoryDir = opts.OutputDir + "/repository"
		cfg.Output.APIDir = opts.OutputDir + "/api/v1"
	}

	// 应用模板目录覆盖
	if opts.TemplateDir != "" {
		cfg.Templates.TemplateDir = opts.TemplateDir
	}

	// 应用层级选择
	if len(opts.Layers) > 0 {
		// 重置所有层级生成标志
		cfg.Generator.GenerateController = false
		cfg.Generator.GenerateService = false
		cfg.Generator.GenerateRepository = false
		cfg.Generator.GenerateAPIStructs = false
		cfg.Generator.GenerateRoutes = false
		cfg.Generator.GenerateTests = false

		// 根据指定的层级设置标志
		for _, layer := range opts.Layers {
			switch strings.ToLower(layer) {
			case "controller":
				cfg.Generator.GenerateController = true
			case "service":
				cfg.Generator.GenerateService = true
			case "repository":
				cfg.Generator.GenerateRepository = true
			case "api":
				cfg.Generator.GenerateAPIStructs = true
			case "routes":
				cfg.Generator.GenerateRoutes = true
			case "test":
				cfg.Generator.GenerateTests = true
			}
		}
	}

	// 应用模型选择
	if len(opts.Models) > 0 {
		cfg.Generator.IncludeModels = opts.Models
	}
}

func initConfigFile(configPath string) error {
	// 检查文件是否已存在
	if _, err := os.Stat(configPath); err == nil {
		return fmt.Errorf("配置文件已存在: %s", configPath)
	}

	// 创建目录
	if err := os.MkdirAll(strings.TrimSuffix(configPath, "/crud_generator.yaml"), 0o755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 创建默认配置
	defaultConfig := config.GetDefaultConfig()
	return config.Save(defaultConfig, configPath)
}

func runGeneration(gen *generator.Generator, opts *Options) error {
	// 分析模型
	models, err := gen.AnalyzeModels()
	if err != nil {
		return fmt.Errorf("分析模型失败: %w", err)
	}

	if len(models) == 0 {
		fmt.Println("⚠️  没有找到可生成的模型")
		return nil
	}

	// 过滤模型
	if !opts.All && len(opts.Models) > 0 {
		var filteredModels []generator.ModelInfo
		for _, model := range models {
			for _, targetModel := range opts.Models {
				if model.Name == targetModel {
					filteredModels = append(filteredModels, model)
					break
				}
			}
		}
		models = filteredModels
	}

	if len(models) == 0 {
		fmt.Println("⚠️  没有匹配的模型需要生成")
		return nil
	}

	// 预览模式
	if opts.Preview || opts.DryRun {
		fmt.Println("🔍 预览模式 - 将要生成的文件:")
		for _, model := range models {
			fmt.Printf("\n📁 模型: %s\n", model.Name)
			files := gen.GetGeneratedFiles(model)
			for _, file := range files {
				fmt.Printf("  📄 %s\n", file)
			}
		}
		return nil
	}

	// 执行生成
	genOpts := generator.GenerateOptions{
		Force:   opts.Force,
		Verbose: opts.Verbose,
		DryRun:  opts.DryRun,
		Backup:  opts.Backup,
	}

	result, err := gen.Generate(models, genOpts)
	if err != nil {
		return fmt.Errorf("生成代码失败: %w", err)
	}

	// 显示结果
	showGenerationResult(result, result.Duration, opts.Verbose)
	return nil
}

func showGenerationResult(result *generator.GenerationResult, duration time.Duration, verbose bool) {
	fmt.Println("🎯 CRUD 代码生成完成！")
	fmt.Println("==========================================")
	fmt.Printf("📊 生成统计：\n")
	fmt.Printf("  - 模型数量: %d\n", result.ModelCount)

	if result.ControllerCount > 0 {
		fmt.Printf("  - Controller: %d 个文件\n", result.ControllerCount)
	}
	if result.ServiceCount > 0 {
		fmt.Printf("  - Service: %d 个文件\n", result.ServiceCount)
	}
	if result.RepositoryCount > 0 {
		fmt.Printf("  - Repository: %d 个文件\n", result.RepositoryCount)
	}
	if result.APICount > 0 {
		fmt.Printf("  - API 结构体: %d 个文件\n", result.APICount)
	}
	if result.RouteCount > 0 {
		fmt.Printf("  - 路由配置: %d 个文件\n", result.RouteCount)
	}
	if result.TestCount > 0 {
		fmt.Printf("  - 测试文件: %d 个文件\n", result.TestCount)
	}

	fmt.Printf("\n⏱️  总耗时: %.2fs\n", duration.Seconds())
	fmt.Printf("💾 总文件大小: %.1fKB\n", float64(result.TotalSize)/1024)

	if len(result.Errors) > 0 {
		fmt.Printf("⚠️  警告: %d 个错误\n", len(result.Errors))
		if verbose {
			for _, err := range result.Errors {
				fmt.Printf("   - %s\n", err)
			}
		}
	} else {
		fmt.Println("🎉 所有文件生成成功！")
	}

	if verbose && len(result.GeneratedFiles) > 0 {
		fmt.Printf("\n📋 生成的文件列表：\n")
		for _, file := range result.GeneratedFiles {
			fmt.Printf("   ✅ %s\n", file)
		}
	}

	// 显示功能特性
	if len(result.Features) > 0 {
		fmt.Printf("\n📋 生成的功能特性：\n")
		for _, feature := range result.Features {
			fmt.Printf("  ✅ %s\n", feature)
		}
	}
}

func showHelp() {
	fmt.Printf(Banner, Version)
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  crud-gen [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  --config string        配置文件路径 (默认: config/crud_generator.yaml)")
	fmt.Println("  --models string        要生成的模型列表，逗号分隔")
	fmt.Println("  --layers string        要生成的层级，逗号分隔 (controller,service,repository,api,routes)")
	fmt.Println("  --output string        输出目录")
	fmt.Println("  --template-dir string  自定义模板目录")
	fmt.Println("  --preview              预览模式，不实际生成文件")
	fmt.Println("  --interactive          交互式模式")
	fmt.Println("  --all                  生成所有模型")
	fmt.Println("  --force                强制覆盖已存在的文件")
	fmt.Println("  --verbose              详细输出")
	fmt.Println("  --dry-run              模拟运行，显示将要生成的文件")
	fmt.Println("  --backup               生成前备份现有文件")
	fmt.Println("  --init                 初始化配置文件")
	fmt.Println("  --version              显示版本信息")
	fmt.Println("  --help                 显示帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 初始化配置文件")
	fmt.Println("  crud-gen --init")
	fmt.Println()
	fmt.Println("  # 生成所有模型的CRUD代码")
	fmt.Println("  crud-gen --all")
	fmt.Println()
	fmt.Println("  # 生成指定模型")
	fmt.Println("  crud-gen --models=User,Post,Category")
	fmt.Println()
	fmt.Println("  # 只生成Controller和Service层")
	fmt.Println("  crud-gen --models=User --layers=controller,service")
	fmt.Println()
	fmt.Println("  # 预览生成结果")
	fmt.Println("  crud-gen --models=User --preview")
	fmt.Println()
	fmt.Println("  # 交互式生成")
	fmt.Println("  crud-gen --interactive")
}
