package utils

import (
	"regexp"
	"strings"
	"unicode"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

// ToSnakeCase 转换为蛇形命名
func ToSnakeCase(s string) string {
	// 处理连续的大写字母
	re1 := regexp.MustCompile(`([A-Z]+)([A-Z][a-z])`)
	s = re1.ReplaceAllString(s, "${1}_${2}")

	// 处理小写字母后跟大写字母
	re2 := regexp.MustCompile(`([a-z\d])([A-Z])`)
	s = re2.ReplaceAllString(s, "${1}_${2}")

	return strings.ToLower(s)
}

// ToCamelCase 转换为驼峰命名（首字母小写）
func ToCamelCase(s string) string {
	if s == "" {
		return s
	}

	// 如果是蛇形命名，先转换
	if strings.Contains(s, "_") {
		parts := strings.Split(s, "_")
		result := strings.ToLower(parts[0])
		titleCaser := cases.Title(language.Und)
		for i := 1; i < len(parts); i++ {
			if parts[i] != "" {
				result += titleCaser.String(strings.ToLower(parts[i]))
			}
		}
		return result
	}

	// 如果已经是驼峰命名，确保首字母小写
	runes := []rune(s)
	runes[0] = unicode.ToLower(runes[0])
	return string(runes)
}

// ToPascalCase 转换为帕斯卡命名（首字母大写）
func ToPascalCase(s string) string {
	if s == "" {
		return s
	}

	// 如果是蛇形命名，先转换
	if strings.Contains(s, "_") {
		parts := strings.Split(s, "_")
		titleCaser := cases.Title(language.Und)
		var result strings.Builder
		for _, part := range parts {
			if part != "" {
				result.WriteString(titleCaser.String(strings.ToLower(part)))
			}
		}
		return result.String()
	}

	// 如果已经是驼峰命名，确保首字母大写
	runes := []rune(s)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// ToKebabCase 转换为短横线命名
func ToKebabCase(s string) string {
	snake := ToSnakeCase(s)
	return strings.ReplaceAll(snake, "_", "-")
}

// Pluralize 转换为复数形式
func Pluralize(s string) string {
	if s == "" {
		return s
	}

	s = strings.ToLower(s)

	// 特殊复数形式
	irregulars := map[string]string{
		"person": "people",
		"man":    "men",
		"woman":  "women",
		"child":  "children",
		"tooth":  "teeth",
		"foot":   "feet",
		"mouse":  "mice",
		"goose":  "geese",
	}

	if plural, exists := irregulars[s]; exists {
		return plural
	}

	// 规则变化
	if strings.HasSuffix(s, "y") && len(s) > 1 {
		// 辅音字母 + y 结尾，变 y 为 ies
		if !isVowel(rune(s[len(s)-2])) {
			return s[:len(s)-1] + "ies"
		}
	}

	if strings.HasSuffix(s, "s") || strings.HasSuffix(s, "sh") ||
		strings.HasSuffix(s, "ch") || strings.HasSuffix(s, "x") ||
		strings.HasSuffix(s, "z") {
		return s + "es"
	}

	if strings.HasSuffix(s, "f") {
		return s[:len(s)-1] + "ves"
	}

	if strings.HasSuffix(s, "fe") {
		return s[:len(s)-2] + "ves"
	}

	if strings.HasSuffix(s, "o") && len(s) > 1 {
		// 辅音字母 + o 结尾，加 es
		if !isVowel(rune(s[len(s)-2])) {
			return s + "es"
		}
	}

	// 默认加 s
	return s + "s"
}

// Singularize 转换为单数形式
func Singularize(s string) string {
	if s == "" {
		return s
	}

	s = strings.ToLower(s)

	// 特殊单数形式
	irregulars := map[string]string{
		"people":   "person",
		"men":      "man",
		"women":    "woman",
		"children": "child",
		"teeth":    "tooth",
		"feet":     "foot",
		"mice":     "mouse",
		"geese":    "goose",
	}

	if singular, exists := irregulars[s]; exists {
		return singular
	}

	// 规则变化
	if strings.HasSuffix(s, "ies") && len(s) > 3 {
		return s[:len(s)-3] + "y"
	}

	if strings.HasSuffix(s, "ves") && len(s) > 3 {
		if strings.HasSuffix(s, "ives") {
			return s[:len(s)-3] + "fe"
		}
		return s[:len(s)-3] + "f"
	}

	if strings.HasSuffix(s, "es") && len(s) > 2 {
		base := s[:len(s)-2]
		if strings.HasSuffix(base, "s") || strings.HasSuffix(base, "sh") ||
			strings.HasSuffix(base, "ch") || strings.HasSuffix(base, "x") ||
			strings.HasSuffix(base, "z") {
			return base
		}
		if strings.HasSuffix(base, "o") && len(base) > 1 && !isVowel(rune(base[len(base)-2])) {
			return base
		}
	}

	if strings.HasSuffix(s, "s") && len(s) > 1 {
		return s[:len(s)-1]
	}

	return s
}

// isVowel 判断是否为元音字母
func isVowel(r rune) bool {
	vowels := "aeiouAEIOU"
	return strings.ContainsRune(vowels, r)
}

// Title 首字母大写
func Title(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(s)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// Lower 转换为小写
func Lower(s string) string {
	return strings.ToLower(s)
}

// Upper 转换为大写
func Upper(s string) string {
	return strings.ToUpper(s)
}

// GetControllerName 获取控制器名称
func GetControllerName(modelName, suffix string) string {
	return ToPascalCase(modelName) + suffix
}

// GetServiceName 获取服务名称
func GetServiceName(modelName, suffix string) string {
	return ToPascalCase(modelName) + suffix
}

// GetRepositoryName 获取仓储名称
func GetRepositoryName(modelName, suffix string) string {
	return ToPascalCase(modelName) + suffix
}

// GetInterfaceName 获取接口名称
func GetInterfaceName(modelName, suffix string) string {
	name := ToPascalCase(modelName)
	if suffix != "" {
		name += suffix
	}
	return name
}

// GetVariableName 获取变量名称
func GetVariableName(modelName string) string {
	return ToCamelCase(modelName)
}

// GetPackageName 获取包名称
func GetPackageName(s string) string {
	return ToSnakeCase(s)
}

// GetFileName 获取文件名称
func GetFileName(modelName string) string {
	return ToSnakeCase(modelName) + ".go"
}

// GetTestFileName 获取测试文件名称
func GetTestFileName(modelName string) string {
	return ToSnakeCase(modelName) + "_test.go"
}

// GetTableName 获取表名称
func GetTableName(modelName string) string {
	return ToSnakeCase(modelName)
}

// GetRouteGroup 获取路由组名称
func GetRouteGroup(modelName string) string {
	return ToSnakeCase(modelName)
}

// GetAPIPath 获取API路径
func GetAPIPath(modelName string) string {
	return "/" + ToSnakeCase(modelName)
}

// GetConstantName 获取常量名称
func GetConstantName(s string) string {
	return strings.ToUpper(ToSnakeCase(s))
}

// SplitCamelCase 分割驼峰命名
func SplitCamelCase(s string) []string {
	var result []string
	var current strings.Builder

	for i, r := range s {
		if i > 0 && unicode.IsUpper(r) {
			if current.Len() > 0 {
				result = append(result, current.String())
				current.Reset()
			}
		}
		current.WriteRune(r)
	}

	if current.Len() > 0 {
		result = append(result, current.String())
	}

	return result
}

// JoinWords 连接单词
func JoinWords(words []string, separator string) string {
	return strings.Join(words, separator)
}

// ValidateIdentifier 验证标识符是否有效
func ValidateIdentifier(s string) bool {
	if s == "" {
		return false
	}

	// 第一个字符必须是字母或下划线
	first := rune(s[0])
	if !unicode.IsLetter(first) && first != '_' {
		return false
	}

	// 其他字符必须是字母、数字或下划线
	for _, r := range s[1:] {
		if !unicode.IsLetter(r) && !unicode.IsDigit(r) && r != '_' {
			return false
		}
	}

	return true
}

// IsGoKeyword 检查是否为Go关键字
func IsGoKeyword(s string) bool {
	keywords := map[string]bool{
		"break": true, "case": true, "chan": true, "const": true, "continue": true,
		"default": true, "defer": true, "else": true, "fallthrough": true, "for": true,
		"func": true, "go": true, "goto": true, "if": true, "import": true,
		"interface": true, "map": true, "package": true, "range": true, "return": true,
		"select": true, "struct": true, "switch": true, "type": true, "var": true,
	}
	return keywords[s]
}

// EscapeKeyword 转义Go关键字
func EscapeKeyword(s string) string {
	if IsGoKeyword(s) {
		return s + "_"
	}
	return s
}

// GenerateGetterName 生成Getter方法名
func GenerateGetterName(fieldName string) string {
	return "Get" + ToPascalCase(fieldName)
}

// GenerateSetterName 生成Setter方法名
func GenerateSetterName(fieldName string) string {
	return "Set" + ToPascalCase(fieldName)
}

// GenerateValidatorName 生成验证器方法名
func GenerateValidatorName(fieldName string) string {
	return "Validate" + ToPascalCase(fieldName)
}

// GenerateBuilderName 生成构建器方法名
func GenerateBuilderName(fieldName string) string {
	return "With" + ToPascalCase(fieldName)
}
