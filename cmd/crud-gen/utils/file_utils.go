package utils

import (
	"fmt"
	"go/format"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// FileUtils 文件工具
type FileUtils struct{}

// NewFileUtils 创建文件工具
func NewFileUtils() *FileUtils {
	return &FileUtils{}
}

// EnsureDir 确保目录存在
func (fu *FileUtils) EnsureDir(dir string) error {
	if dir == "" {
		return fmt.Errorf("目录路径不能为空")
	}
	
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败 %s: %w", dir, err)
		}
	}
	
	return nil
}

// FileExists 检查文件是否存在
func (fu *FileUtils) FileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// DirExists 检查目录是否存在
func (fu *FileUtils) DirExists(path string) bool {
	info, err := os.Stat(path)
	return err == nil && info.IsDir()
}

// WriteFile 写入文件
func (fu *FileUtils) WriteFile(path, content string, perm fs.FileMode) error {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := fu.EnsureDir(dir); err != nil {
		return err
	}
	
	return os.WriteFile(path, []byte(content), perm)
}

// ReadFile 读取文件
func (fu *FileUtils) ReadFile(path string) (string, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// CopyFile 复制文件
func (fu *FileUtils) CopyFile(src, dst string) error {
	data, err := os.ReadFile(src)
	if err != nil {
		return fmt.Errorf("读取源文件失败 %s: %w", src, err)
	}
	
	// 确保目标目录存在
	dir := filepath.Dir(dst)
	if err := fu.EnsureDir(dir); err != nil {
		return err
	}
	
	if err := os.WriteFile(dst, data, 0644); err != nil {
		return fmt.Errorf("写入目标文件失败 %s: %w", dst, err)
	}
	
	return nil
}

// BackupFile 备份文件
func (fu *FileUtils) BackupFile(path, backupDir string) error {
	if !fu.FileExists(path) {
		return nil // 文件不存在，无需备份
	}
	
	// 确保备份目录存在
	if err := fu.EnsureDir(backupDir); err != nil {
		return err
	}
	
	// 生成备份文件名
	filename := filepath.Base(path)
	timestamp := time.Now().Format("20060102_150405")
	backupName := fmt.Sprintf("%s.%s.bak", filename, timestamp)
	backupPath := filepath.Join(backupDir, backupName)
	
	return fu.CopyFile(path, backupPath)
}

// DeleteFile 删除文件
func (fu *FileUtils) DeleteFile(path string) error {
	if !fu.FileExists(path) {
		return nil // 文件不存在，无需删除
	}
	
	return os.Remove(path)
}

// GetFileSize 获取文件大小
func (fu *FileUtils) GetFileSize(path string) (int64, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// GetFileModTime 获取文件修改时间
func (fu *FileUtils) GetFileModTime(path string) (time.Time, error) {
	info, err := os.Stat(path)
	if err != nil {
		return time.Time{}, err
	}
	return info.ModTime(), nil
}

// ListFiles 列出目录下的文件
func (fu *FileUtils) ListFiles(dir string, pattern string) ([]string, error) {
	var files []string
	
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if info.IsDir() {
			return nil
		}
		
		if pattern == "" {
			files = append(files, path)
			return nil
		}
		
		matched, err := filepath.Match(pattern, info.Name())
		if err != nil {
			return err
		}
		
		if matched {
			files = append(files, path)
		}
		
		return nil
	})
	
	return files, err
}

// FindGoFiles 查找Go文件
func (fu *FileUtils) FindGoFiles(dir string) ([]string, error) {
	return fu.ListFiles(dir, "*.go")
}

// FindGenFiles 查找生成的文件
func (fu *FileUtils) FindGenFiles(dir string) ([]string, error) {
	return fu.ListFiles(dir, "*.gen.go")
}

// FormatGoCode 格式化Go代码
func (fu *FileUtils) FormatGoCode(code string) (string, error) {
	formatted, err := format.Source([]byte(code))
	if err != nil {
		return "", fmt.Errorf("格式化Go代码失败: %w", err)
	}
	return string(formatted), nil
}

// ValidateGoCode 验证Go代码语法
func (fu *FileUtils) ValidateGoCode(code string) error {
	_, err := format.Source([]byte(code))
	return err
}

// GetRelativePath 获取相对路径
func (fu *FileUtils) GetRelativePath(basePath, targetPath string) (string, error) {
	return filepath.Rel(basePath, targetPath)
}

// GetAbsolutePath 获取绝对路径
func (fu *FileUtils) GetAbsolutePath(path string) (string, error) {
	return filepath.Abs(path)
}

// JoinPath 连接路径
func (fu *FileUtils) JoinPath(paths ...string) string {
	return filepath.Join(paths...)
}

// CleanPath 清理路径
func (fu *FileUtils) CleanPath(path string) string {
	return filepath.Clean(path)
}

// SplitPath 分割路径
func (fu *FileUtils) SplitPath(path string) (dir, file string) {
	return filepath.Split(path)
}

// GetFileExt 获取文件扩展名
func (fu *FileUtils) GetFileExt(path string) string {
	return filepath.Ext(path)
}

// GetFileName 获取文件名（不含扩展名）
func (fu *FileUtils) GetFileName(path string) string {
	base := filepath.Base(path)
	ext := filepath.Ext(base)
	return strings.TrimSuffix(base, ext)
}

// ReplaceExt 替换文件扩展名
func (fu *FileUtils) ReplaceExt(path, newExt string) string {
	dir := filepath.Dir(path)
	name := fu.GetFileName(path)
	return filepath.Join(dir, name+newExt)
}

// CreateTempFile 创建临时文件
func (fu *FileUtils) CreateTempFile(dir, pattern string) (*os.File, error) {
	return os.CreateTemp(dir, pattern)
}

// CreateTempDir 创建临时目录
func (fu *FileUtils) CreateTempDir(dir, pattern string) (string, error) {
	return os.MkdirTemp(dir, pattern)
}

// RemoveAll 删除目录及其内容
func (fu *FileUtils) RemoveAll(path string) error {
	return os.RemoveAll(path)
}

// IsEmptyDir 检查目录是否为空
func (fu *FileUtils) IsEmptyDir(dir string) (bool, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return false, err
	}
	return len(entries) == 0, nil
}

// CountFiles 统计目录下文件数量
func (fu *FileUtils) CountFiles(dir string, pattern string) (int, error) {
	files, err := fu.ListFiles(dir, pattern)
	if err != nil {
		return 0, err
	}
	return len(files), nil
}

// GetDirSize 获取目录大小
func (fu *FileUtils) GetDirSize(dir string) (int64, error) {
	var size int64
	
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})
	
	return size, err
}

// CompareFiles 比较两个文件是否相同
func (fu *FileUtils) CompareFiles(file1, file2 string) (bool, error) {
	data1, err := os.ReadFile(file1)
	if err != nil {
		return false, err
	}
	
	data2, err := os.ReadFile(file2)
	if err != nil {
		return false, err
	}
	
	return string(data1) == string(data2), nil
}

// MoveFile 移动文件
func (fu *FileUtils) MoveFile(src, dst string) error {
	// 确保目标目录存在
	dir := filepath.Dir(dst)
	if err := fu.EnsureDir(dir); err != nil {
		return err
	}
	
	return os.Rename(src, dst)
}

// CreateSymlink 创建符号链接
func (fu *FileUtils) CreateSymlink(oldname, newname string) error {
	// 确保目标目录存在
	dir := filepath.Dir(newname)
	if err := fu.EnsureDir(dir); err != nil {
		return err
	}
	
	return os.Symlink(oldname, newname)
}

// IsSymlink 检查是否为符号链接
func (fu *FileUtils) IsSymlink(path string) (bool, error) {
	info, err := os.Lstat(path)
	if err != nil {
		return false, err
	}
	return info.Mode()&os.ModeSymlink != 0, nil
}

// ReadSymlink 读取符号链接目标
func (fu *FileUtils) ReadSymlink(path string) (string, error) {
	return os.Readlink(path)
}

// WalkDir 遍历目录
func (fu *FileUtils) WalkDir(root string, fn func(path string, info os.FileInfo, err error) error) error {
	return filepath.Walk(root, fn)
}

// FindFilesByName 按名称查找文件
func (fu *FileUtils) FindFilesByName(root, name string) ([]string, error) {
	var files []string
	
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if info.Name() == name {
			files = append(files, path)
		}
		
		return nil
	})
	
	return files, err
}

// FindFilesByPattern 按模式查找文件
func (fu *FileUtils) FindFilesByPattern(root, pattern string) ([]string, error) {
	var files []string
	
	err := filepath.Walk(root, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if info.IsDir() {
			return nil
		}
		
		matched, err := filepath.Match(pattern, info.Name())
		if err != nil {
			return err
		}
		
		if matched {
			files = append(files, path)
		}
		
		return nil
	})
	
	return files, err
}

// GetFilePermissions 获取文件权限
func (fu *FileUtils) GetFilePermissions(path string) (fs.FileMode, error) {
	info, err := os.Stat(path)
	if err != nil {
		return 0, err
	}
	return info.Mode(), nil
}

// SetFilePermissions 设置文件权限
func (fu *FileUtils) SetFilePermissions(path string, mode fs.FileMode) error {
	return os.Chmod(path, mode)
}

// TouchFile 创建空文件或更新时间戳
func (fu *FileUtils) TouchFile(path string) error {
	// 如果文件不存在，创建空文件
	if !fu.FileExists(path) {
		// 确保目录存在
		dir := filepath.Dir(path)
		if err := fu.EnsureDir(dir); err != nil {
			return err
		}
		
		file, err := os.Create(path)
		if err != nil {
			return err
		}
		return file.Close()
	}
	
	// 如果文件存在，更新时间戳
	now := time.Now()
	return os.Chtimes(path, now, now)
}

// AppendToFile 追加内容到文件
func (fu *FileUtils) AppendToFile(path, content string) error {
	file, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()
	
	_, err = file.WriteString(content)
	return err
}

// PrependToFile 在文件开头插入内容
func (fu *FileUtils) PrependToFile(path, content string) error {
	// 读取现有内容
	existingContent := ""
	if fu.FileExists(path) {
		data, err := os.ReadFile(path)
		if err != nil {
			return err
		}
		existingContent = string(data)
	}
	
	// 写入新内容
	newContent := content + existingContent
	return fu.WriteFile(path, newContent, 0644)
}

// ReplaceInFile 替换文件中的内容
func (fu *FileUtils) ReplaceInFile(path, old, new string) error {
	content, err := fu.ReadFile(path)
	if err != nil {
		return err
	}
	
	newContent := strings.ReplaceAll(content, old, new)
	return fu.WriteFile(path, newContent, 0644)
}

// LineCount 统计文件行数
func (fu *FileUtils) LineCount(path string) (int, error) {
	content, err := fu.ReadFile(path)
	if err != nil {
		return 0, err
	}
	
	lines := strings.Split(content, "\n")
	return len(lines), nil
}