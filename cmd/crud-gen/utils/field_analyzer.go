package utils

import (
	"fmt"
	"go/ast"
	"reflect"
	"strings"
)

// FieldAnalyzer 字段分析器
type FieldAnalyzer struct {
	fieldMappings map[string]FieldMapping
}

// FieldMapping 字段映射
type FieldMapping struct {
	Type         string
	Validation   string
	JSONTag      string
	GORMTag      string
	IsSearchable bool
	IsRequired   bool
	IsSortable   bool
	IsFilterable bool
	CustomRules  map[string]string
}

// NewFieldAnalyzer 创建字段分析器
func NewFieldAnalyzer(fieldMappings map[string]FieldMapping) *FieldAnalyzer {
	return &FieldAnalyzer{
		fieldMappings: fieldMappings,
	}
}

// AnalyzeFieldType 分析字段类型
func (fa *FieldAnalyzer) AnalyzeFieldType(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.StarExpr:
		return "*" + fa.AnalyzeFieldType(t.X)
	case *ast.ArrayType:
		return "[]" + fa.AnalyzeFieldType(t.Elt)
	case *ast.SelectorExpr:
		pkg := fa.AnalyzeFieldType(t.X)
		return pkg + "." + t.Sel.Name
	case *ast.MapType:
		key := fa.AnalyzeFieldType(t.Key)
		value := fa.AnalyzeFieldType(t.Value)
		return "map[" + key + "]" + value
	case *ast.InterfaceType:
		return "interface{}"
	case *ast.StructType:
		return "struct{}"
	case *ast.FuncType:
		return "func"
	case *ast.ChanType:
		return "chan"
	default:
		return "unknown"
	}
}

// IsPointerType 判断是否为指针类型
func (fa *FieldAnalyzer) IsPointerType(expr ast.Expr) bool {
	_, ok := expr.(*ast.StarExpr)
	return ok
}

// IsSliceType 判断是否为切片类型
func (fa *FieldAnalyzer) IsSliceType(expr ast.Expr) bool {
	_, ok := expr.(*ast.ArrayType)
	return ok
}

// IsMapType 判断是否为映射类型
func (fa *FieldAnalyzer) IsMapType(expr ast.Expr) bool {
	_, ok := expr.(*ast.MapType)
	return ok
}

// ExtractTag 提取标签
func (fa *FieldAnalyzer) ExtractTag(tag, key string) string {
	if tag == "" {
		return ""
	}
	
	// 移除反引号
	tag = strings.Trim(tag, "`")
	
	// 使用反射解析标签
	structTag := reflect.StructTag(tag)
	return structTag.Get(key)
}

// ParseGORMTag 解析GORM标签
func (fa *FieldAnalyzer) ParseGORMTag(tag string) map[string]string {
	result := make(map[string]string)
	if tag == "" {
		return result
	}
	
	// 分割标签选项
	options := strings.Split(tag, ";")
	for _, option := range options {
		option = strings.TrimSpace(option)
		if option == "" {
			continue
		}
		
		// 解析键值对
		if strings.Contains(option, ":") {
			parts := strings.SplitN(option, ":", 2)
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			result[key] = value
		} else {
			// 布尔选项
			result[option] = "true"
		}
	}
	
	return result
}

// ParseJSONTag 解析JSON标签
func (fa *FieldAnalyzer) ParseJSONTag(tag string) (name string, omitempty bool, ignore bool) {
	if tag == "" {
		return "", false, false
	}
	
	if tag == "-" {
		return "", false, true
	}
	
	parts := strings.Split(tag, ",")
	name = parts[0]
	
	for i := 1; i < len(parts); i++ {
		if parts[i] == "omitempty" {
			omitempty = true
		}
	}
	
	return name, omitempty, false
}

// IsSearchableField 判断字段是否可搜索
func (fa *FieldAnalyzer) IsSearchableField(fieldName, fieldType string, gormTag string) bool {
	// 检查字段映射配置
	if mapping, exists := fa.fieldMappings[strings.ToLower(fieldName)]; exists {
		return mapping.IsSearchable
	}
	
	// 检查类型映射配置
	if mapping, exists := fa.fieldMappings[fieldType]; exists {
		return mapping.IsSearchable
	}
	
	// 默认规则
	switch {
	case strings.Contains(strings.ToLower(fieldName), "password"):
		return false
	case strings.Contains(strings.ToLower(fieldName), "secret"):
		return false
	case strings.Contains(strings.ToLower(fieldName), "token"):
		return false
	case fieldType == "string" || fieldType == "*string":
		return true
	case strings.Contains(strings.ToLower(fieldName), "name"):
		return true
	case strings.Contains(strings.ToLower(fieldName), "title"):
		return true
	case strings.Contains(strings.ToLower(fieldName), "description"):
		return true
	default:
		return false
	}
}

// IsRequiredField 判断字段是否必填
func (fa *FieldAnalyzer) IsRequiredField(fieldName, fieldType string, gormTag string) bool {
	// 检查字段映射配置
	if mapping, exists := fa.fieldMappings[strings.ToLower(fieldName)]; exists {
		return mapping.IsRequired
	}
	
	// 解析GORM标签
	gormOptions := fa.ParseGORMTag(gormTag)
	
	// 检查GORM标签中的not null
	if _, hasNotNull := gormOptions["not null"]; hasNotNull {
		return true
	}
	
	// 检查是否为指针类型（指针类型通常表示可选）
	if strings.HasPrefix(fieldType, "*") {
		return false
	}
	
	// 检查是否为主键
	if _, hasPrimaryKey := gormOptions["primaryKey"]; hasPrimaryKey {
		return false // 主键通常自动生成
	}
	
	// 检查是否有默认值
	if _, hasDefault := gormOptions["default"]; hasDefault {
		return false
	}
	
	// 特殊字段处理
	switch strings.ToLower(fieldName) {
	case "id", "uuid", "createdat", "updatedat", "deletedat":
		return false
	default:
		return false // 默认不必填，除非明确指定
	}
}

// IsSortableField 判断字段是否可排序
func (fa *FieldAnalyzer) IsSortableField(fieldName, fieldType string) bool {
	// 检查字段映射配置
	if mapping, exists := fa.fieldMappings[strings.ToLower(fieldName)]; exists {
		return mapping.IsSortable
	}
	
	// 检查类型映射配置
	if mapping, exists := fa.fieldMappings[fieldType]; exists {
		return mapping.IsSortable
	}
	
	// 默认规则
	switch {
	case strings.Contains(fieldType, "time.Time"):
		return true
	case fieldType == "int64" || fieldType == "*int64":
		return true
	case fieldType == "int" || fieldType == "*int":
		return true
	case fieldType == "float64" || fieldType == "*float64":
		return true
	case fieldType == "string" || fieldType == "*string":
		return true
	case strings.Contains(strings.ToLower(fieldName), "sort"):
		return true
	case strings.Contains(strings.ToLower(fieldName), "order"):
		return true
	default:
		return false
	}
}

// IsFilterableField 判断字段是否可过滤
func (fa *FieldAnalyzer) IsFilterableField(fieldName, fieldType string) bool {
	// 检查字段映射配置
	if mapping, exists := fa.fieldMappings[strings.ToLower(fieldName)]; exists {
		return mapping.IsFilterable
	}
	
	// 检查类型映射配置
	if mapping, exists := fa.fieldMappings[fieldType]; exists {
		return mapping.IsFilterable
	}
	
	// 默认规则
	switch {
	case strings.Contains(strings.ToLower(fieldName), "password"):
		return false
	case strings.Contains(strings.ToLower(fieldName), "secret"):
		return false
	case fieldType == "bool" || fieldType == "*bool":
		return true
	case strings.Contains(strings.ToLower(fieldName), "status"):
		return true
	case strings.Contains(strings.ToLower(fieldName), "type"):
		return true
	case strings.Contains(strings.ToLower(fieldName), "category"):
		return true
	default:
		return true // 大部分字段都可以过滤
	}
}

// GetValidationRules 获取验证规则
func (fa *FieldAnalyzer) GetValidationRules(fieldName, fieldType string, isRequired bool, gormTag string) string {
	var rules []string
	
	// 检查字段映射配置
	if mapping, exists := fa.fieldMappings[strings.ToLower(fieldName)]; exists {
		if mapping.Validation != "" {
			return mapping.Validation
		}
	}
	
	// 检查类型映射配置
	if mapping, exists := fa.fieldMappings[fieldType]; exists {
		if mapping.Validation != "" {
			return mapping.Validation
		}
	}
	
	// 必填规则
	if isRequired {
		rules = append(rules, "required")
	} else {
		rules = append(rules, "omitempty")
	}
	
	// 根据字段名生成规则
	fieldNameLower := strings.ToLower(fieldName)
	switch {
	case strings.Contains(fieldNameLower, "email"):
		rules = append(rules, "email")
	case strings.Contains(fieldNameLower, "phone"):
		rules = append(rules, "len=11,numeric")
	case strings.Contains(fieldNameLower, "url"):
		rules = append(rules, "url")
	case strings.Contains(fieldNameLower, "uuid"):
		rules = append(rules, "uuid")
	case strings.Contains(fieldNameLower, "password"):
		if isRequired {
			rules = append(rules, "min=6,max=20")
		}
	}
	
	// 根据类型生成规则
	switch fieldType {
	case "string", "*string":
		if !strings.Contains(fieldNameLower, "password") {
			rules = append(rules, "max=255")
		}
	case "int64", "*int64", "int", "*int":
		rules = append(rules, "gte=0")
	case "float64", "*float64":
		rules = append(rules, "gte=0")
	}
	
	// 解析GORM标签获取额外约束
	gormOptions := fa.ParseGORMTag(gormTag)
	if size, hasSize := gormOptions["size"]; hasSize {
		if fieldType == "string" || fieldType == "*string" {
			rules = append(rules, "max="+size)
		}
	}
	
	return strings.Join(rules, ",")
}

// GetJSONTag 获取JSON标签
func (fa *FieldAnalyzer) GetJSONTag(fieldName string, isPointer bool) string {
	jsonName := ToSnakeCase(fieldName)
	
	// 特殊字段处理
	switch strings.ToLower(fieldName) {
	case "password", "salt", "secret":
		return "-"
	case "deletedat":
		return "-"
	}
	
	if isPointer {
		return jsonName + ",omitempty"
	}
	
	return jsonName
}

// GetGORMTag 获取GORM标签
func (fa *FieldAnalyzer) GetGORMTag(fieldName, fieldType string, existingTag string) string {
	if existingTag != "" {
		return existingTag
	}
	
	var options []string
	fieldNameLower := strings.ToLower(fieldName)
	
	// 主键
	if fieldNameLower == "id" {
		options = append(options, "primaryKey", "autoIncrement")
	}
	
	// UUID字段
	if fieldNameLower == "uuid" {
		options = append(options, "uniqueIndex", "size:50")
	}
	
	// 时间字段
	if strings.Contains(fieldType, "time.Time") {
		if fieldNameLower == "createdat" {
			options = append(options, "autoCreateTime")
		} else if fieldNameLower == "updatedat" {
			options = append(options, "autoUpdateTime")
		}
	}
	
	// 字符串字段默认长度
	if fieldType == "string" || fieldType == "*string" {
		switch {
		case strings.Contains(fieldNameLower, "email"):
			options = append(options, "size:100")
		case strings.Contains(fieldNameLower, "phone"):
			options = append(options, "size:15")
		case strings.Contains(fieldNameLower, "url"):
			options = append(options, "size:500")
		case strings.Contains(fieldNameLower, "description"):
			options = append(options, "size:1000")
		case strings.Contains(fieldNameLower, "content"):
			options = append(options, "type:text")
		default:
			options = append(options, "size:255")
		}
	}
	
	return strings.Join(options, ";")
}

// DetectFieldFeatures 检测字段特性
func (fa *FieldAnalyzer) DetectFieldFeatures(fieldName string) map[string]bool {
	features := make(map[string]bool)
	fieldNameLower := strings.ToLower(fieldName)
	
	features["is_uuid"] = fieldNameLower == "uuid"
	features["is_tenant_id"] = fieldNameLower == "tenantid" || fieldNameLower == "tenant_id"
	features["is_parent_id"] = fieldNameLower == "parentid" || fieldNameLower == "parent_id"
	features["is_status"] = fieldNameLower == "status"
	features["is_soft_delete"] = fieldNameLower == "deletedat" || fieldNameLower == "deleted_at"
	features["is_created_at"] = fieldNameLower == "createdat" || fieldNameLower == "created_at"
	features["is_updated_at"] = fieldNameLower == "updatedat" || fieldNameLower == "updated_at"
	features["is_sort"] = fieldNameLower == "sort" || fieldNameLower == "sortorder"
	features["is_password"] = strings.Contains(fieldNameLower, "password")
	features["is_email"] = strings.Contains(fieldNameLower, "email")
	features["is_phone"] = strings.Contains(fieldNameLower, "phone")
	features["is_url"] = strings.Contains(fieldNameLower, "url")
	
	return features
}

// GenerateFieldComment 生成字段注释
func (fa *FieldAnalyzer) GenerateFieldComment(fieldName, fieldType string, features map[string]bool) string {
	fieldNameLower := strings.ToLower(fieldName)
	
	// 特殊字段的注释
	comments := map[string]string{
		"id":        "主键ID",
		"uuid":      "全局唯一标识符",
		"createdat": "创建时间",
		"updatedat": "更新时间",
		"deletedat": "删除时间",
		"status":    "状态",
		"sort":      "排序",
		"parentid":  "父级ID",
		"tenantid":  "租户ID",
	}
	
	if comment, exists := comments[fieldNameLower]; exists {
		return comment
	}
	
	// 根据字段名推断注释
	if strings.Contains(fieldNameLower, "name") {
		return "名称"
	}
	if strings.Contains(fieldNameLower, "title") {
		return "标题"
	}
	if strings.Contains(fieldNameLower, "description") {
		return "描述"
	}
	if strings.Contains(fieldNameLower, "content") {
		return "内容"
	}
	if strings.Contains(fieldNameLower, "email") {
		return "邮箱地址"
	}
	if strings.Contains(fieldNameLower, "phone") {
		return "电话号码"
	}
	if strings.Contains(fieldNameLower, "address") {
		return "地址"
	}
	if strings.Contains(fieldNameLower, "url") {
		return "链接地址"
	}
	if strings.Contains(fieldNameLower, "count") {
		return "数量"
	}
	if strings.Contains(fieldNameLower, "amount") {
		return "金额"
	}
	if strings.Contains(fieldNameLower, "price") {
		return "价格"
	}
	
	// 默认注释
	return ToPascalCase(fieldName)
}

// ValidateFieldName 验证字段名
func (fa *FieldAnalyzer) ValidateFieldName(fieldName string) error {
	if fieldName == "" {
		return fmt.Errorf("字段名不能为空")
	}
	
	if !ValidateIdentifier(fieldName) {
		return fmt.Errorf("字段名 %s 不是有效的标识符", fieldName)
	}
	
	if IsGoKeyword(fieldName) {
		return fmt.Errorf("字段名 %s 是Go关键字", fieldName)
	}
	
	return nil
}

// NormalizeFieldType 规范化字段类型
func (fa *FieldAnalyzer) NormalizeFieldType(fieldType string) string {
	// 移除包名前缀（如果是标准库）
	standardTypes := map[string]string{
		"time.Time":     "time.Time",
		"sql.NullString": "sql.NullString",
		"sql.NullInt64":  "sql.NullInt64",
		"sql.NullBool":   "sql.NullBool",
		"sql.NullTime":   "sql.NullTime",
	}
	
	if normalized, exists := standardTypes[fieldType]; exists {
		return normalized
	}
	
	return fieldType
}