package generator

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"path/filepath"
	"strings"

	"admin/cmd/crud-gen/utils"
)

// ModelInfo 模型信息
type ModelInfo struct {
	Name             string            `json:"name"`
	TableName        string            `json:"table_name"`
	Comment          string            `json:"comment"`
	Fields           []FieldInfo       `json:"fields"`
	Features         ModelFeatures     `json:"features"`
	Package          string            `json:"package"`
	ImportPath       string            `json:"import_path"`
	Relations        []Relation        `json:"relations"`
	FileName         string            `json:"file_name"`
	SmartQueryFields []SmartQueryField `json:"smart_query_fields"`
	RequiredFields   []FieldInfo       `json:"required_fields"`
	OptionalFields   []FieldInfo       `json:"optional_fields"`
	UpdateFields     []FieldInfo       `json:"update_fields"`
	NeedsModelImport bool              `json:"needs_model_import"`
}

// FieldInfo 字段信息
type FieldInfo struct {
	Name         string          `json:"name"`
	Type         string          `json:"type"`
	JSONTag      string          `json:"json_tag"`
	GORMTag      string          `json:"gorm_tag"`
	Comment      string          `json:"comment"`
	IsPointer    bool            `json:"is_pointer"`
	IsSlice      bool            `json:"is_slice"`
	IsSearchable bool            `json:"is_searchable"`
	IsRequired   bool            `json:"is_required"`
	IsSortable   bool            `json:"is_sortable"`
	IsFilterable bool            `json:"is_filterable"`
	Validation   string          `json:"validation"`
	DefaultValue string          `json:"default_value"`
	Features     map[string]bool `json:"features"`
}

// ModelFeatures 模型特性
type ModelFeatures struct {
	HasUUID       bool `json:"has_uuid"`
	HasTenantID   bool `json:"has_tenant_id"`
	HasParentID   bool `json:"has_parent_id"`
	HasStatus     bool `json:"has_status"`
	HasSoftDelete bool `json:"has_soft_delete"`
	HasCreatedAt  bool `json:"has_created_at"`
	HasUpdatedAt  bool `json:"has_updated_at"`
	HasSort       bool `json:"has_sort"`
	IsTreeModel   bool `json:"is_tree_model"`
	HasRelations  bool `json:"has_relations"`
}

// Relation 关联关系
type Relation struct {
	Type       string `json:"type"`        // hasOne, hasMany, belongsTo, manyToMany
	Model      string `json:"model"`       // 关联模型名
	ForeignKey string `json:"foreign_key"` // 外键字段
	References string `json:"references"`  // 引用字段
	Field      string `json:"field"`       // 关联字段名
}

// ModelAnalyzer 模型分析器
type ModelAnalyzer struct {
	modelDir      string
	fileSet       *token.FileSet
	fieldAnalyzer *utils.FieldAnalyzer
	smartAnalyzer *SmartFieldAnalyzer
}

// NewModelAnalyzer 创建模型分析器
func NewModelAnalyzer(modelDir string) *ModelAnalyzer {
	return &ModelAnalyzer{
		modelDir:      modelDir,
		fileSet:       token.NewFileSet(),
		fieldAnalyzer: utils.NewFieldAnalyzer(make(map[string]utils.FieldMapping)),
		smartAnalyzer: NewSmartFieldAnalyzer(),
	}
}

// AnalyzeModels 分析所有模型
func (a *ModelAnalyzer) AnalyzeModels() ([]ModelInfo, error) {
	var models []ModelInfo

	// 查找所有.gen.go文件
	pattern := filepath.Join(a.modelDir, "*.gen.go")
	files, err := filepath.Glob(pattern)
	if err != nil {
		return nil, fmt.Errorf("查找模型文件失败: %w", err)
	}

	if len(files) == 0 {
		// 尝试从项目根目录查找
		rootPattern := filepath.Join("../../", a.modelDir, "*.gen.go")
		files, err = filepath.Glob(rootPattern)
		if err != nil || len(files) == 0 {
			return nil, fmt.Errorf("在目录 %s 中没有找到 .gen.go 文件", a.modelDir)
		}
		fmt.Printf("✅ 在根目录找到 %d 个模型文件\n", len(files))
	}

	// 分析每个文件
	for _, file := range files {
		modelInfo, err := a.analyzeFile(file)
		if err != nil {
			// 记录错误但继续处理其他文件
			fmt.Printf("⚠️  分析文件 %s 失败: %v\n", file, err)
			continue
		}
		if modelInfo != nil {
			models = append(models, *modelInfo)
		}
	}

	return models, nil
}

// analyzeFile 分析单个文件
func (a *ModelAnalyzer) analyzeFile(filename string) (*ModelInfo, error) {
	src, err := parser.ParseFile(a.fileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return nil, fmt.Errorf("解析文件失败: %w", err)
	}

	// 查找结构体定义
	for _, decl := range src.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.TYPE {
			for _, spec := range genDecl.Specs {
				if typeSpec, ok := spec.(*ast.TypeSpec); ok {
					if structType, ok := typeSpec.Type.(*ast.StructType); ok {
						// 跳过非模型结构体（如接口、方法等）
						if a.isModelStruct(typeSpec.Name.Name, structType) {
							return a.analyzeStruct(typeSpec.Name.Name, structType, genDecl.Doc, filename)
						}
					}
				}
			}
		}
	}

	return nil, nil
}

// isModelStruct 判断是否为模型结构体
func (a *ModelAnalyzer) isModelStruct(name string, structType *ast.StructType) bool {
	// 跳过一些明显不是模型的结构体
	skipNames := []string{
		"Query", "DO", "IQuery", "IDO", "Tx", "TxQuery",
	}

	for _, skip := range skipNames {
		if strings.Contains(name, skip) {
			return false
		}
	}

	// 检查是否有典型的模型字段
	hasModelFields := false
	for _, field := range structType.Fields.List {
		if len(field.Names) > 0 {
			fieldName := field.Names[0].Name
			if strings.ToLower(fieldName) == "id" ||
				strings.ToLower(fieldName) == "uuid" ||
				strings.ToLower(fieldName) == "createdat" ||
				strings.ToLower(fieldName) == "updatedat" {
				hasModelFields = true
				break
			}
		}
	}

	return hasModelFields
}

// analyzeStruct 分析结构体
func (a *ModelAnalyzer) analyzeStruct(name string, structType *ast.StructType, doc *ast.CommentGroup, filename string) (*ModelInfo, error) {
	model := &ModelInfo{
		Name:       name,
		Package:    "model",
		Fields:     []FieldInfo{},
		Features:   ModelFeatures{},
		Relations:  []Relation{},
		FileName:   filepath.Base(filename),
		ImportPath: "admin/internal/model",
	}

	// 解析注释获取表名和描述
	if doc != nil {
		for _, comment := range doc.List {
			text := strings.TrimPrefix(comment.Text, "//")
			text = strings.TrimSpace(text)
			if strings.Contains(text, name) {
				model.Comment = text
			}
		}
	}

	// 如果没有注释，使用默认注释
	if model.Comment == "" {
		model.Comment = name + "模型"
	}

	// 分析字段
	for _, field := range structType.Fields.List {
		fieldInfos := a.analyzeField(field)
		for _, fieldInfo := range fieldInfos {
			if fieldInfo != nil {
				model.Fields = append(model.Fields, *fieldInfo)
				a.updateModelFeatures(&model.Features, fieldInfo)
				a.analyzeRelation(fieldInfo, &model.Relations)
			}
		}
	}

	// 设置表名
	model.TableName = a.getTableName(name)

	// 设置树形模型特性
	if model.Features.HasParentID {
		model.Features.IsTreeModel = true
	}

	// 进行智能分析
	a.performSmartAnalysis(model)

	return model, nil
}

// performSmartAnalysis 进行智能分析
func (a *ModelAnalyzer) performSmartAnalysis(model *ModelInfo) {
	// 获取智能查询字段
	model.SmartQueryFields = a.smartAnalyzer.GetSmartQueryFields(model.Fields)

	// 分类字段
	model.RequiredFields = make([]FieldInfo, 0)
	model.OptionalFields = make([]FieldInfo, 0)
	model.UpdateFields = make([]FieldInfo, 0)

	for _, field := range model.Fields {
		// 跳过系统字段
		if a.smartAnalyzer.IsSystemField(field.Name) {
			continue
		}

		// 跳过关联字段
		if strings.Contains(field.Type, "[]*") || strings.Contains(field.Type, "gorm.DeletedAt") {
			continue
		}

		// 设置字段特性
		field.IsSearchable = a.smartAnalyzer.IsSearchableField(field)
		field.IsSortable = a.smartAnalyzer.IsSortableField(field)

		// 分类字段
		if field.IsRequired && !strings.Contains(strings.ToLower(field.Name), "password") {
			model.RequiredFields = append(model.RequiredFields, field)
		} else if !strings.Contains(strings.ToLower(field.Name), "password") {
			model.OptionalFields = append(model.OptionalFields, field)
		}

		// 可更新字段
		if !strings.Contains(strings.ToLower(field.Name), "password") {
			model.UpdateFields = append(model.UpdateFields, field)
		}
	}

	// 判断是否需要引入model包
	model.NeedsModelImport = true
}

// analyzeField 分析字段
func (a *ModelAnalyzer) analyzeField(field *ast.Field) []*FieldInfo {
	var fieldInfos []*FieldInfo

	// 处理匿名字段（嵌入）
	if len(field.Names) == 0 {
		// 这是一个嵌入字段，暂时跳过
		return fieldInfos
	}

	// 处理命名字段
	for _, name := range field.Names {
		fieldInfo := &FieldInfo{
			Name:     name.Name,
			Features: make(map[string]bool),
		}

		// 分析字段类型
		fieldInfo.Type = a.fieldAnalyzer.AnalyzeFieldType(field.Type)
		fieldInfo.IsPointer = a.fieldAnalyzer.IsPointerType(field.Type)
		fieldInfo.IsSlice = a.fieldAnalyzer.IsSliceType(field.Type)

		// 分析标签
		if field.Tag != nil {
			tag := strings.Trim(field.Tag.Value, "`")
			fieldInfo.JSONTag = a.fieldAnalyzer.ExtractTag(tag, "json")
			fieldInfo.GORMTag = a.fieldAnalyzer.ExtractTag(tag, "gorm")
		}

		// 分析注释
		if field.Comment != nil {
			for _, comment := range field.Comment.List {
				fieldInfo.Comment = strings.TrimPrefix(comment.Text, "//")
				fieldInfo.Comment = strings.TrimSpace(fieldInfo.Comment)
				break // 只取第一行注释
			}
		}

		// 如果没有注释，生成默认注释
		if fieldInfo.Comment == "" {
			fieldInfo.Comment = a.fieldAnalyzer.GenerateFieldComment(fieldInfo.Name, fieldInfo.Type, fieldInfo.Features)
		}

		// 设置字段属性
		fieldInfo.IsSearchable = a.fieldAnalyzer.IsSearchableField(fieldInfo.Name, fieldInfo.Type, fieldInfo.GORMTag)
		fieldInfo.IsRequired = a.fieldAnalyzer.IsRequiredField(fieldInfo.Name, fieldInfo.Type, fieldInfo.GORMTag)
		fieldInfo.IsSortable = a.fieldAnalyzer.IsSortableField(fieldInfo.Name, fieldInfo.Type)
		fieldInfo.IsFilterable = a.fieldAnalyzer.IsFilterableField(fieldInfo.Name, fieldInfo.Type)
		fieldInfo.Validation = a.fieldAnalyzer.GetValidationRules(fieldInfo.Name, fieldInfo.Type, fieldInfo.IsRequired, fieldInfo.GORMTag)

		// 检测字段特性
		fieldInfo.Features = a.fieldAnalyzer.DetectFieldFeatures(fieldInfo.Name)

		// 如果JSON标签为空，生成默认标签
		if fieldInfo.JSONTag == "" {
			fieldInfo.JSONTag = a.fieldAnalyzer.GetJSONTag(fieldInfo.Name, fieldInfo.IsPointer)
		}

		fieldInfos = append(fieldInfos, fieldInfo)
	}

	return fieldInfos
}

// updateModelFeatures 更新模型特性
func (a *ModelAnalyzer) updateModelFeatures(features *ModelFeatures, field *FieldInfo) {
	fieldNameLower := strings.ToLower(field.Name)

	switch fieldNameLower {
	case "uuid":
		features.HasUUID = true
	case "tenantid", "tenant_id":
		features.HasTenantID = true
	case "parentid", "parent_id":
		features.HasParentID = true
	case "status":
		features.HasStatus = true
	case "deletedat", "deleted_at":
		features.HasSoftDelete = true
	case "createdat", "created_at":
		features.HasCreatedAt = true
	case "updatedat", "updated_at":
		features.HasUpdatedAt = true
	case "sort", "sortorder", "sort_order":
		features.HasSort = true
	}
}

// analyzeRelation 分析关联关系
func (a *ModelAnalyzer) analyzeRelation(field *FieldInfo, relations *[]Relation) {
	// 检查是否为关联字段
	if field.IsSlice && strings.HasPrefix(field.Type, "[]*") {
		// 一对多关系
		relatedModel := strings.TrimPrefix(field.Type, "[]*")
		relation := Relation{
			Type:  "hasMany",
			Model: relatedModel,
			Field: field.Name,
		}
		*relations = append(*relations, relation)
		return
	}

	if strings.HasPrefix(field.Type, "*") && !a.isBasicType(field.Type) {
		// 一对一关系
		relatedModel := strings.TrimPrefix(field.Type, "*")
		relation := Relation{
			Type:  "hasOne",
			Model: relatedModel,
			Field: field.Name,
		}
		*relations = append(*relations, relation)
		return
	}

	// 检查外键字段
	if strings.HasSuffix(strings.ToLower(field.Name), "id") && field.Name != "ID" {
		// 可能是外键
		modelName := strings.TrimSuffix(field.Name, "ID")
		if modelName != "" {
			relation := Relation{
				Type:       "belongsTo",
				Model:      modelName,
				Field:      field.Name,
				ForeignKey: field.Name,
			}
			*relations = append(*relations, relation)
		}
	}
}

// isBasicType 判断是否为基础类型
func (a *ModelAnalyzer) isBasicType(typeName string) bool {
	basicTypes := []string{
		"string", "int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64",
		"float32", "float64", "bool", "byte", "rune",
		"time.Time", "sql.NullString", "sql.NullInt64", "sql.NullBool", "sql.NullTime",
		"gorm.DeletedAt",
	}

	// 移除指针标记
	typeName = strings.TrimPrefix(typeName, "*")

	for _, basic := range basicTypes {
		if typeName == basic {
			return true
		}
	}

	return false
}

// getTableName 获取表名
func (a *ModelAnalyzer) getTableName(modelName string) string {
	// 查找TableName常量或方法
	// 这里简化处理，直接转换为蛇形命名
	return utils.ToSnakeCase(modelName)
}

// GetModelByName 根据名称获取模型
func (a *ModelAnalyzer) GetModelByName(models []ModelInfo, name string) *ModelInfo {
	for _, model := range models {
		if model.Name == name {
			return &model
		}
	}
	return nil
}

// GetModelsByFeature 根据特性获取模型
func (a *ModelAnalyzer) GetModelsByFeature(models []ModelInfo, feature string) []ModelInfo {
	var result []ModelInfo

	for _, model := range models {
		hasFeature := false
		switch feature {
		case "uuid":
			hasFeature = model.Features.HasUUID
		case "tenant":
			hasFeature = model.Features.HasTenantID
		case "tree":
			hasFeature = model.Features.IsTreeModel
		case "soft_delete":
			hasFeature = model.Features.HasSoftDelete
		case "status":
			hasFeature = model.Features.HasStatus
		case "audit":
			hasFeature = model.Features.HasCreatedAt || model.Features.HasUpdatedAt
		}

		if hasFeature {
			result = append(result, model)
		}
	}

	return result
}

// ValidateModel 验证模型
func (a *ModelAnalyzer) ValidateModel(model ModelInfo) []string {
	var errors []string

	// 检查模型名称
	if model.Name == "" {
		errors = append(errors, "模型名称不能为空")
	}

	if !utils.ValidateIdentifier(model.Name) {
		errors = append(errors, fmt.Sprintf("模型名称 %s 不是有效的标识符", model.Name))
	}

	// 检查字段
	if len(model.Fields) == 0 {
		errors = append(errors, "模型必须至少有一个字段")
	}

	fieldNames := make(map[string]bool)
	for _, field := range model.Fields {
		// 检查字段名称重复
		if fieldNames[field.Name] {
			errors = append(errors, fmt.Sprintf("字段名称重复: %s", field.Name))
		}
		fieldNames[field.Name] = true

		// 验证字段名称
		if err := a.fieldAnalyzer.ValidateFieldName(field.Name); err != nil {
			errors = append(errors, fmt.Sprintf("字段 %s: %v", field.Name, err))
		}
	}

	return errors
}

// GetFieldByName 根据名称获取字段
func (a *ModelAnalyzer) GetFieldByName(model ModelInfo, fieldName string) *FieldInfo {
	for _, field := range model.Fields {
		if field.Name == fieldName {
			return &field
		}
	}
	return nil
}

// GetFieldsByType 根据类型获取字段
func (a *ModelAnalyzer) GetFieldsByType(model ModelInfo, fieldType string) []FieldInfo {
	var result []FieldInfo
	for _, field := range model.Fields {
		if field.Type == fieldType {
			result = append(result, field)
		}
	}
	return result
}

// GetSearchableFields 获取可搜索字段
func (a *ModelAnalyzer) GetSearchableFields(model ModelInfo) []FieldInfo {
	var result []FieldInfo
	for _, field := range model.Fields {
		if field.IsSearchable {
			result = append(result, field)
		}
	}
	return result
}

// GetRequiredFields 获取必填字段
func (a *ModelAnalyzer) GetRequiredFields(model ModelInfo) []FieldInfo {
	var result []FieldInfo
	for _, field := range model.Fields {
		if field.IsRequired {
			result = append(result, field)
		}
	}
	return result
}

// GetSortableFields 获取可排序字段
func (a *ModelAnalyzer) GetSortableFields(model ModelInfo) []FieldInfo {
	var result []FieldInfo
	for _, field := range model.Fields {
		if field.IsSortable {
			result = append(result, field)
		}
	}
	return result
}

// GetFilterableFields 获取可过滤字段
func (a *ModelAnalyzer) GetFilterableFields(model ModelInfo) []FieldInfo {
	var result []FieldInfo
	for _, field := range model.Fields {
		if field.IsFilterable {
			result = append(result, field)
		}
	}
	return result
}
