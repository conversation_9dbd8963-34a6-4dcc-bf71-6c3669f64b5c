package generator

// getSmartAPIStructTemplate 获取智能API结构体模板
func (e *TemplateEngine) getSmartAPIStructTemplate() string {
	return `package v1

{{- if .NeedsModelImport}}
import (
	"admin/internal/model"
)
{{- end}}

{{- if not .ModelFeatures.IsTreeModel}}
// {{.ModelName}}PageReq 分页查询{{.ModelComment}}请求
type {{.ModelName}}PageReq struct {
	QueryPage    // 复用公共分页结构
	{{- if .ModelFeatures.HasCreatedAt}}
	CreateTime   // 复用公共时间查询
	{{- end}}
	{{- if .ModelFeatures.HasUpdatedAt}}
	UpdateTime   // 复用公共时间查询  
	{{- end}}
	{{- range .SmartQueryFields}}
	{{- if eq .QueryType "like"}}
	{{.Name}} string ` + "`json:\"{{.JSONTag}},omitempty\" form:\"{{.JSONTag}}\"` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- else if eq .QueryType "eq"}}
	{{.Name}} {{if .IsPointer}}*{{end}}{{.Type}} ` + "`json:\"{{.JSONTag}},omitempty\" form:\"{{.JSONTag}}\"` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- else if eq .QueryType "in"}}
	{{.Name}}List []{{.Type}} ` + "`json:\"{{.JSONTag}}List,omitempty\" form:\"{{.JSONTag}}List\"` // {{.Comment}}列表" + `
	{{- end}}
	{{- end}}
}
{{- else}}
// {{.ModelName}}TreeReq 树形查询{{.ModelComment}}请求
type {{.ModelName}}TreeReq struct {
	{{- range .SmartQueryFields}}
	{{- if eq .QueryType "like"}}
	{{.Name}} string ` + "`json:\"{{.JSONTag}},omitempty\" form:\"{{.JSONTag}}\"` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- else if eq .QueryType "eq"}}
	{{.Name}} {{if .IsPointer}}*{{end}}{{.Type}} ` + "`json:\"{{.JSONTag}},omitempty\" form:\"{{.JSONTag}}\"` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- end}}
	{{- end}}
}
{{- end}}

// {{.ModelName}}CreateReq 创建{{.ModelComment}}请求
type {{.ModelName}}CreateReq struct {
	{{- range .RequiredFields}}
	{{.Name}} {{.Type}} ` + "`json:\"{{.JSONTag}}\" binding:\"required{{if .Validation}},{{.Validation}}{{end}}\"` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- end}}
	{{- range .OptionalFields}}
	{{.Name}} {{.Type}} ` + "`json:\"{{.JSONTag}},omitempty\"{{if .Validation}} binding:\"{{.Validation}}\"{{end}}` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- end}}
}

// {{.ModelName}}UpdateReq 更新{{.ModelComment}}请求
type {{.ModelName}}UpdateReq struct {
	ID string ` + "`json:\"id\" binding:\"required\"` // ID" + `
	{{- range .UpdateFields}}
	{{.Name}} {{.Type}} ` + "`json:\"{{.JSONTag}},omitempty\"{{if .Validation}} binding:\"{{.Validation}}\"{{end}}` {{if .Comment}}// {{.Comment}}{{end}}" + `
	{{- end}}
}

{{- if .ModelFeatures.IsTreeModel}}
// {{.ModelName}}TreeResp 树形{{.ModelComment}}响应
type {{.ModelName}}TreeResp struct {
	*model.{{.ModelName}}
	Children []*{{.ModelName}}TreeResp ` + "`json:\"children,omitempty\"`" + `
}
{{- end}}

// {{.ModelName}}Resp {{.ModelComment}}响应  
type {{.ModelName}}Resp struct {
	*model.{{.ModelName}}
}`
}

// getSmartControllerTemplate 获取智能Controller模板
func (e *TemplateEngine) getSmartControllerTemplate() string {
	return `package controller

import (
	v1 "admin/api/v1"
	"admin/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// {{.ModelName}}Controller {{.ModelComment}}控制器
type {{.ModelName}}Controller struct {
	*Controller
	{{.ModelNameLower}}Service service.{{.ModelName}}Service
}

// New{{.ModelName}}Controller 创建{{.ModelComment}}控制器
func New{{.ModelName}}Controller(
	controller *Controller,
	{{.ModelNameLower}}Service service.{{.ModelName}}Service,
) *{{.ModelName}}Controller {
	return &{{.ModelName}}Controller{
		Controller: controller,
		{{.ModelNameLower}}Service: {{.ModelNameLower}}Service,
	}
}

// Create 创建{{.ModelComment}}
func (c *{{.ModelName}}Controller) Create(ctx *gin.Context) {
	var req v1.{{.ModelName}}CreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.Create(ctx, &req); err != nil {
		c.logger.Error("创建{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// QueryById 根据ID查询{{.ModelComment}}
func (c *{{.ModelName}}Controller) QueryById(ctx *gin.Context) {
	var req v1.ID
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{.ModelNameLower}}Service.QueryById(ctx, &req)
	if err != nil {
		c.logger.Error("查询{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

{{- if not .ModelFeatures.IsTreeModel}}
// QueryPage 分页查询{{.ModelComment}}
func (c *{{.ModelName}}Controller) QueryPage(ctx *gin.Context) {
	var req v1.{{.ModelName}}PageReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{.ModelNameLower}}Service.QueryPage(ctx, &req)
	if err != nil {
		c.logger.Error("分页查询{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}
{{- else}}
// QueryTree 查询{{.ModelComment}}树形结构
func (c *{{.ModelName}}Controller) QueryTree(ctx *gin.Context) {
	var req v1.{{.ModelName}}TreeReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{.ModelNameLower}}Service.QueryTree(ctx, &req)
	if err != nil {
		c.logger.Error("查询{{.ModelComment}}树形结构失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}
{{- end}}

// Update 更新{{.ModelComment}}
func (c *{{.ModelName}}Controller) Update(ctx *gin.Context) {
	var req v1.{{.ModelName}}UpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.Update(ctx, &req); err != nil {
		c.logger.Error("更新{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Delete 删除{{.ModelComment}}
func (c *{{.ModelName}}Controller) Delete(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.Delete(ctx, &req); err != nil {
		c.logger.Error("删除{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

{{- if .ModelFeatures.HasStatus}}
// Enable 启用{{.ModelComment}}
func (c *{{.ModelName}}Controller) Enable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.UpdateStatus(ctx, &req, "enabled"); err != nil {
		c.logger.Error("启用{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用{{.ModelComment}}
func (c *{{.ModelName}}Controller) Disable(ctx *gin.Context) {
	var req v1.IDS[string]
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.UpdateStatus(ctx, &req, "disabled"); err != nil {
		c.logger.Error("禁用{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}
{{- end}}`
}

// getSmartServiceTemplate 获取智能Service模板
func (e *TemplateEngine) getSmartServiceTemplate() string {
	return `package service

import (
	"context"
	
	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/internal/repository"
)

// {{.ModelName}}Service {{.ModelComment}}服务接口
type {{.ModelName}}Service interface {
	Create(ctx context.Context, req *v1.{{.ModelName}}CreateReq) error
	QueryById(ctx context.Context, req *v1.ID) (*model.{{.ModelName}}, error)
	{{- if not .ModelFeatures.IsTreeModel}}
	QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error)
	{{- else}}
	QueryTree(ctx context.Context, req *v1.{{.ModelName}}TreeReq) ([]*v1.{{.ModelName}}TreeResp, error)
	{{- end}}
	Update(ctx context.Context, req *v1.{{.ModelName}}UpdateReq) error
	Delete(ctx context.Context, req *v1.IDS[string]) error
	{{- if .ModelFeatures.HasStatus}}
	UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error
	{{- end}}
}

// {{.ModelNameLower}}Service {{.ModelComment}}服务实现
type {{.ModelNameLower}}Service struct {
	*Service
	{{.ModelNameLower}}Repository repository.{{.ModelName}}Repository
}

// New{{.ModelName}}Service 创建{{.ModelComment}}服务
func New{{.ModelName}}Service(
	service *Service,
	{{.ModelNameLower}}Repository repository.{{.ModelName}}Repository,
) {{.ModelName}}Service {
	return &{{.ModelNameLower}}Service{
		Service: service,
		{{.ModelNameLower}}Repository: {{.ModelNameLower}}Repository,
	}
}

// Create 创建{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Create(ctx context.Context, req *v1.{{.ModelName}}CreateReq) error {
	entity := &model.{{.ModelName}}{
		{{- range .RequiredFields}}
		{{.Name}}: req.{{.Name}},
		{{- end}}
		{{- range .OptionalFields}}
		{{- if not .IsPointer}}
		{{.Name}}: req.{{.Name}},
		{{- else}}
		{{.Name}}: &req.{{.Name}},
		{{- end}}
		{{- end}}
	}
	
	return s.{{.ModelNameLower}}Repository.Create(ctx, entity)
}

// QueryById 根据ID查询{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) QueryById(ctx context.Context, req *v1.ID) (*model.{{.ModelName}}, error) {
	return s.{{.ModelNameLower}}Repository.QueryById(ctx, req)
}

{{- if not .ModelFeatures.IsTreeModel}}
// QueryPage 分页查询{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error) {
	return s.{{.ModelNameLower}}Repository.QueryPage(ctx, req)
}
{{- else}}
// QueryTree 查询{{.ModelComment}}树形结构
func (s *{{.ModelNameLower}}Service) QueryTree(ctx context.Context, req *v1.{{.ModelName}}TreeReq) ([]*v1.{{.ModelName}}TreeResp, error) {
	list, err := s.{{.ModelNameLower}}Repository.QueryTree(ctx, req)
	if err != nil {
		return nil, err
	}
	
	// 转换为树形响应结构
	return s.buildTreeResp(list), nil
}

// buildTreeResp 构建树形响应
func (s *{{.ModelNameLower}}Service) buildTreeResp(list []*model.{{.ModelName}}) []*v1.{{.ModelName}}TreeResp {
	var result []*v1.{{.ModelName}}TreeResp
	nodeMap := make(map[string]*v1.{{.ModelName}}TreeResp)
	
	// 创建所有节点
	for _, item := range list {
		node := &v1.{{.ModelName}}TreeResp{
			{{.ModelName}}: item,
			Children: make([]*v1.{{.ModelName}}TreeResp, 0),
		}
		nodeMap[item.UUID] = node
	}
	
	// 构建父子关系
	for _, item := range list {
		node := nodeMap[item.UUID]
		if item.ParentID != nil && *item.ParentID != "" {
			if parent, exists := nodeMap[*item.ParentID]; exists {
				parent.Children = append(parent.Children, node)
			}
		} else {
			result = append(result, node)
		}
	}
	
	return result
}
{{- end}}

// Update 更新{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Update(ctx context.Context, req *v1.{{.ModelName}}UpdateReq) error {
	entity := &model.{{.ModelName}}{
		UUID: req.ID,
		{{- range .UpdateFields}}
		{{- if not .IsPointer}}
		{{.Name}}: req.{{.Name}},
		{{- else}}
		{{.Name}}: &req.{{.Name}},
		{{- end}}
		{{- end}}
	}
	
	return s.{{.ModelNameLower}}Repository.Update(ctx, entity)
}

// Delete 删除{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Delete(ctx context.Context, req *v1.IDS[string]) error {
	return s.{{.ModelNameLower}}Repository.Delete(ctx, req.Ids)
}

{{- if .ModelFeatures.HasStatus}}
// UpdateStatus 更新{{.ModelComment}}状态
func (s *{{.ModelNameLower}}Service) UpdateStatus(ctx context.Context, req *v1.IDS[string], status string) error {
	return s.{{.ModelNameLower}}Repository.UpdateStatus(ctx, req.Ids, status)
}
{{- end}}`
}

// getSmartRepositoryTemplate 获取智能Repository模板
func (e *TemplateEngine) getSmartRepositoryTemplate() string {
	return `package repository

import (
	"context"
	
	v1 "admin/api/v1"
	"admin/internal/model"
	"admin/pkg/gen-ext"
	"github.com/pkg/errors"
)

// {{.ModelName}}Repository {{.ModelComment}}仓储接口
type {{.ModelName}}Repository interface {
	Create(ctx context.Context, entity *model.{{.ModelName}}) error
	QueryById(ctx context.Context, req *v1.ID) (*model.{{.ModelName}}, error)
	{{- if not .ModelFeatures.IsTreeModel}}
	QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error)
	{{- else}}
	QueryTree(ctx context.Context, req *v1.{{.ModelName}}TreeReq) ([]*model.{{.ModelName}}, error)
	{{- end}}
	Update(ctx context.Context, entity *model.{{.ModelName}}) error
	Delete(ctx context.Context, ids []string) error
	{{- if .ModelFeatures.HasStatus}}
	UpdateStatus(ctx context.Context, ids []string, status string) error
	{{- end}}
}

// {{.ModelNameLower}}Repository {{.ModelComment}}仓储实现
type {{.ModelNameLower}}Repository struct {
	*Repository
	gen *gen.Query
}

// New{{.ModelName}}Repository 创建{{.ModelComment}}仓储
func New{{.ModelName}}Repository(repository *Repository, gen *gen.Query) {{.ModelName}}Repository {
	return &{{.ModelNameLower}}Repository{
		Repository: repository,
		gen: gen,
	}
}

// getColumns 获取查询列
func (r *{{.ModelNameLower}}Repository) getColumns() []field.Expr {
	t := r.gen.{{.ModelName}}
	return []field.Expr{
		{{- range .Fields}}
		{{- if not (or (eq .Name "DeletedAt") (eq .Name "Password"))}}
		t.{{.Name}},
		{{- end}}
		{{- end}}
	}
}

// Create 创建{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Create(ctx context.Context, entity *model.{{.ModelName}}) error {
	return r.gen.{{.ModelName}}.WithContext(ctx).Create(entity)
}

// QueryById 根据ID查询{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) QueryById(ctx context.Context, req *v1.ID) (*model.{{.ModelName}}, error) {
	t := r.gen.{{.ModelName}}
	return t.WithContext(ctx).
		Select(r.getColumns()...).
		Where(t.UUID.Eq(req.ID)).
		First()
}

{{- if not .ModelFeatures.IsTreeModel}}
// QueryPage 分页查询{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error) {
	t := r.gen.{{.ModelName}}
	query := t.WithContext(ctx).Select(r.getColumns()...)
	
	// 智能构建查询条件
	{{- range .SmartQueryFields}}
	{{- if eq .QueryType "like"}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Like("%" + req.{{.Name}} + "%"))
	}
	{{- else if eq .QueryType "eq"}}
	{{- if .IsPointer}}
	if req.{{.Name}} != nil {
		query = query.Where(t.{{.Name}}.Eq(*req.{{.Name}}))
	}
	{{- else}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Eq(req.{{.Name}}))
	}
	{{- end}}
	{{- else if eq .QueryType "in"}}
	if len(req.{{.Name}}List) > 0 {
		query = query.Where(t.{{.Name}}.In(req.{{.Name}}List...))
	}
	{{- end}}
	{{- end}}
	
	{{- if .ModelFeatures.HasSoftDelete}}
	// 排除软删除的记录
	query = query.Where(t.DeletedAt.IsNull())
	{{- end}}
	
	{{- if .ModelFeatures.HasCreatedAt}}
	// 创建时间范围查询
	if !req.CreateTime.IsZero() {
		query = query.Where(t.CreatedAt.Gte(req.CreateTime))
	}
	{{- end}}
	
	// 排序
	if req.OrderBy != "" {
		if req.OrderDesc {
			query = query.Order(t.GetFieldByName(req.OrderBy).Desc())
		} else {
			query = query.Order(t.GetFieldByName(req.OrderBy))
		}
	} else {
		{{- if .ModelFeatures.HasSort}}
		query = query.Order(t.Sort.Desc(), t.CreatedAt.Desc())
		{{- else}}
		query = query.Order(t.CreatedAt.Desc())
		{{- end}}
	}
	
	// 分页查询
	list, count, err := query.FindByPage((req.GetPage()-1)*req.GetPageSize(), req.GetPageSize())
	if err != nil {
		return nil, errors.Wrap(err, "分页查询失败")
	}
	
	return v1.NewPage(list, count, req.GetPage(), req.GetPageSize()), nil
}
{{- else}}
// QueryTree 查询{{.ModelComment}}树形结构
func (r *{{.ModelNameLower}}Repository) QueryTree(ctx context.Context, req *v1.{{.ModelName}}TreeReq) ([]*model.{{.ModelName}}, error) {
	t := r.gen.{{.ModelName}}
	query := t.WithContext(ctx).Select(r.getColumns()...)
	
	// 智能构建查询条件
	{{- range .SmartQueryFields}}
	{{- if eq .QueryType "like"}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Like("%" + req.{{.Name}} + "%"))
	}
	{{- else if eq .QueryType "eq"}}
	{{- if .IsPointer}}
	if req.{{.Name}} != nil {
		query = query.Where(t.{{.Name}}.Eq(*req.{{.Name}}))
	}
	{{- else}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Eq(req.{{.Name}}))
	}
	{{- end}}
	{{- end}}
	{{- end}}
	
	{{- if .ModelFeatures.HasSoftDelete}}
	// 排除软删除的记录
	query = query.Where(t.DeletedAt.IsNull())
	{{- end}}
	
	// 排序
	{{- if .ModelFeatures.HasSort}}
	query = query.Order(t.Sort, t.CreatedAt)
	{{- else}}
	query = query.Order(t.CreatedAt)
	{{- end}}
	
	return query.Find()
}
{{- end}}

// Update 更新{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Update(ctx context.Context, entity *model.{{.ModelName}}) error {
	t := r.gen.{{.ModelName}}
	_, err := t.WithContext(ctx).Where(t.UUID.Eq(entity.UUID)).Updates(entity)
	return err
}

// Delete 删除{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Delete(ctx context.Context, ids []string) error {
	t := r.gen.{{.ModelName}}
	{{- if .ModelFeatures.HasSoftDelete}}
	// 软删除
	_, err := t.WithContext(ctx).Where(t.UUID.In(ids...)).Delete()
	{{- else}}
	// 硬删除
	_, err := t.WithContext(ctx).Unscoped().Where(t.UUID.In(ids...)).Delete()
	{{- end}}
	return err
}

{{- if .ModelFeatures.HasStatus}}
// UpdateStatus 更新{{.ModelComment}}状态
func (r *{{.ModelNameLower}}Repository) UpdateStatus(ctx context.Context, ids []string, status string) error {
	t := r.gen.{{.ModelName}}
	_, err := t.WithContext(ctx).Where(t.UUID.In(ids...)).Update(t.Status, status)
	return err
}
{{- end}}`
}
