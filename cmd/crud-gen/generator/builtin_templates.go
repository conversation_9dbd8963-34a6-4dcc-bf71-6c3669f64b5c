package generator

// getBuiltinControllerTemplate 获取内置Controller模板
func (e *TemplateEngine) getBuiltinControllerTemplate() string {
	return `package controller

import (
	v1 "{{.ModuleName}}/api/v1"
	"{{.ModuleName}}/internal/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// {{.ModelName}}Controller {{.ModelComment}}控制器
type {{.ModelName}}Controller struct {
	*Controller
	{{.ModelNameLower}}Service service.{{.ModelName}}Service
}

// New{{.ModelName}}Controller 创建{{.ModelComment}}控制器
func New{{.ModelName}}Controller(
	controller *Controller,
	{{.ModelNameLower}}Service service.{{.ModelName}}Service,
) *{{.ModelName}}Controller {
	return &{{.ModelName}}Controller{
		Controller: controller,
		{{.ModelNameLower}}Service: {{.ModelNameLower}}Service,
	}
}

// Create 创建{{.ModelComment}}
func (c *{{.ModelName}}Controller) Create(ctx *gin.Context) {
	var req v1.{{.ModelName}}CreateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.Create(ctx, &req); err != nil {
		c.logger.Error("创建{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// QueryById 根据ID查询{{.ModelComment}}
func (c *{{.ModelName}}Controller) QueryById(ctx *gin.Context) {
	var req v1.IDReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{.ModelNameLower}}Service.QueryById(ctx, &req)
	if err != nil {
		c.logger.Error("查询{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

// QueryPage 分页查询{{.ModelComment}}
func (c *{{.ModelName}}Controller) QueryPage(ctx *gin.Context) {
	var req v1.{{.ModelName}}PageReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	result, err := c.{{.ModelNameLower}}Service.QueryPage(ctx, &req)
	if err != nil {
		c.logger.Error("分页查询{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

// Update 更新{{.ModelComment}}
func (c *{{.ModelName}}Controller) Update(ctx *gin.Context) {
	var req v1.{{.ModelName}}UpdateReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.Update(ctx, &req); err != nil {
		c.logger.Error("更新{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Delete 删除{{.ModelComment}}
func (c *{{.ModelName}}Controller) Delete(ctx *gin.Context) {
	var req v1.IDSReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.Delete(ctx, &req); err != nil {
		c.logger.Error("删除{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Query 查询所有{{.ModelComment}}
func (c *{{.ModelName}}Controller) Query(ctx *gin.Context) {
	result, err := c.{{.ModelNameLower}}Service.Query(ctx)
	if err != nil {
		c.logger.Error("查询所有{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}

{{if .ModelFeatures.HasStatus}}
// Enable 启用{{.ModelComment}}
func (c *{{.ModelName}}Controller) Enable(ctx *gin.Context) {
	var req v1.IDSReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.UpdateStatus(ctx, &req, "enabled"); err != nil {
		c.logger.Error("启用{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}

// Disable 禁用{{.ModelComment}}
func (c *{{.ModelName}}Controller) Disable(ctx *gin.Context) {
	var req v1.IDSReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, err)
		return
	}
	
	if err := c.{{.ModelNameLower}}Service.UpdateStatus(ctx, &req, "disabled"); err != nil {
		c.logger.Error("禁用{{.ModelComment}}失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, nil)
}
{{end}}

{{if .ModelFeatures.IsTreeModel}}
// QueryTree 查询{{.ModelComment}}树形结构
func (c *{{.ModelName}}Controller) QueryTree(ctx *gin.Context) {
	result, err := c.{{.ModelNameLower}}Service.QueryTree(ctx)
	if err != nil {
		c.logger.Error("查询{{.ModelComment}}树形结构失败", zap.Error(err))
		v1.HandleError(ctx, err)
		return
	}
	
	v1.HandleSuccess(ctx, result)
}
{{end}}`
}

// getBuiltinServiceTemplate 获取内置Service模板
func (e *TemplateEngine) getBuiltinServiceTemplate() string {
	return `package service

import (
	"context"
	
	v1 "{{.ModuleName}}/api/v1"
	"{{.ModuleName}}/internal/model"
	"{{.ModuleName}}/internal/repository"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

// {{.ModelName}}Service {{.ModelComment}}服务接口
type {{.ModelName}}Service interface {
	Create(ctx context.Context, req *v1.{{.ModelName}}CreateReq) error
	QueryById(ctx context.Context, req *v1.IDReq) (*model.{{.ModelName}}, error)
	QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error)
	Update(ctx context.Context, req *v1.{{.ModelName}}UpdateReq) error
	Delete(ctx context.Context, req *v1.IDSReq) error
	Query(ctx context.Context) ([]*model.{{.ModelName}}, error)
	{{if .ModelFeatures.HasStatus}}
	UpdateStatus(ctx context.Context, req *v1.IDSReq, status string) error
	{{end}}
	{{if .ModelFeatures.IsTreeModel}}
	QueryTree(ctx context.Context) ([]*model.{{.ModelName}}, error)
	{{end}}
}

// {{.ModelNameLower}}Service {{.ModelComment}}服务实现
type {{.ModelNameLower}}Service struct {
	*Service
	{{.ModelNameLower}}Repository repository.{{.ModelName}}Repository
}

// New{{.ModelName}}Service 创建{{.ModelComment}}服务
func New{{.ModelName}}Service(
	service *Service,
	{{.ModelNameLower}}Repository repository.{{.ModelName}}Repository,
) {{.ModelName}}Service {
	return &{{.ModelNameLower}}Service{
		Service: service,
		{{.ModelNameLower}}Repository: {{.ModelNameLower}}Repository,
	}
}

// Create 创建{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Create(ctx context.Context, req *v1.{{.ModelName}}CreateReq) error {
	entity := &model.{{.ModelName}}{
		{{range .RequiredFields}}
		{{.Name}}: req.{{.Name}},
		{{end}}
	}
	
	return s.{{.ModelNameLower}}Repository.Create(ctx, entity)
}

// QueryById 根据ID查询{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) QueryById(ctx context.Context, req *v1.IDReq) (*model.{{.ModelName}}, error) {
	return s.{{.ModelNameLower}}Repository.QueryById(ctx, req)
}

// QueryPage 分页查询{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error) {
	return s.{{.ModelNameLower}}Repository.QueryPage(ctx, req)
}

// Update 更新{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Update(ctx context.Context, req *v1.{{.ModelName}}UpdateReq) error {
	entity := &model.{{.ModelName}}{
		{{range .Fields}}
		{{if ne .Name "ID"}}
		{{.Name}}: req.{{.Name}},
		{{end}}
		{{end}}
	}
	
	return s.{{.ModelNameLower}}Repository.Update(ctx, entity)
}

// Delete 删除{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Delete(ctx context.Context, req *v1.IDSReq) error {
	return s.{{.ModelNameLower}}Repository.Delete(ctx, req)
}

// Query 查询所有{{.ModelComment}}
func (s *{{.ModelNameLower}}Service) Query(ctx context.Context) ([]*model.{{.ModelName}}, error) {
	return s.{{.ModelNameLower}}Repository.Query(ctx)
}

{{if .ModelFeatures.HasStatus}}
// UpdateStatus 更新{{.ModelComment}}状态
func (s *{{.ModelNameLower}}Service) UpdateStatus(ctx context.Context, req *v1.IDSReq, status string) error {
	return s.{{.ModelNameLower}}Repository.UpdateStatus(ctx, req, status)
}
{{end}}

{{if .ModelFeatures.IsTreeModel}}
// QueryTree 查询{{.ModelComment}}树形结构
func (s *{{.ModelNameLower}}Service) QueryTree(ctx context.Context) ([]*model.{{.ModelName}}, error) {
	list, err := s.{{.ModelNameLower}}Repository.Query(ctx)
	if err != nil {
		return nil, err
	}
	
	// 转换为树形结构
	if len(list) > 0 {
		return list[0].ToTree(list), nil
	}
	
	return list, nil
}
{{end}}`
}

// getBuiltinRepositoryTemplate 获取内置Repository模板
func (e *TemplateEngine) getBuiltinRepositoryTemplate() string {
	return `package repository

import (
	"context"
	
	v1 "{{.ModuleName}}/api/v1"
	"{{.ModuleName}}/internal/gen"
	"{{.ModuleName}}/internal/model"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gen/field"
)

// {{.ModelName}}Repository {{.ModelComment}}仓储接口
type {{.ModelName}}Repository interface {
	Create(ctx context.Context, entity *model.{{.ModelName}}) error
	QueryById(ctx context.Context, req *v1.IDReq) (*model.{{.ModelName}}, error)
	QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error)
	Update(ctx context.Context, entity *model.{{.ModelName}}) error
	Delete(ctx context.Context, req *v1.IDSReq) error
	Query(ctx context.Context) ([]*model.{{.ModelName}}, error)
	{{if .ModelFeatures.HasStatus}}
	UpdateStatus(ctx context.Context, req *v1.IDSReq, status string) error
	{{end}}
}

// {{.ModelNameLower}}Repository {{.ModelComment}}仓储实现
type {{.ModelNameLower}}Repository struct {
	*Repository
}

// New{{.ModelName}}Repository 创建{{.ModelComment}}仓储
func New{{.ModelName}}Repository(r *Repository) {{.ModelName}}Repository {
	return &{{.ModelNameLower}}Repository{
		Repository: r,
	}
}

// getColumns 获取查询字段
func (r *{{.ModelNameLower}}Repository) getColumns() []field.Expr {
	t := r.gen.{{.ModelName}}
	return []field.Expr{
		{{range .Fields}}
		t.{{.Name}},
		{{end}}
	}
}

// Create 创建{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Create(ctx context.Context, entity *model.{{.ModelName}}) error {
	return r.gen.{{.ModelName}}.WithContext(ctx).Create(entity)
}

// QueryById 根据ID查询{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) QueryById(ctx context.Context, req *v1.IDReq) (*model.{{.ModelName}}, error) {
	t := r.gen.{{.ModelName}}
	return t.WithContext(ctx).
		Select(r.getColumns()...).
		Where(t.UUID.Eq(req.ID)).
		First()
}

// QueryPage 分页查询{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) QueryPage(ctx context.Context, req *v1.{{.ModelName}}PageReq) (*v1.Page[*model.{{.ModelName}}], error) {
	t := r.gen.{{.ModelName}}
	query := t.WithContext(ctx).Select(r.getColumns()...)
	
	// 构建查询条件
	{{range .SearchFields}}
	if req.{{.Name}} != "" {
		query = query.Where(t.{{.Name}}.Like("%" + req.{{.Name}} + "%"))
	}
	{{end}}
	
	{{if .ModelFeatures.HasSoftDelete}}
	// 排除软删除的记录
	query = query.Where(t.DeletedAt.IsNull())
	{{end}}
	
	// 时间范围查询
	if !req.CreateTimeStart.IsZero() {
		query = query.Where(t.CreatedAt.Gte(req.CreateTimeStart))
	}
	if !req.CreateTimeEnd.IsZero() {
		query = query.Where(t.CreatedAt.Lte(req.CreateTimeEnd))
	}
	
	// 排序
	if req.OrderBy != "" {
		if req.OrderDesc {
			query = query.Order(t.GetFieldByName(req.OrderBy).Desc())
		} else {
			query = query.Order(t.GetFieldByName(req.OrderBy))
		}
	} else {
		query = query.Order(t.CreatedAt.Desc())
	}
	
	// 分页查询
	list, count, err := query.FindByPage(req.GetPage()-1, req.GetPageSize())
	if err != nil {
		return nil, errors.Wrap(err, "分页查询失败")
	}
	
	return &v1.Page[*model.{{.ModelName}}]{
		Current:  req.GetPage(),
		PageSize: req.GetPageSize(),
		Total:    count,
		List:     list,
	}, nil
}

// Update 更新{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Update(ctx context.Context, entity *model.{{.ModelName}}) error {
	t := r.gen.{{.ModelName}}
	_, err := t.WithContext(ctx).
		Where(t.UUID.Eq(entity.UUID)).
		Updates(entity)
	return err
}

// Delete 删除{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Delete(ctx context.Context, req *v1.IDSReq) error {
	t := r.gen.{{.ModelName}}
	{{if .ModelFeatures.HasSoftDelete}}
	_, err := t.WithContext(ctx).Where(t.UUID.In(req.Ids...)).Delete()
	{{else}}
	_, err := t.WithContext(ctx).Where(t.UUID.In(req.Ids...)).Delete()
	{{end}}
	return err
}

// Query 查询所有{{.ModelComment}}
func (r *{{.ModelNameLower}}Repository) Query(ctx context.Context) ([]*model.{{.ModelName}}, error) {
	t := r.gen.{{.ModelName}}
	return t.WithContext(ctx).
		Select(r.getColumns()...).
		{{if .ModelFeatures.HasSoftDelete}}
		Where(t.DeletedAt.IsNull()).
		{{end}}
		Find()
}

{{if .ModelFeatures.HasStatus}}
// UpdateStatus 更新{{.ModelComment}}状态
func (r *{{.ModelNameLower}}Repository) UpdateStatus(ctx context.Context, req *v1.IDSReq, status string) error {
	t := r.gen.{{.ModelName}}
	_, err := t.WithContext(ctx).
		Where(t.UUID.In(req.Ids...)).
		Update(t.Status, status)
	return err
}
{{end}}`
}

// getBuiltinAPIStructTemplate 获取内置API结构体模板
func (e *TemplateEngine) getBuiltinAPIStructTemplate() string {
	return `package v1

import (
	"{{.ModuleName}}/internal/model"
)

// {{.ModelName}}CreateReq 创建{{.ModelComment}}请求
type {{.ModelName}}CreateReq struct {
	{{range .Fields}}
	{{if and (ne .Name "ID") (ne .Name "CreatedAt") (ne .Name "UpdatedAt") (ne .Name "DeletedAt")}}
	{{.Name}} {{.Type}} ` + "`json:\"{{.JSONTag}}\"{{if .Validation}} binding:\"{{.Validation}}\"{{end}}`" + ` {{if .Comment}}// {{.Comment}}{{end}}
	{{end}}
	{{end}}
}

// {{.ModelName}}UpdateReq 更新{{.ModelComment}}请求
type {{.ModelName}}UpdateReq struct {
	ID string ` + "`json:\"id\" binding:\"required\"`" + ` // ID
	{{range .Fields}}
	{{if and (ne .Name "ID") (ne .Name "CreatedAt") (ne .Name "UpdatedAt") (ne .Name "DeletedAt")}}
	{{.Name}} {{.Type}} ` + "`json:\"{{.JSONTag}}\"{{if .Validation}} binding:\"{{.Validation}}\"{{end}}`" + ` {{if .Comment}}// {{.Comment}}{{end}}
	{{end}}
	{{end}}
}

// {{.ModelName}}PageReq 分页查询{{.ModelComment}}请求
type {{.ModelName}}PageReq struct {
	QueryPage
	CreateTime
	UpdateTime
	{{range .SearchFields}}
	{{.Name}} string ` + "`json:\"{{.JSONTag}}\"`" + ` {{if .Comment}}// {{.Comment}}{{end}}
	{{end}}
}

// {{.ModelName}}Resp {{.ModelComment}}响应
type {{.ModelName}}Resp struct {
	*model.{{.ModelName}}
}`
}

// getBuiltinRoutesTemplate 获取内置路由模板
func (e *TemplateEngine) getBuiltinRoutesTemplate() string {
	return `// 自动生成的路由配置文件
// 请勿手动修改此文件

package server

import (
	"{{.ModuleName}}/internal/controller"
	"github.com/gin-gonic/gin"
)

// RegisterGeneratedRoutes 注册自动生成的路由
func RegisterGeneratedRoutes(
	r *gin.RouterGroup,
	{{range .Models}}
	{{.ModelNameLower}}Controller *controller.{{.ModelName}}Controller,
	{{end}}
) {
	{{range .Models}}
	// {{.ModelComment}}路由
	{{.ModelNameLower}} := r.Group("/{{toSnakeCase .ModelName}}")
	{
		{{.ModelNameLower}}.POST("/create", {{.ModelNameLower}}Controller.Create)
		{{.ModelNameLower}}.POST("/queryById", {{.ModelNameLower}}Controller.QueryById)
		{{.ModelNameLower}}.POST("/queryPage", {{.ModelNameLower}}Controller.QueryPage)
		{{.ModelNameLower}}.POST("/update", {{.ModelNameLower}}Controller.Update)
		{{.ModelNameLower}}.POST("/delete", {{.ModelNameLower}}Controller.Delete)
		{{.ModelNameLower}}.POST("/query", {{.ModelNameLower}}Controller.Query)
		{{if .ModelFeatures.HasStatus}}
		{{.ModelNameLower}}.POST("/enable", {{.ModelNameLower}}Controller.Enable)
		{{.ModelNameLower}}.POST("/disable", {{.ModelNameLower}}Controller.Disable)
		{{end}}
		{{if .ModelFeatures.IsTreeModel}}
		{{.ModelNameLower}}.POST("/queryTree", {{.ModelNameLower}}Controller.QueryTree)
		{{end}}
	}
	{{end}}
}`
}

// getBuiltinTestTemplate 获取内置测试模板
func (e *TemplateEngine) getBuiltinTestTemplate() string {
	return `package test

import (
	"testing"
	
	"{{.ModuleName}}/internal/service"
	"{{.ModuleName}}/internal/repository"
	"github.com/stretchr/testify/assert"
)

// Test{{.ModelName}}Service 测试{{.ModelComment}}服务
func Test{{.ModelName}}Service(t *testing.T) {
	// TODO: 实现测试逻辑
	t.Skip("待实现")
}

// Test{{.ModelName}}Repository 测试{{.ModelComment}}仓储
func Test{{.ModelName}}Repository(t *testing.T) {
	// TODO: 实现测试逻辑
	t.Skip("待实现")
}`
}

// getBuiltinWireSetTemplate 获取内置Wire配置模板
func (e *TemplateEngine) getBuiltinWireSetTemplate() string {
	return `// 自动生成的Wire配置文件
// 请勿手动修改此文件

package wire

import (
	"{{.ModuleName}}/internal/controller"
	"{{.ModuleName}}/internal/service"
	"{{.ModuleName}}/internal/repository"
	"github.com/google/wire"
)

// GeneratedProviderSet 自动生成的Provider集合
var GeneratedProviderSet = wire.NewSet(
	{{range .Models}}
	// {{.ModelComment}}
	repository.New{{.ModelName}}Repository,
	service.New{{.ModelName}}Service,
	controller.New{{.ModelName}}Controller,
	{{end}}
)`
}