package generator

import (
	"fmt"
	"path/filepath"
	"time"

	"admin/cmd/crud-gen/config"
	"admin/cmd/crud-gen/utils"
)

// Generator CRUD代码生成器
type Generator struct {
	config   *config.Config
	analyzer *ModelAnalyzer
	engine   *TemplateEngine
	writer   *FileWriter
}

// GenerateOptions 生成选项
type GenerateOptions struct {
	Force   bool
	Verbose bool
	DryRun  bool
	Backup  bool
}

// GenerationResult 生成结果
type GenerationResult struct {
	ModelCount      int           `json:"model_count"`
	ControllerCount int           `json:"controller_count"`
	ServiceCount    int           `json:"service_count"`
	RepositoryCount int           `json:"repository_count"`
	APICount        int           `json:"api_count"`
	RouteCount      int           `json:"route_count"`
	TestCount       int           `json:"test_count"`
	ControllerSize  int64         `json:"controller_size"`
	ServiceSize     int64         `json:"service_size"`
	RepositorySize  int64         `json:"repository_size"`
	APISize         int64         `json:"api_size"`
	RouteSize       int64         `json:"route_size"`
	TestSize        int64         `json:"test_size"`
	TotalSize       int64         `json:"total_size"`
	Duration        time.Duration `json:"duration"`
	Features        []string      `json:"features"`
	Errors          []string      `json:"errors"`
	GeneratedFiles  []string      `json:"generated_files"`
}

// New 创建新的生成器
func New(cfg *config.Config) (*Generator, error) {
	analyzer := NewModelAnalyzer(cfg.Generator.ModelDir)
	engine := NewTemplateEngine(cfg)
	writer := NewFileWriter(cfg)

	return &Generator{
		config:   cfg,
		analyzer: analyzer,
		engine:   engine,
		writer:   writer,
	}, nil
}

// AnalyzeModels 分析模型
func (g *Generator) AnalyzeModels() ([]ModelInfo, error) {
	models, err := g.analyzer.AnalyzeModels()
	if err != nil {
		return nil, fmt.Errorf("分析模型失败: %w", err)
	}

	// 过滤模型
	var filteredModels []ModelInfo
	for _, model := range models {
		if g.config.IsModelIncluded(model.Name) {
			filteredModels = append(filteredModels, model)
		}
	}

	return filteredModels, nil
}

// Generate 生成代码
func (g *Generator) Generate(models []ModelInfo, opts GenerateOptions) (*GenerationResult, error) {
	startTime := time.Now()
	result := &GenerationResult{
		ModelCount:     len(models),
		Features:       []string{},
		Errors:         []string{},
		GeneratedFiles: []string{},
	}

	// 初始化模板引擎
	if err := g.engine.LoadTemplates(); err != nil {
		return nil, fmt.Errorf("加载模板失败: %w", err)
	}

	// 为每个模型生成代码
	for _, model := range models {
		if err := g.generateForModel(model, opts, result); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("生成模型 %s 失败: %v", model.Name, err))
			if !opts.Force {
				return result, err
			}
		}
	}

	// 生成路由文件
	if g.config.Generator.GenerateRoutes && len(models) > 0 {
		if err := g.generateRoutes(models, opts, result); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("生成路由失败: %v", err))
		}
	}

	// 生成Wire配置
	if g.config.Generator.GenerateWireSet && len(models) > 0 {
		if err := g.generateWireSet(models, opts, result); err != nil {
			result.Errors = append(result.Errors, fmt.Sprintf("生成Wire配置失败: %v", err))
		}
	}

	// 统计功能特性
	result.Features = g.collectFeatures(models)
	result.Duration = time.Since(startTime)

	return result, nil
}

// generateForModel 为单个模型生成代码
func (g *Generator) generateForModel(model ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	// 生成Controller
	if g.config.Generator.GenerateController {
		if err := g.generateController(model, opts, result); err != nil {
			return fmt.Errorf("生成Controller失败: %w", err)
		}
	}

	// 生成Service
	if g.config.Generator.GenerateService {
		if err := g.generateService(model, opts, result); err != nil {
			return fmt.Errorf("生成Service失败: %w", err)
		}
	}

	// 生成Repository
	if g.config.Generator.GenerateRepository {
		if err := g.generateRepository(model, opts, result); err != nil {
			return fmt.Errorf("生成Repository失败: %w", err)
		}
	}

	// 生成API结构体
	if g.config.Generator.GenerateAPIStructs {
		if err := g.generateAPIStruct(model, opts, result); err != nil {
			return fmt.Errorf("生成API结构体失败: %w", err)
		}
	}

	// 生成测试文件
	if g.config.Generator.GenerateTests {
		if err := g.generateTests(model, opts, result); err != nil {
			return fmt.Errorf("生成测试文件失败: %w", err)
		}
	}

	return nil
}

// generateController 生成Controller
func (g *Generator) generateController(model ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := g.prepareTemplateData(model)
	content, err := g.engine.RenderTemplate("controller", templateData)
	if err != nil {
		return err
	}

	outputPath := g.config.GetOutputFile("controller", model.Name)
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.ControllerCount++
	result.ControllerSize += size
	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// generateService 生成Service
func (g *Generator) generateService(model ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := g.prepareTemplateData(model)
	content, err := g.engine.RenderTemplate("service", templateData)
	if err != nil {
		return err
	}

	outputPath := g.config.GetOutputFile("service", model.Name)
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.ServiceCount++
	result.ServiceSize += size
	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// generateRepository 生成Repository
func (g *Generator) generateRepository(model ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := g.prepareTemplateData(model)
	content, err := g.engine.RenderTemplate("repository", templateData)
	if err != nil {
		return err
	}

	outputPath := g.config.GetOutputFile("repository", model.Name)
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.RepositoryCount++
	result.RepositorySize += size
	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// generateAPIStruct 生成API结构体
func (g *Generator) generateAPIStruct(model ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := g.prepareTemplateData(model)
	content, err := g.engine.RenderTemplate("api_struct", templateData)
	if err != nil {
		return err
	}

	outputPath := g.config.GetOutputFile("api_struct", model.Name)
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.APICount++
	result.APISize += size
	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// generateTests 生成测试文件
func (g *Generator) generateTests(model ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := g.prepareTemplateData(model)
	content, err := g.engine.RenderTemplate("test", templateData)
	if err != nil {
		return err
	}

	outputPath := filepath.Join(g.config.Output.TestDir, fmt.Sprintf("%s_test.go", model.Name))
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.TestCount++
	result.TestSize += size
	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// generateRoutes 生成路由文件
func (g *Generator) generateRoutes(models []ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := map[string]interface{}{
		"Models":      models,
		"Config":      g.config,
		"ModuleName":  g.config.Generator.ModuleName,
		"Features":    g.config.Features,
		"GeneratedAt": time.Now().Format("2006-01-02 15:04:05"),
	}

	content, err := g.engine.RenderTemplate("routes", templateData)
	if err != nil {
		return err
	}

	outputPath := g.config.Output.RoutesFile
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.RouteCount++
	result.RouteSize += size
	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// generateWireSet 生成Wire配置
func (g *Generator) generateWireSet(models []ModelInfo, opts GenerateOptions, result *GenerationResult) error {
	templateData := map[string]interface{}{
		"Models":     models,
		"Config":     g.config,
		"ModuleName": g.config.Generator.ModuleName,
	}

	content, err := g.engine.RenderTemplate("wire_set", templateData)
	if err != nil {
		return err
	}

	outputPath := g.config.Output.WireFile
	size, err := g.writer.WriteFile(outputPath, content, opts.Force, opts.DryRun)
	if err != nil {
		return err
	}

	result.TotalSize += size
	result.GeneratedFiles = append(result.GeneratedFiles, outputPath)

	return nil
}

// prepareTemplateData 准备模板数据
func (g *Generator) prepareTemplateData(model ModelInfo) map[string]interface{} {
	return map[string]any{
		"Model":            model,
		"ModelName":        model.Name,
		"ModelNameLower":   utils.ToSnakeCase(model.Name),
		"ModelComment":     model.Comment,
		"TableName":        model.TableName,
		"Fields":           model.Fields,
		"Features":         g.config.Features, // 使用配置中的Features而不是模型的Features
		"ModelFeatures":    model.Features,    // 模型特性单独提供
		"Config":           g.config,
		"ModuleName":       g.config.Generator.ModuleName,
		"PackageName":      g.config.Generator.PackageName,
		"Author":           g.config.Generator.Author,
		"Copyright":        g.config.Generator.Copyright,
		"SearchFields":     g.getSearchableFields(model),
		"SortFields":       g.getSortableFields(model),
		"FilterFields":     g.getFilterableFields(model),
		"RequiredFields":   model.RequiredFields,
		"OptionalFields":   model.OptionalFields,
		"UpdateFields":     model.UpdateFields,
		"SmartQueryFields": model.SmartQueryFields,
		"NeedsModelImport": model.NeedsModelImport,
		"GeneratedAt":      time.Now().Format("2006-01-02 15:04:05"),
	}
}

// getSearchableFields 获取可搜索字段
func (g *Generator) getSearchableFields(model ModelInfo) []FieldInfo {
	var fields []FieldInfo
	for _, field := range model.Fields {
		if field.IsSearchable && !g.config.IsFieldExcluded(field.Name) {
			fields = append(fields, field)
		}
	}
	return fields
}

// getSortableFields 获取可排序字段
func (g *Generator) getSortableFields(model ModelInfo) []FieldInfo {
	var fields []FieldInfo
	for _, field := range model.Fields {
		if field.IsSortable && !g.config.IsFieldExcluded(field.Name) {
			fields = append(fields, field)
		}
	}
	return fields
}

// getFilterableFields 获取可过滤字段
func (g *Generator) getFilterableFields(model ModelInfo) []FieldInfo {
	var fields []FieldInfo
	for _, field := range model.Fields {
		if field.IsFilterable && !g.config.IsFieldExcluded(field.Name) {
			fields = append(fields, field)
		}
	}
	return fields
}

// getRequiredFields 获取必填字段
func (g *Generator) getRequiredFields(model ModelInfo) []FieldInfo {
	var fields []FieldInfo
	for _, field := range model.Fields {
		if field.IsRequired && !g.config.IsFieldExcluded(field.Name) {
			fields = append(fields, field)
		}
	}
	return fields
}

// collectFeatures 收集功能特性
func (g *Generator) collectFeatures(models []ModelInfo) []string {
	features := []string{}
	featureMap := make(map[string]bool)

	// 基础CRUD功能
	featureMap["标准CRUD操作"] = true

	if g.config.Features.EnablePagination {
		featureMap["分页查询"] = true
	}
	if g.config.Features.EnableSearch {
		featureMap["条件搜索"] = true
	}
	if g.config.Features.EnableSort {
		featureMap["排序功能"] = true
	}
	if g.config.Features.EnableValidation {
		featureMap["数据验证"] = true
	}
	if g.config.Features.EnableCache {
		featureMap["缓存支持"] = true
	}

	// 检查模型特性
	for _, model := range models {
		if model.Features.HasStatus {
			featureMap["状态管理"] = true
		}
		if model.Features.HasSoftDelete {
			featureMap["软删除支持"] = true
		}
		if model.Features.IsTreeModel {
			featureMap["树形结构"] = true
		}
		if model.Features.HasTenantID {
			featureMap["多租户支持"] = true
		}
		if model.Features.HasCreatedAt || model.Features.HasUpdatedAt {
			featureMap["审计日志"] = true
		}
	}

	// 转换为切片
	for feature := range featureMap {
		features = append(features, feature)
	}

	return features
}

// GetGeneratedFiles 获取将要生成的文件列表
func (g *Generator) GetGeneratedFiles(model ModelInfo) []string {
	var files []string

	if g.config.Generator.GenerateController {
		files = append(files, g.config.GetOutputFile("controller", model.Name))
	}
	if g.config.Generator.GenerateService {
		files = append(files, g.config.GetOutputFile("service", model.Name))
	}
	if g.config.Generator.GenerateRepository {
		files = append(files, g.config.GetOutputFile("repository", model.Name))
	}
	if g.config.Generator.GenerateAPIStructs {
		files = append(files, g.config.GetOutputFile("api_struct", model.Name))
	}
	if g.config.Generator.GenerateTests {
		files = append(files, filepath.Join(g.config.Output.TestDir, fmt.Sprintf("%s_test.go", model.Name)))
	}

	return files
}

// BackupExistingFiles 备份现有文件
func (g *Generator) BackupExistingFiles(models []ModelInfo) error {
	return g.writer.BackupFiles(models, g.config.Output.BackupDir)
}

// ValidateConfig 验证配置
func (g *Generator) ValidateConfig() error {
	return g.config.Validate()
}
