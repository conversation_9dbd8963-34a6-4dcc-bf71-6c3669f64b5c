package generator

import (
	"strings"
	"unicode"
)

// SmartFieldAnalyzer 智能字段分析器
type SmartFieldAnalyzer struct {
	// 系统字段，不参与查询
	systemFields map[string]bool
	// 字段类型到查询方式的映射
	queryTypeMap map[string]string
	// 公共结构体字段
	publicStructs map[string]bool
}

// NewSmartFieldAnalyzer 创建智能字段分析器
func NewSmartFieldAnalyzer() *SmartFieldAnalyzer {
	return &SmartFieldAnalyzer{
		systemFields: map[string]bool{
			"id":         true,
			"created_at": true,
			"createdat":  true,
			"updated_at": true,
			"updatedat":  true,
			"deleted_at": true,
			"deletedat":  true,
			"sort":       true,
			"create_id":  true,
			"createid":   true,
			"update_id":  true,
			"updateid":   true,
			"parent_id":  true,
			"parentid":   true,
			"uuid":       true,
		},
		queryTypeMap: map[string]string{
			"string":     "like",
			"*string":    "like",
			"int":        "eq",
			"int64":      "eq",
			"*int64":     "eq",
			"uint64":     "eq",
			"bool":       "eq",
			"*bool":      "eq",
			"time.Time":  "range",
			"*time.Time": "range",
		},
		publicStructs: map[string]bool{
			"QueryPage":  true,
			"CreateTime": true,
			"UpdateTime": true,
			"ID":         true,
			"IDS":        true,
		},
	}
}

// IsSystemField 判断是否为系统字段
func (s *SmartFieldAnalyzer) IsSystemField(fieldName string) bool {
	return s.systemFields[strings.ToLower(fieldName)]
}

// IsSearchableField 判断字段是否可搜索
func (s *SmartFieldAnalyzer) IsSearchableField(field FieldInfo) bool {
	// 排除系统字段
	if s.IsSystemField(field.Name) {
		return false
	}

	// 排除虚拟字段
	if field.Name == "Children" {
		return false
	}

	// 排除密码相关字段
	fieldLower := strings.ToLower(field.Name)
	if strings.Contains(fieldLower, "password") || strings.Contains(fieldLower, "pwd") ||
		strings.Contains(fieldLower, "salt") || strings.Contains(fieldLower, "secret") {
		return false
	}

	// 排除关联字段（切片类型，但允许字符串切片）
	if strings.Contains(field.Type, "[]") && !strings.Contains(field.Type, "string") {
		return false
	}

	// 排除复杂类型
	if strings.Contains(field.Type, "gorm.DeletedAt") ||
		strings.Contains(field.Type, "gorm.Model") ||
		strings.Contains(field.Type, "json.RawMessage") {
		return false
	}

	// 排除二进制数据字段
	if strings.Contains(fieldLower, "blob") || strings.Contains(fieldLower, "binary") ||
		strings.Contains(fieldLower, "file") || strings.Contains(fieldLower, "image") {
		return false
	}

	// 排除过长的文本字段（通常不用于搜索）
	if strings.Contains(fieldLower, "content") || strings.Contains(fieldLower, "description") ||
		strings.Contains(fieldLower, "detail") || strings.Contains(fieldLower, "text") {
		// 除非明确标记为可搜索的字段
		if !strings.Contains(fieldLower, "search") && !strings.Contains(fieldLower, "keyword") {
			return false
		}
	}

	// 只允许特定类型进行搜索
	baseType := strings.TrimPrefix(field.Type, "*")
	switch baseType {
	case "string", "int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64",
		"bool", "float32", "float64":
		return true
	}

	// 允许枚举类型（通常是字符串）
	if strings.Contains(field.Type, "enum") || strings.Contains(fieldLower, "enum") {
		return true
	}

	// 根据字段名推断是否应该可搜索
	searchablePatterns := []string{
		"name", "title", "code", "no", "number", "account", "username",
		"email", "phone", "mobile", "tel", "status", "type", "category",
		"level", "grade", "rank", "priority", "tag", "label", "key",
	}

	for _, pattern := range searchablePatterns {
		if strings.Contains(fieldLower, pattern) {
			return true
		}
	}

	return false
}

// GetQueryType 获取字段的查询类型
func (s *SmartFieldAnalyzer) GetQueryType(field FieldInfo) string {
	fieldLower := strings.ToLower(field.Name)
	baseType := strings.TrimPrefix(field.Type, "*")

	// 优先根据字段名判断查询类型
	if strings.Contains(fieldLower, "name") || strings.Contains(fieldLower, "title") ||
		strings.Contains(fieldLower, "account") || strings.Contains(fieldLower, "username") ||
		strings.Contains(fieldLower, "email") || strings.Contains(fieldLower, "phone") ||
		strings.Contains(fieldLower, "mobile") || strings.Contains(fieldLower, "tel") ||
		strings.Contains(fieldLower, "description") || strings.Contains(fieldLower, "remark") ||
		strings.Contains(fieldLower, "keyword") || strings.Contains(fieldLower, "search") {
		return "like"
	}

	// 状态、类型、等级等使用 in 查询（支持多值选择）
	if strings.Contains(fieldLower, "status") || strings.Contains(fieldLower, "type") ||
		strings.Contains(fieldLower, "category") || strings.Contains(fieldLower, "level") ||
		strings.Contains(fieldLower, "grade") || strings.Contains(fieldLower, "rank") ||
		strings.Contains(fieldLower, "priority") || strings.Contains(fieldLower, "gender") ||
		strings.Contains(fieldLower, "sex") || strings.Contains(fieldLower, "state") ||
		strings.Contains(fieldLower, "role") || strings.Contains(fieldLower, "dept") ||
		strings.Contains(fieldLower, "department") {
		return "in"
	}

	// 编码、编号通常使用模糊查询
	if strings.Contains(fieldLower, "code") || strings.Contains(fieldLower, "no") ||
		strings.Contains(fieldLower, "number") || strings.Contains(fieldLower, "serial") {
		return "like"
	}

	// 时间字段使用范围查询
	if strings.Contains(fieldLower, "time") || strings.Contains(fieldLower, "date") ||
		baseType == "time.Time" {
		return "range"
	}

	// 根据数据类型判断
	switch baseType {
	case "string":
		// 字符串默认模糊查询，除非字段是枚举类型
		if len(fieldLower) <= 10 && (strings.Contains(fieldLower, "type") ||
			strings.Contains(fieldLower, "status") || strings.Contains(fieldLower, "flag") ||
			strings.Contains(fieldLower, "role") || strings.Contains(fieldLower, "dept")) {
			return "in" // 枚举类型字段支持多值查询
		}
		return "like"
	case "int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64":
		// 数值类型，如果是ID相关的精确匹配，否则可能是范围查询
		if strings.Contains(fieldLower, "id") && !strings.Contains(fieldLower, "parent") {
			return "eq"
		}
		// 数量、金额等可能需要范围查询
		if strings.Contains(fieldLower, "amount") || strings.Contains(fieldLower, "price") ||
			strings.Contains(fieldLower, "count") || strings.Contains(fieldLower, "quantity") ||
			strings.Contains(fieldLower, "size") || strings.Contains(fieldLower, "weight") {
			return "range"
		}
		return "eq"
	case "float32", "float64":
		// 浮点数通常用于范围查询
		return "range"
	case "bool":
		return "eq"
	case "time.Time":
		return "range"
	}

	// 枚举类型使用IN查询
	if strings.Contains(field.Type, "enum") || strings.Contains(fieldLower, "enum") {
		return "in"
	}

	// 默认精确匹配
	return "eq"
}

// ShouldUsePublicStruct 判断是否应该使用公共结构体
func (s *SmartFieldAnalyzer) ShouldUsePublicStruct(structName string) bool {
	return s.publicStructs[structName]
}

// GetSmartQueryFields 获取智能查询字段
func (s *SmartFieldAnalyzer) GetSmartQueryFields(fields []FieldInfo) []SmartQueryField {
	var queryFields []SmartQueryField

	for _, field := range fields {
		if s.IsSearchableField(field) {
			priority := s.GetFieldSearchPriority(field)
			queryField := SmartQueryField{
				FieldInfo:      field,
				QueryType:      s.GetQueryType(field),
				IsOptional:     true, // 查询字段默认都是可选的
				Placeholder:    s.generatePlaceholder(field),
				Priority:       priority,
				IsQuickSearch:  s.ShouldIncludeInQuickSearch(field),
				ValidationRule: s.GetFieldValidationRules(field),
			}
			queryFields = append(queryFields, queryField)
		}
	}

	// 按优先级排序，高优先级的字段在前面
	for i := 0; i < len(queryFields)-1; i++ {
		for j := i + 1; j < len(queryFields); j++ {
			if queryFields[i].Priority < queryFields[j].Priority {
				queryFields[i], queryFields[j] = queryFields[j], queryFields[i]
			}
		}
	}

	return queryFields
}

// SmartQueryField 智能查询字段
type SmartQueryField struct {
	FieldInfo
	QueryType      string // like, eq, range, in
	IsOptional     bool
	Placeholder    string
	Priority       int    // 搜索优先级 1-5
	IsQuickSearch  bool   // 是否包含在快速搜索中
	ValidationRule string // 验证规则
}

// generatePlaceholder 生成字段占位符
func (s *SmartFieldAnalyzer) generatePlaceholder(field FieldInfo) string {
	fieldLower := strings.ToLower(field.Name)

	if strings.Contains(fieldLower, "name") {
		return "请输入" + field.Comment
	}
	if strings.Contains(fieldLower, "code") {
		return "请输入编码"
	}
	if strings.Contains(fieldLower, "status") {
		return "请选择状态"
	}

	return "请输入" + field.Comment
}

// camelToSnake 驼峰转下划线
func camelToSnake(s string) string {
	var result []rune
	for i, r := range s {
		if i > 0 && unicode.IsUpper(r) {
			result = append(result, '_')
		}
		result = append(result, unicode.ToLower(r))
	}
	return string(result)
}

// snakeToCamel 下划线转驼峰
func snakeToCamel(s string) string {
	parts := strings.Split(s, "_")
	for i := range parts {
		if len(parts[i]) > 0 {
			parts[i] = strings.Title(parts[i])
		}
	}
	return strings.Join(parts, "")
}

// lowerCamelCase 首字母小写的驼峰
func lowerCamelCase(s string) string {
	camel := snakeToCamel(s)
	if len(camel) > 0 {
		return strings.ToLower(camel[:1]) + camel[1:]
	}
	return camel
}

// GetFieldSearchPriority 获取字段搜索优先级（1-5，5最高）
func (s *SmartFieldAnalyzer) GetFieldSearchPriority(field FieldInfo) int {
	fieldLower := strings.ToLower(field.Name)

	// 最高优先级：主要标识字段
	if strings.Contains(fieldLower, "name") || strings.Contains(fieldLower, "title") ||
		strings.Contains(fieldLower, "account") || strings.Contains(fieldLower, "username") ||
		fieldLower == "code" || fieldLower == "no" {
		return 5
	}

	// 高优先级：联系信息
	if strings.Contains(fieldLower, "email") || strings.Contains(fieldLower, "phone") ||
		strings.Contains(fieldLower, "mobile") || strings.Contains(fieldLower, "tel") {
		return 4
	}

	// 中高优先级：状态和分类
	if strings.Contains(fieldLower, "status") || strings.Contains(fieldLower, "type") ||
		strings.Contains(fieldLower, "category") || strings.Contains(fieldLower, "level") {
		return 3
	}

	// 中等优先级：编码和标签
	if strings.Contains(fieldLower, "code") || strings.Contains(fieldLower, "tag") ||
		strings.Contains(fieldLower, "label") || strings.Contains(fieldLower, "key") {
		return 2
	}

	// 低优先级：其他可搜索字段
	return 1
}

// ShouldIncludeInQuickSearch 判断是否应该包含在快速搜索中
func (s *SmartFieldAnalyzer) ShouldIncludeInQuickSearch(field FieldInfo) bool {
	return s.GetFieldSearchPriority(field) >= 3
}

// GetFieldValidationRules 获取字段验证规则
func (s *SmartFieldAnalyzer) GetFieldValidationRules(field FieldInfo) string {
	fieldLower := strings.ToLower(field.Name)
	baseType := strings.TrimPrefix(field.Type, "*")

	var rules []string

	// 基本类型验证
	switch baseType {
	case "string":
		if strings.Contains(fieldLower, "email") {
			rules = append(rules, "email")
		}
		if strings.Contains(fieldLower, "phone") || strings.Contains(fieldLower, "mobile") {
			rules = append(rules, "len=11", "numeric")
		}
		if strings.Contains(fieldLower, "url") {
			rules = append(rules, "url")
		}
		// 字符串长度限制
		if strings.Contains(fieldLower, "name") || strings.Contains(fieldLower, "title") {
			rules = append(rules, "max=100")
		} else if strings.Contains(fieldLower, "code") {
			rules = append(rules, "max=50")
		} else {
			rules = append(rules, "max=255")
		}
	case "int", "int64", "uint", "uint64":
		rules = append(rules, "gte=0")
	}

	if len(rules) == 0 {
		return "omitempty"
	}

	return "omitempty," + strings.Join(rules, ",")
}

// IsSortableField 判断字段是否可排序
func (s *SmartFieldAnalyzer) IsSortableField(field FieldInfo) bool {
	// 排除虚拟字段
	if field.Name == "Children" {
		return false
	}

	fieldLower := strings.ToLower(field.Name)
	baseType := strings.TrimPrefix(field.Type, "*")

	// 时间字段可排序
	if baseType == "time.Time" || strings.Contains(fieldLower, "time") || strings.Contains(fieldLower, "date") {
		return true
	}

	// 数值类型可排序
	switch baseType {
	case "int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64",
		"float32", "float64":
		return true
	}

	// 特定的数值字段
	if strings.Contains(fieldLower, "sort") || strings.Contains(fieldLower, "order") ||
		strings.Contains(fieldLower, "priority") || strings.Contains(fieldLower, "level") ||
		strings.Contains(fieldLower, "count") || strings.Contains(fieldLower, "amount") ||
		fieldLower == "id" {
		return true
	}

	return false
}

// GetSortableFields 获取可排序字段
func (s *SmartFieldAnalyzer) GetSortableFields(fields []FieldInfo) []FieldInfo {
	var sortableFields []FieldInfo

	for _, field := range fields {
		if s.IsSortableField(field) {
			sortableFields = append(sortableFields, field)
		}
	}

	return sortableFields
}
