package generator

import (
	"fmt"
	"io/fs"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"admin/cmd/crud-gen/config"
	"admin/cmd/crud-gen/utils"
)

// FileWriter 文件写入器
type FileWriter struct {
	config    *config.Config
	fileUtils *utils.FileUtils
}

// WriteResult 写入结果
type WriteResult struct {
	FilePath    string
	Size        int64
	IsNew       bool
	IsOverwrite bool
	BackupPath  string
	Error       error
}

// NewFileWriter 创建文件写入器
func NewFileWriter(cfg *config.Config) *FileWriter {
	return &FileWriter{
		config:    cfg,
		fileUtils: utils.NewFileUtils(),
	}
}

// WriteFile 写入文件
func (w *FileWriter) WriteFile(path, content string, force, dryRun bool) (int64, error) {
	// 检查目录是否存在，不存在则创建
	dir := filepath.Dir(path)
	if err := w.fileUtils.EnsureDir(dir); err != nil {
		return 0, fmt.Errorf("创建目录失败: %w", err)
	}

	// 检查文件是否已存在
	exists := w.fileUtils.FileExists(path)
	
	// 如果文件存在且不强制覆盖，返回错误
	if exists && !force {
		return 0, fmt.Errorf("文件已存在: %s (使用 --force 强制覆盖)", path)
	}

	// 干运行模式，只返回内容大小
	if dryRun {
		return int64(len(content)), nil
	}

	// 获取文件权限
	perm, err := w.getFilePermission()
	if err != nil {
		return 0, fmt.Errorf("获取文件权限失败: %w", err)
	}

	// 写入文件
	if err := w.fileUtils.WriteFile(path, content, perm); err != nil {
		return 0, fmt.Errorf("写入文件失败: %w", err)
	}

	return int64(len(content)), nil
}

// WriteFiles 批量写入文件
func (w *FileWriter) WriteFiles(files map[string]string, options WriteOptions) ([]WriteResult, error) {
	var results []WriteResult
	
	for filePath, content := range files {
		result := WriteResult{
			FilePath: filePath,
			Size:     int64(len(content)),
		}
		
		// 检查文件是否存在
		exists := w.fileUtils.FileExists(filePath)
		result.IsNew = !exists
		result.IsOverwrite = exists && options.Force
		
		// 备份现有文件
		if exists && options.Backup {
			backupPath, err := w.backupFile(filePath)
			if err != nil {
				result.Error = fmt.Errorf("备份文件失败: %w", err)
				results = append(results, result)
				continue
			}
			result.BackupPath = backupPath
		}
		
		// 写入文件
		size, err := w.WriteFile(filePath, content, options.Force, options.DryRun)
		if err != nil {
			result.Error = err
		} else {
			result.Size = size
		}
		
		results = append(results, result)
	}
	
	return results, nil
}

// WriteOptions 写入选项
type WriteOptions struct {
	Force   bool
	DryRun  bool
	Backup  bool
	Verbose bool
}

// BackupFiles 备份文件
func (w *FileWriter) BackupFiles(models []ModelInfo, backupDir string) error {
	if backupDir == "" {
		backupDir = w.config.Output.BackupDir
	}
	
	// 确保备份目录存在
	if err := w.fileUtils.EnsureDir(backupDir); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}
	
	// 为每个模型备份相关文件
	for _, model := range models {
		if err := w.backupModelFiles(model, backupDir); err != nil {
			return fmt.Errorf("备份模型 %s 文件失败: %w", model.Name, err)
		}
	}
	
	return nil
}

// backupModelFiles 备份单个模型的文件
func (w *FileWriter) backupModelFiles(model ModelInfo, backupDir string) error {
	// 获取模型相关的文件路径
	files := []string{
		w.config.GetOutputFile("controller", model.Name),
		w.config.GetOutputFile("service", model.Name),
		w.config.GetOutputFile("repository", model.Name),
		w.config.GetOutputFile("api_struct", model.Name),
	}
	
	// 备份每个文件
	for _, filePath := range files {
		if w.fileUtils.FileExists(filePath) {
			if _, err := w.backupFile(filePath); err != nil {
				return fmt.Errorf("备份文件 %s 失败: %w", filePath, err)
			}
		}
	}
	
	return nil
}

// backupFile 备份单个文件
func (w *FileWriter) backupFile(filePath string) (string, error) {
	if !w.fileUtils.FileExists(filePath) {
		return "", nil // 文件不存在，无需备份
	}
	
	backupDir := w.config.Output.BackupDir
	if err := w.fileUtils.EnsureDir(backupDir); err != nil {
		return "", fmt.Errorf("创建备份目录失败: %w", err)
	}
	
	// 生成备份文件名
	filename := filepath.Base(filePath)
	timestamp := time.Now().Format("20060102_150405")
	backupName := fmt.Sprintf("%s.%s.bak", filename, timestamp)
	backupPath := filepath.Join(backupDir, backupName)
	
	// 复制文件到备份目录
	if err := w.fileUtils.CopyFile(filePath, backupPath); err != nil {
		return "", fmt.Errorf("复制文件到备份目录失败: %w", err)
	}
	
	return backupPath, nil
}

// RestoreFile 恢复文件
func (w *FileWriter) RestoreFile(backupPath, targetPath string) error {
	if !w.fileUtils.FileExists(backupPath) {
		return fmt.Errorf("备份文件不存在: %s", backupPath)
	}
	
	// 确保目标目录存在
	dir := filepath.Dir(targetPath)
	if err := w.fileUtils.EnsureDir(dir); err != nil {
		return fmt.Errorf("创建目标目录失败: %w", err)
	}
	
	// 复制备份文件到目标位置
	return w.fileUtils.CopyFile(backupPath, targetPath)
}

// CleanBackups 清理备份文件
func (w *FileWriter) CleanBackups(maxAge time.Duration) error {
	backupDir := w.config.Output.BackupDir
	if !w.fileUtils.DirExists(backupDir) {
		return nil // 备份目录不存在
	}
	
	// 获取备份目录下的所有文件
	files, err := w.fileUtils.ListFiles(backupDir, "*.bak")
	if err != nil {
		return fmt.Errorf("列出备份文件失败: %w", err)
	}
	
	cutoff := time.Now().Add(-maxAge)
	
	for _, file := range files {
		modTime, err := w.fileUtils.GetFileModTime(file)
		if err != nil {
			continue // 跳过无法获取修改时间的文件
		}
		
		if modTime.Before(cutoff) {
			if err := w.fileUtils.DeleteFile(file); err != nil {
				fmt.Printf("⚠️  删除过期备份文件失败: %s, %v\n", file, err)
			}
		}
	}
	
	return nil
}

// ValidateFiles 验证文件
func (w *FileWriter) ValidateFiles(files map[string]string) []ValidationError {
	var errors []ValidationError
	
	for filePath, content := range files {
		// 验证文件路径
		if err := w.validateFilePath(filePath); err != nil {
			errors = append(errors, ValidationError{
				FilePath: filePath,
				Type:     "path",
				Message:  err.Error(),
			})
			continue
		}
		
		// 验证文件内容
		if err := w.validateFileContent(filePath, content); err != nil {
			errors = append(errors, ValidationError{
				FilePath: filePath,
				Type:     "content",
				Message:  err.Error(),
			})
		}
	}
	
	return errors
}

// ValidationError 验证错误
type ValidationError struct {
	FilePath string
	Type     string
	Message  string
}

// validateFilePath 验证文件路径
func (w *FileWriter) validateFilePath(filePath string) error {
	// 检查路径是否为空
	if filePath == "" {
		return fmt.Errorf("文件路径不能为空")
	}
	
	// 检查路径是否为绝对路径或相对路径
	if filepath.IsAbs(filePath) {
		return fmt.Errorf("不允许使用绝对路径: %s", filePath)
	}
	
	// 检查路径是否包含危险字符
	if strings.Contains(filePath, "..") {
		return fmt.Errorf("路径不能包含 '..' : %s", filePath)
	}
	
	// 检查文件扩展名
	ext := filepath.Ext(filePath)
	allowedExts := []string{".go", ".yaml", ".yml", ".json", ".md", ".txt"}
	isAllowed := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			isAllowed = true
			break
		}
	}
	
	if !isAllowed {
		return fmt.Errorf("不支持的文件扩展名: %s", ext)
	}
	
	return nil
}

// validateFileContent 验证文件内容
func (w *FileWriter) validateFileContent(filePath, content string) error {
	// 检查内容是否为空
	if strings.TrimSpace(content) == "" {
		return fmt.Errorf("文件内容不能为空")
	}
	
	// 如果是Go文件，验证语法
	if strings.HasSuffix(filePath, ".go") {
		if err := w.fileUtils.ValidateGoCode(content); err != nil {
			return fmt.Errorf("Go代码语法错误: %w", err)
		}
	}
	
	return nil
}

// GetFileStats 获取文件统计信息
func (w *FileWriter) GetFileStats(results []WriteResult) FileStats {
	stats := FileStats{}
	
	for _, result := range results {
		if result.Error != nil {
			stats.ErrorCount++
			continue
		}
		
		stats.TotalCount++
		stats.TotalSize += result.Size
		
		if result.IsNew {
			stats.NewCount++
		} else if result.IsOverwrite {
			stats.OverwriteCount++
		}
		
		if result.BackupPath != "" {
			stats.BackupCount++
		}
	}
	
	return stats
}

// FileStats 文件统计信息
type FileStats struct {
	TotalCount     int
	NewCount       int
	OverwriteCount int
	BackupCount    int
	ErrorCount     int
	TotalSize      int64
}

// getFilePermission 获取文件权限
func (w *FileWriter) getFilePermission() (fs.FileMode, error) {
	permStr := w.config.Output.FilePermission
	if permStr == "" {
		return 0644, nil
	}
	
	// 解析权限字符串
	perm, err := strconv.ParseUint(permStr, 8, 32)
	if err != nil {
		return 0644, fmt.Errorf("无效的文件权限: %s", permStr)
	}
	
	return fs.FileMode(perm), nil
}

// CreateDirectory 创建目录
func (w *FileWriter) CreateDirectory(path string) error {
	return w.fileUtils.EnsureDir(path)
}

// DeleteFile 删除文件
func (w *FileWriter) DeleteFile(path string) error {
	return w.fileUtils.DeleteFile(path)
}

// MoveFile 移动文件
func (w *FileWriter) MoveFile(src, dst string) error {
	return w.fileUtils.MoveFile(src, dst)
}

// CopyFile 复制文件
func (w *FileWriter) CopyFile(src, dst string) error {
	return w.fileUtils.CopyFile(src, dst)
}

// GetFileSize 获取文件大小
func (w *FileWriter) GetFileSize(path string) (int64, error) {
	return w.fileUtils.GetFileSize(path)
}

// FileExists 检查文件是否存在
func (w *FileWriter) FileExists(path string) bool {
	return w.fileUtils.FileExists(path)
}