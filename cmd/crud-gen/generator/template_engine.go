package generator

import (
	"bytes"
	"fmt"
	"os"
	"strings"
	"text/template"

	"admin/cmd/crud-gen/config"
	"admin/cmd/crud-gen/utils"
)

// TemplateEngine 模板引擎
type TemplateEngine struct {
	config    *config.Config
	templates map[string]*template.Template
	funcMap   template.FuncMap
}

// NewTemplateEngine 创建模板引擎
func NewTemplateEngine(cfg *config.Config) *TemplateEngine {
	engine := &TemplateEngine{
		config:    cfg,
		templates: make(map[string]*template.Template),
	}

	// 初始化模板函数
	engine.initFuncMap()

	return engine
}

// initFuncMap 初始化模板函数映射
func (e *TemplateEngine) initFuncMap() {
	e.funcMap = template.FuncMap{
		// 命名转换函数
		"toSnakeCase":  utils.ToSnakeCase,
		"toCamelCase":  utils.ToCamelCase,
		"toPascalCase": utils.ToPascalCase,
		"toKebabCase":  utils.ToKebabCase,
		"pluralize":    utils.Pluralize,
		"singularize":  utils.Singularize,
		"title":        utils.Title,
		"lower":        utils.Lower,
		"upper":        utils.Upper,

		// 字符串处理函数
		"join":      strings.Join,
		"split":     strings.Split,
		"contains":  strings.Contains,
		"hasPrefix": strings.HasPrefix,
		"hasSuffix": strings.HasSuffix,
		"trim":      strings.TrimSpace,
		"replace":   strings.ReplaceAll,

		// 类型判断函数
		"isStringType":  e.isStringType,
		"isNumberType":  e.isNumberType,
		"isTimeType":    e.isTimeType,
		"isBoolType":    e.isBoolType,
		"isPointerType": e.isPointerType,
		"isSliceType":   e.isSliceType,

		// 特性判断函数
		"hasFeature":        e.hasFeature,
		"isSearchableField": e.isSearchableField,
		"isRequiredField":   e.isRequiredField,
		"isSortableField":   e.isSortableField,
		"isFilterableField": e.isFilterableField,

		// 代码生成辅助函数
		"generateValidationTags": e.generateValidationTags,
		"generateJSONTags":       e.generateJSONTags,
		"generateGORMTags":       e.generateGORMTags,
		"generateImports":        e.generateImports,
		"generateComment":        e.generateComment,

		// 集合操作函数
		"first": e.first,
		"last":  e.last,
		"slice": e.slice,
		"len":   e.length,
		"add":   e.add,
		"sub":   e.sub,
		"mul":   e.mul,
		"div":   e.div,

		// 条件函数
		"eq":  e.eq,
		"ne":  e.ne,
		"lt":  e.lt,
		"le":  e.le,
		"gt":  e.gt,
		"ge":  e.ge,
		"and": e.and,
		"or":  e.or,
		"not": e.not,

		// 格式化函数
		"indent":     e.indent,
		"quote":      e.quote,
		"unquote":    e.unquote,
		"escape":     e.escape,
		"unescape":   e.unescape,
		"formatCode": e.formatCode,
	}
}

// LoadTemplates 加载所有模板
func (e *TemplateEngine) LoadTemplates() error {
	templateTypes := []string{
		"controller", "service", "repository",
		"api_struct", "routes", "test", "wire_set",
	}

	for _, templateType := range templateTypes {
		if err := e.loadTemplate(templateType); err != nil {
			return fmt.Errorf("加载模板 %s 失败: %w", templateType, err)
		}
	}

	// 加载自定义模板
	for name := range e.config.Templates.CustomTemplates {
		if err := e.loadTemplate(name); err != nil {
			return fmt.Errorf("加载自定义模板 %s 失败: %w", name, err)
		}
	}

	return nil
}

// loadTemplate 加载单个模板
func (e *TemplateEngine) loadTemplate(templateType string) error {
	templatePath := e.config.GetTemplateFile(templateType)
	if templatePath == "" {
		return fmt.Errorf("模板类型 %s 未配置", templateType)
	}

	// 尝试相对于当前工作目录的路径
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		// 尝试相对于项目根目录的路径
		rootTemplatePath := "../../" + templatePath
		if _, err := os.Stat(rootTemplatePath); err == nil {
			templatePath = rootTemplatePath
			fmt.Printf("✅ 加载外部模板: %s\n", templatePath)
		} else {
			// 如果模板文件不存在，使用内置模板
			fmt.Printf("⚠️  模板文件不存在，使用内置模板: %s\n", templatePath)
			return e.loadBuiltinTemplate(templateType)
		}
	} else {
		fmt.Printf("✅ 加载外部模板: %s\n", templatePath)
	}

	// 读取模板文件
	content, err := os.ReadFile(templatePath)
	if err != nil {
		return fmt.Errorf("读取模板文件失败: %w", err)
	}

	// 解析模板
	tmpl, err := template.New(templateType).Funcs(e.funcMap).Parse(string(content))
	if err != nil {
		return fmt.Errorf("解析模板失败: %w", err)
	}

	e.templates[templateType] = tmpl
	return nil
}

// loadBuiltinTemplate 加载内置模板
func (e *TemplateEngine) loadBuiltinTemplate(templateType string) error {
	var content string

	switch templateType {
	case "controller":
		content = e.getSmartControllerTemplate()
	case "service":
		content = e.getSmartServiceTemplate()
	case "repository":
		content = e.getSmartRepositoryTemplate()
	case "api_struct":
		content = e.getSmartAPIStructTemplate()
	case "routes":
		content = e.getBuiltinRoutesTemplate()
	case "test":
		content = e.getBuiltinTestTemplate()
	case "wire_set":
		content = e.getBuiltinWireSetTemplate()
	default:
		return fmt.Errorf("未知的内置模板类型: %s", templateType)
	}

	// 解析内置模板
	tmpl, err := template.New(templateType).Funcs(e.funcMap).Parse(content)
	if err != nil {
		return fmt.Errorf("解析内置模板失败: %w", err)
	}

	e.templates[templateType] = tmpl
	return nil
}

// RenderTemplate 渲染模板
func (e *TemplateEngine) RenderTemplate(templateType string, data interface{}) (string, error) {
	tmpl, exists := e.templates[templateType]
	if !exists {
		return "", fmt.Errorf("模板 %s 未加载", templateType)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("渲染模板失败: %w", err)
	}

	result := buf.String()

	// 格式化Go代码
	if strings.HasSuffix(templateType, ".go") ||
		templateType == "controller" || templateType == "service" ||
		templateType == "repository" || templateType == "api_struct" {
		formatted, err := e.formatGoCode(result)
		if err != nil {
			// 如果格式化失败，返回原始代码并记录警告
			fmt.Printf("⚠️  格式化代码失败: %v\n", err)
			return result, nil
		}
		return formatted, nil
	}

	return result, nil
}

// ReloadTemplate 重新加载模板
func (e *TemplateEngine) ReloadTemplate(templateType string) error {
	delete(e.templates, templateType)
	return e.loadTemplate(templateType)
}

// GetTemplateNames 获取所有已加载的模板名称
func (e *TemplateEngine) GetTemplateNames() []string {
	var names []string
	for name := range e.templates {
		names = append(names, name)
	}
	return names
}

// ValidateTemplate 验证模板语法
func (e *TemplateEngine) ValidateTemplate(templateType, content string) error {
	_, err := template.New(templateType).Funcs(e.funcMap).Parse(content)
	return err
}

// 模板函数实现

// 类型判断函数
func (e *TemplateEngine) isStringType(t string) bool {
	return strings.Contains(t, "string")
}

func (e *TemplateEngine) isNumberType(t string) bool {
	numberTypes := []string{
		"int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64", "float32", "float64",
	}
	for _, nt := range numberTypes {
		if strings.Contains(t, nt) {
			return true
		}
	}
	return false
}

func (e *TemplateEngine) isTimeType(t string) bool {
	return strings.Contains(t, "time.Time")
}

func (e *TemplateEngine) isBoolType(t string) bool {
	return strings.Contains(t, "bool")
}

func (e *TemplateEngine) isPointerType(t string) bool {
	return strings.HasPrefix(t, "*")
}

func (e *TemplateEngine) isSliceType(t string) bool {
	return strings.HasPrefix(t, "[]")
}

// 特性判断函数
func (e *TemplateEngine) hasFeature(features interface{}, feature string) bool {
	// 实现特性检查逻辑
	return false
}

func (e *TemplateEngine) isSearchableField(field interface{}) bool {
	// 实现可搜索字段检查逻辑
	return false
}

func (e *TemplateEngine) isRequiredField(field interface{}) bool {
	// 实现必填字段检查逻辑
	return false
}

func (e *TemplateEngine) isSortableField(field interface{}) bool {
	// 实现可排序字段检查逻辑
	return false
}

func (e *TemplateEngine) isFilterableField(field interface{}) bool {
	// 实现可过滤字段检查逻辑
	return false
}

// 代码生成辅助函数
func (e *TemplateEngine) generateValidationTags(field interface{}) string {
	// 实现验证标签生成逻辑
	return ""
}

func (e *TemplateEngine) generateJSONTags(field interface{}) string {
	// 实现JSON标签生成逻辑
	return ""
}

func (e *TemplateEngine) generateGORMTags(field interface{}) string {
	// 实现GORM标签生成逻辑
	return ""
}

func (e *TemplateEngine) generateImports(data interface{}) []string {
	// 实现导入生成逻辑
	return []string{}
}

func (e *TemplateEngine) generateComment(text string) string {
	if text == "" {
		return ""
	}
	return "// " + text
}

// 集合操作函数
func (e *TemplateEngine) first(slice interface{}) interface{} {
	// 实现获取第一个元素逻辑
	return nil
}

func (e *TemplateEngine) last(slice interface{}) interface{} {
	// 实现获取最后一个元素逻辑
	return nil
}

func (e *TemplateEngine) slice(slice interface{}, start, end int) interface{} {
	// 实现切片操作逻辑
	return nil
}

func (e *TemplateEngine) length(slice interface{}) int {
	// 实现长度计算逻辑
	return 0
}

// 数学运算函数
func (e *TemplateEngine) add(a, b int) int { return a + b }
func (e *TemplateEngine) sub(a, b int) int { return a - b }
func (e *TemplateEngine) mul(a, b int) int { return a * b }
func (e *TemplateEngine) div(a, b int) int {
	if b == 0 {
		return 0
	}
	return a / b
}

// 比较函数
func (e *TemplateEngine) eq(a, b interface{}) bool { return a == b }
func (e *TemplateEngine) ne(a, b interface{}) bool { return a != b }
func (e *TemplateEngine) lt(a, b int) bool         { return a < b }
func (e *TemplateEngine) le(a, b int) bool         { return a <= b }
func (e *TemplateEngine) gt(a, b int) bool         { return a > b }
func (e *TemplateEngine) ge(a, b int) bool         { return a >= b }
func (e *TemplateEngine) and(a, b bool) bool       { return a && b }
func (e *TemplateEngine) or(a, b bool) bool        { return a || b }
func (e *TemplateEngine) not(a bool) bool          { return !a }

// 格式化函数
func (e *TemplateEngine) indent(text string, spaces int) string {
	indent := strings.Repeat(" ", spaces)
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		if strings.TrimSpace(line) != "" {
			lines[i] = indent + line
		}
	}
	return strings.Join(lines, "\n")
}

func (e *TemplateEngine) quote(text string) string {
	return fmt.Sprintf(`"%s"`, text)
}

func (e *TemplateEngine) unquote(text string) string {
	return strings.Trim(text, `"`)
}

func (e *TemplateEngine) escape(text string) string {
	return strings.ReplaceAll(text, `"`, `\"`)
}

func (e *TemplateEngine) unescape(text string) string {
	return strings.ReplaceAll(text, `\"`, `"`)
}

func (e *TemplateEngine) formatCode(code string) string {
	fileUtils := utils.NewFileUtils()
	formatted, err := fileUtils.FormatGoCode(code)
	if err != nil {
		return code
	}
	return formatted
}

func (e *TemplateEngine) formatGoCode(code string) (string, error) {
	fileUtils := utils.NewFileUtils()
	return fileUtils.FormatGoCode(code)
}
