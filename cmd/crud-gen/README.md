# N-Admin CRUD Generator 使用文档

## 📖 简介

N-Admin CRUD Generator 是一个强大的Go语言CRUD代码生成器，能够根据数据库模型自动生成完整的Controller、Service、Repository层代码，以及API结构体和Swagger文档。

## 🚀 快速开始

### 安装

```bash
# 编译生成器
cd cmd/crud-gen
go build -o crud-gen main.go
```

### 基本使用

```bash
# 生成指定模型的所有代码
./crud-gen --models=User,Dict --all

# 生成指定模型的特定层级
./crud-gen --models=User --layers=controller,service

# 强制覆盖已存在的文件
./crud-gen --models=Dict --all --force

# 交互式模式
./crud-gen --interactive
```

## ⚙️ 配置文件详解

配置文件位置：`config/crud_generator.yaml`

### 1. Generator 配置

```yaml
generator:
  model_dir: internal/model                    # 模型文件目录
  exclude_models:                              # 排除的模型列表
    - CasbinRule                              # 不生成Casbin规则模型
    - Migration                               # 不生成迁移模型
  include_models: []                          # 包含的模型列表（为空表示包含所有非排除模型）
  exclude_fields:                             # 排除的字段列表
    - Password                                # 不在API中暴露密码字段
    - Salt                                    # 不在API中暴露盐值字段
  generate_controller: true                   # 是否生成Controller层
  generate_service: true                      # 是否生成Service层
  generate_repository: true                   # 是否生成Repository层
  generate_api_structs: true                  # 是否生成API结构体
  generate_routes: true                       # 是否生成路由配置
  generate_tests: false                       # 是否生成测试文件
  generate_wire_set: true                     # 是否生成Wire依赖注入配置
  package_name: admin                         # 包名
  module_name: admin                          # 模块名
  author: ""                                  # 作者信息
  copyright: ""                               # 版权信息
```

**字段说明：**
- `model_dir`: 指定模型文件所在目录，生成器会扫描此目录下的所有.go文件
- `exclude_models`: 不需要生成CRUD代码的模型名称列表
- `include_models`: 只生成指定模型的CRUD代码（优先级高于exclude_models）
- `exclude_fields`: 在API结构体中排除的字段名称
- `generate_*`: 控制生成哪些层级的代码
- `package_name`: 生成代码的包名
- `module_name`: Go模块名，用于import路径

### 2. Templates 配置

```yaml
templates:
  template_dir: templates/crud                # 模板文件目录
  controller: controller.tmpl                 # Controller模板文件名
  service: service.tmpl                       # Service模板文件名
  repository: repository.tmpl                 # Repository模板文件名
  api_struct: api_struct.tmpl                # API结构体模板文件名
  routes: routes.tmpl                         # 路由模板文件名
  test: test.tmpl                            # 测试模板文件名
  wire_set: wire_set.tmpl                    # Wire配置模板文件名
  custom_templates: {}                        # 自定义模板配置
```

**字段说明：**
- `template_dir`: 模板文件存放目录
- 各个模板文件名：可以自定义模板文件名称
- `custom_templates`: 可以添加自定义模板

### 3. Output 配置

```yaml
output:
  controller_dir: internal/controller         # Controller文件输出目录
  service_dir: internal/service              # Service文件输出目录
  repository_dir: internal/repository        # Repository文件输出目录
  api_dir: api/v1                            # API结构体文件输出目录
  routes_file: internal/server/http_generated.go  # 路由文件输出路径
  test_dir: test/generated                   # 测试文件输出目录
  wire_file: internal/wire/wire_generated.go # Wire配置文件输出路径
  backup_dir: .crud_backup                   # 备份目录
  file_permission: "0644"                    # 生成文件的权限
```

**字段说明：**
- 各个`*_dir`: 指定不同层级代码的输出目录
- `routes_file`: 路由配置文件的完整路径
- `backup_dir`: 覆盖文件前的备份目录
- `file_permission`: 生成文件的Unix权限

### 4. Features 配置

```yaml
features:
  enable_cache: true                          # 启用缓存功能
  enable_validation: true                     # 启用数据验证
  enable_soft_delete: true                    # 启用软删除
  enable_audit: true                          # 启用审计日志
  enable_tenant: true                         # 启用多租户
  enable_tree: true                          # 启用树形结构
  enable_status: true                         # 启用状态管理
  enable_pagination: true                     # 启用分页查询
  enable_search: true                         # 启用搜索功能
  enable_sort: true                          # 启用排序功能
  enable_export: false                       # 启用导出功能
  enable_import: false                       # 启用导入功能
  enable_batch: true                         # 启用批量操作
  enable_transaction: true                   # 启用事务支持
```

**功能说明：**
- `enable_cache`: 在Service层添加Redis缓存逻辑
- `enable_validation`: 在Service层添加数据验证逻辑
- `enable_soft_delete`: 支持软删除（逻辑删除）
- `enable_audit`: 自动记录创建时间、更新时间等审计信息
- `enable_tenant`: 支持多租户数据隔离
- `enable_tree`: 支持树形结构数据（如菜单、分类等）
- `enable_status`: 支持状态管理（启用/禁用）
- `enable_pagination`: 支持分页查询
- `enable_search`: 支持条件搜索
- `enable_sort`: 支持排序功能
- `enable_export`: 支持数据导出（Excel等）
- `enable_import`: 支持数据导入
- `enable_batch`: 支持批量操作（批量删除、批量更新状态等）
- `enable_transaction`: 支持数据库事务

### 5. Code Style 配置

```yaml
code_style:
  naming:
    controller_suffix: Controller             # Controller类名后缀
    service_suffix: Service                   # Service类名后缀
    repository_suffix: Repository             # Repository类名后缀
    interface_suffix: ""                      # 接口名后缀
    test_suffix: _test                        # 测试文件后缀
  comments:
    controller: '{{.ModelComment}}控制器'      # Controller注释模板
    service: '{{.ModelComment}}服务'          # Service注释模板
    repository: '{{.ModelComment}}仓储'       # Repository注释模板
    api_struct: '{{.ModelComment}}API结构体'  # API结构体注释模板
  formatting:
    indent: "\t"                              # 缩进字符（制表符）
    line_length: 120                          # 行长度限制
    sort_imports: true                        # 是否排序import语句
```

**字段说明：**
- `naming`: 控制生成代码的命名规范
- `comments`: 控制生成代码的注释模板
- `formatting`: 控制代码格式化规则

### 6. Field Mappings 配置

```yaml
field_mappings:
  string:
    type: ""                                  # 字段类型（空表示使用原类型）
    validation: omitempty,max=255             # 验证规则
    json_tag: ""                             # JSON标签（空表示使用字段名）
    gorm_tag: ""                             # GORM标签
    is_searchable: true                       # 是否可搜索
    is_required: false                        # 是否必填
    is_sortable: true                         # 是否可排序
    custom_rules: {}                          # 自定义规则
  int64:
    validation: omitempty,gte=0
    is_searchable: false
    is_sortable: true
  time.Time:
    validation: omitempty
    is_searchable: false
    is_sortable: true
  email:
    validation: omitempty,email
    is_searchable: true
    is_sortable: false
  phone:
    validation: omitempty,len=11,numeric
    is_searchable: true
    is_sortable: false
  password:
    validation: required,min=6,max=20
    json_tag: '-'                            # 不在JSON中序列化
    is_searchable: false
    is_sortable: false
```

**字段说明：**
- 为不同类型的字段定义默认的验证规则和行为
- `validation`: Gin的binding验证规则
- `is_searchable`: 字段是否出现在搜索条件中
- `is_required`: 字段是否为必填
- `is_sortable`: 字段是否支持排序

### 7. Validation 配置

```yaml
validation:
  enable_custom_validators: true              # 启用自定义验证器
  custom_validators:                          # 自定义验证器列表
    - unique                                  # 唯一性验证
    - exists                                  # 存在性验证
    - enum                                    # 枚举值验证
```

### 8. Database 配置

```yaml
database:
  driver: mysql                               # 数据库驱动
  connection_string: ""                       # 连接字符串
  max_open_conns: 100                        # 最大打开连接数
  max_idle_conns: 10                         # 最大空闲连接数
```

## 🎯 命令行参数

```bash
./crud-gen [选项]

选项：
  --models string         要生成的模型列表，逗号分隔 (如: User,Dict,Role)
  --layers string         要生成的层级，逗号分隔 (controller,service,repository,api,routes)
  --all                   生成所有层级
  --force                 强制覆盖已存在的文件
  --config string         配置文件路径 (默认: config/crud_generator.yaml)
  --output string         输出目录
  --template-dir string   自定义模板目录
  --backup                生成前备份现有文件
  --dry-run              模拟运行，显示将要生成的文件
  --preview              预览模式，不实际生成文件
  --interactive          交互式模式
  --verbose              详细输出
  --version              显示版本信息
  --help                 显示帮助信息
  --init                 初始化配置文件
```

## 📝 使用示例

### 1. 生成单个模型的所有代码

```bash
./crud-gen --models=User --all --force
```

### 2. 生成多个模型的特定层级

```bash
./crud-gen --models=User,Dict,Role --layers=controller,service --force
```

### 3. 使用自定义配置文件

```bash
./crud-gen --models=User --all --config=custom_config.yaml
```

### 4. 预览模式（不实际生成文件）

```bash
./crud-gen --models=User --all --preview
```

### 5. 交互式模式

```bash
./crud-gen --interactive
```

## 🏗️ 生成的代码结构

```
project/
├── api/v1/                     # API结构体
│   ├── user.go                # 用户API结构体
│   ├── dict.go                # 字典API结构体
│   └── public.go              # 公共结构体
├── internal/
│   ├── controller/            # 控制器层
│   │   ├── user.go           # 用户控制器
│   │   └── dict.go           # 字典控制器
│   ├── service/               # 服务层
│   │   ├── user.go           # 用户服务
│   │   └── dict.go           # 字典服务
│   ├── repository/            # 仓储层
│   │   ├── user.go           # 用户仓储
│   │   └── dict.go           # 字典仓储
│   ├── server/
│   │   └── http_generated.go # 生成的路由配置
│   └── wire/
│       └── wire_generated.go # 生成的依赖注入配置
└── test/generated/            # 测试文件
    ├── user_test.go
    └── dict_test.go
```

## 🎨 自定义模板

你可以创建自定义模板来满足特定需求：

1. 复制默认模板到自定义目录
2. 修改模板内容
3. 在配置文件中指定自定义模板目录

模板使用Go的text/template语法，可用变量：
- `{{.ModelName}}`: 模型名称
- `{{.ModelComment}}`: 模型注释
- `{{.Fields}}`: 字段列表
- `{{.Features}}`: 功能特性
- `{{.Config}}`: 配置信息

## 🔧 高级功能

### 1. 代码质量分析

生成器内置了代码质量分析功能：

```bash
./crud-gen --models=User --analyze
```

### 2. 文档生成

自动生成API文档：

```bash
./crud-gen --models=User --docs
```

### 3. 数据库迁移

生成数据库迁移文件：

```bash
./crud-gen --models=User --migration
```

## 🐛 故障排除

### 常见问题

1. **模型文件找不到**
   - 检查 `model_dir` 配置是否正确
   - 确保模型文件存在且为有效的Go文件

2. **生成的代码编译错误**
   - 检查模型定义是否正确
   - 确保所有依赖包已安装

3. **权限错误**
   - 检查输出目录的写权限
   - 使用 `--force` 参数覆盖已存在的文件

### 调试模式

使用 `--verbose` 参数查看详细的生成过程：

```bash
./crud-gen --models=User --all --verbose
```

## 📚 更多资源

- [模板开发指南](./docs/template-guide.md)
- [API文档规范](./docs/api-docs.md)
- [最佳实践](./docs/best-practices.md)
- [更新日志](./CHANGELOG.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License