package features

import (
	   "bytes"
	   "encoding/json"
	   "fmt"
	   "go/ast"
	   "go/parser"
	   "go/token"
	   "html/template"
	   "os"
	   "path/filepath"
	   "regexp"
	   "strings"
	   "time"
	   "strconv"

	"admin/cmd/crud-gen/config"
	"admin/cmd/crud-gen/generator"
)

// AdvancedFeatures 高级功能管理器
type AdvancedFeatures struct {
	config          *config.Config
	codeAnalyzer    *CodeAnalyzer
	dependencyGraph *DependencyGraph
	migrationGen    *MigrationGenerator
	docGenerator    *DocumentationGenerator
	qualityChecker  *QualityChecker
}

// CodeAnalyzer 代码分析器
type CodeAnalyzer struct {
	metrics CodeMetrics
}

// CodeMetrics 代码指标
type CodeMetrics struct {
	LinesOfCode          int                `json:"linesOfCode"`
	CyclomaticComplexity int                `json:"cyclomaticComplexity"`
	TestCoverage         float64            `json:"testCoverage"`
	Duplications         []DuplicationInfo  `json:"duplications"`
	Dependencies         []DependencyInfo   `json:"dependencies"`
	SecurityIssues       []SecurityIssue    `json:"securityIssues"`
	Performance          PerformanceMetrics `json:"performance"`
}

// DuplicationInfo 重复代码信息
type DuplicationInfo struct {
	File1      string  `json:"file1"`
	File2      string  `json:"file2"`
	Lines      int     `json:"lines"`
	Similarity float64 `json:"similarity"`
}

// DependencyInfo 依赖信息
type DependencyInfo struct {
	Name    string `json:"name"`
	Version string `json:"version"`
	Type    string `json:"type"` // direct, indirect
	License string `json:"license"`
}

// SecurityIssue 安全问题
type SecurityIssue struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"`
	File        string `json:"file"`
	Line        int    `json:"line"`
	Description string `json:"description"`
	Solution    string `json:"solution"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	MemoryUsage  int64   `json:"memoryUsage"`
	CPUUsage     float64 `json:"cpuUsage"`
	ResponseTime int64   `json:"responseTime"`
	Throughput   float64 `json:"throughput"`
}

// DependencyGraph 依赖关系图
type DependencyGraph struct {
	Nodes []GraphNode `json:"nodes"`
	Edges []GraphEdge `json:"edges"`
}

// GraphNode 图节点
type GraphNode struct {
	ID       string            `json:"id"`
	Label    string            `json:"label"`
	Type     string            `json:"type"`
	Metadata map[string]string `json:"metadata"`
}

// GraphEdge 图边
type GraphEdge struct {
	From   string `json:"from"`
	To     string `json:"to"`
	Type   string `json:"type"`
	Weight int    `json:"weight"`
}

// MigrationGenerator 数据库迁移生成器
type MigrationGenerator struct {
	config *config.Config
}

// Migration 数据库迁移
type Migration struct {
	Version     string    `json:"version"`
	Name        string    `json:"name"`
	UpSQL       string    `json:"upSQL"`
	DownSQL     string    `json:"downSQL"`
	CreatedAt   time.Time `json:"createdAt"`
	Description string    `json:"description"`
}

// DocumentationGenerator 文档生成器
type DocumentationGenerator struct {
	config *config.Config
}

// Documentation 文档
type Documentation struct {
	Title       string               `json:"title"`
	Version     string               `json:"version"`
	Description string               `json:"description"`
	APIs        []APIDocumentation   `json:"apis"`
	Models      []ModelDocumentation `json:"models"`
	Examples    []ExampleCode        `json:"examples"`
	Changelog   []ChangelogEntry     `json:"changelog"`
}

// APIDocumentation API文档
type APIDocumentation struct {
	Path        string           `json:"path"`
	Method      string           `json:"method"`
	Summary     string           `json:"summary"`
	Description string           `json:"description"`
	Parameters  []ParameterDoc   `json:"parameters"`
	Responses   []ResponseDoc    `json:"responses"`
	Examples    []RequestExample `json:"examples"`
	Tags        []string         `json:"tags"`
}

// ParameterDoc 参数文档
type ParameterDoc struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Required    bool   `json:"required"`
	Description string `json:"description"`
	Example     string `json:"example"`
}

// ResponseDoc 响应文档
type ResponseDoc struct {
	Code        int    `json:"code"`
	Description string `json:"description"`
	Schema      string `json:"schema"`
	Example     string `json:"example"`
}

// RequestExample 请求示例
type RequestExample struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Request     string `json:"request"`
	Response    string `json:"response"`
}

// ModelDocumentation 模型文档
type ModelDocumentation struct {
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Fields      []FieldDoc    `json:"fields"`
	Relations   []RelationDoc `json:"relations"`
	Examples    []string      `json:"examples"`
}

// FieldDoc 字段文档
type FieldDoc struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Required    bool   `json:"required"`
	Description string `json:"description"`
	Validation  string `json:"validation"`
	Example     string `json:"example"`
}

// RelationDoc 关系文档
type RelationDoc struct {
	Type        string `json:"type"`
	Model       string `json:"model"`
	Description string `json:"description"`
}

// ExampleCode 示例代码
type ExampleCode struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Language    string `json:"language"`
	Code        string `json:"code"`
	Output      string `json:"output"`
}

// ChangelogEntry 变更日志条目
type ChangelogEntry struct {
	Version    string    `json:"version"`
	Date       time.Time `json:"date"`
	Changes    []string  `json:"changes"`
	Breaking   []string  `json:"breaking"`
	Deprecated []string  `json:"deprecated"`
}

// QualityChecker 质量检查器
type QualityChecker struct {
	config *config.Config
	rules  []QualityRule
}

// QualityRule 质量规则
type QualityRule struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Severity    string `json:"severity"`
	Category    string `json:"category"`
	Enabled     bool   `json:"enabled"`
}

// QualityReport 质量报告
type QualityReport struct {
	Score       float64        `json:"score"`
	Grade       string         `json:"grade"`
	Issues      []QualityIssue `json:"issues"`
	Suggestions []string       `json:"suggestions"`
	Metrics     CodeMetrics    `json:"metrics"`
	Timestamp   time.Time      `json:"timestamp"`
}

// QualityIssue 质量问题
type QualityIssue struct {
	RuleID     string `json:"ruleId"`
	File       string `json:"file"`
	Line       int    `json:"line"`
	Column     int    `json:"column"`
	Message    string `json:"message"`
	Severity   string `json:"severity"`
	Category   string `json:"category"`
	Suggestion string `json:"suggestion"`
}

// NewAdvancedFeatures 创建高级功能管理器
func NewAdvancedFeatures(cfg *config.Config) *AdvancedFeatures {
	qualityChecker := &QualityChecker{
		config: cfg,
		rules: []QualityRule{
			{
				ID:          "function_length",
				Name:        "函数长度检查",
				Description: "检查函数是否过长",
				Severity:    "medium",
				Category:    "maintainability",
				Enabled:     true,
			},
			{
				ID:          "cyclomatic_complexity",
				Name:        "圈复杂度检查",
				Description: "检查函数圈复杂度是否过高",
				Severity:    "high",
				Category:    "maintainability",
				Enabled:     true,
			},
			{
				ID:          "naming_convention",
				Name:        "命名规范检查",
				Description: "检查是否遵循Go语言命名规范",
				Severity:    "low",
				Category:    "style",
				Enabled:     true,
			},
			{
				ID:          "comment_coverage",
				Name:        "注释覆盖率检查",
				Description: "检查导出函数是否有注释",
				Severity:    "medium",
				Category:    "documentation",
				Enabled:     true,
			},
			{
				ID:          "error_handling",
				Name:        "错误处理检查",
				Description: "检查是否正确处理错误",
				Severity:    "high",
				Category:    "reliability",
				Enabled:     true,
			},
		},
	}

	return &AdvancedFeatures{
		config:          cfg,
		codeAnalyzer:    &CodeAnalyzer{},
		dependencyGraph: &DependencyGraph{},
		migrationGen:    &MigrationGenerator{config: cfg},
		docGenerator:    &DocumentationGenerator{config: cfg},
		qualityChecker:  qualityChecker,
	}
}

// AnalyzeCode 分析代码质量
func (af *AdvancedFeatures) AnalyzeCode(projectPath string) (*CodeMetrics, error) {
	metrics := &CodeMetrics{}

	// 分析代码行数
	loc, err := af.countLinesOfCode(projectPath)
	if err != nil {
		return nil, fmt.Errorf("统计代码行数失败: %w", err)
	}
	metrics.LinesOfCode = loc

	// 分析圈复杂度
	complexity, err := af.calculateComplexity(projectPath)
	if err != nil {
		return nil, fmt.Errorf("计算圈复杂度失败: %w", err)
	}
	metrics.CyclomaticComplexity = complexity

	// 检查重复代码
	duplications, err := af.findDuplications(projectPath)
	if err != nil {
		return nil, fmt.Errorf("检查重复代码失败: %w", err)
	}
	metrics.Duplications = duplications

	// 分析依赖关系
	dependencies, err := af.analyzeDependencies(projectPath)
	if err != nil {
		return nil, fmt.Errorf("分析依赖关系失败: %w", err)
	}
	metrics.Dependencies = dependencies

	// 安全检查
	securityIssues, err := af.checkSecurity(projectPath)
	if err != nil {
		return nil, fmt.Errorf("安全检查失败: %w", err)
	}
	metrics.SecurityIssues = securityIssues

	return metrics, nil
}

// GenerateMigrations 生成数据库迁移
func (af *AdvancedFeatures) GenerateMigrations(models []generator.ModelInfo) ([]Migration, error) {
	var migrations []Migration

	for _, model := range models {
		migration, err := af.migrationGen.generateModelMigration(model)
		if err != nil {
			return nil, fmt.Errorf("生成模型 %s 迁移失败: %w", model.Name, err)
		}
		migrations = append(migrations, migration)
	}

	return migrations, nil
}

// GenerateDocumentation 生成文档
func (af *AdvancedFeatures) GenerateDocumentation(models []generator.ModelInfo) (*Documentation, error) {
	doc := &Documentation{
		Title:       af.config.Generator.PackageName + " API Documentation",
		Version:     "1.0.0",
		Description: "自动生成的API文档",
		APIs:        []APIDocumentation{},
		Models:      []ModelDocumentation{},
		Examples:    []ExampleCode{},
		Changelog:   []ChangelogEntry{},
	}

	// 生成API文档
	for _, model := range models {
		apis := af.docGenerator.generateAPIDoc(model)
		doc.APIs = append(doc.APIs, apis...)

		modelDoc := af.docGenerator.generateModelDoc(model)
		doc.Models = append(doc.Models, modelDoc)
	}

	// 生成示例代码
	examples := af.docGenerator.generateExamples(models)
	doc.Examples = examples

	return doc, nil
}

// CheckQuality 检查代码质量
func (af *AdvancedFeatures) CheckQuality(projectPath string) (*QualityReport, error) {
	report := &QualityReport{
		Issues:    []QualityIssue{},
		Timestamp: time.Now(),
	}

	// 运行质量检查规则
	for _, rule := range af.qualityChecker.rules {
		if !rule.Enabled {
			continue
		}

		issues, err := af.runQualityRule(rule, projectPath)
		if err != nil {
			return nil, fmt.Errorf("运行质量规则 %s 失败: %w", rule.ID, err)
		}
		report.Issues = append(report.Issues, issues...)
	}

	// 计算质量分数
	report.Score = af.calculateQualityScore(report.Issues)
	report.Grade = af.getQualityGrade(report.Score)

	// 生成建议
	report.Suggestions = af.generateSuggestions(report.Issues)

	return report, nil
}

// BuildDependencyGraph 构建依赖关系图
func (af *AdvancedFeatures) BuildDependencyGraph(models []generator.ModelInfo) (*DependencyGraph, error) {
	graph := &DependencyGraph{
		Nodes: []GraphNode{},
		Edges: []GraphEdge{},
	}

	// 添加模型节点
	for _, model := range models {
		node := GraphNode{
			ID:    model.Name,
			Label: model.Comment,
			Type:  "model",
			Metadata: map[string]string{
				"tableName": model.TableName,
				"package":   model.Package,
			},
		}
		graph.Nodes = append(graph.Nodes, node)
	}

	// 添加关系边
	for _, model := range models {
		for _, relation := range model.Relations {
			edge := GraphEdge{
				From:   model.Name,
				To:     relation.Model,
				Type:   relation.Type,
				Weight: 1,
			}
			graph.Edges = append(graph.Edges, edge)
		}
	}

	return graph, nil
}

// ExportReport 导出报告
func (af *AdvancedFeatures) ExportReport(report interface{}, format string, outputPath string) error {
	var data []byte
	var err error

	switch format {
	case "json":
		data, err = json.MarshalIndent(report, "", "  ")
	case "html":
		data, err = af.generateHTMLReport(report)
	case "markdown":
		data, err = af.generateMarkdownReport(report)
	default:
		return fmt.Errorf("不支持的格式: %s", format)
	}

	if err != nil {
		return fmt.Errorf("生成报告失败: %w", err)
	}

	// 确保输出目录存在
	dir := filepath.Dir(outputPath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(outputPath, data, 0o644); err != nil {
		return fmt.Errorf("写入报告文件失败: %w", err)
	}

	return nil
}

// 私有方法实现

func (af *AdvancedFeatures) countLinesOfCode(projectPath string) (int, error) {
	totalLines := 0
	
	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 只统计Go文件
		if !info.IsDir() && filepath.Ext(path) == ".go" {
			// 跳过vendor和.git目录
			if filepath.Base(filepath.Dir(path)) == "vendor" || 
			   filepath.Base(filepath.Dir(path)) == ".git" {
				return nil
			}
			
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			
			lines := len(strings.Split(string(content), "\n"))
			totalLines += lines
		}
		
		return nil
	})
	
	return totalLines, err
}

func (af *AdvancedFeatures) calculateComplexity(projectPath string) (int, error) {
	totalComplexity := 0
	
	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 只分析Go文件
		if !info.IsDir() && filepath.Ext(path) == ".go" {
			// 跳过vendor和.git目录
			if filepath.Base(filepath.Dir(path)) == "vendor" || 
			   filepath.Base(filepath.Dir(path)) == ".git" {
				return nil
			}
			
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			
			// 简单的圈复杂度计算：统计控制流语句
			complexity := af.calculateFileComplexity(string(content))
			totalComplexity += complexity
		}
		
		return nil
	})
	
	return totalComplexity, err
}

func (af *AdvancedFeatures) calculateFileComplexity(content string) int {
	complexity := 1 // 基础复杂度
	
	// 统计控制流关键字
	keywords := []string{"if", "for", "switch", "case", "&&", "||", "range"}
	
	for _, keyword := range keywords {
		complexity += strings.Count(content, keyword)
	}
	
	return complexity
}

func (af *AdvancedFeatures) findDuplications(projectPath string) ([]DuplicationInfo, error) {
	var duplications []DuplicationInfo
	fileContents := make(map[string][]string)
	
	// 读取所有Go文件内容
	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if !info.IsDir() && filepath.Ext(path) == ".go" {
			// 跳过vendor和.git目录
			if filepath.Base(filepath.Dir(path)) == "vendor" || 
			   filepath.Base(filepath.Dir(path)) == ".git" {
				return nil
			}
			
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			
			lines := strings.Split(string(content), "\n")
			fileContents[path] = lines
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 简单的重复代码检测：比较文件间的相似行
	for file1, lines1 := range fileContents {
		for file2, lines2 := range fileContents {
			if file1 >= file2 { // 避免重复比较
				continue
			}
			
			similarity := af.calculateSimilarity(lines1, lines2)
			if similarity > 0.8 { // 80%以上相似度认为是重复
				duplications = append(duplications, DuplicationInfo{
					File1:      file1,
					File2:      file2,
					Lines:      min(len(lines1), len(lines2)),
					Similarity: similarity,
				})
			}
		}
	}
	
	return duplications, nil
}

func (af *AdvancedFeatures) calculateSimilarity(lines1, lines2 []string) float64 {
	if len(lines1) == 0 || len(lines2) == 0 {
		return 0
	}
	
	matches := 0
	maxLen := max(len(lines1), len(lines2))
	minLen := min(len(lines1), len(lines2))
	
	for i := 0; i < minLen; i++ {
		if strings.TrimSpace(lines1[i]) == strings.TrimSpace(lines2[i]) {
			matches++
		}
	}
	
	return float64(matches) / float64(maxLen)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func (af *AdvancedFeatures) analyzeDependencies(projectPath string) ([]DependencyInfo, error) {
	var dependencies []DependencyInfo
	
	// 读取go.mod文件
	goModPath := filepath.Join(projectPath, "go.mod")
	content, err := os.ReadFile(goModPath)
	if err != nil {
		return nil, fmt.Errorf("读取go.mod失败: %w", err)
	}
	
	lines := strings.Split(string(content), "\n")
	inRequireBlock := false
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		if strings.HasPrefix(line, "require (") {
			inRequireBlock = true
			continue
		}
		
		if inRequireBlock && line == ")" {
			inRequireBlock = false
			continue
		}
		
		if inRequireBlock || strings.HasPrefix(line, "require ") {
			// 解析依赖行
			dep := af.parseDependencyLine(line)
			if dep.Name != "" {
				dependencies = append(dependencies, dep)
			}
		}
	}
	
	return dependencies, nil
}

func (af *AdvancedFeatures) parseDependencyLine(line string) DependencyInfo {
	// 移除require前缀
	line = strings.TrimPrefix(line, "require ")
	line = strings.TrimSpace(line)
	
	// 解析模块名和版本
	parts := strings.Fields(line)
	if len(parts) >= 2 {
		name := parts[0]
		version := parts[1]
		
		// 判断是否为间接依赖
		depType := "direct"
		if strings.Contains(line, "// indirect") {
			depType = "indirect"
		}
		
		return DependencyInfo{
			Name:    name,
			Version: version,
			Type:    depType,
			License: "unknown", // 需要额外的API调用来获取许可证信息
		}
	}
	
	return DependencyInfo{}
}

func (af *AdvancedFeatures) checkSecurity(projectPath string) ([]SecurityIssue, error) {
	var issues []SecurityIssue
	
	// 定义安全检查规则
	securityPatterns := map[string]SecurityRule{
		"hardcoded_password": {
			Pattern:     `(?i)(password|pwd|secret|key)\s*[:=]\s*["'][^"']{3,}["']`,
			Severity:    "high",
			Description: "发现硬编码密码或密钥",
			Solution:    "使用环境变量或配置文件存储敏感信息",
		},
		"sql_injection": {
			Pattern:     `(?i)(query|exec)\s*\(\s*["'][^"']*\+`,
			Severity:    "high",
			Description: "可能存在SQL注入风险",
			Solution:    "使用参数化查询或ORM",
		},
		"weak_crypto": {
			Pattern:     `(?i)(md5|sha1)\.`,
			Severity:    "medium",
			Description: "使用了弱加密算法",
			Solution:    "使用SHA-256或更强的加密算法",
		},
		"unsafe_http": {
			Pattern:     `http://`,
			Severity:    "medium",
			Description: "使用了不安全的HTTP协议",
			Solution:    "使用HTTPS协议",
		},
	}
	
	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if !info.IsDir() && filepath.Ext(path) == ".go" {
			// 跳过vendor和.git目录
			if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
				return nil
			}
			
			content, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			
			fileContent := string(content)
			lines := strings.Split(fileContent, "\n")
			
			// 检查每个安全规则
			for ruleID, rule := range securityPatterns {
				regex, err := regexp.Compile(rule.Pattern)
				if err != nil {
					continue
				}
				
				for lineNum, line := range lines {
					if regex.MatchString(line) {
						issues = append(issues, SecurityIssue{
							Type:        ruleID,
							Severity:    rule.Severity,
							File:        path,
							Line:        lineNum + 1,
							Description: rule.Description,
							Solution:    rule.Solution,
						})
					}
				}
			}
		}
		
		return nil
	})
	
	return issues, err
}

type SecurityRule struct {
	Pattern     string
	Severity    string
	Description string
	Solution    string
}

func (mg *MigrationGenerator) generateModelMigration(model generator.ModelInfo) (Migration, error) {
	version := time.Now().Format("20060102150405")
	name := "create_" + strings.ToLower(model.TableName) + "_table"
	
	// 生成CREATE TABLE语句
	upSQL := mg.generateCreateTableSQL(model)
	
	// 生成DROP TABLE语句
	downSQL := "DROP TABLE IF EXISTS `" + model.TableName + "`;"
	
	migration := Migration{
		Version:     version,
		Name:        name,
		UpSQL:       upSQL,
		DownSQL:     downSQL,
		CreatedAt:   time.Now(),
		Description: "创建" + model.Comment + "表",
	}
	
	return migration, nil
}

func (mg *MigrationGenerator) generateCreateTableSQL(model generator.ModelInfo) string {
	var sql strings.Builder
	
	sql.WriteString("CREATE TABLE `" + model.TableName + "` (\n")
	
	// 生成字段定义
	var fieldDefs []string
	var indexes []string
	
	for _, field := range model.Fields {
		fieldDef := mg.generateFieldDefinition(field)
		if fieldDef != "" {
			fieldDefs = append(fieldDefs, "  "+fieldDef)
		}
		
		// 生成索引 - 从GORM标签中检查索引信息
		if strings.Contains(field.GORMTag, "index") {
			columnName := mg.extractColumnName(field.GORMTag)
			if columnName == "" {
				columnName = strings.ToLower(field.Name)
			}
			indexes = append(indexes, "  KEY `idx_" + model.TableName + "_" + columnName + "` (`" + columnName + "`)")
		}
		if strings.Contains(field.GORMTag, "unique") {
			columnName := mg.extractColumnName(field.GORMTag)
			if columnName == "" {
				columnName = strings.ToLower(field.Name)
			}
			indexes = append(indexes, "  UNIQUE KEY `uk_" + model.TableName + "_" + columnName + "` (`" + columnName + "`)")
		}
	}
	
	// 添加主键
	fieldDefs = append(fieldDefs, "  PRIMARY KEY (`id`)")
	
	// 合并字段定义和索引
	allDefs := append(fieldDefs, indexes...)
	sql.WriteString(strings.Join(allDefs, ",\n"))
	
	sql.WriteString("\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='" + model.Comment + "';")
	
	return sql.String()
}

func (mg *MigrationGenerator) generateFieldDefinition(field generator.FieldInfo) string {
	if field.Name == "ID" {
		return "`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'"
	}
	
	var def strings.Builder
	// 从GORM标签中提取列名，如果没有则使用字段名的蛇形命名
	columnName := mg.extractColumnName(field.GORMTag)
	if columnName == "" {
		columnName = strings.ToLower(field.Name)
	}
	def.WriteString("`" + columnName + "` ")
	
	// 字段类型映射
	switch field.Type {
	case "string":
		// 默认varchar长度为255，可以根据字段名或标签调整
		length := 255
		if strings.Contains(strings.ToLower(field.Name), "description") || 
		   strings.Contains(strings.ToLower(field.Name), "content") {
			length = 1000
		}
		def.WriteString("varchar(" + strconv.Itoa(length) + ")")
	case "int", "int32":
		def.WriteString("int(11)")
	case "int64":
		def.WriteString("bigint(20)")
	case "float64":
		def.WriteString("decimal(10,2)")
	case "bool":
		def.WriteString("tinyint(1)")
	case "time.Time":
		def.WriteString("datetime")
	default:
		def.WriteString("text")
	}
	
	// NULL约束
	if field.IsRequired {
		def.WriteString(" NOT NULL")
	} else {
		def.WriteString(" NULL")
	}
	
	// 默认值
	if field.DefaultValue != "" {
		def.WriteString(" DEFAULT '" + field.DefaultValue + "'")
	}
	
	// 注释
	if field.Comment != "" {
		def.WriteString(" COMMENT '" + field.Comment + "'")
	}
	
	return def.String()
}

// extractColumnName 从GORM标签中提取列名
func (mg *MigrationGenerator) extractColumnName(gormTag string) string {
	if gormTag == "" {
		return ""
	}
	
	// 解析GORM标签，查找column:xxx
	parts := strings.Split(gormTag, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "column:") {
			return strings.TrimPrefix(part, "column:")
		}
	}
	
	return ""
}

func (dg *DocumentationGenerator) generateAPIDoc(model generator.ModelInfo) []APIDocumentation {
	var apis []APIDocumentation
	basePath := "/api/v1/" + strings.ToLower(model.Name)
	
	// 创建API
	apis = append(apis, APIDocumentation{
		Path:        basePath,
		Method:      "POST",
		Summary:     "创建" + model.Comment,
		Description: "创建新的" + model.Comment + "记录",
		Parameters:  dg.generateCreateParameters(model),
		Responses:   dg.generateStandardResponses(),
		Examples:    dg.generateCreateExamples(model),
		Tags:        []string{model.Comment},
	})
	
	// 获取列表API
	apis = append(apis, APIDocumentation{
		Path:        basePath,
		Method:      "GET",
		Summary:     "获取" + model.Comment + "列表",
		Description: "分页获取" + model.Comment + "列表，支持搜索和排序",
		Parameters:  dg.generateListParameters(model),
		Responses:   dg.generateListResponses(model),
		Examples:    dg.generateListExamples(model),
		Tags:        []string{model.Comment},
	})
	
	// 获取详情API
	apis = append(apis, APIDocumentation{
		Path:        basePath + "/{id}",
		Method:      "GET",
		Summary:     "获取" + model.Comment + "详情",
		Description: "根据ID获取" + model.Comment + "详细信息",
		Parameters:  dg.generateDetailParameters(),
		Responses:   dg.generateDetailResponses(model),
		Examples:    dg.generateDetailExamples(model),
		Tags:        []string{model.Comment},
	})
	
	// 更新API
	apis = append(apis, APIDocumentation{
		Path:        basePath + "/{id}",
		Method:      "PUT",
		Summary:     "更新" + model.Comment,
		Description: "根据ID更新" + model.Comment + "信息",
		Parameters:  dg.generateUpdateParameters(model),
		Responses:   dg.generateStandardResponses(),
		Examples:    dg.generateUpdateExamples(model),
		Tags:        []string{model.Comment},
	})
	
	// 删除API
	apis = append(apis, APIDocumentation{
		Path:        basePath + "/{id}",
		Method:      "DELETE",
		Summary:     "删除" + model.Comment,
		Description: "根据ID删除" + model.Comment,
		Parameters:  dg.generateDetailParameters(),
		Responses:   dg.generateStandardResponses(),
		Examples:    dg.generateDeleteExamples(model),
		Tags:        []string{model.Comment},
	})
	
	return apis
}

func (dg *DocumentationGenerator) generateCreateParameters(model generator.ModelInfo) []ParameterDoc {
	var params []ParameterDoc
	
	for _, field := range model.Fields {
		if field.Name == "ID" || field.Name == "CreatedAt" || field.Name == "UpdatedAt" {
			continue
		}
		
		params = append(params, ParameterDoc{
			Name:        field.JSONTag,
			Type:        field.Type,
			Required:    field.IsRequired,
			Description: field.Comment,
			Example:     dg.generateFieldExample(field),
		})
	}
	
	return params
}

func (dg *DocumentationGenerator) generateListParameters(model generator.ModelInfo) []ParameterDoc {
	params := []ParameterDoc{
		{
			Name:        "page",
			Type:        "int",
			Required:    false,
			Description: "页码，从1开始",
			Example:     "1",
		},
		{
			Name:        "pageSize",
			Type:        "int",
			Required:    false,
			Description: "每页数量，默认10",
			Example:     "10",
		},
		{
			Name:        "keyword",
			Type:        "string",
			Required:    false,
			Description: "搜索关键词",
			Example:     "搜索内容",
		},
		{
			Name:        "sortBy",
			Type:        "string",
			Required:    false,
			Description: "排序字段",
			Example:     "created_at",
		},
		{
			Name:        "sortOrder",
			Type:        "string",
			Required:    false,
			Description: "排序方向：asc/desc",
			Example:     "desc",
		},
	}
	
	return params
}

func (dg *DocumentationGenerator) generateDetailParameters() []ParameterDoc {
	return []ParameterDoc{
		{
			Name:        "id",
			Type:        "int64",
			Required:    true,
			Description: "记录ID",
			Example:     "1",
		},
	}
}

func (dg *DocumentationGenerator) generateUpdateParameters(model generator.ModelInfo) []ParameterDoc {
	params := dg.generateDetailParameters()
	createParams := dg.generateCreateParameters(model)
	return append(params, createParams...)
}

func (dg *DocumentationGenerator) generateStandardResponses() []ResponseDoc {
	return []ResponseDoc{
		{
			Code:        200,
			Description: "操作成功",
			Schema:      "StandardResponse",
			Example:     `{"code": 200, "message": "操作成功", "data": null}`,
		},
		{
			Code:        400,
			Description: "请求参数错误",
			Schema:      "ErrorResponse",
			Example:     `{"code": 400, "message": "参数验证失败", "data": null}`,
		},
		{
			Code:        500,
			Description: "服务器内部错误",
			Schema:      "ErrorResponse",
			Example:     `{"code": 500, "message": "服务器内部错误", "data": null}`,
		},
	}
}

func (dg *DocumentationGenerator) generateListResponses(model generator.ModelInfo) []ResponseDoc {
	return []ResponseDoc{
		{
			Code:        200,
			Description: "获取成功",
			Schema:      "ListResponse",
			Example:     fmt.Sprintf(`{"code": 200, "message": "获取成功", "data": {"list": [], "total": 0, "page": 1, "pageSize": 10}}`),
		},
	}
}

func (dg *DocumentationGenerator) generateDetailResponses(model generator.ModelInfo) []ResponseDoc {
	return []ResponseDoc{
		{
			Code:        200,
			Description: "获取成功",
			Schema:      model.Name,
			Example:     dg.generateModelExample(model),
		},
		{
			Code:        404,
			Description: "记录不存在",
			Schema:      "ErrorResponse",
			Example:     `{"code": 404, "message": "记录不存在", "data": null}`,
		},
	}
}

func (dg *DocumentationGenerator) generateCreateExamples(model generator.ModelInfo) []RequestExample {
	return []RequestExample{
		{
			Name:        "创建示例",
			Description: fmt.Sprintf("创建%s的示例请求", model.Comment),
			Request:     dg.generateCreateRequestExample(model),
			Response:    `{"code": 200, "message": "创建成功", "data": {"id": 1}}`,
		},
	}
}

func (dg *DocumentationGenerator) generateListExamples(model generator.ModelInfo) []RequestExample {
	return []RequestExample{
		{
			Name:        "列表查询示例",
			Description: fmt.Sprintf("获取%s列表的示例请求", model.Comment),
			Request:     "GET /api/v1/" + strings.ToLower(model.Name) + "?page=1&pageSize=10",
			Response:    dg.generateListResponseExample(model),
		},
	}
}

func (dg *DocumentationGenerator) generateDetailExamples(model generator.ModelInfo) []RequestExample {
	return []RequestExample{
		{
			Name:        "详情查询示例",
			Description: fmt.Sprintf("获取%s详情的示例请求", model.Comment),
			Request:     "GET /api/v1/" + strings.ToLower(model.Name) + "/1",
			Response:    fmt.Sprintf(`{"code": 200, "message": "获取成功", "data": %s}`, dg.generateModelExample(model)),
		},
	}
}

func (dg *DocumentationGenerator) generateUpdateExamples(model generator.ModelInfo) []RequestExample {
	return []RequestExample{
		{
			Name:        "更新示例",
			Description: fmt.Sprintf("更新%s的示例请求", model.Comment),
			Request:     dg.generateUpdateRequestExample(model),
			Response:    `{"code": 200, "message": "更新成功", "data": null}`,
		},
	}
}

func (dg *DocumentationGenerator) generateDeleteExamples(model generator.ModelInfo) []RequestExample {
	return []RequestExample{
		{
			Name:        "删除示例",
			Description: fmt.Sprintf("删除%s的示例请求", model.Comment),
			Request:     "DELETE /api/v1/" + strings.ToLower(model.Name) + "/1",
			Response:    `{"code": 200, "message": "删除成功", "data": null}`,
		},
	}
}

func (dg *DocumentationGenerator) generateFieldExample(field generator.FieldInfo) string {
	switch field.Type {
	case "string":
		if strings.Contains(strings.ToLower(field.Name), "email") {
			return "<EMAIL>"
		}
		if strings.Contains(strings.ToLower(field.Name), "phone") {
			return "13800138000"
		}
		if strings.Contains(strings.ToLower(field.Name), "name") {
			return "示例名称"
		}
		return "示例文本"
	case "int", "int32", "int64":
		return "1"
	case "float64":
		return "99.99"
	case "bool":
		return "true"
	case "time.Time":
		return "2024-01-01T00:00:00Z"
	default:
		return "示例值"
	}
}

func (dg *DocumentationGenerator) generateModelExample(model generator.ModelInfo) string {
	var fields []string
	
	for _, field := range model.Fields {
		example := dg.generateFieldExample(field)
		if field.Type == "string" {
			fields = append(fields, fmt.Sprintf(`"%s": "%s"`, field.JSONTag, example))
		} else {
			fields = append(fields, fmt.Sprintf(`"%s": %s`, field.JSONTag, example))
		}
	}
	
	return fmt.Sprintf("{%s}", strings.Join(fields, ", "))
}

func (dg *DocumentationGenerator) generateCreateRequestExample(model generator.ModelInfo) string {
	var fields []string
	
	for _, field := range model.Fields {
		if field.Name == "ID" || field.Name == "CreatedAt" || field.Name == "UpdatedAt" {
			continue
		}
		
		example := dg.generateFieldExample(field)
		if field.Type == "string" {
			fields = append(fields, fmt.Sprintf(`"%s": "%s"`, field.JSONTag, example))
		} else {
			fields = append(fields, fmt.Sprintf(`"%s": %s`, field.JSONTag, example))
		}
	}
	
	return fmt.Sprintf("{%s}", strings.Join(fields, ", "))
}

func (dg *DocumentationGenerator) generateUpdateRequestExample(model generator.ModelInfo) string {
	return dg.generateCreateRequestExample(model)
}

func (dg *DocumentationGenerator) generateListResponseExample(model generator.ModelInfo) string {
	modelExample := dg.generateModelExample(model)
	return fmt.Sprintf(`{"code": 200, "message": "获取成功", "data": {"list": [%s], "total": 1, "page": 1, "pageSize": 10}}`, modelExample)
}

func (dg *DocumentationGenerator) generateModelDoc(model generator.ModelInfo) ModelDocumentation {
	var fields []FieldDoc
	var relations []RelationDoc
	
	// 生成字段文档
	for _, field := range model.Fields {
		fieldDoc := FieldDoc{
			Name:        field.Name,
			Type:        field.Type,
			Required:    field.IsRequired,
			Description: field.Comment,
			Validation:  field.Validation,
			Example:     dg.generateFieldExample(field),
		}
		fields = append(fields, fieldDoc)
	}
	
	// 生成关系文档
	for _, relation := range model.Relations {
		relationDoc := RelationDoc{
			Type:        relation.Type,
			Model:       relation.Model,
			Description: fmt.Sprintf("%s关系到%s", relation.Type, relation.Model),
		}
		relations = append(relations, relationDoc)
	}
	
	// 生成示例
	examples := []string{
		dg.generateModelExample(model),
		dg.generateCreateRequestExample(model),
	}
	
	return ModelDocumentation{
		Name:        model.Name,
		Description: model.Comment,
		Fields:      fields,
		Relations:   relations,
		Examples:    examples,
	}
}

func (dg *DocumentationGenerator) generateExamples(models []generator.ModelInfo) []ExampleCode {
	var examples []ExampleCode
	
	for _, model := range models {
		// Go客户端示例
		goExample := ExampleCode{
			Title:       fmt.Sprintf("%s Go客户端示例", model.Comment),
			Description: fmt.Sprintf("使用Go语言调用%s API的示例代码", model.Comment),
			Language:    "go",
			Code:        dg.generateGoClientExample(model),
			Output:      `{"code": 200, "message": "操作成功", "data": {...}}`,
		}
		examples = append(examples, goExample)
		
		// JavaScript示例
		jsExample := ExampleCode{
			Title:       fmt.Sprintf("%s JavaScript示例", model.Comment),
			Description: fmt.Sprintf("使用JavaScript调用%s API的示例代码", model.Comment),
			Language:    "javascript",
			Code:        dg.generateJavaScriptExample(model),
			Output:      `{"code": 200, "message": "操作成功", "data": {...}}`,
		}
		examples = append(examples, jsExample)
		
		// cURL示例
		curlExample := ExampleCode{
			Title:       fmt.Sprintf("%s cURL示例", model.Comment),
			Description: fmt.Sprintf("使用cURL调用%s API的示例命令", model.Comment),
			Language:    "bash",
			Code:        dg.generateCurlExample(model),
			Output:      `{"code": 200, "message": "操作成功", "data": {...}}`,
		}
		examples = append(examples, curlExample)
	}
	
	return examples
}

func (dg *DocumentationGenerator) generateGoClientExample(model generator.ModelInfo) string {
	modelName := strings.ToLower(model.Name)
	return fmt.Sprintf(`package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

type %s struct {
	%s
}

func main() {
	// 创建%s
	%sData := %s{
		%s
	}
	
	jsonData, _ := json.Marshal(%sData)
	resp, err := http.Post("http://localhost:8080/api/v1/%s", 
		"application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	
	body, _ := io.ReadAll(resp.Body)
	fmt.Println(string(body))
	
	// 获取%s列表
	resp, err = http.Get("http://localhost:8080/api/v1/%s?page=1&pageSize=10")
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	
	body, _ = io.ReadAll(resp.Body)
	fmt.Println(string(body))
}`,
		model.Name,
		dg.generateGoStructFields(model),
		model.Comment,
		modelName,
		model.Name,
		dg.generateGoStructInit(model),
		modelName,
		modelName,
		model.Comment,
		modelName)
}

func (dg *DocumentationGenerator) generateGoStructFields(model generator.ModelInfo) string {
	var fields []string
	for _, field := range model.Fields {
		if field.Name == "ID" || field.Name == "CreatedAt" || field.Name == "UpdatedAt" {
			continue
		}
		fields = append(fields, fmt.Sprintf("\t%s %s `json:\"%s\"`", 
			field.Name, field.Type, field.JSONTag))
	}
	return strings.Join(fields, "\n")
}

func (dg *DocumentationGenerator) generateGoStructInit(model generator.ModelInfo) string {
	var fields []string
	for _, field := range model.Fields {
		if field.Name == "ID" || field.Name == "CreatedAt" || field.Name == "UpdatedAt" {
			continue
		}
		example := dg.generateFieldExample(field)
		if field.Type == "string" {
			fields = append(fields, fmt.Sprintf("\t\t%s: \"%s\",", field.Name, example))
		} else {
			fields = append(fields, fmt.Sprintf("\t\t%s: %s,", field.Name, example))
		}
	}
	return strings.Join(fields, "\n")
}

func (dg *DocumentationGenerator) generateJavaScriptExample(model generator.ModelInfo) string {
	modelName := strings.ToLower(model.Name)
	return fmt.Sprintf(`// 创建%s
const %sData = %s;

fetch('/api/v1/%s', {
  method: 'POST',
  headers: {
	'Content-Type': 'application/json',
  },
  body: JSON.stringify(%sData)
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));

// 获取%s列表
fetch('/api/v1/%s?page=1&pageSize=10')
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));

// 获取%s详情
fetch('/api/v1/%s/1')
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));`,
		model.Comment,
		modelName,
		dg.generateCreateRequestExample(model),
		modelName,
		modelName,
		model.Comment,
		modelName,
		model.Comment,
		modelName)
}

func (dg *DocumentationGenerator) generateCurlExample(model generator.ModelInfo) string {
	modelName := strings.ToLower(model.Name)
	return fmt.Sprintf(`# 创建%s
curl -X POST http://localhost:8080/api/v1/%s \
  -H "Content-Type: application/json" \
  -d '%s'

# 获取%s列表
curl -X GET "http://localhost:8080/api/v1/%s?page=1&pageSize=10"

# 获取%s详情
curl -X GET http://localhost:8080/api/v1/%s/1

# 更新%s
curl -X PUT http://localhost:8080/api/v1/%s/1 \
  -H "Content-Type: application/json" \
  -d '%s'

# 删除%s
curl -X DELETE http://localhost:8080/api/v1/%s/1`,
		model.Comment,
		modelName,
		dg.generateCreateRequestExample(model),
		model.Comment,
		modelName,
		model.Comment,
		modelName,
		model.Comment,
		modelName,
		dg.generateUpdateRequestExample(model),
		model.Comment,
		modelName)
}

func (af *AdvancedFeatures) runQualityRule(rule QualityRule, projectPath string) ([]QualityIssue, error) {
	var issues []QualityIssue
	
	switch rule.ID {
	case "function_length":
		issues = af.checkFunctionLength(projectPath, rule)
	case "cyclomatic_complexity":
		issues = af.checkCyclomaticComplexity(projectPath, rule)
	case "naming_convention":
		issues = af.checkNamingConvention(projectPath, rule)
	case "comment_coverage":
		issues = af.checkCommentCoverage(projectPath, rule)
	case "error_handling":
		issues = af.checkErrorHandling(projectPath, rule)
	default:
		return []QualityIssue{}, fmt.Errorf("未知的质量规则: %s", rule.ID)
	}
	
	return issues, nil
}

func (af *AdvancedFeatures) checkFunctionLength(projectPath string, rule QualityRule) []QualityIssue {
	var issues []QualityIssue
	maxLines := 50 // 函数最大行数
	
	filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || !strings.HasSuffix(path, ".go") {
			return nil
		}
		
		if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
			return nil
		}
		
		content, err := os.ReadFile(path)
		if err != nil {
			return nil
		}
		
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, content, parser.ParseComments)
		if err != nil {
			return nil
		}
		
		ast.Inspect(node, func(n ast.Node) bool {
			if fn, ok := n.(*ast.FuncDecl); ok {
				start := fset.Position(fn.Pos()).Line
				end := fset.Position(fn.End()).Line
				lines := end - start + 1
				
				if lines > maxLines {
					issues = append(issues, QualityIssue{
						RuleID:     rule.ID,
						File:       path,
						Line:       start,
						Column:     1,
						Message:    fmt.Sprintf("函数 %s 过长，有 %d 行，建议不超过 %d 行", fn.Name.Name, lines, maxLines),
						Severity:   rule.Severity,
						Category:   rule.Category,
						Suggestion: "考虑将大函数拆分为多个小函数",
					})
				}
			}
			return true
		})
		
		return nil
	})
	
	return issues
}

func (af *AdvancedFeatures) checkCyclomaticComplexity(projectPath string, rule QualityRule) []QualityIssue {
	var issues []QualityIssue
	maxComplexity := 10 // 最大圈复杂度
	
	filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || !strings.HasSuffix(path, ".go") {
			return nil
		}
		
		if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
			return nil
		}
		
		content, err := os.ReadFile(path)
		if err != nil {
			return nil
		}
		
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, content, parser.ParseComments)
		if err != nil {
			return nil
		}
		
		ast.Inspect(node, func(n ast.Node) bool {
			if fn, ok := n.(*ast.FuncDecl); ok {
				complexity := af.calculateFunctionComplexity(fn)
				if complexity > maxComplexity {
					issues = append(issues, QualityIssue{
						RuleID:     rule.ID,
						File:       path,
						Line:       fset.Position(fn.Pos()).Line,
						Column:     1,
						Message:    fmt.Sprintf("函数 %s 圈复杂度过高: %d，建议不超过 %d", fn.Name.Name, complexity, maxComplexity),
						Severity:   rule.Severity,
						Category:   rule.Category,
						Suggestion: "考虑简化函数逻辑，减少嵌套和条件分支",
					})
				}
			}
			return true
		})
		
		return nil
	})
	
	return issues
}

func (af *AdvancedFeatures) calculateFunctionComplexity(fn *ast.FuncDecl) int {
	complexity := 1 // 基础复杂度
	
	ast.Inspect(fn, func(n ast.Node) bool {
		switch n.(type) {
		case *ast.IfStmt, *ast.ForStmt, *ast.RangeStmt, *ast.SwitchStmt, *ast.TypeSwitchStmt:
			complexity++
		case *ast.CaseClause:
			complexity++
		}
		return true
	})
	
	return complexity
}

func (af *AdvancedFeatures) checkNamingConvention(projectPath string, rule QualityRule) []QualityIssue {
	var issues []QualityIssue
	
	// 检查命名规范
	filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || !strings.HasSuffix(path, ".go") {
			return nil
		}
		
		if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
			return nil
		}
		
		content, err := os.ReadFile(path)
		if err != nil {
			return nil
		}
		
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, content, parser.ParseComments)
		if err != nil {
			return nil
		}
		
		ast.Inspect(node, func(n ast.Node) bool {
			switch decl := n.(type) {
			case *ast.FuncDecl:
				if decl.Name.IsExported() && !af.isValidExportedName(decl.Name.Name) {
					issues = append(issues, QualityIssue{
						RuleID:     rule.ID,
						File:       path,
						Line:       fset.Position(decl.Pos()).Line,
						Column:     1,
						Message:    fmt.Sprintf("导出函数 %s 命名不符合规范", decl.Name.Name),
						Severity:   rule.Severity,
						Category:   rule.Category,
						Suggestion: "导出函数应使用驼峰命名法",
					})
				}
			case *ast.GenDecl:
				for _, spec := range decl.Specs {
					if typeSpec, ok := spec.(*ast.TypeSpec); ok {
						if typeSpec.Name.IsExported() && !af.isValidExportedName(typeSpec.Name.Name) {
							issues = append(issues, QualityIssue{
								RuleID:     rule.ID,
								File:       path,
								Line:       fset.Position(typeSpec.Pos()).Line,
								Column:     1,
								Message:    fmt.Sprintf("导出类型 %s 命名不符合规范", typeSpec.Name.Name),
								Severity:   rule.Severity,
								Category:   rule.Category,
								Suggestion: "导出类型应使用驼峰命名法",
							})
						}
					}
				}
			}
			return true
		})
		
		return nil
	})
	
	return issues
}

func (af *AdvancedFeatures) isValidExportedName(name string) bool {
	// 简单的驼峰命名检查
	if len(name) == 0 {
		return false
	}
	
	// 首字母应该大写
	if name[0] < 'A' || name[0] > 'Z' {
		return false
	}
	
	// 不应该包含下划线
	return !strings.Contains(name, "_")
}

func (af *AdvancedFeatures) checkCommentCoverage(projectPath string, rule QualityRule) []QualityIssue {
	var issues []QualityIssue
	
	filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || !strings.HasSuffix(path, ".go") {
			return nil
		}
		
		if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
			return nil
		}
		
		content, err := os.ReadFile(path)
		if err != nil {
			return nil
		}
		
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, content, parser.ParseComments)
		if err != nil {
			return nil
		}
		
		ast.Inspect(node, func(n ast.Node) bool {
			if fn, ok := n.(*ast.FuncDecl); ok {
				if fn.Name.IsExported() && fn.Doc == nil {
					issues = append(issues, QualityIssue{
						RuleID:     rule.ID,
						File:       path,
						Line:       fset.Position(fn.Pos()).Line,
						Column:     1,
						Message:    fmt.Sprintf("导出函数 %s 缺少注释", fn.Name.Name),
						Severity:   rule.Severity,
						Category:   rule.Category,
						Suggestion: "为导出函数添加注释说明",
					})
				}
			}
			return true
		})
		
		return nil
	})
	
	return issues
}

func (af *AdvancedFeatures) checkErrorHandling(projectPath string, rule QualityRule) []QualityIssue {
	var issues []QualityIssue
	
	filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil || !strings.HasSuffix(path, ".go") {
			return nil
		}
		
		if strings.Contains(path, "vendor/") || strings.Contains(path, ".git/") {
			return nil
		}
		
		content, err := os.ReadFile(path)
		if err != nil {
			return nil
		}
		
		lines := strings.Split(string(content), "\n")
		for i, line := range lines {
			// 检查是否忽略了错误
			if strings.Contains(line, "_ =") && strings.Contains(line, "err") {
				issues = append(issues, QualityIssue{
					RuleID:     rule.ID,
					File:       path,
					Line:       i + 1,
					Column:     1,
					Message:    "忽略了错误处理",
					Severity:   rule.Severity,
					Category:   rule.Category,
					Suggestion: "应该正确处理错误，而不是忽略",
				})
			}
		}
		
		return nil
	})
	
	return issues
}

func (af *AdvancedFeatures) calculateQualityScore(issues []QualityIssue) float64 {
	// 实现质量分数计算
	return 85.0
}

func (af *AdvancedFeatures) getQualityGrade(score float64) string {
	if score >= 90 {
		return "A"
	} else if score >= 80 {
		return "B"
	} else if score >= 70 {
		return "C"
	} else if score >= 60 {
		return "D"
	}
	return "F"
}

func (af *AdvancedFeatures) generateSuggestions(issues []QualityIssue) []string {
	suggestions := make(map[string]int)
	
	// 统计问题类型
	for _, issue := range issues {
		switch issue.RuleID {
		case "function_length":
			suggestions["考虑将大函数拆分为多个小函数，提高代码可读性"]++
		case "cyclomatic_complexity":
			suggestions["简化函数逻辑，减少嵌套和条件分支"]++
		case "naming_convention":
			suggestions["遵循Go语言命名规范，使用驼峰命名法"]++
		case "comment_coverage":
			suggestions["为导出的函数和类型添加注释说明"]++
		case "error_handling":
			suggestions["正确处理错误，避免忽略错误返回值"]++
		case "hardcoded_password":
			suggestions["使用环境变量或配置文件存储敏感信息"]++
		case "sql_injection":
			suggestions["使用参数化查询或ORM防止SQL注入"]++
		case "weak_crypto":
			suggestions["使用更强的加密算法，如SHA-256"]++
		case "unsafe_http":
			suggestions["使用HTTPS协议保证数据传输安全"]++
		}
	}
	
	// 转换为切片并按频率排序
	var result []string
	for suggestion, count := range suggestions {
		if count > 1 {
			result = append(result, fmt.Sprintf("%s (发现%d处)", suggestion, count))
		} else {
			result = append(result, suggestion)
		}
	}
	
	// 添加通用建议
	if len(issues) > 0 {
		result = append(result, "定期进行代码审查，保持代码质量")
		result = append(result, "使用静态分析工具自动检测代码问题")
		result = append(result, "编写单元测试，提高代码覆盖率")
	}
	
	return result
}

func (af *AdvancedFeatures) generateHTMLReport(report interface{}) ([]byte, error) {
	htmlTemplate := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>代码质量报告</title>
	<style>
		body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
		.container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
		.header { text-align: center; margin-bottom: 30px; }
		.score { font-size: 48px; font-weight: bold; margin: 20px 0; }
		.grade-A { color: #28a745; }
		.grade-B { color: #17a2b8; }
		.grade-C { color: #ffc107; }
		.grade-D { color: #fd7e14; }
		.grade-F { color: #dc3545; }
		.metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
		.metric { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
		.metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
		.issues { margin: 20px 0; }
		.issue { background: #fff; border-left: 4px solid #dc3545; padding: 10px; margin: 10px 0; border-radius: 0 5px 5px 0; }
		.issue-high { border-left-color: #dc3545; }
		.issue-medium { border-left-color: #ffc107; }
		.issue-low { border-left-color: #28a745; }
		.suggestions { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
		.suggestions ul { margin: 10px 0; padding-left: 20px; }
		table { width: 100%; border-collapse: collapse; margin: 20px 0; }
		th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
		th { background-color: #f8f9fa; font-weight: bold; }
	</style>
</head>
<body>
	<div class="container">
		<div class="header">
			<h1>代码质量报告</h1>
			<div class="score grade-{{.Grade}}">{{.Score}}</div>
			<div>等级: {{.Grade}}</div>
			<div>生成时间: {{.Timestamp.Format "2006-01-02 15:04:05"}}</div>
		</div>

		{{if .Metrics}}
		<div class="metrics">
			<div class="metric">
				<div class="metric-value">{{.Metrics.LinesOfCode}}</div>
				<div>代码行数</div>
			</div>
			<div class="metric">
				<div class="metric-value">{{.Metrics.CyclomaticComplexity}}</div>
				<div>圈复杂度</div>
			</div>
			<div class="metric">
				<div class="metric-value">{{printf "%.1f%%" .Metrics.TestCoverage}}</div>
				<div>测试覆盖率</div>
			</div>
			<div class="metric">
				<div class="metric-value">{{len .Issues}}</div>
				<div>问题数量</div>
			</div>
		</div>
		{{end}}

		{{if .Issues}}
		<div class="issues">
			<h2>发现的问题</h2>
			{{range .Issues}}
			<div class="issue issue-{{.Severity}}">
				<strong>{{.Message}}</strong><br>
				<small>文件: {{.File}}:{{.Line}} | 规则: {{.RuleID}} | 严重程度: {{.Severity}}</small><br>
				<em>建议: {{.Suggestion}}</em>
			</div>
			{{end}}
		</div>
		{{end}}

		{{if .Suggestions}}
		<div class="suggestions">
			<h2>改进建议</h2>
			<ul>
				{{range .Suggestions}}
				<li>{{.}}</li>
				{{end}}
			</ul>
		</div>
		{{end}}
	</div>
</body>
</html>`

	// 使用Go模板生成HTML
	tmpl, err := template.New("report").Parse(htmlTemplate)
	if err != nil {
		return nil, fmt.Errorf("解析HTML模板失败: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, report); err != nil {
		return nil, fmt.Errorf("生成HTML报告失败: %w", err)
	}

	return buf.Bytes(), nil
}

func (af *AdvancedFeatures) generateMarkdownReport(report interface{}) ([]byte, error) {
	var buf strings.Builder
	
	switch r := report.(type) {
	case *QualityReport:
		buf.WriteString("# 代码质量报告\n\n")
		buf.WriteString(fmt.Sprintf("**生成时间**: %s\n\n", r.Timestamp.Format("2006-01-02 15:04:05")))
		
		// 质量分数
		buf.WriteString("## 质量评分\n\n")
		buf.WriteString(fmt.Sprintf("**分数**: %.1f/100\n", r.Score))
		buf.WriteString(fmt.Sprintf("**等级**: %s\n\n", r.Grade))
		
		// 代码指标
		if r.Metrics.LinesOfCode > 0 {
			buf.WriteString("## 代码指标\n\n")
			buf.WriteString("| 指标 | 数值 |\n")
			buf.WriteString("|------|------|\n")
			buf.WriteString(fmt.Sprintf("| 代码行数 | %d |\n", r.Metrics.LinesOfCode))
			buf.WriteString(fmt.Sprintf("| 圈复杂度 | %d |\n", r.Metrics.CyclomaticComplexity))
			buf.WriteString(fmt.Sprintf("| 测试覆盖率 | %.1f%% |\n", r.Metrics.TestCoverage))
			buf.WriteString(fmt.Sprintf("| 问题数量 | %d |\n\n", len(r.Issues)))
		}
		
		// 问题列表
		if len(r.Issues) > 0 {
			buf.WriteString("## 发现的问题\n\n")
			
			// 按严重程度分组
			severityGroups := make(map[string][]QualityIssue)
			for _, issue := range r.Issues {
				severityGroups[issue.Severity] = append(severityGroups[issue.Severity], issue)
			}
			
			severityOrder := []string{"high", "medium", "low"}
			severityNames := map[string]string{
				"high":   "🔴 高危",
				"medium": "🟡 中等",
				"low":    "🟢 低危",
			}
			
			for _, severity := range severityOrder {
				if issues, exists := severityGroups[severity]; exists {
					buf.WriteString(fmt.Sprintf("### %s\n\n", severityNames[severity]))
					for _, issue := range issues {
						buf.WriteString(fmt.Sprintf("- **%s**\n", issue.Message))
						buf.WriteString(fmt.Sprintf("  - 文件: `%s:%d`\n", issue.File, issue.Line))
						buf.WriteString(fmt.Sprintf("  - 规则: %s\n", issue.RuleID))
						buf.WriteString(fmt.Sprintf("  - 建议: %s\n\n", issue.Suggestion))
					}
				}
			}
		}
		
		// 改进建议
		if len(r.Suggestions) > 0 {
			buf.WriteString("## 改进建议\n\n")
			for _, suggestion := range r.Suggestions {
				buf.WriteString(fmt.Sprintf("- %s\n", suggestion))
			}
			buf.WriteString("\n")
		}
		
	case *Documentation:
		buf.WriteString(fmt.Sprintf("# %s\n\n", r.Title))
		buf.WriteString(fmt.Sprintf("**版本**: %s\n\n", r.Version))
		buf.WriteString(fmt.Sprintf("%s\n\n", r.Description))
		
		// API文档
		if len(r.APIs) > 0 {
			buf.WriteString("## API接口\n\n")
			for _, api := range r.APIs {
				buf.WriteString(fmt.Sprintf("### %s %s\n\n", api.Method, api.Path))
				buf.WriteString(fmt.Sprintf("**描述**: %s\n\n", api.Description))
				
				if len(api.Parameters) > 0 {
					buf.WriteString("**参数**:\n\n")
					buf.WriteString("| 参数名 | 类型 | 必填 | 描述 | 示例 |\n")
					buf.WriteString("|--------|------|------|------|------|\n")
					for _, param := range api.Parameters {
						required := "否"
						if param.Required {
							required = "是"
						}
						buf.WriteString(fmt.Sprintf("| %s | %s | %s | %s | %s |\n",
							param.Name, param.Type, required, param.Description, param.Example))
					}
					buf.WriteString("\n")
				}
				
				if len(api.Examples) > 0 {
					buf.WriteString("**示例**:\n\n")
					for _, example := range api.Examples {
						buf.WriteString(fmt.Sprintf("```\n%s\n```\n\n", example.Request))
						buf.WriteString(fmt.Sprintf("响应:\n```json\n%s\n```\n\n", example.Response))
					}
				}
			}
		}
		
		// 数据模型
		if len(r.Models) > 0 {
			buf.WriteString("## 数据模型\n\n")
			for _, model := range r.Models {
				buf.WriteString(fmt.Sprintf("### %s\n\n", model.Name))
				buf.WriteString(fmt.Sprintf("%s\n\n", model.Description))
				
				if len(model.Fields) > 0 {
					buf.WriteString("**字段**:\n\n")
					buf.WriteString("| 字段名 | 类型 | 必填 | 描述 | 验证规则 |\n")
					buf.WriteString("|--------|------|------|------|----------|\n")
					for _, field := range model.Fields {
						required := "否"
						if field.Required {
							required = "是"
						}
						buf.WriteString(fmt.Sprintf("| %s | %s | %s | %s | %s |\n",
							field.Name, field.Type, required, field.Description, field.Validation))
					}
					buf.WriteString("\n")
				}
			}
		}
		
	default:
		// 通用JSON格式
		jsonData, err := json.MarshalIndent(report, "", "  ")
		if err != nil {
			return nil, fmt.Errorf("序列化报告失败: %w", err)
		}
		buf.WriteString("# 报告\n\n")
		buf.WriteString("```json\n")
		buf.WriteString(string(jsonData))
		buf.WriteString("\n```\n")
	}
	
	return []byte(buf.String()), nil
}
