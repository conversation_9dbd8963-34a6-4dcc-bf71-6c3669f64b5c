//go:build wireinject
// +build wireinject

package wire

import (
	"admin/internal/controller"
	"admin/internal/job"
	"admin/internal/repository"
	"admin/internal/server"
	"admin/internal/service"
	"admin/pkg/app"
	"admin/pkg/jwt"
	"admin/pkg/log"
	"admin/pkg/server/http"
	"admin/pkg/sid"

	"github.com/google/wire"
	"github.com/spf13/viper"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	repository.NewRepository,
	repository.NewUserRepository,
	repository.NewCasbinEnforcer,
	repository.NewLoginRepository,
	repository.NewPostRepository,
	repository.NewDeptRepository,
	repository.NewRoleRepository,
	repository.NewSysRouteRepository,
	repository.NewDictRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewUserService,
	service.NewLoginService,
	service.NewPostService,
	service.NewDeptService,
	service.NewRoleService,
	service.NewSysRouteService,
	service.NewDictService,
)

var controllerSet = wire.NewSet(
	controller.NewController,
	controller.NewUserController,
	controller.NewLoginController,
	controller.NewPostController,
	controller.NewDeptController,
	controller.NewRoleController,
	controller.NewSysRouteController,
	controller.NewDictController,
)

var jobSet = wire.NewSet(
	job.NewJob,
	job.NewUserJob,
)

var serverSet = wire.NewSet(
	server.NewHTTPServer,
	server.NewJobServer,
)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, jobServer),
		app.WithName("demo-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		controllerSet,
		jobSet,
		serverSet,
		sid.NewSid,
		jwt.NewJwt,
		newApp,
	))
}
