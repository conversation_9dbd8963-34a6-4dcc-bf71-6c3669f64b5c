// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"admin/internal/controller"
	"admin/internal/job"
	"admin/internal/repository"
	"admin/internal/server"
	"admin/internal/service"
	"admin/pkg/app"
	"admin/pkg/jwt"
	"admin/pkg/log"
	"admin/pkg/server/http"
	"admin/pkg/sid"
	"github.com/google/wire"
	"github.com/spf13/viper"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	jwtJWT := jwt.NewJwt(viperViper)
	db := repository.NewDB(viperViper, logger)
	syncedEnforcer := repository.NewCasbinEnforcer(viperViper, logger, db)
	controllerController := controller.NewController(logger)
	sidSid := sid.NewSid()
	client := repository.NewRedis(viperViper)
	serviceService := service.NewService(logger, sidSid, jwtJWT, client)
	repositoryRepository := repository.NewRepository(logger, db, syncedEnforcer, client)
	userRepository := repository.NewUserRepository(repositoryRepository)
	userService := service.NewUserService(serviceService, userRepository)
	userController := controller.NewUserController(controllerController, userService)
	loginRepository := repository.NewLoginRepository(repositoryRepository)
	loginService := service.NewLoginService(serviceService, loginRepository)
	loginController := controller.NewLoginController(controllerController, loginService)
	postRepository := repository.NewPostRepository(repositoryRepository)
	postService := service.NewPostService(serviceService, postRepository)
	postController := controller.NewPostController(controllerController, postService)
	deptRepository := repository.NewDeptRepository(repositoryRepository)
	deptService := service.NewDeptService(serviceService, deptRepository)
	deptController := controller.NewDeptController(controllerController, deptService)
	roleRepository := repository.NewRoleRepository(repositoryRepository)
	roleService := service.NewRoleService(serviceService, roleRepository)
	roleController := controller.NewRoleController(controllerController, roleService)
	sysRouteRepository := repository.NewSysRouteRepository(repositoryRepository)
	sysRouteService := service.NewSysRouteService(serviceService, sysRouteRepository)
	sysRouteController := controller.NewSysRouteController(controllerController, sysRouteService)
	dictRepository := repository.NewDictRepository(repositoryRepository)
	dictService := service.NewDictService(serviceService, dictRepository)
	dictController := controller.NewDictController(controllerController, dictService)
	httpServer := server.NewHTTPServer(logger, viperViper, jwtJWT, syncedEnforcer, userController, loginController, postController, deptController, roleController, sysRouteController, dictController)
	jobJob := job.NewJob(logger, sidSid)
	userJob := job.NewUserJob(jobJob, userRepository)
	jobServer := server.NewJobServer(logger, userJob)
	appApp := newApp(httpServer, jobServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewUserRepository, repository.NewCasbinEnforcer, repository.NewLoginRepository, repository.NewPostRepository, repository.NewDeptRepository, repository.NewRoleRepository, repository.NewSysRouteRepository, repository.NewDictRepository)

var serviceSet = wire.NewSet(service.NewService, service.NewUserService, service.NewLoginService, service.NewPostService, service.NewDeptService, service.NewRoleService, service.NewSysRouteService, service.NewDictService)

var controllerSet = wire.NewSet(controller.NewController, controller.NewUserController, controller.NewLoginController, controller.NewPostController, controller.NewDeptController, controller.NewRoleController, controller.NewSysRouteController, controller.NewDictController)

var jobSet = wire.NewSet(job.NewJob, job.NewUserJob)

var serverSet = wire.NewSet(server.NewHTTPServer, server.NewJobServer)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,
) *app.App {
	return app.NewApp(app.WithServer(httpServer, jobServer), app.WithName("demo-server"))
}
