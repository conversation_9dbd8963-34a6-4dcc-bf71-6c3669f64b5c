// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"admin/internal/repository"
	"admin/internal/server"
	"admin/internal/task"
	"admin/pkg/app"
	"admin/pkg/log"
	"admin/pkg/sid"
	"github.com/google/wire"
	"github.com/spf13/viper"
)

// Injectors from wire.go:

func NewWire(viperViper *viper.Viper, logger *log.Logger) (*app.App, func(), error) {
	sidSid := sid.NewSid()
	taskTask := task.NewTask(logger, sidSid)
	db := repository.NewDB(viperViper, logger)
	syncedEnforcer := repository.NewCasbinEnforcer(viperViper, logger, db)
	client := repository.NewRedis(viperViper)
	repositoryRepository := repository.NewRepository(logger, db, syncedEnforcer, client)
	userRepository := repository.NewUserRepository(repositoryRepository)
	userTask := task.NewUserTask(taskTask, userRepository)
	taskServer := server.NewTaskServer(logger, userTask)
	appApp := newApp(taskServer)
	return appApp, func() {
	}, nil
}

// wire.go:

var repositorySet = wire.NewSet(repository.NewDB, repository.NewRedis, repository.NewRepository, repository.NewUserRepository, repository.NewCasbinEnforcer)

var taskSet = wire.NewSet(task.NewTask, task.NewUserTask)

var serverSet = wire.NewSet(server.NewTaskServer)

// build App
func newApp(task2 *server.TaskServer,
) *app.App {
	return app.NewApp(app.WithServer(task2), app.WithName("demo-task"))
}
